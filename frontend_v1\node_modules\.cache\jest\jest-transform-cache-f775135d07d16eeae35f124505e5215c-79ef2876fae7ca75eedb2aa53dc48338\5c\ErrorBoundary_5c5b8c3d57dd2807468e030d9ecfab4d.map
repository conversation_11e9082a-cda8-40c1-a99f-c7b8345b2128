{"version": 3, "names": ["_react", "_interopRequireWildcard", "require", "_reactNative", "_Typography", "_Animated<PERSON><PERSON><PERSON>", "_HighContrastContext", "_performanceMonitor", "_errorHandlingUtils", "_jsxRuntime", "_excluded", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "_callSuper", "_getPrototypeOf2", "_possibleConstructorReturn2", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "ErrorDisplay", "_ref", "_colors$background", "_colors$status", "_colors$text", "_colors$text2", "_colors$text3", "_colors$text4", "_colors$text6", "error", "onRetry", "enableRetry", "retryCount", "maxRetries", "_useHighContrastColor", "useHighContrastColors", "colors", "_formatErrorForDispla", "formatErrorForDisplay", "title", "message", "suggestions", "actions", "canRetry", "retryable", "jsx", "View", "style", "styles", "container", "backgroundColor", "background", "primary", "children", "jsxs", "content", "iconContainer", "status", "Body", "color", "text", "inverse", "Heading", "level", "align", "secondary", "length", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>le", "map", "suggestion", "index", "_colors$text5", "actionsContainer", "AnimatedButton", "onPress", "variant", "actionButton", "accessibilityLabel", "accessibilityHint", "action", "label", "tertiary", "errorId", "id", "Error<PERSON>ou<PERSON><PERSON>", "exports", "_Component", "props", "_this", "_classCallCheck2", "resetTimeoutId", "resetErrorBoundary", "clearTimeout", "setState", "createErrorBoundaryState", "handleRetry", "_this$props$maxRetrie", "state", "prevState", "assign", "window", "setTimeout", "_inherits2", "_createClass2", "key", "value", "componentDidCatch", "errorInfo", "appError", "createAppError", "component", "additionalData", "componentStack", "errorBoundary", "logError", "performanceMonitor", "trackUserInteraction", "errorType", "name", "onError", "Platform", "OS", "AccessibilityInfo", "announceForAccessibility", "componentDidUpdate", "prevProps", "_this$props", "resetOnPropsChange", "resetKeys", "<PERSON><PERSON><PERSON><PERSON>", "hasResetKeyChanged", "some", "_prevProps$resetKeys", "componentWillUnmount", "render", "_this$props2", "fallback", "_this$props2$enableRe", "_this$props2$maxRetri", "_this$state", "getDerivedStateFromError", "Component", "ErrorBoundaryWrapper", "_ref2", "_objectWithoutProperties2", "useErrorHandler", "throwError", "errorObj", "Error", "StyleSheet", "create", "flex", "justifyContent", "alignItems", "padding", "max<PERSON><PERSON><PERSON>", "width", "height", "borderRadius", "marginBottom", "lineHeight", "fontWeight", "paddingLeft", "gap", "marginTop", "fontSize", "fontFamily", "_default"], "sources": ["ErrorBoundary.tsx"], "sourcesContent": ["/**\n * Error Boundary Component\n *\n * React Error Boundary component for graceful error handling\n * with user-friendly error displays and recovery options.\n *\n * Features:\n * - Graceful error catching\n * - User-friendly error display\n * - Error recovery actions\n * - Accessibility compliance\n * - Error reporting\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\nimport React, { Component, ReactNode } from 'react';\nimport { View } from 'react-native';\nimport { Platform } from 'react-native';\nimport { StyleSheet, AccessibilityInfo } from 'react-native';\nimport { Heading, Body } from '../typography/Typography';\nimport { AnimatedButton } from '../animation/AnimatedButton';\nimport { useHighContrastColors } from '../../contexts/HighContrastContext';\nimport { performanceMonitor } from '../../services/performanceMonitor';\nimport {\n  createAppError,\n  formatErrorForDisplay,\n  logError,\n  AppError,\n  ErrorBoundaryState,\n  createErrorBoundaryState,\n} from '../../utils/errorHandlingUtils';\n\n// Error boundary props\ninterface ErrorBoundaryProps {\n  children: ReactNode;\n  fallback?: (error: AppError, retry: () => void) => ReactNode;\n  onError?: (error: AppError) => void;\n  enableRetry?: boolean;\n  maxRetries?: number;\n  resetOnPropsChange?: boolean;\n  resetKeys?: Array<string | number>;\n}\n\n// Error display component\ninterface ErrorDisplayProps {\n  error: AppError;\n  onRetry: () => void;\n  enableRetry: boolean;\n  retryCount: number;\n  maxRetries: number;\n}\n\nconst ErrorDisplay: React.FC<ErrorDisplayProps> = ({\n  error,\n  onRetry,\n  enableRetry,\n  retryCount,\n  maxRetries,\n}) => {\n  const { colors } = useHighContrastColors();\n  const { title, message, suggestions, actions } = formatErrorForDisplay(error);\n\n  const canRetry = enableRetry && retryCount < maxRetries && error.retryable;\n\n  return (\n    <View style={[styles.container, { backgroundColor: colors?.background?.primary || '#FFFFFF' }]}>\n      <View style={styles.content}>\n        {/* Error Icon */}\n        <View style={[styles.iconContainer, { backgroundColor: colors?.status?.error || '#FF6B6B' }]}>\n          <Body color={colors?.text?.inverse || '#FFFFFF'}>⚠️</Body>\n        </View>\n\n        {/* Error Title */}\n        <Heading\n          level={2}\n          color={colors?.text?.primary}\n          align=\"center\"\n          style={styles.title}\n        >\n          {title}\n        </Heading>\n\n        {/* Error Message */}\n        <Body\n          color={colors?.text?.secondary}\n          align=\"center\"\n          style={styles.message}\n        >\n          {message}\n        </Body>\n\n        {/* Suggestions */}\n        {suggestions.length > 0 && (\n          <View style={styles.suggestionsContainer}>\n            <Body\n              color={colors?.text?.primary}\n              style={styles.suggestionsTitle}\n            >\n              Try these solutions:\n            </Body>\n            {suggestions.map((suggestion, index) => (\n              <Body\n                key={index}\n                color={colors?.text?.secondary}\n                style={styles.suggestion}\n              >\n                • {suggestion}\n              </Body>\n            ))}\n          </View>\n        )}\n\n        {/* Action Buttons */}\n        <View style={styles.actionsContainer}>\n          {canRetry && (\n            <AnimatedButton\n              title=\"Try Again\"\n              onPress={onRetry}\n              variant=\"primary\"\n              style={styles.actionButton}\n              accessibilityLabel=\"Try again to resolve the error\"\n              accessibilityHint={`Attempt ${retryCount + 1} of ${maxRetries}`}\n            />\n          )}\n          \n          {actions.map((action, index) => (\n            <AnimatedButton\n              key={index}\n              title={action.label}\n              onPress={action.action}\n              variant={action.primary ? 'primary' : 'outline'}\n              style={styles.actionButton}\n              accessibilityLabel={action.label}\n            />\n          ))}\n        </View>\n\n        {/* Error ID for support */}\n        <Body\n          color={colors?.text?.tertiary}\n          align=\"center\"\n          style={styles.errorId}\n        >\n          Error ID: {error.id}\n        </Body>\n      </View>\n    </View>\n  );\n};\n\n// Error boundary class component\nexport class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {\n  private resetTimeoutId: number | null = null;\n\n  constructor(props: ErrorBoundaryProps) {\n    super(props);\n    this.state = createErrorBoundaryState();\n  }\n\n  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {\n    const appError = createAppError(error, {\n      component: 'ErrorBoundary',\n      action: 'render',\n    });\n\n    return {\n      hasError: true,\n      error: appError,\n      errorId: appError.id,\n    };\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    const appError = createAppError(error, {\n      component: 'ErrorBoundary',\n      action: 'componentDidCatch',\n      additionalData: {\n        componentStack: errorInfo.componentStack,\n        errorBoundary: true,\n      },\n    });\n\n    // Log the error\n    logError(appError);\n\n    // Track error in performance monitor\n    performanceMonitor.trackUserInteraction('error_boundary_catch', 0, {\n      error: error.message,\n      errorType: error.name,\n      componentStack: errorInfo.componentStack,\n      errorId: appError.id,\n      retryCount: this.state.retryCount,\n    });\n\n    // Call onError callback if provided\n    if (this.props.onError) {\n      this.props.onError(appError);\n    }\n\n    // Announce error to screen readers\n    if (Platform.OS === 'ios' || Platform.OS === 'android') {\n      AccessibilityInfo.announceForAccessibility(\n        'An error occurred. Please check the error message and try again.'\n      );\n    }\n  }\n\n  componentDidUpdate(prevProps: ErrorBoundaryProps) {\n    const { resetOnPropsChange, resetKeys } = this.props;\n    const { hasError } = this.state;\n\n    // Reset error state if props changed and resetOnPropsChange is enabled\n    if (hasError && resetOnPropsChange && resetKeys) {\n      const hasResetKeyChanged = resetKeys.some(\n        (key, index) => key !== (prevProps.resetKeys?.[index])\n      );\n\n      if (hasResetKeyChanged) {\n        this.resetErrorBoundary();\n      }\n    }\n  }\n\n  componentWillUnmount() {\n    if (this.resetTimeoutId) {\n      clearTimeout(this.resetTimeoutId);\n    }\n  }\n\n  resetErrorBoundary = () => {\n    if (this.resetTimeoutId) {\n      clearTimeout(this.resetTimeoutId);\n    }\n\n    this.setState(createErrorBoundaryState());\n  };\n\n  handleRetry = () => {\n    const { maxRetries = 3 } = this.props;\n    const { retryCount } = this.state;\n\n    if (retryCount < maxRetries) {\n      this.setState(prevState => ({\n        ...prevState,\n        retryCount: prevState.retryCount + 1,\n      }));\n\n      // Reset error state after a short delay\n      this.resetTimeoutId = window.setTimeout(() => {\n        this.resetErrorBoundary();\n      }, 100);\n    }\n  };\n\n  render() {\n    const { children, fallback, enableRetry = true, maxRetries = 3 } = this.props;\n    const { hasError, error, retryCount } = this.state;\n\n    if (hasError && error) {\n      // Use custom fallback if provided\n      if (fallback) {\n        return fallback(error, this.handleRetry);\n      }\n\n      // Use default error display\n      return (\n        <ErrorDisplay\n          error={error}\n          onRetry={this.handleRetry}\n          enableRetry={enableRetry}\n          retryCount={retryCount}\n          maxRetries={maxRetries}\n        />\n      );\n    }\n\n    return children;\n  }\n}\n\n// Functional wrapper for easier usage with hooks\ninterface ErrorBoundaryWrapperProps extends Omit<ErrorBoundaryProps, 'children'> {\n  children: ReactNode;\n}\n\nexport const ErrorBoundaryWrapper: React.FC<ErrorBoundaryWrapperProps> = ({\n  children,\n  ...props\n}) => {\n  return (\n    <ErrorBoundary {...props}>\n      {children}\n    </ErrorBoundary>\n  );\n};\n\n// Hook for manual error throwing\nexport const useErrorHandler = () => {\n  const throwError = (error: Error | string) => {\n    const errorObj = typeof error === 'string' ? new Error(error) : error;\n    throw errorObj;\n  };\n\n  return { throwError };\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    justifyContent: 'center',\n    alignItems: 'center',\n    padding: 20,\n  },\n  content: {\n    maxWidth: 400,\n    width: '100%',\n    alignItems: 'center',\n  },\n  iconContainer: {\n    width: 80,\n    height: 80,\n    borderRadius: 40,\n    justifyContent: 'center',\n    alignItems: 'center',\n    marginBottom: 24,\n  },\n  title: {\n    marginBottom: 16,\n  },\n  message: {\n    marginBottom: 24,\n    lineHeight: 24,\n  },\n  suggestionsContainer: {\n    width: '100%',\n    marginBottom: 32,\n    padding: 16,\n    backgroundColor: '#F8F9FA',\n    borderRadius: 8,\n  },\n  suggestionsTitle: {\n    marginBottom: 12,\n    fontWeight: '600',\n  },\n  suggestion: {\n    marginBottom: 8,\n    paddingLeft: 8,\n  },\n  actionsContainer: {\n    width: '100%',\n    gap: 12,\n  },\n  actionButton: {\n    width: '100%',\n  },\n  errorId: {\n    marginTop: 24,\n    fontSize: 12,\n    fontFamily: 'monospace',\n  },\n});\n\nexport default ErrorBoundary;\n"], "mappings": ";;;;;;;;;;;AAiBA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAGA,IAAAE,WAAA,GAAAF,OAAA;AACA,IAAAG,eAAA,GAAAH,OAAA;AACA,IAAAI,oBAAA,GAAAJ,OAAA;AACA,IAAAK,mBAAA,GAAAL,OAAA;AACA,IAAAM,mBAAA,GAAAN,OAAA;AAOwC,IAAAO,WAAA,GAAAP,OAAA;AAAA,IAAAQ,SAAA;AAAA,SAAAT,wBAAAU,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAZ,uBAAA,YAAAA,wBAAAU,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAAA,SAAAmB,WAAAnB,CAAA,EAAAK,CAAA,EAAAN,CAAA,WAAAM,CAAA,OAAAe,gBAAA,CAAAX,OAAA,EAAAJ,CAAA,OAAAgB,2BAAA,CAAAZ,OAAA,EAAAT,CAAA,EAAAsB,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAnB,CAAA,EAAAN,CAAA,YAAAqB,gBAAA,CAAAX,OAAA,EAAAT,CAAA,EAAAyB,WAAA,IAAApB,CAAA,CAAAqB,KAAA,CAAA1B,CAAA,EAAAD,CAAA;AAAA,SAAAuB,0BAAA,cAAAtB,CAAA,IAAA2B,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAd,IAAA,CAAAQ,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAA3B,CAAA,aAAAsB,yBAAA,YAAAA,0BAAA,aAAAtB,CAAA;AAsBxC,IAAM8B,YAAyC,GAAG,SAA5CA,YAAyCA,CAAAC,IAAA,EAMzC;EAAA,IAAAC,kBAAA,EAAAC,cAAA,EAAAC,YAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA;EAAA,IALJC,KAAK,GAAAR,IAAA,CAALQ,KAAK;IACLC,OAAO,GAAAT,IAAA,CAAPS,OAAO;IACPC,WAAW,GAAAV,IAAA,CAAXU,WAAW;IACXC,UAAU,GAAAX,IAAA,CAAVW,UAAU;IACVC,UAAU,GAAAZ,IAAA,CAAVY,UAAU;EAEV,IAAAC,qBAAA,GAAmB,IAAAC,0CAAqB,EAAC,CAAC;IAAlCC,MAAM,GAAAF,qBAAA,CAANE,MAAM;EACd,IAAAC,qBAAA,GAAiD,IAAAC,yCAAqB,EAACT,KAAK,CAAC;IAArEU,KAAK,GAAAF,qBAAA,CAALE,KAAK;IAAEC,OAAO,GAAAH,qBAAA,CAAPG,OAAO;IAAEC,WAAW,GAAAJ,qBAAA,CAAXI,WAAW;IAAEC,OAAO,GAAAL,qBAAA,CAAPK,OAAO;EAE5C,IAAMC,QAAQ,GAAGZ,WAAW,IAAIC,UAAU,GAAGC,UAAU,IAAIJ,KAAK,CAACe,SAAS;EAE1E,OACE,IAAAzD,WAAA,CAAA0D,GAAA,EAAChE,YAAA,CAAAiE,IAAI;IAACC,KAAK,EAAE,CAACC,MAAM,CAACC,SAAS,EAAE;MAAEC,eAAe,EAAE,CAAAd,MAAM,aAAAd,kBAAA,GAANc,MAAM,CAAEe,UAAU,qBAAlB7B,kBAAA,CAAoB8B,OAAO,KAAI;IAAU,CAAC,CAAE;IAAAC,QAAA,EAC7F,IAAAlE,WAAA,CAAAmE,IAAA,EAACzE,YAAA,CAAAiE,IAAI;MAACC,KAAK,EAAEC,MAAM,CAACO,OAAQ;MAAAF,QAAA,GAE1B,IAAAlE,WAAA,CAAA0D,GAAA,EAAChE,YAAA,CAAAiE,IAAI;QAACC,KAAK,EAAE,CAACC,MAAM,CAACQ,aAAa,EAAE;UAAEN,eAAe,EAAE,CAAAd,MAAM,aAAAb,cAAA,GAANa,MAAM,CAAEqB,MAAM,qBAAdlC,cAAA,CAAgBM,KAAK,KAAI;QAAU,CAAC,CAAE;QAAAwB,QAAA,EAC3F,IAAAlE,WAAA,CAAA0D,GAAA,EAAC/D,WAAA,CAAA4E,IAAI;UAACC,KAAK,EAAE,CAAAvB,MAAM,aAAAZ,YAAA,GAANY,MAAM,CAAEwB,IAAI,qBAAZpC,YAAA,CAAcqC,OAAO,KAAI,SAAU;UAAAR,QAAA,EAAC;QAAE,CAAM;MAAC,CACtD,CAAC,EAGP,IAAAlE,WAAA,CAAA0D,GAAA,EAAC/D,WAAA,CAAAgF,OAAO;QACNC,KAAK,EAAE,CAAE;QACTJ,KAAK,EAAEvB,MAAM,aAAAX,aAAA,GAANW,MAAM,CAAEwB,IAAI,qBAAZnC,aAAA,CAAc2B,OAAQ;QAC7BY,KAAK,EAAC,QAAQ;QACdjB,KAAK,EAAEC,MAAM,CAACT,KAAM;QAAAc,QAAA,EAEnBd;MAAK,CACC,CAAC,EAGV,IAAApD,WAAA,CAAA0D,GAAA,EAAC/D,WAAA,CAAA4E,IAAI;QACHC,KAAK,EAAEvB,MAAM,aAAAV,aAAA,GAANU,MAAM,CAAEwB,IAAI,qBAAZlC,aAAA,CAAcuC,SAAU;QAC/BD,KAAK,EAAC,QAAQ;QACdjB,KAAK,EAAEC,MAAM,CAACR,OAAQ;QAAAa,QAAA,EAErBb;MAAO,CACJ,CAAC,EAGNC,WAAW,CAACyB,MAAM,GAAG,CAAC,IACrB,IAAA/E,WAAA,CAAAmE,IAAA,EAACzE,YAAA,CAAAiE,IAAI;QAACC,KAAK,EAAEC,MAAM,CAACmB,oBAAqB;QAAAd,QAAA,GACvC,IAAAlE,WAAA,CAAA0D,GAAA,EAAC/D,WAAA,CAAA4E,IAAI;UACHC,KAAK,EAAEvB,MAAM,aAAAT,aAAA,GAANS,MAAM,CAAEwB,IAAI,qBAAZjC,aAAA,CAAcyB,OAAQ;UAC7BL,KAAK,EAAEC,MAAM,CAACoB,gBAAiB;UAAAf,QAAA,EAChC;QAED,CAAM,CAAC,EACNZ,WAAW,CAAC4B,GAAG,CAAC,UAACC,UAAU,EAAEC,KAAK;UAAA,IAAAC,aAAA;UAAA,OACjC,IAAArF,WAAA,CAAAmE,IAAA,EAACxE,WAAA,CAAA4E,IAAI;YAEHC,KAAK,EAAEvB,MAAM,aAAAoC,aAAA,GAANpC,MAAM,CAAEwB,IAAI,qBAAZY,aAAA,CAAcP,SAAU;YAC/BlB,KAAK,EAAEC,MAAM,CAACsB,UAAW;YAAAjB,QAAA,GAC1B,SACG,EAACiB,UAAU;UAAA,GAJRC,KAKD,CAAC;QAAA,CACR,CAAC;MAAA,CACE,CACP,EAGD,IAAApF,WAAA,CAAAmE,IAAA,EAACzE,YAAA,CAAAiE,IAAI;QAACC,KAAK,EAAEC,MAAM,CAACyB,gBAAiB;QAAApB,QAAA,GAClCV,QAAQ,IACP,IAAAxD,WAAA,CAAA0D,GAAA,EAAC9D,eAAA,CAAA2F,cAAc;UACbnC,KAAK,EAAC,WAAW;UACjBoC,OAAO,EAAE7C,OAAQ;UACjB8C,OAAO,EAAC,SAAS;UACjB7B,KAAK,EAAEC,MAAM,CAAC6B,YAAa;UAC3BC,kBAAkB,EAAC,gCAAgC;UACnDC,iBAAiB,EAAE,WAAW/C,UAAU,GAAG,CAAC,OAAOC,UAAU;QAAG,CACjE,CACF,EAEAS,OAAO,CAAC2B,GAAG,CAAC,UAACW,MAAM,EAAET,KAAK;UAAA,OACzB,IAAApF,WAAA,CAAA0D,GAAA,EAAC9D,eAAA,CAAA2F,cAAc;YAEbnC,KAAK,EAAEyC,MAAM,CAACC,KAAM;YACpBN,OAAO,EAAEK,MAAM,CAACA,MAAO;YACvBJ,OAAO,EAAEI,MAAM,CAAC5B,OAAO,GAAG,SAAS,GAAG,SAAU;YAChDL,KAAK,EAAEC,MAAM,CAAC6B,YAAa;YAC3BC,kBAAkB,EAAEE,MAAM,CAACC;UAAM,GAL5BV,KAMN,CAAC;QAAA,CACH,CAAC;MAAA,CACE,CAAC,EAGP,IAAApF,WAAA,CAAAmE,IAAA,EAACxE,WAAA,CAAA4E,IAAI;QACHC,KAAK,EAAEvB,MAAM,aAAAR,aAAA,GAANQ,MAAM,CAAEwB,IAAI,qBAAZhC,aAAA,CAAcsD,QAAS;QAC9BlB,KAAK,EAAC,QAAQ;QACdjB,KAAK,EAAEC,MAAM,CAACmC,OAAQ;QAAA9B,QAAA,GACvB,YACW,EAACxB,KAAK,CAACuD,EAAE;MAAA,CACf,CAAC;IAAA,CACH;EAAC,CACH,CAAC;AAEX,CAAC;AAAC,IAGWC,aAAa,GAAAC,OAAA,CAAAD,aAAA,aAAAE,UAAA;EAGxB,SAAAF,cAAYG,KAAyB,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAA3F,OAAA,QAAAsF,aAAA;IACrCI,KAAA,GAAAhF,UAAA,OAAA4E,aAAA,GAAMG,KAAK;IAAEC,KAAA,CAHPE,cAAc,GAAkB,IAAI;IAAAF,KAAA,CA6E5CG,kBAAkB,GAAG,YAAM;MACzB,IAAIH,KAAA,CAAKE,cAAc,EAAE;QACvBE,YAAY,CAACJ,KAAA,CAAKE,cAAc,CAAC;MACnC;MAEAF,KAAA,CAAKK,QAAQ,CAAC,IAAAC,4CAAwB,EAAC,CAAC,CAAC;IAC3C,CAAC;IAAAN,KAAA,CAEDO,WAAW,GAAG,YAAM;MAClB,IAAAC,qBAAA,GAA2BR,KAAA,CAAKD,KAAK,CAA7BvD,UAAU;QAAVA,UAAU,GAAAgE,qBAAA,cAAG,CAAC,GAAAA,qBAAA;MACtB,IAAQjE,UAAU,GAAKyD,KAAA,CAAKS,KAAK,CAAzBlE,UAAU;MAElB,IAAIA,UAAU,GAAGC,UAAU,EAAE;QAC3BwD,KAAA,CAAKK,QAAQ,CAAC,UAAAK,SAAS;UAAA,OAAA7F,MAAA,CAAA8F,MAAA,KAClBD,SAAS;YACZnE,UAAU,EAAEmE,SAAS,CAACnE,UAAU,GAAG;UAAC;QAAA,CACpC,CAAC;QAGHyD,KAAA,CAAKE,cAAc,GAAGU,MAAM,CAACC,UAAU,CAAC,YAAM;UAC5Cb,KAAA,CAAKG,kBAAkB,CAAC,CAAC;QAC3B,CAAC,EAAE,GAAG,CAAC;MACT;IACF,CAAC;IAhGCH,KAAA,CAAKS,KAAK,GAAG,IAAAH,4CAAwB,EAAC,CAAC;IAAC,OAAAN,KAAA;EAC1C;EAAC,IAAAc,UAAA,CAAAxG,OAAA,EAAAsF,aAAA,EAAAE,UAAA;EAAA,WAAAiB,aAAA,CAAAzG,OAAA,EAAAsF,aAAA;IAAAoB,GAAA;IAAAC,KAAA,EAeD,SAAAC,iBAAiBA,CAAC9E,KAAY,EAAE+E,SAA0B,EAAE;MAC1D,IAAMC,QAAQ,GAAG,IAAAC,kCAAc,EAACjF,KAAK,EAAE;QACrCkF,SAAS,EAAE,eAAe;QAC1B/B,MAAM,EAAE,mBAAmB;QAC3BgC,cAAc,EAAE;UACdC,cAAc,EAAEL,SAAS,CAACK,cAAc;UACxCC,aAAa,EAAE;QACjB;MACF,CAAC,CAAC;MAGF,IAAAC,4BAAQ,EAACN,QAAQ,CAAC;MAGlBO,sCAAkB,CAACC,oBAAoB,CAAC,sBAAsB,EAAE,CAAC,EAAE;QACjExF,KAAK,EAAEA,KAAK,CAACW,OAAO;QACpB8E,SAAS,EAAEzF,KAAK,CAAC0F,IAAI;QACrBN,cAAc,EAAEL,SAAS,CAACK,cAAc;QACxC9B,OAAO,EAAE0B,QAAQ,CAACzB,EAAE;QACpBpD,UAAU,EAAE,IAAI,CAACkE,KAAK,CAAClE;MACzB,CAAC,CAAC;MAGF,IAAI,IAAI,CAACwD,KAAK,CAACgC,OAAO,EAAE;QACtB,IAAI,CAAChC,KAAK,CAACgC,OAAO,CAACX,QAAQ,CAAC;MAC9B;MAGA,IAAIY,qBAAQ,CAACC,EAAE,KAAK,KAAK,IAAID,qBAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;QACtDC,8BAAiB,CAACC,wBAAwB,CACxC,kEACF,CAAC;MACH;IACF;EAAC;IAAAnB,GAAA;IAAAC,KAAA,EAED,SAAAmB,kBAAkBA,CAACC,SAA6B,EAAE;MAChD,IAAAC,WAAA,GAA0C,IAAI,CAACvC,KAAK;QAA5CwC,kBAAkB,GAAAD,WAAA,CAAlBC,kBAAkB;QAAEC,SAAS,GAAAF,WAAA,CAATE,SAAS;MACrC,IAAQC,QAAQ,GAAK,IAAI,CAAChC,KAAK,CAAvBgC,QAAQ;MAGhB,IAAIA,QAAQ,IAAIF,kBAAkB,IAAIC,SAAS,EAAE;QAC/C,IAAME,kBAAkB,GAAGF,SAAS,CAACG,IAAI,CACvC,UAAC3B,GAAG,EAAElC,KAAK;UAAA,IAAA8D,oBAAA;UAAA,OAAK5B,GAAG,OAAA4B,oBAAA,GAAMP,SAAS,CAACG,SAAS,qBAAnBI,oBAAA,CAAsB9D,KAAK,CAAC,CAAC;QAAA,CACxD,CAAC;QAED,IAAI4D,kBAAkB,EAAE;UACtB,IAAI,CAACvC,kBAAkB,CAAC,CAAC;QAC3B;MACF;IACF;EAAC;IAAAa,GAAA;IAAAC,KAAA,EAED,SAAA4B,oBAAoBA,CAAA,EAAG;MACrB,IAAI,IAAI,CAAC3C,cAAc,EAAE;QACvBE,YAAY,CAAC,IAAI,CAACF,cAAc,CAAC;MACnC;IACF;EAAC;IAAAc,GAAA;IAAAC,KAAA,EA2BD,SAAA6B,MAAMA,CAAA,EAAG;MACP,IAAAC,YAAA,GAAmE,IAAI,CAAChD,KAAK;QAArEnC,QAAQ,GAAAmF,YAAA,CAARnF,QAAQ;QAAEoF,QAAQ,GAAAD,YAAA,CAARC,QAAQ;QAAAC,qBAAA,GAAAF,YAAA,CAAEzG,WAAW;QAAXA,WAAW,GAAA2G,qBAAA,cAAG,IAAI,GAAAA,qBAAA;QAAAC,qBAAA,GAAAH,YAAA,CAAEvG,UAAU;QAAVA,UAAU,GAAA0G,qBAAA,cAAG,CAAC,GAAAA,qBAAA;MAC9D,IAAAC,WAAA,GAAwC,IAAI,CAAC1C,KAAK;QAA1CgC,QAAQ,GAAAU,WAAA,CAARV,QAAQ;QAAErG,KAAK,GAAA+G,WAAA,CAAL/G,KAAK;QAAEG,UAAU,GAAA4G,WAAA,CAAV5G,UAAU;MAEnC,IAAIkG,QAAQ,IAAIrG,KAAK,EAAE;QAErB,IAAI4G,QAAQ,EAAE;UACZ,OAAOA,QAAQ,CAAC5G,KAAK,EAAE,IAAI,CAACmE,WAAW,CAAC;QAC1C;QAGA,OACE,IAAA7G,WAAA,CAAA0D,GAAA,EAACzB,YAAY;UACXS,KAAK,EAAEA,KAAM;UACbC,OAAO,EAAE,IAAI,CAACkE,WAAY;UAC1BjE,WAAW,EAAEA,WAAY;UACzBC,UAAU,EAAEA,UAAW;UACvBC,UAAU,EAAEA;QAAW,CACxB,CAAC;MAEN;MAEA,OAAOoB,QAAQ;IACjB;EAAC;IAAAoD,GAAA;IAAAC,KAAA,EAtHD,SAAOmC,wBAAwBA,CAAChH,KAAY,EAA+B;MACzE,IAAMgF,QAAQ,GAAG,IAAAC,kCAAc,EAACjF,KAAK,EAAE;QACrCkF,SAAS,EAAE,eAAe;QAC1B/B,MAAM,EAAE;MACV,CAAC,CAAC;MAEF,OAAO;QACLkD,QAAQ,EAAE,IAAI;QACdrG,KAAK,EAAEgF,QAAQ;QACf1B,OAAO,EAAE0B,QAAQ,CAACzB;MACpB,CAAC;IACH;EAAC;AAAA,EAnBgC0D,gBAAS;AAsIrC,IAAMC,oBAAyD,GAAAzD,OAAA,CAAAyD,oBAAA,GAAG,SAA5DA,oBAAyDA,CAAAC,KAAA,EAGhE;EAAA,IAFJ3F,QAAQ,GAAA2F,KAAA,CAAR3F,QAAQ;IACLmC,KAAK,OAAAyD,yBAAA,CAAAlJ,OAAA,EAAAiJ,KAAA,EAAA5J,SAAA;EAER,OACE,IAAAD,WAAA,CAAA0D,GAAA,EAACwC,aAAa,EAAA/E,MAAA,CAAA8F,MAAA,KAAKZ,KAAK;IAAAnC,QAAA,EACrBA;EAAQ,EACI,CAAC;AAEpB,CAAC;AAGM,IAAM6F,eAAe,GAAA5D,OAAA,CAAA4D,eAAA,GAAG,SAAlBA,eAAeA,CAAA,EAAS;EACnC,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAItH,KAAqB,EAAK;IAC5C,IAAMuH,QAAQ,GAAG,OAAOvH,KAAK,KAAK,QAAQ,GAAG,IAAIwH,KAAK,CAACxH,KAAK,CAAC,GAAGA,KAAK;IACrE,MAAMuH,QAAQ;EAChB,CAAC;EAED,OAAO;IAAED,UAAU,EAAVA;EAAW,CAAC;AACvB,CAAC;AAED,IAAMnG,MAAM,GAAGsG,uBAAU,CAACC,MAAM,CAAC;EAC/BtG,SAAS,EAAE;IACTuG,IAAI,EAAE,CAAC;IACPC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,QAAQ;IACpBC,OAAO,EAAE;EACX,CAAC;EACDpG,OAAO,EAAE;IACPqG,QAAQ,EAAE,GAAG;IACbC,KAAK,EAAE,MAAM;IACbH,UAAU,EAAE;EACd,CAAC;EACDlG,aAAa,EAAE;IACbqG,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,YAAY,EAAE,EAAE;IAChBN,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,QAAQ;IACpBM,YAAY,EAAE;EAChB,CAAC;EACDzH,KAAK,EAAE;IACLyH,YAAY,EAAE;EAChB,CAAC;EACDxH,OAAO,EAAE;IACPwH,YAAY,EAAE,EAAE;IAChBC,UAAU,EAAE;EACd,CAAC;EACD9F,oBAAoB,EAAE;IACpB0F,KAAK,EAAE,MAAM;IACbG,YAAY,EAAE,EAAE;IAChBL,OAAO,EAAE,EAAE;IACXzG,eAAe,EAAE,SAAS;IAC1B6G,YAAY,EAAE;EAChB,CAAC;EACD3F,gBAAgB,EAAE;IAChB4F,YAAY,EAAE,EAAE;IAChBE,UAAU,EAAE;EACd,CAAC;EACD5F,UAAU,EAAE;IACV0F,YAAY,EAAE,CAAC;IACfG,WAAW,EAAE;EACf,CAAC;EACD1F,gBAAgB,EAAE;IAChBoF,KAAK,EAAE,MAAM;IACbO,GAAG,EAAE;EACP,CAAC;EACDvF,YAAY,EAAE;IACZgF,KAAK,EAAE;EACT,CAAC;EACD1E,OAAO,EAAE;IACPkF,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AAAC,IAAAC,QAAA,GAAAlF,OAAA,CAAAvF,OAAA,GAEYsF,aAAa", "ignoreList": []}