/**
 * Test Accounts Panel - Development Helper Component
 *
 * Component Contract:
 * - Provides easy access to test accounts during development
 * - Shows quick login buttons for different account types
 * - Displays account statistics and information
 * - Only renders in development mode
 * - Integrates with test accounts service
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity } from 'react-native';
import { StyleSheet, Alert, Modal,  } from 'react-native';

import { QUICK_LOGIN_ACCOUNTS } from '../../config/testAccounts';
import { Colors } from '../../constants/Colors';
import {
  testAccountsService,
  TestLoginResult,
} from '../../services/testAccountsService';
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
} from '../../utils/responsiveUtils';

interface TestAccountsPanelProps {
  visible: boolean;
  onClose: () => void;
  onLoginSuccess?: (result: TestLoginResult) => void;
}

export const TestAccountsPanel: React.FC<TestAccountsPanelProps> = ({
  visible,
  onClose,
  onLoginSuccess,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [stats, setStats] = useState(testAccountsService.getAccountsStats());

  useEffect(() => {
    if (visible) {
      // Log test accounts summary when panel opens
      testAccountsService.logTestAccountsSummary();
      setStats(testAccountsService.getAccountsStats());
    }
  }, [visible]);

  const handleQuickLogin = async (
    accountType: keyof typeof QUICK_LOGIN_ACCOUNTS,
  ) => {
    setIsLoading(true);

    try {
      const result = await testAccountsService.quickLogin(accountType);

      if (result.success && result.account) {
        Alert.alert(
          'Login Successful',
          `Logged in as ${result.account.firstName || 'Unknown'} ${result.account.lastName || 'User'}\n(${result.account.email || 'No email'})`,
          [
            {
              text: 'OK',
              onPress: () => {
                onLoginSuccess?.(result);
                onClose();
              },
            },
          ],
        );
      } else {
        Alert.alert('Login Failed', result.error || 'Unknown error occurred');
      }
    } catch (error) {
      Alert.alert(
        'Login Error',
        error instanceof Error ? error.message : 'Unknown error',
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleRandomLogin = async (role?: 'customer' | 'service_provider') => {
    setIsLoading(true);

    try {
      const result = await testAccountsService.loginWithRandomAccount(role);

      if (result.success && result.account) {
        Alert.alert(
          'Random Login Successful',
          `Logged in as ${result.account.firstName || 'Unknown'} ${result.account.lastName || 'User'}\n(${result.account.email || 'No email'})`,
          [
            {
              text: 'OK',
              onPress: () => {
                onLoginSuccess?.(result);
                onClose();
              },
            },
          ],
        );
      } else {
        Alert.alert('Login Failed', result.error || 'Unknown error occurred');
      }
    } catch (error) {
      Alert.alert(
        'Login Error',
        error instanceof Error ? error.message : 'Unknown error',
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Don't render in production
  if (!__DEV__) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}>
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>🧪 Test Accounts Panel</Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Text style={styles.closeButtonText}>✕</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Statistics Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>📊 Account Statistics</Text>
            <View style={styles.statsContainer}>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{stats.totalAccounts}</Text>
                <Text style={styles.statLabel}>Total Accounts</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{stats.customerAccounts}</Text>
                <Text style={styles.statLabel}>Customers</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{stats.providerAccounts}</Text>
                <Text style={styles.statLabel}>Providers</Text>
              </View>
            </View>
          </View>

          {/* Quick Login Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>⚡ Quick Login</Text>

            <TouchableOpacity
              style={[styles.loginButton, styles.customerButton]}
              onPress={() => handleQuickLogin('CUSTOMER')}
              disabled={isLoading}>
              <Text style={styles.loginButtonText}>👤 Login as Customer</Text>
              <Text style={styles.loginButtonSubtext}>
                {QUICK_LOGIN_ACCOUNTS.CUSTOMER?.email || 'No email'}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.loginButton, styles.providerButton]}
              onPress={() => handleQuickLogin('BARBER_PROVIDER')}
              disabled={isLoading}>
              <Text style={styles.loginButtonText}>
                ✂️ Barber Provider
              </Text>
              <Text style={styles.loginButtonSubtext}>
                {QUICK_LOGIN_ACCOUNTS.BARBER_PROVIDER?.email || 'No email'}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.loginButton, styles.providerButton]}
              onPress={() => handleQuickLogin('SALON_PROVIDER')}
              disabled={isLoading}>
              <Text style={styles.loginButtonText}>
                💇‍♀️ Salon Provider
              </Text>
              <Text style={styles.loginButtonSubtext}>
                {QUICK_LOGIN_ACCOUNTS.SALON_PROVIDER?.email || 'No email'}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.loginButton, styles.providerButton]}
              onPress={() => handleQuickLogin('NAIL_PROVIDER')}
              disabled={isLoading}>
              <Text style={styles.loginButtonText}>
                💅 Nail Services Provider
              </Text>
              <Text style={styles.loginButtonSubtext}>
                {QUICK_LOGIN_ACCOUNTS.NAIL_PROVIDER?.email || 'No email'}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.loginButton, styles.providerButton]}
              onPress={() => handleQuickLogin('LASH_PROVIDER')}
              disabled={isLoading}>
              <Text style={styles.loginButtonText}>
                👁️ Lash Services Provider
              </Text>
              <Text style={styles.loginButtonSubtext}>
                {QUICK_LOGIN_ACCOUNTS.LASH_PROVIDER?.email || 'No email'}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Random Login Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>🎲 Random Login</Text>

            <TouchableOpacity
              style={[styles.loginButton, styles.randomButton]}
              onPress={() => handleRandomLogin()}
              disabled={isLoading}>
              <Text style={styles.loginButtonText}>🎯 Random Account</Text>
              <Text style={styles.loginButtonSubtext}>
                Any role, any category
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.loginButton, styles.randomButton]}
              onPress={() => handleRandomLogin('customer')}
              disabled={isLoading}>
              <Text style={styles.loginButtonText}>👥 Random Customer</Text>
              <Text style={styles.loginButtonSubtext}>
                Customer accounts only
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.loginButton, styles.randomButton]}
              onPress={() => handleRandomLogin('service_provider')}
              disabled={isLoading}>
              <Text style={styles.loginButtonText}>🏪 Random Provider</Text>
              <Text style={styles.loginButtonSubtext}>
                Service provider accounts only
              </Text>
            </TouchableOpacity>
          </View>

          {/* Categories Breakdown */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>📂 Categories Breakdown</Text>
            {Object.entries(stats.categoriesBreakdown).map(
              ([category, count]) => (
                <View key={category} style={styles.categoryItem}>
                  <Text style={styles.categoryName}>{category}</Text>
                  <Text style={styles.categoryCount}>{count} providers</Text>
                </View>
              ),
            )}
          </View>

          {/* Cities Breakdown */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>🏙️ Cities Breakdown</Text>
            {Object.entries(stats.citiesBreakdown).map(([city, count]) => (
              <View key={city} style={styles.categoryItem}>
                <Text style={styles.categoryName}>{city}</Text>
                <Text style={styles.categoryCount}>{count} accounts</Text>
              </View>
            ))}
          </View>

          {/* Footer */}
          <View style={styles.footer}>
            <Text style={styles.footerText}>🔧 Development Mode Only</Text>
            <Text style={styles.footerSubtext}>
              This panel is only available in development builds
            </Text>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: getResponsiveSpacing(20),
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.primary,
  },
  title: {
    fontSize: getResponsiveFontSize(20),
    fontWeight: 'bold',
    color: Colors.text.primary,
  },
  closeButton: {
    padding: getResponsiveSpacing(8),
  },
  closeButtonText: {
    fontSize: getResponsiveFontSize(18),
    color: Colors.text.secondary,
  },
  content: {
    flex: 1,
    padding: getResponsiveSpacing(20),
  },
  section: {
    marginBottom: getResponsiveSpacing(24),
  },
  sectionTitle: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: getResponsiveSpacing(12),
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: Colors.surface.secondary,
    borderRadius: getResponsiveSpacing(12),
    padding: getResponsiveSpacing(16),
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: getResponsiveFontSize(24),
    fontWeight: 'bold',
    color: Colors.sage600,
  },
  statLabel: {
    fontSize: getResponsiveFontSize(12),
    color: Colors.text.secondary,
    marginTop: getResponsiveSpacing(4),
  },
  loginButton: {
    padding: getResponsiveSpacing(16),
    borderRadius: getResponsiveSpacing(12),
    marginBottom: getResponsiveSpacing(12),
  },
  customerButton: {
    backgroundColor: Colors.sage100,
    borderWidth: 1,
    borderColor: Colors.sage300,
  },
  providerButton: {
    backgroundColor: Colors.sage200,
    borderWidth: 1,
    borderColor: Colors.sage400,
  },
  randomButton: {
    backgroundColor: Colors.surface.tertiary,
    borderWidth: 1,
    borderColor: Colors.border.secondary,
  },
  loginButtonText: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: getResponsiveSpacing(4),
  },
  loginButtonSubtext: {
    fontSize: getResponsiveFontSize(12),
    color: Colors.text.secondary,
  },
  categoryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: getResponsiveSpacing(8),
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.light,
  },
  categoryName: {
    fontSize: getResponsiveFontSize(14),
    color: Colors.text.primary,
  },
  categoryCount: {
    fontSize: getResponsiveFontSize(14),
    color: Colors.text.secondary,
    fontWeight: '500',
  },
  footer: {
    alignItems: 'center',
    paddingVertical: getResponsiveSpacing(24),
  },
  footerText: {
    fontSize: getResponsiveFontSize(14),
    fontWeight: '600',
    color: Colors.sage600,
  },
  footerSubtext: {
    fontSize: getResponsiveFontSize(12),
    color: Colors.text.tertiary,
    marginTop: getResponsiveSpacing(4),
  },
});
