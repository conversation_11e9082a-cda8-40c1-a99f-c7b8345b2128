6f3417bec41203ca7205b8e46af1c95c
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.NetworkErrorFallback = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _react = _interopRequireWildcard(require("react"));
var _reactNative = require("react-native");
var _vectorIcons = require("@expo/vector-icons");
var _ThemeContext = require("../../contexts/ThemeContext");
var _performanceMonitor = require("../../services/performanceMonitor");
var _jsxRuntime = require("react/jsx-runtime");
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var NetworkErrorFallback = exports.NetworkErrorFallback = function NetworkErrorFallback(_ref) {
  var error = _ref.error,
    onRetry = _ref.onRetry,
    onOfflineMode = _ref.onOfflineMode,
    _ref$retryCount = _ref.retryCount,
    retryCount = _ref$retryCount === void 0 ? 0 : _ref$retryCount,
    _ref$maxRetries = _ref.maxRetries,
    maxRetries = _ref$maxRetries === void 0 ? 3 : _ref$maxRetries,
    _ref$showOfflineOptio = _ref.showOfflineOption,
    showOfflineOption = _ref$showOfflineOptio === void 0 ? true : _ref$showOfflineOptio,
    customMessage = _ref.customMessage,
    _ref$testID = _ref.testID,
    testID = _ref$testID === void 0 ? 'network-error-fallback' : _ref$testID;
  var _useTheme = (0, _ThemeContext.useTheme)(),
    colors = _useTheme.colors;
  var _useState = (0, _react.useState)(false),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    isRetrying = _useState2[0],
    setIsRetrying = _useState2[1];
  var _useState3 = (0, _react.useState)({
      isConnected: true,
      connectionType: 'unknown',
      isInternetReachable: true
    }),
    _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
    networkStatus = _useState4[0],
    setNetworkStatus = _useState4[1];
  var getErrorInfo = function getErrorInfo() {
    if (customMessage) {
      return {
        title: 'Connection Issue',
        message: customMessage,
        icon: 'cloud-offline-outline'
      };
    }
    if (error != null && error.message.includes('timeout')) {
      return {
        title: 'Request Timeout',
        message: 'The request took too long to complete. Please check your connection and try again.',
        icon: 'time-outline'
      };
    }
    if (error != null && error.message.includes('Network request failed')) {
      return {
        title: 'Network Error',
        message: 'Unable to connect to our servers. Please check your internet connection.',
        icon: 'wifi-outline'
      };
    }
    if (error != null && error.message.includes('404')) {
      return {
        title: 'Service Unavailable',
        message: 'The requested service is temporarily unavailable. Please try again later.',
        icon: 'server-outline'
      };
    }
    if (error != null && error.message.includes('500')) {
      return {
        title: 'Server Error',
        message: 'Our servers are experiencing issues. Please try again in a few moments.',
        icon: 'warning-outline'
      };
    }
    return {
      title: 'Connection Problem',
      message: 'Something went wrong with the connection. Please try again.',
      icon: 'cloud-offline-outline'
    };
  };
  var errorInfo = getErrorInfo();
  var canRetry = retryCount < maxRetries;
  var handleRetry = function () {
    var _ref2 = (0, _asyncToGenerator2.default)(function* () {
      if (!canRetry || isRetrying) return;
      setIsRetrying(true);
      _performanceMonitor.performanceMonitor.trackUserInteraction('network_error_retry', 0, {
        errorType: (error == null ? void 0 : error.name) || 'NetworkError',
        retryCount: retryCount + 1,
        errorMessage: error == null ? void 0 : error.message
      });
      try {
        yield onRetry == null ? void 0 : onRetry();
      } finally {
        setIsRetrying(false);
      }
    });
    return function handleRetry() {
      return _ref2.apply(this, arguments);
    };
  }();
  var handleOfflineMode = function handleOfflineMode() {
    _performanceMonitor.performanceMonitor.trackUserInteraction('network_error_offline_mode', 0, {
      errorType: (error == null ? void 0 : error.name) || 'NetworkError',
      retryCount: retryCount
    });
    onOfflineMode == null || onOfflineMode();
  };
  var getRetryButtonText = function getRetryButtonText() {
    if (isRetrying) return 'Retrying...';
    if (retryCount === 0) return 'Try Again';
    return `Try Again (${retryCount}/${maxRetries})`;
  };
  var getConnectionStatusColor = function getConnectionStatusColor() {
    if (!networkStatus.isConnected) return colors.error;
    if (!networkStatus.isInternetReachable) return colors.warning;
    return colors.success;
  };
  return (0, _jsxRuntime.jsxs)(_reactNative.View, {
    style: [styles.container, {
      backgroundColor: colors.background.primary
    }],
    testID: testID,
    children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
      style: [styles.iconContainer, {
        backgroundColor: colors.background.secondary
      }],
      children: (0, _jsxRuntime.jsx)(_vectorIcons.Ionicons, {
        name: errorInfo.icon,
        size: 64,
        color: colors.text.secondary,
        testID: `${testID}-icon`
      })
    }), (0, _jsxRuntime.jsx)(_reactNative.Text, {
      style: [styles.title, {
        color: colors.text.primary
      }],
      testID: `${testID}-title`,
      accessibilityRole: "header",
      children: errorInfo.title
    }), (0, _jsxRuntime.jsx)(_reactNative.Text, {
      style: [styles.message, {
        color: colors.text.secondary
      }],
      testID: `${testID}-message`,
      accessibilityRole: "text",
      children: errorInfo.message
    }), (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: styles.statusContainer,
      children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.statusRow,
        children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: [styles.statusDot, {
            backgroundColor: getConnectionStatusColor()
          }]
        }), (0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: [styles.statusText, {
            color: colors.text.tertiary
          }],
          children: networkStatus.isConnected ? networkStatus.isInternetReachable ? 'Connected' : 'Limited connectivity' : 'No connection'
        })]
      })
    }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.actionsContainer,
      children: [canRetry && (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        style: [styles.retryButton, {
          backgroundColor: colors.interactive.primary.default,
          opacity: isRetrying ? 0.6 : 1
        }],
        onPress: handleRetry,
        disabled: isRetrying,
        testID: `${testID}-retry-button`,
        accessibilityRole: "button",
        accessibilityLabel: `Retry connection. Attempt ${retryCount + 1} of ${maxRetries}`,
        accessibilityHint: "Double tap to retry the failed network request",
        children: [isRetrying ? (0, _jsxRuntime.jsx)(_reactNative.ActivityIndicator, {
          size: "small",
          color: colors.interactive.primary.text
        }) : (0, _jsxRuntime.jsx)(_vectorIcons.Ionicons, {
          name: "refresh-outline",
          size: 20,
          color: colors.interactive.primary.text
        }), (0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: [styles.retryButtonText, {
            color: colors.interactive.primary.text
          }],
          children: getRetryButtonText()
        })]
      }), !canRetry && (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.maxRetriesContainer,
        children: (0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: [styles.maxRetriesText, {
            color: colors.text.tertiary
          }],
          children: "Maximum retry attempts reached"
        })
      }), showOfflineOption && (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        style: [styles.offlineButton, {
          borderColor: colors.interactive.secondary.border,
          backgroundColor: colors.interactive.secondary.default
        }],
        onPress: handleOfflineMode,
        testID: `${testID}-offline-button`,
        accessibilityRole: "button",
        accessibilityLabel: "Continue in offline mode",
        accessibilityHint: "Double tap to use the app with limited functionality",
        children: [(0, _jsxRuntime.jsx)(_vectorIcons.Ionicons, {
          name: "cloud-offline-outline",
          size: 20,
          color: colors.interactive.secondary.text
        }), (0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: [styles.offlineButtonText, {
            color: colors.interactive.secondary.text
          }],
          children: "Continue Offline"
        })]
      })]
    }), __DEV__ && error && (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: [styles.debugContainer, {
        backgroundColor: colors.background.tertiary
      }],
      children: [(0, _jsxRuntime.jsx)(_reactNative.Text, {
        style: [styles.debugTitle, {
          color: colors.text.secondary
        }],
        children: "Debug Info:"
      }), (0, _jsxRuntime.jsx)(_reactNative.Text, {
        style: [styles.debugText, {
          color: colors.text.tertiary
        }],
        children: error.message
      })]
    })]
  });
};
var styles = _reactNative.StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 12
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
    maxWidth: 300
  },
  statusContainer: {
    marginBottom: 32
  },
  statusRow: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8
  },
  statusText: {
    fontSize: 14
  },
  actionsContainer: {
    width: '100%',
    maxWidth: 300
  },
  retryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    marginBottom: 12
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8
  },
  maxRetriesContainer: {
    alignItems: 'center',
    marginBottom: 12
  },
  maxRetriesText: {
    fontSize: 14,
    textAlign: 'center'
  },
  offlineButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    borderWidth: 1
  },
  offlineButtonText: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8
  },
  debugContainer: {
    position: 'absolute',
    bottom: 24,
    left: 24,
    right: 24,
    padding: 16,
    borderRadius: 8
  },
  debugTitle: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 4
  },
  debugText: {
    fontSize: 11,
    fontFamily: 'monospace'
  }
});
var _default = exports.default = NetworkErrorFallback;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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