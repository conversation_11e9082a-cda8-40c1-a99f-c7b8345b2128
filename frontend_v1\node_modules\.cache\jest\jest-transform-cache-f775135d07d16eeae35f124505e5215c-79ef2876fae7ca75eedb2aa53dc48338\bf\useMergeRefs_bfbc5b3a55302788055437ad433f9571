2ee7150955568ed885eca141e4c3d372
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = useMergeRefs;
var _useRefEffect = _interopRequireDefault(require("./useRefEffect"));
var _react = _interopRequireWildcard(require("react"));
var React = _react;
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
function useMergeRefs() {
  for (var _len = arguments.length, refs = new Array(_len), _key = 0; _key < _len; _key++) {
    refs[_key] = arguments[_key];
  }
  var refEffect = (0, _react.useCallback)(function (current) {
    var cleanups = refs.map(function (ref) {
      if (ref == null) {
        return undefined;
      } else {
        if (typeof ref === 'function') {
          var cleanup = ref(current);
          return typeof cleanup === 'function' ? cleanup : function () {
            ref(null);
          };
        } else {
          ref.current = current;
          return function () {
            ref.current = null;
          };
        }
      }
    });
    return function () {
      for (var cleanup of cleanups) {
        cleanup == null || cleanup();
      }
    };
  }, [].concat(refs));
  return (0, _useRefEffect.default)(refEffect);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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