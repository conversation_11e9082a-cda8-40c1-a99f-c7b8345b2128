0e3110536cee9a960124b0462a7a4f72
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _classPrivateFieldLooseBase2 = _interopRequireDefault(require("@babel/runtime/helpers/classPrivateFieldLooseBase"));
var _classPrivateFieldLooseKey2 = _interopRequireDefault(require("@babel/runtime/helpers/classPrivateFieldLooseKey"));
var _NativeAnimatedHelper = _interopRequireDefault(require("../../../src/private/animated/NativeAnimatedHelper"));
var _invariant = _interopRequireDefault(require("invariant"));
var _uniqueId = 1;
var _assertNativeAnimatedModule2 = function _assertNativeAnimatedModule() {
  _NativeAnimatedHelper.default.assertNativeAnimatedModule();
  _assertNativeAnimatedModule2 = null;
};
var _listeners = (0, _classPrivateFieldLooseKey2.default)("listeners");
var AnimatedNode = exports.default = function () {
  function AnimatedNode(config) {
    (0, _classCallCheck2.default)(this, AnimatedNode);
    Object.defineProperty(this, _listeners, {
      writable: true,
      value: new Map()
    });
    this._platformConfig = undefined;
    this.__isNative = false;
    this.__nativeTag = undefined;
    this.__debugID = undefined;
    if (__DEV__) {
      this.__debugID = config == null ? void 0 : config.debugID;
    }
  }
  return (0, _createClass2.default)(AnimatedNode, [{
    key: "__attach",
    value: function __attach() {}
  }, {
    key: "__detach",
    value: function __detach() {
      this.removeAllListeners();
      if (this.__isNative && this.__nativeTag != null) {
        _NativeAnimatedHelper.default.API.dropAnimatedNode(this.__nativeTag);
        this.__nativeTag = undefined;
      }
    }
  }, {
    key: "__getValue",
    value: function __getValue() {}
  }, {
    key: "__getAnimatedValue",
    value: function __getAnimatedValue() {
      return this.__getValue();
    }
  }, {
    key: "__addChild",
    value: function __addChild(child) {}
  }, {
    key: "__removeChild",
    value: function __removeChild(child) {}
  }, {
    key: "__getChildren",
    value: function __getChildren() {
      return [];
    }
  }, {
    key: "__makeNative",
    value: function __makeNative(platformConfig) {
      (0, _invariant.default)(this.__isNative, 'This node cannot be made a "native" animated node');
      this._platformConfig = platformConfig;
    }
  }, {
    key: "addListener",
    value: function addListener(callback) {
      var id = String(_uniqueId++);
      (0, _classPrivateFieldLooseBase2.default)(this, _listeners)[_listeners].set(id, callback);
      return id;
    }
  }, {
    key: "removeListener",
    value: function removeListener(id) {
      (0, _classPrivateFieldLooseBase2.default)(this, _listeners)[_listeners].delete(id);
    }
  }, {
    key: "removeAllListeners",
    value: function removeAllListeners() {
      (0, _classPrivateFieldLooseBase2.default)(this, _listeners)[_listeners].clear();
    }
  }, {
    key: "hasListeners",
    value: function hasListeners() {
      return (0, _classPrivateFieldLooseBase2.default)(this, _listeners)[_listeners].size > 0;
    }
  }, {
    key: "__onAnimatedValueUpdateReceived",
    value: function __onAnimatedValueUpdateReceived(value) {
      this.__callListeners(value);
    }
  }, {
    key: "__callListeners",
    value: function __callListeners(value) {
      var event = {
        value: value
      };
      (0, _classPrivateFieldLooseBase2.default)(this, _listeners)[_listeners].forEach(function (listener) {
        listener(event);
      });
    }
  }, {
    key: "__getNativeTag",
    value: function __getNativeTag() {
      var nativeTag = this.__nativeTag;
      if (nativeTag == null) {
        _assertNativeAnimatedModule2 == null || _assertNativeAnimatedModule2();
        (0, _invariant.default)(this.__isNative, 'Attempt to get native tag from node not marked as "native"');
        nativeTag = _NativeAnimatedHelper.default.generateNewNodeTag();
        this.__nativeTag = nativeTag;
        var config = this.__getNativeConfig();
        if (this._platformConfig) {
          config.platformConfig = this._platformConfig;
        }
        _NativeAnimatedHelper.default.API.createAnimatedNode(nativeTag, config);
      }
      return nativeTag;
    }
  }, {
    key: "__getNativeConfig",
    value: function __getNativeConfig() {
      throw new Error('This JS animated node type cannot be used as native animated node');
    }
  }, {
    key: "__getPlatformConfig",
    value: function __getPlatformConfig() {
      return this._platformConfig;
    }
  }, {
    key: "__setPlatformConfig",
    value: function __setPlatformConfig(platformConfig) {
      this._platformConfig = platformConfig;
    }
  }, {
    key: "toJSON",
    value: function toJSON() {
      return this.__getValue();
    }
  }, {
    key: "__getDebugID",
    value: function __getDebugID() {
      if (__DEV__) {
        return this.__debugID;
      }
      return undefined;
    }
  }]);
}();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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