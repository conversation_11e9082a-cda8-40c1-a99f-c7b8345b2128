{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_reactNative", "_Typography", "_HighContrastContext", "_CognitiveAccessibilityContext", "_jsxRuntime", "_excluded", "_excluded2", "_excluded3", "_excluded4", "_excluded5", "Typography", "exports", "_ref", "_colors$text", "children", "_ref$variant", "variant", "color", "_ref$align", "align", "_ref$transform", "transform", "_ref$accessibilityLev", "accessibilityLevel", "semanticLevel", "_ref$selectable", "selectable", "numberOfLines", "_ref$ellipsizeMode", "ellipsizeMode", "style", "_ref$gutterBottom", "gutterBottom", "_ref$gutterTop", "gutterTop", "testID", "textProps", "_objectWithoutProperties2", "default", "_useHighContrastColor", "useHighContrastColors", "colors", "_useCognitiveAccessib", "useCognitiveAccessibility", "processText", "settings", "variantStyles", "TYPOGRAPHY_VARIANTS", "processedText", "accessibleFontSize", "getAccessibleFontSize", "fontSize", "effectiveColor", "text", "primary", "getAccessibilityProps", "props", "startsWith", "accessibilityRole", "parseInt", "char<PERSON>t", "accessibilityLabel", "textStyles", "styles", "base", "fontFamily", "fontWeight", "lineHeight", "letterSpacing", "textAlign", "textTransform", "filter", "Boolean", "__DEV__", "isTextSizeAccessible", "console", "warn", "jsx", "Text", "Object", "assign", "Heading", "_ref2", "level", "Body", "_ref3", "_ref3$size", "size", "Caption", "Label", "Subtitle", "_ref4", "_ref4$level", "Overline", "Display", "_ref5", "_ref5$size", "variantMap", "small", "medium", "large", "Code", "StyleSheet", "create", "marginTop", "marginBottom", "_default"], "sources": ["Typography.tsx"], "sourcesContent": ["/**\n * Typography Component\n *\n * Comprehensive typography component with semantic variants,\n * accessibility features, and visual hierarchy support.\n *\n * Features:\n * - Semantic typography variants\n * - Accessibility compliance\n * - Responsive font scaling\n * - Visual hierarchy support\n * - Platform optimization\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\nimport React from 'react';\nimport { Text } from 'react-native';\nimport { StyleSheet, TextProps } from 'react-native';\nimport { TYPOGRAPHY_VARIANTS, getAccessibleFontSize, isTextSizeAccessible } from '../../constants/Typography';\nimport { useHighContrastColors } from '../../contexts/HighContrastContext';\nimport { useCognitiveAccessibility } from '../../contexts/CognitiveAccessibilityContext';\n\n// Typography variant types\nexport type TypographyVariant = keyof typeof TYPOGRAPHY_VARIANTS;\n\n// Component props\nexport interface TypographyProps extends Omit<TextProps, 'style'> {\n  // Content\n  children: React.ReactNode;\n  \n  // Typography\n  variant?: TypographyVariant;\n  color?: string;\n  align?: 'left' | 'center' | 'right' | 'justify';\n  transform?: 'none' | 'uppercase' | 'lowercase' | 'capitalize';\n  \n  // Accessibility\n  accessibilityLevel?: 'normal' | 'large' | 'extraLarge';\n  semanticLevel?: 1 | 2 | 3 | 4 | 5 | 6; // For headings\n  \n  // Behavior\n  selectable?: boolean;\n  numberOfLines?: number;\n  ellipsizeMode?: 'head' | 'middle' | 'tail' | 'clip';\n  \n  // Styling\n  style?: any;\n  gutterBottom?: boolean;\n  gutterTop?: boolean;\n  \n  // Testing\n  testID?: string;\n}\n\nexport const Typography: React.FC<TypographyProps> = ({\n  children,\n  variant = 'body1',\n  color,\n  align = 'left',\n  transform = 'none',\n  accessibilityLevel = 'normal',\n  semanticLevel,\n  selectable = true,\n  numberOfLines,\n  ellipsizeMode = 'tail',\n  style,\n  gutterBottom = false,\n  gutterTop = false,\n  testID,\n  ...textProps\n}) => {\n  // Hooks\n  const { colors } = useHighContrastColors();\n  const { processText, settings } = useCognitiveAccessibility();\n\n  // Get typography variant styles\n  const variantStyles = TYPOGRAPHY_VARIANTS[variant];\n\n  // Process text for cognitive accessibility\n  const processedText = typeof children === 'string' ? processText(children) : children;\n\n  // Calculate accessible font size\n  const accessibleFontSize = getAccessibleFontSize(\n    variantStyles.fontSize,\n    accessibilityLevel === 'normal' ? 'normal' :\n    accessibilityLevel === 'large' ? 'large' : 'extraLarge'\n  );\n\n  // Get effective color\n  const effectiveColor = color || colors?.text?.primary || '#333333';\n\n  // Generate accessibility props\n  const getAccessibilityProps = () => {\n    const props: any = {};\n\n    // Set accessibility role for headings\n    if (variant.startsWith('h') || semanticLevel) {\n      props.accessibilityRole = 'header';\n      props.accessibilityLevel = semanticLevel || parseInt(variant.charAt(1)) || 1;\n    } else {\n      props.accessibilityRole = 'text';\n    }\n\n    // Add accessibility label if text is truncated\n    if (numberOfLines && typeof children === 'string') {\n      props.accessibilityLabel = children;\n    }\n\n    return props;\n  };\n\n  // Generate styles\n  const textStyles = [\n    styles.base,\n    {\n      fontSize: accessibleFontSize,\n      fontFamily: variantStyles.fontFamily,\n      fontWeight: variantStyles.fontWeight,\n      lineHeight: variantStyles.lineHeight,\n      letterSpacing: variantStyles.letterSpacing,\n      color: effectiveColor,\n      textAlign: align,\n      textTransform: transform,\n    },\n    gutterTop && styles.gutterTop,\n    gutterBottom && styles.gutterBottom,\n    style,\n  ].filter(Boolean);\n\n  // Warn about accessibility issues in development\n  if (__DEV__ && !isTextSizeAccessible(accessibleFontSize)) {\n    console.warn(`Typography: Font size ${accessibleFontSize}px may not be accessible`);\n  }\n\n  return (\n    <Text\n      style={textStyles}\n      selectable={selectable}\n      numberOfLines={numberOfLines}\n      ellipsizeMode={ellipsizeMode}\n      testID={testID}\n      {...getAccessibilityProps()}\n      {...textProps}\n    >\n      {processedText}\n    </Text>\n  );\n};\n\n// Specialized typography components for common use cases\nexport const Heading: React.FC<Omit<TypographyProps, 'variant'> & {\n  level: 1 | 2 | 3 | 4 | 5 | 6;\n}> = ({ level, ...props }) => (\n  <Typography\n    variant={`h${level}` as TypographyVariant}\n    semanticLevel={level}\n    gutterBottom\n    {...props}\n  />\n);\n\nexport const Body: React.FC<Omit<TypographyProps, 'variant'> & {\n  size?: 'small' | 'medium';\n}> = ({ size = 'medium', ...props }) => (\n  <Typography\n    variant={size === 'small' ? 'body2' : 'body1'}\n    gutterBottom\n    {...props}\n  />\n);\n\nexport const Caption: React.FC<Omit<TypographyProps, 'variant'>> = (props) => (\n  <Typography\n    variant=\"caption\"\n    {...props}\n  />\n);\n\nexport const Label: React.FC<Omit<TypographyProps, 'variant'>> = (props) => (\n  <Typography\n    variant=\"label\"\n    {...props}\n  />\n);\n\nexport const Subtitle: React.FC<Omit<TypographyProps, 'variant'> & {\n  level?: 1 | 2;\n}> = ({ level = 1, ...props }) => (\n  <Typography\n    variant={level === 1 ? 'subtitle1' : 'subtitle2'}\n    gutterBottom\n    {...props}\n  />\n);\n\nexport const Overline: React.FC<Omit<TypographyProps, 'variant'>> = (props) => (\n  <Typography\n    variant=\"overline\"\n    {...props}\n  />\n);\n\n// Display component for large text\nexport const Display: React.FC<Omit<TypographyProps, 'variant'> & {\n  size?: 'small' | 'medium' | 'large';\n}> = ({ size = 'medium', ...props }) => {\n  const variantMap = {\n    small: 'h2',\n    medium: 'h1',\n    large: 'h1',\n  } as const;\n\n  return (\n    <Typography\n      variant={variantMap[size]}\n      gutterBottom\n      {...props}\n    />\n  );\n};\n\n// Code component for monospace text\nexport const Code: React.FC<Omit<TypographyProps, 'variant'>> = (props) => (\n  <Typography\n    variant=\"code\"\n    selectable\n    {...props}\n  />\n);\n\nconst styles = StyleSheet.create({\n  base: {\n    // Base styles applied to all typography\n  },\n  gutterTop: {\n    marginTop: 16,\n  },\n  gutterBottom: {\n    marginBottom: 16,\n  },\n});\n\n// Export all components\nexport default Typography;\nexport {\n  Heading,\n  Body,\n  Caption,\n  Label,\n  Subtitle,\n  Overline,\n  Display,\n  Code,\n};\n"], "mappings": ";;;;;;AAiBA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAEA,IAAAE,WAAA,GAAAF,OAAA;AACA,IAAAG,oBAAA,GAAAH,OAAA;AACA,IAAAI,8BAAA,GAAAJ,OAAA;AAAyF,IAAAK,WAAA,GAAAL,OAAA;AAAA,IAAAM,SAAA;EAAAC,UAAA;EAAAC,UAAA;EAAAC,UAAA;EAAAC,UAAA;AAkClF,IAAMC,UAAqC,GAAAC,OAAA,CAAAD,UAAA,GAAG,SAAxCA,UAAqCA,CAAAE,IAAA,EAgB5C;EAAA,IAAAC,YAAA;EAAA,IAfJC,QAAQ,GAAAF,IAAA,CAARE,QAAQ;IAAAC,YAAA,GAAAH,IAAA,CACRI,OAAO;IAAPA,OAAO,GAAAD,YAAA,cAAG,OAAO,GAAAA,YAAA;IACjBE,KAAK,GAAAL,IAAA,CAALK,KAAK;IAAAC,UAAA,GAAAN,IAAA,CACLO,KAAK;IAALA,KAAK,GAAAD,UAAA,cAAG,MAAM,GAAAA,UAAA;IAAAE,cAAA,GAAAR,IAAA,CACdS,SAAS;IAATA,SAAS,GAAAD,cAAA,cAAG,MAAM,GAAAA,cAAA;IAAAE,qBAAA,GAAAV,IAAA,CAClBW,kBAAkB;IAAlBA,kBAAkB,GAAAD,qBAAA,cAAG,QAAQ,GAAAA,qBAAA;IAC7BE,aAAa,GAAAZ,IAAA,CAAbY,aAAa;IAAAC,eAAA,GAAAb,IAAA,CACbc,UAAU;IAAVA,UAAU,GAAAD,eAAA,cAAG,IAAI,GAAAA,eAAA;IACjBE,aAAa,GAAAf,IAAA,CAAbe,aAAa;IAAAC,kBAAA,GAAAhB,IAAA,CACbiB,aAAa;IAAbA,aAAa,GAAAD,kBAAA,cAAG,MAAM,GAAAA,kBAAA;IACtBE,KAAK,GAAAlB,IAAA,CAALkB,KAAK;IAAAC,iBAAA,GAAAnB,IAAA,CACLoB,YAAY;IAAZA,YAAY,GAAAD,iBAAA,cAAG,KAAK,GAAAA,iBAAA;IAAAE,cAAA,GAAArB,IAAA,CACpBsB,SAAS;IAATA,SAAS,GAAAD,cAAA,cAAG,KAAK,GAAAA,cAAA;IACjBE,MAAM,GAAAvB,IAAA,CAANuB,MAAM;IACHC,SAAS,OAAAC,yBAAA,CAAAC,OAAA,EAAA1B,IAAA,EAAAP,SAAA;EAGZ,IAAAkC,qBAAA,GAAmB,IAAAC,0CAAqB,EAAC,CAAC;IAAlCC,MAAM,GAAAF,qBAAA,CAANE,MAAM;EACd,IAAAC,qBAAA,GAAkC,IAAAC,wDAAyB,EAAC,CAAC;IAArDC,WAAW,GAAAF,qBAAA,CAAXE,WAAW;IAAEC,QAAQ,GAAAH,qBAAA,CAARG,QAAQ;EAG7B,IAAMC,aAAa,GAAGC,+BAAmB,CAAC/B,OAAO,CAAC;EAGlD,IAAMgC,aAAa,GAAG,OAAOlC,QAAQ,KAAK,QAAQ,GAAG8B,WAAW,CAAC9B,QAAQ,CAAC,GAAGA,QAAQ;EAGrF,IAAMmC,kBAAkB,GAAG,IAAAC,iCAAqB,EAC9CJ,aAAa,CAACK,QAAQ,EACtB5B,kBAAkB,KAAK,QAAQ,GAAG,QAAQ,GAC1CA,kBAAkB,KAAK,OAAO,GAAG,OAAO,GAAG,YAC7C,CAAC;EAGD,IAAM6B,cAAc,GAAGnC,KAAK,KAAIwB,MAAM,aAAA5B,YAAA,GAAN4B,MAAM,CAAEY,IAAI,qBAAZxC,YAAA,CAAcyC,OAAO,KAAI,SAAS;EAGlE,IAAMC,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAA,EAAS;IAClC,IAAMC,KAAU,GAAG,CAAC,CAAC;IAGrB,IAAIxC,OAAO,CAACyC,UAAU,CAAC,GAAG,CAAC,IAAIjC,aAAa,EAAE;MAC5CgC,KAAK,CAACE,iBAAiB,GAAG,QAAQ;MAClCF,KAAK,CAACjC,kBAAkB,GAAGC,aAAa,IAAImC,QAAQ,CAAC3C,OAAO,CAAC4C,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC9E,CAAC,MAAM;MACLJ,KAAK,CAACE,iBAAiB,GAAG,MAAM;IAClC;IAGA,IAAI/B,aAAa,IAAI,OAAOb,QAAQ,KAAK,QAAQ,EAAE;MACjD0C,KAAK,CAACK,kBAAkB,GAAG/C,QAAQ;IACrC;IAEA,OAAO0C,KAAK;EACd,CAAC;EAGD,IAAMM,UAAU,GAAG,CACjBC,MAAM,CAACC,IAAI,EACX;IACEb,QAAQ,EAAEF,kBAAkB;IAC5BgB,UAAU,EAAEnB,aAAa,CAACmB,UAAU;IACpCC,UAAU,EAAEpB,aAAa,CAACoB,UAAU;IACpCC,UAAU,EAAErB,aAAa,CAACqB,UAAU;IACpCC,aAAa,EAAEtB,aAAa,CAACsB,aAAa;IAC1CnD,KAAK,EAAEmC,cAAc;IACrBiB,SAAS,EAAElD,KAAK;IAChBmD,aAAa,EAAEjD;EACjB,CAAC,EACDa,SAAS,IAAI6B,MAAM,CAAC7B,SAAS,EAC7BF,YAAY,IAAI+B,MAAM,CAAC/B,YAAY,EACnCF,KAAK,CACN,CAACyC,MAAM,CAACC,OAAO,CAAC;EAGjB,IAAIC,OAAO,IAAI,CAAC,IAAAC,gCAAoB,EAACzB,kBAAkB,CAAC,EAAE;IACxD0B,OAAO,CAACC,IAAI,CAAC,yBAAyB3B,kBAAkB,0BAA0B,CAAC;EACrF;EAEA,OACE,IAAA7C,WAAA,CAAAyE,GAAA,EAAC7E,YAAA,CAAA8E,IAAI,EAAAC,MAAA,CAAAC,MAAA;IACHlD,KAAK,EAAEgC,UAAW;IAClBpC,UAAU,EAAEA,UAAW;IACvBC,aAAa,EAAEA,aAAc;IAC7BE,aAAa,EAAEA,aAAc;IAC7BM,MAAM,EAAEA;EAAO,GACXoB,qBAAqB,CAAC,CAAC,EACvBnB,SAAS;IAAAtB,QAAA,EAEZkC;EAAa,EACV,CAAC;AAEX,CAAC;AAGM,IAAMiC,OAEX,GAAAtE,OAAA,CAAAsE,OAAA,GAAAtE,OAAA,CAAAsE,OAAA,GAAG,SAFQA,OAEXA,CAAAC,KAAA;EAAA,IAAMC,KAAK,GAAAD,KAAA,CAALC,KAAK;IAAK3B,KAAK,OAAAnB,yBAAA,CAAAC,OAAA,EAAA4C,KAAA,EAAA5E,UAAA;EAAA,OACrB,IAAAF,WAAA,CAAAyE,GAAA,EAACnE,UAAU,EAAAqE,MAAA,CAAAC,MAAA;IACThE,OAAO,EAAE,IAAImE,KAAK,EAAwB;IAC1C3D,aAAa,EAAE2D,KAAM;IACrBnD,YAAY;EAAA,GACRwB,KAAK,CACV,CAAC;AAAA,CACH;AAEM,IAAM4B,IAEX,GAAAzE,OAAA,CAAAyE,IAAA,GAAAzE,OAAA,CAAAyE,IAAA,GAAG,SAFQA,IAEXA,CAAAC,KAAA;EAAA,IAAAC,UAAA,GAAAD,KAAA,CAAME,IAAI;IAAJA,IAAI,GAAAD,UAAA,cAAG,QAAQ,GAAAA,UAAA;IAAK9B,KAAK,OAAAnB,yBAAA,CAAAC,OAAA,EAAA+C,KAAA,EAAA9E,UAAA;EAAA,OAC/B,IAAAH,WAAA,CAAAyE,GAAA,EAACnE,UAAU,EAAAqE,MAAA,CAAAC,MAAA;IACThE,OAAO,EAAEuE,IAAI,KAAK,OAAO,GAAG,OAAO,GAAG,OAAQ;IAC9CvD,YAAY;EAAA,GACRwB,KAAK,CACV,CAAC;AAAA,CACH;AAEM,IAAMgC,OAAmD,GAAA7E,OAAA,CAAA6E,OAAA,GAAA7E,OAAA,CAAA6E,OAAA,GAAG,SAAtDA,OAAmDA,CAAIhC,KAAK;EAAA,OACvE,IAAApD,WAAA,CAAAyE,GAAA,EAACnE,UAAU,EAAAqE,MAAA,CAAAC,MAAA;IACThE,OAAO,EAAC;EAAS,GACbwC,KAAK,CACV,CAAC;AAAA,CACH;AAEM,IAAMiC,KAAiD,GAAA9E,OAAA,CAAA8E,KAAA,GAAA9E,OAAA,CAAA8E,KAAA,GAAG,SAApDA,KAAiDA,CAAIjC,KAAK;EAAA,OACrE,IAAApD,WAAA,CAAAyE,GAAA,EAACnE,UAAU,EAAAqE,MAAA,CAAAC,MAAA;IACThE,OAAO,EAAC;EAAO,GACXwC,KAAK,CACV,CAAC;AAAA,CACH;AAEM,IAAMkC,QAEX,GAAA/E,OAAA,CAAA+E,QAAA,GAAA/E,OAAA,CAAA+E,QAAA,GAAG,SAFQA,QAEXA,CAAAC,KAAA;EAAA,IAAAC,WAAA,GAAAD,KAAA,CAAMR,KAAK;IAALA,KAAK,GAAAS,WAAA,cAAG,CAAC,GAAAA,WAAA;IAAKpC,KAAK,OAAAnB,yBAAA,CAAAC,OAAA,EAAAqD,KAAA,EAAAnF,UAAA;EAAA,OACzB,IAAAJ,WAAA,CAAAyE,GAAA,EAACnE,UAAU,EAAAqE,MAAA,CAAAC,MAAA;IACThE,OAAO,EAAEmE,KAAK,KAAK,CAAC,GAAG,WAAW,GAAG,WAAY;IACjDnD,YAAY;EAAA,GACRwB,KAAK,CACV,CAAC;AAAA,CACH;AAEM,IAAMqC,QAAoD,GAAAlF,OAAA,CAAAkF,QAAA,GAAAlF,OAAA,CAAAkF,QAAA,GAAG,SAAvDA,QAAoDA,CAAIrC,KAAK;EAAA,OACxE,IAAApD,WAAA,CAAAyE,GAAA,EAACnE,UAAU,EAAAqE,MAAA,CAAAC,MAAA;IACThE,OAAO,EAAC;EAAU,GACdwC,KAAK,CACV,CAAC;AAAA,CACH;AAGM,IAAMsC,OAEX,GAAAnF,OAAA,CAAAmF,OAAA,GAAAnF,OAAA,CAAAmF,OAAA,GAAG,SAFQA,OAEXA,CAAAC,KAAA,EAAsC;EAAA,IAAAC,UAAA,GAAAD,KAAA,CAAhCR,IAAI;IAAJA,IAAI,GAAAS,UAAA,cAAG,QAAQ,GAAAA,UAAA;IAAKxC,KAAK,OAAAnB,yBAAA,CAAAC,OAAA,EAAAyD,KAAA,EAAAtF,UAAA;EAC/B,IAAMwF,UAAU,GAAG;IACjBC,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE;EACT,CAAU;EAEV,OACE,IAAAhG,WAAA,CAAAyE,GAAA,EAACnE,UAAU,EAAAqE,MAAA,CAAAC,MAAA;IACThE,OAAO,EAAEiF,UAAU,CAACV,IAAI,CAAE;IAC1BvD,YAAY;EAAA,GACRwB,KAAK,CACV,CAAC;AAEN,CAAC;AAGM,IAAM6C,IAAgD,GAAA1F,OAAA,CAAA0F,IAAA,GAAA1F,OAAA,CAAA0F,IAAA,GAAG,SAAnDA,IAAgDA,CAAI7C,KAAK;EAAA,OACpE,IAAApD,WAAA,CAAAyE,GAAA,EAACnE,UAAU,EAAAqE,MAAA,CAAAC,MAAA;IACThE,OAAO,EAAC,MAAM;IACdU,UAAU;EAAA,GACN8B,KAAK,CACV,CAAC;AAAA,CACH;AAED,IAAMO,MAAM,GAAGuC,uBAAU,CAACC,MAAM,CAAC;EAC/BvC,IAAI,EAAE,CAEN,CAAC;EACD9B,SAAS,EAAE;IACTsE,SAAS,EAAE;EACb,CAAC;EACDxE,YAAY,EAAE;IACZyE,YAAY,EAAE;EAChB;AACF,CAAC,CAAC;AAAC,IAAAC,QAAA,GAAA/F,OAAA,CAAA2B,OAAA,GAGY5B,UAAU", "ignoreList": []}