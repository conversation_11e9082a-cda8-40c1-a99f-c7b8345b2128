72658e8801c3b687f298f6edef5e0ddf
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.cacheService = void 0;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _asyncStorage = _interopRequireDefault(require("@react-native-async-storage/async-storage"));
var CacheService = function () {
  function CacheService(config) {
    (0, _classCallCheck2.default)(this, CacheService);
    this.memoryCache = new Map();
    this.CACHE_VERSION = '1.0.0';
    this.STORAGE_PREFIX = '@vierla_cache_';
    this.config = Object.assign({
      defaultTTL: 5 * 60 * 1000,
      maxMemorySize: 50 * 1024 * 1024,
      maxStorageSize: 100 * 1024 * 1024,
      compressionThreshold: 1024,
      enableEncryption: false,
      enableAnalytics: true,
      cleanupInterval: 60 * 1000
    }, config);
    this.stats = {
      memoryHits: 0,
      memoryMisses: 0,
      storageHits: 0,
      storageMisses: 0,
      totalSize: 0,
      entryCount: 0,
      hitRate: 0,
      averageAccessTime: 0
    };
    this.startCleanupTimer();
  }
  return (0, _createClass2.default)(CacheService, [{
    key: "get",
    value: (function () {
      var _get = (0, _asyncToGenerator2.default)(function* (key) {
        var startTime = Date.now();
        try {
          var memoryEntry = this.memoryCache.get(key);
          if (memoryEntry && this.isValidEntry(memoryEntry)) {
            this.updateStats('memoryHit', Date.now() - startTime);
            this.updateEntryAccess(key, memoryEntry);
            return memoryEntry.data;
          }
          if (memoryEntry) {
            this.memoryCache.delete(key);
          }
          var storageEntry = yield this.getFromStorage(key);
          if (storageEntry && this.isValidEntry(storageEntry)) {
            this.updateStats('storageHit', Date.now() - startTime);
            this.updateEntryAccess(key, storageEntry);
            this.memoryCache.set(key, storageEntry);
            return storageEntry.data;
          }
          this.updateStats('miss', Date.now() - startTime);
          return null;
        } catch (error) {
          console.error('Cache get error:', error);
          return null;
        }
      });
      function get(_x) {
        return _get.apply(this, arguments);
      }
      return get;
    }())
  }, {
    key: "set",
    value: (function () {
      var _set = (0, _asyncToGenerator2.default)(function* (key, data, ttl, options) {
        var entry = {
          data: data,
          timestamp: Date.now(),
          ttl: ttl || this.config.defaultTTL,
          version: this.CACHE_VERSION,
          accessCount: 0,
          lastAccessed: Date.now()
        };
        try {
          var dataSize = this.estimateSize(data);
          var shouldCompress = (options == null ? void 0 : options.compress) || dataSize > this.config.compressionThreshold && !(options != null && options.memoryOnly);
          if (shouldCompress) {
            entry.compressed = true;
          }
          if (!(options != null && options.storageOnly)) {
            this.memoryCache.set(key, entry);
            this.enforceMemoryLimit();
          }
          if (!(options != null && options.memoryOnly)) {
            yield this.setInStorage(key, entry);
          }
          this.updateCacheStats();
        } catch (error) {
          console.error('Cache set error:', error);
        }
      });
      function set(_x2, _x3, _x4, _x5) {
        return _set.apply(this, arguments);
      }
      return set;
    }())
  }, {
    key: "remove",
    value: (function () {
      var _remove = (0, _asyncToGenerator2.default)(function* (key) {
        try {
          this.memoryCache.delete(key);
          yield _asyncStorage.default.removeItem(this.STORAGE_PREFIX + key);
          this.updateCacheStats();
        } catch (error) {
          console.error('Cache remove error:', error);
        }
      });
      function remove(_x6) {
        return _remove.apply(this, arguments);
      }
      return remove;
    }())
  }, {
    key: "clear",
    value: (function () {
      var _clear = (0, _asyncToGenerator2.default)(function* () {
        var _this = this;
        try {
          this.memoryCache.clear();
          var keys = yield _asyncStorage.default.getAllKeys();
          var cacheKeys = keys.filter(function (key) {
            return key.startsWith(_this.STORAGE_PREFIX);
          });
          yield _asyncStorage.default.multiRemove(cacheKeys);
          this.resetStats();
        } catch (error) {
          console.error('Cache clear error:', error);
        }
      });
      function clear() {
        return _clear.apply(this, arguments);
      }
      return clear;
    }())
  }, {
    key: "preload",
    value: (function () {
      var _preload = (0, _asyncToGenerator2.default)(function* (entries) {
        var _this2 = this;
        var promises = entries.map(function (_ref) {
          var key = _ref.key,
            data = _ref.data,
            ttl = _ref.ttl;
          return _this2.set(key, data, ttl);
        });
        yield Promise.allSettled(promises);
      });
      function preload(_x7) {
        return _preload.apply(this, arguments);
      }
      return preload;
    }())
  }, {
    key: "getStats",
    value: function getStats() {
      return Object.assign({}, this.stats);
    }
  }, {
    key: "getEntryInfo",
    value: function getEntryInfo(key) {
      var entry = this.memoryCache.get(key);
      if (!entry) return null;
      return {
        timestamp: entry.timestamp,
        ttl: entry.ttl,
        version: entry.version,
        accessCount: entry.accessCount,
        lastAccessed: entry.lastAccessed,
        compressed: entry.compressed,
        encrypted: entry.encrypted
      };
    }
  }, {
    key: "invalidatePattern",
    value: (function () {
      var _invalidatePattern = (0, _asyncToGenerator2.default)(function* (pattern) {
        var _this3 = this;
        for (var key of this.memoryCache.keys()) {
          if (pattern.test(key)) {
            this.memoryCache.delete(key);
          }
        }
        try {
          var keys = yield _asyncStorage.default.getAllKeys();
          var cacheKeys = keys.filter(function (key) {
            return key.startsWith(_this3.STORAGE_PREFIX);
          }).map(function (key) {
            return key.replace(_this3.STORAGE_PREFIX, '');
          }).filter(function (key) {
            return pattern.test(key);
          });
          var storageKeys = cacheKeys.map(function (key) {
            return _this3.STORAGE_PREFIX + key;
          });
          yield _asyncStorage.default.multiRemove(storageKeys);
        } catch (error) {
          console.error('Cache pattern invalidation error:', error);
        }
        this.updateCacheStats();
      });
      function invalidatePattern(_x8) {
        return _invalidatePattern.apply(this, arguments);
      }
      return invalidatePattern;
    }())
  }, {
    key: "getFromStorage",
    value: (function () {
      var _getFromStorage = (0, _asyncToGenerator2.default)(function* (key) {
        try {
          var stored = yield _asyncStorage.default.getItem(this.STORAGE_PREFIX + key);
          if (!stored) return null;
          var entry = JSON.parse(stored);
          if (entry.compressed) {}
          return entry;
        } catch (error) {
          console.error('Storage get error:', error);
          return null;
        }
      });
      function getFromStorage(_x9) {
        return _getFromStorage.apply(this, arguments);
      }
      return getFromStorage;
    }())
  }, {
    key: "setInStorage",
    value: (function () {
      var _setInStorage = (0, _asyncToGenerator2.default)(function* (key, entry) {
        try {
          var serialized = JSON.stringify(entry);
          yield _asyncStorage.default.setItem(this.STORAGE_PREFIX + key, serialized);
        } catch (error) {
          console.error('Storage set error:', error);
        }
      });
      function setInStorage(_x0, _x1) {
        return _setInStorage.apply(this, arguments);
      }
      return setInStorage;
    }())
  }, {
    key: "isValidEntry",
    value: function isValidEntry(entry) {
      var now = Date.now();
      return now - entry.timestamp < entry.ttl;
    }
  }, {
    key: "updateEntryAccess",
    value: function updateEntryAccess(key, entry) {
      entry.accessCount++;
      entry.lastAccessed = Date.now();
      this.memoryCache.set(key, entry);
    }
  }, {
    key: "estimateSize",
    value: function estimateSize(data) {
      return JSON.stringify(data).length * 2;
    }
  }, {
    key: "enforceMemoryLimit",
    value: function enforceMemoryLimit() {
      var _this4 = this;
      var entries = Array.from(this.memoryCache.entries());
      var totalSize = entries.reduce(function (sum, _ref2) {
        var _ref3 = (0, _slicedToArray2.default)(_ref2, 2),
          entry = _ref3[1];
        return sum + _this4.estimateSize(entry.data);
      }, 0);
      if (totalSize <= this.config.maxMemorySize) return;
      entries.sort(function (_ref4, _ref5) {
        var _ref6 = (0, _slicedToArray2.default)(_ref4, 2),
          a = _ref6[1];
        var _ref7 = (0, _slicedToArray2.default)(_ref5, 2),
          b = _ref7[1];
        var scoreA = a.accessCount / (Date.now() - a.lastAccessed);
        var scoreB = b.accessCount / (Date.now() - b.lastAccessed);
        return scoreA - scoreB;
      });
      while (totalSize > this.config.maxMemorySize && entries.length > 0) {
        var _ref8 = entries.shift(),
          _ref9 = (0, _slicedToArray2.default)(_ref8, 2),
          key = _ref9[0],
          entry = _ref9[1];
        this.memoryCache.delete(key);
        totalSize -= this.estimateSize(entry.data);
      }
    }
  }, {
    key: "updateStats",
    value: function updateStats(type, accessTime) {
      if (!this.config.enableAnalytics) return;
      switch (type) {
        case 'memoryHit':
          this.stats.memoryHits++;
          break;
        case 'storageHit':
          this.stats.storageHits++;
          break;
        case 'miss':
          this.stats.memoryMisses++;
          this.stats.storageMisses++;
          break;
      }
      var totalRequests = this.stats.memoryHits + this.stats.storageHits + this.stats.memoryMisses;
      this.stats.hitRate = totalRequests > 0 ? (this.stats.memoryHits + this.stats.storageHits) / totalRequests : 0;
      this.stats.averageAccessTime = this.stats.averageAccessTime * 0.9 + accessTime * 0.1;
    }
  }, {
    key: "updateCacheStats",
    value: function updateCacheStats() {
      var _this5 = this;
      this.stats.entryCount = this.memoryCache.size;
      this.stats.totalSize = Array.from(this.memoryCache.values()).reduce(function (sum, entry) {
        return sum + _this5.estimateSize(entry.data);
      }, 0);
    }
  }, {
    key: "resetStats",
    value: function resetStats() {
      this.stats = {
        memoryHits: 0,
        memoryMisses: 0,
        storageHits: 0,
        storageMisses: 0,
        totalSize: 0,
        entryCount: 0,
        hitRate: 0,
        averageAccessTime: 0
      };
    }
  }, {
    key: "startCleanupTimer",
    value: function startCleanupTimer() {
      var _this6 = this;
      this.cleanupTimer = setInterval(function () {
        _this6.cleanup();
      }, this.config.cleanupInterval);
    }
  }, {
    key: "cleanup",
    value: function cleanup() {
      var now = Date.now();
      for (var _ref0 of this.memoryCache.entries()) {
        var _ref1 = (0, _slicedToArray2.default)(_ref0, 2);
        var key = _ref1[0];
        var entry = _ref1[1];
        if (!this.isValidEntry(entry)) {
          this.memoryCache.delete(key);
        }
      }
      this.updateCacheStats();
    }
  }, {
    key: "destroy",
    value: function destroy() {
      if (this.cleanupTimer) {
        clearInterval(this.cleanupTimer);
      }
      this.memoryCache.clear();
    }
  }]);
}();
var cacheService = exports.cacheService = new CacheService();
var _default = exports.default = cacheService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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