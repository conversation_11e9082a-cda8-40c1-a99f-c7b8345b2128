/**
 * Change Password Screen - Secure Password Management
 *
 * Component Contract:
 * - Provides secure password change functionality
 * - Validates current password before allowing change
 * - Enforces strong password requirements
 * - Integrates with backend authentication service
 * - Follows security best practices and accessibility guidelines
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, Alert, KeyboardAvoidingView, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { SafeAreaScreen } from '../components/templates/SafeAreaScreen';
import { StandardizedButton } from '../components/atoms/StandardizedButton';
import { Card } from '../components/molecules/Card';
import { useTheme } from '../contexts/ThemeContext';
import { getResponsiveSpacing, getResponsiveFontSize } from '../utils/responsiveUtils';
import { profileService } from '../services/profileService';

type ChangePasswordScreenNavigationProp = StackNavigationProp<any, 'ChangePassword'>;

interface PasswordForm {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

interface PasswordValidation {
  minLength: boolean;
  hasUppercase: boolean;
  hasLowercase: boolean;
  hasNumber: boolean;
  hasSpecialChar: boolean;
}

export const ChangePasswordScreen: React.FC = () => {
  const { colors, isDark } = useTheme();
  const navigation = useNavigation<ChangePasswordScreenNavigationProp>();
  const styles = createStyles(colors);
  
  const [form, setForm] = useState<PasswordForm>({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
  });
  
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Partial<PasswordForm>>({});

  const validatePassword = (password: string): PasswordValidation => {
    return {
      minLength: password.length >= 8,
      hasUppercase: /[A-Z]/.test(password),
      hasLowercase: /[a-z]/.test(password),
      hasNumber: /\d/.test(password),
      hasSpecialChar: /[!@#$%^&*(),.?":{}|<>]/.test(password),
    };
  };

  const isPasswordValid = (password: string): boolean => {
    const validation = validatePassword(password);
    return Object.values(validation).every(Boolean);
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<PasswordForm> = {};
    
    if (!form.currentPassword) {
      newErrors.currentPassword = 'Current password is required';
    }
    
    if (!form.newPassword) {
      newErrors.newPassword = 'New password is required';
    } else if (!isPasswordValid(form.newPassword)) {
      newErrors.newPassword = 'Password does not meet requirements';
    }
    
    if (!form.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your new password';
    } else if (form.newPassword !== form.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }
    
    if (form.currentPassword === form.newPassword) {
      newErrors.newPassword = 'New password must be different from current password';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChangePassword = async () => {
    if (!validateForm()) {
      return;
    }
    
    try {
      setLoading(true);
      
      await profileService.changePassword({
        old_password: form.currentPassword,
        new_password: form.newPassword,
        confirm_password: form.confirmPassword,
      });
      
      Alert.alert(
        'Success',
        'Your password has been changed successfully.',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
      
    } catch (error: any) {
      console.error('Failed to change password:', error);
      
      // Handle specific error messages from backend
      if (error.message?.includes('current password')) {
        setErrors({ currentPassword: 'Current password is incorrect' });
      } else {
        Alert.alert('Error', 'Failed to change password. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const updateForm = (field: keyof PasswordForm, value: string) => {
    setForm(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const togglePasswordVisibility = (field: 'current' | 'new' | 'confirm') => {
    setShowPasswords(prev => ({ ...prev, [field]: !prev[field] }));
  };

  const renderPasswordField = (
    label: string,
    field: keyof PasswordForm,
    visibilityField: 'current' | 'new' | 'confirm',
    placeholder: string
  ) => (
    <View style={styles.formField}>
      <Text style={styles.fieldLabel}>{label}</Text>
      <View style={styles.passwordContainer}>
        <TextInput
          style={[styles.passwordInput, errors[field] && styles.inputError]}
          value={form[field]}
          onChangeText={(value) => updateForm(field, value)}
          placeholder={placeholder}
          placeholderTextColor={colors.text.tertiary}
          secureTextEntry={!showPasswords[visibilityField]}
          autoCapitalize="none"
          autoCorrect={false}
          testID={`${field}-input`}
        />
        <TouchableOpacity
          style={styles.eyeButton}
          onPress={() => togglePasswordVisibility(visibilityField)}
          testID={`${field}-visibility-toggle`}>
          <Ionicons
            name={showPasswords[visibilityField] ? 'eye-off' : 'eye'}
            size={20}
            color={colors.text.tertiary}
          />
        </TouchableOpacity>
      </View>
      {errors[field] && (
        <Text style={styles.errorText}>{errors[field]}</Text>
      )}
    </View>
  );

  const renderPasswordRequirements = () => {
    const validation = validatePassword(form.newPassword);
    
    return (
      <View style={styles.requirementsContainer}>
        <Text style={styles.requirementsTitle}>Password Requirements:</Text>
        {Object.entries({
          'At least 8 characters': validation.minLength,
          'One uppercase letter': validation.hasUppercase,
          'One lowercase letter': validation.hasLowercase,
          'One number': validation.hasNumber,
          'One special character': validation.hasSpecialChar,
        }).map(([requirement, met]) => (
          <View key={requirement} style={styles.requirementItem}>
            <Ionicons
              name={met ? 'checkmark-circle' : 'ellipse-outline'}
              size={16}
              color={met ? colors.success : colors.text.tertiary}
            />
            <Text style={[
              styles.requirementText,
              met && styles.requirementMet
            ]}>
              {requirement}
            </Text>
          </View>
        ))}
      </View>
    );
  };

  return (
    <SafeAreaScreen
      backgroundColor={colors.background.primary}
      statusBarStyle={isDark ? 'light-content' : 'dark-content'}
      testID="change-password-screen">
      
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={styles.backButton}
            testID="back-button">
            <Ionicons name="arrow-back" size={24} color={colors.text.primary} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Change Password</Text>
          <View style={styles.headerSpacer} />
        </View>

        <View style={styles.content}>
          <Card style={styles.formCard}>
            <Text style={styles.formTitle}>Update Your Password</Text>
            <Text style={styles.formSubtitle}>
              Enter your current password and choose a new secure password.
            </Text>

            {renderPasswordField(
              'Current Password',
              'currentPassword',
              'current',
              'Enter your current password'
            )}

            {renderPasswordField(
              'New Password',
              'newPassword',
              'new',
              'Enter your new password'
            )}

            {form.newPassword.length > 0 && renderPasswordRequirements()}

            {renderPasswordField(
              'Confirm New Password',
              'confirmPassword',
              'confirm',
              'Confirm your new password'
            )}

            <StandardizedButton
              action="save"
              onPress={handleChangePassword}
              disabled={loading || !validateForm()}
              style={styles.changeButton}
              testID="change-password-button">
              {loading ? 'Changing Password...' : 'Change Password'}
            </StandardizedButton>
          </Card>

          <View style={styles.securityTip}>
            <Ionicons name="shield-checkmark" size={20} color={colors.sage400} />
            <Text style={styles.securityTipText}>
              Use a strong password with a mix of letters, numbers, and symbols to keep your account secure.
            </Text>
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaScreen>
  );
};

const createStyles = (colors: any) => ({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    paddingHorizontal: getResponsiveSpacing(16),
    paddingVertical: getResponsiveSpacing(12),
    borderBottomWidth: 1,
    borderBottomColor: colors.border.primary,
  },
  backButton: {
    padding: getResponsiveSpacing(8),
    marginLeft: -getResponsiveSpacing(8),
  },
  headerTitle: {
    fontSize: getResponsiveFontSize(20),
    fontWeight: '600',
    color: colors.text.primary,
    flex: 1,
    textAlign: 'center' as const,
  },
  headerSpacer: {
    width: 40,
  },
  content: {
    flex: 1,
    paddingHorizontal: getResponsiveSpacing(16),
    paddingVertical: getResponsiveSpacing(24),
  },
  formCard: {
    padding: getResponsiveSpacing(24),
    marginBottom: getResponsiveSpacing(24),
  },
  formTitle: {
    fontSize: getResponsiveFontSize(24),
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: getResponsiveSpacing(8),
    textAlign: 'center' as const,
  },
  formSubtitle: {
    fontSize: getResponsiveFontSize(16),
    color: colors.text.secondary,
    textAlign: 'center' as const,
    marginBottom: getResponsiveSpacing(32),
  },
  formField: {
    marginBottom: getResponsiveSpacing(20),
  },
  fieldLabel: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '500',
    color: colors.text.primary,
    marginBottom: getResponsiveSpacing(8),
  },
  passwordContainer: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    borderWidth: 1,
    borderColor: colors.border.primary,
    borderRadius: 8,
    backgroundColor: colors.background.secondary,
  },
  passwordInput: {
    flex: 1,
    paddingHorizontal: getResponsiveSpacing(16),
    paddingVertical: getResponsiveSpacing(12),
    fontSize: getResponsiveFontSize(16),
    color: colors.text.primary,
  },
  eyeButton: {
    padding: getResponsiveSpacing(12),
  },
  inputError: {
    borderColor: colors.error,
  },
  errorText: {
    fontSize: getResponsiveFontSize(14),
    color: colors.error,
    marginTop: getResponsiveSpacing(4),
  },
  requirementsContainer: {
    marginBottom: getResponsiveSpacing(20),
    padding: getResponsiveSpacing(16),
    backgroundColor: colors.background.tertiary,
    borderRadius: 8,
  },
  requirementsTitle: {
    fontSize: getResponsiveFontSize(14),
    fontWeight: '500',
    color: colors.text.primary,
    marginBottom: getResponsiveSpacing(8),
  },
  requirementItem: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    marginBottom: getResponsiveSpacing(4),
  },
  requirementText: {
    fontSize: getResponsiveFontSize(14),
    color: colors.text.secondary,
    marginLeft: getResponsiveSpacing(8),
  },
  requirementMet: {
    color: colors.success,
  },
  changeButton: {
    marginTop: getResponsiveSpacing(24),
  },
  securityTip: {
    flexDirection: 'row' as const,
    alignItems: 'flex-start' as const,
    padding: getResponsiveSpacing(16),
    backgroundColor: colors.background.secondary,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: colors.sage400,
  },
  securityTipText: {
    fontSize: getResponsiveFontSize(14),
    color: colors.text.secondary,
    marginLeft: getResponsiveSpacing(12),
    flex: 1,
    lineHeight: 20,
  },
});
