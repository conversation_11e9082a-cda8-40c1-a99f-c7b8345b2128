"""
Serializers for messaging models
"""

from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import Conversation, Message, MessageAttachment

User = get_user_model()


class UserBasicSerializer(serializers.ModelSerializer):
    """Basic user serializer for messaging"""
    full_name = serializers.CharField(source='get_full_name', read_only=True)
    
    class Meta:
        model = User
        fields = ['id', 'full_name', 'email', 'role', 'avatar']


class MessageAttachmentSerializer(serializers.ModelSerializer):
    """Serializer for message attachments"""
    class Meta:
        model = MessageAttachment
        fields = [
            'id', 'file', 'file_name', 'file_size',
            'file_type', 'mime_type', 'created_at'
        ]


class MessageSerializer(serializers.ModelSerializer):
    """Serializer for messages"""
    sender = UserBasicSerializer(read_only=True)
    attachments = MessageAttachmentSerializer(many=True, read_only=True)
    reply_to = serializers.SerializerMethodField()
    
    class Meta:
        model = Message
        fields = [
            'id', 'conversation', 'sender', 'content', 'message_type',
            'reply_to', 'delivery_status', 'is_read', 'read_at',
            'attachments', 'metadata', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'sender', 'created_at', 'updated_at']
    
    def get_reply_to(self, obj):
        """Get reply_to message details"""
        if obj.reply_to:
            return {
                'id': obj.reply_to.id,
                'content': obj.reply_to.content[:100],  # Truncate for preview
                'sender': obj.reply_to.sender.get_full_name(),
                'created_at': obj.reply_to.created_at
            }
        return None


class MessageCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating messages"""
    attachments = serializers.ListField(
        child=serializers.FileField(),
        required=False,
        allow_empty=True
    )
    
    class Meta:
        model = Message
        fields = ['content', 'message_type', 'reply_to', 'attachments', 'metadata']
    
    def validate_content(self, value):
        """Validate message content"""
        if not value or not value.strip():
            raise serializers.ValidationError("Message content cannot be empty")
        return value.strip()


class ConversationSerializer(serializers.ModelSerializer):
    """Serializer for conversations"""
    participants = UserBasicSerializer(many=True, read_only=True)
    last_message = MessageSerializer(read_only=True)
    unread_count = serializers.SerializerMethodField()
    other_participant = serializers.SerializerMethodField()
    
    class Meta:
        model = Conversation
        fields = [
            'id', 'participants', 'conversation_type', 'booking',
            'title', 'last_message', 'unread_count', 'other_participant',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_unread_count(self, obj):
        """Get unread message count for current user"""
        request = self.context.get('request')
        if request and request.user:
            return obj.get_unread_count_for_user(request.user)
        return 0
    
    def get_other_participant(self, obj):
        """Get the other participant in a 2-person conversation"""
        request = self.context.get('request')
        if request and request.user:
            other_participant = obj.get_other_participant(request.user)
            if other_participant:
                return UserBasicSerializer(other_participant).data
        return None


class ConversationCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating conversations"""
    participants = serializers.ListField(
        child=serializers.IntegerField(),
        required=True
    )
    
    class Meta:
        model = Conversation
        fields = ['participants', 'conversation_type', 'booking', 'title']
    
    def validate_participants(self, value):
        """Validate participants"""
        if not value:
            raise serializers.ValidationError("At least one participant is required")
        
        # Check if all participants exist
        existing_users = User.objects.filter(id__in=value).count()
        if existing_users != len(value):
            raise serializers.ValidationError("Some participants do not exist")
        
        return value
    
    def create(self, validated_data):
        """Create conversation with participants"""
        participants = validated_data.pop('participants')
        conversation = Conversation.objects.create(**validated_data)
        
        # Add current user to participants if not already included
        request = self.context.get('request')
        if request and request.user and request.user.id not in participants:
            participants.append(request.user.id)
        
        conversation.participants.set(participants)
        return conversation
