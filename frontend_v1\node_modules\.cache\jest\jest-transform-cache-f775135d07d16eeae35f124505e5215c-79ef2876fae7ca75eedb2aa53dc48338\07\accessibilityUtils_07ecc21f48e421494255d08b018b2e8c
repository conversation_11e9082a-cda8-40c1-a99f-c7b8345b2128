4877895aaa550a662474b2139c99d86c
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.validateTouchTargetSize = exports.prefersReducedMotion = exports.meetsWCAGAA = exports.hexToRgb = exports.getWCAGCompliantColorLocal = exports.getWCAGCompliantColor = exports.getTouchTargetStyle = exports.getResponsiveSpacing = exports.getResponsiveFontSize = exports.getRelativeLuminance = exports.getRecommendedTouchTarget = exports.getMinimumTouchTarget = exports.getImageAccessibilityProps = exports.getFocusIndicatorStyle = exports.getContrastRatio = exports.enhanceAccessibilityProps = exports.default = exports.accessibilityUtils = exports.WCAG_STANDARDS = exports.WCAG_STANDARDS = exports.VoiceControlUtils = exports.TouchTargetUtils = exports.TouchTargetUtils = exports.SinglePointerUtils = exports.ScreenReaderUtils = exports.ScreenReaderUtils = exports.ImageAccessibilityUtils = exports.GestureAccessibilityUtils = exports.FocusUtils = exports.FocusUtils = exports.FocusManagementUtils = exports.ColorContrastUtils = exports.ColorContrastUtils = exports.CognitiveAccessibilityUtils = exports.AccessibilityUtils = exports.AccessibilityTestUtils = exports.AccessibilityTestUtils = void 0;
var _toConsumableArray2 = _interopRequireDefault(require("@babel/runtime/helpers/toConsumableArray"));
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _reactNative = require("react-native");
var _colorContrastAudit = require("./colorContrastAudit");
var WCAG_STANDARDS = exports.WCAG_STANDARDS = exports.WCAG_STANDARDS = {
  CONTRAST_RATIOS: {
    AA_NORMAL: 4.5,
    AA_LARGE: 3.0,
    AAA_NORMAL: 7.0,
    AAA_LARGE: 4.5
  },
  TOUCH_TARGETS: {
    MINIMUM_SIZE: 44,
    RECOMMENDED_SIZE: 48,
    SPACING: 8
  },
  TARGET_SIZE: {
    MINIMUM: 44
  },
  FOCUS_INDICATORS: {
    MIN_WIDTH: 2,
    RECOMMENDED_WIDTH: 3,
    OFFSET: 2,
    Z_INDEX_BASE: 9999,
    SHADOW_OPACITY: 0.4,
    SHADOW_RADIUS: 6
  }
};
if (typeof global !== 'undefined') {
  global.WCAG_STANDARDS = WCAG_STANDARDS;
}
var hexToRgb = exports.hexToRgb = function hexToRgb(hex) {
  var result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
};
var getRelativeLuminance = exports.getRelativeLuminance = function getRelativeLuminance(r, g, b) {
  var _map = [r, g, b].map(function (c) {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    }),
    _map2 = (0, _slicedToArray2.default)(_map, 3),
    rs = _map2[0],
    gs = _map2[1],
    bs = _map2[2];
  return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
};
var getContrastRatio = exports.getContrastRatio = function getContrastRatio(color1, color2) {
  var rgb1 = hexToRgb(color1);
  var rgb2 = hexToRgb(color2);
  if (!rgb1 || !rgb2) return 1;
  var lum1 = getRelativeLuminance(rgb1.r, rgb1.g, rgb1.b);
  var lum2 = getRelativeLuminance(rgb2.r, rgb2.g, rgb2.b);
  var brightest = Math.max(lum1, lum2);
  var darkest = Math.min(lum1, lum2);
  return (brightest + 0.05) / (darkest + 0.05);
};
var meetsWCAGAA = exports.meetsWCAGAA = function meetsWCAGAA(foreground, background) {
  var isLargeText = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
  var ratio = getContrastRatio(foreground, background);
  var requiredRatio = isLargeText ? WCAG_STANDARDS.CONTRAST_RATIOS.AA_LARGE : WCAG_STANDARDS.CONTRAST_RATIOS.AA_NORMAL;
  return ratio >= requiredRatio;
};
var getWCAGCompliantColorLocal = exports.getWCAGCompliantColorLocal = function getWCAGCompliantColorLocal(baseColor, backgroundColor) {
  var isLargeText = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
  if (meetsWCAGAA(baseColor, backgroundColor, isLargeText)) {
    return baseColor;
  }
  var compliantColors = {
    sage: {
      light: '#4A6B52',
      medium: '#3A5B42',
      dark: '#2A4B32',
      darker: '#1F3A26'
    },
    neutral: {
      dark: '#374151',
      darker: '#1F2937'
    }
  };
  if (backgroundColor === '#FFFFFF' || backgroundColor === '#F9FAFB') {
    return isLargeText ? compliantColors.sage.light : compliantColors.sage.medium;
  }
  return baseColor;
};
var getFocusIndicatorStyle = exports.getFocusIndicatorStyle = function getFocusIndicatorStyle() {
  var baseColor = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '#3B82F6';
  return {
    borderWidth: WCAG_STANDARDS.FOCUS_INDICATORS.RECOMMENDED_WIDTH,
    borderColor: baseColor,
    borderStyle: 'solid',
    shadowColor: baseColor,
    shadowOffset: {
      width: 0,
      height: 0
    },
    shadowOpacity: WCAG_STANDARDS.FOCUS_INDICATORS.SHADOW_OPACITY,
    shadowRadius: WCAG_STANDARDS.FOCUS_INDICATORS.SHADOW_RADIUS,
    elevation: _reactNative.Platform.OS === 'android' ? 8 : 0,
    zIndex: WCAG_STANDARDS.FOCUS_INDICATORS.Z_INDEX_BASE
  };
};
var FocusUtils = exports.FocusUtils = exports.FocusUtils = {
  ensureFocusVisible: function ensureFocusVisible(element) {
    if (_reactNative.Platform.OS === 'web') {
      if (element && element.focus) {
        element.focus();
        element.scrollIntoView == null || element.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
          inline: 'nearest'
        });
      }
    }
  },
  getEnhancedFocusStyle: function getEnhancedFocusStyle(color) {
    var focusColor = color || '#3B82F6';
    return Object.assign({}, getFocusIndicatorStyle(focusColor), {
      outlineWidth: WCAG_STANDARDS.FOCUS_INDICATORS.RECOMMENDED_WIDTH,
      outlineColor: focusColor,
      outlineStyle: 'solid',
      outlineOffset: WCAG_STANDARDS.FOCUS_INDICATORS.OFFSET
    });
  },
  checkFocusObscured: function checkFocusObscured(elementY) {
    var stickyFooterHeight = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 80;
    var screenHeight = _reactNative.Platform.OS === 'web' ? window.innerHeight : 800;
    var focusAreaBottom = elementY + WCAG_STANDARDS.TOUCH_TARGETS.MINIMUM_SIZE;
    var stickyFooterTop = screenHeight - stickyFooterHeight;
    return focusAreaBottom > stickyFooterTop;
  },
  getFocusIndicatorStyle: function getFocusIndicatorStyle(isFocused) {
    var baseStyle = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
    var _options$color = options.color,
      color = _options$color === void 0 ? '#3B82F6' : _options$color,
      _options$width = options.width,
      requestedWidth = _options$width === void 0 ? WCAG_STANDARDS.FOCUS_INDICATORS.RECOMMENDED_WIDTH : _options$width,
      _options$offset = options.offset,
      offset = _options$offset === void 0 ? WCAG_STANDARDS.FOCUS_INDICATORS.OFFSET : _options$offset,
      _options$preventObscu = options.preventObscuring,
      preventObscuring = _options$preventObscu === void 0 ? true : _options$preventObscu;
    var width = Math.max(WCAG_STANDARDS.FOCUS_INDICATORS.MIN_WIDTH, requestedWidth);
    if (!isFocused) {
      return baseStyle;
    }
    var baseStyles = Object.assign({}, baseStyle, {
      borderWidth: width,
      borderColor: color,
      borderStyle: 'solid',
      shadowColor: color,
      shadowOffset: {
        width: 0,
        height: 0
      },
      shadowOpacity: WCAG_STANDARDS.FOCUS_INDICATORS.SHADOW_OPACITY,
      shadowRadius: WCAG_STANDARDS.FOCUS_INDICATORS.SHADOW_RADIUS,
      overflow: 'visible',
      zIndex: preventObscuring ? WCAG_STANDARDS.FOCUS_INDICATORS.Z_INDEX_BASE : undefined,
      backgroundColor: `${color}10`,
      borderRadius: Math.max(baseStyle.borderRadius || 0, 4)
    });
    var platformStyles = _reactNative.Platform.select({
      web: {
        outlineWidth: width,
        outlineColor: color,
        outlineStyle: 'solid',
        outlineOffset: offset
      },
      android: {
        elevation: 6
      },
      ios: {
        elevation: 0
      },
      default: {}
    });
    return Object.assign({}, baseStyles, platformStyles);
  },
  ensureFocusNotObscured: function ensureFocusNotObscured(focusedElementStyle) {
    var stickyElements = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];
    var maxStickyZIndex = stickyElements.reduce(function (max, element) {
      if (element.position === 'sticky' || element.position === 'fixed') {
        return Math.max(max, element.zIndex || 0);
      }
      return max;
    }, 0);
    return Object.assign({}, focusedElementStyle, {
      zIndex: Math.max(focusedElementStyle.zIndex || 0, maxStickyZIndex + 1, WCAG_STANDARDS.FOCUS_INDICATORS.Z_INDEX_BASE)
    });
  },
  createFocusManager: function createFocusManager() {
    var currentFocusedElement = null;
    return {
      setFocus: function setFocus(element) {
        currentFocusedElement = element;
      },
      getCurrentFocus: function getCurrentFocus() {
        return currentFocusedElement;
      },
      clearFocus: function clearFocus() {
        currentFocusedElement = null;
      },
      moveFocus: function moveFocus(direction) {
        console.log(`Moving focus ${direction}`);
      }
    };
  }
};
var getMinimumTouchTarget = exports.getMinimumTouchTarget = function getMinimumTouchTarget() {
  return WCAG_STANDARDS.TOUCH_TARGETS.MINIMUM_SIZE;
};
var getRecommendedTouchTarget = exports.getRecommendedTouchTarget = function getRecommendedTouchTarget() {
  return WCAG_STANDARDS.TOUCH_TARGETS.RECOMMENDED_SIZE;
};
var validateTouchTargetSize = exports.validateTouchTargetSize = function validateTouchTargetSize(width, height) {
  var minSize = WCAG_STANDARDS.TOUCH_TARGETS.MINIMUM_SIZE;
  return width >= minSize && height >= minSize;
};
var getTouchTargetStyle = exports.getTouchTargetStyle = function getTouchTargetStyle(customSize) {
  var size = customSize || WCAG_STANDARDS.TOUCH_TARGETS.RECOMMENDED_SIZE;
  return {
    minWidth: size,
    minHeight: size,
    paddingHorizontal: Math.max(0, (size - 24) / 2),
    paddingVertical: Math.max(0, (size - 24) / 2)
  };
};
var TouchTargetUtils = exports.TouchTargetUtils = exports.TouchTargetUtils = {
  getSpacing: function getSpacing() {
    return WCAG_STANDARDS.TOUCH_TARGETS.SPACING;
  },
  getToolbarIconStyle: function getToolbarIconStyle() {
    return Object.assign({}, getTouchTargetStyle(WCAG_STANDARDS.TOUCH_TARGETS.RECOMMENDED_SIZE), {
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: 8
    });
  },
  validate: validateTouchTargetSize,
  validateTouchTarget: function validateTouchTarget(width, height) {
    var minSize = WCAG_STANDARDS.TOUCH_TARGETS.MINIMUM_SIZE;
    var issues = [];
    if (width < minSize) {
      issues.push(`Width ${width}px is below minimum ${minSize}px`);
    }
    if (height < minSize) {
      issues.push(`Height ${height}px is below minimum ${minSize}px`);
    }
    return {
      isValid: issues.length === 0,
      issues: issues
    };
  },
  getMinSize: getMinimumTouchTarget,
  getRecommendedSize: getRecommendedTouchTarget,
  getPlatformTouchTarget: function getPlatformTouchTarget() {
    return _reactNative.Platform.select({
      ios: 44,
      android: 48,
      default: 44
    });
  },
  calculateHitSlop: function calculateHitSlop(targetSize) {
    var minSize = WCAG_STANDARDS.TOUCH_TARGETS.MINIMUM_SIZE;
    var deficit = Math.max(0, minSize - targetSize);
    var slop = Math.ceil(deficit / 2);
    return {
      top: slop,
      bottom: slop,
      left: slop,
      right: slop
    };
  }
};
var getImageAccessibilityProps = exports.getImageAccessibilityProps = function getImageAccessibilityProps(type, description, action) {
  switch (type) {
    case 'decorative':
      return {
        accessibilityLabel: '',
        accessibilityRole: 'image',
        accessible: false,
        importantForAccessibility: 'no'
      };
    case 'informative':
      return {
        accessibilityLabel: description || 'Informative image',
        accessibilityRole: 'image',
        accessible: true,
        importantForAccessibility: 'yes'
      };
    case 'functional':
      return {
        accessibilityLabel: description || 'Interactive image',
        accessibilityHint: action ? `Double tap to ${action}` : undefined,
        accessibilityRole: 'imagebutton',
        accessible: true,
        importantForAccessibility: 'yes'
      };
    default:
      return {
        accessibilityLabel: description || 'Image',
        accessibilityRole: 'image',
        accessible: true,
        importantForAccessibility: 'yes'
      };
  }
};
var ImageAccessibilityUtils = exports.ImageAccessibilityUtils = {
  getDecorative: function getDecorative() {
    return getImageAccessibilityProps('decorative');
  },
  getInformative: function getInformative(description) {
    return getImageAccessibilityProps('informative', description);
  },
  getFunctional: function getFunctional(description, action) {
    return getImageAccessibilityProps('functional', description, action);
  },
  generateAltText: function generateAltText(type, entityName, action) {
    switch (type) {
      case 'logo':
        return entityName ? `${entityName} logo` : 'Company logo';
      case 'avatar':
        return entityName ? `${entityName} profile picture` : 'User profile picture';
      case 'service':
        return entityName ? `${entityName} service image` : 'Service image';
      case 'store':
        return entityName ? `${entityName} store image` : 'Store image';
      case 'icon':
        return action ? `${action} icon` : entityName || 'Icon';
      default:
        return entityName || 'Image';
    }
  }
};
var SinglePointerUtils = exports.SinglePointerUtils = {
  getDragAlternative: function getDragAlternative(onMove) {
    return {
      onMoveUp: function onMoveUp() {
        return onMove('up');
      },
      onMoveDown: function onMoveDown() {
        return onMove('down');
      },
      accessibilityActions: [{
        name: 'increment',
        label: 'Move up'
      }, {
        name: 'decrement',
        label: 'Move down'
      }],
      onAccessibilityAction: function onAccessibilityAction(event) {
        switch (event.nativeEvent.actionName) {
          case 'increment':
            onMove('up');
            break;
          case 'decrement':
            onMove('down');
            break;
        }
      }
    };
  },
  getSwipeAlternative: function getSwipeAlternative(onSwipeLeft, onSwipeRight, onSwipeUp, onSwipeDown) {
    return {
      buttons: [onSwipeLeft && {
        label: 'Previous',
        onPress: onSwipeLeft
      }, onSwipeRight && {
        label: 'Next',
        onPress: onSwipeRight
      }, onSwipeUp && {
        label: 'Up',
        onPress: onSwipeUp
      }, onSwipeDown && {
        label: 'Down',
        onPress: onSwipeDown
      }].filter(Boolean)
    };
  },
  getMultiTouchAlternative: function getMultiTouchAlternative(onPinch, onRotate) {
    return {
      zoomIn: onPinch ? function () {
        return onPinch(1.2);
      } : undefined,
      zoomOut: onPinch ? function () {
        return onPinch(0.8);
      } : undefined,
      rotateLeft: onRotate ? function () {
        return onRotate(-15);
      } : undefined,
      rotateRight: onRotate ? function () {
        return onRotate(15);
      } : undefined
    };
  }
};
var ScreenReaderUtils = exports.ScreenReaderUtils = exports.ScreenReaderUtils = {
  announceForAccessibility: function announceForAccessibility(message) {
    if (_reactNative.Platform.OS === 'ios' || _reactNative.Platform.OS === 'android') {
      _reactNative.AccessibilityInfo.announceForAccessibility(message);
    }
  },
  isScreenReaderEnabled: function () {
    var _isScreenReaderEnabled = (0, _asyncToGenerator2.default)(function* () {
      try {
        return yield _reactNative.AccessibilityInfo.isScreenReaderEnabled();
      } catch (_unused) {
        return false;
      }
    });
    function isScreenReaderEnabled() {
      return _isScreenReaderEnabled.apply(this, arguments);
    }
    return isScreenReaderEnabled;
  }(),
  getAccessibleLabel: function getAccessibleLabel(primary, secondary, state, position) {
    var parts = [primary];
    if (secondary) parts.push(secondary);
    if (state) parts.push(state);
    if (position) parts.push(position);
    return parts.join(', ');
  },
  generateAccessibleLabel: function generateAccessibleLabel(primary, state, context, hint) {
    var parts = [primary];
    if (state) parts.push(state);
    if (context) parts.push(context);
    if (hint) parts.push(hint);
    return parts.join(', ');
  },
  getSemanticRole: function getSemanticRole(componentType) {
    var roleMap = {
      button: 'button',
      input: 'text',
      checkbox: 'checkbox',
      radio: 'radio',
      link: 'link',
      image: 'image',
      text: 'text',
      header: 'header',
      list: 'list',
      listitem: 'listitem'
    };
    return roleMap[componentType] || 'none';
  }
};
var prefersReducedMotion = exports.prefersReducedMotion = function () {
  var _ref = (0, _asyncToGenerator2.default)(function* () {
    try {
      return yield _reactNative.AccessibilityInfo.isReduceMotionEnabled();
    } catch (_unused2) {
      return false;
    }
  });
  return function prefersReducedMotion() {
    return _ref.apply(this, arguments);
  };
}();
var getResponsiveSpacing = exports.getResponsiveSpacing = function getResponsiveSpacing(baseSpacing) {
  return Math.max(baseSpacing, WCAG_STANDARDS.TOUCH_TARGETS.SPACING);
};
var getResponsiveFontSize = exports.getResponsiveFontSize = function getResponsiveFontSize(baseFontSize) {
  return Math.max(baseFontSize, 12);
};
var enhanceAccessibilityProps = exports.enhanceAccessibilityProps = function enhanceAccessibilityProps(props) {
  return Object.assign({}, props, {
    accessible: props.accessible !== false,
    accessibilityRole: props.accessibilityRole || 'button',
    importantForAccessibility: props.importantForAccessibility || 'yes'
  });
};
var AccessibilityUtils = exports.AccessibilityUtils = {
  WCAG_STANDARDS: WCAG_STANDARDS,
  hexToRgb: hexToRgb,
  getRelativeLuminance: getRelativeLuminance,
  getContrastRatio: getContrastRatio,
  meetsWCAGAA: meetsWCAGAA,
  getWCAGCompliantColor: getWCAGCompliantColorLocal,
  FocusUtils: FocusUtils,
  getFocusIndicatorStyle: getFocusIndicatorStyle,
  TouchTargetUtils: TouchTargetUtils,
  getMinimumTouchTarget: getMinimumTouchTarget,
  getRecommendedTouchTarget: getRecommendedTouchTarget,
  validateTouchTargetSize: validateTouchTargetSize,
  getTouchTargetStyle: getTouchTargetStyle,
  ImageAccessibilityUtils: ImageAccessibilityUtils,
  getImageAccessibilityProps: getImageAccessibilityProps,
  SinglePointerUtils: SinglePointerUtils,
  ScreenReaderUtils: ScreenReaderUtils,
  prefersReducedMotion: prefersReducedMotion,
  getResponsiveSpacing: getResponsiveSpacing,
  getResponsiveFontSize: getResponsiveFontSize,
  enhanceAccessibilityProps: enhanceAccessibilityProps
};
Object.freeze(WCAG_STANDARDS);
Object.freeze(WCAG_STANDARDS.CONTRAST_RATIOS);
Object.freeze(WCAG_STANDARDS.TOUCH_TARGETS);
Object.freeze(WCAG_STANDARDS.TARGET_SIZE);
Object.freeze(WCAG_STANDARDS.FOCUS_INDICATORS);
var ColorContrastUtils = exports.ColorContrastUtils = exports.ColorContrastUtils = {
  hexToRgb: function hexToRgb(hex) {
    var result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  },
  getRelativeLuminance: function getRelativeLuminance(hex) {
    var rgb = ColorContrastUtils.hexToRgb(hex);
    if (!rgb) return 0;
    var r = rgb.r,
      g = rgb.g,
      b = rgb.b;
    var _map3 = [r, g, b].map(function (c) {
        c = c / 255;
        return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
      }),
      _map4 = (0, _slicedToArray2.default)(_map3, 3),
      rs = _map4[0],
      gs = _map4[1],
      bs = _map4[2];
    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
  },
  getContrastRatio: function getContrastRatio(color1, color2) {
    var l1 = ColorContrastUtils.getRelativeLuminance(color1);
    var l2 = ColorContrastUtils.getRelativeLuminance(color2);
    var lighter = Math.max(l1, l2);
    var darker = Math.min(l1, l2);
    return (lighter + 0.05) / (darker + 0.05);
  },
  validateContrast: function validateContrast(foreground, background) {
    var level = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'AA';
    var isLargeText = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;
    var ratio = ColorContrastUtils.getContrastRatio(foreground, background);
    var requiredRatio;
    if (level === 'AAA') {
      requiredRatio = isLargeText ? 4.5 : 7.0;
    } else {
      requiredRatio = isLargeText ? 3.0 : 4.5;
    }
    var isCompliant = ratio >= requiredRatio;
    var recommendation = '';
    if (!isCompliant) {
      var improvement = (requiredRatio / ratio).toFixed(2);
      recommendation = `Increase contrast by ${improvement}x to meet ${level} standards`;
    } else {
      recommendation = `Meets ${level} standards (${ratio.toFixed(2)}:1)`;
    }
    return {
      ratio: Math.round(ratio * 100) / 100,
      isCompliant: isCompliant,
      requiredRatio: requiredRatio,
      recommendation: recommendation
    };
  },
  getAccessibleTextColor: function getAccessibleTextColor(backgroundColor) {
    var whiteContrast = ColorContrastUtils.getContrastRatio('#FFFFFF', backgroundColor);
    var blackContrast = ColorContrastUtils.getContrastRatio('#000000', backgroundColor);
    return whiteContrast > blackContrast ? '#FFFFFF' : '#000000';
  },
  enhanceColorContrast: function enhanceColorContrast(color, targetBackground) {
    var targetRatio = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 4.5;
    var currentRatio = ColorContrastUtils.getContrastRatio(color, targetBackground);
    if (currentRatio >= targetRatio) {
      return color;
    }
    var rgb = ColorContrastUtils.hexToRgb(color);
    if (!rgb) return color;
    var backgroundLuminance = ColorContrastUtils.getRelativeLuminance(targetBackground);
    var enhanced;
    if (backgroundLuminance > 0.5) {
      var factor = 0.3;
      enhanced = {
        r: Math.max(0, Math.round(rgb.r * factor)),
        g: Math.max(0, Math.round(rgb.g * factor)),
        b: Math.max(0, Math.round(rgb.b * factor))
      };
    } else {
      var _factor = 2.5;
      enhanced = {
        r: Math.min(255, Math.round(rgb.r * _factor)),
        g: Math.min(255, Math.round(rgb.g * _factor)),
        b: Math.min(255, Math.round(rgb.b * _factor))
      };
    }
    var enhancedHex = `#${enhanced.r.toString(16).padStart(2, '0')}${enhanced.g.toString(16).padStart(2, '0')}${enhanced.b.toString(16).padStart(2, '0')}`;
    var newRatio = ColorContrastUtils.getContrastRatio(enhancedHex, targetBackground);
    if (newRatio >= targetRatio) {
      return enhancedHex;
    }
    return backgroundLuminance > 0.5 ? '#000000' : '#FFFFFF';
  }
};
var AccessibilityTestUtils = exports.AccessibilityTestUtils = exports.AccessibilityTestUtils = {
  auditComponent: function auditComponent(componentProps) {
    var _componentProps$style, _componentProps$style2;
    var issues = [];
    var warnings = [];
    var recommendations = [];
    if (!componentProps.accessibilityLabel && !componentProps.children) {
      issues.push('Missing accessibility label');
      recommendations.push('Add accessibilityLabel prop');
    }
    if (!componentProps.accessibilityRole) {
      warnings.push('Missing accessibility role');
      recommendations.push('Add appropriate accessibilityRole');
    }
    if ((_componentProps$style = componentProps.style) != null && _componentProps$style.width && (_componentProps$style2 = componentProps.style) != null && _componentProps$style2.height) {
      var validation = TouchTargetUtils.validateTouchTarget(componentProps.style.width, componentProps.style.height);
      if (!validation.isValid) {
        issues.push.apply(issues, (0, _toConsumableArray2.default)(validation.issues));
        recommendations.push.apply(recommendations, (0, _toConsumableArray2.default)(validation.recommendations));
      }
    }
    return {
      issues: issues,
      warnings: warnings,
      recommendations: recommendations
    };
  },
  generateAccessibilityReport: function generateAccessibilityReport(components) {
    var details = components.map(function (component, index) {
      return {
        componentIndex: index,
        audit: AccessibilityTestUtils.auditComponent(component)
      };
    });
    var totalIssues = details.reduce(function (sum, detail) {
      return sum + detail.audit.issues.length;
    }, 0);
    var totalWarnings = details.reduce(function (sum, detail) {
      return sum + detail.audit.warnings.length;
    }, 0);
    var complianceScore = Math.max(0, 100 - totalIssues * 10 - totalWarnings * 5);
    return {
      totalComponents: components.length,
      issuesFound: totalIssues,
      warningsFound: totalWarnings,
      complianceScore: complianceScore,
      details: details
    };
  }
};
var VoiceControlUtils = exports.VoiceControlUtils = {
  isVoiceControlAvailable: function isVoiceControlAvailable() {
    return _reactNative.Platform.OS === 'ios' && _reactNative.Platform.Version >= '13.0';
  },
  generateVoiceLabel: function generateVoiceLabel(text, context) {
    var cleanText = text.replace(/[^\w\s]/gi, '').toLowerCase();
    return context ? `${context} ${cleanText}` : cleanText;
  },
  createVoiceHints: function createVoiceHints(actions) {
    return `Available actions: ${actions.join(', ')}`;
  }
};
var GestureAccessibilityUtils = exports.GestureAccessibilityUtils = {
  needsAlternativeGestures: function () {
    var _needsAlternativeGestures = (0, _asyncToGenerator2.default)(function* () {
      try {
        var isReduceMotionEnabled = yield _reactNative.AccessibilityInfo.isReduceMotionEnabled();
        return isReduceMotionEnabled;
      } catch (_unused3) {
        return false;
      }
    });
    function needsAlternativeGestures() {
      return _needsAlternativeGestures.apply(this, arguments);
    }
    return needsAlternativeGestures;
  }(),
  getGestureAlternatives: function getGestureAlternatives(gestureType) {
    var alternatives = {
      swipe: ['double tap to activate', 'use navigation buttons'],
      pinch: ['use zoom controls', 'double tap to zoom'],
      longPress: ['use context menu button', 'double tap and hold'],
      drag: ['use move buttons', 'select and use arrow keys']
    };
    return alternatives[gestureType] || ['use alternative controls'];
  },
  createGestureInstructions: function createGestureInstructions(gesture, alternative) {
    return `Gesture: ${gesture}. Alternative: ${alternative}`;
  }
};
var CognitiveAccessibilityUtils = exports.CognitiveAccessibilityUtils = {
  simplifyText: function simplifyText(text) {
    var level = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'intermediate';
    if (level === 'basic') {
      return text.replace(/utilize/gi, 'use').replace(/facilitate/gi, 'help').replace(/approximately/gi, 'about').replace(/subsequently/gi, 'then');
    }
    return text;
  },
  estimateReadingTime: function estimateReadingTime(text) {
    var wordsPerMinute = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 200;
    var wordCount = text.split(/\s+/).length;
    var minutes = Math.ceil(wordCount / wordsPerMinute);
    return `Reading time: ${minutes} minute${minutes !== 1 ? 's' : ''}`;
  },
  createProgressIndicator: function createProgressIndicator(currentStep, totalSteps) {
    return `Step ${currentStep} of ${totalSteps}`;
  }
};
var FocusManagementUtils = exports.FocusManagementUtils = FocusUtils;
var accessibilityUtils = exports.accessibilityUtils = AccessibilityUtils;
var _default = exports.default = {
  WCAG_STANDARDS: WCAG_STANDARDS,
  ColorContrastUtils: ColorContrastUtils,
  TouchTargetUtils: TouchTargetUtils,
  FocusUtils: FocusUtils,
  FocusManagementUtils: FocusManagementUtils,
  ScreenReaderUtils: ScreenReaderUtils,
  AccessibilityTestUtils: AccessibilityTestUtils,
  VoiceControlUtils: VoiceControlUtils,
  GestureAccessibilityUtils: GestureAccessibilityUtils,
  CognitiveAccessibilityUtils: CognitiveAccessibilityUtils,
  getWCAGCompliantColor: getWCAGCompliantColorLocal,
  checkColorContrast: function checkColorContrast(foreground, background) {
    var level = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'AA';
    var textSize = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'normal';
    var ratio = (0, _colorContrastAudit.calculateContrastRatio)(foreground, background);
    var requiredRatio = level === 'AAA' ? textSize === 'large' ? 4.5 : 7.0 : textSize === 'large' ? 3.0 : 4.5;
    return ratio >= requiredRatio;
  }
};
var getWCAGCompliantColor = exports.getWCAGCompliantColor = getWCAGCompliantColorLocal;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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