{"version": 3, "names": ["originalConsoleError", "console", "error", "originalConsoleWarn", "warn", "message", "arguments", "length", "undefined", "includes", "apply", "global", "mockNavigate", "jest", "fn", "mockGoBack", "mockReset"], "sources": ["jestSetup.js"], "sourcesContent": ["/**\n * Jest Setup - Early Mocks\n *\n * This file runs before any tests and sets up essential mocks\n * that need to be available during module loading.\n */\n\n// Mock console methods to reduce noise in tests\nconst originalConsoleError = console.error;\nconst originalConsoleWarn = console.warn;\n\nconsole.error = (...args) => {\n  const message = args[0];\n  if (\n    typeof message === 'string' &&\n    (message.includes('Warning: ReactDOM.render is no longer supported') ||\n     message.includes('Warning: componentWillMount has been renamed') ||\n     message.includes('Warning: componentWillReceiveProps has been renamed') ||\n     message.includes('VirtualizedLists should never be nested'))\n  ) {\n    return;\n  }\n  originalConsoleError(...args);\n};\n\nconsole.warn = (...args) => {\n  const message = args[0];\n  if (\n    typeof message === 'string' &&\n    (message.includes('Animated: `useNativeDriver`') ||\n     message.includes('source.uri should not be an empty string'))\n  ) {\n    return;\n  }\n  originalConsoleWarn(...args);\n};\n\n// Global test utilities\nglobal.mockNavigate = jest.fn();\nglobal.mockGoBack = jest.fn();\nglobal.mockReset = jest.fn();\n"], "mappings": "AAQA,IAAMA,oBAAoB,GAAGC,OAAO,CAACC,KAAK;AAC1C,IAAMC,mBAAmB,GAAGF,OAAO,CAACG,IAAI;AAExCH,OAAO,CAACC,KAAK,GAAG,YAAa;EAC3B,IAAMG,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAC,SAAA,GAAAF,SAAA,GAAU;EACvB,IACE,OAAOD,OAAO,KAAK,QAAQ,KAC1BA,OAAO,CAACI,QAAQ,CAAC,iDAAiD,CAAC,IACnEJ,OAAO,CAACI,QAAQ,CAAC,8CAA8C,CAAC,IAChEJ,OAAO,CAACI,QAAQ,CAAC,qDAAqD,CAAC,IACvEJ,OAAO,CAACI,QAAQ,CAAC,yCAAyC,CAAC,CAAC,EAC7D;IACA;EACF;EACAT,oBAAoB,CAAAU,KAAA,SAAAJ,SAAQ,CAAC;AAC/B,CAAC;AAEDL,OAAO,CAACG,IAAI,GAAG,YAAa;EAC1B,IAAMC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAC,SAAA,GAAAF,SAAA,GAAU;EACvB,IACE,OAAOD,OAAO,KAAK,QAAQ,KAC1BA,OAAO,CAACI,QAAQ,CAAC,6BAA6B,CAAC,IAC/CJ,OAAO,CAACI,QAAQ,CAAC,0CAA0C,CAAC,CAAC,EAC9D;IACA;EACF;EACAN,mBAAmB,CAAAO,KAAA,SAAAJ,SAAQ,CAAC;AAC9B,CAAC;AAGDK,MAAM,CAACC,YAAY,GAAGC,IAAI,CAACC,EAAE,CAAC,CAAC;AAC/BH,MAAM,CAACI,UAAU,GAAGF,IAAI,CAACC,EAAE,CAAC,CAAC;AAC7BH,MAAM,CAACK,SAAS,GAAGH,IAAI,CAACC,EAAE,CAAC,CAAC", "ignoreList": []}