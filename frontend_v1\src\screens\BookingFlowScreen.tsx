/**
 * Booking Flow Screen - Complete Booking Process
 *
 * Component Contract:
 * - Manages the complete booking flow from service selection to confirmation
 * - Integrates service selection, time slot booking, customer info, and payment
 * - Provides step-by-step navigation through the booking process
 * - Connects with backend booking and payment APIs
 * - Implements proper error handling and loading states
 * - Follows responsive design and accessibility guidelines
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp, StackScreenProps } from '@react-navigation/stack';

import { SafeAreaScreen } from '../components/templates/SafeAreaScreen';
import { Card } from '../components/molecules/Card';
import { StandardizedButton } from '../components/atoms/StandardizedButton';
import { ProgressIndicator } from '../components/atoms/ProgressIndicator';
import { ServiceSelectionStep } from '../components/booking/ServiceSelectionStep';
import { TimeSlotSelectionStep } from '../components/booking/TimeSlotSelectionStep';
import { CustomerInfoStep } from '../components/booking/CustomerInfoStep';
import { PaymentStep } from '../components/booking/PaymentStep';
import { BookingSummaryStep } from '../components/booking/BookingSummaryStep';
import { useTheme } from '../contexts/ThemeContext';
import { useAuthStore } from '../store/authSlice';
import { getResponsiveSpacing, getResponsiveFontSize } from '../utils/responsiveUtils';
import { bookingService } from '../services/bookingService';
import { paymentService } from '../services/paymentService';

type BookingFlowScreenNavigationProp = StackNavigationProp<any, 'BookingFlow'>;
type BookingFlowScreenRouteProp = StackScreenProps<any, 'BookingFlow'>['route'];

interface BookingData {
  providerId: string;
  serviceId?: string;
  serviceName?: string;
  servicePrice?: number;
  serviceDuration?: number;
  selectedDate?: string;
  selectedTimeSlot?: string;
  customerInfo?: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    notes?: string;
  };
  paymentMethodId?: string;
  totalAmount?: number;
}

const BOOKING_STEPS = [
  { id: 'service', title: 'Select Service', icon: 'cut-outline' },
  { id: 'time', title: 'Choose Time', icon: 'time-outline' },
  { id: 'info', title: 'Your Info', icon: 'person-outline' },
  { id: 'payment', title: 'Payment', icon: 'card-outline' },
  { id: 'summary', title: 'Confirm', icon: 'checkmark-circle-outline' },
];

export const BookingFlowScreen: React.FC = () => {
  const { colors, isDark } = useTheme();
  const navigation = useNavigation<BookingFlowScreenNavigationProp>();
  const route = useRoute<BookingFlowScreenRouteProp>();
  const { user } = useAuthStore();
  const styles = createStyles(colors);
  
  const routeParams = route.params as any;
  
  const [currentStep, setCurrentStep] = useState(0);
  const [bookingData, setBookingData] = useState<BookingData>({
    providerId: routeParams?.providerId || '',
    serviceId: routeParams?.serviceId,
    serviceName: routeParams?.serviceName,
    servicePrice: routeParams?.servicePrice,
    serviceDuration: routeParams?.serviceDuration,
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Pre-fill customer info if user is logged in
    if (user) {
      setBookingData(prev => ({
        ...prev,
        customerInfo: {
          firstName: user.firstName || '',
          lastName: user.lastName || '',
          email: user.email || '',
          phone: user.phone || '',
        },
      }));
    }
  }, [user]);

  const handleNext = () => {
    if (currentStep < BOOKING_STEPS.length - 1) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1);
    } else {
      navigation.goBack();
    }
  };

  const handleStepComplete = (stepData: any) => {
    setBookingData(prev => ({ ...prev, ...stepData }));
    handleNext();
  };

  const handleBookingConfirm = async () => {
    try {
      setLoading(true);
      setError(null);

      // Validate booking data
      if (!bookingData.serviceId || !bookingData.selectedDate || !bookingData.selectedTimeSlot) {
        throw new Error('Missing required booking information');
      }

      // Create booking
      const booking = await bookingService.createBooking({
        providerId: bookingData.providerId,
        serviceId: bookingData.serviceId,
        scheduledDate: bookingData.selectedDate,
        scheduledTime: bookingData.selectedTimeSlot,
        customerInfo: bookingData.customerInfo,
        notes: bookingData.customerInfo?.notes,
      });

      // Process payment if payment method is selected
      if (bookingData.paymentMethodId && bookingData.totalAmount) {
        await paymentService.processPayment({
          bookingId: booking.id,
          paymentMethodId: bookingData.paymentMethodId,
          amount: bookingData.totalAmount,
        });
      }

      // Navigate to confirmation screen
      navigation.replace('BookingConfirmation', {
        bookingId: booking.id,
        bookingData,
      });

    } catch (err: any) {
      console.error('Booking failed:', err);
      setError(err.message || 'Failed to create booking');
      Alert.alert('Booking Failed', err.message || 'Please try again');
    } finally {
      setLoading(false);
    }
  };

  const renderStepIndicator = () => (
    <Card style={styles.stepIndicator}>
      <View style={styles.stepsContainer}>
        {BOOKING_STEPS.map((step, index) => (
          <View key={step.id} style={styles.stepItem}>
            <View style={[
              styles.stepCircle,
              index <= currentStep && styles.stepCircleActive,
              index < currentStep && styles.stepCircleCompleted,
            ]}>
              <Ionicons
                name={index < currentStep ? 'checkmark' : step.icon as any}
                size={16}
                color={index <= currentStep ? colors.background.primary : colors.text.tertiary}
              />
            </View>
            <Text style={[
              styles.stepTitle,
              index <= currentStep && styles.stepTitleActive,
            ]}>
              {step.title}
            </Text>
            {index < BOOKING_STEPS.length - 1 && (
              <View style={[
                styles.stepConnector,
                index < currentStep && styles.stepConnectorCompleted,
              ]} />
            )}
          </View>
        ))}
      </View>
    </Card>
  );

  const renderCurrentStep = () => {
    const currentStepId = BOOKING_STEPS[currentStep].id;

    switch (currentStepId) {
      case 'service':
        return (
          <ServiceSelectionStep
            providerId={bookingData.providerId}
            selectedServiceId={bookingData.serviceId}
            onServiceSelect={handleStepComplete}
          />
        );
      case 'time':
        return (
          <TimeSlotSelectionStep
            providerId={bookingData.providerId}
            serviceId={bookingData.serviceId!}
            serviceDuration={bookingData.serviceDuration}
            selectedDate={bookingData.selectedDate}
            selectedTimeSlot={bookingData.selectedTimeSlot}
            onTimeSlotSelect={handleStepComplete}
          />
        );
      case 'info':
        return (
          <CustomerInfoStep
            initialData={bookingData.customerInfo}
            onInfoComplete={handleStepComplete}
          />
        );
      case 'payment':
        return (
          <PaymentStep
            amount={bookingData.servicePrice || 0}
            onPaymentMethodSelect={handleStepComplete}
          />
        );
      case 'summary':
        return (
          <BookingSummaryStep
            bookingData={bookingData}
            onConfirm={handleBookingConfirm}
            loading={loading}
          />
        );
      default:
        return null;
    }
  };

  const renderNavigationButtons = () => {
    const isLastStep = currentStep === BOOKING_STEPS.length - 1;
    const isFirstStep = currentStep === 0;

    return (
      <View style={styles.navigationContainer}>
        <StandardizedButton
          action="back"
          onPress={handleBack}
          style={[styles.navButton, styles.backButton]}
          variant="outline"
          testID="back-button">
          {isFirstStep ? 'Cancel' : 'Back'}
        </StandardizedButton>

        {!isLastStep && (
          <StandardizedButton
            action="next"
            onPress={handleNext}
            style={[styles.navButton, styles.nextButton]}
            testID="next-button">
            Next
          </StandardizedButton>
        )}
      </View>
    );
  };

  return (
    <SafeAreaScreen
      backgroundColor={colors.background.primary}
      statusBarStyle={isDark ? 'light-content' : 'dark-content'}
      testID="booking-flow-screen">
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={styles.closeButton}
          testID="close-button">
          <Ionicons name="close" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Book Service</Text>
        <View style={styles.headerSpacer} />
      </View>

      {renderStepIndicator()}

      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}>
        
        {error && (
          <Card style={styles.errorCard}>
            <Text style={styles.errorText}>{error}</Text>
          </Card>
        )}

        {renderCurrentStep()}
      </ScrollView>

      {renderNavigationButtons()}
    </SafeAreaScreen>
  );
};

const createStyles = (colors: any) => ({
  header: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    paddingHorizontal: getResponsiveSpacing(16),
    paddingVertical: getResponsiveSpacing(12),
    borderBottomWidth: 1,
    borderBottomColor: colors.border.primary,
  },
  closeButton: {
    padding: getResponsiveSpacing(8),
    marginLeft: -getResponsiveSpacing(8),
  },
  headerTitle: {
    fontSize: getResponsiveFontSize(20),
    fontWeight: '600',
    color: colors.text.primary,
    flex: 1,
    textAlign: 'center' as const,
  },
  headerSpacer: {
    width: 40,
  },
  stepIndicator: {
    margin: getResponsiveSpacing(16),
    padding: getResponsiveSpacing(16),
  },
  stepsContainer: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'space-between' as const,
  },
  stepItem: {
    alignItems: 'center' as const,
    flex: 1,
    position: 'relative' as const,
  },
  stepCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.background.secondary,
    borderWidth: 2,
    borderColor: colors.border.primary,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    marginBottom: getResponsiveSpacing(8),
  },
  stepCircleActive: {
    backgroundColor: colors.sage400,
    borderColor: colors.sage400,
  },
  stepCircleCompleted: {
    backgroundColor: colors.sage500,
    borderColor: colors.sage500,
  },
  stepTitle: {
    fontSize: getResponsiveFontSize(12),
    color: colors.text.tertiary,
    textAlign: 'center' as const,
  },
  stepTitleActive: {
    color: colors.text.primary,
    fontWeight: '500',
  },
  stepConnector: {
    position: 'absolute' as const,
    top: 16,
    left: '50%',
    right: -50,
    height: 2,
    backgroundColor: colors.border.primary,
    zIndex: -1,
  },
  stepConnectorCompleted: {
    backgroundColor: colors.sage400,
  },
  container: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: getResponsiveSpacing(16),
    paddingBottom: getResponsiveSpacing(100),
  },
  errorCard: {
    marginBottom: getResponsiveSpacing(16),
    padding: getResponsiveSpacing(16),
    backgroundColor: colors.error + '10',
    borderLeftWidth: 4,
    borderLeftColor: colors.error,
  },
  errorText: {
    fontSize: getResponsiveFontSize(14),
    color: colors.error,
  },
  navigationContainer: {
    position: 'absolute' as const,
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row' as const,
    paddingHorizontal: getResponsiveSpacing(16),
    paddingVertical: getResponsiveSpacing(16),
    backgroundColor: colors.background.primary,
    borderTopWidth: 1,
    borderTopColor: colors.border.primary,
  },
  navButton: {
    flex: 1,
  },
  backButton: {
    marginRight: getResponsiveSpacing(12),
  },
  nextButton: {
    marginLeft: getResponsiveSpacing(12),
  },
});
