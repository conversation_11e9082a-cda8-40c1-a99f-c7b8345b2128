8b854be4e9cf06649704811af69b1dea
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.validateInteractiveColors = exports.testColorCombination = exports.getBestTextColor = exports.generateEnhancedCTAColors = exports.generateContrastReport = exports.ensureMinimumContrast = exports.enhancePrimaryCTAContrast = exports.default = exports.applyEnhancedColors = exports.EnhancedCTAColors = void 0;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _accessibilityUtils = require("./accessibilityUtils");
var enhancePrimaryCTAContrast = exports.enhancePrimaryCTAContrast = function enhancePrimaryCTAContrast(backgroundColor) {
  var textColor = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '#FFFFFF';
  var targetRatio = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 4.5;
  var currentRatio = _accessibilityUtils.ColorContrastUtils.getContrastRatio(textColor, backgroundColor);
  var result = {
    ratio: Math.round(currentRatio * 100) / 100,
    isCompliant: currentRatio >= targetRatio,
    isOptimal: currentRatio >= 7.0,
    recommendation: ''
  };
  if (currentRatio >= 7.0) {
    result.recommendation = `Excellent contrast (${result.ratio}:1) - Exceeds WCAG AAA standards`;
    return result;
  }
  if (currentRatio >= targetRatio) {
    result.recommendation = `Good contrast (${result.ratio}:1) - Meets WCAG AA standards`;
    return result;
  }
  var enhancedColor = _accessibilityUtils.ColorContrastUtils.enhanceColorContrast(backgroundColor, textColor, targetRatio);
  var enhancedRatio = _accessibilityUtils.ColorContrastUtils.getContrastRatio(textColor, enhancedColor);
  result.enhancedColor = enhancedColor;
  result.recommendation = `Enhanced from ${result.ratio}:1 to ${Math.round(enhancedRatio * 100) / 100}:1 for WCAG AA compliance`;
  return result;
};
var validateInteractiveColors = exports.validateInteractiveColors = function validateInteractiveColors() {
  var colors = {
    primary: '#5A7A63',
    primaryHover: '#4A6B52',
    primaryPressed: '#3A5B42',
    secondary: '#E1EDE4',
    destructive: '#DC2626'
  };
  var results = {};
  Object.entries(colors).forEach(function (_ref) {
    var _ref2 = (0, _slicedToArray2.default)(_ref, 2),
      key = _ref2[0],
      color = _ref2[1];
    var textColor = key === 'secondary' ? '#1F2937' : '#FFFFFF';
    results[key] = enhancePrimaryCTAContrast(color, textColor);
  });
  return results;
};
var generateEnhancedCTAColors = exports.generateEnhancedCTAColors = function generateEnhancedCTAColors() {
  var originalColors = {
    primary: '#5A7A63',
    primaryHover: '#4A6B52',
    primaryPressed: '#3A5B42'
  };
  var enhancedColors = {};
  Object.entries(originalColors).forEach(function (_ref3) {
    var _ref4 = (0, _slicedToArray2.default)(_ref3, 2),
      key = _ref4[0],
      color = _ref4[1];
    var validation = enhancePrimaryCTAContrast(color);
    enhancedColors[key] = validation.enhancedColor || color;
  });
  return enhancedColors;
};
var testColorCombination = exports.testColorCombination = function testColorCombination(foreground, background) {
  var context = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'General';
  var validation = enhancePrimaryCTAContrast(background, foreground);
  return {
    context: context,
    foreground: foreground,
    background: background,
    validation: validation
  };
};
var generateContrastReport = exports.generateContrastReport = function generateContrastReport() {
  var testCases = [{
    fg: '#FFFFFF',
    bg: '#5A7A63',
    context: 'Primary CTA Button'
  }, {
    fg: '#FFFFFF',
    bg: '#4A6B52',
    context: 'Primary CTA Hover'
  }, {
    fg: '#FFFFFF',
    bg: '#3A5B42',
    context: 'Primary CTA Pressed'
  }, {
    fg: '#1F2937',
    bg: '#E1EDE4',
    context: 'Secondary Button'
  }, {
    fg: '#FFFFFF',
    bg: '#DC2626',
    context: 'Destructive Button'
  }, {
    fg: '#1F2937',
    bg: '#FFFFFF',
    context: 'Primary Text'
  }, {
    fg: '#6B7280',
    bg: '#FFFFFF',
    context: 'Secondary Text'
  }, {
    fg: '#9CA3AF',
    bg: '#FFFFFF',
    context: 'Tertiary Text'
  }];
  var results = testCases.map(function (testCase) {
    return testColorCombination(testCase.fg, testCase.bg, testCase.context);
  });
  var compliantCount = results.filter(function (r) {
    return r.validation.isCompliant;
  }).length;
  var optimalCount = results.filter(function (r) {
    return r.validation.isOptimal;
  }).length;
  return {
    totalTests: results.length,
    compliantCount: compliantCount,
    optimalCount: optimalCount,
    complianceRate: Math.round(compliantCount / results.length * 100),
    results: results,
    summary: {
      wcagAA: `${compliantCount}/${results.length} combinations meet WCAG AA standards`,
      wcagAAA: `${optimalCount}/${results.length} combinations meet WCAG AAA standards`,
      overallStatus: compliantCount === results.length ? 'FULLY_COMPLIANT' : 'NEEDS_IMPROVEMENT'
    }
  };
};
var applyEnhancedColors = exports.applyEnhancedColors = function applyEnhancedColors() {
  var enhancedColors = generateEnhancedCTAColors();
  var report = generateContrastReport();
  console.log('🎨 Color Contrast Enhancement Report:');
  console.log(`📊 Compliance Rate: ${report.complianceRate}%`);
  console.log(`✅ WCAG AA: ${report.summary.wcagAA}`);
  console.log(`🌟 WCAG AAA: ${report.summary.wcagAAA}`);
  console.log(`🔍 Status: ${report.summary.overallStatus}`);
  report.results.forEach(function (result) {
    if (!result.validation.isCompliant) {
      console.log(`⚠️  ${result.context}: ${result.validation.recommendation}`);
    }
  });
  return {
    enhancedColors: enhancedColors,
    report: report
  };
};
var getBestTextColor = exports.getBestTextColor = function getBestTextColor(backgroundColor) {
  var whiteContrast = _accessibilityUtils.ColorContrastUtils.getContrastRatio('#FFFFFF', backgroundColor);
  var blackContrast = _accessibilityUtils.ColorContrastUtils.getContrastRatio('#000000', backgroundColor);
  return whiteContrast > blackContrast ? '#FFFFFF' : '#000000';
};
var ensureMinimumContrast = exports.ensureMinimumContrast = function ensureMinimumContrast(foregroundColor, backgroundColor) {
  var minimumRatio = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 4.5;
  var currentRatio = _accessibilityUtils.ColorContrastUtils.getContrastRatio(foregroundColor, backgroundColor);
  if (currentRatio >= minimumRatio) {
    return foregroundColor;
  }
  return getBestTextColor(backgroundColor);
};
var EnhancedCTAColors = exports.EnhancedCTAColors = {
  primary: '#5A7A63',
  primaryHover: '#4A6B52',
  primaryPressed: '#3A5B42',
  primaryText: '#FFFFFF',
  secondary: '#E1EDE4',
  secondaryText: '#1F2937',
  success: '#10B981',
  warning: '#F59E0B',
  error: '#DC2626',
  info: '#3B82F6'
};
var _default = exports.default = {
  enhancePrimaryCTAContrast: enhancePrimaryCTAContrast,
  validateInteractiveColors: validateInteractiveColors,
  generateEnhancedCTAColors: generateEnhancedCTAColors,
  testColorCombination: testColorCombination,
  generateContrastReport: generateContrastReport,
  applyEnhancedColors: applyEnhancedColors,
  getBestTextColor: getBestTextColor,
  ensureMinimumContrast: ensureMinimumContrast,
  EnhancedCTAColors: EnhancedCTAColors
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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