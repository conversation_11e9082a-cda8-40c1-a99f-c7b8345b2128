e573948e08d3930944dbfa03f5991b3d
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.fromBouncinessAndSpeed = fromBouncinessAndSpeed;
exports.fromOrigamiTensionAndFriction = fromOrigamiTensionAndFriction;
function stiffnessFromOrigamiValue(oValue) {
  return (oValue - 30) * 3.62 + 194;
}
function dampingFromOrigamiValue(oValue) {
  return (oValue - 8) * 3 + 25;
}
function fromOrigamiTensionAndFriction(tension, friction) {
  return {
    stiffness: stiffnessFromOrigamiValue(tension),
    damping: dampingFromOrigamiValue(friction)
  };
}
function fromBouncinessAndSpeed(bounciness, speed) {
  function normalize(value, startValue, endValue) {
    return (value - startValue) / (endValue - startValue);
  }
  function projectNormal(n, start, end) {
    return start + n * (end - start);
  }
  function linearInterpolation(t, start, end) {
    return t * end + (1 - t) * start;
  }
  function quadraticOutInterpolation(t, start, end) {
    return linearInterpolation(2 * t - t * t, start, end);
  }
  function b3Friction1(x) {
    return 0.0007 * Math.pow(x, 3) - 0.031 * Math.pow(x, 2) + 0.64 * x + 1.28;
  }
  function b3Friction2(x) {
    return 0.000044 * Math.pow(x, 3) - 0.006 * Math.pow(x, 2) + 0.36 * x + 2;
  }
  function b3Friction3(x) {
    return 0.00000045 * Math.pow(x, 3) - 0.000332 * Math.pow(x, 2) + 0.1078 * x + 5.84;
  }
  function b3Nobounce(tension) {
    if (tension <= 18) {
      return b3Friction1(tension);
    } else if (tension > 18 && tension <= 44) {
      return b3Friction2(tension);
    } else {
      return b3Friction3(tension);
    }
  }
  var b = normalize(bounciness / 1.7, 0, 20);
  b = projectNormal(b, 0, 0.8);
  var s = normalize(speed / 1.7, 0, 20);
  var bouncyTension = projectNormal(s, 0.5, 200);
  var bouncyFriction = quadraticOutInterpolation(b, b3Nobounce(bouncyTension), 0.01);
  return {
    stiffness: stiffnessFromOrigamiValue(bouncyTension),
    damping: dampingFromOrigamiValue(bouncyFriction)
  };
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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