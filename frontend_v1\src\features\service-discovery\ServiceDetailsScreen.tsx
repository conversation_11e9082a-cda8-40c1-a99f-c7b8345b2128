/**
 * Service Details Screen - Detailed Service Information Display
 *
 * Component Contract:
 * - Displays comprehensive service information
 * - Shows provider details and ratings
 * - Provides booking functionality
 * - Displays service images and gallery
 * - Shows reviews and testimonials
 * - Follows TDD methodology with comprehensive test coverage
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { useNavigation, useRoute } from '@react-navigation/native';
import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity } from 'react-native';
import { Platform, Dimensions } from 'react-native';
import { StyleSheet, ActivityIndicator, Alert, SafeAreaView, StatusBar,  } from 'react-native';

import { Box } from '../../components/atoms/Box';
import { Button } from '../../components/atoms/Button';
import { HeaderHelpButton } from '../../components/help';
import { useBookingFeedback } from '../../hooks/useActionFeedbackHooks';
import { useTheme } from '../../contexts/ThemeContext';
import { useAuthStore } from '../../store/authSlice';
import { providerService } from '../../services/providerService';
import { bookingService } from '../../services/bookingService';

import { ServiceDetails, Review } from './types';

// Get screen dimensions for responsive design
const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Responsive design utilities
const getResponsiveFontSize = (size: number) => {
  const scale = screenWidth / 375; // Base width (iPhone X)
  return Math.round(size * scale);
};

const getResponsiveSpacing = (spacing: number) => {
  const scale = screenWidth / 375;
  return Math.round(spacing * scale);
};

const isTablet = screenWidth >= 768;
const isSmallScreen = screenWidth < 375;

// Local interface for mock data compatibility
interface LocalServiceDetails {
  id: string;
  name: string;
  description: string;
  longDescription: string;
  category: string;
  price: number;
  duration: number;
  provider: {
    id: string;
    name: string;
    rating: number;
    reviewCount: number;
    location: string;
    address: string;
    phone: string;
    email: string;
  };
  images: string[];
  features: string[];
  reviews: Review[];
  availability: string[];
}

interface RouteParams {
  serviceId: string;
}

export const ServiceDetailsScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { serviceId } = route.params as RouteParams;
  const { colors } = useTheme();
  const { user } = useAuthStore();

  // Feedback hooks
  const { createBooking } = useBookingFeedback();

  // State
  const [service, setService] = useState<LocalServiceDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isBooking, setIsBooking] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isFavorite, setIsFavorite] = useState(false);

  // Load service details from backend
  useEffect(() => {
    fetchServiceDetails();
  }, [serviceId]);

  const fetchServiceDetails = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Load service details from backend
      const serviceData = await providerService.getServiceDetails(serviceId);

      // Transform backend data to match local interface
      const transformedService: LocalServiceDetails = {
        id: serviceData.id,
        name: serviceData.name,
        description: serviceData.description,
        longDescription: serviceData.long_description || serviceData.description,
        category: serviceData.category,
        price: serviceData.price,
        duration: serviceData.duration,
        provider: {
          id: serviceData.provider.id,
          name: serviceData.provider.business_name,
          rating: serviceData.provider.rating,
          reviewCount: serviceData.provider.review_count,
          location: serviceData.provider.location || 'Location not specified',
          address: serviceData.provider.address || '',
          phone: serviceData.provider.phone || '',
          email: serviceData.provider.email || '',
        },
        images: serviceData.images || [],
        features: serviceData.features || [],
        reviews: serviceData.reviews?.map(review => ({
          id: review.id,
          customerName: review.customer_name,
          rating: review.rating,
          comment: review.comment,
          date: review.created_at,
        })) || [],
      };

      setService(transformedService);

      // Check if service provider is in favorites
      // TODO: Implement favorites check
      setIsFavorite(false);

    } catch (err) {
      console.error('Failed to load service details:', err);
      setError('Failed to load service details');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBookService = async () => {
    if (!service) return;

    setIsBooking(true);
    try {
      // Navigate to booking flow with service details
      navigation.navigate('BookingFlow' as never, {
        serviceId: service.id,
        providerId: service.provider.id,
        serviceName: service.name,
        servicePrice: service.price,
        serviceDuration: service.duration,
      });
    } catch (error) {
      console.error('Navigation to booking failed:', error);
    } finally {
      setIsBooking(false);
    }
  };

  const handleToggleFavorite = async () => {
    if (!service) return;

    try {
      if (isFavorite) {
        await providerService.removeFromFavorites(service.provider.id);
        setIsFavorite(false);
      } else {
        await providerService.addToFavorites(service.provider.id);
        setIsFavorite(true);
      }
    } catch (error) {
      console.error('Failed to toggle favorite:', error);
    }
  };

  const renderStars = (rating: number) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <Text key={i} style={[styles.star, i <= rating && styles.starFilled]}>
          ★
        </Text>,
      );
    }
    return stars;
  };

  const renderReview = (review: Review) => (
    <View key={review.id} style={styles.reviewCard}>
      <View style={styles.reviewHeader}>
        <Text style={styles.reviewerName}>{review.customerName}</Text>
        <View style={styles.reviewRating}>{renderStars(review.rating)}</View>
      </View>
      <Text style={styles.reviewComment}>{review.comment}</Text>
      <Text style={styles.reviewDate}>{review.date}</Text>
    </View>
  );

  if (isLoading) {
    return (
      <View style={styles.loadingContainer} testID="loading-indicator">
        <ActivityIndicator size="large" color="#2A4B32" />
        <Text style={styles.loadingText}>Loading service details...</Text>
      </View>
    );
  }

  if (!service) {
    return (
      <SafeAreaView style={styles.errorContainer} testID="error-container">
        <StatusBar
          barStyle="dark-content"
          backgroundColor="#FFFFFF"
          translucent={false}
        />
        <Text style={styles.errorText}>Service not found</Text>
        <Button onPress={() => navigation.goBack()} style={styles.goBackButton}>
          Go Back
        </Button>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.safeArea} testID="service-details-screen">
      <StatusBar
        barStyle="dark-content"
        backgroundColor="#FFFFFF"
        translucent={false}
      />
      <ScrollView style={styles.container}>
        {/* Service Header */}
        <Box style={styles.serviceHeader}>
          <View style={styles.serviceHeaderContent}>
            <Text style={styles.serviceName}>{service.name}</Text>
            <Text style={styles.serviceCategory}>{service.category}</Text>
            <Text style={styles.serviceDescription}>{service.description}</Text>

            <View style={styles.serviceMeta}>
              <Text style={styles.servicePrice}>${service.price}</Text>
              <Text style={styles.serviceDuration}>
                {service.duration} minutes
              </Text>
            </View>
          </View>
          <HeaderHelpButton
            size="medium"
            testID="service-details-help-button"
          />
        </Box>

        {/* Provider Information */}
        <Box style={styles.providerSection}>
          <Text style={styles.sectionTitle}>Provider</Text>
          <Text style={styles.providerName}>{service.provider.name}</Text>
          <Text style={styles.providerLocation}>
            {service.provider.address}
          </Text>

          <View style={styles.providerRating}>
            <View style={styles.ratingStars}>
              {renderStars(Math.round(service.provider.rating))}
            </View>
            <Text style={styles.ratingText}>
              {service.provider.rating} ({service.provider.reviewCount} reviews)
            </Text>
          </View>

          <Text style={styles.providerContact}>
            Phone: {service.provider.phone}
          </Text>
        </Box>

        {/* Service Details */}
        <Box style={styles.detailsSection}>
          <Text style={styles.sectionTitle}>About This Service</Text>
          <Text style={styles.longDescription}>{service.longDescription}</Text>

          <Text style={styles.featuresTitle}>What's Included:</Text>
          {service.features.map((feature, index) => (
            <Text key={index} style={styles.featureItem}>
              • {feature}
            </Text>
          ))}
        </Box>

        {/* Reviews */}
        <Box style={styles.reviewsSection}>
          <Text style={styles.sectionTitle}>Reviews</Text>
          {service.reviews.map(renderReview)}
        </Box>

        {/* Booking Section */}
        <Box style={styles.bookingSection}>
          <Text style={styles.sectionTitle}>Available Times</Text>
          <View style={styles.timeSlots}>
            {service.availability.map((time, index) => (
              <TouchableOpacity key={index} style={styles.timeSlot}>
                <Text style={styles.timeSlotText}>{time}</Text>
              </TouchableOpacity>
            ))}
          </View>

          <Button
            testID="book-service-button"
            onPress={handleBookService}
            disabled={isBooking}
            style={styles.bookButton}>
            {isBooking ? 'Booking...' : 'Book This Service'}
          </Button>
        </Box>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: getResponsiveSpacing(24),
  },
  loadingText: {
    marginTop: getResponsiveSpacing(16),
    fontSize: getResponsiveFontSize(16),
    color: '#6B7280',
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: getResponsiveSpacing(24),
  },
  errorText: {
    fontSize: getResponsiveFontSize(18),
    color: '#EF4444',
    marginBottom: getResponsiveSpacing(24),
    textAlign: 'center',
    lineHeight: getResponsiveFontSize(24),
  },
  goBackButton: {
    minWidth: getResponsiveSpacing(120),
  },
  serviceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingHorizontal: getResponsiveSpacing(20),
    paddingVertical: getResponsiveSpacing(16),
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#E5E7EB',
    backgroundColor: '#FFFFFF',
  },
  serviceHeaderContent: {
    flex: 1,
  },
  serviceName: {
    fontSize: getResponsiveFontSize(24),
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: getResponsiveSpacing(4),
    lineHeight: getResponsiveFontSize(32),
  },
  serviceCategory: {
    fontSize: getResponsiveFontSize(14),
    color: '#2A4B32',
    fontWeight: '500',
    marginBottom: getResponsiveSpacing(8),
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  serviceDescription: {
    fontSize: getResponsiveFontSize(16),
    color: '#6B7280',
    marginBottom: getResponsiveSpacing(16),
    lineHeight: getResponsiveFontSize(22),
  },
  serviceMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexWrap: isSmallScreen ? 'wrap' : 'nowrap',
    gap: getResponsiveSpacing(12),
  },
  servicePrice: {
    fontSize: getResponsiveFontSize(20),
    fontWeight: 'bold',
    color: '#1F2937',
  },
  serviceDuration: {
    fontSize: getResponsiveFontSize(16),
    color: '#6B7280',
  },
  providerSection: {
    paddingHorizontal: getResponsiveSpacing(20),
    paddingVertical: getResponsiveSpacing(16),
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#E5E7EB',
    backgroundColor: '#FFFFFF',
  },
  sectionTitle: {
    fontSize: getResponsiveFontSize(18),
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: getResponsiveSpacing(12),
    lineHeight: getResponsiveFontSize(24),
  },
  providerName: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '500',
    color: '#1F2937',
    marginBottom: getResponsiveSpacing(4),
    lineHeight: getResponsiveFontSize(22),
  },
  providerLocation: {
    fontSize: getResponsiveFontSize(14),
    color: '#6B7280',
    marginBottom: getResponsiveSpacing(8),
    lineHeight: getResponsiveFontSize(20),
  },
  providerRating: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: getResponsiveSpacing(8),
    flexWrap: 'wrap',
  },
  ratingStars: {
    flexDirection: 'row',
    marginRight: getResponsiveSpacing(8),
  },
  star: {
    fontSize: getResponsiveFontSize(16),
    color: '#D1D5DB',
  },
  starFilled: {
    color: '#F59E0B',
  },
  ratingText: {
    fontSize: 14,
    color: '#6B7280',
  },
  providerContact: {
    fontSize: 14,
    color: '#6B7280',
  },
  detailsSection: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  longDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
    marginBottom: 16,
  },
  featuresTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1F2937',
    marginBottom: 8,
  },
  featureItem: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4,
  },
  reviewsSection: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  reviewCard: {
    backgroundColor: '#F9FAFB',
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
  },
  reviewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  reviewerName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1F2937',
  },
  reviewRating: {
    flexDirection: 'row',
  },
  reviewComment: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4,
  },
  reviewDate: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  bookingSection: {
    padding: 20,
  },
  timeSlots: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 20,
  },
  timeSlot: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#F3F4F6',
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#D1D5DB',
  },
  timeSlotText: {
    fontSize: 14,
    color: '#374151',
  },
  bookButton: {
    marginTop: 8,
  },
});
