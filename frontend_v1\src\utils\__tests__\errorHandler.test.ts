/**
 * <PERSON><PERSON><PERSON> Tests
 * Tests for the comprehensive error handling system
 */

import { errorHandler, ErrorType, ErrorSeverity, handleError, handleNetworkError } from '../errorHandler';

// Mock Haptics
jest.mock('expo-haptics', () => ({
  notificationAsync: jest.fn(),
  NotificationFeedbackType: {
    Warning: 'warning',
    Error: 'error',
  },
}));

// Mock Alert
jest.mock('react-native', () => ({
  Alert: {
    alert: jest.fn(),
  },
}));

describe('ErrorHandler', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    errorHandler.clearErrors();
  });

  describe('handleError', () => {
    it('should handle basic errors correctly', () => {
      const error = new Error('Test error');
      const result = handleError(error);

      expect(result).toMatchObject({
        type: ErrorType.UNKNOWN,
        message: 'Test error',
        severity: ErrorSeverity.LOW,
      });
      expect(result.id).toBeDefined();
      expect(result.timestamp).toBeInstanceOf(Date);
    });

    it('should handle errors with context', () => {
      const error = new Error('Test error');
      const context = { component: 'TestComponent', action: 'testAction' };
      const result = handleError(error, context);

      expect(result.context).toEqual(context);
    });

    it('should preserve AppError properties', () => {
      const appError = {
        id: 'test-id',
        type: ErrorType.VALIDATION,
        severity: ErrorSeverity.HIGH,
        message: 'Validation error',
        userMessage: 'Please fix the form',
        timestamp: new Date(),
      };

      const result = handleError(appError);
      expect(result).toMatchObject(appError);
    });
  });

  describe('handleNetworkError', () => {
    it('should handle network errors correctly', () => {
      const error = new Error('Network request failed');
      const result = handleNetworkError(error);

      expect(result).toMatchObject({
        type: ErrorType.NETWORK,
        severity: ErrorSeverity.MEDIUM,
        message: 'Network request failed',
        userMessage: 'Network connection issue. Please check your internet connection and try again.',
      });
    });
  });

  describe('error type detection', () => {
    it('should detect network errors', () => {
      const error = new Error('fetch failed');
      const result = handleError(error);
      expect(result.type).toBe(ErrorType.NETWORK);
    });

    it('should detect authentication errors', () => {
      const error = new Error('unauthorized access');
      const result = handleError(error);
      expect(result.type).toBe(ErrorType.AUTHENTICATION);
    });

    it('should detect authorization errors', () => {
      const error = new Error('forbidden resource');
      const result = handleError(error);
      expect(result.type).toBe(ErrorType.AUTHORIZATION);
    });

    it('should detect not found errors', () => {
      const error = new Error('resource not found');
      const result = handleError(error);
      expect(result.type).toBe(ErrorType.NOT_FOUND);
    });

    it('should detect server errors', () => {
      const error = new Error('internal server error');
      const result = handleError(error);
      expect(result.type).toBe(ErrorType.SERVER);
    });
  });

  describe('severity detection', () => {
    it('should detect critical errors', () => {
      const error = new Error('critical system failure');
      const result = handleError(error);
      expect(result.severity).toBe(ErrorSeverity.CRITICAL);
    });

    it('should detect high severity errors', () => {
      const error = new Error('unauthorized access');
      const result = handleError(error);
      expect(result.severity).toBe(ErrorSeverity.HIGH);
    });

    it('should detect medium severity errors', () => {
      const error = new Error('network timeout');
      const result = handleError(error);
      expect(result.severity).toBe(ErrorSeverity.MEDIUM);
    });

    it('should default to low severity', () => {
      const error = new Error('minor issue');
      const result = handleError(error);
      expect(result.severity).toBe(ErrorSeverity.LOW);
    });
  });

  describe('user message generation', () => {
    it('should generate appropriate network error messages', () => {
      const error = new Error('fetch failed');
      const result = handleError(error);
      expect(result.userMessage).toBe('Please check your internet connection and try again.');
    });

    it('should generate appropriate auth error messages', () => {
      const error = new Error('unauthorized');
      const result = handleError(error);
      expect(result.userMessage).toBe('Please log in to continue.');
    });

    it('should generate appropriate authorization error messages', () => {
      const error = new Error('forbidden');
      const result = handleError(error);
      expect(result.userMessage).toBe('You don\'t have permission to perform this action.');
    });

    it('should generate appropriate not found error messages', () => {
      const error = new Error('not found');
      const result = handleError(error);
      expect(result.userMessage).toBe('The requested item could not be found.');
    });

    it('should generate appropriate server error messages', () => {
      const error = new Error('server error');
      const result = handleError(error);
      expect(result.userMessage).toBe('Server is temporarily unavailable. Please try again later.');
    });

    it('should generate default error messages', () => {
      const error = new Error('unknown error');
      const result = handleError(error);
      expect(result.userMessage).toBe('An unexpected error occurred. Please try again.');
    });
  });

  describe('error statistics', () => {
    it('should track error statistics correctly', () => {
      // Generate some errors
      handleError(new Error('network error'));
      handleError(new Error('validation error'));
      handleError(new Error('critical error'));

      const stats = errorHandler.getErrorStats();
      
      expect(stats.total).toBe(3);
      expect(stats.byType[ErrorType.NETWORK]).toBe(1);
      expect(stats.byType[ErrorType.UNKNOWN]).toBe(2);
      expect(stats.bySeverity[ErrorSeverity.MEDIUM]).toBe(1);
      expect(stats.bySeverity[ErrorSeverity.CRITICAL]).toBe(1);
      expect(stats.bySeverity[ErrorSeverity.LOW]).toBe(1);
    });

    it('should clear errors correctly', () => {
      handleError(new Error('test error'));
      expect(errorHandler.getErrorStats().total).toBe(1);

      errorHandler.clearErrors();
      expect(errorHandler.getErrorStats().total).toBe(0);
    });
  });

  describe('error queue management', () => {
    it('should maintain queue size limit', () => {
      // Generate more than 100 errors (the max queue size)
      for (let i = 0; i < 105; i++) {
        handleError(new Error(`Error ${i}`));
      }

      const stats = errorHandler.getErrorStats();
      expect(stats.total).toBe(100); // Should be limited to 100
    });
  });

  describe('error ID generation', () => {
    it('should generate unique error IDs', () => {
      const error1 = handleError(new Error('Error 1'));
      const error2 = handleError(new Error('Error 2'));

      expect(error1.id).toBeDefined();
      expect(error2.id).toBeDefined();
      expect(error1.id).not.toBe(error2.id);
    });

    it('should generate IDs with correct format', () => {
      const error = handleError(new Error('Test error'));
      expect(error.id).toMatch(/^error_\d+_[a-z0-9]+$/);
    });
  });
});
