c9cb48fa352f548a10900f38394b2e6f
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _contrastValidator = require("../contrastValidator");
var _Colors = _interopRequireDefault(require("../../constants/Colors"));
describe('Button Contrast Validation - WCAG 2.1 AA Compliance', function () {
  describe('Primary Button Contrast', function () {
    it('should meet WCAG AA standards for primary button text', function () {
      var backgroundColors = [_Colors.default.interactive.primary.default, _Colors.default.interactive.primary.hover, _Colors.default.interactive.primary.pressed];
      var textColor = _Colors.default.interactive.primary.text;
      backgroundColors.forEach(function (backgroundColor, index) {
        var stateName = ['default', 'hover', 'pressed'][index];
        var validation = (0, _contrastValidator.validateContrast)(textColor, backgroundColor);
        expect(validation.isValid).toBe(true);
        expect(validation.ratio).toBeGreaterThanOrEqual(_contrastValidator.CONTRAST_STANDARDS.AA_NORMAL);
        console.log(`Primary ${stateName}: ${validation.ratio.toFixed(2)}:1 contrast`);
      });
    });
    it('should have appropriate disabled state contrast', function () {
      var validation = (0, _contrastValidator.validateContrast)(_Colors.default.interactive.primary.textDisabled, _Colors.default.interactive.primary.disabled);
      expect(validation.ratio).toBeGreaterThan(1.5);
      console.log(`Primary disabled: ${validation.ratio.toFixed(2)}:1 contrast`);
    });
  });
  describe('Secondary Button Contrast', function () {
    it('should meet WCAG AA standards for secondary button text on white background', function () {
      var validation = (0, _contrastValidator.validateContrast)(_Colors.default.interactive.secondary.text, '#FFFFFF');
      expect(validation.isValid).toBe(true);
      expect(validation.ratio).toBeGreaterThanOrEqual(_contrastValidator.CONTRAST_STANDARDS.AA_NORMAL);
      console.log(`Secondary text: ${validation.ratio.toFixed(2)}:1 contrast`);
    });
    it('should meet WCAG AA standards for secondary button border', function () {
      var validation = (0, _contrastValidator.validateContrast)(_Colors.default.interactive.secondary.border, '#FFFFFF');
      expect(validation.isValid).toBe(true);
      expect(validation.ratio).toBeGreaterThanOrEqual(_contrastValidator.CONTRAST_STANDARDS.NON_TEXT);
      console.log(`Secondary border: ${validation.ratio.toFixed(2)}:1 contrast`);
    });
  });
  describe('Destructive Button Contrast', function () {
    it('should meet WCAG AA standards for destructive button text', function () {
      var backgroundColors = [_Colors.default.interactive.destructive.default, _Colors.default.interactive.destructive.hover, _Colors.default.interactive.destructive.pressed];
      var textColor = _Colors.default.interactive.destructive.text;
      backgroundColors.forEach(function (backgroundColor, index) {
        var stateName = ['default', 'hover', 'pressed'][index];
        var validation = (0, _contrastValidator.validateContrast)(textColor, backgroundColor);
        expect(validation.isValid).toBe(true);
        expect(validation.ratio).toBeGreaterThanOrEqual(_contrastValidator.CONTRAST_STANDARDS.AA_NORMAL);
        console.log(`Destructive ${stateName}: ${validation.ratio.toFixed(2)}:1 contrast`);
      });
    });
  });
  describe('Ghost Button Contrast', function () {
    it('should meet WCAG AA standards for ghost button text on white background', function () {
      var validation = (0, _contrastValidator.validateContrast)(_Colors.default.interactive.ghost.text, '#FFFFFF');
      expect(validation.isValid).toBe(true);
      expect(validation.ratio).toBeGreaterThanOrEqual(_contrastValidator.CONTRAST_STANDARDS.AA_NORMAL);
      console.log(`Ghost text: ${validation.ratio.toFixed(2)}:1 contrast`);
    });
  });
  describe('Focus States Contrast', function () {
    it('should meet WCAG AA standards for focus indicators', function () {
      var focusColors = [_Colors.default.focus.ring, _Colors.default.focus.outline, _Colors.default.focus.sage];
      focusColors.forEach(function (focusColor, index) {
        var colorName = ['ring', 'outline', 'sage'][index];
        var validation = (0, _contrastValidator.validateContrast)(focusColor, '#FFFFFF');
        expect(validation.ratio).toBeGreaterThanOrEqual(_contrastValidator.CONTRAST_STANDARDS.NON_TEXT);
        console.log(`Focus ${colorName}: ${validation.ratio.toFixed(2)}:1 contrast`);
      });
    });
  });
  describe('Comprehensive Color Combinations', function () {
    it('should validate all interactive color combinations', function () {
      var combinations = [{
        name: 'Primary Default',
        foreground: _Colors.default.interactive.primary.text,
        background: _Colors.default.interactive.primary.default,
        expected: 'pass'
      }, {
        name: 'Primary Hover',
        foreground: _Colors.default.interactive.primary.text,
        background: _Colors.default.interactive.primary.hover,
        expected: 'pass'
      }, {
        name: 'Primary Pressed',
        foreground: _Colors.default.interactive.primary.text,
        background: _Colors.default.interactive.primary.pressed,
        expected: 'pass'
      }, {
        name: 'Secondary Text',
        foreground: _Colors.default.interactive.secondary.text,
        background: '#FFFFFF',
        expected: 'pass'
      }, {
        name: 'Secondary Border',
        foreground: _Colors.default.interactive.secondary.border,
        background: '#FFFFFF',
        expected: 'pass'
      }, {
        name: 'Destructive Default',
        foreground: _Colors.default.interactive.destructive.text,
        background: _Colors.default.interactive.destructive.default,
        expected: 'pass'
      }, {
        name: 'Ghost Text',
        foreground: _Colors.default.interactive.ghost.text,
        background: '#FFFFFF',
        expected: 'pass'
      }];
      var results = [];
      combinations.forEach(function (_ref) {
        var name = _ref.name,
          foreground = _ref.foreground,
          background = _ref.background,
          expected = _ref.expected;
        var validation = (0, _contrastValidator.validateContrast)(foreground, background);
        results.push({
          name: name,
          ratio: validation.ratio,
          isValid: validation.isValid
        });
        if (expected === 'pass') {
          expect(validation.isValid).toBe(true);
        }
        console.log(`${name}: ${validation.ratio.toFixed(2)}:1 - ${validation.isValid ? 'PASS' : 'FAIL'}`);
      });
      var passCount = results.filter(function (r) {
        return r.isValid;
      }).length;
      var totalCount = results.length;
      console.log(`\nContrast Validation Summary: ${passCount}/${totalCount} combinations passed`);
      expect(passCount).toBe(totalCount);
    });
  });
  describe('Large Text Contrast', function () {
    it('should meet WCAG AA standards for large text (18pt+)', function () {
      var combinations = [{
        name: 'Primary Large Text',
        foreground: _Colors.default.interactive.primary.text,
        background: _Colors.default.interactive.primary.default
      }, {
        name: 'Secondary Large Text',
        foreground: _Colors.default.interactive.secondary.text,
        background: '#FFFFFF'
      }];
      combinations.forEach(function (_ref2) {
        var name = _ref2.name,
          foreground = _ref2.foreground,
          background = _ref2.background;
        var validation = (0, _contrastValidator.validateContrast)(foreground, background, true);
        expect(validation.isValid).toBe(true);
        expect(validation.ratio).toBeGreaterThanOrEqual(_contrastValidator.CONTRAST_STANDARDS.AA_LARGE);
        console.log(`${name} (Large): ${validation.ratio.toFixed(2)}:1 contrast`);
      });
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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