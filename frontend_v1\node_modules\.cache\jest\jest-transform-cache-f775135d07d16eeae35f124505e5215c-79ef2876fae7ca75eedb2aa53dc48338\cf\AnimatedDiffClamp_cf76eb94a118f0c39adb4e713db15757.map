{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "default", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_get2", "_inherits2", "_AnimatedInterpolation", "_AnimatedWithChildren2", "_callSuper", "t", "o", "e", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_superPropGet", "r", "p", "AnimatedDiffClamp", "_Animated<PERSON>ith<PERSON><PERSON><PERSON><PERSON>", "a", "min", "max", "config", "_this", "_a", "_min", "_max", "_value", "_lastValue", "__getValue", "key", "__makeNative", "platformConfig", "interpolate", "AnimatedInterpolation", "diff", "Math", "__attach", "__add<PERSON><PERSON>d", "__detach", "__remove<PERSON><PERSON>d", "__getNativeConfig", "type", "input", "__getNativeTag", "debugID", "__getDebugID", "AnimatedWithChildren"], "sources": ["AnimatedDiffClamp.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n * @format\n */\n\n'use strict';\n\nimport type {PlatformConfig} from '../AnimatedPlatformConfig';\nimport type {InterpolationConfigType} from './AnimatedInterpolation';\nimport type AnimatedNode from './AnimatedNode';\nimport type {AnimatedNodeConfig} from './AnimatedNode';\n\nimport AnimatedInterpolation from './AnimatedInterpolation';\nimport AnimatedWithChildren from './AnimatedWithChildren';\n\nexport default class AnimatedDiffClamp extends AnimatedWithChildren {\n  _a: AnimatedNode;\n  _min: number;\n  _max: number;\n  _value: number;\n  _lastValue: number;\n\n  constructor(\n    a: AnimatedNode,\n    min: number,\n    max: number,\n    config?: ?AnimatedNodeConfig,\n  ) {\n    super(config);\n\n    this._a = a;\n    this._min = min;\n    this._max = max;\n    this._value = this._lastValue = this._a.__getValue();\n  }\n\n  __makeNative(platformConfig: ?PlatformConfig) {\n    this._a.__makeNative(platformConfig);\n    super.__makeNative(platformConfig);\n  }\n\n  interpolate<OutputT: number | string>(\n    config: InterpolationConfigType<OutputT>,\n  ): AnimatedInterpolation<OutputT> {\n    return new AnimatedInterpolation(this, config);\n  }\n\n  __getValue(): number {\n    const value = this._a.__getValue();\n    const diff = value - this._lastValue;\n    this._lastValue = value;\n    this._value = Math.min(Math.max(this._value + diff, this._min), this._max);\n    return this._value;\n  }\n\n  __attach(): void {\n    this._a.__addChild(this);\n    super.__attach();\n  }\n\n  __detach(): void {\n    this._a.__removeChild(this);\n    super.__detach();\n  }\n\n  __getNativeConfig(): any {\n    return {\n      type: 'diffclamp',\n      input: this._a.__getNativeTag(),\n      min: this._min,\n      max: this._max,\n      debugID: this.__getDebugID(),\n    };\n  }\n}\n"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAAA,IAAAC,gBAAA,GAAAP,sBAAA,CAAAC,OAAA;AAAA,IAAAO,aAAA,GAAAR,sBAAA,CAAAC,OAAA;AAAA,IAAAQ,2BAAA,GAAAT,sBAAA,CAAAC,OAAA;AAAA,IAAAS,gBAAA,GAAAV,sBAAA,CAAAC,OAAA;AAAA,IAAAU,KAAA,GAAAX,sBAAA,CAAAC,OAAA;AAAA,IAAAW,UAAA,GAAAZ,sBAAA,CAAAC,OAAA;AAOb,IAAAY,sBAAA,GAAAb,sBAAA,CAAAC,OAAA;AACA,IAAAa,sBAAA,GAAAd,sBAAA,CAAAC,OAAA;AAA0D,SAAAc,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAP,gBAAA,CAAAJ,OAAA,EAAAW,CAAA,OAAAR,2BAAA,CAAAH,OAAA,EAAAU,CAAA,EAAAG,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAJ,CAAA,EAAAC,CAAA,YAAAR,gBAAA,CAAAJ,OAAA,EAAAU,CAAA,EAAAM,WAAA,IAAAL,CAAA,CAAAM,KAAA,CAAAP,CAAA,EAAAE,CAAA;AAAA,SAAAC,0BAAA,cAAAH,CAAA,IAAAQ,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAR,CAAA,aAAAG,yBAAA,YAAAA,0BAAA,aAAAH,CAAA;AAAA,SAAAY,cAAAZ,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAW,CAAA,QAAAC,CAAA,OAAAnB,KAAA,CAAAL,OAAA,MAAAI,gBAAA,CAAAJ,OAAA,MAAAuB,CAAA,GAAAb,CAAA,CAAAS,SAAA,GAAAT,CAAA,GAAAC,CAAA,EAAAC,CAAA,cAAAW,CAAA,yBAAAC,CAAA,aAAAd,CAAA,WAAAc,CAAA,CAAAP,KAAA,CAAAL,CAAA,EAAAF,CAAA,OAAAc,CAAA;AAAA,IAErCC,iBAAiB,GAAA3B,OAAA,CAAAE,OAAA,aAAA0B,qBAAA;EAOpC,SAAAD,kBACEE,CAAe,EACfC,GAAW,EACXC,GAAW,EACXC,MAA4B,EAC5B;IAAA,IAAAC,KAAA;IAAA,IAAA9B,gBAAA,CAAAD,OAAA,QAAAyB,iBAAA;IACAM,KAAA,GAAAtB,UAAA,OAAAgB,iBAAA,GAAMK,MAAM;IAEZC,KAAA,CAAKC,EAAE,GAAGL,CAAC;IACXI,KAAA,CAAKE,IAAI,GAAGL,GAAG;IACfG,KAAA,CAAKG,IAAI,GAAGL,GAAG;IACfE,KAAA,CAAKI,MAAM,GAAGJ,KAAA,CAAKK,UAAU,GAAGL,KAAA,CAAKC,EAAE,CAACK,UAAU,CAAC,CAAC;IAAC,OAAAN,KAAA;EACvD;EAAC,IAAAzB,UAAA,CAAAN,OAAA,EAAAyB,iBAAA,EAAAC,qBAAA;EAAA,WAAAxB,aAAA,CAAAF,OAAA,EAAAyB,iBAAA;IAAAa,GAAA;IAAAvC,KAAA,EAED,SAAAwC,YAAYA,CAACC,cAA+B,EAAE;MAC5C,IAAI,CAACR,EAAE,CAACO,YAAY,CAACC,cAAc,CAAC;MACpClB,aAAA,CAAAG,iBAAA,4BAAmBe,cAAc;IACnC;EAAC;IAAAF,GAAA;IAAAvC,KAAA,EAED,SAAA0C,WAAWA,CACTX,MAAwC,EACR;MAChC,OAAO,IAAIY,8BAAqB,CAAC,IAAI,EAAEZ,MAAM,CAAC;IAChD;EAAC;IAAAQ,GAAA;IAAAvC,KAAA,EAED,SAAAsC,UAAUA,CAAA,EAAW;MACnB,IAAMtC,KAAK,GAAG,IAAI,CAACiC,EAAE,CAACK,UAAU,CAAC,CAAC;MAClC,IAAMM,IAAI,GAAG5C,KAAK,GAAG,IAAI,CAACqC,UAAU;MACpC,IAAI,CAACA,UAAU,GAAGrC,KAAK;MACvB,IAAI,CAACoC,MAAM,GAAGS,IAAI,CAAChB,GAAG,CAACgB,IAAI,CAACf,GAAG,CAAC,IAAI,CAACM,MAAM,GAAGQ,IAAI,EAAE,IAAI,CAACV,IAAI,CAAC,EAAE,IAAI,CAACC,IAAI,CAAC;MAC1E,OAAO,IAAI,CAACC,MAAM;IACpB;EAAC;IAAAG,GAAA;IAAAvC,KAAA,EAED,SAAA8C,QAAQA,CAAA,EAAS;MACf,IAAI,CAACb,EAAE,CAACc,UAAU,CAAC,IAAI,CAAC;MACxBxB,aAAA,CAAAG,iBAAA;IACF;EAAC;IAAAa,GAAA;IAAAvC,KAAA,EAED,SAAAgD,QAAQA,CAAA,EAAS;MACf,IAAI,CAACf,EAAE,CAACgB,aAAa,CAAC,IAAI,CAAC;MAC3B1B,aAAA,CAAAG,iBAAA;IACF;EAAC;IAAAa,GAAA;IAAAvC,KAAA,EAED,SAAAkD,iBAAiBA,CAAA,EAAQ;MACvB,OAAO;QACLC,IAAI,EAAE,WAAW;QACjBC,KAAK,EAAE,IAAI,CAACnB,EAAE,CAACoB,cAAc,CAAC,CAAC;QAC/BxB,GAAG,EAAE,IAAI,CAACK,IAAI;QACdJ,GAAG,EAAE,IAAI,CAACK,IAAI;QACdmB,OAAO,EAAE,IAAI,CAACC,YAAY,CAAC;MAC7B,CAAC;IACH;EAAC;AAAA,EA1D4CC,8BAAoB", "ignoreList": []}