/**
 * Error Handling Service - Comprehensive Error Management
 *
 * Service Contract:
 * - Provides centralized error handling and reporting
 * - Implements user-friendly error messages and recovery
 * - Manages error logging and analytics
 * - Provides error boundary functionality
 * - Implements retry mechanisms and fallback strategies
 * - Supports offline error handling and sync
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { Alert } from 'react-native';

export interface ErrorContext {
  component?: string;
  action?: string;
  userId?: string;
  sessionId?: string;
  timestamp: string;
  deviceInfo?: {
    platform: string;
    version: string;
    model?: string;
  };
  networkStatus?: 'online' | 'offline';
  additionalData?: Record<string, any>;
}

export interface ErrorReport {
  id: string;
  type: 'network' | 'validation' | 'authentication' | 'permission' | 'system' | 'unknown';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  stack?: string;
  context: ErrorContext;
  userMessage: string;
  recoveryActions: RecoveryAction[];
  retryable: boolean;
  reported: boolean;
}

export interface RecoveryAction {
  id: string;
  label: string;
  action: () => Promise<void> | void;
  primary?: boolean;
}

export interface ErrorHandlingConfig {
  enableLogging: boolean;
  enableReporting: boolean;
  maxRetries: number;
  retryDelay: number;
  offlineStorage: boolean;
  userFeedback: boolean;
}

class ErrorHandlingService {
  private config: ErrorHandlingConfig = {
    enableLogging: true,
    enableReporting: true,
    maxRetries: 3,
    retryDelay: 1000,
    offlineStorage: true,
    userFeedback: true,
  };

  private errorQueue: ErrorReport[] = [];
  private retryAttempts: Map<string, number> = new Map();
  private errorListeners: Set<(error: ErrorReport) => void> = new Set();

  constructor() {
    this.loadOfflineErrors();
    this.setupGlobalErrorHandlers();
  }

  // Error Processing
  async handleError(
    error: Error | any,
    context: Partial<ErrorContext> = {},
    userMessage?: string
  ): Promise<ErrorReport> {
    const errorReport = await this.createErrorReport(error, context, userMessage);
    
    // Log error
    if (this.config.enableLogging) {
      this.logError(errorReport);
    }

    // Store offline if needed
    if (this.config.offlineStorage) {
      await this.storeErrorOffline(errorReport);
    }

    // Notify listeners
    this.notifyErrorListeners(errorReport);

    // Show user feedback if enabled
    if (this.config.userFeedback && errorReport.severity !== 'low') {
      this.showUserFeedback(errorReport);
    }

    // Report to analytics/crash reporting
    if (this.config.enableReporting) {
      await this.reportError(errorReport);
    }

    return errorReport;
  }

  private async createErrorReport(
    error: Error | any,
    context: Partial<ErrorContext>,
    userMessage?: string
  ): Promise<ErrorReport> {
    const errorType = this.determineErrorType(error);
    const severity = this.determineSeverity(error, errorType);
    const recoveryActions = this.generateRecoveryActions(error, errorType);

    return {
      id: this.generateErrorId(),
      type: errorType,
      severity,
      message: error.message || String(error),
      stack: error.stack,
      context: {
        timestamp: new Date().toISOString(),
        ...context,
      },
      userMessage: userMessage || this.generateUserMessage(errorType, severity),
      recoveryActions,
      retryable: this.isRetryable(error, errorType),
      reported: false,
    };
  }

  private determineErrorType(error: any): ErrorReport['type'] {
    if (error.name === 'NetworkError' || error.code === 'NETWORK_ERROR') {
      return 'network';
    }
    if (error.status === 401 || error.code === 'UNAUTHORIZED') {
      return 'authentication';
    }
    if (error.status === 403 || error.code === 'FORBIDDEN') {
      return 'permission';
    }
    if (error.name === 'ValidationError' || error.code === 'VALIDATION_ERROR') {
      return 'validation';
    }
    if (error.name === 'TypeError' || error.name === 'ReferenceError') {
      return 'system';
    }
    return 'unknown';
  }

  private determineSeverity(error: any, type: ErrorReport['type']): ErrorReport['severity'] {
    if (type === 'authentication' || type === 'system') {
      return 'critical';
    }
    if (type === 'network' || type === 'permission') {
      return 'high';
    }
    if (type === 'validation') {
      return 'medium';
    }
    return 'low';
  }

  private generateRecoveryActions(error: any, type: ErrorReport['type']): RecoveryAction[] {
    const actions: RecoveryAction[] = [];

    switch (type) {
      case 'network':
        actions.push({
          id: 'retry',
          label: 'Try Again',
          action: () => this.retryLastAction(),
          primary: true,
        });
        actions.push({
          id: 'offline',
          label: 'Work Offline',
          action: () => this.enableOfflineMode(),
        });
        break;

      case 'authentication':
        actions.push({
          id: 'login',
          label: 'Login Again',
          action: () => this.redirectToLogin(),
          primary: true,
        });
        break;

      case 'validation':
        actions.push({
          id: 'correct',
          label: 'Correct Input',
          action: () => this.focusOnError(),
          primary: true,
        });
        break;

      default:
        actions.push({
          id: 'retry',
          label: 'Try Again',
          action: () => this.retryLastAction(),
          primary: true,
        });
        actions.push({
          id: 'report',
          label: 'Report Issue',
          action: () => this.reportIssue(),
        });
        break;
    }

    return actions;
  }

  private generateUserMessage(type: ErrorReport['type'], severity: ErrorReport['severity']): string {
    const messages = {
      network: {
        low: 'Connection issue. Please check your internet.',
        medium: 'Network error. Please try again.',
        high: 'Unable to connect. Please check your connection.',
        critical: 'Critical network error. Please contact support.',
      },
      authentication: {
        low: 'Please log in again.',
        medium: 'Authentication required.',
        high: 'Session expired. Please log in.',
        critical: 'Authentication failed. Please contact support.',
      },
      validation: {
        low: 'Please check your input.',
        medium: 'Some information is missing or incorrect.',
        high: 'Please correct the highlighted fields.',
        critical: 'Critical validation error.',
      },
      permission: {
        low: 'Permission required.',
        medium: 'Access denied.',
        high: 'You don\'t have permission for this action.',
        critical: 'Critical permission error.',
      },
      system: {
        low: 'Minor system issue.',
        medium: 'System error occurred.',
        high: 'System error. Please try again.',
        critical: 'Critical system error. Please contact support.',
      },
      unknown: {
        low: 'Something went wrong.',
        medium: 'An error occurred.',
        high: 'Unexpected error. Please try again.',
        critical: 'Critical error. Please contact support.',
      },
    };

    return messages[type][severity];
  }

  private isRetryable(error: any, type: ErrorReport['type']): boolean {
    const retryableTypes = ['network', 'system'];
    const nonRetryableStatuses = [400, 401, 403, 404, 422];
    
    if (error.status && nonRetryableStatuses.includes(error.status)) {
      return false;
    }

    return retryableTypes.includes(type);
  }

  // Error Recovery Actions
  private async retryLastAction(): Promise<void> {
    // Implement retry logic based on context
    console.log('Retrying last action...');
  }

  private async enableOfflineMode(): Promise<void> {
    // Enable offline mode
    console.log('Enabling offline mode...');
  }

  private async redirectToLogin(): Promise<void> {
    // Redirect to login screen
    console.log('Redirecting to login...');
  }

  private async focusOnError(): Promise<void> {
    // Focus on error field
    console.log('Focusing on error field...');
  }

  private async reportIssue(): Promise<void> {
    // Open issue reporting
    console.log('Opening issue reporting...');
  }

  // User Feedback
  private showUserFeedback(errorReport: ErrorReport): void {
    if (errorReport.severity === 'critical') {
      Alert.alert(
        'Critical Error',
        errorReport.userMessage,
        errorReport.recoveryActions.map(action => ({
          text: action.label,
          onPress: action.action,
          style: action.primary ? 'default' : 'cancel',
        }))
      );
    } else {
      // Show toast or in-app notification for non-critical errors
      console.log(`Error: ${errorReport.userMessage}`);
    }
  }

  // Error Reporting and Analytics
  private async reportError(errorReport: ErrorReport): Promise<void> {
    try {
      // Report to crash analytics service
      console.log('Reporting error to analytics:', errorReport.id);
      errorReport.reported = true;
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  }

  private logError(errorReport: ErrorReport): void {
    console.error('Error Report:', {
      id: errorReport.id,
      type: errorReport.type,
      severity: errorReport.severity,
      message: errorReport.message,
      context: errorReport.context,
    });
  }

  // Offline Error Management
  private async storeErrorOffline(errorReport: ErrorReport): Promise<void> {
    try {
      const offlineErrors = await this.getOfflineErrors();
      offlineErrors.push(errorReport);
      
      // Keep only last 100 errors
      const limitedErrors = offlineErrors.slice(-100);
      
      await AsyncStorage.setItem('offline_errors', JSON.stringify(limitedErrors));
    } catch (error) {
      console.error('Failed to store error offline:', error);
    }
  }

  private async getOfflineErrors(): Promise<ErrorReport[]> {
    try {
      const stored = await AsyncStorage.getItem('offline_errors');
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Failed to get offline errors:', error);
      return [];
    }
  }

  private async loadOfflineErrors(): Promise<void> {
    this.errorQueue = await this.getOfflineErrors();
  }

  // Event Listeners
  addErrorListener(listener: (error: ErrorReport) => void): void {
    this.errorListeners.add(listener);
  }

  removeErrorListener(listener: (error: ErrorReport) => void): void {
    this.errorListeners.delete(listener);
  }

  private notifyErrorListeners(errorReport: ErrorReport): void {
    this.errorListeners.forEach(listener => {
      try {
        listener(errorReport);
      } catch (error) {
        console.error('Error in error listener:', error);
      }
    });
  }

  // Global Error Handlers
  private setupGlobalErrorHandlers(): void {
    // Handle unhandled promise rejections
    if (typeof window !== 'undefined') {
      window.addEventListener('unhandledrejection', (event) => {
        this.handleError(event.reason, { component: 'global', action: 'unhandled_rejection' });
      });
    }

    // Handle React Native errors
    const originalConsoleError = console.error;
    console.error = (...args) => {
      if (args[0] && typeof args[0] === 'string' && args[0].includes('Warning:')) {
        // Skip React warnings
        originalConsoleError.apply(console, args);
        return;
      }

      this.handleError(new Error(args.join(' ')), { component: 'global', action: 'console_error' });
      originalConsoleError.apply(console, args);
    };
  }

  // Utility Methods
  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Public API
  getErrorQueue(): ErrorReport[] {
    return [...this.errorQueue];
  }

  clearErrorQueue(): void {
    this.errorQueue = [];
    AsyncStorage.removeItem('offline_errors');
  }

  updateConfig(config: Partial<ErrorHandlingConfig>): void {
    this.config = { ...this.config, ...config };
  }
}

// Create singleton instance
export const errorHandlingService = new ErrorHandlingService();

export default errorHandlingService;
