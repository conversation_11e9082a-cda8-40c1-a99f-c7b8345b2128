/**
 * Network Error Fallback Component - Specialized fallback for network errors
 *
 * Component Contract:
 * - Displays user-friendly network error messages
 * - Provides retry and offline mode options
 * - Shows network status and connectivity info
 * - Implements progressive error recovery
 * - Tracks network error analytics
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import { performanceMonitor } from '../../services/performanceMonitor';

interface NetworkErrorFallbackProps {
  error?: Error;
  onRetry?: () => void;
  onOfflineMode?: () => void;
  retryCount?: number;
  maxRetries?: number;
  showOfflineOption?: boolean;
  customMessage?: string;
  testID?: string;
}

interface NetworkStatus {
  isConnected: boolean;
  connectionType: string;
  isInternetReachable: boolean;
}

export const NetworkErrorFallback: React.FC<NetworkErrorFallbackProps> = ({
  error,
  onRetry,
  onOfflineMode,
  retryCount = 0,
  maxRetries = 3,
  showOfflineOption = true,
  customMessage,
  testID = 'network-error-fallback',
}) => {
  const { colors } = useTheme();
  const [isRetrying, setIsRetrying] = useState(false);
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>({
    isConnected: true,
    connectionType: 'unknown',
    isInternetReachable: true,
  });

  // Determine error type and message
  const getErrorInfo = () => {
    if (customMessage) {
      return {
        title: 'Connection Issue',
        message: customMessage,
        icon: 'cloud-offline-outline' as const,
      };
    }

    if (error?.message.includes('timeout')) {
      return {
        title: 'Request Timeout',
        message: 'The request took too long to complete. Please check your connection and try again.',
        icon: 'time-outline' as const,
      };
    }

    if (error?.message.includes('Network request failed')) {
      return {
        title: 'Network Error',
        message: 'Unable to connect to our servers. Please check your internet connection.',
        icon: 'wifi-outline' as const,
      };
    }

    if (error?.message.includes('404')) {
      return {
        title: 'Service Unavailable',
        message: 'The requested service is temporarily unavailable. Please try again later.',
        icon: 'server-outline' as const,
      };
    }

    if (error?.message.includes('500')) {
      return {
        title: 'Server Error',
        message: 'Our servers are experiencing issues. Please try again in a few moments.',
        icon: 'warning-outline' as const,
      };
    }

    return {
      title: 'Connection Problem',
      message: 'Something went wrong with the connection. Please try again.',
      icon: 'cloud-offline-outline' as const,
    };
  };

  const errorInfo = getErrorInfo();
  const canRetry = retryCount < maxRetries;

  // Handle retry with performance tracking
  const handleRetry = async () => {
    if (!canRetry || isRetrying) return;

    setIsRetrying(true);
    
    // Track retry attempt
    performanceMonitor.trackUserInteraction('network_error_retry', 0, {
      errorType: error?.name || 'NetworkError',
      retryCount: retryCount + 1,
      errorMessage: error?.message,
    });

    try {
      await onRetry?.();
    } finally {
      setIsRetrying(false);
    }
  };

  // Handle offline mode
  const handleOfflineMode = () => {
    performanceMonitor.trackUserInteraction('network_error_offline_mode', 0, {
      errorType: error?.name || 'NetworkError',
      retryCount,
    });

    onOfflineMode?.();
  };

  // Get retry button text
  const getRetryButtonText = () => {
    if (isRetrying) return 'Retrying...';
    if (retryCount === 0) return 'Try Again';
    return `Try Again (${retryCount}/${maxRetries})`;
  };

  // Get connection status color
  const getConnectionStatusColor = () => {
    if (!networkStatus.isConnected) return colors.error;
    if (!networkStatus.isInternetReachable) return colors.warning;
    return colors.success;
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background.primary }]} testID={testID}>
      {/* Error Icon */}
      <View style={[styles.iconContainer, { backgroundColor: colors.background.secondary }]}>
        <Ionicons
          name={errorInfo.icon}
          size={64}
          color={colors.text.secondary}
          testID={`${testID}-icon`}
        />
      </View>

      {/* Error Title */}
      <Text
        style={[styles.title, { color: colors.text.primary }]}
        testID={`${testID}-title`}
        accessibilityRole="header"
      >
        {errorInfo.title}
      </Text>

      {/* Error Message */}
      <Text
        style={[styles.message, { color: colors.text.secondary }]}
        testID={`${testID}-message`}
        accessibilityRole="text"
      >
        {errorInfo.message}
      </Text>

      {/* Network Status Indicator */}
      <View style={styles.statusContainer}>
        <View style={styles.statusRow}>
          <View
            style={[
              styles.statusDot,
              { backgroundColor: getConnectionStatusColor() }
            ]}
          />
          <Text style={[styles.statusText, { color: colors.text.tertiary }]}>
            {networkStatus.isConnected
              ? networkStatus.isInternetReachable
                ? 'Connected'
                : 'Limited connectivity'
              : 'No connection'
            }
          </Text>
        </View>
      </View>

      {/* Action Buttons */}
      <View style={styles.actionsContainer}>
        {/* Retry Button */}
        {canRetry && (
          <TouchableOpacity
            style={[
              styles.retryButton,
              {
                backgroundColor: colors.interactive.primary.default,
                opacity: isRetrying ? 0.6 : 1,
              }
            ]}
            onPress={handleRetry}
            disabled={isRetrying}
            testID={`${testID}-retry-button`}
            accessibilityRole="button"
            accessibilityLabel={`Retry connection. Attempt ${retryCount + 1} of ${maxRetries}`}
            accessibilityHint="Double tap to retry the failed network request"
          >
            {isRetrying ? (
              <ActivityIndicator
                size="small"
                color={colors.interactive.primary.text}
              />
            ) : (
              <Ionicons
                name="refresh-outline"
                size={20}
                color={colors.interactive.primary.text}
              />
            )}
            <Text
              style={[
                styles.retryButtonText,
                { color: colors.interactive.primary.text }
              ]}
            >
              {getRetryButtonText()}
            </Text>
          </TouchableOpacity>
        )}

        {/* Max Retries Reached */}
        {!canRetry && (
          <View style={styles.maxRetriesContainer}>
            <Text style={[styles.maxRetriesText, { color: colors.text.tertiary }]}>
              Maximum retry attempts reached
            </Text>
          </View>
        )}

        {/* Offline Mode Button */}
        {showOfflineOption && (
          <TouchableOpacity
            style={[
              styles.offlineButton,
              {
                borderColor: colors.interactive.secondary.border,
                backgroundColor: colors.interactive.secondary.default,
              }
            ]}
            onPress={handleOfflineMode}
            testID={`${testID}-offline-button`}
            accessibilityRole="button"
            accessibilityLabel="Continue in offline mode"
            accessibilityHint="Double tap to use the app with limited functionality"
          >
            <Ionicons
              name="cloud-offline-outline"
              size={20}
              color={colors.interactive.secondary.text}
            />
            <Text
              style={[
                styles.offlineButtonText,
                { color: colors.interactive.secondary.text }
              ]}
            >
              Continue Offline
            </Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Technical Details (Development Mode) */}
      {__DEV__ && error && (
        <View style={[styles.debugContainer, { backgroundColor: colors.background.tertiary }]}>
          <Text style={[styles.debugTitle, { color: colors.text.secondary }]}>
            Debug Info:
          </Text>
          <Text style={[styles.debugText, { color: colors.text.tertiary }]}>
            {error.message}
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 12,
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
    maxWidth: 300,
  },
  statusContainer: {
    marginBottom: 32,
  },
  statusRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  statusText: {
    fontSize: 14,
  },
  actionsContainer: {
    width: '100%',
    maxWidth: 300,
  },
  retryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    marginBottom: 12,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  maxRetriesContainer: {
    alignItems: 'center',
    marginBottom: 12,
  },
  maxRetriesText: {
    fontSize: 14,
    textAlign: 'center',
  },
  offlineButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    borderWidth: 1,
  },
  offlineButtonText: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
  debugContainer: {
    position: 'absolute',
    bottom: 24,
    left: 24,
    right: 24,
    padding: 16,
    borderRadius: 8,
  },
  debugTitle: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 4,
  },
  debugText: {
    fontSize: 11,
    fontFamily: 'monospace',
  },
});

export default NetworkErrorFallback;
