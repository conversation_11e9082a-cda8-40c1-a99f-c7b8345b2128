{"version": 3, "names": ["_authService", "require", "global", "fetch", "jest", "fn", "describe", "beforeEach", "clearAllMocks", "it", "_asyncToGenerator2", "default", "mockResponse", "access", "refresh", "user", "id", "email", "first_name", "last_name", "role", "is_verified", "mockResolvedValueOnce", "ok", "json", "_json", "apply", "arguments", "result", "authService", "login", "password", "expect", "toHaveBeenCalledWith", "stringContaining", "objectContaining", "method", "headers", "body", "JSON", "stringify", "toEqual", "mockError", "detail", "_json2", "rejects", "toThrow", "mockRejectedValueOnce", "Error", "_json3", "register", "_json4", "toBe", "errors", "_json5", "_json6", "refreshToken", "_json7", "logout", "resolves", "not"], "sources": ["authService.test.ts"], "sourcesContent": ["/**\n * Authentication Service Tests - TDD Implementation\n * Following Red-Green-Refactor methodology\n */\n\nimport { authService } from '../authService';\n\n// Mock fetch globally\nglobal.fetch = jest.fn();\n\ndescribe('AuthService', () => {\n  beforeEach(() => {\n    jest.clearAllMocks();\n  });\n\n  describe('login', () => {\n    it('should successfully login with valid credentials', async () => {\n      const mockResponse = {\n        access: 'mock-access-token',\n        refresh: 'mock-refresh-token',\n        user: {\n          id: '1',\n          email: '<EMAIL>',\n          first_name: '<PERSON>',\n          last_name: '<PERSON><PERSON>',\n          role: 'customer',\n          is_verified: true,\n        },\n      };\n\n      (fetch as jest.Mock).mockResolvedValueOnce({\n        ok: true,\n        json: async () => mockResponse,\n      });\n\n      const result = await authService.login({\n        email: '<EMAIL>',\n        password: 'password123',\n      });\n\n      expect(fetch).toHaveBeenCalledWith(\n        expect.stringContaining('/api/auth/login/'),\n        expect.objectContaining({\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({\n            email: '<EMAIL>',\n            password: 'password123',\n          }),\n        }),\n      );\n\n      expect(result).toEqual(mockResponse);\n    });\n\n    it('should handle login failure with error message', async () => {\n      const mockError = {\n        detail: 'Invalid credentials',\n      };\n\n      (fetch as jest.Mock).mockResolvedValueOnce({\n        ok: false,\n        json: async () => mockError,\n      });\n\n      await expect(\n        authService.login({\n          email: '<EMAIL>',\n          password: 'wrongpassword',\n        }),\n      ).rejects.toThrow('Invalid credentials');\n    });\n\n    it('should handle network errors', async () => {\n      (fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));\n\n      await expect(\n        authService.login({\n          email: '<EMAIL>',\n          password: 'password123',\n        }),\n      ).rejects.toThrow('Network error');\n    });\n  });\n\n  describe('register', () => {\n    it('should successfully register a new customer', async () => {\n      const mockResponse = {\n        access: 'mock-access-token',\n        refresh: 'mock-refresh-token',\n        user: {\n          id: '2',\n          email: '<EMAIL>',\n          first_name: 'Jane',\n          last_name: 'Smith',\n          role: 'customer',\n          is_verified: false,\n        },\n      };\n\n      (fetch as jest.Mock).mockResolvedValueOnce({\n        ok: true,\n        json: async () => mockResponse,\n      });\n\n      const result = await authService.register({\n        first_name: 'Jane',\n        last_name: 'Smith',\n        email: '<EMAIL>',\n        password: 'password123',\n        role: 'customer',\n      });\n\n      expect(fetch).toHaveBeenCalledWith(\n        expect.stringContaining('/api/auth/register/'),\n        expect.objectContaining({\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({\n            first_name: 'Jane',\n            last_name: 'Smith',\n            email: '<EMAIL>',\n            password: 'password123',\n            role: 'customer',\n          }),\n        }),\n      );\n\n      expect(result).toEqual(mockResponse);\n    });\n\n    it('should successfully register a new service provider', async () => {\n      const mockResponse = {\n        access: 'mock-access-token',\n        refresh: 'mock-refresh-token',\n        user: {\n          id: '3',\n          email: '<EMAIL>',\n          first_name: 'Bob',\n          last_name: 'Johnson',\n          role: 'service_provider',\n          is_verified: false,\n        },\n      };\n\n      (fetch as jest.Mock).mockResolvedValueOnce({\n        ok: true,\n        json: async () => mockResponse,\n      });\n\n      const result = await authService.register({\n        first_name: 'Bob',\n        last_name: 'Johnson',\n        email: '<EMAIL>',\n        password: 'password123',\n        role: 'service_provider',\n      });\n\n      expect(result.user.role).toBe('service_provider');\n    });\n\n    it('should handle registration validation errors', async () => {\n      const mockError = {\n        errors: {\n          email: ['This email is already registered'],\n        },\n      };\n\n      (fetch as jest.Mock).mockResolvedValueOnce({\n        ok: false,\n        json: async () => mockError,\n      });\n\n      await expect(\n        authService.register({\n          first_name: 'Jane',\n          last_name: 'Smith',\n          email: '<EMAIL>',\n          password: 'password123',\n          role: 'customer',\n        }),\n      ).rejects.toThrow('This email is already registered');\n    });\n  });\n\n  describe('refreshToken', () => {\n    it('should successfully refresh access token', async () => {\n      const mockResponse = {\n        access: 'new-access-token',\n      };\n\n      (fetch as jest.Mock).mockResolvedValueOnce({\n        ok: true,\n        json: async () => mockResponse,\n      });\n\n      const result = await authService.refreshToken('refresh-token');\n\n      expect(fetch).toHaveBeenCalledWith(\n        expect.stringContaining('/api/auth/token/refresh/'),\n        expect.objectContaining({\n          method: 'POST',\n          body: JSON.stringify({ refresh: 'refresh-token' }),\n        }),\n      );\n\n      expect(result).toEqual(mockResponse);\n    });\n  });\n\n  describe('logout', () => {\n    it('should successfully logout', async () => {\n      (fetch as jest.Mock).mockResolvedValueOnce({\n        ok: true,\n        json: async () => ({}),\n      });\n\n      await expect(authService.logout('refresh-token')).resolves.not.toThrow();\n\n      expect(fetch).toHaveBeenCalledWith(\n        expect.stringContaining('/api/auth/logout/'),\n        expect.objectContaining({\n          method: 'POST',\n          body: JSON.stringify({ refresh: 'refresh-token' }),\n        }),\n      );\n    });\n\n    it('should handle logout errors gracefully', async () => {\n      (fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));\n\n      // Should not throw - logout errors are not critical\n      await expect(authService.logout('refresh-token')).resolves.not.toThrow();\n    });\n  });\n});\n"], "mappings": ";;AAKA,IAAAA,YAAA,GAAAC,OAAA;AAGAC,MAAM,CAACC,KAAK,GAAGC,IAAI,CAACC,EAAE,CAAC,CAAC;AAExBC,QAAQ,CAAC,aAAa,EAAE,YAAM;EAC5BC,UAAU,CAAC,YAAM;IACfH,IAAI,CAACI,aAAa,CAAC,CAAC;EACtB,CAAC,CAAC;EAEFF,QAAQ,CAAC,OAAO,EAAE,YAAM;IACtBG,EAAE,CAAC,kDAAkD,MAAAC,kBAAA,CAAAC,OAAA,EAAE,aAAY;MACjE,IAAMC,YAAY,GAAG;QACnBC,MAAM,EAAE,mBAAmB;QAC3BC,OAAO,EAAE,oBAAoB;QAC7BC,IAAI,EAAE;UACJC,EAAE,EAAE,GAAG;UACPC,KAAK,EAAE,kBAAkB;UACzBC,UAAU,EAAE,MAAM;UAClBC,SAAS,EAAE,KAAK;UAChBC,IAAI,EAAE,UAAU;UAChBC,WAAW,EAAE;QACf;MACF,CAAC;MAEAlB,KAAK,CAAemB,qBAAqB,CAAC;QACzCC,EAAE,EAAE,IAAI;QACRC,IAAI;UAAA,IAAAC,KAAA,OAAAf,kBAAA,CAAAC,OAAA,EAAE;YAAA,OAAYC,YAAY;UAAA;UAAA,SAA9BY,IAAIA,CAAA;YAAA,OAAAC,KAAA,CAAAC,KAAA,OAAAC,SAAA;UAAA;UAAA,OAAJH,IAAI;QAAA;MACN,CAAC,CAAC;MAEF,IAAMI,MAAM,SAASC,wBAAW,CAACC,KAAK,CAAC;QACrCb,KAAK,EAAE,kBAAkB;QACzBc,QAAQ,EAAE;MACZ,CAAC,CAAC;MAEFC,MAAM,CAAC7B,KAAK,CAAC,CAAC8B,oBAAoB,CAChCD,MAAM,CAACE,gBAAgB,CAAC,kBAAkB,CAAC,EAC3CF,MAAM,CAACG,gBAAgB,CAAC;QACtBC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBvB,KAAK,EAAE,kBAAkB;UACzBc,QAAQ,EAAE;QACZ,CAAC;MACH,CAAC,CACH,CAAC;MAEDC,MAAM,CAACJ,MAAM,CAAC,CAACa,OAAO,CAAC7B,YAAY,CAAC;IACtC,CAAC,EAAC;IAEFH,EAAE,CAAC,gDAAgD,MAAAC,kBAAA,CAAAC,OAAA,EAAE,aAAY;MAC/D,IAAM+B,SAAS,GAAG;QAChBC,MAAM,EAAE;MACV,CAAC;MAEAxC,KAAK,CAAemB,qBAAqB,CAAC;QACzCC,EAAE,EAAE,KAAK;QACTC,IAAI;UAAA,IAAAoB,MAAA,OAAAlC,kBAAA,CAAAC,OAAA,EAAE;YAAA,OAAY+B,SAAS;UAAA;UAAA,SAA3BlB,IAAIA,CAAA;YAAA,OAAAoB,MAAA,CAAAlB,KAAA,OAAAC,SAAA;UAAA;UAAA,OAAJH,IAAI;QAAA;MACN,CAAC,CAAC;MAEF,MAAMQ,MAAM,CACVH,wBAAW,CAACC,KAAK,CAAC;QAChBb,KAAK,EAAE,kBAAkB;QACzBc,QAAQ,EAAE;MACZ,CAAC,CACH,CAAC,CAACc,OAAO,CAACC,OAAO,CAAC,qBAAqB,CAAC;IAC1C,CAAC,EAAC;IAEFrC,EAAE,CAAC,8BAA8B,MAAAC,kBAAA,CAAAC,OAAA,EAAE,aAAY;MAC5CR,KAAK,CAAe4C,qBAAqB,CAAC,IAAIC,KAAK,CAAC,eAAe,CAAC,CAAC;MAEtE,MAAMhB,MAAM,CACVH,wBAAW,CAACC,KAAK,CAAC;QAChBb,KAAK,EAAE,kBAAkB;QACzBc,QAAQ,EAAE;MACZ,CAAC,CACH,CAAC,CAACc,OAAO,CAACC,OAAO,CAAC,eAAe,CAAC;IACpC,CAAC,EAAC;EACJ,CAAC,CAAC;EAEFxC,QAAQ,CAAC,UAAU,EAAE,YAAM;IACzBG,EAAE,CAAC,6CAA6C,MAAAC,kBAAA,CAAAC,OAAA,EAAE,aAAY;MAC5D,IAAMC,YAAY,GAAG;QACnBC,MAAM,EAAE,mBAAmB;QAC3BC,OAAO,EAAE,oBAAoB;QAC7BC,IAAI,EAAE;UACJC,EAAE,EAAE,GAAG;UACPC,KAAK,EAAE,qBAAqB;UAC5BC,UAAU,EAAE,MAAM;UAClBC,SAAS,EAAE,OAAO;UAClBC,IAAI,EAAE,UAAU;UAChBC,WAAW,EAAE;QACf;MACF,CAAC;MAEAlB,KAAK,CAAemB,qBAAqB,CAAC;QACzCC,EAAE,EAAE,IAAI;QACRC,IAAI;UAAA,IAAAyB,MAAA,OAAAvC,kBAAA,CAAAC,OAAA,EAAE;YAAA,OAAYC,YAAY;UAAA;UAAA,SAA9BY,IAAIA,CAAA;YAAA,OAAAyB,MAAA,CAAAvB,KAAA,OAAAC,SAAA;UAAA;UAAA,OAAJH,IAAI;QAAA;MACN,CAAC,CAAC;MAEF,IAAMI,MAAM,SAASC,wBAAW,CAACqB,QAAQ,CAAC;QACxChC,UAAU,EAAE,MAAM;QAClBC,SAAS,EAAE,OAAO;QAClBF,KAAK,EAAE,qBAAqB;QAC5Bc,QAAQ,EAAE,aAAa;QACvBX,IAAI,EAAE;MACR,CAAC,CAAC;MAEFY,MAAM,CAAC7B,KAAK,CAAC,CAAC8B,oBAAoB,CAChCD,MAAM,CAACE,gBAAgB,CAAC,qBAAqB,CAAC,EAC9CF,MAAM,CAACG,gBAAgB,CAAC;QACtBC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBtB,UAAU,EAAE,MAAM;UAClBC,SAAS,EAAE,OAAO;UAClBF,KAAK,EAAE,qBAAqB;UAC5Bc,QAAQ,EAAE,aAAa;UACvBX,IAAI,EAAE;QACR,CAAC;MACH,CAAC,CACH,CAAC;MAEDY,MAAM,CAACJ,MAAM,CAAC,CAACa,OAAO,CAAC7B,YAAY,CAAC;IACtC,CAAC,EAAC;IAEFH,EAAE,CAAC,qDAAqD,MAAAC,kBAAA,CAAAC,OAAA,EAAE,aAAY;MACpE,IAAMC,YAAY,GAAG;QACnBC,MAAM,EAAE,mBAAmB;QAC3BC,OAAO,EAAE,oBAAoB;QAC7BC,IAAI,EAAE;UACJC,EAAE,EAAE,GAAG;UACPC,KAAK,EAAE,sBAAsB;UAC7BC,UAAU,EAAE,KAAK;UACjBC,SAAS,EAAE,SAAS;UACpBC,IAAI,EAAE,kBAAkB;UACxBC,WAAW,EAAE;QACf;MACF,CAAC;MAEAlB,KAAK,CAAemB,qBAAqB,CAAC;QACzCC,EAAE,EAAE,IAAI;QACRC,IAAI;UAAA,IAAA2B,MAAA,OAAAzC,kBAAA,CAAAC,OAAA,EAAE;YAAA,OAAYC,YAAY;UAAA;UAAA,SAA9BY,IAAIA,CAAA;YAAA,OAAA2B,MAAA,CAAAzB,KAAA,OAAAC,SAAA;UAAA;UAAA,OAAJH,IAAI;QAAA;MACN,CAAC,CAAC;MAEF,IAAMI,MAAM,SAASC,wBAAW,CAACqB,QAAQ,CAAC;QACxChC,UAAU,EAAE,KAAK;QACjBC,SAAS,EAAE,SAAS;QACpBF,KAAK,EAAE,sBAAsB;QAC7Bc,QAAQ,EAAE,aAAa;QACvBX,IAAI,EAAE;MACR,CAAC,CAAC;MAEFY,MAAM,CAACJ,MAAM,CAACb,IAAI,CAACK,IAAI,CAAC,CAACgC,IAAI,CAAC,kBAAkB,CAAC;IACnD,CAAC,EAAC;IAEF3C,EAAE,CAAC,8CAA8C,MAAAC,kBAAA,CAAAC,OAAA,EAAE,aAAY;MAC7D,IAAM+B,SAAS,GAAG;QAChBW,MAAM,EAAE;UACNpC,KAAK,EAAE,CAAC,kCAAkC;QAC5C;MACF,CAAC;MAEAd,KAAK,CAAemB,qBAAqB,CAAC;QACzCC,EAAE,EAAE,KAAK;QACTC,IAAI;UAAA,IAAA8B,MAAA,OAAA5C,kBAAA,CAAAC,OAAA,EAAE;YAAA,OAAY+B,SAAS;UAAA;UAAA,SAA3BlB,IAAIA,CAAA;YAAA,OAAA8B,MAAA,CAAA5B,KAAA,OAAAC,SAAA;UAAA;UAAA,OAAJH,IAAI;QAAA;MACN,CAAC,CAAC;MAEF,MAAMQ,MAAM,CACVH,wBAAW,CAACqB,QAAQ,CAAC;QACnBhC,UAAU,EAAE,MAAM;QAClBC,SAAS,EAAE,OAAO;QAClBF,KAAK,EAAE,sBAAsB;QAC7Bc,QAAQ,EAAE,aAAa;QACvBX,IAAI,EAAE;MACR,CAAC,CACH,CAAC,CAACyB,OAAO,CAACC,OAAO,CAAC,kCAAkC,CAAC;IACvD,CAAC,EAAC;EACJ,CAAC,CAAC;EAEFxC,QAAQ,CAAC,cAAc,EAAE,YAAM;IAC7BG,EAAE,CAAC,0CAA0C,MAAAC,kBAAA,CAAAC,OAAA,EAAE,aAAY;MACzD,IAAMC,YAAY,GAAG;QACnBC,MAAM,EAAE;MACV,CAAC;MAEAV,KAAK,CAAemB,qBAAqB,CAAC;QACzCC,EAAE,EAAE,IAAI;QACRC,IAAI;UAAA,IAAA+B,MAAA,OAAA7C,kBAAA,CAAAC,OAAA,EAAE;YAAA,OAAYC,YAAY;UAAA;UAAA,SAA9BY,IAAIA,CAAA;YAAA,OAAA+B,MAAA,CAAA7B,KAAA,OAAAC,SAAA;UAAA;UAAA,OAAJH,IAAI;QAAA;MACN,CAAC,CAAC;MAEF,IAAMI,MAAM,SAASC,wBAAW,CAAC2B,YAAY,CAAC,eAAe,CAAC;MAE9DxB,MAAM,CAAC7B,KAAK,CAAC,CAAC8B,oBAAoB,CAChCD,MAAM,CAACE,gBAAgB,CAAC,0BAA0B,CAAC,EACnDF,MAAM,CAACG,gBAAgB,CAAC;QACtBC,MAAM,EAAE,MAAM;QACdE,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAE1B,OAAO,EAAE;QAAgB,CAAC;MACnD,CAAC,CACH,CAAC;MAEDkB,MAAM,CAACJ,MAAM,CAAC,CAACa,OAAO,CAAC7B,YAAY,CAAC;IACtC,CAAC,EAAC;EACJ,CAAC,CAAC;EAEFN,QAAQ,CAAC,QAAQ,EAAE,YAAM;IACvBG,EAAE,CAAC,4BAA4B,MAAAC,kBAAA,CAAAC,OAAA,EAAE,aAAY;MAC1CR,KAAK,CAAemB,qBAAqB,CAAC;QACzCC,EAAE,EAAE,IAAI;QACRC,IAAI;UAAA,IAAAiC,MAAA,OAAA/C,kBAAA,CAAAC,OAAA,EAAE;YAAA,OAAa,CAAC,CAAC;UAAA,CAAC;UAAA,SAAtBa,IAAIA,CAAA;YAAA,OAAAiC,MAAA,CAAA/B,KAAA,OAAAC,SAAA;UAAA;UAAA,OAAJH,IAAI;QAAA;MACN,CAAC,CAAC;MAEF,MAAMQ,MAAM,CAACH,wBAAW,CAAC6B,MAAM,CAAC,eAAe,CAAC,CAAC,CAACC,QAAQ,CAACC,GAAG,CAACd,OAAO,CAAC,CAAC;MAExEd,MAAM,CAAC7B,KAAK,CAAC,CAAC8B,oBAAoB,CAChCD,MAAM,CAACE,gBAAgB,CAAC,mBAAmB,CAAC,EAC5CF,MAAM,CAACG,gBAAgB,CAAC;QACtBC,MAAM,EAAE,MAAM;QACdE,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAE1B,OAAO,EAAE;QAAgB,CAAC;MACnD,CAAC,CACH,CAAC;IACH,CAAC,EAAC;IAEFL,EAAE,CAAC,wCAAwC,MAAAC,kBAAA,CAAAC,OAAA,EAAE,aAAY;MACtDR,KAAK,CAAe4C,qBAAqB,CAAC,IAAIC,KAAK,CAAC,eAAe,CAAC,CAAC;MAGtE,MAAMhB,MAAM,CAACH,wBAAW,CAAC6B,MAAM,CAAC,eAAe,CAAC,CAAC,CAACC,QAAQ,CAACC,GAAG,CAACd,OAAO,CAAC,CAAC;IAC1E,CAAC,EAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}