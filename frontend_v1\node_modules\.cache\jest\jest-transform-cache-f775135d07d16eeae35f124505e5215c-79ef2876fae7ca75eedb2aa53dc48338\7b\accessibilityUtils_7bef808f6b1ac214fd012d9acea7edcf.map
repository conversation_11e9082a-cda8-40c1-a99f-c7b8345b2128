{"version": 3, "names": ["_reactNative", "require", "_colorContrastAudit", "WCAG_STANDARDS", "exports", "CONTRAST_RATIOS", "AA_NORMAL", "AA_LARGE", "AAA_NORMAL", "AAA_LARGE", "TOUCH_TARGETS", "MINIMUM_SIZE", "RECOMMENDED_SIZE", "SPACING", "TARGET_SIZE", "MINIMUM", "FOCUS_INDICATORS", "MIN_WIDTH", "RECOMMENDED_WIDTH", "OFFSET", "Z_INDEX_BASE", "SHADOW_OPACITY", "SHADOW_RADIUS", "global", "hexToRgb", "hex", "result", "exec", "r", "parseInt", "g", "b", "getRelativeLuminance", "_map", "map", "c", "Math", "pow", "_map2", "_slicedToArray2", "default", "rs", "gs", "bs", "getContrastRatio", "color1", "color2", "rgb1", "rgb2", "lum1", "lum2", "brightest", "max", "darkest", "min", "meetsWCAGAA", "foreground", "background", "isLargeText", "arguments", "length", "undefined", "ratio", "requiredRatio", "getWCAGCompliantColorLocal", "baseColor", "backgroundColor", "compliantColors", "sage", "light", "medium", "dark", "darker", "neutral", "getFocusIndicatorStyle", "borderWidth", "borderColor", "borderStyle", "shadowColor", "shadowOffset", "width", "height", "shadowOpacity", "shadowRadius", "elevation", "Platform", "OS", "zIndex", "FocusUtils", "ensureFocusVisible", "element", "focus", "scrollIntoView", "behavior", "block", "inline", "getEnhancedFocusStyle", "color", "focusColor", "Object", "assign", "outlineWidth", "outlineColor", "outlineStyle", "outlineOffset", "checkFocusObscured", "elementY", "stickyFooterHeight", "screenHeight", "window", "innerHeight", "focusAreaBottom", "stickyFooterTop", "isFocused", "baseStyle", "options", "_options$color", "_options$width", "<PERSON><PERSON><PERSON><PERSON>", "_options$offset", "offset", "_options$preventObscu", "preventObscuring", "baseStyles", "overflow", "borderRadius", "platformStyles", "select", "web", "android", "ios", "ensureFocusNotObscured", "focusedElementStyle", "stickyElements", "maxStickyZIndex", "reduce", "position", "createFocusManager", "currentFocusedElement", "setFocus", "getCurrentFocus", "clearFocus", "moveFocus", "direction", "console", "log", "getMinimumTouchTarget", "getRecommendedTouchTarget", "validateTouchTargetSize", "minSize", "getTouchTargetStyle", "customSize", "size", "min<PERSON><PERSON><PERSON>", "minHeight", "paddingHorizontal", "paddingVertical", "TouchTargetUtils", "getSpacing", "getToolbarIconStyle", "alignItems", "justifyContent", "validate", "validate<PERSON><PERSON><PERSON><PERSON><PERSON>get", "issues", "push", "<PERSON><PERSON><PERSON><PERSON>", "getMinSize", "getRecommendedSize", "getPlatformTouchTarget", "calculateHitSlop", "targetSize", "deficit", "slop", "ceil", "top", "bottom", "left", "right", "getImageAccessibilityProps", "type", "description", "action", "accessibilityLabel", "accessibilityRole", "accessible", "importantForAccessibility", "accessibilityHint", "ImageAccessibilityUtils", "getDecorative", "getInformative", "getFunctional", "generateAltText", "entityName", "SinglePointerUtils", "getDragAlternative", "onMove", "onMoveUp", "onMoveDown", "accessibilityActions", "name", "label", "onAccessibilityAction", "event", "nativeEvent", "actionName", "getSwipeAlternative", "onSwipeLeft", "onSwipeRight", "onSwipeUp", "onSwipeDown", "buttons", "onPress", "filter", "Boolean", "getMultiTouchAlternative", "onPinch", "onRotate", "zoomIn", "zoomOut", "rotateLeft", "rotateRight", "ScreenReaderUtils", "announceForAccessibility", "message", "AccessibilityInfo", "isScreenReaderEnabled", "_isScreenReaderEnabled", "_asyncToGenerator2", "_unused", "apply", "getAccessibleLabel", "primary", "secondary", "state", "parts", "join", "generateAccessibleLabel", "context", "hint", "getSemanticRole", "componentType", "roleMap", "button", "input", "checkbox", "radio", "link", "image", "text", "header", "list", "listitem", "prefersReducedMotion", "_ref", "isReduceMotionEnabled", "_unused2", "getResponsiveSpacing", "baseSpacing", "getResponsiveFontSize", "baseFontSize", "enhanceAccessibilityProps", "props", "AccessibilityUtils", "getWCAGCompliantColor", "freeze", "ColorContrastUtils", "rgb", "_map3", "_map4", "l1", "l2", "lighter", "validateC<PERSON><PERSON>t", "level", "isCompliant", "recommendation", "improvement", "toFixed", "round", "getAccessibleTextColor", "whiteContrast", "blackContrast", "enhanceColorContrast", "targetBackground", "targetRatio", "currentRatio", "backgroundLuminance", "enhanced", "factor", "enhancedHex", "toString", "padStart", "newRatio", "AccessibilityTestUtils", "auditComponent", "componentProps", "_componentProps$style", "_componentProps$style2", "warnings", "recommendations", "children", "style", "validation", "_toConsumableArray2", "generateAccessibilityReport", "components", "details", "component", "index", "componentIndex", "audit", "totalIssues", "sum", "detail", "totalWarnings", "complianceScore", "totalComponents", "issuesFound", "warningsFound", "VoiceControlUtils", "isVoiceControlAvailable", "Version", "generateVoiceLabel", "cleanText", "replace", "toLowerCase", "createVoiceHints", "actions", "GestureAccessibilityUtils", "needsAlternativeGestures", "_needsAlternativeGestures", "_unused3", "getGestureAlternatives", "gestureType", "alternatives", "swipe", "pinch", "longPress", "drag", "createGestureInstructions", "gesture", "alternative", "CognitiveAccessibilityUtils", "simplifyText", "estimateReadingTime", "wordsPerMinute", "wordCount", "split", "minutes", "createProgressIndicator", "currentStep", "totalSteps", "FocusManagementUtils", "accessibilityUtils", "_default", "checkColorContrast", "textSize", "calculateContrastRatio"], "sources": ["accessibilityUtils.ts"], "sourcesContent": ["/**\n * Accessibility Utilities for WCAG 2.2 AA Compliance\n *\n * This module provides comprehensive accessibility utilities to ensure\n * the Vierla application meets WCAG 2.2 AA standards.\n *\n * Key Features:\n * - Color contrast validation and enhancement\n * - Focus management for keyboard navigation\n * - Touch target size validation\n * - Screen reader compatibility helpers\n * - Accessibility testing utilities\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\nimport { Platform } from 'react-native';\nimport { AccessibilityInfo } from 'react-native';\nimport { calculateContrastRatio } from './colorContrastAudit';\n\n// WCAG 2.2 AA Compliance Constants\n// Simplified version to resolve runtime error\nexport const WCAG_STANDARDS = {\n  CONTRAST_RATIOS: {\n    AA_NORMAL: 4.5,\n    AA_LARGE: 3.0,\n    AAA_NORMAL: 7.0,\n    AAA_LARGE: 4.5,\n  },\n  TOUCH_TARGETS: {\n    MINIMUM_SIZE: 44, // iOS HIG and Material Design minimum\n    RECOMMENDED_SIZE: 48,\n    SPACING: 8, // Minimum spacing between targets\n  },\n  // Backward compatibility alias for TARGET_SIZE\n  TARGET_SIZE: {\n    MINIMUM: 44, // Alias for TOUCH_TARGETS.MINIMUM_SIZE\n  },\n  FOCUS_INDICATORS: {\n    MIN_WIDTH: 2, // WCAG 2.1 AA minimum thickness\n    RECOMMENDED_WIDTH: 3, // Enhanced visibility\n    OFFSET: 2,\n    Z_INDEX_BASE: 9999, // Ensure focus indicators are above sticky elements\n    SHADOW_OPACITY: 0.4, // Enhanced visibility with shadow\n    SHADOW_RADIUS: 6,\n  },\n} as const;\n\n// Create a global reference to prevent runtime errors\nif (typeof global !== 'undefined') {\n  (global as any).WCAG_STANDARDS = WCAG_STANDARDS;\n}\n\n/**\n * Color Contrast Utilities - REC-ACC-001 Implementation\n */\n\n// Convert hex color to RGB\nexport const hexToRgb = (hex: string): { r: number; g: number; b: number } | null => {\n  const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex);\n  return result ? {\n    r: parseInt(result[1], 16),\n    g: parseInt(result[2], 16),\n    b: parseInt(result[3], 16)\n  } : null;\n};\n\n// Calculate relative luminance according to WCAG\nexport const getRelativeLuminance = (r: number, g: number, b: number): number => {\n  const [rs, gs, bs] = [r, g, b].map(c => {\n    c = c / 255;\n    return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);\n  });\n  return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;\n};\n\n// Calculate contrast ratio between two colors\nexport const getContrastRatio = (color1: string, color2: string): number => {\n  const rgb1 = hexToRgb(color1);\n  const rgb2 = hexToRgb(color2);\n\n  if (!rgb1 || !rgb2) return 1;\n\n  const lum1 = getRelativeLuminance(rgb1.r, rgb1.g, rgb1.b);\n  const lum2 = getRelativeLuminance(rgb2.r, rgb2.g, rgb2.b);\n\n  const brightest = Math.max(lum1, lum2);\n  const darkest = Math.min(lum1, lum2);\n\n  return (brightest + 0.05) / (darkest + 0.05);\n};\n\n// Check if color combination meets WCAG AA standards\nexport const meetsWCAGAA = (foreground: string, background: string, isLargeText = false): boolean => {\n  const ratio = getContrastRatio(foreground, background);\n  const requiredRatio = isLargeText ? WCAG_STANDARDS.CONTRAST_RATIOS.AA_LARGE : WCAG_STANDARDS.CONTRAST_RATIOS.AA_NORMAL;\n  return ratio >= requiredRatio;\n};\n\n// Get WCAG-compliant color variations (wrapper for imported function)\nexport const getWCAGCompliantColorLocal = (\n  baseColor: string,\n  backgroundColor: string,\n  isLargeText = false\n): string => {\n  if (meetsWCAGAA(baseColor, backgroundColor, isLargeText)) {\n    return baseColor;\n  }\n\n  // Enhanced sage green colors that meet WCAG AA standards\n  const compliantColors = {\n    sage: {\n      light: '#4A6B52', // 4.52:1 contrast ratio on white\n      medium: '#3A5B42', // 5.89:1 contrast ratio on white\n      dark: '#2A4B32', // 7.12:1 contrast ratio on white\n      darker: '#1F3A26', // 9.45:1 contrast ratio on white\n    },\n    neutral: {\n      dark: '#374151', // 8.9:1 contrast ratio on white\n      darker: '#1F2937', // 13.1:1 contrast ratio on white\n    }\n  };\n\n  // Return the most appropriate compliant color\n  if (backgroundColor === '#FFFFFF' || backgroundColor === '#F9FAFB') {\n    return isLargeText ? compliantColors.sage.light : compliantColors.sage.medium;\n  }\n\n  return baseColor; // Fallback to original color\n};\n\n/**\n * Keyboard Focus Utilities - REC-ACC-002 Implementation\n */\n\n// Enhanced focus indicator styles\nexport const getFocusIndicatorStyle = (baseColor = '#3B82F6') => ({\n  borderWidth: WCAG_STANDARDS.FOCUS_INDICATORS.RECOMMENDED_WIDTH,\n  borderColor: baseColor,\n  borderStyle: 'solid' as const,\n  shadowColor: baseColor,\n  shadowOffset: { width: 0, height: 0 },\n  shadowOpacity: WCAG_STANDARDS.FOCUS_INDICATORS.SHADOW_OPACITY,\n  shadowRadius: WCAG_STANDARDS.FOCUS_INDICATORS.SHADOW_RADIUS,\n  elevation: Platform.OS === 'android' ? 8 : 0,\n  zIndex: WCAG_STANDARDS.FOCUS_INDICATORS.Z_INDEX_BASE,\n});\n\n// Focus management utilities\nexport const FocusUtils = {\n  // Ensure focus is visible and not obscured\n  ensureFocusVisible: (element: any) => {\n    if (Platform.OS === 'web') {\n      // Web-specific focus management\n      if (element && element.focus) {\n        element.focus();\n        element.scrollIntoView?.({\n          behavior: 'smooth',\n          block: 'center',\n          inline: 'nearest'\n        });\n      }\n    }\n  },\n\n  // Get focus indicator with enhanced visibility\n  getEnhancedFocusStyle: (color?: string) => {\n    const focusColor = color || '#3B82F6';\n    return {\n      ...getFocusIndicatorStyle(focusColor),\n      // Additional enhancement for better visibility\n      outlineWidth: WCAG_STANDARDS.FOCUS_INDICATORS.RECOMMENDED_WIDTH,\n      outlineColor: focusColor,\n      outlineStyle: 'solid' as const,\n      outlineOffset: WCAG_STANDARDS.FOCUS_INDICATORS.OFFSET,\n    };\n  },\n\n  // Check if focus is potentially obscured by sticky elements\n  checkFocusObscured: (elementY: number, stickyFooterHeight = 80) => {\n    // Simple check for focus obscured by sticky footer\n    const screenHeight = Platform.OS === 'web' ? window.innerHeight : 800; // Fallback height\n    const focusAreaBottom = elementY + WCAG_STANDARDS.TOUCH_TARGETS.MINIMUM_SIZE;\n    const stickyFooterTop = screenHeight - stickyFooterHeight;\n\n    return focusAreaBottom > stickyFooterTop;\n  },\n\n  /**\n   * Get enhanced WCAG-compliant focus indicator styles for React Native\n   */\n  getFocusIndicatorStyle: (\n    isFocused: boolean,\n    baseStyle: any = {},\n    options: {\n      color?: string;\n      width?: number;\n      offset?: number;\n      preventObscuring?: boolean;\n    } = {}\n  ) => {\n    const {\n      color = '#3B82F6', // Default focus color\n      width: requestedWidth = WCAG_STANDARDS.FOCUS_INDICATORS.RECOMMENDED_WIDTH,\n      offset = WCAG_STANDARDS.FOCUS_INDICATORS.OFFSET,\n      preventObscuring = true, // Prevent sticky elements from obscuring focus\n    } = options;\n\n    // Ensure minimum width compliance\n    const width = Math.max(WCAG_STANDARDS.FOCUS_INDICATORS.MIN_WIDTH, requestedWidth);\n\n    if (!isFocused) {\n      return baseStyle;\n    }\n\n    const baseStyles = {\n      ...baseStyle,\n      borderWidth: width,\n      borderColor: color,\n      borderStyle: 'solid' as const,\n      // Enhanced shadow for better visibility and WCAG compliance\n      shadowColor: color,\n      shadowOffset: { width: 0, height: 0 },\n      shadowOpacity: WCAG_STANDARDS.FOCUS_INDICATORS.SHADOW_OPACITY,\n      shadowRadius: WCAG_STANDARDS.FOCUS_INDICATORS.SHADOW_RADIUS,\n      // Ensure focus ring is always visible and above sticky elements\n      overflow: 'visible' as const,\n      zIndex: preventObscuring ? WCAG_STANDARDS.FOCUS_INDICATORS.Z_INDEX_BASE : undefined,\n      // Add subtle background highlight for better contrast\n      backgroundColor: `${color}10`, // 10% opacity background\n      // Ensure minimum border radius for better visibility\n      borderRadius: Math.max(baseStyle.borderRadius || 0, 4),\n    };\n\n    // Add platform-specific properties\n    const platformStyles = Platform.select({\n      web: {\n        outlineWidth: width,\n        outlineColor: color,\n        outlineStyle: 'solid' as const,\n        outlineOffset: offset,\n      },\n      android: {\n        elevation: 6,\n      },\n      ios: {\n        elevation: 0,\n      },\n      default: {},\n    });\n\n    return {\n      ...baseStyles,\n      ...platformStyles,\n    };\n  },\n\n  /**\n   * Ensure focus indicator is not obscured by sticky elements\n   */\n  ensureFocusNotObscured: (\n    focusedElementStyle: any,\n    stickyElements: Array<{ zIndex: number; position: string }> = []\n  ) => {\n    const maxStickyZIndex = stickyElements.reduce((max, element) => {\n      if (element.position === 'sticky' || element.position === 'fixed') {\n        return Math.max(max, element.zIndex || 0);\n      }\n      return max;\n    }, 0);\n\n    return {\n      ...focusedElementStyle,\n      zIndex: Math.max(\n        focusedElementStyle.zIndex || 0,\n        maxStickyZIndex + 1,\n        WCAG_STANDARDS.FOCUS_INDICATORS.Z_INDEX_BASE\n      ),\n    };\n  },\n\n  /**\n   * Enhanced focus management for complex components\n   */\n  createFocusManager: () => {\n    let currentFocusedElement: any = null;\n\n    return {\n      setFocus: (element: any) => {\n        currentFocusedElement = element;\n      },\n\n      getCurrentFocus: () => currentFocusedElement,\n\n      clearFocus: () => {\n        currentFocusedElement = null;\n      },\n\n      moveFocus: (direction: 'next' | 'previous') => {\n        // Implementation would depend on specific navigation requirements\n        console.log(`Moving focus ${direction}`);\n      },\n    };\n  },\n};\n\n/**\n * Touch Target Utilities - REC-ACC-003 Implementation\n */\n\n// Ensure minimum touch target size\nexport const getMinimumTouchTarget = () => WCAG_STANDARDS.TOUCH_TARGETS.MINIMUM_SIZE;\n\n// Get recommended touch target size\nexport const getRecommendedTouchTarget = () => WCAG_STANDARDS.TOUCH_TARGETS.RECOMMENDED_SIZE;\n\n// Validate touch target size\nexport const validateTouchTargetSize = (width: number, height: number): boolean => {\n  const minSize = WCAG_STANDARDS.TOUCH_TARGETS.MINIMUM_SIZE;\n  return width >= minSize && height >= minSize;\n};\n\n// Get touch target style with minimum size enforcement\nexport const getTouchTargetStyle = (customSize?: number) => {\n  const size = customSize || WCAG_STANDARDS.TOUCH_TARGETS.RECOMMENDED_SIZE;\n  return {\n    minWidth: size,\n    minHeight: size,\n    paddingHorizontal: Math.max(0, (size - 24) / 2), // Ensure content is centered\n    paddingVertical: Math.max(0, (size - 24) / 2),\n  };\n};\n\n// Touch target utilities\nexport const TouchTargetUtils = {\n  // Ensure adequate spacing between touch targets\n  getSpacing: () => WCAG_STANDARDS.TOUCH_TARGETS.SPACING,\n\n  // Get enhanced touch target for toolbar icons\n  getToolbarIconStyle: () => ({\n    ...getTouchTargetStyle(WCAG_STANDARDS.TOUCH_TARGETS.RECOMMENDED_SIZE),\n    alignItems: 'center' as const,\n    justifyContent: 'center' as const,\n    borderRadius: 8,\n  }),\n\n  // Validate touch target meets WCAG requirements\n  validate: validateTouchTargetSize,\n\n  // Validate touch target with detailed results\n  validateTouchTarget: (width: number, height: number): { isValid: boolean; issues: string[] } => {\n    const minSize = WCAG_STANDARDS.TOUCH_TARGETS.MINIMUM_SIZE;\n    const issues: string[] = [];\n\n    if (width < minSize) {\n      issues.push(`Width ${width}px is below minimum ${minSize}px`);\n    }\n    if (height < minSize) {\n      issues.push(`Height ${height}px is below minimum ${minSize}px`);\n    }\n\n    return {\n      isValid: issues.length === 0,\n      issues,\n    };\n  },\n\n  // Get minimum size\n  getMinSize: getMinimumTouchTarget,\n\n  // Get recommended size\n  getRecommendedSize: getRecommendedTouchTarget,\n\n  // Get platform-specific minimum touch target size\n  getPlatformTouchTarget: (): number => {\n    return Platform.select({\n      ios: 44, // iOS HIG minimum\n      android: 48, // Material Design minimum\n      default: 44,\n    });\n  },\n\n  // Calculate hit slop for small targets\n  calculateHitSlop: (targetSize: number): { top: number; bottom: number; left: number; right: number } => {\n    const minSize = WCAG_STANDARDS.TOUCH_TARGETS.MINIMUM_SIZE;\n    const deficit = Math.max(0, minSize - targetSize);\n    const slop = Math.ceil(deficit / 2);\n\n    return {\n      top: slop,\n      bottom: slop,\n      left: slop,\n      right: slop,\n    };\n  },\n};\n\n/**\n * Image Accessibility Utilities - REC-ACC-005 Implementation\n */\n\nexport interface ImageAccessibilityProps {\n  accessibilityLabel: string;\n  accessibilityHint?: string;\n  accessibilityRole: 'image' | 'imagebutton';\n  accessible: boolean;\n  importantForAccessibility: 'yes' | 'no' | 'auto';\n}\n\n// Generate accessibility props for images\nexport const getImageAccessibilityProps = (\n  type: 'decorative' | 'informative' | 'functional',\n  description?: string,\n  action?: string\n): ImageAccessibilityProps => {\n  switch (type) {\n    case 'decorative':\n      return {\n        accessibilityLabel: '',\n        accessibilityRole: 'image',\n        accessible: false,\n        importantForAccessibility: 'no',\n      };\n\n    case 'informative':\n      return {\n        accessibilityLabel: description || 'Informative image',\n        accessibilityRole: 'image',\n        accessible: true,\n        importantForAccessibility: 'yes',\n      };\n\n    case 'functional':\n      return {\n        accessibilityLabel: description || 'Interactive image',\n        accessibilityHint: action ? `Double tap to ${action}` : undefined,\n        accessibilityRole: 'imagebutton',\n        accessible: true,\n        importantForAccessibility: 'yes',\n      };\n\n    default:\n      return {\n        accessibilityLabel: description || 'Image',\n        accessibilityRole: 'image',\n        accessible: true,\n        importantForAccessibility: 'yes',\n      };\n  }\n};\n\n// Image accessibility utilities\nexport const ImageAccessibilityUtils = {\n  // Get props for different image types\n  getDecorative: () => getImageAccessibilityProps('decorative'),\n  getInformative: (description: string) => getImageAccessibilityProps('informative', description),\n  getFunctional: (description: string, action?: string) => getImageAccessibilityProps('functional', description, action),\n\n  // Generate alt text for common image types\n  generateAltText: (type: string, entityName?: string, action?: string): string => {\n    switch (type) {\n      case 'logo':\n        return entityName ? `${entityName} logo` : 'Company logo';\n      case 'avatar':\n        return entityName ? `${entityName} profile picture` : 'User profile picture';\n      case 'service':\n        return entityName ? `${entityName} service image` : 'Service image';\n      case 'store':\n        return entityName ? `${entityName} store image` : 'Store image';\n      case 'icon':\n        return action ? `${action} icon` : entityName || 'Icon';\n      default:\n        return entityName || 'Image';\n    }\n  },\n};\n\n/**\n * Single-Pointer Alternative Utilities - REC-ACC-007 Implementation\n */\n\n// Single-pointer interaction utilities\nexport const SinglePointerUtils = {\n  // Convert drag-and-drop to single-pointer alternative\n  getDragAlternative: (onMove: (direction: 'up' | 'down') => void) => ({\n    onMoveUp: () => onMove('up'),\n    onMoveDown: () => onMove('down'),\n    accessibilityActions: [\n      { name: 'increment', label: 'Move up' },\n      { name: 'decrement', label: 'Move down' },\n    ],\n    onAccessibilityAction: (event: any) => {\n      switch (event.nativeEvent.actionName) {\n        case 'increment':\n          onMove('up');\n          break;\n        case 'decrement':\n          onMove('down');\n          break;\n      }\n    },\n  }),\n\n  // Convert swipe gestures to button alternatives\n  getSwipeAlternative: (\n    onSwipeLeft?: () => void,\n    onSwipeRight?: () => void,\n    onSwipeUp?: () => void,\n    onSwipeDown?: () => void\n  ) => ({\n    buttons: [\n      onSwipeLeft && { label: 'Previous', onPress: onSwipeLeft },\n      onSwipeRight && { label: 'Next', onPress: onSwipeRight },\n      onSwipeUp && { label: 'Up', onPress: onSwipeUp },\n      onSwipeDown && { label: 'Down', onPress: onSwipeDown },\n    ].filter(Boolean),\n  }),\n\n  // Convert multi-touch gestures to single-pointer\n  getMultiTouchAlternative: (\n    onPinch?: (scale: number) => void,\n    onRotate?: (rotation: number) => void\n  ) => ({\n    zoomIn: onPinch ? () => onPinch(1.2) : undefined,\n    zoomOut: onPinch ? () => onPinch(0.8) : undefined,\n    rotateLeft: onRotate ? () => onRotate(-15) : undefined,\n    rotateRight: onRotate ? () => onRotate(15) : undefined,\n  }),\n};\n\n/**\n * Screen Reader Utilities\n */\n\nexport const ScreenReaderUtils = {\n  // Announce message to screen readers\n  announceForAccessibility: (message: string) => {\n    if (Platform.OS === 'ios' || Platform.OS === 'android') {\n      AccessibilityInfo.announceForAccessibility(message);\n    }\n  },\n\n  // Check if screen reader is enabled\n  isScreenReaderEnabled: async (): Promise<boolean> => {\n    try {\n      return await AccessibilityInfo.isScreenReaderEnabled();\n    } catch {\n      return false;\n    }\n  },\n\n  // Get accessible label with context\n  getAccessibleLabel: (\n    primary: string,\n    secondary?: string,\n    state?: string,\n    position?: string\n  ): string => {\n    const parts = [primary];\n    if (secondary) parts.push(secondary);\n    if (state) parts.push(state);\n    if (position) parts.push(position);\n    return parts.join(', ');\n  },\n\n  // Generate comprehensive accessible label\n  generateAccessibleLabel: (\n    primary: string,\n    state?: string,\n    context?: string,\n    hint?: string\n  ): string => {\n    const parts = [primary];\n    if (state) parts.push(state);\n    if (context) parts.push(context);\n    if (hint) parts.push(hint);\n    return parts.join(', ');\n  },\n\n  // Get semantic role for component types\n  getSemanticRole: (componentType: string): string => {\n    const roleMap: Record<string, string> = {\n      button: 'button',\n      input: 'text',\n      checkbox: 'checkbox',\n      radio: 'radio',\n      link: 'link',\n      image: 'image',\n      text: 'text',\n      header: 'header',\n      list: 'list',\n      listitem: 'listitem',\n    };\n    return roleMap[componentType] || 'none';\n  },\n};\n\n/**\n * General Accessibility Utilities\n */\n\n// Check if user prefers reduced motion\nexport const prefersReducedMotion = async (): Promise<boolean> => {\n  try {\n    return await AccessibilityInfo.isReduceMotionEnabled();\n  } catch {\n    return false;\n  }\n};\n\n// Get responsive spacing that meets accessibility requirements\nexport const getResponsiveSpacing = (baseSpacing: number): number => {\n  // Ensure minimum spacing for touch targets\n  return Math.max(baseSpacing, WCAG_STANDARDS.TOUCH_TARGETS.SPACING);\n};\n\n// Get responsive font size that meets accessibility requirements\nexport const getResponsiveFontSize = (baseFontSize: number): number => {\n  // Ensure minimum font size for readability\n  return Math.max(baseFontSize, 12);\n};\n\n// Validate and enhance accessibility props\nexport const enhanceAccessibilityProps = (props: any) => ({\n  ...props,\n  accessible: props.accessible !== false,\n  accessibilityRole: props.accessibilityRole || 'button',\n  importantForAccessibility: props.importantForAccessibility || 'yes',\n});\n\n// Export all utilities for easy access\nexport const AccessibilityUtils = {\n  WCAG_STANDARDS,\n  // Color contrast\n  hexToRgb,\n  getRelativeLuminance,\n  getContrastRatio,\n  meetsWCAGAA,\n  getWCAGCompliantColor: getWCAGCompliantColorLocal,\n  // Focus management\n  FocusUtils,\n  getFocusIndicatorStyle,\n  // Touch targets\n  TouchTargetUtils,\n  getMinimumTouchTarget,\n  getRecommendedTouchTarget,\n  validateTouchTargetSize,\n  getTouchTargetStyle,\n  // Images\n  ImageAccessibilityUtils,\n  getImageAccessibilityProps,\n  // Single-pointer alternatives\n  SinglePointerUtils,\n  // Screen reader\n  ScreenReaderUtils,\n  // General utilities\n  prefersReducedMotion,\n  getResponsiveSpacing,\n  getResponsiveFontSize,\n  enhanceAccessibilityProps,\n};\n\n// Ensure the constants are properly frozen to prevent modification\nObject.freeze(WCAG_STANDARDS);\nObject.freeze(WCAG_STANDARDS.CONTRAST_RATIOS);\nObject.freeze(WCAG_STANDARDS.TOUCH_TARGETS);\nObject.freeze(WCAG_STANDARDS.TARGET_SIZE);\nObject.freeze(WCAG_STANDARDS.FOCUS_INDICATORS);\n\n// Color Contrast Utilities\nexport const ColorContrastUtils = {\n  /**\n   * Convert hex color to RGB values\n   */\n  hexToRgb: (hex: string): { r: number; g: number; b: number } | null => {\n    const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex);\n    return result\n      ? {\n          r: parseInt(result[1], 16),\n          g: parseInt(result[2], 16),\n          b: parseInt(result[3], 16),\n        }\n      : null;\n  },\n\n  /**\n   * Calculate relative luminance of a color\n   */\n  getRelativeLuminance: (hex: string): number => {\n    const rgb = ColorContrastUtils.hexToRgb(hex);\n    if (!rgb) return 0;\n\n    const { r, g, b } = rgb;\n    const [rs, gs, bs] = [r, g, b].map(c => {\n      c = c / 255;\n      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);\n    });\n\n    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;\n  },\n\n  /**\n   * Calculate contrast ratio between two colors\n   */\n  getContrastRatio: (color1: string, color2: string): number => {\n    const l1 = ColorContrastUtils.getRelativeLuminance(color1);\n    const l2 = ColorContrastUtils.getRelativeLuminance(color2);\n    \n    const lighter = Math.max(l1, l2);\n    const darker = Math.min(l1, l2);\n    \n    return (lighter + 0.05) / (darker + 0.05);\n  },\n\n  /**\n   * Validate color contrast for WCAG compliance\n   */\n  validateContrast: (\n    foreground: string,\n    background: string,\n    level: 'AA' | 'AAA' = 'AA',\n    isLargeText: boolean = false\n  ): {\n    ratio: number;\n    isCompliant: boolean;\n    requiredRatio: number;\n    recommendation: string;\n  } => {\n    const ratio = ColorContrastUtils.getContrastRatio(foreground, background);\n    \n    let requiredRatio: number;\n    if (level === 'AAA') {\n      requiredRatio = isLargeText ? 4.5 : 7.0; // WCAG_STANDARDS.CONTRAST_RATIOS.AAA_LARGE : WCAG_STANDARDS.CONTRAST_RATIOS.AAA_NORMAL;\n    } else {\n      requiredRatio = isLargeText ? 3.0 : 4.5; // WCAG_STANDARDS.CONTRAST_RATIOS.AA_LARGE : WCAG_STANDARDS.CONTRAST_RATIOS.AA_NORMAL;\n    }\n\n    const isCompliant = ratio >= requiredRatio;\n    \n    let recommendation = '';\n    if (!isCompliant) {\n      const improvement = (requiredRatio / ratio).toFixed(2);\n      recommendation = `Increase contrast by ${improvement}x to meet ${level} standards`;\n    } else {\n      recommendation = `Meets ${level} standards (${ratio.toFixed(2)}:1)`;\n    }\n\n    return {\n      ratio: Math.round(ratio * 100) / 100,\n      isCompliant,\n      requiredRatio,\n      recommendation,\n    };\n  },\n\n  /**\n   * Get accessible text color for a given background\n   */\n  getAccessibleTextColor: (backgroundColor: string): string => {\n    const whiteContrast = ColorContrastUtils.getContrastRatio('#FFFFFF', backgroundColor);\n    const blackContrast = ColorContrastUtils.getContrastRatio('#000000', backgroundColor);\n    \n    return whiteContrast > blackContrast ? '#FFFFFF' : '#000000';\n  },\n\n  /**\n   * Enhance color for better contrast\n   */\n  enhanceColorContrast: (\n    color: string,\n    targetBackground: string,\n    targetRatio: number = 4.5 // WCAG_STANDARDS.CONTRAST_RATIOS.AA_NORMAL\n  ): string => {\n    const currentRatio = ColorContrastUtils.getContrastRatio(color, targetBackground);\n\n    if (currentRatio >= targetRatio) {\n      return color; // Already compliant\n    }\n\n    const rgb = ColorContrastUtils.hexToRgb(color);\n    if (!rgb) return color;\n\n    // More aggressive enhancement: significantly darken or lighten the color\n    const backgroundLuminance = ColorContrastUtils.getRelativeLuminance(targetBackground);\n\n    // For light backgrounds, darken significantly; for dark backgrounds, lighten significantly\n    let enhanced;\n    if (backgroundLuminance > 0.5) {\n      // Light background - darken the color more aggressively\n      const factor = 0.3; // Much more aggressive darkening\n      enhanced = {\n        r: Math.max(0, Math.round(rgb.r * factor)),\n        g: Math.max(0, Math.round(rgb.g * factor)),\n        b: Math.max(0, Math.round(rgb.b * factor)),\n      };\n    } else {\n      // Dark background - lighten the color more aggressively\n      const factor = 2.5; // Much more aggressive lightening\n      enhanced = {\n        r: Math.min(255, Math.round(rgb.r * factor)),\n        g: Math.min(255, Math.round(rgb.g * factor)),\n        b: Math.min(255, Math.round(rgb.b * factor)),\n      };\n    }\n\n    const enhancedHex = `#${enhanced.r.toString(16).padStart(2, '0')}${enhanced.g.toString(16).padStart(2, '0')}${enhanced.b.toString(16).padStart(2, '0')}`;\n\n    // Verify the enhancement worked, if not, use a fallback\n    const newRatio = ColorContrastUtils.getContrastRatio(enhancedHex, targetBackground);\n    if (newRatio >= targetRatio) {\n      return enhancedHex;\n    }\n\n    // Fallback: use high contrast colors\n    return backgroundLuminance > 0.5 ? '#000000' : '#FFFFFF';\n  },\n};\n\n\n\n\n\n\n\n\n// Accessibility Testing Utilities\nexport const AccessibilityTestUtils = {\n  /**\n   * Audit component for accessibility issues\n   */\n  auditComponent: (componentProps: any): {\n    issues: string[];\n    warnings: string[];\n    recommendations: string[];\n  } => {\n    const issues: string[] = [];\n    const warnings: string[] = [];\n    const recommendations: string[] = [];\n\n    // Check for accessibility label\n    if (!componentProps.accessibilityLabel && !componentProps.children) {\n      issues.push('Missing accessibility label');\n      recommendations.push('Add accessibilityLabel prop');\n    }\n\n    // Check for accessibility role\n    if (!componentProps.accessibilityRole) {\n      warnings.push('Missing accessibility role');\n      recommendations.push('Add appropriate accessibilityRole');\n    }\n\n    // Check for touch target size\n    if (componentProps.style?.width && componentProps.style?.height) {\n      const validation = TouchTargetUtils.validateTouchTarget(\n        componentProps.style.width,\n        componentProps.style.height\n      );\n\n      if (!validation.isValid) {\n        issues.push(...validation.issues);\n        recommendations.push(...validation.recommendations);\n      }\n    }\n\n    return { issues, warnings, recommendations };\n  },\n\n  /**\n   * Generate accessibility report\n   */\n  generateAccessibilityReport: (components: any[]): {\n    totalComponents: number;\n    issuesFound: number;\n    warningsFound: number;\n    complianceScore: number;\n    details: any[];\n  } => {\n    const details = components.map((component, index) => ({\n      componentIndex: index,\n      audit: AccessibilityTestUtils.auditComponent(component),\n    }));\n\n    const totalIssues = details.reduce((sum, detail) => sum + detail.audit.issues.length, 0);\n    const totalWarnings = details.reduce((sum, detail) => sum + detail.audit.warnings.length, 0);\n\n    const complianceScore = Math.max(0, 100 - (totalIssues * 10) - (totalWarnings * 5));\n\n    return {\n      totalComponents: components.length,\n      issuesFound: totalIssues,\n      warningsFound: totalWarnings,\n      complianceScore,\n      details,\n    };\n  },\n};\n\n// Export all utilities\nexport {\n  WCAG_STANDARDS,\n  ColorContrastUtils,\n  TouchTargetUtils,\n  FocusUtils,\n  ScreenReaderUtils,\n  AccessibilityTestUtils,\n};\n\n// Default export for convenience\n// Voice Control Utilities\nexport const VoiceControlUtils = {\n  /**\n   * Check if voice control is available\n   */\n  isVoiceControlAvailable: (): boolean => {\n    return Platform.OS === 'ios' && Platform.Version >= '13.0';\n  },\n\n  /**\n   * Generate voice control labels for components\n   */\n  generateVoiceLabel: (text: string, context?: string): string => {\n    // Remove special characters and normalize text for voice recognition\n    const cleanText = text.replace(/[^\\w\\s]/gi, '').toLowerCase();\n    return context ? `${context} ${cleanText}` : cleanText;\n  },\n\n  /**\n   * Create voice control hints\n   */\n  createVoiceHints: (actions: string[]): string => {\n    return `Available actions: ${actions.join(', ')}`;\n  },\n};\n\n// Gesture Accessibility Utilities\nexport const GestureAccessibilityUtils = {\n  /**\n   * Check if alternative gestures are needed\n   */\n  needsAlternativeGestures: async (): Promise<boolean> => {\n    try {\n      const isReduceMotionEnabled = await AccessibilityInfo.isReduceMotionEnabled();\n      return isReduceMotionEnabled;\n    } catch {\n      return false;\n    }\n  },\n\n  /**\n   * Get accessible gesture alternatives\n   */\n  getGestureAlternatives: (gestureType: string): string[] => {\n    const alternatives: Record<string, string[]> = {\n      swipe: ['double tap to activate', 'use navigation buttons'],\n      pinch: ['use zoom controls', 'double tap to zoom'],\n      longPress: ['use context menu button', 'double tap and hold'],\n      drag: ['use move buttons', 'select and use arrow keys'],\n    };\n    return alternatives[gestureType] || ['use alternative controls'];\n  },\n\n  /**\n   * Create gesture accessibility instructions\n   */\n  createGestureInstructions: (gesture: string, alternative: string): string => {\n    return `Gesture: ${gesture}. Alternative: ${alternative}`;\n  },\n};\n\n// Cognitive Accessibility Utilities\nexport const CognitiveAccessibilityUtils = {\n  /**\n   * Simplify text for cognitive accessibility\n   */\n  simplifyText: (text: string, level: 'basic' | 'intermediate' | 'advanced' = 'intermediate'): string => {\n    if (level === 'basic') {\n      // Use simpler words and shorter sentences\n      return text\n        .replace(/utilize/gi, 'use')\n        .replace(/facilitate/gi, 'help')\n        .replace(/approximately/gi, 'about')\n        .replace(/subsequently/gi, 'then');\n    }\n    return text;\n  },\n\n  /**\n   * Add reading time estimate\n   */\n  estimateReadingTime: (text: string, wordsPerMinute: number = 200): string => {\n    const wordCount = text.split(/\\s+/).length;\n    const minutes = Math.ceil(wordCount / wordsPerMinute);\n    return `Reading time: ${minutes} minute${minutes !== 1 ? 's' : ''}`;\n  },\n\n  /**\n   * Create progress indicators for multi-step processes\n   */\n  createProgressIndicator: (currentStep: number, totalSteps: number): string => {\n    return `Step ${currentStep} of ${totalSteps}`;\n  },\n};\n\n// Alias for backward compatibility\nexport const FocusManagementUtils = FocusUtils;\n\n// Backward compatibility alias\nexport const accessibilityUtils = AccessibilityUtils;\n\nexport default {\n  WCAG_STANDARDS,\n  ColorContrastUtils,\n  TouchTargetUtils,\n  FocusUtils,\n  FocusManagementUtils,\n  ScreenReaderUtils,\n  AccessibilityTestUtils,\n  VoiceControlUtils,\n  GestureAccessibilityUtils,\n  CognitiveAccessibilityUtils,\n\n  /**\n   * Get WCAG compliant color with proper contrast ratio\n   */\n  getWCAGCompliantColor: getWCAGCompliantColorLocal,\n\n  /**\n   * Check if color combination meets WCAG contrast requirements\n   */\n  checkColorContrast: (\n    foreground: string,\n    background: string,\n    level: 'AA' | 'AAA' = 'AA',\n    textSize: 'normal' | 'large' = 'normal'\n  ): boolean => {\n    const ratio = calculateContrastRatio(foreground, background);\n    const requiredRatio = level === 'AAA'\n      ? (textSize === 'large' ? 4.5 : 7.0)\n      : (textSize === 'large' ? 3.0 : 4.5);\n    return ratio >= requiredRatio;\n  },\n};\n\n// Direct export for backward compatibility\nexport const getWCAGCompliantColor = getWCAGCompliantColorLocal;\n"], "mappings": ";;;;;;;;AAiBA,IAAAA,YAAA,GAAAC,OAAA;AAEA,IAAAC,mBAAA,GAAAD,OAAA;AAIO,IAAME,cAAc,GAAAC,OAAA,CAAAD,cAAA,GAAAC,OAAA,CAAAD,cAAA,GAAG;EAC5BE,eAAe,EAAE;IACfC,SAAS,EAAE,GAAG;IACdC,QAAQ,EAAE,GAAG;IACbC,UAAU,EAAE,GAAG;IACfC,SAAS,EAAE;EACb,CAAC;EACDC,aAAa,EAAE;IACbC,YAAY,EAAE,EAAE;IAChBC,gBAAgB,EAAE,EAAE;IACpBC,OAAO,EAAE;EACX,CAAC;EAEDC,WAAW,EAAE;IACXC,OAAO,EAAE;EACX,CAAC;EACDC,gBAAgB,EAAE;IAChBC,SAAS,EAAE,CAAC;IACZC,iBAAiB,EAAE,CAAC;IACpBC,MAAM,EAAE,CAAC;IACTC,YAAY,EAAE,IAAI;IAClBC,cAAc,EAAE,GAAG;IACnBC,aAAa,EAAE;EACjB;AACF,CAAU;AAGV,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;EAChCA,MAAM,CAASpB,cAAc,GAAGA,cAAc;AACjD;AAOO,IAAMqB,QAAQ,GAAApB,OAAA,CAAAoB,QAAA,GAAG,SAAXA,QAAQA,CAAIC,GAAW,EAAiD;EACnF,IAAMC,MAAM,GAAG,2CAA2C,CAACC,IAAI,CAACF,GAAG,CAAC;EACpE,OAAOC,MAAM,GAAG;IACdE,CAAC,EAAEC,QAAQ,CAACH,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAC1BI,CAAC,EAAED,QAAQ,CAACH,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAC1BK,CAAC,EAAEF,QAAQ,CAACH,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE;EAC3B,CAAC,GAAG,IAAI;AACV,CAAC;AAGM,IAAMM,oBAAoB,GAAA5B,OAAA,CAAA4B,oBAAA,GAAG,SAAvBA,oBAAoBA,CAAIJ,CAAS,EAAEE,CAAS,EAAEC,CAAS,EAAa;EAC/E,IAAAE,IAAA,GAAqB,CAACL,CAAC,EAAEE,CAAC,EAAEC,CAAC,CAAC,CAACG,GAAG,CAAC,UAAAC,CAAC,EAAI;MACtCA,CAAC,GAAGA,CAAC,GAAG,GAAG;MACX,OAAOA,CAAC,IAAI,OAAO,GAAGA,CAAC,GAAG,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,CAACF,CAAC,GAAG,KAAK,IAAI,KAAK,EAAE,GAAG,CAAC;IACtE,CAAC,CAAC;IAAAG,KAAA,OAAAC,eAAA,CAAAC,OAAA,EAAAP,IAAA;IAHKQ,EAAE,GAAAH,KAAA;IAAEI,EAAE,GAAAJ,KAAA;IAAEK,EAAE,GAAAL,KAAA;EAIjB,OAAO,MAAM,GAAGG,EAAE,GAAG,MAAM,GAAGC,EAAE,GAAG,MAAM,GAAGC,EAAE;AAChD,CAAC;AAGM,IAAMC,gBAAgB,GAAAxC,OAAA,CAAAwC,gBAAA,GAAG,SAAnBA,gBAAgBA,CAAIC,MAAc,EAAEC,MAAc,EAAa;EAC1E,IAAMC,IAAI,GAAGvB,QAAQ,CAACqB,MAAM,CAAC;EAC7B,IAAMG,IAAI,GAAGxB,QAAQ,CAACsB,MAAM,CAAC;EAE7B,IAAI,CAACC,IAAI,IAAI,CAACC,IAAI,EAAE,OAAO,CAAC;EAE5B,IAAMC,IAAI,GAAGjB,oBAAoB,CAACe,IAAI,CAACnB,CAAC,EAAEmB,IAAI,CAACjB,CAAC,EAAEiB,IAAI,CAAChB,CAAC,CAAC;EACzD,IAAMmB,IAAI,GAAGlB,oBAAoB,CAACgB,IAAI,CAACpB,CAAC,EAAEoB,IAAI,CAAClB,CAAC,EAAEkB,IAAI,CAACjB,CAAC,CAAC;EAEzD,IAAMoB,SAAS,GAAGf,IAAI,CAACgB,GAAG,CAACH,IAAI,EAAEC,IAAI,CAAC;EACtC,IAAMG,OAAO,GAAGjB,IAAI,CAACkB,GAAG,CAACL,IAAI,EAAEC,IAAI,CAAC;EAEpC,OAAO,CAACC,SAAS,GAAG,IAAI,KAAKE,OAAO,GAAG,IAAI,CAAC;AAC9C,CAAC;AAGM,IAAME,WAAW,GAAAnD,OAAA,CAAAmD,WAAA,GAAG,SAAdA,WAAWA,CAAIC,UAAkB,EAAEC,UAAkB,EAAmC;EAAA,IAAjCC,WAAW,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EACrF,IAAMG,KAAK,GAAGlB,gBAAgB,CAACY,UAAU,EAAEC,UAAU,CAAC;EACtD,IAAMM,aAAa,GAAGL,WAAW,GAAGvD,cAAc,CAACE,eAAe,CAACE,QAAQ,GAAGJ,cAAc,CAACE,eAAe,CAACC,SAAS;EACtH,OAAOwD,KAAK,IAAIC,aAAa;AAC/B,CAAC;AAGM,IAAMC,0BAA0B,GAAA5D,OAAA,CAAA4D,0BAAA,GAAG,SAA7BA,0BAA0BA,CACrCC,SAAiB,EACjBC,eAAuB,EAEZ;EAAA,IADXR,WAAW,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAEnB,IAAIJ,WAAW,CAACU,SAAS,EAAEC,eAAe,EAAER,WAAW,CAAC,EAAE;IACxD,OAAOO,SAAS;EAClB;EAGA,IAAME,eAAe,GAAG;IACtBC,IAAI,EAAE;MACJC,KAAK,EAAE,SAAS;MAChBC,MAAM,EAAE,SAAS;MACjBC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC;IACDC,OAAO,EAAE;MACPF,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV;EACF,CAAC;EAGD,IAAIN,eAAe,KAAK,SAAS,IAAIA,eAAe,KAAK,SAAS,EAAE;IAClE,OAAOR,WAAW,GAAGS,eAAe,CAACC,IAAI,CAACC,KAAK,GAAGF,eAAe,CAACC,IAAI,CAACE,MAAM;EAC/E;EAEA,OAAOL,SAAS;AAClB,CAAC;AAOM,IAAMS,sBAAsB,GAAAtE,OAAA,CAAAsE,sBAAA,GAAG,SAAzBA,sBAAsBA,CAAA;EAAA,IAAIT,SAAS,GAAAN,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,SAAS;EAAA,OAAM;IAChEgB,WAAW,EAAExE,cAAc,CAACa,gBAAgB,CAACE,iBAAiB;IAC9D0D,WAAW,EAAEX,SAAS;IACtBY,WAAW,EAAE,OAAgB;IAC7BC,WAAW,EAAEb,SAAS;IACtBc,YAAY,EAAE;MAAEC,KAAK,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC;IACrCC,aAAa,EAAE/E,cAAc,CAACa,gBAAgB,CAACK,cAAc;IAC7D8D,YAAY,EAAEhF,cAAc,CAACa,gBAAgB,CAACM,aAAa;IAC3D8D,SAAS,EAAEC,qBAAQ,CAACC,EAAE,KAAK,SAAS,GAAG,CAAC,GAAG,CAAC;IAC5CC,MAAM,EAAEpF,cAAc,CAACa,gBAAgB,CAACI;EAC1C,CAAC;AAAA,CAAC;AAGK,IAAMoE,UAAU,GAAApF,OAAA,CAAAoF,UAAA,GAAApF,OAAA,CAAAoF,UAAA,GAAG;EAExBC,kBAAkB,EAAE,SAApBA,kBAAkBA,CAAGC,OAAY,EAAK;IACpC,IAAIL,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;MAEzB,IAAII,OAAO,IAAIA,OAAO,CAACC,KAAK,EAAE;QAC5BD,OAAO,CAACC,KAAK,CAAC,CAAC;QACfD,OAAO,CAACE,cAAc,YAAtBF,OAAO,CAACE,cAAc,CAAG;UACvBC,QAAQ,EAAE,QAAQ;UAClBC,KAAK,EAAE,QAAQ;UACfC,MAAM,EAAE;QACV,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAGDC,qBAAqB,EAAE,SAAvBA,qBAAqBA,CAAGC,KAAc,EAAK;IACzC,IAAMC,UAAU,GAAGD,KAAK,IAAI,SAAS;IACrC,OAAAE,MAAA,CAAAC,MAAA,KACK1B,sBAAsB,CAACwB,UAAU,CAAC;MAErCG,YAAY,EAAElG,cAAc,CAACa,gBAAgB,CAACE,iBAAiB;MAC/DoF,YAAY,EAAEJ,UAAU;MACxBK,YAAY,EAAE,OAAgB;MAC9BC,aAAa,EAAErG,cAAc,CAACa,gBAAgB,CAACG;IAAM;EAEzD,CAAC;EAGDsF,kBAAkB,EAAE,SAApBA,kBAAkBA,CAAGC,QAAgB,EAA8B;IAAA,IAA5BC,kBAAkB,GAAAhD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;IAE5D,IAAMiD,YAAY,GAAGvB,qBAAQ,CAACC,EAAE,KAAK,KAAK,GAAGuB,MAAM,CAACC,WAAW,GAAG,GAAG;IACrE,IAAMC,eAAe,GAAGL,QAAQ,GAAGvG,cAAc,CAACO,aAAa,CAACC,YAAY;IAC5E,IAAMqG,eAAe,GAAGJ,YAAY,GAAGD,kBAAkB;IAEzD,OAAOI,eAAe,GAAGC,eAAe;EAC1C,CAAC;EAKDtC,sBAAsB,EAAE,SAAxBA,sBAAsBA,CACpBuC,SAAkB,EAQf;IAAA,IAPHC,SAAc,GAAAvD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAAA,IACnBwD,OAKC,GAAAxD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAEN,IAAAyD,cAAA,GAKID,OAAO,CAJTlB,KAAK;MAALA,KAAK,GAAAmB,cAAA,cAAG,SAAS,GAAAA,cAAA;MAAAC,cAAA,GAIfF,OAAO,CAHTnC,KAAK;MAAEsC,cAAc,GAAAD,cAAA,cAAGlH,cAAc,CAACa,gBAAgB,CAACE,iBAAiB,GAAAmG,cAAA;MAAAE,eAAA,GAGvEJ,OAAO,CAFTK,MAAM;MAANA,MAAM,GAAAD,eAAA,cAAGpH,cAAc,CAACa,gBAAgB,CAACG,MAAM,GAAAoG,eAAA;MAAAE,qBAAA,GAE7CN,OAAO,CADTO,gBAAgB;MAAhBA,gBAAgB,GAAAD,qBAAA,cAAG,IAAI,GAAAA,qBAAA;IAIzB,IAAMzC,KAAK,GAAG5C,IAAI,CAACgB,GAAG,CAACjD,cAAc,CAACa,gBAAgB,CAACC,SAAS,EAAEqG,cAAc,CAAC;IAEjF,IAAI,CAACL,SAAS,EAAE;MACd,OAAOC,SAAS;IAClB;IAEA,IAAMS,UAAU,GAAAxB,MAAA,CAAAC,MAAA,KACXc,SAAS;MACZvC,WAAW,EAAEK,KAAK;MAClBJ,WAAW,EAAEqB,KAAK;MAClBpB,WAAW,EAAE,OAAgB;MAE7BC,WAAW,EAAEmB,KAAK;MAClBlB,YAAY,EAAE;QAAEC,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MACrCC,aAAa,EAAE/E,cAAc,CAACa,gBAAgB,CAACK,cAAc;MAC7D8D,YAAY,EAAEhF,cAAc,CAACa,gBAAgB,CAACM,aAAa;MAE3DsG,QAAQ,EAAE,SAAkB;MAC5BrC,MAAM,EAAEmC,gBAAgB,GAAGvH,cAAc,CAACa,gBAAgB,CAACI,YAAY,GAAGyC,SAAS;MAEnFK,eAAe,EAAE,GAAG+B,KAAK,IAAI;MAE7B4B,YAAY,EAAEzF,IAAI,CAACgB,GAAG,CAAC8D,SAAS,CAACW,YAAY,IAAI,CAAC,EAAE,CAAC;IAAC,EACvD;IAGD,IAAMC,cAAc,GAAGzC,qBAAQ,CAAC0C,MAAM,CAAC;MACrCC,GAAG,EAAE;QACH3B,YAAY,EAAErB,KAAK;QACnBsB,YAAY,EAAEL,KAAK;QACnBM,YAAY,EAAE,OAAgB;QAC9BC,aAAa,EAAEgB;MACjB,CAAC;MACDS,OAAO,EAAE;QACP7C,SAAS,EAAE;MACb,CAAC;MACD8C,GAAG,EAAE;QACH9C,SAAS,EAAE;MACb,CAAC;MACD5C,OAAO,EAAE,CAAC;IACZ,CAAC,CAAC;IAEF,OAAA2D,MAAA,CAAAC,MAAA,KACKuB,UAAU,EACVG,cAAc;EAErB,CAAC;EAKDK,sBAAsB,EAAE,SAAxBA,sBAAsBA,CACpBC,mBAAwB,EAErB;IAAA,IADHC,cAA2D,GAAA1E,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;IAEhE,IAAM2E,eAAe,GAAGD,cAAc,CAACE,MAAM,CAAC,UAACnF,GAAG,EAAEsC,OAAO,EAAK;MAC9D,IAAIA,OAAO,CAAC8C,QAAQ,KAAK,QAAQ,IAAI9C,OAAO,CAAC8C,QAAQ,KAAK,OAAO,EAAE;QACjE,OAAOpG,IAAI,CAACgB,GAAG,CAACA,GAAG,EAAEsC,OAAO,CAACH,MAAM,IAAI,CAAC,CAAC;MAC3C;MACA,OAAOnC,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC;IAEL,OAAA+C,MAAA,CAAAC,MAAA,KACKgC,mBAAmB;MACtB7C,MAAM,EAAEnD,IAAI,CAACgB,GAAG,CACdgF,mBAAmB,CAAC7C,MAAM,IAAI,CAAC,EAC/B+C,eAAe,GAAG,CAAC,EACnBnI,cAAc,CAACa,gBAAgB,CAACI,YAClC;IAAC;EAEL,CAAC;EAKDqH,kBAAkB,EAAE,SAApBA,kBAAkBA,CAAA,EAAQ;IACxB,IAAIC,qBAA0B,GAAG,IAAI;IAErC,OAAO;MACLC,QAAQ,EAAE,SAAVA,QAAQA,CAAGjD,OAAY,EAAK;QAC1BgD,qBAAqB,GAAGhD,OAAO;MACjC,CAAC;MAEDkD,eAAe,EAAE,SAAjBA,eAAeA,CAAA;QAAA,OAAQF,qBAAqB;MAAA;MAE5CG,UAAU,EAAE,SAAZA,UAAUA,CAAA,EAAQ;QAChBH,qBAAqB,GAAG,IAAI;MAC9B,CAAC;MAEDI,SAAS,EAAE,SAAXA,SAASA,CAAGC,SAA8B,EAAK;QAE7CC,OAAO,CAACC,GAAG,CAAC,gBAAgBF,SAAS,EAAE,CAAC;MAC1C;IACF,CAAC;EACH;AACF,CAAC;AAOM,IAAMG,qBAAqB,GAAA9I,OAAA,CAAA8I,qBAAA,GAAG,SAAxBA,qBAAqBA,CAAA;EAAA,OAAS/I,cAAc,CAACO,aAAa,CAACC,YAAY;AAAA;AAG7E,IAAMwI,yBAAyB,GAAA/I,OAAA,CAAA+I,yBAAA,GAAG,SAA5BA,yBAAyBA,CAAA;EAAA,OAAShJ,cAAc,CAACO,aAAa,CAACE,gBAAgB;AAAA;AAGrF,IAAMwI,uBAAuB,GAAAhJ,OAAA,CAAAgJ,uBAAA,GAAG,SAA1BA,uBAAuBA,CAAIpE,KAAa,EAAEC,MAAc,EAAc;EACjF,IAAMoE,OAAO,GAAGlJ,cAAc,CAACO,aAAa,CAACC,YAAY;EACzD,OAAOqE,KAAK,IAAIqE,OAAO,IAAIpE,MAAM,IAAIoE,OAAO;AAC9C,CAAC;AAGM,IAAMC,mBAAmB,GAAAlJ,OAAA,CAAAkJ,mBAAA,GAAG,SAAtBA,mBAAmBA,CAAIC,UAAmB,EAAK;EAC1D,IAAMC,IAAI,GAAGD,UAAU,IAAIpJ,cAAc,CAACO,aAAa,CAACE,gBAAgB;EACxE,OAAO;IACL6I,QAAQ,EAAED,IAAI;IACdE,SAAS,EAAEF,IAAI;IACfG,iBAAiB,EAAEvH,IAAI,CAACgB,GAAG,CAAC,CAAC,EAAE,CAACoG,IAAI,GAAG,EAAE,IAAI,CAAC,CAAC;IAC/CI,eAAe,EAAExH,IAAI,CAACgB,GAAG,CAAC,CAAC,EAAE,CAACoG,IAAI,GAAG,EAAE,IAAI,CAAC;EAC9C,CAAC;AACH,CAAC;AAGM,IAAMK,gBAAgB,GAAAzJ,OAAA,CAAAyJ,gBAAA,GAAAzJ,OAAA,CAAAyJ,gBAAA,GAAG;EAE9BC,UAAU,EAAE,SAAZA,UAAUA,CAAA;IAAA,OAAQ3J,cAAc,CAACO,aAAa,CAACG,OAAO;EAAA;EAGtDkJ,mBAAmB,EAAE,SAArBA,mBAAmBA,CAAA;IAAA,OAAA5D,MAAA,CAAAC,MAAA,KACdkD,mBAAmB,CAACnJ,cAAc,CAACO,aAAa,CAACE,gBAAgB,CAAC;MACrEoJ,UAAU,EAAE,QAAiB;MAC7BC,cAAc,EAAE,QAAiB;MACjCpC,YAAY,EAAE;IAAC;EAAA,CACf;EAGFqC,QAAQ,EAAEd,uBAAuB;EAGjCe,mBAAmB,EAAE,SAArBA,mBAAmBA,CAAGnF,KAAa,EAAEC,MAAc,EAA6C;IAC9F,IAAMoE,OAAO,GAAGlJ,cAAc,CAACO,aAAa,CAACC,YAAY;IACzD,IAAMyJ,MAAgB,GAAG,EAAE;IAE3B,IAAIpF,KAAK,GAAGqE,OAAO,EAAE;MACnBe,MAAM,CAACC,IAAI,CAAC,SAASrF,KAAK,uBAAuBqE,OAAO,IAAI,CAAC;IAC/D;IACA,IAAIpE,MAAM,GAAGoE,OAAO,EAAE;MACpBe,MAAM,CAACC,IAAI,CAAC,UAAUpF,MAAM,uBAAuBoE,OAAO,IAAI,CAAC;IACjE;IAEA,OAAO;MACLiB,OAAO,EAAEF,MAAM,CAACxG,MAAM,KAAK,CAAC;MAC5BwG,MAAM,EAANA;IACF,CAAC;EACH,CAAC;EAGDG,UAAU,EAAErB,qBAAqB;EAGjCsB,kBAAkB,EAAErB,yBAAyB;EAG7CsB,sBAAsB,EAAE,SAAxBA,sBAAsBA,CAAA,EAAgB;IACpC,OAAOpF,qBAAQ,CAAC0C,MAAM,CAAC;MACrBG,GAAG,EAAE,EAAE;MACPD,OAAO,EAAE,EAAE;MACXzF,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EAGDkI,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAGC,UAAkB,EAAmE;IACtG,IAAMtB,OAAO,GAAGlJ,cAAc,CAACO,aAAa,CAACC,YAAY;IACzD,IAAMiK,OAAO,GAAGxI,IAAI,CAACgB,GAAG,CAAC,CAAC,EAAEiG,OAAO,GAAGsB,UAAU,CAAC;IACjD,IAAME,IAAI,GAAGzI,IAAI,CAAC0I,IAAI,CAACF,OAAO,GAAG,CAAC,CAAC;IAEnC,OAAO;MACLG,GAAG,EAAEF,IAAI;MACTG,MAAM,EAAEH,IAAI;MACZI,IAAI,EAAEJ,IAAI;MACVK,KAAK,EAAEL;IACT,CAAC;EACH;AACF,CAAC;AAeM,IAAMM,0BAA0B,GAAA/K,OAAA,CAAA+K,0BAAA,GAAG,SAA7BA,0BAA0BA,CACrCC,IAAiD,EACjDC,WAAoB,EACpBC,MAAe,EACa;EAC5B,QAAQF,IAAI;IACV,KAAK,YAAY;MACf,OAAO;QACLG,kBAAkB,EAAE,EAAE;QACtBC,iBAAiB,EAAE,OAAO;QAC1BC,UAAU,EAAE,KAAK;QACjBC,yBAAyB,EAAE;MAC7B,CAAC;IAEH,KAAK,aAAa;MAChB,OAAO;QACLH,kBAAkB,EAAEF,WAAW,IAAI,mBAAmB;QACtDG,iBAAiB,EAAE,OAAO;QAC1BC,UAAU,EAAE,IAAI;QAChBC,yBAAyB,EAAE;MAC7B,CAAC;IAEH,KAAK,YAAY;MACf,OAAO;QACLH,kBAAkB,EAAEF,WAAW,IAAI,mBAAmB;QACtDM,iBAAiB,EAAEL,MAAM,GAAG,iBAAiBA,MAAM,EAAE,GAAGzH,SAAS;QACjE2H,iBAAiB,EAAE,aAAa;QAChCC,UAAU,EAAE,IAAI;QAChBC,yBAAyB,EAAE;MAC7B,CAAC;IAEH;MACE,OAAO;QACLH,kBAAkB,EAAEF,WAAW,IAAI,OAAO;QAC1CG,iBAAiB,EAAE,OAAO;QAC1BC,UAAU,EAAE,IAAI;QAChBC,yBAAyB,EAAE;MAC7B,CAAC;EACL;AACF,CAAC;AAGM,IAAME,uBAAuB,GAAAxL,OAAA,CAAAwL,uBAAA,GAAG;EAErCC,aAAa,EAAE,SAAfA,aAAaA,CAAA;IAAA,OAAQV,0BAA0B,CAAC,YAAY,CAAC;EAAA;EAC7DW,cAAc,EAAE,SAAhBA,cAAcA,CAAGT,WAAmB;IAAA,OAAKF,0BAA0B,CAAC,aAAa,EAAEE,WAAW,CAAC;EAAA;EAC/FU,aAAa,EAAE,SAAfA,aAAaA,CAAGV,WAAmB,EAAEC,MAAe;IAAA,OAAKH,0BAA0B,CAAC,YAAY,EAAEE,WAAW,EAAEC,MAAM,CAAC;EAAA;EAGtHU,eAAe,EAAE,SAAjBA,eAAeA,CAAGZ,IAAY,EAAEa,UAAmB,EAAEX,MAAe,EAAa;IAC/E,QAAQF,IAAI;MACV,KAAK,MAAM;QACT,OAAOa,UAAU,GAAG,GAAGA,UAAU,OAAO,GAAG,cAAc;MAC3D,KAAK,QAAQ;QACX,OAAOA,UAAU,GAAG,GAAGA,UAAU,kBAAkB,GAAG,sBAAsB;MAC9E,KAAK,SAAS;QACZ,OAAOA,UAAU,GAAG,GAAGA,UAAU,gBAAgB,GAAG,eAAe;MACrE,KAAK,OAAO;QACV,OAAOA,UAAU,GAAG,GAAGA,UAAU,cAAc,GAAG,aAAa;MACjE,KAAK,MAAM;QACT,OAAOX,MAAM,GAAG,GAAGA,MAAM,OAAO,GAAGW,UAAU,IAAI,MAAM;MACzD;QACE,OAAOA,UAAU,IAAI,OAAO;IAChC;EACF;AACF,CAAC;AAOM,IAAMC,kBAAkB,GAAA9L,OAAA,CAAA8L,kBAAA,GAAG;EAEhCC,kBAAkB,EAAE,SAApBA,kBAAkBA,CAAGC,MAA0C;IAAA,OAAM;MACnEC,QAAQ,EAAE,SAAVA,QAAQA,CAAA;QAAA,OAAQD,MAAM,CAAC,IAAI,CAAC;MAAA;MAC5BE,UAAU,EAAE,SAAZA,UAAUA,CAAA;QAAA,OAAQF,MAAM,CAAC,MAAM,CAAC;MAAA;MAChCG,oBAAoB,EAAE,CACpB;QAAEC,IAAI,EAAE,WAAW;QAAEC,KAAK,EAAE;MAAU,CAAC,EACvC;QAAED,IAAI,EAAE,WAAW;QAAEC,KAAK,EAAE;MAAY,CAAC,CAC1C;MACDC,qBAAqB,EAAE,SAAvBA,qBAAqBA,CAAGC,KAAU,EAAK;QACrC,QAAQA,KAAK,CAACC,WAAW,CAACC,UAAU;UAClC,KAAK,WAAW;YACdT,MAAM,CAAC,IAAI,CAAC;YACZ;UACF,KAAK,WAAW;YACdA,MAAM,CAAC,MAAM,CAAC;YACd;QACJ;MACF;IACF,CAAC;EAAA,CAAC;EAGFU,mBAAmB,EAAE,SAArBA,mBAAmBA,CACjBC,WAAwB,EACxBC,YAAyB,EACzBC,SAAsB,EACtBC,WAAwB;IAAA,OACpB;MACJC,OAAO,EAAE,CACPJ,WAAW,IAAI;QAAEN,KAAK,EAAE,UAAU;QAAEW,OAAO,EAAEL;MAAY,CAAC,EAC1DC,YAAY,IAAI;QAAEP,KAAK,EAAE,MAAM;QAAEW,OAAO,EAAEJ;MAAa,CAAC,EACxDC,SAAS,IAAI;QAAER,KAAK,EAAE,IAAI;QAAEW,OAAO,EAAEH;MAAU,CAAC,EAChDC,WAAW,IAAI;QAAET,KAAK,EAAE,MAAM;QAAEW,OAAO,EAAEF;MAAY,CAAC,CACvD,CAACG,MAAM,CAACC,OAAO;IAClB,CAAC;EAAA,CAAC;EAGFC,wBAAwB,EAAE,SAA1BA,wBAAwBA,CACtBC,OAAiC,EACjCC,QAAqC;IAAA,OACjC;MACJC,MAAM,EAAEF,OAAO,GAAG;QAAA,OAAMA,OAAO,CAAC,GAAG,CAAC;MAAA,IAAG3J,SAAS;MAChD8J,OAAO,EAAEH,OAAO,GAAG;QAAA,OAAMA,OAAO,CAAC,GAAG,CAAC;MAAA,IAAG3J,SAAS;MACjD+J,UAAU,EAAEH,QAAQ,GAAG;QAAA,OAAMA,QAAQ,CAAC,CAAC,EAAE,CAAC;MAAA,IAAG5J,SAAS;MACtDgK,WAAW,EAAEJ,QAAQ,GAAG;QAAA,OAAMA,QAAQ,CAAC,EAAE,CAAC;MAAA,IAAG5J;IAC/C,CAAC;EAAA;AACH,CAAC;AAMM,IAAMiK,iBAAiB,GAAA1N,OAAA,CAAA0N,iBAAA,GAAA1N,OAAA,CAAA0N,iBAAA,GAAG;EAE/BC,wBAAwB,EAAE,SAA1BA,wBAAwBA,CAAGC,OAAe,EAAK;IAC7C,IAAI3I,qBAAQ,CAACC,EAAE,KAAK,KAAK,IAAID,qBAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;MACtD2I,8BAAiB,CAACF,wBAAwB,CAACC,OAAO,CAAC;IACrD;EACF,CAAC;EAGDE,qBAAqB;IAAA,IAAAC,sBAAA,OAAAC,kBAAA,CAAA5L,OAAA,EAAE,aAA8B;MACnD,IAAI;QACF,aAAayL,8BAAiB,CAACC,qBAAqB,CAAC,CAAC;MACxD,CAAC,CAAC,OAAAG,OAAA,EAAM;QACN,OAAO,KAAK;MACd;IACF,CAAC;IAAA,SANDH,qBAAqBA,CAAA;MAAA,OAAAC,sBAAA,CAAAG,KAAA,OAAA3K,SAAA;IAAA;IAAA,OAArBuK,qBAAqB;EAAA,GAMpB;EAGDK,kBAAkB,EAAE,SAApBA,kBAAkBA,CAChBC,OAAe,EACfC,SAAkB,EAClBC,KAAc,EACdlG,QAAiB,EACN;IACX,IAAMmG,KAAK,GAAG,CAACH,OAAO,CAAC;IACvB,IAAIC,SAAS,EAAEE,KAAK,CAACtE,IAAI,CAACoE,SAAS,CAAC;IACpC,IAAIC,KAAK,EAAEC,KAAK,CAACtE,IAAI,CAACqE,KAAK,CAAC;IAC5B,IAAIlG,QAAQ,EAAEmG,KAAK,CAACtE,IAAI,CAAC7B,QAAQ,CAAC;IAClC,OAAOmG,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC;EACzB,CAAC;EAGDC,uBAAuB,EAAE,SAAzBA,uBAAuBA,CACrBL,OAAe,EACfE,KAAc,EACdI,OAAgB,EAChBC,IAAa,EACF;IACX,IAAMJ,KAAK,GAAG,CAACH,OAAO,CAAC;IACvB,IAAIE,KAAK,EAAEC,KAAK,CAACtE,IAAI,CAACqE,KAAK,CAAC;IAC5B,IAAII,OAAO,EAAEH,KAAK,CAACtE,IAAI,CAACyE,OAAO,CAAC;IAChC,IAAIC,IAAI,EAAEJ,KAAK,CAACtE,IAAI,CAAC0E,IAAI,CAAC;IAC1B,OAAOJ,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC;EACzB,CAAC;EAGDI,eAAe,EAAE,SAAjBA,eAAeA,CAAGC,aAAqB,EAAa;IAClD,IAAMC,OAA+B,GAAG;MACtCC,MAAM,EAAE,QAAQ;MAChBC,KAAK,EAAE,MAAM;MACbC,QAAQ,EAAE,UAAU;MACpBC,KAAK,EAAE,OAAO;MACdC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,OAAO;MACdC,IAAI,EAAE,MAAM;MACZC,MAAM,EAAE,QAAQ;MAChBC,IAAI,EAAE,MAAM;MACZC,QAAQ,EAAE;IACZ,CAAC;IACD,OAAOV,OAAO,CAACD,aAAa,CAAC,IAAI,MAAM;EACzC;AACF,CAAC;AAOM,IAAMY,oBAAoB,GAAAzP,OAAA,CAAAyP,oBAAA;EAAA,IAAAC,IAAA,OAAA1B,kBAAA,CAAA5L,OAAA,EAAG,aAA8B;IAChE,IAAI;MACF,aAAayL,8BAAiB,CAAC8B,qBAAqB,CAAC,CAAC;IACxD,CAAC,CAAC,OAAAC,QAAA,EAAM;MACN,OAAO,KAAK;IACd;EACF,CAAC;EAAA,gBANYH,oBAAoBA,CAAA;IAAA,OAAAC,IAAA,CAAAxB,KAAA,OAAA3K,SAAA;EAAA;AAAA,GAMhC;AAGM,IAAMsM,oBAAoB,GAAA7P,OAAA,CAAA6P,oBAAA,GAAG,SAAvBA,oBAAoBA,CAAIC,WAAmB,EAAa;EAEnE,OAAO9N,IAAI,CAACgB,GAAG,CAAC8M,WAAW,EAAE/P,cAAc,CAACO,aAAa,CAACG,OAAO,CAAC;AACpE,CAAC;AAGM,IAAMsP,qBAAqB,GAAA/P,OAAA,CAAA+P,qBAAA,GAAG,SAAxBA,qBAAqBA,CAAIC,YAAoB,EAAa;EAErE,OAAOhO,IAAI,CAACgB,GAAG,CAACgN,YAAY,EAAE,EAAE,CAAC;AACnC,CAAC;AAGM,IAAMC,yBAAyB,GAAAjQ,OAAA,CAAAiQ,yBAAA,GAAG,SAA5BA,yBAAyBA,CAAIC,KAAU;EAAA,OAAAnK,MAAA,CAAAC,MAAA,KAC/CkK,KAAK;IACR7E,UAAU,EAAE6E,KAAK,CAAC7E,UAAU,KAAK,KAAK;IACtCD,iBAAiB,EAAE8E,KAAK,CAAC9E,iBAAiB,IAAI,QAAQ;IACtDE,yBAAyB,EAAE4E,KAAK,CAAC5E,yBAAyB,IAAI;EAAK;AAAA,CACnE;AAGK,IAAM6E,kBAAkB,GAAAnQ,OAAA,CAAAmQ,kBAAA,GAAG;EAChCpQ,cAAc,EAAdA,cAAc;EAEdqB,QAAQ,EAARA,QAAQ;EACRQ,oBAAoB,EAApBA,oBAAoB;EACpBY,gBAAgB,EAAhBA,gBAAgB;EAChBW,WAAW,EAAXA,WAAW;EACXiN,qBAAqB,EAAExM,0BAA0B;EAEjDwB,UAAU,EAAVA,UAAU;EACVd,sBAAsB,EAAtBA,sBAAsB;EAEtBmF,gBAAgB,EAAhBA,gBAAgB;EAChBX,qBAAqB,EAArBA,qBAAqB;EACrBC,yBAAyB,EAAzBA,yBAAyB;EACzBC,uBAAuB,EAAvBA,uBAAuB;EACvBE,mBAAmB,EAAnBA,mBAAmB;EAEnBsC,uBAAuB,EAAvBA,uBAAuB;EACvBT,0BAA0B,EAA1BA,0BAA0B;EAE1Be,kBAAkB,EAAlBA,kBAAkB;EAElB4B,iBAAiB,EAAjBA,iBAAiB;EAEjB+B,oBAAoB,EAApBA,oBAAoB;EACpBI,oBAAoB,EAApBA,oBAAoB;EACpBE,qBAAqB,EAArBA,qBAAqB;EACrBE,yBAAyB,EAAzBA;AACF,CAAC;AAGDlK,MAAM,CAACsK,MAAM,CAACtQ,cAAc,CAAC;AAC7BgG,MAAM,CAACsK,MAAM,CAACtQ,cAAc,CAACE,eAAe,CAAC;AAC7C8F,MAAM,CAACsK,MAAM,CAACtQ,cAAc,CAACO,aAAa,CAAC;AAC3CyF,MAAM,CAACsK,MAAM,CAACtQ,cAAc,CAACW,WAAW,CAAC;AACzCqF,MAAM,CAACsK,MAAM,CAACtQ,cAAc,CAACa,gBAAgB,CAAC;AAGvC,IAAM0P,kBAAkB,GAAAtQ,OAAA,CAAAsQ,kBAAA,GAAAtQ,OAAA,CAAAsQ,kBAAA,GAAG;EAIhClP,QAAQ,EAAE,SAAVA,QAAQA,CAAGC,GAAW,EAAiD;IACrE,IAAMC,MAAM,GAAG,2CAA2C,CAACC,IAAI,CAACF,GAAG,CAAC;IACpE,OAAOC,MAAM,GACT;MACEE,CAAC,EAAEC,QAAQ,CAACH,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAC1BI,CAAC,EAAED,QAAQ,CAACH,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAC1BK,CAAC,EAAEF,QAAQ,CAACH,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE;IAC3B,CAAC,GACD,IAAI;EACV,CAAC;EAKDM,oBAAoB,EAAE,SAAtBA,oBAAoBA,CAAGP,GAAW,EAAa;IAC7C,IAAMkP,GAAG,GAAGD,kBAAkB,CAAClP,QAAQ,CAACC,GAAG,CAAC;IAC5C,IAAI,CAACkP,GAAG,EAAE,OAAO,CAAC;IAElB,IAAQ/O,CAAC,GAAW+O,GAAG,CAAf/O,CAAC;MAAEE,CAAC,GAAQ6O,GAAG,CAAZ7O,CAAC;MAAEC,CAAC,GAAK4O,GAAG,CAAT5O,CAAC;IACf,IAAA6O,KAAA,GAAqB,CAAChP,CAAC,EAAEE,CAAC,EAAEC,CAAC,CAAC,CAACG,GAAG,CAAC,UAAAC,CAAC,EAAI;QACtCA,CAAC,GAAGA,CAAC,GAAG,GAAG;QACX,OAAOA,CAAC,IAAI,OAAO,GAAGA,CAAC,GAAG,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,CAACF,CAAC,GAAG,KAAK,IAAI,KAAK,EAAE,GAAG,CAAC;MACtE,CAAC,CAAC;MAAA0O,KAAA,OAAAtO,eAAA,CAAAC,OAAA,EAAAoO,KAAA;MAHKnO,EAAE,GAAAoO,KAAA;MAAEnO,EAAE,GAAAmO,KAAA;MAAElO,EAAE,GAAAkO,KAAA;IAKjB,OAAO,MAAM,GAAGpO,EAAE,GAAG,MAAM,GAAGC,EAAE,GAAG,MAAM,GAAGC,EAAE;EAChD,CAAC;EAKDC,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAGC,MAAc,EAAEC,MAAc,EAAa;IAC5D,IAAMgO,EAAE,GAAGJ,kBAAkB,CAAC1O,oBAAoB,CAACa,MAAM,CAAC;IAC1D,IAAMkO,EAAE,GAAGL,kBAAkB,CAAC1O,oBAAoB,CAACc,MAAM,CAAC;IAE1D,IAAMkO,OAAO,GAAG5O,IAAI,CAACgB,GAAG,CAAC0N,EAAE,EAAEC,EAAE,CAAC;IAChC,IAAMvM,MAAM,GAAGpC,IAAI,CAACkB,GAAG,CAACwN,EAAE,EAAEC,EAAE,CAAC;IAE/B,OAAO,CAACC,OAAO,GAAG,IAAI,KAAKxM,MAAM,GAAG,IAAI,CAAC;EAC3C,CAAC;EAKDyM,gBAAgB,EAAE,SAAlBA,gBAAgBA,CACdzN,UAAkB,EAClBC,UAAkB,EAQf;IAAA,IAPHyN,KAAmB,GAAAvN,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;IAAA,IAC1BD,WAAoB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;IAO5B,IAAMG,KAAK,GAAG4M,kBAAkB,CAAC9N,gBAAgB,CAACY,UAAU,EAAEC,UAAU,CAAC;IAEzE,IAAIM,aAAqB;IACzB,IAAImN,KAAK,KAAK,KAAK,EAAE;MACnBnN,aAAa,GAAGL,WAAW,GAAG,GAAG,GAAG,GAAG;IACzC,CAAC,MAAM;MACLK,aAAa,GAAGL,WAAW,GAAG,GAAG,GAAG,GAAG;IACzC;IAEA,IAAMyN,WAAW,GAAGrN,KAAK,IAAIC,aAAa;IAE1C,IAAIqN,cAAc,GAAG,EAAE;IACvB,IAAI,CAACD,WAAW,EAAE;MAChB,IAAME,WAAW,GAAG,CAACtN,aAAa,GAAGD,KAAK,EAAEwN,OAAO,CAAC,CAAC,CAAC;MACtDF,cAAc,GAAG,wBAAwBC,WAAW,aAAaH,KAAK,YAAY;IACpF,CAAC,MAAM;MACLE,cAAc,GAAG,SAASF,KAAK,eAAepN,KAAK,CAACwN,OAAO,CAAC,CAAC,CAAC,KAAK;IACrE;IAEA,OAAO;MACLxN,KAAK,EAAE1B,IAAI,CAACmP,KAAK,CAACzN,KAAK,GAAG,GAAG,CAAC,GAAG,GAAG;MACpCqN,WAAW,EAAXA,WAAW;MACXpN,aAAa,EAAbA,aAAa;MACbqN,cAAc,EAAdA;IACF,CAAC;EACH,CAAC;EAKDI,sBAAsB,EAAE,SAAxBA,sBAAsBA,CAAGtN,eAAuB,EAAa;IAC3D,IAAMuN,aAAa,GAAGf,kBAAkB,CAAC9N,gBAAgB,CAAC,SAAS,EAAEsB,eAAe,CAAC;IACrF,IAAMwN,aAAa,GAAGhB,kBAAkB,CAAC9N,gBAAgB,CAAC,SAAS,EAAEsB,eAAe,CAAC;IAErF,OAAOuN,aAAa,GAAGC,aAAa,GAAG,SAAS,GAAG,SAAS;EAC9D,CAAC;EAKDC,oBAAoB,EAAE,SAAtBA,oBAAoBA,CAClB1L,KAAa,EACb2L,gBAAwB,EAEb;IAAA,IADXC,WAAmB,GAAAlO,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,GAAG;IAEzB,IAAMmO,YAAY,GAAGpB,kBAAkB,CAAC9N,gBAAgB,CAACqD,KAAK,EAAE2L,gBAAgB,CAAC;IAEjF,IAAIE,YAAY,IAAID,WAAW,EAAE;MAC/B,OAAO5L,KAAK;IACd;IAEA,IAAM0K,GAAG,GAAGD,kBAAkB,CAAClP,QAAQ,CAACyE,KAAK,CAAC;IAC9C,IAAI,CAAC0K,GAAG,EAAE,OAAO1K,KAAK;IAGtB,IAAM8L,mBAAmB,GAAGrB,kBAAkB,CAAC1O,oBAAoB,CAAC4P,gBAAgB,CAAC;IAGrF,IAAII,QAAQ;IACZ,IAAID,mBAAmB,GAAG,GAAG,EAAE;MAE7B,IAAME,MAAM,GAAG,GAAG;MAClBD,QAAQ,GAAG;QACTpQ,CAAC,EAAEQ,IAAI,CAACgB,GAAG,CAAC,CAAC,EAAEhB,IAAI,CAACmP,KAAK,CAACZ,GAAG,CAAC/O,CAAC,GAAGqQ,MAAM,CAAC,CAAC;QAC1CnQ,CAAC,EAAEM,IAAI,CAACgB,GAAG,CAAC,CAAC,EAAEhB,IAAI,CAACmP,KAAK,CAACZ,GAAG,CAAC7O,CAAC,GAAGmQ,MAAM,CAAC,CAAC;QAC1ClQ,CAAC,EAAEK,IAAI,CAACgB,GAAG,CAAC,CAAC,EAAEhB,IAAI,CAACmP,KAAK,CAACZ,GAAG,CAAC5O,CAAC,GAAGkQ,MAAM,CAAC;MAC3C,CAAC;IACH,CAAC,MAAM;MAEL,IAAMA,OAAM,GAAG,GAAG;MAClBD,QAAQ,GAAG;QACTpQ,CAAC,EAAEQ,IAAI,CAACkB,GAAG,CAAC,GAAG,EAAElB,IAAI,CAACmP,KAAK,CAACZ,GAAG,CAAC/O,CAAC,GAAGqQ,OAAM,CAAC,CAAC;QAC5CnQ,CAAC,EAAEM,IAAI,CAACkB,GAAG,CAAC,GAAG,EAAElB,IAAI,CAACmP,KAAK,CAACZ,GAAG,CAAC7O,CAAC,GAAGmQ,OAAM,CAAC,CAAC;QAC5ClQ,CAAC,EAAEK,IAAI,CAACkB,GAAG,CAAC,GAAG,EAAElB,IAAI,CAACmP,KAAK,CAACZ,GAAG,CAAC5O,CAAC,GAAGkQ,OAAM,CAAC;MAC7C,CAAC;IACH;IAEA,IAAMC,WAAW,GAAG,IAAIF,QAAQ,CAACpQ,CAAC,CAACuQ,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAGJ,QAAQ,CAAClQ,CAAC,CAACqQ,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAGJ,QAAQ,CAACjQ,CAAC,CAACoQ,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;IAGxJ,IAAMC,QAAQ,GAAG3B,kBAAkB,CAAC9N,gBAAgB,CAACsP,WAAW,EAAEN,gBAAgB,CAAC;IACnF,IAAIS,QAAQ,IAAIR,WAAW,EAAE;MAC3B,OAAOK,WAAW;IACpB;IAGA,OAAOH,mBAAmB,GAAG,GAAG,GAAG,SAAS,GAAG,SAAS;EAC1D;AACF,CAAC;AAUM,IAAMO,sBAAsB,GAAAlS,OAAA,CAAAkS,sBAAA,GAAAlS,OAAA,CAAAkS,sBAAA,GAAG;EAIpCC,cAAc,EAAE,SAAhBA,cAAcA,CAAGC,cAAmB,EAI/B;IAAA,IAAAC,qBAAA,EAAAC,sBAAA;IACH,IAAMtI,MAAgB,GAAG,EAAE;IAC3B,IAAMuI,QAAkB,GAAG,EAAE;IAC7B,IAAMC,eAAyB,GAAG,EAAE;IAGpC,IAAI,CAACJ,cAAc,CAACjH,kBAAkB,IAAI,CAACiH,cAAc,CAACK,QAAQ,EAAE;MAClEzI,MAAM,CAACC,IAAI,CAAC,6BAA6B,CAAC;MAC1CuI,eAAe,CAACvI,IAAI,CAAC,6BAA6B,CAAC;IACrD;IAGA,IAAI,CAACmI,cAAc,CAAChH,iBAAiB,EAAE;MACrCmH,QAAQ,CAACtI,IAAI,CAAC,4BAA4B,CAAC;MAC3CuI,eAAe,CAACvI,IAAI,CAAC,mCAAmC,CAAC;IAC3D;IAGA,IAAI,CAAAoI,qBAAA,GAAAD,cAAc,CAACM,KAAK,aAApBL,qBAAA,CAAsBzN,KAAK,KAAA0N,sBAAA,GAAIF,cAAc,CAACM,KAAK,aAApBJ,sBAAA,CAAsBzN,MAAM,EAAE;MAC/D,IAAM8N,UAAU,GAAGlJ,gBAAgB,CAACM,mBAAmB,CACrDqI,cAAc,CAACM,KAAK,CAAC9N,KAAK,EAC1BwN,cAAc,CAACM,KAAK,CAAC7N,MACvB,CAAC;MAED,IAAI,CAAC8N,UAAU,CAACzI,OAAO,EAAE;QACvBF,MAAM,CAACC,IAAI,CAAAiE,KAAA,CAAXlE,MAAM,MAAA4I,mBAAA,CAAAxQ,OAAA,EAASuQ,UAAU,CAAC3I,MAAM,EAAC;QACjCwI,eAAe,CAACvI,IAAI,CAAAiE,KAAA,CAApBsE,eAAe,MAAAI,mBAAA,CAAAxQ,OAAA,EAASuQ,UAAU,CAACH,eAAe,EAAC;MACrD;IACF;IAEA,OAAO;MAAExI,MAAM,EAANA,MAAM;MAAEuI,QAAQ,EAARA,QAAQ;MAAEC,eAAe,EAAfA;IAAgB,CAAC;EAC9C,CAAC;EAKDK,2BAA2B,EAAE,SAA7BA,2BAA2BA,CAAGC,UAAiB,EAM1C;IACH,IAAMC,OAAO,GAAGD,UAAU,CAAChR,GAAG,CAAC,UAACkR,SAAS,EAAEC,KAAK;MAAA,OAAM;QACpDC,cAAc,EAAED,KAAK;QACrBE,KAAK,EAAEjB,sBAAsB,CAACC,cAAc,CAACa,SAAS;MACxD,CAAC;IAAA,CAAC,CAAC;IAEH,IAAMI,WAAW,GAAGL,OAAO,CAAC5K,MAAM,CAAC,UAACkL,GAAG,EAAEC,MAAM;MAAA,OAAKD,GAAG,GAAGC,MAAM,CAACH,KAAK,CAACnJ,MAAM,CAACxG,MAAM;IAAA,GAAE,CAAC,CAAC;IACxF,IAAM+P,aAAa,GAAGR,OAAO,CAAC5K,MAAM,CAAC,UAACkL,GAAG,EAAEC,MAAM;MAAA,OAAKD,GAAG,GAAGC,MAAM,CAACH,KAAK,CAACZ,QAAQ,CAAC/O,MAAM;IAAA,GAAE,CAAC,CAAC;IAE5F,IAAMgQ,eAAe,GAAGxR,IAAI,CAACgB,GAAG,CAAC,CAAC,EAAE,GAAG,GAAIoQ,WAAW,GAAG,EAAG,GAAIG,aAAa,GAAG,CAAE,CAAC;IAEnF,OAAO;MACLE,eAAe,EAAEX,UAAU,CAACtP,MAAM;MAClCkQ,WAAW,EAAEN,WAAW;MACxBO,aAAa,EAAEJ,aAAa;MAC5BC,eAAe,EAAfA,eAAe;MACfT,OAAO,EAAPA;IACF,CAAC;EACH;AACF,CAAC;AAcM,IAAMa,iBAAiB,GAAA5T,OAAA,CAAA4T,iBAAA,GAAG;EAI/BC,uBAAuB,EAAE,SAAzBA,uBAAuBA,CAAA,EAAiB;IACtC,OAAO5O,qBAAQ,CAACC,EAAE,KAAK,KAAK,IAAID,qBAAQ,CAAC6O,OAAO,IAAI,MAAM;EAC5D,CAAC;EAKDC,kBAAkB,EAAE,SAApBA,kBAAkBA,CAAG1E,IAAY,EAAEX,OAAgB,EAAa;IAE9D,IAAMsF,SAAS,GAAG3E,IAAI,CAAC4E,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC;IAC7D,OAAOxF,OAAO,GAAG,GAAGA,OAAO,IAAIsF,SAAS,EAAE,GAAGA,SAAS;EACxD,CAAC;EAKDG,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAGC,OAAiB,EAAa;IAC/C,OAAO,sBAAsBA,OAAO,CAAC5F,IAAI,CAAC,IAAI,CAAC,EAAE;EACnD;AACF,CAAC;AAGM,IAAM6F,yBAAyB,GAAArU,OAAA,CAAAqU,yBAAA,GAAG;EAIvCC,wBAAwB;IAAA,IAAAC,yBAAA,OAAAvG,kBAAA,CAAA5L,OAAA,EAAE,aAA8B;MACtD,IAAI;QACF,IAAMuN,qBAAqB,SAAS9B,8BAAiB,CAAC8B,qBAAqB,CAAC,CAAC;QAC7E,OAAOA,qBAAqB;MAC9B,CAAC,CAAC,OAAA6E,QAAA,EAAM;QACN,OAAO,KAAK;MACd;IACF,CAAC;IAAA,SAPDF,wBAAwBA,CAAA;MAAA,OAAAC,yBAAA,CAAArG,KAAA,OAAA3K,SAAA;IAAA;IAAA,OAAxB+Q,wBAAwB;EAAA,GAOvB;EAKDG,sBAAsB,EAAE,SAAxBA,sBAAsBA,CAAGC,WAAmB,EAAe;IACzD,IAAMC,YAAsC,GAAG;MAC7CC,KAAK,EAAE,CAAC,wBAAwB,EAAE,wBAAwB,CAAC;MAC3DC,KAAK,EAAE,CAAC,mBAAmB,EAAE,oBAAoB,CAAC;MAClDC,SAAS,EAAE,CAAC,yBAAyB,EAAE,qBAAqB,CAAC;MAC7DC,IAAI,EAAE,CAAC,kBAAkB,EAAE,2BAA2B;IACxD,CAAC;IACD,OAAOJ,YAAY,CAACD,WAAW,CAAC,IAAI,CAAC,0BAA0B,CAAC;EAClE,CAAC;EAKDM,yBAAyB,EAAE,SAA3BA,yBAAyBA,CAAGC,OAAe,EAAEC,WAAmB,EAAa;IAC3E,OAAO,YAAYD,OAAO,kBAAkBC,WAAW,EAAE;EAC3D;AACF,CAAC;AAGM,IAAMC,2BAA2B,GAAAnV,OAAA,CAAAmV,2BAAA,GAAG;EAIzCC,YAAY,EAAE,SAAdA,YAAYA,CAAG/F,IAAY,EAA4E;IAAA,IAA1EyB,KAA4C,GAAAvN,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,cAAc;IACxF,IAAIuN,KAAK,KAAK,OAAO,EAAE;MAErB,OAAOzB,IAAI,CACR4E,OAAO,CAAC,WAAW,EAAE,KAAK,CAAC,CAC3BA,OAAO,CAAC,cAAc,EAAE,MAAM,CAAC,CAC/BA,OAAO,CAAC,iBAAiB,EAAE,OAAO,CAAC,CACnCA,OAAO,CAAC,gBAAgB,EAAE,MAAM,CAAC;IACtC;IACA,OAAO5E,IAAI;EACb,CAAC;EAKDgG,mBAAmB,EAAE,SAArBA,mBAAmBA,CAAGhG,IAAY,EAA2C;IAAA,IAAzCiG,cAAsB,GAAA/R,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,GAAG;IAC9D,IAAMgS,SAAS,GAAGlG,IAAI,CAACmG,KAAK,CAAC,KAAK,CAAC,CAAChS,MAAM;IAC1C,IAAMiS,OAAO,GAAGzT,IAAI,CAAC0I,IAAI,CAAC6K,SAAS,GAAGD,cAAc,CAAC;IACrD,OAAO,iBAAiBG,OAAO,UAAUA,OAAO,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE;EACrE,CAAC;EAKDC,uBAAuB,EAAE,SAAzBA,uBAAuBA,CAAGC,WAAmB,EAAEC,UAAkB,EAAa;IAC5E,OAAO,QAAQD,WAAW,OAAOC,UAAU,EAAE;EAC/C;AACF,CAAC;AAGM,IAAMC,oBAAoB,GAAA7V,OAAA,CAAA6V,oBAAA,GAAGzQ,UAAU;AAGvC,IAAM0Q,kBAAkB,GAAA9V,OAAA,CAAA8V,kBAAA,GAAG3F,kBAAkB;AAAC,IAAA4F,QAAA,GAAA/V,OAAA,CAAAoC,OAAA,GAEtC;EACbrC,cAAc,EAAdA,cAAc;EACduQ,kBAAkB,EAAlBA,kBAAkB;EAClB7G,gBAAgB,EAAhBA,gBAAgB;EAChBrE,UAAU,EAAVA,UAAU;EACVyQ,oBAAoB,EAApBA,oBAAoB;EACpBnI,iBAAiB,EAAjBA,iBAAiB;EACjBwE,sBAAsB,EAAtBA,sBAAsB;EACtB0B,iBAAiB,EAAjBA,iBAAiB;EACjBS,yBAAyB,EAAzBA,yBAAyB;EACzBc,2BAA2B,EAA3BA,2BAA2B;EAK3B/E,qBAAqB,EAAExM,0BAA0B;EAKjDoS,kBAAkB,EAAE,SAApBA,kBAAkBA,CAChB5S,UAAkB,EAClBC,UAAkB,EAGN;IAAA,IAFZyN,KAAmB,GAAAvN,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;IAAA,IAC1B0S,QAA4B,GAAA1S,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,QAAQ;IAEvC,IAAMG,KAAK,GAAG,IAAAwS,0CAAsB,EAAC9S,UAAU,EAAEC,UAAU,CAAC;IAC5D,IAAMM,aAAa,GAAGmN,KAAK,KAAK,KAAK,GAChCmF,QAAQ,KAAK,OAAO,GAAG,GAAG,GAAG,GAAG,GAChCA,QAAQ,KAAK,OAAO,GAAG,GAAG,GAAG,GAAI;IACtC,OAAOvS,KAAK,IAAIC,aAAa;EAC/B;AACF,CAAC;AAGM,IAAMyM,qBAAqB,GAAApQ,OAAA,CAAAoQ,qBAAA,GAAGxM,0BAA0B", "ignoreList": []}