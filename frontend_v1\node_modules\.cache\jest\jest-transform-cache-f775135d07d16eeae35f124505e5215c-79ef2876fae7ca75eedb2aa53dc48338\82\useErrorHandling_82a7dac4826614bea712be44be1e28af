8006919c448631341e2207c1d20d3ca4
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useErrorHandling = exports.default = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _react = require("react");
var _reactNative = require("react-native");
var _performanceMonitor = require("../services/performanceMonitor");
var _errorHandlingUtils = require("../utils/errorHandlingUtils");
var useErrorHandling = exports.useErrorHandling = function useErrorHandling() {
  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  var _options$maxRetries = options.maxRetries,
    maxRetries = _options$maxRetries === void 0 ? 3 : _options$maxRetries,
    _options$retryDelay = options.retryDelay,
    retryDelay = _options$retryDelay === void 0 ? 2000 : _options$retryDelay,
    _options$progressiveR = options.progressiveRetryDelay,
    progressiveRetryDelay = _options$progressiveR === void 0 ? true : _options$progressiveR,
    _options$autoRetryOnA = options.autoRetryOnAppFocus,
    autoRetryOnAppFocus = _options$autoRetryOnA === void 0 ? true : _options$autoRetryOnA,
    _options$autoRetryOnC = options.autoRetryOnConnectionRestored,
    autoRetryOnConnectionRestored = _options$autoRetryOnC === void 0 ? true : _options$autoRetryOnC,
    _options$reportErrors = options.reportErrors,
    reportErrors = _options$reportErrors === void 0 ? true : _options$reportErrors,
    _options$errorContext = options.errorContext,
    errorContext = _options$errorContext === void 0 ? {} : _options$errorContext,
    onError = options.onError,
    onRetry = options.onRetry,
    onMaxRetriesExceeded = options.onMaxRetriesExceeded;
  var _useState = (0, _react.useState)({
      error: null,
      isError: false,
      retryCount: 0,
      lastRetryTime: null,
      canRetry: true
    }),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    state = _useState2[0],
    setState = _useState2[1];
  var appStateRef = (0, _react.useRef)(_reactNative.AppState.currentState);
  var retryTimeoutRef = (0, _react.useRef)(null);
  var componentMountedRef = (0, _react.useRef)(true);
  (0, _react.useEffect)(function () {
    return function () {
      componentMountedRef.current = false;
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, []);
  (0, _react.useEffect)(function () {
    if (!autoRetryOnAppFocus) return;
    var handleAppStateChange = function handleAppStateChange(nextAppState) {
      if (appStateRef.current.match(/inactive|background/) && nextAppState === 'active' && state.isError && state.retryCount < maxRetries) {
        retry();
      }
      appStateRef.current = nextAppState;
    };
    var subscription = _reactNative.AppState.addEventListener('change', handleAppStateChange);
    return function () {
      subscription.remove();
    };
  }, [state.isError, state.retryCount, maxRetries, autoRetryOnAppFocus]);
  var handleError = (0, _react.useCallback)(function (errorInput) {
    var error = typeof errorInput === 'string' ? new Error(errorInput) : errorInput;
    var appError = error instanceof _errorHandlingUtils.AppError ? error : (0, _errorHandlingUtils.createAppError)(error, {
      component: 'useErrorHandling',
      action: 'handleError',
      additionalData: errorContext
    });
    setState(function (prev) {
      return {
        error: appError,
        isError: true,
        retryCount: prev.retryCount,
        lastRetryTime: Date.now(),
        canRetry: prev.retryCount < maxRetries
      };
    });
    if (reportErrors) {
      (0, _errorHandlingUtils.logError)(appError);
    }
    _performanceMonitor.performanceMonitor.trackUserInteraction('error_handled', 0, {
      errorType: appError.name,
      errorMessage: appError.message,
      retryCount: state.retryCount,
      component: errorContext.component
    });
    if (onError) {
      onError(appError);
    }
    if (state.retryCount >= maxRetries && onMaxRetriesExceeded) {
      onMaxRetriesExceeded();
    }
  }, [errorContext, maxRetries, onError, onMaxRetriesExceeded, reportErrors, state.retryCount]);
  var clearError = (0, _react.useCallback)(function () {
    setState({
      error: null,
      isError: false,
      retryCount: 0,
      lastRetryTime: null,
      canRetry: true
    });
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
      retryTimeoutRef.current = null;
    }
  }, []);
  var retry = (0, _react.useCallback)((0, _asyncToGenerator2.default)(function* () {
    var _state$error, _state$error2;
    if (!state.isError || state.retryCount >= maxRetries) {
      return;
    }
    var currentRetryDelay = progressiveRetryDelay ? retryDelay * Math.pow(1.5, state.retryCount) : retryDelay;
    setState(function (prev) {
      return Object.assign({}, prev, {
        retryCount: prev.retryCount + 1,
        lastRetryTime: Date.now(),
        canRetry: prev.retryCount + 1 < maxRetries
      });
    });
    _performanceMonitor.performanceMonitor.trackUserInteraction('error_retry', 0, {
      errorType: ((_state$error = state.error) == null ? void 0 : _state$error.name) || 'Unknown',
      errorMessage: ((_state$error2 = state.error) == null ? void 0 : _state$error2.message) || 'Unknown error',
      retryCount: state.retryCount + 1,
      retryDelay: currentRetryDelay
    });
    if (onRetry) {
      onRetry(state.retryCount + 1);
    }
    yield new Promise(function (resolve) {
      retryTimeoutRef.current = setTimeout(function () {
        if (componentMountedRef.current) {
          resolve();
        }
      }, currentRetryDelay);
    });
    if (componentMountedRef.current) {
      clearError();
    }
  }), [state.isError, state.retryCount, state.error, maxRetries, progressiveRetryDelay, retryDelay, onRetry, clearError]);
  var getErrorMessage = (0, _react.useCallback)(function () {
    if (!state.error) return '';
    if (isNetworkError()) {
      return 'Unable to connect to the server. Please check your internet connection and try again.';
    }
    if (isServerError()) {
      return 'Our servers are experiencing issues. Please try again later.';
    }
    if (isAuthError()) {
      return 'Your session has expired. Please sign in again.';
    }
    return state.error.message || 'An unexpected error occurred. Please try again.';
  }, [state.error]);
  var isNetworkError = (0, _react.useCallback)(function () {
    if (!state.error) return false;
    var errorMessage = state.error.message.toLowerCase();
    return errorMessage.includes('network') || errorMessage.includes('connection') || errorMessage.includes('offline') || errorMessage.includes('internet') || errorMessage.includes('timeout') || errorMessage.includes('abort');
  }, [state.error]);
  var isServerError = (0, _react.useCallback)(function () {
    if (!state.error) return false;
    var errorMessage = state.error.message.toLowerCase();
    return errorMessage.includes('500') || errorMessage.includes('502') || errorMessage.includes('503') || errorMessage.includes('504') || errorMessage.includes('server error') || errorMessage.includes('internal server');
  }, [state.error]);
  var isAuthError = (0, _react.useCallback)(function () {
    if (!state.error) return false;
    var errorMessage = state.error.message.toLowerCase();
    return errorMessage.includes('401') || errorMessage.includes('403') || errorMessage.includes('unauthorized') || errorMessage.includes('forbidden') || errorMessage.includes('authentication') || errorMessage.includes('not authenticated') || errorMessage.includes('token') || errorMessage.includes('session expired');
  }, [state.error]);
  return {
    error: state.error,
    isError: state.isError,
    retryCount: state.retryCount,
    canRetry: state.canRetry,
    handleError: handleError,
    clearError: clearError,
    retry: retry,
    getErrorMessage: getErrorMessage,
    isNetworkError: isNetworkError,
    isServerError: isServerError,
    isAuthError: isAuthError
  };
};
var _default = exports.default = useErrorHandling;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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