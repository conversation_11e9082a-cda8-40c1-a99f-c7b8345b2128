{"version": 3, "names": ["_react", "_interopRequireWildcard", "require", "_reactNative", "_vectorIcons", "_ThemeContext", "_performanceMonitor", "_NetworkErrorFallback", "_interopRequireDefault", "_jsxRuntime", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "DataLoadingFallback", "exports", "_ref", "isLoading", "isError", "_ref$isEmpty", "isEmpty", "error", "_ref$retryCount", "retryCount", "_ref$maxRetries", "maxRetries", "_ref$loadingMessage", "loadingMessage", "_ref$emptyMessage", "emptyMessage", "_ref$emptyIcon", "emptyIcon", "onRetry", "onEmptyAction", "_ref$emptyActionText", "emptyActionText", "loadingComponent", "errorComponent", "emptyComponent", "_ref$loading<PERSON>elay", "loadingDelay", "_ref$showLoadingIndic", "showLoadingIndicator", "_ref$testID", "testID", "_useTheme", "useTheme", "colors", "useEffect", "startTime", "Date", "now", "loadTime", "performanceMonitor", "trackUserInteraction", "success", "handleRetry", "errorType", "name", "errorMessage", "message", "handleEmptyAction", "actionText", "jsx", "Fragment", "children", "jsxs", "View", "style", "styles", "container", "backgroundColor", "background", "primary", "accessibilityLabel", "accessibilityRole", "accessibilityState", "busy", "ActivityIndicator", "size", "color", "loadingIndicator", "Text", "loadingText", "text", "secondary", "_error$message", "_error$message2", "_error$message3", "includes", "iconContainer", "errorLight", "Ionicons", "errorTitle", "TouchableOpacity", "retryButton", "interactive", "onPress", "buttonIcon", "buttonText", "__DEV__", "debugContainer", "debugText", "tertiary", "stack", "emptyText", "emptyActionButton", "borderColor", "border", "StyleSheet", "create", "flex", "justifyContent", "alignItems", "padding", "marginBottom", "fontSize", "textAlign", "width", "height", "borderRadius", "fontWeight", "max<PERSON><PERSON><PERSON>", "lineHeight", "flexDirection", "paddingVertical", "paddingHorizontal", "marginRight", "borderWidth", "marginTop", "maxHeight", "overflow", "fontFamily", "_default"], "sources": ["DataLoadingFallback.tsx"], "sourcesContent": ["/**\n * Data Loading Fallback Component - Handles data loading states and errors\n *\n * Component Contract:\n * - Displays appropriate UI for loading, error, and empty states\n * - Provides retry functionality for failed data loads\n * - Supports skeleton loading placeholders\n * - Implements accessibility features\n * - Tracks loading performance metrics\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\nimport React, { useEffect } from 'react';\nimport { View, Text, StyleSheet, ActivityIndicator, TouchableOpacity } from 'react-native';\nimport { Ionicons } from '@expo/vector-icons';\nimport { useTheme } from '../../contexts/ThemeContext';\nimport { performanceMonitor } from '../../services/performanceMonitor';\nimport NetworkErrorFallback from './NetworkErrorFallback';\n\ninterface DataLoadingFallbackProps {\n  // State flags\n  isLoading: boolean;\n  isError: boolean;\n  isEmpty?: boolean;\n  \n  // Data and error info\n  error?: Error;\n  retryCount?: number;\n  maxRetries?: number;\n  \n  // Content\n  loadingMessage?: string;\n  emptyMessage?: string;\n  emptyIcon?: string;\n  \n  // Actions\n  onRetry?: () => void;\n  onEmptyAction?: () => void;\n  emptyActionText?: string;\n  \n  // Customization\n  loadingComponent?: React.ReactNode;\n  errorComponent?: React.ReactNode;\n  emptyComponent?: React.ReactNode;\n  \n  // Behavior\n  loadingDelay?: number;\n  showLoadingIndicator?: boolean;\n  \n  // Accessibility\n  testID?: string;\n}\n\nexport const DataLoadingFallback: React.FC<DataLoadingFallbackProps> = ({\n  // State flags\n  isLoading,\n  isError,\n  isEmpty = false,\n  \n  // Data and error info\n  error,\n  retryCount = 0,\n  maxRetries = 3,\n  \n  // Content\n  loadingMessage = 'Loading data...',\n  emptyMessage = 'No data available',\n  emptyIcon = 'document-outline',\n  \n  // Actions\n  onRetry,\n  onEmptyAction,\n  emptyActionText = 'Refresh',\n  \n  // Customization\n  loadingComponent,\n  errorComponent,\n  emptyComponent,\n  \n  // Behavior\n  loadingDelay = 300,\n  showLoadingIndicator = true,\n  \n  // Accessibility\n  testID = 'data-loading-fallback',\n}) => {\n  const { colors } = useTheme();\n  \n  // Track loading performance\n  useEffect(() => {\n    if (isLoading) {\n      const startTime = Date.now();\n      \n      return () => {\n        const loadTime = Date.now() - startTime;\n        \n        // Only track if loading took more than the delay\n        if (loadTime > loadingDelay) {\n          performanceMonitor.trackUserInteraction('data_loading', loadTime, {\n            success: !isError,\n            retryCount,\n          });\n        }\n      };\n    }\n  }, [isLoading, isError, loadingDelay, retryCount]);\n  \n  // Handle retry with performance tracking\n  const handleRetry = () => {\n    performanceMonitor.trackUserInteraction('data_loading_retry', 0, {\n      errorType: error?.name || 'DataLoadingError',\n      retryCount: retryCount + 1,\n      errorMessage: error?.message,\n    });\n    \n    onRetry?.();\n  };\n  \n  // Handle empty action\n  const handleEmptyAction = () => {\n    performanceMonitor.trackUserInteraction('empty_state_action', 0, {\n      actionText: emptyActionText,\n    });\n    \n    onEmptyAction?.();\n  };\n  \n  // Render loading state\n  if (isLoading) {\n    if (loadingComponent) {\n      return <>{loadingComponent}</>;\n    }\n    \n    return (\n      <View \n        style={[styles.container, { backgroundColor: colors.background.primary }]}\n        testID={`${testID}-loading`}\n        accessibilityLabel={loadingMessage}\n        accessibilityRole=\"progressbar\"\n        accessibilityState={{ busy: true }}\n      >\n        {showLoadingIndicator && (\n          <ActivityIndicator\n            size=\"large\"\n            color={colors.primary.default}\n            style={styles.loadingIndicator}\n          />\n        )}\n        <Text style={[styles.loadingText, { color: colors.text.secondary }]}>\n          {loadingMessage}\n        </Text>\n      </View>\n    );\n  }\n  \n  // Render error state\n  if (isError) {\n    if (errorComponent) {\n      return <>{errorComponent}</>;\n    }\n    \n    // Use NetworkErrorFallback for network errors\n    if (error?.name === 'NetworkError' || \n        error?.message?.includes('network') || \n        error?.message?.includes('connection') ||\n        error?.message?.includes('timeout')) {\n      return (\n        <NetworkErrorFallback\n          error={error}\n          onRetry={handleRetry}\n          retryCount={retryCount}\n          maxRetries={maxRetries}\n          testID={`${testID}-network-error`}\n        />\n      );\n    }\n    \n    // Generic error fallback\n    return (\n      <View \n        style={[styles.container, { backgroundColor: colors.background.primary }]}\n        testID={`${testID}-error`}\n        accessibilityLabel=\"Error loading data\"\n        accessibilityRole=\"alert\"\n      >\n        <View style={[styles.iconContainer, { backgroundColor: colors.errorLight }]}>\n          <Ionicons\n            name=\"alert-circle-outline\"\n            size={48}\n            color={colors.error}\n          />\n        </View>\n        <Text style={[styles.errorTitle, { color: colors.text.primary }]}>\n          Unable to Load Data\n        </Text>\n        <Text style={[styles.errorMessage, { color: colors.text.secondary }]}>\n          {error?.message || 'Something went wrong while loading the data.'}\n        </Text>\n        \n        {retryCount < maxRetries && (\n          <TouchableOpacity\n            style={[styles.retryButton, { backgroundColor: colors.interactive.primary.default }]}\n            onPress={handleRetry}\n            testID={`${testID}-retry-button`}\n            accessibilityRole=\"button\"\n            accessibilityLabel=\"Retry loading data\"\n          >\n            <Ionicons\n              name=\"refresh-outline\"\n              size={20}\n              color={colors.interactive.primary.text}\n              style={styles.buttonIcon}\n            />\n            <Text style={[styles.buttonText, { color: colors.interactive.primary.text }]}>\n              Try Again\n            </Text>\n          </TouchableOpacity>\n        )}\n        \n        {/* Show debug info in development */}\n        {__DEV__ && error && (\n          <View style={styles.debugContainer}>\n            <Text style={[styles.debugText, { color: colors.text.tertiary }]}>\n              {error.stack || error.message}\n            </Text>\n          </View>\n        )}\n      </View>\n    );\n  }\n  \n  // Render empty state\n  if (isEmpty) {\n    if (emptyComponent) {\n      return <>{emptyComponent}</>;\n    }\n    \n    return (\n      <View \n        style={[styles.container, { backgroundColor: colors.background.primary }]}\n        testID={`${testID}-empty`}\n        accessibilityLabel={emptyMessage}\n        accessibilityRole=\"text\"\n      >\n        <View style={[styles.iconContainer, { backgroundColor: colors.background.secondary }]}>\n          <Ionicons\n            name={emptyIcon as any}\n            size={48}\n            color={colors.text.secondary}\n          />\n        </View>\n        <Text style={[styles.emptyText, { color: colors.text.secondary }]}>\n          {emptyMessage}\n        </Text>\n        \n        {onEmptyAction && (\n          <TouchableOpacity\n            style={[styles.emptyActionButton, { \n              backgroundColor: colors.interactive.secondary.default,\n              borderColor: colors.interactive.secondary.border,\n            }]}\n            onPress={handleEmptyAction}\n            testID={`${testID}-empty-action-button`}\n            accessibilityRole=\"button\"\n            accessibilityLabel={emptyActionText}\n          >\n            <Text style={[styles.emptyActionText, { color: colors.interactive.secondary.text }]}>\n              {emptyActionText}\n            </Text>\n          </TouchableOpacity>\n        )}\n      </View>\n    );\n  }\n  \n  // If none of the states match, return null\n  return null;\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    justifyContent: 'center',\n    alignItems: 'center',\n    padding: 24,\n  },\n  loadingIndicator: {\n    marginBottom: 16,\n  },\n  loadingText: {\n    fontSize: 16,\n    textAlign: 'center',\n  },\n  iconContainer: {\n    width: 96,\n    height: 96,\n    borderRadius: 48,\n    justifyContent: 'center',\n    alignItems: 'center',\n    marginBottom: 16,\n  },\n  errorTitle: {\n    fontSize: 20,\n    fontWeight: '600',\n    marginBottom: 8,\n    textAlign: 'center',\n  },\n  errorMessage: {\n    fontSize: 16,\n    textAlign: 'center',\n    marginBottom: 24,\n    maxWidth: 300,\n    lineHeight: 22,\n  },\n  retryButton: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    justifyContent: 'center',\n    paddingVertical: 12,\n    paddingHorizontal: 24,\n    borderRadius: 8,\n  },\n  buttonIcon: {\n    marginRight: 8,\n  },\n  buttonText: {\n    fontSize: 16,\n    fontWeight: '500',\n  },\n  emptyText: {\n    fontSize: 16,\n    textAlign: 'center',\n    marginBottom: 24,\n    maxWidth: 300,\n    lineHeight: 22,\n  },\n  emptyActionButton: {\n    paddingVertical: 12,\n    paddingHorizontal: 24,\n    borderRadius: 8,\n    borderWidth: 1,\n  },\n  emptyActionText: {\n    fontSize: 16,\n    fontWeight: '500',\n  },\n  debugContainer: {\n    marginTop: 24,\n    padding: 12,\n    borderRadius: 8,\n    maxWidth: '100%',\n    maxHeight: 200,\n    overflow: 'scroll',\n  },\n  debugText: {\n    fontSize: 12,\n    fontFamily: 'monospace',\n  },\n});\n\nexport default DataLoadingFallback;\n"], "mappings": ";;;;;AAcA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AACA,IAAAE,YAAA,GAAAF,OAAA;AACA,IAAAG,aAAA,GAAAH,OAAA;AACA,IAAAI,mBAAA,GAAAJ,OAAA;AACA,IAAAK,qBAAA,GAAAC,sBAAA,CAAAN,OAAA;AAA0D,IAAAO,WAAA,GAAAP,OAAA;AAAA,SAAAD,wBAAAS,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAX,uBAAA,YAAAA,wBAAAS,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAoCnD,IAAMmB,mBAAuD,GAAAC,OAAA,CAAAD,mBAAA,GAAG,SAA1DA,mBAAuDA,CAAAE,IAAA,EAgC9D;EAAA,IA9BJC,SAAS,GAAAD,IAAA,CAATC,SAAS;IACTC,OAAO,GAAAF,IAAA,CAAPE,OAAO;IAAAC,YAAA,GAAAH,IAAA,CACPI,OAAO;IAAPA,OAAO,GAAAD,YAAA,cAAG,KAAK,GAAAA,YAAA;IAGfE,KAAK,GAAAL,IAAA,CAALK,KAAK;IAAAC,eAAA,GAAAN,IAAA,CACLO,UAAU;IAAVA,UAAU,GAAAD,eAAA,cAAG,CAAC,GAAAA,eAAA;IAAAE,eAAA,GAAAR,IAAA,CACdS,UAAU;IAAVA,UAAU,GAAAD,eAAA,cAAG,CAAC,GAAAA,eAAA;IAAAE,mBAAA,GAAAV,IAAA,CAGdW,cAAc;IAAdA,cAAc,GAAAD,mBAAA,cAAG,iBAAiB,GAAAA,mBAAA;IAAAE,iBAAA,GAAAZ,IAAA,CAClCa,YAAY;IAAZA,YAAY,GAAAD,iBAAA,cAAG,mBAAmB,GAAAA,iBAAA;IAAAE,cAAA,GAAAd,IAAA,CAClCe,SAAS;IAATA,SAAS,GAAAD,cAAA,cAAG,kBAAkB,GAAAA,cAAA;IAG9BE,OAAO,GAAAhB,IAAA,CAAPgB,OAAO;IACPC,aAAa,GAAAjB,IAAA,CAAbiB,aAAa;IAAAC,oBAAA,GAAAlB,IAAA,CACbmB,eAAe;IAAfA,eAAe,GAAAD,oBAAA,cAAG,SAAS,GAAAA,oBAAA;IAG3BE,gBAAgB,GAAApB,IAAA,CAAhBoB,gBAAgB;IAChBC,cAAc,GAAArB,IAAA,CAAdqB,cAAc;IACdC,cAAc,GAAAtB,IAAA,CAAdsB,cAAc;IAAAC,iBAAA,GAAAvB,IAAA,CAGdwB,YAAY;IAAZA,YAAY,GAAAD,iBAAA,cAAG,GAAG,GAAAA,iBAAA;IAAAE,qBAAA,GAAAzB,IAAA,CAClB0B,oBAAoB;IAApBA,oBAAoB,GAAAD,qBAAA,cAAG,IAAI,GAAAA,qBAAA;IAAAE,WAAA,GAAA3B,IAAA,CAG3B4B,MAAM;IAANA,MAAM,GAAAD,WAAA,cAAG,uBAAuB,GAAAA,WAAA;EAEhC,IAAAE,SAAA,GAAmB,IAAAC,sBAAQ,EAAC,CAAC;IAArBC,MAAM,GAAAF,SAAA,CAANE,MAAM;EAGd,IAAAC,gBAAS,EAAC,YAAM;IACd,IAAI/B,SAAS,EAAE;MACb,IAAMgC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;MAE5B,OAAO,YAAM;QACX,IAAMC,QAAQ,GAAGF,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,SAAS;QAGvC,IAAIG,QAAQ,GAAGZ,YAAY,EAAE;UAC3Ba,sCAAkB,CAACC,oBAAoB,CAAC,cAAc,EAAEF,QAAQ,EAAE;YAChEG,OAAO,EAAE,CAACrC,OAAO;YACjBK,UAAU,EAAVA;UACF,CAAC,CAAC;QACJ;MACF,CAAC;IACH;EACF,CAAC,EAAE,CAACN,SAAS,EAAEC,OAAO,EAAEsB,YAAY,EAAEjB,UAAU,CAAC,CAAC;EAGlD,IAAMiC,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;IACxBH,sCAAkB,CAACC,oBAAoB,CAAC,oBAAoB,EAAE,CAAC,EAAE;MAC/DG,SAAS,EAAE,CAAApC,KAAK,oBAALA,KAAK,CAAEqC,IAAI,KAAI,kBAAkB;MAC5CnC,UAAU,EAAEA,UAAU,GAAG,CAAC;MAC1BoC,YAAY,EAAEtC,KAAK,oBAALA,KAAK,CAAEuC;IACvB,CAAC,CAAC;IAEF5B,OAAO,YAAPA,OAAO,CAAG,CAAC;EACb,CAAC;EAGD,IAAM6B,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA,EAAS;IAC9BR,sCAAkB,CAACC,oBAAoB,CAAC,oBAAoB,EAAE,CAAC,EAAE;MAC/DQ,UAAU,EAAE3B;IACd,CAAC,CAAC;IAEFF,aAAa,YAAbA,aAAa,CAAG,CAAC;EACnB,CAAC;EAGD,IAAIhB,SAAS,EAAE;IACb,IAAImB,gBAAgB,EAAE;MACpB,OAAO,IAAA3C,WAAA,CAAAsE,GAAA,EAAAtE,WAAA,CAAAuE,QAAA;QAAAC,QAAA,EAAG7B;MAAgB,CAAG,CAAC;IAChC;IAEA,OACE,IAAA3C,WAAA,CAAAyE,IAAA,EAAC/E,YAAA,CAAAgF,IAAI;MACHC,KAAK,EAAE,CAACC,MAAM,CAACC,SAAS,EAAE;QAAEC,eAAe,EAAExB,MAAM,CAACyB,UAAU,CAACC;MAAQ,CAAC,CAAE;MAC1E7B,MAAM,EAAE,GAAGA,MAAM,UAAW;MAC5B8B,kBAAkB,EAAE/C,cAAe;MACnCgD,iBAAiB,EAAC,aAAa;MAC/BC,kBAAkB,EAAE;QAAEC,IAAI,EAAE;MAAK,CAAE;MAAAZ,QAAA,GAElCvB,oBAAoB,IACnB,IAAAjD,WAAA,CAAAsE,GAAA,EAAC5E,YAAA,CAAA2F,iBAAiB;QAChBC,IAAI,EAAC,OAAO;QACZC,KAAK,EAAEjC,MAAM,CAAC0B,OAAO,CAACrE,OAAQ;QAC9BgE,KAAK,EAAEC,MAAM,CAACY;MAAiB,CAChC,CACF,EACD,IAAAxF,WAAA,CAAAsE,GAAA,EAAC5E,YAAA,CAAA+F,IAAI;QAACd,KAAK,EAAE,CAACC,MAAM,CAACc,WAAW,EAAE;UAAEH,KAAK,EAAEjC,MAAM,CAACqC,IAAI,CAACC;QAAU,CAAC,CAAE;QAAApB,QAAA,EACjEtC;MAAc,CACX,CAAC;IAAA,CACH,CAAC;EAEX;EAGA,IAAIT,OAAO,EAAE;IAAA,IAAAoE,cAAA,EAAAC,eAAA,EAAAC,eAAA;IACX,IAAInD,cAAc,EAAE;MAClB,OAAO,IAAA5C,WAAA,CAAAsE,GAAA,EAAAtE,WAAA,CAAAuE,QAAA;QAAAC,QAAA,EAAG5B;MAAc,CAAG,CAAC;IAC9B;IAGA,IAAI,CAAAhB,KAAK,oBAALA,KAAK,CAAEqC,IAAI,MAAK,cAAc,IAC9BrC,KAAK,aAAAiE,cAAA,GAALjE,KAAK,CAAEuC,OAAO,aAAd0B,cAAA,CAAgBG,QAAQ,CAAC,SAAS,CAAC,IACnCpE,KAAK,aAAAkE,eAAA,GAALlE,KAAK,CAAEuC,OAAO,aAAd2B,eAAA,CAAgBE,QAAQ,CAAC,YAAY,CAAC,IACtCpE,KAAK,aAAAmE,eAAA,GAALnE,KAAK,CAAEuC,OAAO,aAAd4B,eAAA,CAAgBC,QAAQ,CAAC,SAAS,CAAC,EAAE;MACvC,OACE,IAAAhG,WAAA,CAAAsE,GAAA,EAACxE,qBAAA,CAAAa,OAAoB;QACnBiB,KAAK,EAAEA,KAAM;QACbW,OAAO,EAAEwB,WAAY;QACrBjC,UAAU,EAAEA,UAAW;QACvBE,UAAU,EAAEA,UAAW;QACvBmB,MAAM,EAAE,GAAGA,MAAM;MAAiB,CACnC,CAAC;IAEN;IAGA,OACE,IAAAnD,WAAA,CAAAyE,IAAA,EAAC/E,YAAA,CAAAgF,IAAI;MACHC,KAAK,EAAE,CAACC,MAAM,CAACC,SAAS,EAAE;QAAEC,eAAe,EAAExB,MAAM,CAACyB,UAAU,CAACC;MAAQ,CAAC,CAAE;MAC1E7B,MAAM,EAAE,GAAGA,MAAM,QAAS;MAC1B8B,kBAAkB,EAAC,oBAAoB;MACvCC,iBAAiB,EAAC,OAAO;MAAAV,QAAA,GAEzB,IAAAxE,WAAA,CAAAsE,GAAA,EAAC5E,YAAA,CAAAgF,IAAI;QAACC,KAAK,EAAE,CAACC,MAAM,CAACqB,aAAa,EAAE;UAAEnB,eAAe,EAAExB,MAAM,CAAC4C;QAAW,CAAC,CAAE;QAAA1B,QAAA,EAC1E,IAAAxE,WAAA,CAAAsE,GAAA,EAAC3E,YAAA,CAAAwG,QAAQ;UACPlC,IAAI,EAAC,sBAAsB;UAC3BqB,IAAI,EAAE,EAAG;UACTC,KAAK,EAAEjC,MAAM,CAAC1B;QAAM,CACrB;MAAC,CACE,CAAC,EACP,IAAA5B,WAAA,CAAAsE,GAAA,EAAC5E,YAAA,CAAA+F,IAAI;QAACd,KAAK,EAAE,CAACC,MAAM,CAACwB,UAAU,EAAE;UAAEb,KAAK,EAAEjC,MAAM,CAACqC,IAAI,CAACX;QAAQ,CAAC,CAAE;QAAAR,QAAA,EAAC;MAElE,CAAM,CAAC,EACP,IAAAxE,WAAA,CAAAsE,GAAA,EAAC5E,YAAA,CAAA+F,IAAI;QAACd,KAAK,EAAE,CAACC,MAAM,CAACV,YAAY,EAAE;UAAEqB,KAAK,EAAEjC,MAAM,CAACqC,IAAI,CAACC;QAAU,CAAC,CAAE;QAAApB,QAAA,EAClE,CAAA5C,KAAK,oBAALA,KAAK,CAAEuC,OAAO,KAAI;MAA8C,CAC7D,CAAC,EAENrC,UAAU,GAAGE,UAAU,IACtB,IAAAhC,WAAA,CAAAyE,IAAA,EAAC/E,YAAA,CAAA2G,gBAAgB;QACf1B,KAAK,EAAE,CAACC,MAAM,CAAC0B,WAAW,EAAE;UAAExB,eAAe,EAAExB,MAAM,CAACiD,WAAW,CAACvB,OAAO,CAACrE;QAAQ,CAAC,CAAE;QACrF6F,OAAO,EAAEzC,WAAY;QACrBZ,MAAM,EAAE,GAAGA,MAAM,eAAgB;QACjC+B,iBAAiB,EAAC,QAAQ;QAC1BD,kBAAkB,EAAC,oBAAoB;QAAAT,QAAA,GAEvC,IAAAxE,WAAA,CAAAsE,GAAA,EAAC3E,YAAA,CAAAwG,QAAQ;UACPlC,IAAI,EAAC,iBAAiB;UACtBqB,IAAI,EAAE,EAAG;UACTC,KAAK,EAAEjC,MAAM,CAACiD,WAAW,CAACvB,OAAO,CAACW,IAAK;UACvChB,KAAK,EAAEC,MAAM,CAAC6B;QAAW,CAC1B,CAAC,EACF,IAAAzG,WAAA,CAAAsE,GAAA,EAAC5E,YAAA,CAAA+F,IAAI;UAACd,KAAK,EAAE,CAACC,MAAM,CAAC8B,UAAU,EAAE;YAAEnB,KAAK,EAAEjC,MAAM,CAACiD,WAAW,CAACvB,OAAO,CAACW;UAAK,CAAC,CAAE;UAAAnB,QAAA,EAAC;QAE9E,CAAM,CAAC;MAAA,CACS,CACnB,EAGAmC,OAAO,IAAI/E,KAAK,IACf,IAAA5B,WAAA,CAAAsE,GAAA,EAAC5E,YAAA,CAAAgF,IAAI;QAACC,KAAK,EAAEC,MAAM,CAACgC,cAAe;QAAApC,QAAA,EACjC,IAAAxE,WAAA,CAAAsE,GAAA,EAAC5E,YAAA,CAAA+F,IAAI;UAACd,KAAK,EAAE,CAACC,MAAM,CAACiC,SAAS,EAAE;YAAEtB,KAAK,EAAEjC,MAAM,CAACqC,IAAI,CAACmB;UAAS,CAAC,CAAE;UAAAtC,QAAA,EAC9D5C,KAAK,CAACmF,KAAK,IAAInF,KAAK,CAACuC;QAAO,CACzB;MAAC,CACH,CACP;IAAA,CACG,CAAC;EAEX;EAGA,IAAIxC,OAAO,EAAE;IACX,IAAIkB,cAAc,EAAE;MAClB,OAAO,IAAA7C,WAAA,CAAAsE,GAAA,EAAAtE,WAAA,CAAAuE,QAAA;QAAAC,QAAA,EAAG3B;MAAc,CAAG,CAAC;IAC9B;IAEA,OACE,IAAA7C,WAAA,CAAAyE,IAAA,EAAC/E,YAAA,CAAAgF,IAAI;MACHC,KAAK,EAAE,CAACC,MAAM,CAACC,SAAS,EAAE;QAAEC,eAAe,EAAExB,MAAM,CAACyB,UAAU,CAACC;MAAQ,CAAC,CAAE;MAC1E7B,MAAM,EAAE,GAAGA,MAAM,QAAS;MAC1B8B,kBAAkB,EAAE7C,YAAa;MACjC8C,iBAAiB,EAAC,MAAM;MAAAV,QAAA,GAExB,IAAAxE,WAAA,CAAAsE,GAAA,EAAC5E,YAAA,CAAAgF,IAAI;QAACC,KAAK,EAAE,CAACC,MAAM,CAACqB,aAAa,EAAE;UAAEnB,eAAe,EAAExB,MAAM,CAACyB,UAAU,CAACa;QAAU,CAAC,CAAE;QAAApB,QAAA,EACpF,IAAAxE,WAAA,CAAAsE,GAAA,EAAC3E,YAAA,CAAAwG,QAAQ;UACPlC,IAAI,EAAE3B,SAAiB;UACvBgD,IAAI,EAAE,EAAG;UACTC,KAAK,EAAEjC,MAAM,CAACqC,IAAI,CAACC;QAAU,CAC9B;MAAC,CACE,CAAC,EACP,IAAA5F,WAAA,CAAAsE,GAAA,EAAC5E,YAAA,CAAA+F,IAAI;QAACd,KAAK,EAAE,CAACC,MAAM,CAACoC,SAAS,EAAE;UAAEzB,KAAK,EAAEjC,MAAM,CAACqC,IAAI,CAACC;QAAU,CAAC,CAAE;QAAApB,QAAA,EAC/DpC;MAAY,CACT,CAAC,EAENI,aAAa,IACZ,IAAAxC,WAAA,CAAAsE,GAAA,EAAC5E,YAAA,CAAA2G,gBAAgB;QACf1B,KAAK,EAAE,CAACC,MAAM,CAACqC,iBAAiB,EAAE;UAChCnC,eAAe,EAAExB,MAAM,CAACiD,WAAW,CAACX,SAAS,CAACjF,OAAO;UACrDuG,WAAW,EAAE5D,MAAM,CAACiD,WAAW,CAACX,SAAS,CAACuB;QAC5C,CAAC,CAAE;QACHX,OAAO,EAAEpC,iBAAkB;QAC3BjB,MAAM,EAAE,GAAGA,MAAM,sBAAuB;QACxC+B,iBAAiB,EAAC,QAAQ;QAC1BD,kBAAkB,EAAEvC,eAAgB;QAAA8B,QAAA,EAEpC,IAAAxE,WAAA,CAAAsE,GAAA,EAAC5E,YAAA,CAAA+F,IAAI;UAACd,KAAK,EAAE,CAACC,MAAM,CAAClC,eAAe,EAAE;YAAE6C,KAAK,EAAEjC,MAAM,CAACiD,WAAW,CAACX,SAAS,CAACD;UAAK,CAAC,CAAE;UAAAnB,QAAA,EACjF9B;QAAe,CACZ;MAAC,CACS,CACnB;IAAA,CACG,CAAC;EAEX;EAGA,OAAO,IAAI;AACb,CAAC;AAED,IAAMkC,MAAM,GAAGwC,uBAAU,CAACC,MAAM,CAAC;EAC/BxC,SAAS,EAAE;IACTyC,IAAI,EAAE,CAAC;IACPC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,QAAQ;IACpBC,OAAO,EAAE;EACX,CAAC;EACDjC,gBAAgB,EAAE;IAChBkC,YAAY,EAAE;EAChB,CAAC;EACDhC,WAAW,EAAE;IACXiC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE;EACb,CAAC;EACD3B,aAAa,EAAE;IACb4B,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,YAAY,EAAE,EAAE;IAChBR,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,QAAQ;IACpBE,YAAY,EAAE;EAChB,CAAC;EACDtB,UAAU,EAAE;IACVuB,QAAQ,EAAE,EAAE;IACZK,UAAU,EAAE,KAAK;IACjBN,YAAY,EAAE,CAAC;IACfE,SAAS,EAAE;EACb,CAAC;EACD1D,YAAY,EAAE;IACZyD,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,QAAQ;IACnBF,YAAY,EAAE,EAAE;IAChBO,QAAQ,EAAE,GAAG;IACbC,UAAU,EAAE;EACd,CAAC;EACD5B,WAAW,EAAE;IACX6B,aAAa,EAAE,KAAK;IACpBX,UAAU,EAAE,QAAQ;IACpBD,cAAc,EAAE,QAAQ;IACxBa,eAAe,EAAE,EAAE;IACnBC,iBAAiB,EAAE,EAAE;IACrBN,YAAY,EAAE;EAChB,CAAC;EACDtB,UAAU,EAAE;IACV6B,WAAW,EAAE;EACf,CAAC;EACD5B,UAAU,EAAE;IACViB,QAAQ,EAAE,EAAE;IACZK,UAAU,EAAE;EACd,CAAC;EACDhB,SAAS,EAAE;IACTW,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,QAAQ;IACnBF,YAAY,EAAE,EAAE;IAChBO,QAAQ,EAAE,GAAG;IACbC,UAAU,EAAE;EACd,CAAC;EACDjB,iBAAiB,EAAE;IACjBmB,eAAe,EAAE,EAAE;IACnBC,iBAAiB,EAAE,EAAE;IACrBN,YAAY,EAAE,CAAC;IACfQ,WAAW,EAAE;EACf,CAAC;EACD7F,eAAe,EAAE;IACfiF,QAAQ,EAAE,EAAE;IACZK,UAAU,EAAE;EACd,CAAC;EACDpB,cAAc,EAAE;IACd4B,SAAS,EAAE,EAAE;IACbf,OAAO,EAAE,EAAE;IACXM,YAAY,EAAE,CAAC;IACfE,QAAQ,EAAE,MAAM;IAChBQ,SAAS,EAAE,GAAG;IACdC,QAAQ,EAAE;EACZ,CAAC;EACD7B,SAAS,EAAE;IACTc,QAAQ,EAAE,EAAE;IACZgB,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AAAC,IAAAC,QAAA,GAAAtH,OAAA,CAAAX,OAAA,GAEYU,mBAAmB", "ignoreList": []}