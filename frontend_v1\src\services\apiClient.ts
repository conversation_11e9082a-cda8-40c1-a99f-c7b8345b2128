/**
 * API Client - HTTP Client for Backend Communication
 *
 * Component Contract:
 * - Provides centralized HTTP client for all API calls
 * - Handles authentication token management
 * - Implements request/response interceptors
 * - Provides error handling and retry logic
 * - Supports request cancellation and timeouts
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import { useAuthStore } from '../store/authSlice';

// Configuration
const getApiBaseUrl = () => {
  if (!__DEV__) {
    return 'https://api.vierla.com';
  }

  // In development, handle Android emulator networking
  if (Platform.OS === 'android') {
    // Android emulator special networking - use ******** to access host machine
    return 'http://********:8000';
  } else {
    // iOS simulator and physical devices can use network IP
    return 'http://************:8000';
  }
};

const API_BASE_URL = getApiBaseUrl();
const DEFAULT_TIMEOUT = 10000; // 10 seconds

// Types
export interface ApiResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
}

export interface ApiError {
  message: string;
  status: number;
  details?: any;
}

export interface ApiProgress {
  loaded: number;
  total: number;
  percentage: number;
}

export interface RequestConfig {
  method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  url: string;
  data?: any;
  params?: Record<string, any>;
  headers?: Record<string, string>;
  timeout?: number;
  requiresAuth?: boolean;
  onProgress?: (progress: ApiProgress) => void;
  onStatusUpdate?: (status: string) => void;
}

class ApiClient {
  private baseURL: string;
  private defaultTimeout: number;
  private authToken: string | null = null;

  constructor(baseURL: string = API_BASE_URL, timeout: number = DEFAULT_TIMEOUT) {
    this.baseURL = baseURL;
    this.defaultTimeout = timeout;
    this.loadAuthToken();
  }

  /**
   * Load authentication token from storage
   */
  private async loadAuthToken(): Promise<void> {
    try {
      const token = await AsyncStorage.getItem('auth_token');
      this.authToken = token;
    } catch (error) {
      console.warn('Failed to load auth token:', error);
    }
  }

  /**
   * Set authentication token
   */
  public setAuthToken(token: string | null): void {
    this.authToken = token;
    if (token) {
      AsyncStorage.setItem('auth_token', token);
    } else {
      AsyncStorage.removeItem('auth_token');
    }
  }

  /**
   * Refresh authentication token
   */
  private async refreshAuthToken(): Promise<void> {
    try {
      // Get refresh token from AsyncStorage
      const refreshToken = await AsyncStorage.getItem('refresh_token');

      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      // Make refresh request without auth header
      const refreshResponse = await fetch(`${this.baseURL}/api/auth/token/refresh/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({ refresh: refreshToken }),
      });

      if (!refreshResponse.ok) {
        const errorData = await refreshResponse.json();
        throw new Error(errorData.detail || 'Token refresh failed');
      }

      const refreshData = await refreshResponse.json();

      // Update tokens
      if (refreshData.access) {
        this.setAuthToken(refreshData.access);

        // Update auth store
        const authState = useAuthStore.getState();
        authState.loginSuccess(refreshData.access, authState.userRole || 'customer');

        console.log('✅ API: Token refreshed successfully');
      }

      // Store new refresh token if provided
      if (refreshData.refresh) {
        await AsyncStorage.setItem('refresh_token', refreshData.refresh);
      }

    } catch (error: any) {
      console.error('❌ API: Token refresh failed:', error.message);

      // Clear invalid tokens
      this.setAuthToken(null);
      await AsyncStorage.removeItem('refresh_token');

      // Update auth store to logged out state
      const authState = useAuthStore.getState();
      authState.logout();

      throw error;
    }
  }

  /**
   * Build full URL with query parameters
   */
  private buildUrl(url: string, params?: Record<string, any>): string {
    const fullUrl = url.startsWith('http') ? url : `${this.baseURL}${url}`;
    
    if (!params || Object.keys(params).length === 0) {
      return fullUrl;
    }

    const urlParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        urlParams.append(key, String(value));
      }
    });

    const separator = fullUrl.includes('?') ? '&' : '?';
    return `${fullUrl}${separator}${urlParams.toString()}`;
  }

  /**
   * Build request headers
   */
  private buildHeaders(customHeaders?: Record<string, string>, requiresAuth: boolean = true): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...customHeaders,
    };

    if (requiresAuth) {
      // Get token from auth store first, fallback to instance token
      const authState = useAuthStore.getState();
      const token = authState.authToken || this.authToken;

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
    }

    return headers;
  }

  /**
   * Make HTTP request
   */
  private async makeRequest<T>(config: RequestConfig): Promise<ApiResponse<T>> {
    const {
      method,
      url,
      data,
      params,
      headers: customHeaders,
      timeout = this.defaultTimeout,
      requiresAuth = true,
      onProgress,
      onStatusUpdate,
    } = config;

    const fullUrl = this.buildUrl(url, params);
    const headers = this.buildHeaders(customHeaders, requiresAuth);

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      // Notify request start
      onStatusUpdate?.('Preparing request...');

      const requestInit: RequestInit = {
        method,
        headers,
        signal: controller.signal,
      };

      if (data && method !== 'GET') {
        requestInit.body = JSON.stringify(data);
      }

      // Notify request sending
      onStatusUpdate?.('Sending request...');
      onProgress?.({ loaded: 0, total: 100, percentage: 0 });

      const response = await fetch(fullUrl, requestInit);
      clearTimeout(timeoutId);

      // Notify response received
      onStatusUpdate?.('Processing response...');
      onProgress?.({ loaded: 50, total: 100, percentage: 50 });

      let responseData: T;
      const contentType = response.headers.get('content-type');

      // Update progress for response parsing
      onProgress?.({ loaded: 75, total: 100, percentage: 75 });

      if (contentType && contentType.includes('application/json')) {
        responseData = await response.json();
      } else {
        responseData = (await response.text()) as unknown as T;
      }

      if (!response.ok) {
        onStatusUpdate?.('Request failed');

        // Handle authentication errors with token refresh
        if (response.status === 401 && requiresAuth) {
          console.warn('🔐 API: Authentication failed, attempting token refresh...');

          try {
            // Attempt to refresh token
            await this.refreshAuthToken();

            // Retry the original request with new token
            console.log('🔄 API: Retrying request with refreshed token...');
            const retryHeaders = this.buildHeaders(customHeaders, requiresAuth);
            const retryResponse = await fetch(fullUrl, {
              ...requestInit,
              headers: retryHeaders,
            });

            if (retryResponse.ok) {
              const retryData = await retryResponse.json();
              console.log('✅ API: Request succeeded after token refresh');
              return {
                data: retryData,
                status: retryResponse.status,
                statusText: retryResponse.statusText,
                headers: Object.fromEntries(retryResponse.headers.entries()),
              };
            }
          } catch (refreshError) {
            console.error('❌ API: Token refresh failed:', refreshError);
            // Fall through to throw original 401 error
          }
        }

        throw {
          message: responseData || response.statusText,
          status: response.status,
          details: responseData,
        } as ApiError;
      }

      // Complete progress
      onStatusUpdate?.('Request completed');
      onProgress?.({ loaded: 100, total: 100, percentage: 100 });

      return {
        data: responseData,
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
      };
    } catch (error: any) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        throw {
          message: 'Request timeout',
          status: 408,
        } as ApiError;
      }

      if (error.status) {
        throw error as ApiError;
      }

      throw {
        message: error.message || 'Network error',
        status: 0,
        details: error,
      } as ApiError;
    }
  }

  /**
   * GET request
   */
  async get<T>(url: string, params?: Record<string, any>, requiresAuth: boolean = true): Promise<ApiResponse<T>> {
    return this.makeRequest<T>({ method: 'GET', url, params, requiresAuth });
  }

  /**
   * POST request
   */
  async post<T>(url: string, data?: any, requiresAuth: boolean = true): Promise<ApiResponse<T>> {
    return this.makeRequest<T>({ method: 'POST', url, data, requiresAuth });
  }

  /**
   * PUT request
   */
  async put<T>(url: string, data?: any, requiresAuth: boolean = true): Promise<ApiResponse<T>> {
    return this.makeRequest<T>({ method: 'PUT', url, data, requiresAuth });
  }

  /**
   * PATCH request
   */
  async patch<T>(url: string, data?: any, requiresAuth: boolean = true): Promise<ApiResponse<T>> {
    return this.makeRequest<T>({ method: 'PATCH', url, data, requiresAuth });
  }

  /**
   * DELETE request
   */
  async delete<T>(url: string, requiresAuth: boolean = true): Promise<ApiResponse<T>> {
    return this.makeRequest<T>({ method: 'DELETE', url, requiresAuth });
  }
}

// Create and export singleton instance
export const apiClient = new ApiClient();
export default apiClient;
