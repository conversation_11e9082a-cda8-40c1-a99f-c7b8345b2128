7c5bb91c2b140dc5f05644e9b778201a
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _get2 = _interopRequireDefault(require("@babel/runtime/helpers/get"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _Systrace = require("../Performance/Systrace");
var _EventEmitter2 = _interopRequireDefault(require("../vendor/emitter/EventEmitter"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && "function" == typeof p ? function (t) { return p.apply(e, t); } : p; }
var RCTDeviceEventEmitterImpl = function (_EventEmitter) {
  function RCTDeviceEventEmitterImpl() {
    (0, _classCallCheck2.default)(this, RCTDeviceEventEmitterImpl);
    return _callSuper(this, RCTDeviceEventEmitterImpl, arguments);
  }
  (0, _inherits2.default)(RCTDeviceEventEmitterImpl, _EventEmitter);
  return (0, _createClass2.default)(RCTDeviceEventEmitterImpl, [{
    key: "emit",
    value: function emit(eventType) {
      (0, _Systrace.beginEvent)(function () {
        return `RCTDeviceEventEmitter.emit#${eventType}`;
      });
      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
        args[_key - 1] = arguments[_key];
      }
      _superPropGet(RCTDeviceEventEmitterImpl, "emit", this, 3)([eventType].concat(args));
      (0, _Systrace.endEvent)();
    }
  }]);
}(_EventEmitter2.default);
var RCTDeviceEventEmitter = new RCTDeviceEventEmitterImpl();
Object.defineProperty(global, '__rctDeviceEventEmitter', {
  configurable: true,
  value: RCTDeviceEventEmitter
});
var _default = exports.default = RCTDeviceEventEmitter;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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