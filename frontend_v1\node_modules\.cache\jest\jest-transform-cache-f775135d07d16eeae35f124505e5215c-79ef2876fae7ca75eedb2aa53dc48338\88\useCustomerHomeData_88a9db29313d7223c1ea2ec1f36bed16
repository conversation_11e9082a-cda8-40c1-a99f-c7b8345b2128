f1168f2972e587f45e92f7b39b01cd81
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useCustomerHomeData = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _react = require("react");
var _customerService = _interopRequireDefault(require("../services/customerService"));
var _authSlice = require("../store/authSlice");
var _cacheService = _interopRequireDefault(require("../services/cacheService"));
var _performanceMonitor = require("../services/performanceMonitor");
var initialData = {
  categories: [],
  featuredProviders: [],
  favoriteProviders: [],
  nearbyProviders: [],
  dashboard: null,
  recommendations: []
};
var initialLoading = {
  categories: false,
  featuredProviders: false,
  favoriteProviders: false,
  nearbyProviders: false,
  dashboard: false,
  recommendations: false,
  overall: false
};
var initialError = {
  categories: null,
  featuredProviders: null,
  favoriteProviders: null,
  nearbyProviders: null,
  dashboard: null,
  recommendations: null,
  overall: null
};
var useCustomerHomeData = exports.useCustomerHomeData = function useCustomerHomeData() {
  var _useAuthStore = (0, _authSlice.useAuthStore)(),
    isAuthenticated = _useAuthStore.isAuthenticated,
    user = _useAuthStore.user;
  var _useState = (0, _react.useState)({
      data: initialData,
      loading: initialLoading,
      error: initialError,
      lastUpdated: null,
      refreshing: false
    }),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    state = _useState2[0],
    setState = _useState2[1];
  var abortControllerRef = (0, _react.useRef)(null);
  var cacheRef = (0, _react.useRef)({});
  var CACHE_DURATION = 5 * 60 * 1000;
  var isCacheValid = (0, _react.useCallback)(function (key) {
    var cached = cacheRef.current[key];
    if (!cached) return false;
    return Date.now() - cached.timestamp < CACHE_DURATION;
  }, []);
  var getCachedData = (0, _react.useCallback)(function (key) {
    var cached = cacheRef.current[key];
    return cached ? cached.data : null;
  }, []);
  var setCache = (0, _react.useCallback)(function (key, data) {
    cacheRef.current[key] = {
      data: data,
      timestamp: Date.now()
    };
  }, []);
  var setLoading = (0, _react.useCallback)(function (key, value) {
    setState(function (prev) {
      return Object.assign({}, prev, {
        loading: Object.assign({}, prev.loading, (0, _defineProperty2.default)((0, _defineProperty2.default)({}, key, value), "overall", value || Object.values(Object.assign({}, prev.loading, (0, _defineProperty2.default)({}, key, value))).some(Boolean)))
      });
    });
  }, []);
  var setError = (0, _react.useCallback)(function (key, value) {
    setState(function (prev) {
      return Object.assign({}, prev, {
        error: Object.assign({}, prev.error, (0, _defineProperty2.default)({}, key, value))
      });
    });
  }, []);
  var setData = (0, _react.useCallback)(function (key, value) {
    setState(function (prev) {
      return Object.assign({}, prev, {
        data: Object.assign({}, prev.data, (0, _defineProperty2.default)({}, key, value)),
        lastUpdated: new Date()
      });
    });
  }, []);
  var fetchCategories = (0, _react.useCallback)((0, _asyncToGenerator2.default)(function* () {
    var cacheKey = 'customer_home_categories';
    var startTime = Date.now();
    setLoading('categories', true);
    setError('categories', null);
    try {
      var cachedData = yield _cacheService.default.get(cacheKey);
      if (cachedData) {
        setData('categories', cachedData);
        setLoading('categories', false);
        _performanceMonitor.performanceMonitor.trackNetworkRequest('/api/v1/catalog/categories/', 'GET', Date.now() - startTime, 200, 0, 0, true);
        return;
      }
      var categories = yield _customerService.default.getServiceCategories();
      _performanceMonitor.performanceMonitor.trackNetworkRequest('/api/v1/catalog/categories/', 'GET', Date.now() - startTime, 200, 0, 0, false);
      yield _cacheService.default.set(cacheKey, categories, 5 * 60 * 1000);
      setData('categories', categories);
    } catch (error) {
      console.error('Failed to fetch categories:', error);
      setError('categories', error.message || 'Failed to load categories');
      _performanceMonitor.performanceMonitor.trackNetworkRequest('/api/v1/catalog/categories/', 'GET', Date.now() - startTime, error.status || 500, 0, 0, false);
    } finally {
      setLoading('categories', false);
    }
  }), [setData, setLoading, setError]);
  var fetchFeaturedProviders = (0, _react.useCallback)((0, _asyncToGenerator2.default)(function* () {
    var cacheKey = 'customer_home_featured_providers';
    var startTime = Date.now();
    setLoading('featuredProviders', true);
    setError('featuredProviders', null);
    try {
      var cachedData = yield _cacheService.default.get(cacheKey);
      if (cachedData) {
        setData('featuredProviders', cachedData);
        setLoading('featuredProviders', false);
        _performanceMonitor.performanceMonitor.trackNetworkRequest('/api/v1/catalog/providers/featured/', 'GET', Date.now() - startTime, 200, 0, 0, true);
        return;
      }
      var providers = yield _customerService.default.getFeaturedProviders(10);
      _performanceMonitor.performanceMonitor.trackNetworkRequest('/api/v1/catalog/providers/featured/', 'GET', Date.now() - startTime, 200, 0, 0, false);
      yield _cacheService.default.set(cacheKey, providers, 10 * 60 * 1000);
      setData('featuredProviders', providers);
    } catch (error) {
      console.error('Failed to fetch featured providers:', error);
      setError('featuredProviders', error.message || 'Failed to load featured providers');
      _performanceMonitor.performanceMonitor.trackNetworkRequest('/api/v1/catalog/providers/featured/', 'GET', Date.now() - startTime, error.status || 500, 0, 0, false);
    } finally {
      setLoading('featuredProviders', false);
    }
  }), [setData, setLoading, setError]);
  var fetchFavoriteProviders = (0, _react.useCallback)((0, _asyncToGenerator2.default)(function* () {
    if (!isAuthenticated) return;
    var cacheKey = 'favoriteProviders';
    if (isCacheValid(cacheKey)) {
      setData('favoriteProviders', getCachedData(cacheKey));
      return;
    }
    setLoading('favoriteProviders', true);
    setError('favoriteProviders', null);
    try {
      var providers = yield _customerService.default.getFavoriteProviders();
      setData('favoriteProviders', providers);
      setCache(cacheKey, providers);
    } catch (error) {
      console.error('Failed to fetch favorite providers:', error);
      setError('favoriteProviders', error.message || 'Failed to load favorite providers');
    } finally {
      setLoading('favoriteProviders', false);
    }
  }), [isAuthenticated, isCacheValid, getCachedData, setData, setCache, setLoading, setError]);
  var fetchNearbyProviders = (0, _react.useCallback)((0, _asyncToGenerator2.default)(function* () {
    var defaultLat = 45.4215;
    var defaultLng = -75.6972;
    var cacheKey = 'nearbyProviders';
    if (isCacheValid(cacheKey)) {
      setData('nearbyProviders', getCachedData(cacheKey));
      return;
    }
    setLoading('nearbyProviders', true);
    setError('nearbyProviders', null);
    try {
      var providers = yield _customerService.default.getNearbyProviders(defaultLat, defaultLng, 10, 10);
      setData('nearbyProviders', providers);
      setCache(cacheKey, providers);
    } catch (error) {
      console.error('Failed to fetch nearby providers:', error);
      setError('nearbyProviders', error.message || 'Failed to load nearby providers');
    } finally {
      setLoading('nearbyProviders', false);
    }
  }), [isCacheValid, getCachedData, setData, setCache, setLoading, setError]);
  var fetchDashboard = (0, _react.useCallback)((0, _asyncToGenerator2.default)(function* () {
    if (!isAuthenticated) return;
    var cacheKey = 'dashboard';
    if (isCacheValid(cacheKey)) {
      setData('dashboard', getCachedData(cacheKey));
      return;
    }
    setLoading('dashboard', true);
    setError('dashboard', null);
    try {
      var dashboard = yield _customerService.default.getCustomerDashboard();
      setData('dashboard', dashboard);
      setCache(cacheKey, dashboard);
    } catch (error) {
      console.error('Failed to fetch dashboard:', error);
      setError('dashboard', error.message || 'Failed to load dashboard');
    } finally {
      setLoading('dashboard', false);
    }
  }), [isAuthenticated, isCacheValid, getCachedData, setData, setCache, setLoading, setError]);
  var fetchRecommendations = (0, _react.useCallback)((0, _asyncToGenerator2.default)(function* () {
    if (!isAuthenticated) return;
    var cacheKey = 'recommendations';
    if (isCacheValid(cacheKey)) {
      setData('recommendations', getCachedData(cacheKey));
      return;
    }
    setLoading('recommendations', true);
    setError('recommendations', null);
    try {
      var recommendations = yield _customerService.default.getPersonalizedRecommendations();
      setData('recommendations', recommendations);
      setCache(cacheKey, recommendations);
    } catch (error) {
      console.error('Failed to fetch recommendations:', error);
      setError('recommendations', error.message || 'Failed to load recommendations');
    } finally {
      setLoading('recommendations', false);
    }
  }), [isAuthenticated, isCacheValid, getCachedData, setData, setCache, setLoading, setError]);
  var loadAllData = (0, _react.useCallback)((0, _asyncToGenerator2.default)(function* () {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    abortControllerRef.current = new AbortController();
    yield Promise.allSettled([fetchCategories(), fetchFeaturedProviders(), fetchFavoriteProviders(), fetchNearbyProviders(), fetchDashboard(), fetchRecommendations()]);
  }), [fetchCategories, fetchFeaturedProviders, fetchFavoriteProviders, fetchNearbyProviders, fetchDashboard, fetchRecommendations]);
  var refresh = (0, _react.useCallback)((0, _asyncToGenerator2.default)(function* () {
    setState(function (prev) {
      return Object.assign({}, prev, {
        refreshing: true
      });
    });
    cacheRef.current = {};
    try {
      yield loadAllData();
    } finally {
      setState(function (prev) {
        return Object.assign({}, prev, {
          refreshing: false
        });
      });
    }
  }), [loadAllData]);
  (0, _react.useEffect)(function () {
    loadAllData();
    return function () {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [loadAllData]);
  return Object.assign({}, state, {
    refresh: refresh,
    loadAllData: loadAllData,
    fetchCategories: fetchCategories,
    fetchFeaturedProviders: fetchFeaturedProviders,
    fetchFavoriteProviders: fetchFavoriteProviders,
    fetchNearbyProviders: fetchNearbyProviders,
    fetchDashboard: fetchDashboard,
    fetchRecommendations: fetchRecommendations
  });
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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