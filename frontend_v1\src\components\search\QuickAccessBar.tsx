/**
 * Quick Access Bar Component
 *
 * Component Contract:
 * - Provides quick access to frequently used searches and actions
 * - Shows favorite providers, recent bookings, and saved searches
 * - Implements power user features for efficiency
 * - Supports customization and personalization
 * - Follows accessibility guidelines
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { useTheme } from '../../contexts/ThemeContext';
import { usePerformance } from '../../hooks/usePerformance';
import { useAuthStore } from '../../store/authSlice';
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
} from '../../utils/responsiveUtils';

interface QuickAccessItem {
  id: string;
  title: string;
  subtitle?: string;
  icon: string;
  type: 'favorite_provider' | 'recent_booking' | 'saved_search' | 'quick_action';
  action: () => void;
  badge?: string;
}

interface QuickAccessBarProps {
  onFavoriteProviderPress: (providerId: string) => void;
  onRecentBookingPress: (bookingId: string) => void;
  onSavedSearchPress: (searchQuery: string) => void;
  onQuickActionPress: (actionType: string) => void;
  visible?: boolean;
}

export const QuickAccessBar: React.FC<QuickAccessBarProps> = ({
  onFavoriteProviderPress,
  onRecentBookingPress,
  onSavedSearchPress,
  onQuickActionPress,
  visible = true,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  const { user } = useAuthStore();
  const [quickAccessItems, setQuickAccessItems] = useState<QuickAccessItem[]>([]);

  const { trackInteraction } = usePerformance({
    componentName: 'QuickAccessBar',
    trackInteractions: true,
  });

  // Generate quick access items based on user data
  const generateQuickAccessItems = useCallback(() => {
    const items: QuickAccessItem[] = [];

    // Add favorite providers (mock data for now)
    const favoriteProviders = [
      { id: '1', name: 'Bella Beauty Salon', category: 'Hair' },
      { id: '2', name: 'Nail Art Studio', category: 'Nails' },
      { id: '3', name: 'Zen Spa', category: 'Massage' },
    ];

    favoriteProviders.slice(0, 2).forEach((provider) => {
      items.push({
        id: `fav_${provider.id}`,
        title: provider.name,
        subtitle: provider.category,
        icon: 'heart',
        type: 'favorite_provider',
        action: () => onFavoriteProviderPress(provider.id),
      });
    });

    // Add recent bookings
    const recentBookings = [
      { id: '1', service: 'Hair Cut', provider: 'Bella Beauty', date: '2 days ago' },
      { id: '2', service: 'Manicure', provider: 'Nail Art', date: '1 week ago' },
    ];

    recentBookings.slice(0, 1).forEach((booking) => {
      items.push({
        id: `recent_${booking.id}`,
        title: `Rebook ${booking.service}`,
        subtitle: booking.provider,
        icon: 'refresh',
        type: 'recent_booking',
        action: () => onRecentBookingPress(booking.id),
        badge: 'Rebook',
      });
    });

    // Add saved searches
    const savedSearches = [
      'Hair salons near me',
      'Nail services downtown',
      'Massage therapy',
    ];

    savedSearches.slice(0, 1).forEach((search, index) => {
      items.push({
        id: `saved_${index}`,
        title: search,
        subtitle: 'Saved search',
        icon: 'bookmark',
        type: 'saved_search',
        action: () => onSavedSearchPress(search),
      });
    });

    // Add quick actions
    const quickActions = [
      { id: 'emergency', title: 'Emergency Booking', icon: 'flash', subtitle: 'Same day' },
      { id: 'gift_card', title: 'Gift Cards', icon: 'gift', subtitle: 'Buy & send' },
    ];

    quickActions.slice(0, 1).forEach((action) => {
      items.push({
        id: `action_${action.id}`,
        title: action.title,
        subtitle: action.subtitle,
        icon: action.icon,
        type: 'quick_action',
        action: () => onQuickActionPress(action.id),
      });
    });

    setQuickAccessItems(items);
  }, [onFavoriteProviderPress, onRecentBookingPress, onSavedSearchPress, onQuickActionPress]);

  useEffect(() => {
    generateQuickAccessItems();
  }, [generateQuickAccessItems]);

  const handleItemPress = useCallback((item: QuickAccessItem) => {
    trackInteraction('quickAccessItemPress', { type: item.type, title: item.title });
    item.action();
  }, [trackInteraction]);

  const renderQuickAccessItem = (item: QuickAccessItem) => (
    <TouchableOpacity
      key={item.id}
      style={styles.quickAccessItem}
      onPress={() => handleItemPress(item)}
      accessibilityRole="button"
      accessibilityLabel={`${item.title}${item.subtitle ? `, ${item.subtitle}` : ''}`}
      accessibilityHint={`Tap to ${item.title.toLowerCase()}`}
    >
      <View style={styles.itemIconContainer}>
        <Ionicons
          name={item.icon as any}
          size={20}
          color={colors.sage600}
        />
        {item.badge && (
          <View style={styles.badge}>
            <Text style={styles.badgeText}>{item.badge}</Text>
          </View>
        )}
      </View>
      <View style={styles.itemContent}>
        <Text style={styles.itemTitle} numberOfLines={1}>
          {item.title}
        </Text>
        {item.subtitle && (
          <Text style={styles.itemSubtitle} numberOfLines={1}>
            {item.subtitle}
          </Text>
        )}
      </View>
    </TouchableOpacity>
  );

  if (!visible || quickAccessItems.length === 0) {
    return null;
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerText}>Quick Access</Text>
        <TouchableOpacity
          style={styles.customizeButton}
          onPress={() => onQuickActionPress('customize')}
          accessibilityRole="button"
          accessibilityLabel="Customize quick access"
        >
          <Ionicons name="settings-outline" size={16} color={colors.text.secondary} />
        </TouchableOpacity>
      </View>
      
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        style={styles.scrollView}
      >
        {quickAccessItems.map(renderQuickAccessItem)}
      </ScrollView>
    </View>
  );
};

// Styles
const createStyles = (colors: any) => ({
  container: {
    backgroundColor: colors.background.primary,
    borderRadius: 12,
    marginVertical: getResponsiveSpacing(8),
    shadowColor: colors.shadow.default,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: getResponsiveSpacing(16),
    paddingTop: getResponsiveSpacing(12),
    paddingBottom: getResponsiveSpacing(8),
  },
  headerText: {
    fontSize: getResponsiveFontSize(14),
    fontWeight: '600',
    color: colors.text.primary,
  },
  customizeButton: {
    padding: getResponsiveSpacing(4),
  },
  scrollView: {
    paddingBottom: getResponsiveSpacing(12),
  },
  scrollContent: {
    paddingHorizontal: getResponsiveSpacing(16),
  },
  quickAccessItem: {
    backgroundColor: colors.background.secondary,
    borderRadius: 8,
    padding: getResponsiveSpacing(12),
    marginRight: getResponsiveSpacing(12),
    minWidth: 120,
    maxWidth: 140,
    alignItems: 'center',
  },
  itemIconContainer: {
    position: 'relative',
    marginBottom: getResponsiveSpacing(8),
  },
  badge: {
    position: 'absolute',
    top: -4,
    right: -8,
    backgroundColor: colors.sage600,
    borderRadius: 8,
    paddingHorizontal: getResponsiveSpacing(4),
    paddingVertical: getResponsiveSpacing(1),
  },
  badgeText: {
    fontSize: getResponsiveFontSize(10),
    color: colors.background.primary,
    fontWeight: '600',
  },
  itemContent: {
    alignItems: 'center',
  },
  itemTitle: {
    fontSize: getResponsiveFontSize(12),
    fontWeight: '600',
    color: colors.text.primary,
    textAlign: 'center',
    marginBottom: getResponsiveSpacing(2),
  },
  itemSubtitle: {
    fontSize: getResponsiveFontSize(10),
    color: colors.text.secondary,
    textAlign: 'center',
  },
});

export default QuickAccessBar;
