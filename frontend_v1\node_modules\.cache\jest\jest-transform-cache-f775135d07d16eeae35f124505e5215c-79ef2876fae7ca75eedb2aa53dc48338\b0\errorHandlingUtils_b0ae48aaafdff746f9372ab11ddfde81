b2604ed40c5dbd806effadbccc0f0fc5
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.withErrorHandling = exports.retryOperation = exports.logError = exports.isRetryableError = exports.getUserFriendlyMessage = exports.getRetryDelay = exports.getErrorSuggestions = exports.getErrorSeverity = exports.generateErrorId = exports.formatErrorForDisplay = exports.default = exports.createRecoveryActions = exports.createErrorBoundaryState = exports.createAppError = exports.classifyError = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var generateErrorId = exports.generateErrorId = function generateErrorId() {
  return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};
var classifyError = exports.classifyError = function classifyError(error) {
  var _error$response, _error$message;
  if (!error) return 'unknown';
  if (error.name === 'NetworkError' || error.code === 'NETWORK_ERROR') {
    return 'network';
  }
  if (error.name === 'TimeoutError' || error.code === 'TIMEOUT') {
    return 'timeout';
  }
  if (error.status || (_error$response = error.response) != null && _error$response.status) {
    var status = error.status || error.response.status;
    if (status === 401) return 'authentication';
    if (status === 403) return 'authorization';
    if (status === 404) return 'not_found';
    if (status >= 400 && status < 500) return 'client';
    if (status >= 500) return 'server';
  }
  if (error.name === 'ValidationError' || error.type === 'validation') {
    return 'validation';
  }
  if ((_error$message = error.message) != null && _error$message.includes('offline') || error.code === 'OFFLINE') {
    return 'offline';
  }
  return 'unknown';
};
var getErrorSeverity = exports.getErrorSeverity = function getErrorSeverity(error, type) {
  if (type === 'authentication' || type === 'server') {
    return 'critical';
  }
  if (type === 'network' || type === 'timeout' || type === 'authorization') {
    return 'high';
  }
  if (type === 'validation' || type === 'not_found') {
    return 'medium';
  }
  return 'low';
};
var getUserFriendlyMessage = exports.getUserFriendlyMessage = function getUserFriendlyMessage(type, originalMessage) {
  var messages = {
    network: 'Unable to connect to the internet. Please check your connection and try again.',
    validation: 'Please check your input and try again.',
    authentication: 'Your session has expired. Please sign in again.',
    authorization: 'You don\'t have permission to perform this action.',
    not_found: 'The requested information could not be found.',
    server: 'We\'re experiencing technical difficulties. Please try again later.',
    client: 'Something went wrong with your request. Please try again.',
    timeout: 'The request took too long to complete. Please try again.',
    offline: 'You appear to be offline. Please check your internet connection.',
    unknown: 'An unexpected error occurred. Please try again.'
  };
  return messages[type] || messages.unknown;
};
var getErrorSuggestions = exports.getErrorSuggestions = function getErrorSuggestions(type) {
  var suggestions = {
    network: ['Check your internet connection', 'Try switching between WiFi and mobile data', 'Restart your router if using WiFi'],
    validation: ['Double-check all required fields', 'Ensure email addresses are valid', 'Check password requirements'],
    authentication: ['Sign in again with your credentials', 'Reset your password if needed', 'Contact support if the problem persists'],
    authorization: ['Contact your administrator for access', 'Ensure you\'re signed in to the correct account', 'Try refreshing the page'],
    not_found: ['Check the URL for typos', 'Go back and try again', 'Use the search function to find what you need'],
    server: ['Wait a few minutes and try again', 'Check our status page for updates', 'Contact support if the issue persists'],
    client: ['Refresh the page and try again', 'Clear your browser cache', 'Try using a different browser'],
    timeout: ['Check your internet connection speed', 'Try again with a better connection', 'Break large requests into smaller ones'],
    offline: ['Check your internet connection', 'Try again when you\'re back online', 'Some features may work offline'],
    unknown: ['Try refreshing the page', 'Restart the app', 'Contact support if the problem continues']
  };
  return suggestions[type] || suggestions.unknown;
};
var createAppError = exports.createAppError = function createAppError(error) {
  var context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var type = classifyError(error);
  var severity = getErrorSeverity(error, type);
  var userMessage = getUserFriendlyMessage(type, error.message);
  var suggestions = getErrorSuggestions(type);
  return {
    id: generateErrorId(),
    type: type,
    severity: severity,
    message: error.message || 'Unknown error',
    userMessage: userMessage,
    code: error.code || error.status,
    context: Object.assign({
      timestamp: new Date()
    }, context),
    originalError: error,
    recoverable: type !== 'critical',
    retryable: ['network', 'timeout', 'server'].includes(type),
    suggestions: suggestions
  };
};
var isRetryableError = exports.isRetryableError = function isRetryableError(error) {
  return error.retryable && error.retryCount < 3;
};
var getRetryDelay = exports.getRetryDelay = function getRetryDelay(attemptCount) {
  return Math.min(1000 * Math.pow(2, attemptCount), 10000);
};
var logError = exports.logError = function logError(error) {
  var _error$originalError;
  var logData = {
    id: error.id,
    type: error.type,
    severity: error.severity,
    message: error.message,
    code: error.code,
    context: error.context,
    stack: (_error$originalError = error.originalError) == null ? void 0 : _error$originalError.stack
  };
  if (__DEV__) {
    console.group(`🚨 Error [${error.severity.toUpperCase()}]`);
    console.error('Error Details:', logData);
    console.error('Original Error:', error.originalError);
    console.groupEnd();
  }
};
var withErrorHandling = exports.withErrorHandling = function () {
  var _ref = (0, _asyncToGenerator2.default)(function* (operation) {
    var context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
    try {
      var data = yield operation();
      return {
        data: data,
        error: null
      };
    } catch (error) {
      var appError = createAppError(error, context);
      logError(appError);
      return {
        data: null,
        error: appError
      };
    }
  });
  return function withErrorHandling(_x) {
    return _ref.apply(this, arguments);
  };
}();
var retryOperation = exports.retryOperation = function () {
  var _ref2 = (0, _asyncToGenerator2.default)(function* (operation) {
    var maxRetries = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 3;
    var context = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
    var lastError;
    var _loop = function* _loop() {
        try {
          return {
            v: yield operation()
          };
        } catch (error) {
          lastError = error;
          if (attempt === maxRetries) {
            throw createAppError(error, Object.assign({}, context, {
              retryCount: attempt
            }));
          }
          var delay = getRetryDelay(attempt);
          yield new Promise(function (resolve) {
            return setTimeout(resolve, delay);
          });
        }
      },
      _ret;
    for (var attempt = 0; attempt <= maxRetries; attempt++) {
      _ret = yield* _loop();
      if (_ret) return _ret.v;
    }
    throw createAppError(lastError, context);
  });
  return function retryOperation(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
var createRecoveryActions = exports.createRecoveryActions = function createRecoveryActions(error) {
  var actions = [];
  if (error.retryable) {
    actions.push({
      label: 'Try Again',
      action: function action() {
        window.location.reload();
      },
      primary: true
    });
  }
  actions.push({
    label: 'Go Back',
    action: function action() {
      if (window.history.length > 1) {
        window.history.back();
      } else {
        window.location.href = '/';
      }
    }
  });
  if (error.severity === 'critical') {
    actions.push({
      label: 'Contact Support',
      action: function action() {
        window.open('mailto:<EMAIL>?subject=Error Report&body=' + encodeURIComponent(`Error ID: ${error.id}\nMessage: ${error.message}`));
      }
    });
  }
  return actions;
};
var formatErrorForDisplay = exports.formatErrorForDisplay = function formatErrorForDisplay(error) {
  var titles = {
    low: 'Minor Issue',
    medium: 'Something Went Wrong',
    high: 'Connection Problem',
    critical: 'Service Unavailable'
  };
  return {
    title: titles[error.severity],
    message: error.userMessage,
    suggestions: error.suggestions,
    actions: createRecoveryActions(error)
  };
};
var createErrorBoundaryState = exports.createErrorBoundaryState = function createErrorBoundaryState() {
  return {
    hasError: false,
    error: null,
    errorId: null,
    retryCount: 0
  };
};
var _default = exports.default = {
  generateErrorId: generateErrorId,
  classifyError: classifyError,
  getErrorSeverity: getErrorSeverity,
  getUserFriendlyMessage: getUserFriendlyMessage,
  getErrorSuggestions: getErrorSuggestions,
  createAppError: createAppError,
  isRetryableError: isRetryableError,
  getRetryDelay: getRetryDelay,
  logError: logError,
  withErrorHandling: withErrorHandling,
  retryOperation: retryOperation,
  createRecoveryActions: createRecoveryActions,
  formatErrorForDisplay: formatErrorForDisplay,
  createErrorBoundaryState: createErrorBoundaryState
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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