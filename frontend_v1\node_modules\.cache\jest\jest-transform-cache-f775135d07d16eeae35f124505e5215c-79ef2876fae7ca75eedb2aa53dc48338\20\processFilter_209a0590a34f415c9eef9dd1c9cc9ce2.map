{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "default", "processFilter", "_slicedToArray2", "_processColor", "filter", "result", "replace", "regex", "matches", "exec", "filterName", "toLowerCase", "dropShadow", "parseDropShadow", "push", "camelizedName", "amount", "_getFilterAmount", "filterFunction", "Array", "isArray", "_Object$entries$", "entries", "filterValue", "resultObject", "TypeError", "filterArgs", "filterArgAsNumber", "unit", "argsWithUnitsRegex", "RegExp", "match", "isNaN", "Number", "undefined", "Math", "PI", "rawDropShadow", "parseDropShadowString", "parsedDropShadow", "offsetX", "offsetY", "arg", "parse<PERSON><PERSON>th", "standardDeviation", "color", "processColor", "lengthCount", "keywordDetectedAfterLength", "split", "processedColor", "length"], "sources": ["processFilter.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format strict-local\n * @flow\n * @oncall react-native\n */\n\n'use strict';\n\nimport type {ColorValue} from './StyleSheet';\nimport type {DropShadowValue, FilterFunction} from './StyleSheetTypes';\n\nimport processColor from './processColor';\n\ntype ParsedFilter =\n  | {brightness: number}\n  | {blur: number}\n  | {contrast: number}\n  | {grayscale: number}\n  | {hueRotate: number}\n  | {invert: number}\n  | {opacity: number}\n  | {saturate: number}\n  | {sepia: number}\n  | {dropShadow: ParsedDropShadow};\n\ntype ParsedDropShadow = {\n  offsetX: number,\n  offsetY: number,\n  standardDeviation?: number,\n  color?: ColorValue,\n};\n\nexport default function processFilter(\n  filter: ?($ReadOnlyArray<FilterFunction> | string),\n): $ReadOnlyArray<ParsedFilter> {\n  let result: Array<ParsedFilter> = [];\n  if (filter == null) {\n    return result;\n  }\n\n  if (typeof filter === 'string') {\n    filter = filter.replace(/\\n/g, ' ');\n\n    // matches on functions with args and nested functions like \"drop-shadow(10 10 10 rgba(0, 0, 0, 1))\"\n    const regex = /([\\w-]+)\\(([^()]*|\\([^()]*\\)|[^()]*\\([^()]*\\)[^()]*)\\)/g;\n    let matches;\n\n    while ((matches = regex.exec(filter))) {\n      let filterName = matches[1].toLowerCase();\n      if (filterName === 'drop-shadow') {\n        const dropShadow = parseDropShadow(matches[2]);\n        if (dropShadow != null) {\n          result.push({dropShadow});\n        } else {\n          return [];\n        }\n      } else {\n        const camelizedName =\n          filterName === 'drop-shadow'\n            ? 'dropShadow'\n            : filterName === 'hue-rotate'\n              ? 'hueRotate'\n              : filterName;\n        const amount = _getFilterAmount(camelizedName, matches[2]);\n\n        if (amount != null) {\n          const filterFunction = {};\n          // $FlowFixMe The key will be the correct one but flow can't see that.\n          filterFunction[camelizedName] = amount;\n          // $FlowFixMe The key will be the correct one but flow can't see that.\n          result.push(filterFunction);\n        } else {\n          // If any primitive is invalid then apply none of the filters. This is how\n          // web works and makes it clear that something is wrong becuase no\n          // graphical effects are happening.\n          return [];\n        }\n      }\n    }\n  } else if (Array.isArray(filter)) {\n    for (const filterFunction of filter) {\n      const [filterName, filterValue] = Object.entries(filterFunction)[0];\n      if (filterName === 'dropShadow') {\n        // $FlowFixMe\n        const dropShadow = parseDropShadow(filterValue);\n        if (dropShadow == null) {\n          return [];\n        }\n        result.push({dropShadow});\n      } else {\n        const amount = _getFilterAmount(filterName, filterValue);\n\n        if (amount != null) {\n          const resultObject = {};\n          // $FlowFixMe\n          resultObject[filterName] = amount;\n          // $FlowFixMe\n          result.push(resultObject);\n        } else {\n          // If any primitive is invalid then apply none of the filters. This is how\n          // web works and makes it clear that something is wrong becuase no\n          // graphical effects are happening.\n          return [];\n        }\n      }\n    }\n  } else {\n    throw new TypeError(`${typeof filter} filter is not a string or array`);\n  }\n\n  return result;\n}\n\nfunction _getFilterAmount(filterName: string, filterArgs: mixed): ?number {\n  let filterArgAsNumber: number;\n  let unit: string;\n  if (typeof filterArgs === 'string') {\n    // matches on args with units like \"1.5 5% -80deg\"\n    const argsWithUnitsRegex = new RegExp(/([+-]?\\d*(\\.\\d+)?)([a-zA-Z%]+)?/g);\n    const match = argsWithUnitsRegex.exec(filterArgs);\n\n    if (!match || isNaN(Number(match[1]))) {\n      return undefined;\n    }\n\n    filterArgAsNumber = Number(match[1]);\n    unit = match[3];\n  } else if (typeof filterArgs === 'number') {\n    filterArgAsNumber = filterArgs;\n  } else {\n    return undefined;\n  }\n\n  switch (filterName) {\n    // Hue rotate takes some angle that can have a unit and can be\n    // negative. Additionally, 0 with no unit is allowed.\n    case 'hueRotate':\n      if (filterArgAsNumber === 0) {\n        return 0;\n      }\n      if (unit !== 'deg' && unit !== 'rad') {\n        return undefined;\n      }\n      return unit === 'rad'\n        ? (180 * filterArgAsNumber) / Math.PI\n        : filterArgAsNumber;\n    // blur takes any positive CSS length that is not a percent. In RN\n    // we currently only have DIPs, so we are not parsing units here.\n    case 'blur':\n      if ((unit && unit !== 'px') || filterArgAsNumber < 0) {\n        return undefined;\n      }\n      return filterArgAsNumber;\n    // All other filters except take a non negative number or percentage. There\n    // are no units associated with this value and percentage numbers map 1-to-1\n    // to a non-percentage number (e.g. 50% == 0.5).\n    case 'brightness':\n    case 'contrast':\n    case 'grayscale':\n    case 'invert':\n    case 'opacity':\n    case 'saturate':\n    case 'sepia':\n      if ((unit && unit !== '%' && unit !== 'px') || filterArgAsNumber < 0) {\n        return undefined;\n      }\n      if (unit === '%') {\n        filterArgAsNumber /= 100;\n      }\n      return filterArgAsNumber;\n    default:\n      return undefined;\n  }\n}\n\nfunction parseDropShadow(\n  rawDropShadow: string | DropShadowValue,\n): ?ParsedDropShadow {\n  const dropShadow =\n    typeof rawDropShadow === 'string'\n      ? parseDropShadowString(rawDropShadow)\n      : rawDropShadow;\n\n  const parsedDropShadow: ParsedDropShadow = {\n    offsetX: 0,\n    offsetY: 0,\n  };\n  let offsetX: number;\n  let offsetY: number;\n\n  for (const arg in dropShadow) {\n    let value;\n    switch (arg) {\n      case 'offsetX':\n        value =\n          typeof dropShadow.offsetX === 'string'\n            ? parseLength(dropShadow.offsetX)\n            : dropShadow.offsetX;\n        if (value == null) {\n          return null;\n        }\n        offsetX = value;\n        break;\n      case 'offsetY':\n        value =\n          typeof dropShadow.offsetY === 'string'\n            ? parseLength(dropShadow.offsetY)\n            : dropShadow.offsetY;\n        if (value == null) {\n          return null;\n        }\n        offsetY = value;\n        break;\n      case 'standardDeviation':\n        value =\n          typeof dropShadow.standardDeviation === 'string'\n            ? parseLength(dropShadow.standardDeviation)\n            : dropShadow.standardDeviation;\n        if (value == null || value < 0) {\n          return null;\n        }\n        parsedDropShadow.standardDeviation = value;\n        break;\n      case 'color':\n        const color = processColor(dropShadow.color);\n        if (color == null) {\n          return null;\n        }\n        parsedDropShadow.color = color;\n        break;\n      default:\n        return null;\n    }\n  }\n\n  if (offsetX == null || offsetY == null) {\n    return null;\n  }\n\n  parsedDropShadow.offsetX = offsetX;\n  parsedDropShadow.offsetY = offsetY;\n\n  return parsedDropShadow;\n}\n\nfunction parseDropShadowString(rawDropShadow: string): ?DropShadowValue {\n  const dropShadow: DropShadowValue = {\n    offsetX: 0,\n    offsetY: 0,\n  };\n  let offsetX: string;\n  let offsetY: string;\n  let lengthCount = 0;\n  let keywordDetectedAfterLength = false;\n\n  // split args by all whitespaces that are not in parenthesis\n  for (const arg of rawDropShadow.split(/\\s+(?![^(]*\\))/)) {\n    const processedColor = processColor(arg);\n    if (processedColor != null) {\n      if (dropShadow.color != null) {\n        return null;\n      }\n      if (offsetX != null) {\n        keywordDetectedAfterLength = true;\n      }\n      dropShadow.color = arg;\n      continue;\n    }\n\n    switch (lengthCount) {\n      case 0:\n        offsetX = arg;\n        lengthCount++;\n        break;\n      case 1:\n        if (keywordDetectedAfterLength) {\n          return null;\n        }\n        offsetY = arg;\n        lengthCount++;\n        break;\n      case 2:\n        if (keywordDetectedAfterLength) {\n          return null;\n        }\n        dropShadow.standardDeviation = arg;\n        lengthCount++;\n        break;\n      default:\n        return null;\n    }\n  }\n  if (offsetX == null || offsetY == null) {\n    return null;\n  }\n\n  dropShadow.offsetX = offsetX;\n  dropShadow.offsetY = offsetY;\n  return dropShadow;\n}\n\nfunction parseLength(length: string): ?number {\n  // matches on args with units like \"1.5 5% -80deg\"\n  const argsWithUnitsRegex = /([+-]?\\d*(\\.\\d+)?)([\\w\\W]+)?/g;\n  const match = argsWithUnitsRegex.exec(length);\n\n  if (!match || Number.isNaN(match[1])) {\n    return null;\n  }\n\n  if (match[3] != null && match[3] !== 'px') {\n    return null;\n  }\n\n  if (match[3] == null && match[1] !== '0') {\n    return null;\n  }\n\n  return Number(match[1]);\n}\n"], "mappings": "AAWA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA,GAAAC,aAAA;AAAA,IAAAC,eAAA,GAAAR,sBAAA,CAAAC,OAAA;AAKb,IAAAQ,aAAA,GAAAT,sBAAA,CAAAC,OAAA;AAqBe,SAASM,aAAaA,CACnCG,MAAkD,EACpB;EAC9B,IAAIC,MAA2B,GAAG,EAAE;EACpC,IAAID,MAAM,IAAI,IAAI,EAAE;IAClB,OAAOC,MAAM;EACf;EAEA,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;IAC9BA,MAAM,GAAGA,MAAM,CAACE,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;IAGnC,IAAMC,KAAK,GAAG,yDAAyD;IACvE,IAAIC,OAAO;IAEX,OAAQA,OAAO,GAAGD,KAAK,CAACE,IAAI,CAACL,MAAM,CAAC,EAAG;MACrC,IAAIM,UAAU,GAAGF,OAAO,CAAC,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC;MACzC,IAAID,UAAU,KAAK,aAAa,EAAE;QAChC,IAAME,UAAU,GAAGC,eAAe,CAACL,OAAO,CAAC,CAAC,CAAC,CAAC;QAC9C,IAAII,UAAU,IAAI,IAAI,EAAE;UACtBP,MAAM,CAACS,IAAI,CAAC;YAACF,UAAU,EAAVA;UAAU,CAAC,CAAC;QAC3B,CAAC,MAAM;UACL,OAAO,EAAE;QACX;MACF,CAAC,MAAM;QACL,IAAMG,aAAa,GACjBL,UAAU,KAAK,aAAa,GACxB,YAAY,GACZA,UAAU,KAAK,YAAY,GACzB,WAAW,GACXA,UAAU;QAClB,IAAMM,MAAM,GAAGC,gBAAgB,CAACF,aAAa,EAAEP,OAAO,CAAC,CAAC,CAAC,CAAC;QAE1D,IAAIQ,MAAM,IAAI,IAAI,EAAE;UAClB,IAAME,cAAc,GAAG,CAAC,CAAC;UAEzBA,cAAc,CAACH,aAAa,CAAC,GAAGC,MAAM;UAEtCX,MAAM,CAACS,IAAI,CAACI,cAAc,CAAC;QAC7B,CAAC,MAAM;UAIL,OAAO,EAAE;QACX;MACF;IACF;EACF,CAAC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAAChB,MAAM,CAAC,EAAE;IAChC,KAAK,IAAMc,eAAc,IAAId,MAAM,EAAE;MACnC,IAAAiB,gBAAA,OAAAnB,eAAA,CAAAF,OAAA,EAAkCJ,MAAM,CAAC0B,OAAO,CAACJ,eAAc,CAAC,CAAC,CAAC,CAAC;QAA5DR,WAAU,GAAAW,gBAAA;QAAEE,WAAW,GAAAF,gBAAA;MAC9B,IAAIX,WAAU,KAAK,YAAY,EAAE;QAE/B,IAAME,WAAU,GAAGC,eAAe,CAACU,WAAW,CAAC;QAC/C,IAAIX,WAAU,IAAI,IAAI,EAAE;UACtB,OAAO,EAAE;QACX;QACAP,MAAM,CAACS,IAAI,CAAC;UAACF,UAAU,EAAVA;QAAU,CAAC,CAAC;MAC3B,CAAC,MAAM;QACL,IAAMI,OAAM,GAAGC,gBAAgB,CAACP,WAAU,EAAEa,WAAW,CAAC;QAExD,IAAIP,OAAM,IAAI,IAAI,EAAE;UAClB,IAAMQ,YAAY,GAAG,CAAC,CAAC;UAEvBA,YAAY,CAACd,WAAU,CAAC,GAAGM,OAAM;UAEjCX,MAAM,CAACS,IAAI,CAACU,YAAY,CAAC;QAC3B,CAAC,MAAM;UAIL,OAAO,EAAE;QACX;MACF;IACF;EACF,CAAC,MAAM;IACL,MAAM,IAAIC,SAAS,CAAC,GAAG,OAAOrB,MAAM,kCAAkC,CAAC;EACzE;EAEA,OAAOC,MAAM;AACf;AAEA,SAASY,gBAAgBA,CAACP,UAAkB,EAAEgB,UAAiB,EAAW;EACxE,IAAIC,iBAAyB;EAC7B,IAAIC,IAAY;EAChB,IAAI,OAAOF,UAAU,KAAK,QAAQ,EAAE;IAElC,IAAMG,kBAAkB,GAAG,IAAIC,MAAM,CAAC,kCAAkC,CAAC;IACzE,IAAMC,KAAK,GAAGF,kBAAkB,CAACpB,IAAI,CAACiB,UAAU,CAAC;IAEjD,IAAI,CAACK,KAAK,IAAIC,KAAK,CAACC,MAAM,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACrC,OAAOG,SAAS;IAClB;IAEAP,iBAAiB,GAAGM,MAAM,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC;IACpCH,IAAI,GAAGG,KAAK,CAAC,CAAC,CAAC;EACjB,CAAC,MAAM,IAAI,OAAOL,UAAU,KAAK,QAAQ,EAAE;IACzCC,iBAAiB,GAAGD,UAAU;EAChC,CAAC,MAAM;IACL,OAAOQ,SAAS;EAClB;EAEA,QAAQxB,UAAU;IAGhB,KAAK,WAAW;MACd,IAAIiB,iBAAiB,KAAK,CAAC,EAAE;QAC3B,OAAO,CAAC;MACV;MACA,IAAIC,IAAI,KAAK,KAAK,IAAIA,IAAI,KAAK,KAAK,EAAE;QACpC,OAAOM,SAAS;MAClB;MACA,OAAON,IAAI,KAAK,KAAK,GAChB,GAAG,GAAGD,iBAAiB,GAAIQ,IAAI,CAACC,EAAE,GACnCT,iBAAiB;IAGvB,KAAK,MAAM;MACT,IAAKC,IAAI,IAAIA,IAAI,KAAK,IAAI,IAAKD,iBAAiB,GAAG,CAAC,EAAE;QACpD,OAAOO,SAAS;MAClB;MACA,OAAOP,iBAAiB;IAI1B,KAAK,YAAY;IACjB,KAAK,UAAU;IACf,KAAK,WAAW;IAChB,KAAK,QAAQ;IACb,KAAK,SAAS;IACd,KAAK,UAAU;IACf,KAAK,OAAO;MACV,IAAKC,IAAI,IAAIA,IAAI,KAAK,GAAG,IAAIA,IAAI,KAAK,IAAI,IAAKD,iBAAiB,GAAG,CAAC,EAAE;QACpE,OAAOO,SAAS;MAClB;MACA,IAAIN,IAAI,KAAK,GAAG,EAAE;QAChBD,iBAAiB,IAAI,GAAG;MAC1B;MACA,OAAOA,iBAAiB;IAC1B;MACE,OAAOO,SAAS;EACpB;AACF;AAEA,SAASrB,eAAeA,CACtBwB,aAAuC,EACpB;EACnB,IAAMzB,UAAU,GACd,OAAOyB,aAAa,KAAK,QAAQ,GAC7BC,qBAAqB,CAACD,aAAa,CAAC,GACpCA,aAAa;EAEnB,IAAME,gBAAkC,GAAG;IACzCC,OAAO,EAAE,CAAC;IACVC,OAAO,EAAE;EACX,CAAC;EACD,IAAID,OAAe;EACnB,IAAIC,OAAe;EAEnB,KAAK,IAAMC,GAAG,IAAI9B,UAAU,EAAE;IAC5B,IAAIb,KAAK;IACT,QAAQ2C,GAAG;MACT,KAAK,SAAS;QACZ3C,KAAK,GACH,OAAOa,UAAU,CAAC4B,OAAO,KAAK,QAAQ,GAClCG,WAAW,CAAC/B,UAAU,CAAC4B,OAAO,CAAC,GAC/B5B,UAAU,CAAC4B,OAAO;QACxB,IAAIzC,KAAK,IAAI,IAAI,EAAE;UACjB,OAAO,IAAI;QACb;QACAyC,OAAO,GAAGzC,KAAK;QACf;MACF,KAAK,SAAS;QACZA,KAAK,GACH,OAAOa,UAAU,CAAC6B,OAAO,KAAK,QAAQ,GAClCE,WAAW,CAAC/B,UAAU,CAAC6B,OAAO,CAAC,GAC/B7B,UAAU,CAAC6B,OAAO;QACxB,IAAI1C,KAAK,IAAI,IAAI,EAAE;UACjB,OAAO,IAAI;QACb;QACA0C,OAAO,GAAG1C,KAAK;QACf;MACF,KAAK,mBAAmB;QACtBA,KAAK,GACH,OAAOa,UAAU,CAACgC,iBAAiB,KAAK,QAAQ,GAC5CD,WAAW,CAAC/B,UAAU,CAACgC,iBAAiB,CAAC,GACzChC,UAAU,CAACgC,iBAAiB;QAClC,IAAI7C,KAAK,IAAI,IAAI,IAAIA,KAAK,GAAG,CAAC,EAAE;UAC9B,OAAO,IAAI;QACb;QACAwC,gBAAgB,CAACK,iBAAiB,GAAG7C,KAAK;QAC1C;MACF,KAAK,OAAO;QACV,IAAM8C,KAAK,GAAG,IAAAC,qBAAY,EAAClC,UAAU,CAACiC,KAAK,CAAC;QAC5C,IAAIA,KAAK,IAAI,IAAI,EAAE;UACjB,OAAO,IAAI;QACb;QACAN,gBAAgB,CAACM,KAAK,GAAGA,KAAK;QAC9B;MACF;QACE,OAAO,IAAI;IACf;EACF;EAEA,IAAIL,OAAO,IAAI,IAAI,IAAIC,OAAO,IAAI,IAAI,EAAE;IACtC,OAAO,IAAI;EACb;EAEAF,gBAAgB,CAACC,OAAO,GAAGA,OAAO;EAClCD,gBAAgB,CAACE,OAAO,GAAGA,OAAO;EAElC,OAAOF,gBAAgB;AACzB;AAEA,SAASD,qBAAqBA,CAACD,aAAqB,EAAoB;EACtE,IAAMzB,UAA2B,GAAG;IAClC4B,OAAO,EAAE,CAAC;IACVC,OAAO,EAAE;EACX,CAAC;EACD,IAAID,OAAe;EACnB,IAAIC,OAAe;EACnB,IAAIM,WAAW,GAAG,CAAC;EACnB,IAAIC,0BAA0B,GAAG,KAAK;EAGtC,KAAK,IAAMN,GAAG,IAAIL,aAAa,CAACY,KAAK,CAAC,gBAAgB,CAAC,EAAE;IACvD,IAAMC,cAAc,GAAG,IAAAJ,qBAAY,EAACJ,GAAG,CAAC;IACxC,IAAIQ,cAAc,IAAI,IAAI,EAAE;MAC1B,IAAItC,UAAU,CAACiC,KAAK,IAAI,IAAI,EAAE;QAC5B,OAAO,IAAI;MACb;MACA,IAAIL,OAAO,IAAI,IAAI,EAAE;QACnBQ,0BAA0B,GAAG,IAAI;MACnC;MACApC,UAAU,CAACiC,KAAK,GAAGH,GAAG;MACtB;IACF;IAEA,QAAQK,WAAW;MACjB,KAAK,CAAC;QACJP,OAAO,GAAGE,GAAG;QACbK,WAAW,EAAE;QACb;MACF,KAAK,CAAC;QACJ,IAAIC,0BAA0B,EAAE;UAC9B,OAAO,IAAI;QACb;QACAP,OAAO,GAAGC,GAAG;QACbK,WAAW,EAAE;QACb;MACF,KAAK,CAAC;QACJ,IAAIC,0BAA0B,EAAE;UAC9B,OAAO,IAAI;QACb;QACApC,UAAU,CAACgC,iBAAiB,GAAGF,GAAG;QAClCK,WAAW,EAAE;QACb;MACF;QACE,OAAO,IAAI;IACf;EACF;EACA,IAAIP,OAAO,IAAI,IAAI,IAAIC,OAAO,IAAI,IAAI,EAAE;IACtC,OAAO,IAAI;EACb;EAEA7B,UAAU,CAAC4B,OAAO,GAAGA,OAAO;EAC5B5B,UAAU,CAAC6B,OAAO,GAAGA,OAAO;EAC5B,OAAO7B,UAAU;AACnB;AAEA,SAAS+B,WAAWA,CAACQ,MAAc,EAAW;EAE5C,IAAMtB,kBAAkB,GAAG,+BAA+B;EAC1D,IAAME,KAAK,GAAGF,kBAAkB,CAACpB,IAAI,CAAC0C,MAAM,CAAC;EAE7C,IAAI,CAACpB,KAAK,IAAIE,MAAM,CAACD,KAAK,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;IACpC,OAAO,IAAI;EACb;EAEA,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,IAAIA,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;IACzC,OAAO,IAAI;EACb;EAEA,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,IAAIA,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACxC,OAAO,IAAI;EACb;EAEA,OAAOE,MAAM,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC;AACzB", "ignoreList": []}