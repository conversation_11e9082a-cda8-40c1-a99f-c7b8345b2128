/**
 * Payment Methods Screen - Payment Management
 *
 * Component Contract:
 * - Displays user's saved payment methods
 * - Allows adding, editing, and removing payment methods
 * - Integrates with backend payment service
 * - Supports multiple payment types (cards, digital wallets)
 * - Follows security best practices for payment data
 * - Implements proper accessibility and responsive design
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { SafeAreaScreen } from '../components/templates/SafeAreaScreen';
import { Card } from '../components/molecules/Card';
import { StandardizedButton } from '../components/atoms/StandardizedButton';
import { AccessibleTouchable } from '../components/atoms/AccessibleTouchable';
import { useTheme } from '../contexts/ThemeContext';
import { getResponsiveSpacing, getResponsiveFontSize } from '../utils/responsiveUtils';
import { paymentService } from '../services/paymentService';

type PaymentMethodsScreenNavigationProp = StackNavigationProp<any, 'PaymentMethods'>;

interface PaymentMethod {
  id: string;
  type: 'card' | 'paypal' | 'apple_pay' | 'google_pay';
  last_four?: string;
  brand?: string;
  expiry_month?: number;
  expiry_year?: number;
  is_default: boolean;
  created_at: string;
}

export const PaymentMethodsScreen: React.FC = () => {
  const { colors, isDark } = useTheme();
  const navigation = useNavigation<PaymentMethodsScreenNavigationProp>();
  const styles = createStyles(colors);
  
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadPaymentMethods();
  }, []);

  const loadPaymentMethods = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const methods = await paymentService.getPaymentMethods();
      setPaymentMethods(methods);
    } catch (err) {
      console.error('Failed to load payment methods:', err);
      setError('Failed to load payment methods');
    } finally {
      setLoading(false);
    }
  };

  const handleAddPaymentMethod = () => {
    navigation.navigate('AddPaymentMethod');
  };

  const handleSetDefault = async (methodId: string) => {
    try {
      await paymentService.setDefaultPaymentMethod(methodId);
      
      // Update local state
      setPaymentMethods(prev => 
        prev.map(method => ({
          ...method,
          is_default: method.id === methodId,
        }))
      );
      
      Alert.alert('Success', 'Default payment method updated');
    } catch (error) {
      console.error('Failed to set default payment method:', error);
      Alert.alert('Error', 'Failed to update default payment method');
    }
  };

  const handleDeletePaymentMethod = (method: PaymentMethod) => {
    Alert.alert(
      'Delete Payment Method',
      `Are you sure you want to delete this ${method.type === 'card' ? 'card' : method.type}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => deletePaymentMethod(method.id),
        },
      ]
    );
  };

  const deletePaymentMethod = async (methodId: string) => {
    try {
      await paymentService.deletePaymentMethod(methodId);
      
      // Update local state
      setPaymentMethods(prev => prev.filter(method => method.id !== methodId));
      
      Alert.alert('Success', 'Payment method deleted');
    } catch (error) {
      console.error('Failed to delete payment method:', error);
      Alert.alert('Error', 'Failed to delete payment method');
    }
  };

  const getPaymentMethodIcon = (type: string) => {
    switch (type) {
      case 'card':
        return 'card-outline';
      case 'paypal':
        return 'logo-paypal';
      case 'apple_pay':
        return 'logo-apple';
      case 'google_pay':
        return 'logo-google';
      default:
        return 'card-outline';
    }
  };

  const getPaymentMethodTitle = (method: PaymentMethod) => {
    if (method.type === 'card') {
      return `${method.brand?.toUpperCase() || 'Card'} •••• ${method.last_four}`;
    }
    return method.type.replace('_', ' ').toUpperCase();
  };

  const getPaymentMethodSubtitle = (method: PaymentMethod) => {
    if (method.type === 'card' && method.expiry_month && method.expiry_year) {
      return `Expires ${method.expiry_month.toString().padStart(2, '0')}/${method.expiry_year}`;
    }
    return 'Digital wallet';
  };

  const renderPaymentMethod = (method: PaymentMethod) => (
    <Card key={method.id} style={styles.paymentMethodCard}>
      <View style={styles.paymentMethodHeader}>
        <View style={styles.paymentMethodInfo}>
          <View style={styles.paymentMethodIcon}>
            <Ionicons
              name={getPaymentMethodIcon(method.type) as any}
              size={24}
              color={colors.sage400}
            />
          </View>
          <View style={styles.paymentMethodDetails}>
            <Text style={styles.paymentMethodTitle}>
              {getPaymentMethodTitle(method)}
            </Text>
            <Text style={styles.paymentMethodSubtitle}>
              {getPaymentMethodSubtitle(method)}
            </Text>
            {method.is_default && (
              <Text style={styles.defaultBadge}>Default</Text>
            )}
          </View>
        </View>
        
        <View style={styles.paymentMethodActions}>
          {!method.is_default && (
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleSetDefault(method.id)}
              testID={`set-default-${method.id}`}>
              <Text style={styles.actionButtonText}>Set Default</Text>
            </TouchableOpacity>
          )}
          
          <TouchableOpacity
            style={[styles.actionButton, styles.deleteButton]}
            onPress={() => handleDeletePaymentMethod(method)}
            testID={`delete-${method.id}`}>
            <Ionicons name="trash-outline" size={16} color={colors.error} />
          </TouchableOpacity>
        </View>
      </View>
    </Card>
  );

  const renderEmptyState = () => (
    <Card style={styles.emptyStateCard}>
      <View style={styles.emptyStateContent}>
        <Ionicons name="card-outline" size={48} color={colors.text.tertiary} />
        <Text style={styles.emptyStateTitle}>No Payment Methods</Text>
        <Text style={styles.emptyStateSubtitle}>
          Add a payment method to make booking services easier
        </Text>
        <StandardizedButton
          action="add"
          onPress={handleAddPaymentMethod}
          style={styles.addFirstMethodButton}
          testID="add-first-payment-method">
          Add Payment Method
        </StandardizedButton>
      </View>
    </Card>
  );

  return (
    <SafeAreaScreen
      backgroundColor={colors.background.primary}
      statusBarStyle={isDark ? 'light-content' : 'dark-content'}
      testID="payment-methods-screen">
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={styles.backButton}
          testID="back-button">
          <Ionicons name="arrow-back" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Payment Methods</Text>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}>
        
        {loading ? (
          <Card style={styles.loadingCard}>
            <Text style={styles.loadingText}>Loading payment methods...</Text>
          </Card>
        ) : error ? (
          <Card style={styles.errorCard}>
            <Text style={styles.errorText}>{error}</Text>
            <StandardizedButton
              action="retry"
              onPress={loadPaymentMethods}
              style={styles.retryButton}
              testID="retry-button">
              Try Again
            </StandardizedButton>
          </Card>
        ) : paymentMethods.length === 0 ? (
          renderEmptyState()
        ) : (
          <>
            {paymentMethods.map(renderPaymentMethod)}
            
            <StandardizedButton
              action="add"
              onPress={handleAddPaymentMethod}
              style={styles.addMethodButton}
              testID="add-payment-method">
              Add New Payment Method
            </StandardizedButton>
          </>
        )}

        {/* Security Notice */}
        <Card style={styles.securityNotice}>
          <View style={styles.securityHeader}>
            <Ionicons name="shield-checkmark" size={20} color={colors.sage400} />
            <Text style={styles.securityTitle}>Secure Payments</Text>
          </View>
          <Text style={styles.securityText}>
            Your payment information is encrypted and securely stored. We never store your full card details.
          </Text>
        </Card>
      </ScrollView>
    </SafeAreaScreen>
  );
};

const createStyles = (colors: any) => ({
  header: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    paddingHorizontal: getResponsiveSpacing(16),
    paddingVertical: getResponsiveSpacing(12),
    borderBottomWidth: 1,
    borderBottomColor: colors.border.primary,
  },
  backButton: {
    padding: getResponsiveSpacing(8),
    marginLeft: -getResponsiveSpacing(8),
  },
  headerTitle: {
    fontSize: getResponsiveFontSize(20),
    fontWeight: '600',
    color: colors.text.primary,
    flex: 1,
    textAlign: 'center' as const,
  },
  headerSpacer: {
    width: 40,
  },
  container: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: getResponsiveSpacing(16),
    paddingVertical: getResponsiveSpacing(16),
  },
  paymentMethodCard: {
    marginBottom: getResponsiveSpacing(12),
    padding: getResponsiveSpacing(16),
  },
  paymentMethodHeader: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'space-between' as const,
  },
  paymentMethodInfo: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    flex: 1,
  },
  paymentMethodIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors.background.secondary,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    marginRight: getResponsiveSpacing(12),
  },
  paymentMethodDetails: {
    flex: 1,
  },
  paymentMethodTitle: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '500',
    color: colors.text.primary,
    marginBottom: getResponsiveSpacing(2),
  },
  paymentMethodSubtitle: {
    fontSize: getResponsiveFontSize(14),
    color: colors.text.secondary,
    marginBottom: getResponsiveSpacing(4),
  },
  defaultBadge: {
    fontSize: getResponsiveFontSize(12),
    fontWeight: '500',
    color: colors.sage500,
    backgroundColor: colors.sage100,
    paddingHorizontal: getResponsiveSpacing(8),
    paddingVertical: getResponsiveSpacing(2),
    borderRadius: 4,
    alignSelf: 'flex-start' as const,
  },
  paymentMethodActions: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
  },
  actionButton: {
    paddingHorizontal: getResponsiveSpacing(12),
    paddingVertical: getResponsiveSpacing(6),
    borderRadius: 6,
    marginLeft: getResponsiveSpacing(8),
  },
  actionButtonText: {
    fontSize: getResponsiveFontSize(14),
    fontWeight: '500',
    color: colors.sage500,
  },
  deleteButton: {
    backgroundColor: colors.background.secondary,
  },
  emptyStateCard: {
    padding: getResponsiveSpacing(32),
    alignItems: 'center' as const,
  },
  emptyStateContent: {
    alignItems: 'center' as const,
  },
  emptyStateTitle: {
    fontSize: getResponsiveFontSize(20),
    fontWeight: '600',
    color: colors.text.primary,
    marginTop: getResponsiveSpacing(16),
    marginBottom: getResponsiveSpacing(8),
  },
  emptyStateSubtitle: {
    fontSize: getResponsiveFontSize(16),
    color: colors.text.secondary,
    textAlign: 'center' as const,
    marginBottom: getResponsiveSpacing(24),
  },
  addFirstMethodButton: {
    minWidth: 200,
  },
  addMethodButton: {
    marginTop: getResponsiveSpacing(16),
    marginBottom: getResponsiveSpacing(24),
  },
  loadingCard: {
    padding: getResponsiveSpacing(24),
    alignItems: 'center' as const,
  },
  loadingText: {
    fontSize: getResponsiveFontSize(16),
    color: colors.text.secondary,
  },
  errorCard: {
    padding: getResponsiveSpacing(24),
    alignItems: 'center' as const,
  },
  errorText: {
    fontSize: getResponsiveFontSize(16),
    color: colors.error,
    textAlign: 'center' as const,
    marginBottom: getResponsiveSpacing(16),
  },
  retryButton: {
    minWidth: 120,
  },
  securityNotice: {
    padding: getResponsiveSpacing(16),
    backgroundColor: colors.background.secondary,
    borderLeftWidth: 4,
    borderLeftColor: colors.sage400,
  },
  securityHeader: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    marginBottom: getResponsiveSpacing(8),
  },
  securityTitle: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '500',
    color: colors.text.primary,
    marginLeft: getResponsiveSpacing(8),
  },
  securityText: {
    fontSize: getResponsiveFontSize(14),
    color: colors.text.secondary,
    lineHeight: 20,
  },
});
