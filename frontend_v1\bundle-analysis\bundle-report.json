{"timestamp": "2025-07-21T05:13:42.597Z", "summary": {"totalBundleSize": "368.73 MB", "totalDependencies": 51, "duplicatePackages": 0, "recommendationsCount": 3}, "performance": {"totalBundleSize": 386637057, "largestDependencies": [{"name": "react-native", "size": 74968088, "sizeFormatted": "71.5 MB"}, {"name": "typescript", "size": 22867703, "sizeFormatted": "21.81 MB"}, {"name": "prettier", "size": 8458264, "sizeFormatted": "8.07 MB"}, {"name": "@reduxjs/toolkit", "size": 7398697, "sizeFormatted": "7.06 MB"}, {"name": "style-dictionary", "size": 7130182, "sizeFormatted": "6.8 MB"}, {"name": "react-native-gesture-handler", "size": 4235617, "sizeFormatted": "4.04 MB"}, {"name": "react-native-svg", "size": 4047944, "sizeFormatted": "3.86 MB"}, {"name": "react-native-reanimated", "size": 3456721, "sizeFormatted": "3.3 MB"}, {"name": "eslint", "size": 3231262, "sizeFormatted": "3.08 MB"}, {"name": "@react-native/eslint-config", "size": 2496187, "sizeFormatted": "2.38 MB"}], "duplicateCount": 0, "totalDependencies": 51}, "recommendations": [{"type": "warning", "category": "Bundle Size", "message": "Bundle size (368.73 MB) exceeds recommended threshold", "action": "Consider removing unused dependencies or implementing code splitting"}, {"type": "info", "category": "Large Dependencies", "message": "5 dependencies are larger than 5 MB", "action": "Consider lazy loading or finding lighter alternatives", "details": [{"name": "react-native", "size": 74968088, "sizeFormatted": "71.5 MB"}, {"name": "typescript", "size": 22867703, "sizeFormatted": "21.81 MB"}, {"name": "prettier", "size": 8458264, "sizeFormatted": "8.07 MB"}]}, {"type": "info", "category": "Performance", "message": "Enable tree shaking and minification in production builds", "action": "Ensure Metro config has proper optimization settings"}], "duplicates": [], "largestDependencies": [{"name": "react-native", "size": 74968088, "sizeFormatted": "71.5 MB"}, {"name": "typescript", "size": 22867703, "sizeFormatted": "21.81 MB"}, {"name": "prettier", "size": 8458264, "sizeFormatted": "8.07 MB"}, {"name": "@reduxjs/toolkit", "size": 7398697, "sizeFormatted": "7.06 MB"}, {"name": "style-dictionary", "size": 7130182, "sizeFormatted": "6.8 MB"}, {"name": "react-native-gesture-handler", "size": 4235617, "sizeFormatted": "4.04 MB"}, {"name": "react-native-svg", "size": 4047944, "sizeFormatted": "3.86 MB"}, {"name": "react-native-reanimated", "size": 3456721, "sizeFormatted": "3.3 MB"}, {"name": "eslint", "size": 3231262, "sizeFormatted": "3.08 MB"}, {"name": "@react-native/eslint-config", "size": 2496187, "sizeFormatted": "2.38 MB"}]}