1ec7484b774b8a6c6b4d9ecb902b5ded
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var dummyInsets = {
  top: undefined,
  left: undefined,
  right: undefined,
  bottom: undefined
};
function insetsDiffer(one, two) {
  one = one || dummyInsets;
  two = two || dummyInsets;
  return one !== two && (one.top !== two.top || one.left !== two.left || one.right !== two.right || one.bottom !== two.bottom);
}
var _default = exports.default = insetsDiffer;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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