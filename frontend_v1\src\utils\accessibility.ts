/**
 * Accessibility Utilities
 * 
 * Comprehensive accessibility utilities for WCAG 2.2 AA compliance.
 * Implements REC-ACC-001, REC-ACC-002, and REC-ACC-003 requirements.
 * 
 * Features:
 * - Screen reader support utilities
 * - Keyboard navigation helpers
 * - Color contrast validation
 * - Focus management
 * - Semantic markup helpers
 * - Accessibility testing utilities
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { Platform } from 'react-native';
import { AccessibilityInfo } from 'react-native';

// WCAG 2.2 AA compliance constants
export const WCAG_CONSTANTS = {
  // Color contrast ratios
  CONTRAST_RATIOS: {
    NORMAL_TEXT: 4.5,
    LARGE_TEXT: 3.0,
    NON_TEXT: 3.0,
  },
  
  // Touch target sizes (44x44 minimum)
  TOUCH_TARGET: {
    MIN_SIZE: 44,
    RECOMMENDED_SIZE: 48,
  },
  
  // Animation timing
  ANIMATION: {
    MAX_DURATION: 5000, // 5 seconds max for auto-playing content
    REDUCED_MOTION_DURATION: 200, // Reduced motion preference
  },
  
  // Text sizing
  TEXT: {
    MIN_SIZE: 12,
    LARGE_TEXT_THRESHOLD: 18,
  },
} as const;

// Accessibility role types
export type AccessibilityRole = 
  | 'button'
  | 'link'
  | 'search'
  | 'image'
  | 'keyboardkey'
  | 'text'
  | 'adjustable'
  | 'imagebutton'
  | 'header'
  | 'summary'
  | 'alert'
  | 'checkbox'
  | 'combobox'
  | 'menu'
  | 'menubar'
  | 'menuitem'
  | 'progressbar'
  | 'radio'
  | 'radiogroup'
  | 'scrollbar'
  | 'spinbutton'
  | 'switch'
  | 'tab'
  | 'tablist'
  | 'timer'
  | 'toolbar'
  | 'grid'
  | 'list'
  | 'listitem';

// Accessibility state interface
export interface AccessibilityState {
  disabled?: boolean;
  selected?: boolean;
  checked?: boolean | 'mixed';
  busy?: boolean;
  expanded?: boolean;
}

// Screen reader utilities
export class ScreenReaderUtils {
  /**
   * Check if screen reader is enabled
   */
  static async isScreenReaderEnabled(): Promise<boolean> {
    try {
      return await AccessibilityInfo.isScreenReaderEnabled();
    } catch (error) {
      console.warn('Failed to check screen reader status:', error);
      return false;
    }
  }

  /**
   * Announce message to screen reader
   */
  static announceForAccessibility(message: string): void {
    if (Platform.OS === 'ios' || Platform.OS === 'android') {
      AccessibilityInfo.announceForAccessibility(message);
    }
  }

  /**
   * Set accessibility focus to element
   */
  static setAccessibilityFocus(reactTag: number): void {
    if (Platform.OS === 'ios' || Platform.OS === 'android') {
      AccessibilityInfo.setAccessibilityFocus(reactTag);
    }
  }

  /**
   * Generate accessible label for form fields
   */
  static generateFormFieldLabel(
    label: string,
    required: boolean = false,
    error?: string
  ): string {
    let accessibleLabel = label;
    
    if (required) {
      accessibleLabel += ', required';
    }
    
    if (error) {
      accessibleLabel += `, error: ${error}`;
    }
    
    return accessibleLabel;
  }

  /**
   * Generate accessible hint for interactive elements
   */
  static generateInteractionHint(
    action: string,
    additionalInfo?: string
  ): string {
    let hint = `Double tap to ${action}`;
    
    if (additionalInfo) {
      hint += `. ${additionalInfo}`;
    }
    
    return hint;
  }
}

// Color contrast utilities
export class ColorContrastUtils {
  /**
   * Calculate relative luminance of a color
   */
  static getRelativeLuminance(color: string): number {
    // Convert hex to RGB
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16) / 255;
    const g = parseInt(hex.substr(2, 2), 16) / 255;
    const b = parseInt(hex.substr(4, 2), 16) / 255;

    // Apply gamma correction
    const sRGB = [r, g, b].map(c => {
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });

    // Calculate relative luminance
    return 0.2126 * sRGB[0] + 0.7152 * sRGB[1] + 0.0722 * sRGB[2];
  }

  /**
   * Calculate contrast ratio between two colors
   */
  static getContrastRatio(color1: string, color2: string): number {
    const lum1 = this.getRelativeLuminance(color1);
    const lum2 = this.getRelativeLuminance(color2);
    
    const lighter = Math.max(lum1, lum2);
    const darker = Math.min(lum1, lum2);
    
    return (lighter + 0.05) / (darker + 0.05);
  }

  /**
   * Check if color combination meets WCAG AA standards
   */
  static meetsWCAGAA(
    foreground: string,
    background: string,
    isLargeText: boolean = false
  ): boolean {
    const ratio = this.getContrastRatio(foreground, background);
    const requiredRatio = isLargeText 
      ? WCAG_CONSTANTS.CONTRAST_RATIOS.LARGE_TEXT 
      : WCAG_CONSTANTS.CONTRAST_RATIOS.NORMAL_TEXT;
    
    return ratio >= requiredRatio;
  }

  /**
   * Suggest accessible color alternatives
   */
  static suggestAccessibleColor(
    foreground: string,
    background: string,
    isLargeText: boolean = false
  ): string | null {
    if (this.meetsWCAGAA(foreground, background, isLargeText)) {
      return null; // Already accessible
    }

    // Simple algorithm to darken/lighten color
    // In production, this would be more sophisticated
    const hex = foreground.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);

    // Try multiple darkening factors
    const factors = [0.7, 0.5, 0.3, 0.1];

    for (const factor of factors) {
      const newR = Math.floor(r * factor);
      const newG = Math.floor(g * factor);
      const newB = Math.floor(b * factor);

      const newColor = `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;

      if (this.meetsWCAGAA(newColor, background, isLargeText)) {
        return newColor;
      }
    }

    // If darkening doesn't work, try pure black
    return this.meetsWCAGAA('#000000', background, isLargeText) ? '#000000' : null;
  }
}

// Focus management utilities
export class FocusManagementUtils {
  private static focusStack: number[] = [];

  /**
   * Push focus to stack and set new focus
   */
  static pushFocus(reactTag: number): void {
    // Store current focus (simplified - in production would get actual focused element)
    this.focusStack.push(reactTag);
    ScreenReaderUtils.setAccessibilityFocus(reactTag);
  }

  /**
   * Pop focus from stack and restore previous focus
   */
  static popFocus(): void {
    this.focusStack.pop(); // Remove current
    const previousFocus = this.focusStack[this.focusStack.length - 1];
    
    if (previousFocus) {
      ScreenReaderUtils.setAccessibilityFocus(previousFocus);
    }
  }

  /**
   * Clear focus stack
   */
  static clearFocusStack(): void {
    this.focusStack = [];
  }

  /**
   * Generate focus trap for modals
   */
  static createFocusTrap(firstElement: number, lastElement: number) {
    return {
      onKeyPress: (event: any) => {
        if (event.key === 'Tab') {
          if (event.shiftKey) {
            // Shift+Tab - move to previous element
            if (event.target === firstElement) {
              event.preventDefault();
              ScreenReaderUtils.setAccessibilityFocus(lastElement);
            }
          } else {
            // Tab - move to next element
            if (event.target === lastElement) {
              event.preventDefault();
              ScreenReaderUtils.setAccessibilityFocus(firstElement);
            }
          }
        }
      },
    };
  }
}

// Semantic markup helpers
export class SemanticMarkupUtils {
  /**
   * Generate semantic props for headings
   */
  static generateHeadingProps(level: 1 | 2 | 3 | 4 | 5 | 6, text: string) {
    return {
      accessibilityRole: 'header' as AccessibilityRole,
      accessibilityLevel: level,
      accessibilityLabel: text,
    };
  }

  /**
   * Generate semantic props for lists
   */
  static generateListProps(itemCount: number) {
    return {
      accessibilityRole: 'list' as AccessibilityRole,
      accessibilityLabel: `List with ${itemCount} items`,
    };
  }

  /**
   * Generate semantic props for list items
   */
  static generateListItemProps(index: number, total: number, content: string) {
    return {
      accessibilityRole: 'listitem' as AccessibilityRole,
      accessibilityLabel: `${content}, ${index + 1} of ${total}`,
    };
  }

  /**
   * Generate semantic props for buttons
   */
  static generateButtonProps(
    label: string,
    action?: string,
    state?: AccessibilityState
  ) {
    return {
      accessibilityRole: 'button' as AccessibilityRole,
      accessibilityLabel: label,
      accessibilityHint: action ? ScreenReaderUtils.generateInteractionHint(action) : undefined,
      accessibilityState: state,
    };
  }

  /**
   * Generate semantic props for form inputs
   */
  static generateInputProps(
    label: string,
    value?: string,
    required: boolean = false,
    error?: string
  ) {
    return {
      accessibilityLabel: ScreenReaderUtils.generateFormFieldLabel(label, required, error),
      accessibilityValue: value ? { text: value } : undefined,
      accessibilityState: {
        disabled: false,
      } as AccessibilityState,
    };
  }
}

// Valid React Native accessibility roles
export const VALID_ACCESSIBILITY_ROLES: AccessibilityRole[] = [
  'button',
  'link',
  'search',
  'image',
  'keyboardkey',
  'text',
  'adjustable',
  'imagebutton',
  'header',
  'summary',
  'alert',
  'checkbox',
  'combobox',
  'menu',
  'menubar',
  'menuitem',
  'progressbar',
  'radio',
  'radiogroup',
  'scrollbar',
  'spinbutton',
  'switch',
  'tab',
  'tablist',
  'timer',
  'toolbar',
  'grid',
  'list',
  'listitem',
  'none'
];

/**
 * Validate if an accessibility role is supported in React Native
 */
export const isValidAccessibilityRole = (role: string): boolean => {
  return VALID_ACCESSIBILITY_ROLES.includes(role as AccessibilityRole);
};

/**
 * Get a safe accessibility role, falling back to 'none' for invalid roles
 */
export const getSafeAccessibilityRole = (role: string): AccessibilityRole => {
  return isValidAccessibilityRole(role) ? (role as AccessibilityRole) : 'none';
};

// Accessibility testing utilities
export class AccessibilityTestUtils {
  /**
   * Validate component accessibility props
   */
  static validateAccessibilityProps(props: any): string[] {
    const issues: string[] = [];

    // Check for missing accessibility role
    if (!props.accessibilityRole && props.onPress) {
      issues.push('Interactive element missing accessibilityRole');
    }

    // Check for invalid accessibility role
    if (props.accessibilityRole && !isValidAccessibilityRole(props.accessibilityRole)) {
      issues.push(`Invalid accessibility role: ${props.accessibilityRole}. Use one of: ${VALID_ACCESSIBILITY_ROLES.join(', ')}`);
    }

    // Check for missing accessibility label
    if (!props.accessibilityLabel && !props.children) {
      issues.push('Element missing accessibilityLabel or text content');
    }

    // Check touch target size
    if (props.style?.width && props.style?.height) {
      const width = props.style.width;
      const height = props.style.height;
      
      if (width < WCAG_CONSTANTS.TOUCH_TARGET.MIN_SIZE || 
          height < WCAG_CONSTANTS.TOUCH_TARGET.MIN_SIZE) {
        issues.push(`Touch target too small: ${width}x${height}. Minimum: ${WCAG_CONSTANTS.TOUCH_TARGET.MIN_SIZE}x${WCAG_CONSTANTS.TOUCH_TARGET.MIN_SIZE}`);
      }
    }

    return issues;
  }

  /**
   * Generate accessibility report for component tree
   */
  static generateAccessibilityReport(componentTree: any[]): {
    passed: number;
    failed: number;
    issues: Array<{ component: string; issues: string[] }>;
  } {
    let passed = 0;
    let failed = 0;
    const issues: Array<{ component: string; issues: string[] }> = [];

    componentTree.forEach((component, index) => {
      const componentIssues = this.validateAccessibilityProps(component.props);
      
      if (componentIssues.length === 0) {
        passed++;
      } else {
        failed++;
        issues.push({
          component: component.type || `Component ${index}`,
          issues: componentIssues,
        });
      }
    });

    return { passed, failed, issues };
  }
}

// Advanced accessibility features
export const AdvancedAccessibilityUtils = {
  // Live region announcements with priority
  announceLiveRegion: (message: string, priority: 'polite' | 'assertive' = 'polite') => {
    if (Platform.OS === 'ios') {
      AccessibilityInfo.announceForAccessibility(message);
    } else {
      // For Android, we can simulate live regions
      setTimeout(() => {
        AccessibilityInfo.announceForAccessibility(message);
      }, priority === 'assertive' ? 0 : 100);
    }
  },

  // Dynamic content updates
  announceContentChange: (
    element: string,
    changeType: 'added' | 'removed' | 'updated',
    details?: string
  ) => {
    const message = `${element} ${changeType}${details ? `: ${details}` : ''}`;
    AdvancedAccessibilityUtils.announceLiveRegion(message, 'polite');
  },

  // Form validation announcements
  announceFormValidation: (
    fieldName: string,
    isValid: boolean,
    errorMessage?: string
  ) => {
    if (isValid) {
      AdvancedAccessibilityUtils.announceLiveRegion(`${fieldName} is valid`, 'polite');
    } else {
      AdvancedAccessibilityUtils.announceLiveRegion(
        `${fieldName} error: ${errorMessage || 'Invalid input'}`,
        'assertive'
      );
    }
  },

  // Progress announcements
  announceProgress: (
    current: number,
    total: number,
    label: string = 'Progress'
  ) => {
    const percentage = Math.round((current / total) * 100);
    AdvancedAccessibilityUtils.announceLiveRegion(
      `${label}: ${percentage}% complete, ${current} of ${total}`,
      'polite'
    );
  },

  // Loading state announcements
  announceLoadingState: (
    isLoading: boolean,
    context: string = 'Content'
  ) => {
    if (isLoading) {
      AdvancedAccessibilityUtils.announceLiveRegion(`${context} loading`, 'polite');
    } else {
      AdvancedAccessibilityUtils.announceLiveRegion(`${context} loaded`, 'polite');
    }
  },
};

// Accessibility performance monitoring
export const AccessibilityMonitoringUtils = {
  // Track accessibility interactions
  trackAccessibilityUsage: async () => {
    const stats = {
      screenReaderUsage: 0,
      keyboardNavigation: 0,
      voiceControlUsage: 0,
      reducedMotionPreference: false,
    };

    try {
      // Monitor screen reader usage
      stats.screenReaderUsage = await AccessibilityInfo.isScreenReaderEnabled() ? 1 : 0;

      // Monitor reduced motion preference
      if (Platform.OS === 'ios') {
        stats.reducedMotionPreference = await AccessibilityInfo.isReduceMotionEnabled();
      }
    } catch (error) {
      console.warn('Accessibility usage tracking failed:', error);
    }

    return stats;
  },

  // Validate accessibility compliance
  validateCompliance: async () => {
    const compliance = {
      screenReaderSupport: false,
      keyboardNavigation: true, // Assume true for React Native
      colorContrast: true, // Would need specific color checking
      touchTargets: true, // Would need specific size checking
      textScaling: true, // React Native handles this
      reducedMotion: false,
    };

    try {
      compliance.screenReaderSupport = await AccessibilityInfo.isScreenReaderEnabled();

      if (Platform.OS === 'ios') {
        compliance.reducedMotion = await AccessibilityInfo.isReduceMotionEnabled();
      }
    } catch (error) {
      console.warn('Accessibility compliance check failed:', error);
    }

    return compliance;
  },
};

// Export all utilities
export {
  ScreenReaderUtils,
  ColorContrastUtils,
  FocusManagementUtils,
  SemanticMarkupUtils,
  AccessibilityTestUtils,
  AdvancedAccessibilityUtils,
  AccessibilityMonitoringUtils,
};
