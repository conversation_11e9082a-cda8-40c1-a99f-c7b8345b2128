{"version": 3, "names": ["PerformanceMonitor", "exports", "_classCallCheck2", "default", "metrics", "observers", "Map", "initializeObservers", "_createClass2", "key", "value", "_this", "window", "navObserver", "PerformanceObserver", "list", "getEntries", "for<PERSON>ach", "entry", "entryType", "recordNavigationTiming", "observe", "entryTypes", "set", "error", "console", "warn", "paintObserver", "recordMetric", "name", "startTime", "timestamp", "Date", "now", "type", "tags", "lcpObserver", "entries", "lastEntry", "length", "fidObserver", "processingStart", "timing", "navigationStart", "domContentLoadedEventEnd", "loadEventEnd", "domContentLoaded", "pageLoad", "metric", "push", "slice", "__DEV__", "log", "getMetrics", "filter", "_toConsumableArray2", "getSummary", "summary", "avg", "min", "Infinity", "max", "count", "stat", "Math", "clearMetrics", "dispose", "observer", "disconnect", "clear", "getInstance", "instance", "PerformanceTimer", "performance", "end", "duration", "CriticalCSSOptimizer", "setCriticalCSS", "css", "criticalCSS", "addNonCriticalCSS", "cssUrl", "nonCriticalCSS", "loadCSSAsync", "document", "link", "createElement", "rel", "href", "media", "onload", "head", "append<PERSON><PERSON><PERSON>", "getCriticalCSS", "preloadCriticalResources", "resources", "resource", "as", "CodeSplittingUtils", "_importModule", "_asyncToGenerator2", "importFn", "chunkName", "timer", "module", "chunk", "status", "loadedChunks", "add", "importModule", "_x", "_x2", "apply", "arguments", "preloadChunk", "_this2", "has", "requestIdleCallback", "catch", "setTimeout", "getLoadedChunks", "Array", "from", "Set", "MemoryMonitor", "getMemoryUsage", "memory", "used", "usedJSHeapSize", "total", "totalJSHeapSize", "percentage", "startMemoryMonitoring", "_this3", "threshold", "undefined", "checkMemory", "usage", "toFixed", "toString", "setInterval", "performanceMonitor", "measurePerformance", "withPerformanceTracking", "fn", "result", "then", "finally"], "sources": ["performance.ts"], "sourcesContent": ["/**\n * Performance Monitoring Utilities\n * \n * Comprehensive performance monitoring and optimization utilities.\n * Implements performance tracking, metrics collection, and optimization helpers.\n * \n * Features:\n * - Performance metrics collection\n * - Bundle size analysis\n * - Memory usage monitoring\n * - Network performance tracking\n * - Critical rendering path optimization\n * - Code splitting utilities\n * \n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\n// Performance metric types\nexport interface PerformanceMetric {\n  name: string;\n  value: number;\n  timestamp: number;\n  type: 'timing' | 'counter' | 'gauge';\n  tags?: Record<string, string>;\n}\n\nexport interface NavigationTiming {\n  navigationStart: number;\n  domContentLoadedEventEnd: number;\n  loadEventEnd: number;\n  firstPaint?: number;\n  firstContentfulPaint?: number;\n  largestContentfulPaint?: number;\n  firstInputDelay?: number;\n  cumulativeLayoutShift?: number;\n}\n\nexport interface BundleAnalysis {\n  totalSize: number;\n  gzippedSize: number;\n  chunks: Array<{\n    name: string;\n    size: number;\n    modules: string[];\n  }>;\n  duplicateModules: string[];\n}\n\n// Performance monitoring class\nexport class PerformanceMonitor {\n  private static instance: PerformanceMonitor;\n  private metrics: PerformanceMetric[] = [];\n  private observers: Map<string, PerformanceObserver> = new Map();\n  \n  private constructor() {\n    this.initializeObservers();\n  }\n  \n  static getInstance(): PerformanceMonitor {\n    if (!PerformanceMonitor.instance) {\n      PerformanceMonitor.instance = new PerformanceMonitor();\n    }\n    return PerformanceMonitor.instance;\n  }\n  \n  /**\n   * Initialize performance observers\n   */\n  private initializeObservers(): void {\n    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {\n      return;\n    }\n    \n    // Navigation timing observer\n    try {\n      const navObserver = new PerformanceObserver((list) => {\n        list.getEntries().forEach((entry) => {\n          if (entry.entryType === 'navigation') {\n            this.recordNavigationTiming(entry as PerformanceNavigationTiming);\n          }\n        });\n      });\n      navObserver.observe({ entryTypes: ['navigation'] });\n      this.observers.set('navigation', navObserver);\n    } catch (error) {\n      console.warn('Navigation timing observer not supported:', error);\n    }\n    \n    // Paint timing observer\n    try {\n      const paintObserver = new PerformanceObserver((list) => {\n        list.getEntries().forEach((entry) => {\n          this.recordMetric({\n            name: entry.name,\n            value: entry.startTime,\n            timestamp: Date.now(),\n            type: 'timing',\n            tags: { type: 'paint' },\n          });\n        });\n      });\n      paintObserver.observe({ entryTypes: ['paint'] });\n      this.observers.set('paint', paintObserver);\n    } catch (error) {\n      console.warn('Paint timing observer not supported:', error);\n    }\n    \n    // Largest Contentful Paint observer\n    try {\n      const lcpObserver = new PerformanceObserver((list) => {\n        const entries = list.getEntries();\n        const lastEntry = entries[entries.length - 1];\n        this.recordMetric({\n          name: 'largest-contentful-paint',\n          value: lastEntry.startTime,\n          timestamp: Date.now(),\n          type: 'timing',\n          tags: { type: 'lcp' },\n        });\n      });\n      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });\n      this.observers.set('lcp', lcpObserver);\n    } catch (error) {\n      console.warn('LCP observer not supported:', error);\n    }\n    \n    // First Input Delay observer\n    try {\n      const fidObserver = new PerformanceObserver((list) => {\n        list.getEntries().forEach((entry) => {\n          this.recordMetric({\n            name: 'first-input-delay',\n            value: (entry as any).processingStart - entry.startTime,\n            timestamp: Date.now(),\n            type: 'timing',\n            tags: { type: 'fid' },\n          });\n        });\n      });\n      fidObserver.observe({ entryTypes: ['first-input'] });\n      this.observers.set('fid', fidObserver);\n    } catch (error) {\n      console.warn('FID observer not supported:', error);\n    }\n  }\n  \n  /**\n   * Record navigation timing metrics\n   */\n  private recordNavigationTiming(entry: PerformanceNavigationTiming): void {\n    const timing: NavigationTiming = {\n      navigationStart: entry.navigationStart,\n      domContentLoadedEventEnd: entry.domContentLoadedEventEnd,\n      loadEventEnd: entry.loadEventEnd,\n    };\n    \n    // Calculate derived metrics\n    const domContentLoaded = timing.domContentLoadedEventEnd - timing.navigationStart;\n    const pageLoad = timing.loadEventEnd - timing.navigationStart;\n    \n    this.recordMetric({\n      name: 'dom-content-loaded',\n      value: domContentLoaded,\n      timestamp: Date.now(),\n      type: 'timing',\n      tags: { type: 'navigation' },\n    });\n    \n    this.recordMetric({\n      name: 'page-load',\n      value: pageLoad,\n      timestamp: Date.now(),\n      type: 'timing',\n      tags: { type: 'navigation' },\n    });\n  }\n  \n  /**\n   * Record a performance metric\n   */\n  recordMetric(metric: PerformanceMetric): void {\n    this.metrics.push(metric);\n    \n    // Keep only last 1000 metrics to prevent memory leaks\n    if (this.metrics.length > 1000) {\n      this.metrics = this.metrics.slice(-1000);\n    }\n    \n    // Log in development\n    if (__DEV__) {\n      console.log(`[Performance] ${metric.name}: ${metric.value}ms`, metric.tags);\n    }\n  }\n  \n  /**\n   * Get metrics by name\n   */\n  getMetrics(name?: string): PerformanceMetric[] {\n    if (name) {\n      return this.metrics.filter(metric => metric.name === name);\n    }\n    return [...this.metrics];\n  }\n  \n  /**\n   * Get performance summary\n   */\n  getSummary(): Record<string, { avg: number; min: number; max: number; count: number }> {\n    const summary: Record<string, { avg: number; min: number; max: number; count: number }> = {};\n    \n    this.metrics.forEach(metric => {\n      if (!summary[metric.name]) {\n        summary[metric.name] = {\n          avg: 0,\n          min: Infinity,\n          max: -Infinity,\n          count: 0,\n        };\n      }\n      \n      const stat = summary[metric.name];\n      stat.count++;\n      stat.min = Math.min(stat.min, metric.value);\n      stat.max = Math.max(stat.max, metric.value);\n      stat.avg = (stat.avg * (stat.count - 1) + metric.value) / stat.count;\n    });\n    \n    return summary;\n  }\n  \n  /**\n   * Clear all metrics\n   */\n  clearMetrics(): void {\n    this.metrics = [];\n  }\n  \n  /**\n   * Dispose of observers\n   */\n  dispose(): void {\n    this.observers.forEach(observer => observer.disconnect());\n    this.observers.clear();\n    this.metrics = [];\n  }\n}\n\n// Performance timing utilities\nexport class PerformanceTimer {\n  private startTime: number;\n  private name: string;\n  \n  constructor(name: string) {\n    this.name = name;\n    this.startTime = performance.now();\n  }\n  \n  /**\n   * End timing and record metric\n   */\n  end(tags?: Record<string, string>): number {\n    const duration = performance.now() - this.startTime;\n    \n    PerformanceMonitor.getInstance().recordMetric({\n      name: this.name,\n      value: duration,\n      timestamp: Date.now(),\n      type: 'timing',\n      tags,\n    });\n    \n    return duration;\n  }\n}\n\n// Critical CSS utilities\nexport class CriticalCSSOptimizer {\n  private static criticalCSS: string = '';\n  private static nonCriticalCSS: string[] = [];\n  \n  /**\n   * Set critical CSS that should be inlined\n   */\n  static setCriticalCSS(css: string): void {\n    this.criticalCSS = css;\n  }\n  \n  /**\n   * Add non-critical CSS to be loaded asynchronously\n   */\n  static addNonCriticalCSS(cssUrl: string): void {\n    this.nonCriticalCSS.push(cssUrl);\n    this.loadCSSAsync(cssUrl);\n  }\n  \n  /**\n   * Load CSS asynchronously\n   */\n  private static loadCSSAsync(cssUrl: string): void {\n    if (typeof document === 'undefined') return;\n    \n    const link = document.createElement('link');\n    link.rel = 'stylesheet';\n    link.href = cssUrl;\n    link.media = 'print';\n    link.onload = () => {\n      link.media = 'all';\n    };\n    \n    document.head.appendChild(link);\n  }\n  \n  /**\n   * Get critical CSS for inlining\n   */\n  static getCriticalCSS(): string {\n    return this.criticalCSS;\n  }\n  \n  /**\n   * Preload critical resources\n   */\n  static preloadCriticalResources(resources: Array<{ href: string; as: string; type?: string }>): void {\n    if (typeof document === 'undefined') return;\n    \n    resources.forEach(resource => {\n      const link = document.createElement('link');\n      link.rel = 'preload';\n      link.href = resource.href;\n      link.as = resource.as;\n      if (resource.type) {\n        link.type = resource.type;\n      }\n      \n      document.head.appendChild(link);\n    });\n  }\n}\n\n// Code splitting utilities\nexport class CodeSplittingUtils {\n  private static loadedChunks: Set<string> = new Set();\n  \n  /**\n   * Dynamically import a module with performance tracking\n   */\n  static async importModule<T>(\n    importFn: () => Promise<T>,\n    chunkName: string\n  ): Promise<T> {\n    const timer = new PerformanceTimer(`chunk-load-${chunkName}`);\n    \n    try {\n      const module = await importFn();\n      timer.end({ chunk: chunkName, status: 'success' });\n      this.loadedChunks.add(chunkName);\n      return module;\n    } catch (error) {\n      timer.end({ chunk: chunkName, status: 'error' });\n      throw error;\n    }\n  }\n  \n  /**\n   * Preload a chunk\n   */\n  static preloadChunk(importFn: () => Promise<any>, chunkName: string): void {\n    if (this.loadedChunks.has(chunkName)) return;\n    \n    // Preload with low priority\n    if ('requestIdleCallback' in window) {\n      requestIdleCallback(() => {\n        this.importModule(importFn, chunkName).catch(() => {\n          // Ignore preload errors\n        });\n      });\n    } else {\n      setTimeout(() => {\n        this.importModule(importFn, chunkName).catch(() => {\n          // Ignore preload errors\n        });\n      }, 100);\n    }\n  }\n  \n  /**\n   * Get loaded chunks\n   */\n  static getLoadedChunks(): string[] {\n    return Array.from(this.loadedChunks);\n  }\n}\n\n// Memory monitoring utilities\nexport class MemoryMonitor {\n  /**\n   * Get memory usage information\n   */\n  static getMemoryUsage(): {\n    used: number;\n    total: number;\n    percentage: number;\n  } | null {\n    if (typeof window === 'undefined' || !('performance' in window) || !(performance as any).memory) {\n      return null;\n    }\n    \n    const memory = (performance as any).memory;\n    return {\n      used: memory.usedJSHeapSize,\n      total: memory.totalJSHeapSize,\n      percentage: (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100,\n    };\n  }\n  \n  /**\n   * Monitor memory usage and warn if high\n   */\n  static startMemoryMonitoring(threshold: number = 80): void {\n    if (typeof window === 'undefined') return;\n    \n    const checkMemory = () => {\n      const usage = this.getMemoryUsage();\n      if (usage && usage.percentage > threshold) {\n        console.warn(`High memory usage: ${usage.percentage.toFixed(1)}%`);\n        \n        PerformanceMonitor.getInstance().recordMetric({\n          name: 'memory-usage-high',\n          value: usage.percentage,\n          timestamp: Date.now(),\n          type: 'gauge',\n          tags: { threshold: threshold.toString() },\n        });\n      }\n    };\n    \n    // Check every 30 seconds\n    setInterval(checkMemory, 30000);\n  }\n}\n\n// Export singleton instance\nexport const performanceMonitor = PerformanceMonitor.getInstance();\n\n// Utility functions\nexport const measurePerformance = (name: string) => new PerformanceTimer(name);\n\nexport const withPerformanceTracking = <T extends (...args: any[]) => any>(\n  fn: T,\n  name: string\n): T => {\n  return ((...args: any[]) => {\n    const timer = new PerformanceTimer(name);\n    try {\n      const result = fn(...args);\n      \n      // Handle async functions\n      if (result && typeof result.then === 'function') {\n        return result.finally(() => timer.end());\n      }\n      \n      timer.end();\n      return result;\n    } catch (error) {\n      timer.end({ status: 'error' });\n      throw error;\n    }\n  }) as T;\n};\n"], "mappings": ";;;;;;;;;IAkDaA,kBAAkB,GAAAC,OAAA,CAAAD,kBAAA;EAK7B,SAAAA,mBAAA,EAAsB;IAAA,IAAAE,gBAAA,CAAAC,OAAA,QAAAH,kBAAA;IAAA,KAHdI,OAAO,GAAwB,EAAE;IAAA,KACjCC,SAAS,GAAqC,IAAIC,GAAG,CAAC,CAAC;IAG7D,IAAI,CAACC,mBAAmB,CAAC,CAAC;EAC5B;EAAC,WAAAC,aAAA,CAAAL,OAAA,EAAAH,kBAAA;IAAAS,GAAA;IAAAC,KAAA,EAYD,SAAQH,mBAAmBA,CAAA,EAAS;MAAA,IAAAI,KAAA;MAClC,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAI,EAAE,qBAAqB,IAAIA,MAAM,CAAC,EAAE;QACvE;MACF;MAGA,IAAI;QACF,IAAMC,WAAW,GAAG,IAAIC,mBAAmB,CAAC,UAACC,IAAI,EAAK;UACpDA,IAAI,CAACC,UAAU,CAAC,CAAC,CAACC,OAAO,CAAC,UAACC,KAAK,EAAK;YACnC,IAAIA,KAAK,CAACC,SAAS,KAAK,YAAY,EAAE;cACpCR,KAAI,CAACS,sBAAsB,CAACF,KAAoC,CAAC;YACnE;UACF,CAAC,CAAC;QACJ,CAAC,CAAC;QACFL,WAAW,CAACQ,OAAO,CAAC;UAAEC,UAAU,EAAE,CAAC,YAAY;QAAE,CAAC,CAAC;QACnD,IAAI,CAACjB,SAAS,CAACkB,GAAG,CAAC,YAAY,EAAEV,WAAW,CAAC;MAC/C,CAAC,CAAC,OAAOW,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,2CAA2C,EAAEF,KAAK,CAAC;MAClE;MAGA,IAAI;QACF,IAAMG,aAAa,GAAG,IAAIb,mBAAmB,CAAC,UAACC,IAAI,EAAK;UACtDA,IAAI,CAACC,UAAU,CAAC,CAAC,CAACC,OAAO,CAAC,UAACC,KAAK,EAAK;YACnCP,KAAI,CAACiB,YAAY,CAAC;cAChBC,IAAI,EAAEX,KAAK,CAACW,IAAI;cAChBnB,KAAK,EAAEQ,KAAK,CAACY,SAAS;cACtBC,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;cACrBC,IAAI,EAAE,QAAQ;cACdC,IAAI,EAAE;gBAAED,IAAI,EAAE;cAAQ;YACxB,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ,CAAC,CAAC;QACFP,aAAa,CAACN,OAAO,CAAC;UAAEC,UAAU,EAAE,CAAC,OAAO;QAAE,CAAC,CAAC;QAChD,IAAI,CAACjB,SAAS,CAACkB,GAAG,CAAC,OAAO,EAAEI,aAAa,CAAC;MAC5C,CAAC,CAAC,OAAOH,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,sCAAsC,EAAEF,KAAK,CAAC;MAC7D;MAGA,IAAI;QACF,IAAMY,WAAW,GAAG,IAAItB,mBAAmB,CAAC,UAACC,IAAI,EAAK;UACpD,IAAMsB,OAAO,GAAGtB,IAAI,CAACC,UAAU,CAAC,CAAC;UACjC,IAAMsB,SAAS,GAAGD,OAAO,CAACA,OAAO,CAACE,MAAM,GAAG,CAAC,CAAC;UAC7C5B,KAAI,CAACiB,YAAY,CAAC;YAChBC,IAAI,EAAE,0BAA0B;YAChCnB,KAAK,EAAE4B,SAAS,CAACR,SAAS;YAC1BC,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;YACrBC,IAAI,EAAE,QAAQ;YACdC,IAAI,EAAE;cAAED,IAAI,EAAE;YAAM;UACtB,CAAC,CAAC;QACJ,CAAC,CAAC;QACFE,WAAW,CAACf,OAAO,CAAC;UAAEC,UAAU,EAAE,CAAC,0BAA0B;QAAE,CAAC,CAAC;QACjE,IAAI,CAACjB,SAAS,CAACkB,GAAG,CAAC,KAAK,EAAEa,WAAW,CAAC;MACxC,CAAC,CAAC,OAAOZ,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,6BAA6B,EAAEF,KAAK,CAAC;MACpD;MAGA,IAAI;QACF,IAAMgB,WAAW,GAAG,IAAI1B,mBAAmB,CAAC,UAACC,IAAI,EAAK;UACpDA,IAAI,CAACC,UAAU,CAAC,CAAC,CAACC,OAAO,CAAC,UAACC,KAAK,EAAK;YACnCP,KAAI,CAACiB,YAAY,CAAC;cAChBC,IAAI,EAAE,mBAAmB;cACzBnB,KAAK,EAAGQ,KAAK,CAASuB,eAAe,GAAGvB,KAAK,CAACY,SAAS;cACvDC,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;cACrBC,IAAI,EAAE,QAAQ;cACdC,IAAI,EAAE;gBAAED,IAAI,EAAE;cAAM;YACtB,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ,CAAC,CAAC;QACFM,WAAW,CAACnB,OAAO,CAAC;UAAEC,UAAU,EAAE,CAAC,aAAa;QAAE,CAAC,CAAC;QACpD,IAAI,CAACjB,SAAS,CAACkB,GAAG,CAAC,KAAK,EAAEiB,WAAW,CAAC;MACxC,CAAC,CAAC,OAAOhB,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,6BAA6B,EAAEF,KAAK,CAAC;MACpD;IACF;EAAC;IAAAf,GAAA;IAAAC,KAAA,EAKD,SAAQU,sBAAsBA,CAACF,KAAkC,EAAQ;MACvE,IAAMwB,MAAwB,GAAG;QAC/BC,eAAe,EAAEzB,KAAK,CAACyB,eAAe;QACtCC,wBAAwB,EAAE1B,KAAK,CAAC0B,wBAAwB;QACxDC,YAAY,EAAE3B,KAAK,CAAC2B;MACtB,CAAC;MAGD,IAAMC,gBAAgB,GAAGJ,MAAM,CAACE,wBAAwB,GAAGF,MAAM,CAACC,eAAe;MACjF,IAAMI,QAAQ,GAAGL,MAAM,CAACG,YAAY,GAAGH,MAAM,CAACC,eAAe;MAE7D,IAAI,CAACf,YAAY,CAAC;QAChBC,IAAI,EAAE,oBAAoB;QAC1BnB,KAAK,EAAEoC,gBAAgB;QACvBf,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;QACrBC,IAAI,EAAE,QAAQ;QACdC,IAAI,EAAE;UAAED,IAAI,EAAE;QAAa;MAC7B,CAAC,CAAC;MAEF,IAAI,CAACN,YAAY,CAAC;QAChBC,IAAI,EAAE,WAAW;QACjBnB,KAAK,EAAEqC,QAAQ;QACfhB,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;QACrBC,IAAI,EAAE,QAAQ;QACdC,IAAI,EAAE;UAAED,IAAI,EAAE;QAAa;MAC7B,CAAC,CAAC;IACJ;EAAC;IAAAzB,GAAA;IAAAC,KAAA,EAKD,SAAAkB,YAAYA,CAACoB,MAAyB,EAAQ;MAC5C,IAAI,CAAC5C,OAAO,CAAC6C,IAAI,CAACD,MAAM,CAAC;MAGzB,IAAI,IAAI,CAAC5C,OAAO,CAACmC,MAAM,GAAG,IAAI,EAAE;QAC9B,IAAI,CAACnC,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC8C,KAAK,CAAC,CAAC,IAAI,CAAC;MAC1C;MAGA,IAAIC,OAAO,EAAE;QACX1B,OAAO,CAAC2B,GAAG,CAAC,iBAAiBJ,MAAM,CAACnB,IAAI,KAAKmB,MAAM,CAACtC,KAAK,IAAI,EAAEsC,MAAM,CAACb,IAAI,CAAC;MAC7E;IACF;EAAC;IAAA1B,GAAA;IAAAC,KAAA,EAKD,SAAA2C,UAAUA,CAACxB,IAAa,EAAuB;MAC7C,IAAIA,IAAI,EAAE;QACR,OAAO,IAAI,CAACzB,OAAO,CAACkD,MAAM,CAAC,UAAAN,MAAM;UAAA,OAAIA,MAAM,CAACnB,IAAI,KAAKA,IAAI;QAAA,EAAC;MAC5D;MACA,WAAA0B,mBAAA,CAAApD,OAAA,EAAW,IAAI,CAACC,OAAO;IACzB;EAAC;IAAAK,GAAA;IAAAC,KAAA,EAKD,SAAA8C,UAAUA,CAAA,EAA6E;MACrF,IAAMC,OAAiF,GAAG,CAAC,CAAC;MAE5F,IAAI,CAACrD,OAAO,CAACa,OAAO,CAAC,UAAA+B,MAAM,EAAI;QAC7B,IAAI,CAACS,OAAO,CAACT,MAAM,CAACnB,IAAI,CAAC,EAAE;UACzB4B,OAAO,CAACT,MAAM,CAACnB,IAAI,CAAC,GAAG;YACrB6B,GAAG,EAAE,CAAC;YACNC,GAAG,EAAEC,QAAQ;YACbC,GAAG,EAAE,CAACD,QAAQ;YACdE,KAAK,EAAE;UACT,CAAC;QACH;QAEA,IAAMC,IAAI,GAAGN,OAAO,CAACT,MAAM,CAACnB,IAAI,CAAC;QACjCkC,IAAI,CAACD,KAAK,EAAE;QACZC,IAAI,CAACJ,GAAG,GAAGK,IAAI,CAACL,GAAG,CAACI,IAAI,CAACJ,GAAG,EAAEX,MAAM,CAACtC,KAAK,CAAC;QAC3CqD,IAAI,CAACF,GAAG,GAAGG,IAAI,CAACH,GAAG,CAACE,IAAI,CAACF,GAAG,EAAEb,MAAM,CAACtC,KAAK,CAAC;QAC3CqD,IAAI,CAACL,GAAG,GAAG,CAACK,IAAI,CAACL,GAAG,IAAIK,IAAI,CAACD,KAAK,GAAG,CAAC,CAAC,GAAGd,MAAM,CAACtC,KAAK,IAAIqD,IAAI,CAACD,KAAK;MACtE,CAAC,CAAC;MAEF,OAAOL,OAAO;IAChB;EAAC;IAAAhD,GAAA;IAAAC,KAAA,EAKD,SAAAuD,YAAYA,CAAA,EAAS;MACnB,IAAI,CAAC7D,OAAO,GAAG,EAAE;IACnB;EAAC;IAAAK,GAAA;IAAAC,KAAA,EAKD,SAAAwD,OAAOA,CAAA,EAAS;MACd,IAAI,CAAC7D,SAAS,CAACY,OAAO,CAAC,UAAAkD,QAAQ;QAAA,OAAIA,QAAQ,CAACC,UAAU,CAAC,CAAC;MAAA,EAAC;MACzD,IAAI,CAAC/D,SAAS,CAACgE,KAAK,CAAC,CAAC;MACtB,IAAI,CAACjE,OAAO,GAAG,EAAE;IACnB;EAAC;IAAAK,GAAA;IAAAC,KAAA,EA1LD,SAAO4D,WAAWA,CAAA,EAAuB;MACvC,IAAI,CAACtE,kBAAkB,CAACuE,QAAQ,EAAE;QAChCvE,kBAAkB,CAACuE,QAAQ,GAAG,IAAIvE,kBAAkB,CAAC,CAAC;MACxD;MACA,OAAOA,kBAAkB,CAACuE,QAAQ;IACpC;EAAC;AAAA;AAAA,IAyLUC,gBAAgB,GAAAvE,OAAA,CAAAuE,gBAAA;EAI3B,SAAAA,iBAAY3C,IAAY,EAAE;IAAA,IAAA3B,gBAAA,CAAAC,OAAA,QAAAqE,gBAAA;IACxB,IAAI,CAAC3C,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,SAAS,GAAG2C,WAAW,CAACxC,GAAG,CAAC,CAAC;EACpC;EAAC,WAAAzB,aAAA,CAAAL,OAAA,EAAAqE,gBAAA;IAAA/D,GAAA;IAAAC,KAAA,EAKD,SAAAgE,GAAGA,CAACvC,IAA6B,EAAU;MACzC,IAAMwC,QAAQ,GAAGF,WAAW,CAACxC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACH,SAAS;MAEnD9B,kBAAkB,CAACsE,WAAW,CAAC,CAAC,CAAC1C,YAAY,CAAC;QAC5CC,IAAI,EAAE,IAAI,CAACA,IAAI;QACfnB,KAAK,EAAEiE,QAAQ;QACf5C,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;QACrBC,IAAI,EAAE,QAAQ;QACdC,IAAI,EAAJA;MACF,CAAC,CAAC;MAEF,OAAOwC,QAAQ;IACjB;EAAC;AAAA;AAAA,IAIUC,oBAAoB,GAAA3E,OAAA,CAAA2E,oBAAA;EAAA,SAAAA,qBAAA;IAAA,IAAA1E,gBAAA,CAAAC,OAAA,QAAAyE,oBAAA;EAAA;EAAA,WAAApE,aAAA,CAAAL,OAAA,EAAAyE,oBAAA;IAAAnE,GAAA;IAAAC,KAAA,EAO/B,SAAOmE,cAAcA,CAACC,GAAW,EAAQ;MACvC,IAAI,CAACC,WAAW,GAAGD,GAAG;IACxB;EAAC;IAAArE,GAAA;IAAAC,KAAA,EAKD,SAAOsE,iBAAiBA,CAACC,MAAc,EAAQ;MAC7C,IAAI,CAACC,cAAc,CAACjC,IAAI,CAACgC,MAAM,CAAC;MAChC,IAAI,CAACE,YAAY,CAACF,MAAM,CAAC;IAC3B;EAAC;IAAAxE,GAAA;IAAAC,KAAA,EAKD,SAAeyE,YAAYA,CAACF,MAAc,EAAQ;MAChD,IAAI,OAAOG,QAAQ,KAAK,WAAW,EAAE;MAErC,IAAMC,IAAI,GAAGD,QAAQ,CAACE,aAAa,CAAC,MAAM,CAAC;MAC3CD,IAAI,CAACE,GAAG,GAAG,YAAY;MACvBF,IAAI,CAACG,IAAI,GAAGP,MAAM;MAClBI,IAAI,CAACI,KAAK,GAAG,OAAO;MACpBJ,IAAI,CAACK,MAAM,GAAG,YAAM;QAClBL,IAAI,CAACI,KAAK,GAAG,KAAK;MACpB,CAAC;MAEDL,QAAQ,CAACO,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;IACjC;EAAC;IAAA5E,GAAA;IAAAC,KAAA,EAKD,SAAOmF,cAAcA,CAAA,EAAW;MAC9B,OAAO,IAAI,CAACd,WAAW;IACzB;EAAC;IAAAtE,GAAA;IAAAC,KAAA,EAKD,SAAOoF,wBAAwBA,CAACC,SAA6D,EAAQ;MACnG,IAAI,OAAOX,QAAQ,KAAK,WAAW,EAAE;MAErCW,SAAS,CAAC9E,OAAO,CAAC,UAAA+E,QAAQ,EAAI;QAC5B,IAAMX,IAAI,GAAGD,QAAQ,CAACE,aAAa,CAAC,MAAM,CAAC;QAC3CD,IAAI,CAACE,GAAG,GAAG,SAAS;QACpBF,IAAI,CAACG,IAAI,GAAGQ,QAAQ,CAACR,IAAI;QACzBH,IAAI,CAACY,EAAE,GAAGD,QAAQ,CAACC,EAAE;QACrB,IAAID,QAAQ,CAAC9D,IAAI,EAAE;UACjBmD,IAAI,CAACnD,IAAI,GAAG8D,QAAQ,CAAC9D,IAAI;QAC3B;QAEAkD,QAAQ,CAACO,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MACjC,CAAC,CAAC;IACJ;EAAC;AAAA;AA5DUT,oBAAoB,CAChBG,WAAW,GAAW,EAAE;AAD5BH,oBAAoB,CAEhBM,cAAc,GAAa,EAAE;AAAA,IA8DjCgB,kBAAkB,GAAAjG,OAAA,CAAAiG,kBAAA;EAAA,SAAAA,mBAAA;IAAA,IAAAhG,gBAAA,CAAAC,OAAA,QAAA+F,kBAAA;EAAA;EAAA,WAAA1F,aAAA,CAAAL,OAAA,EAAA+F,kBAAA;IAAAzF,GAAA;IAAAC,KAAA;MAAA,IAAAyF,aAAA,OAAAC,kBAAA,CAAAjG,OAAA,EAM7B,WACEkG,QAA0B,EAC1BC,SAAiB,EACL;QACZ,IAAMC,KAAK,GAAG,IAAI/B,gBAAgB,CAAC,cAAc8B,SAAS,EAAE,CAAC;QAE7D,IAAI;UACF,IAAME,MAAM,SAASH,QAAQ,CAAC,CAAC;UAC/BE,KAAK,CAAC7B,GAAG,CAAC;YAAE+B,KAAK,EAAEH,SAAS;YAAEI,MAAM,EAAE;UAAU,CAAC,CAAC;UAClD,IAAI,CAACC,YAAY,CAACC,GAAG,CAACN,SAAS,CAAC;UAChC,OAAOE,MAAM;QACf,CAAC,CAAC,OAAOhF,KAAK,EAAE;UACd+E,KAAK,CAAC7B,GAAG,CAAC;YAAE+B,KAAK,EAAEH,SAAS;YAAEI,MAAM,EAAE;UAAQ,CAAC,CAAC;UAChD,MAAMlF,KAAK;QACb;MACF,CAAC;MAAA,SAfYqF,YAAYA,CAAAC,EAAA,EAAAC,GAAA;QAAA,OAAAZ,aAAA,CAAAa,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAZJ,YAAY;IAAA;EAAA;IAAApG,GAAA;IAAAC,KAAA,EAoBzB,SAAOwG,YAAYA,CAACb,QAA4B,EAAEC,SAAiB,EAAQ;MAAA,IAAAa,MAAA;MACzE,IAAI,IAAI,CAACR,YAAY,CAACS,GAAG,CAACd,SAAS,CAAC,EAAE;MAGtC,IAAI,qBAAqB,IAAI1F,MAAM,EAAE;QACnCyG,mBAAmB,CAAC,YAAM;UACxBF,MAAI,CAACN,YAAY,CAACR,QAAQ,EAAEC,SAAS,CAAC,CAACgB,KAAK,CAAC,YAAM,CAEnD,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,MAAM;QACLC,UAAU,CAAC,YAAM;UACfJ,MAAI,CAACN,YAAY,CAACR,QAAQ,EAAEC,SAAS,CAAC,CAACgB,KAAK,CAAC,YAAM,CAEnD,CAAC,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;MACT;IACF;EAAC;IAAA7G,GAAA;IAAAC,KAAA,EAKD,SAAO8G,eAAeA,CAAA,EAAa;MACjC,OAAOC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACf,YAAY,CAAC;IACtC;EAAC;AAAA;AAlDUT,kBAAkB,CACdS,YAAY,GAAgB,IAAIgB,GAAG,CAAC,CAAC;AAAA,IAqDzCC,aAAa,GAAA3H,OAAA,CAAA2H,aAAA;EAAA,SAAAA,cAAA;IAAA,IAAA1H,gBAAA,CAAAC,OAAA,QAAAyH,aAAA;EAAA;EAAA,WAAApH,aAAA,CAAAL,OAAA,EAAAyH,aAAA;IAAAnH,GAAA;IAAAC,KAAA,EAIxB,SAAOmH,cAAcA,CAAA,EAIZ;MACP,IAAI,OAAOjH,MAAM,KAAK,WAAW,IAAI,EAAE,aAAa,IAAIA,MAAM,CAAC,IAAI,CAAE6D,WAAW,CAASqD,MAAM,EAAE;QAC/F,OAAO,IAAI;MACb;MAEA,IAAMA,MAAM,GAAIrD,WAAW,CAASqD,MAAM;MAC1C,OAAO;QACLC,IAAI,EAAED,MAAM,CAACE,cAAc;QAC3BC,KAAK,EAAEH,MAAM,CAACI,eAAe;QAC7BC,UAAU,EAAGL,MAAM,CAACE,cAAc,GAAGF,MAAM,CAACI,eAAe,GAAI;MACjE,CAAC;IACH;EAAC;IAAAzH,GAAA;IAAAC,KAAA,EAKD,SAAO0H,qBAAqBA,CAAA,EAA+B;MAAA,IAAAC,MAAA;MAAA,IAA9BC,SAAiB,GAAArB,SAAA,CAAA1E,MAAA,QAAA0E,SAAA,QAAAsB,SAAA,GAAAtB,SAAA,MAAG,EAAE;MACjD,IAAI,OAAOrG,MAAM,KAAK,WAAW,EAAE;MAEnC,IAAM4H,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;QACxB,IAAMC,KAAK,GAAGJ,MAAI,CAACR,cAAc,CAAC,CAAC;QACnC,IAAIY,KAAK,IAAIA,KAAK,CAACN,UAAU,GAAGG,SAAS,EAAE;UACzC7G,OAAO,CAACC,IAAI,CAAC,sBAAsB+G,KAAK,CAACN,UAAU,CAACO,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;UAElE1I,kBAAkB,CAACsE,WAAW,CAAC,CAAC,CAAC1C,YAAY,CAAC;YAC5CC,IAAI,EAAE,mBAAmB;YACzBnB,KAAK,EAAE+H,KAAK,CAACN,UAAU;YACvBpG,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;YACrBC,IAAI,EAAE,OAAO;YACbC,IAAI,EAAE;cAAEmG,SAAS,EAAEA,SAAS,CAACK,QAAQ,CAAC;YAAE;UAC1C,CAAC,CAAC;QACJ;MACF,CAAC;MAGDC,WAAW,CAACJ,WAAW,EAAE,KAAK,CAAC;IACjC;EAAC;AAAA;AAII,IAAMK,kBAAkB,GAAA5I,OAAA,CAAA4I,kBAAA,GAAG7I,kBAAkB,CAACsE,WAAW,CAAC,CAAC;AAG3D,IAAMwE,kBAAkB,GAAA7I,OAAA,CAAA6I,kBAAA,GAAG,SAArBA,kBAAkBA,CAAIjH,IAAY;EAAA,OAAK,IAAI2C,gBAAgB,CAAC3C,IAAI,CAAC;AAAA;AAEvE,IAAMkH,uBAAuB,GAAA9I,OAAA,CAAA8I,uBAAA,GAAG,SAA1BA,uBAAuBA,CAClCC,EAAK,EACLnH,IAAY,EACN;EACN,OAAQ,YAAoB;IAC1B,IAAM0E,KAAK,GAAG,IAAI/B,gBAAgB,CAAC3C,IAAI,CAAC;IACxC,IAAI;MACF,IAAMoH,MAAM,GAAGD,EAAE,CAAAhC,KAAA,SAAAC,SAAQ,CAAC;MAG1B,IAAIgC,MAAM,IAAI,OAAOA,MAAM,CAACC,IAAI,KAAK,UAAU,EAAE;QAC/C,OAAOD,MAAM,CAACE,OAAO,CAAC;UAAA,OAAM5C,KAAK,CAAC7B,GAAG,CAAC,CAAC;QAAA,EAAC;MAC1C;MAEA6B,KAAK,CAAC7B,GAAG,CAAC,CAAC;MACX,OAAOuE,MAAM;IACf,CAAC,CAAC,OAAOzH,KAAK,EAAE;MACd+E,KAAK,CAAC7B,GAAG,CAAC;QAAEgC,MAAM,EAAE;MAAQ,CAAC,CAAC;MAC9B,MAAMlF,KAAK;IACb;EACF,CAAC;AACH,CAAC", "ignoreList": []}