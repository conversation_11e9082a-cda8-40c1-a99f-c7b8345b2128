{"version": 3, "names": ["_NativeAnimatedModule", "_interopRequireWildcard", "require", "Object", "keys", "for<PERSON>ach", "key", "prototype", "hasOwnProperty", "call", "_exportNames", "exports", "defineProperty", "enumerable", "get", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "set", "_t", "getOwnPropertyDescriptor", "_default", "NativeAnimatedModule"], "sources": ["NativeAnimatedModule.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\nexport * from '../../src/private/specs_DEPRECATED/modules/NativeAnimatedModule';\nimport NativeAnimatedModule from '../../src/private/specs_DEPRECATED/modules/NativeAnimatedModule';\nexport default NativeAnimatedModule;\n"], "mappings": ";;;;;AAUA,IAAAA,qBAAA,GAAAC,uBAAA,CAAAC,OAAA;AAAAC,MAAA,CAAAC,IAAA,CAAAJ,qBAAA,EAAAK,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAC,YAAA,EAAAJ,GAAA;EAAA,IAAAA,GAAA,IAAAK,OAAA,IAAAA,OAAA,CAAAL,GAAA,MAAAN,qBAAA,CAAAM,GAAA;EAAAH,MAAA,CAAAS,cAAA,CAAAD,OAAA,EAAAL,GAAA;IAAAO,UAAA;IAAAC,GAAA,WAAAA,IAAA;MAAA,OAAAd,qBAAA,CAAAM,GAAA;IAAA;EAAA;AAAA;AAAgF,SAAAL,wBAAAc,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAhB,uBAAA,YAAAA,wBAAAc,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAP,GAAA,CAAAC,CAAA,GAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,EAAAQ,CAAA,cAAAK,EAAA,IAAAb,CAAA,gBAAAa,EAAA,OAAApB,cAAA,CAAAC,IAAA,CAAAM,CAAA,EAAAa,EAAA,OAAAN,CAAA,IAAAD,CAAA,GAAAlB,MAAA,CAAAS,cAAA,KAAAT,MAAA,CAAA0B,wBAAA,CAAAd,CAAA,EAAAa,EAAA,OAAAN,CAAA,CAAAR,GAAA,IAAAQ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAK,EAAA,EAAAN,CAAA,IAAAC,CAAA,CAAAK,EAAA,IAAAb,CAAA,CAAAa,EAAA,WAAAL,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAAA,IAAAc,QAAA,GAAAnB,OAAA,CAAAc,OAAA,GAEjEM,6BAAoB", "ignoreList": []}