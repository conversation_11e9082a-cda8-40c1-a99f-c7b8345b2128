c4a6b930da331eb90d127a80fedc7659
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.validateFormAccessibility = exports.getAccessibilityValue = exports.generateLabelProps = exports.generateHelperTextProps = exports.generateFormFieldIds = exports.generateFormFieldAccessibilityProps = exports.generateErrorMessageProps = exports.generateAccessibilityLabel = exports.generateAccessibilityHint = exports.default = exports.FORM_ACCESSIBILITY_CONFIG = void 0;
var _reactNative = require("react-native");
var FORM_ACCESSIBILITY_CONFIG = exports.FORM_ACCESSIBILITY_CONFIG = {
  LABEL_ASSOCIATION: {
    USE_ARIA_LABELLEDBY: true,
    USE_ARIA_DESCRIBEDBY: true,
    INCLUDE_REQUIRED_INDICATOR: true
  },
  ERROR_MESSAGING: {
    ANNOUNCE_ERRORS: true,
    ERROR_ROLE: 'alert',
    LIVE_REGION: 'polite'
  },
  FIELD_VALIDATION: {
    ANNOUNCE_SUCCESS: false,
    ANNOUNCE_ERRORS: true,
    DEBOUNCE_DELAY: 300
  }
};
var generateFormFieldIds = exports.generateFormFieldIds = function generateFormFieldIds(baseId) {
  var timestamp = Date.now();
  var random = Math.random().toString(36).substr(2, 5);
  var uniqueId = `${baseId}_${timestamp}_${random}`;
  return {
    fieldId: uniqueId,
    labelId: `${uniqueId}_label`,
    errorId: `${uniqueId}_error`,
    helperId: `${uniqueId}_helper`,
    descriptionId: `${uniqueId}_description`
  };
};
var generateFormFieldAccessibilityProps = exports.generateFormFieldAccessibilityProps = function generateFormFieldAccessibilityProps(props) {
  var id = props.id,
    label = props.label,
    helperText = props.helperText,
    errorMessage = props.errorMessage,
    _props$required = props.required,
    required = _props$required === void 0 ? false : _props$required,
    _props$disabled = props.disabled,
    disabled = _props$disabled === void 0 ? false : _props$disabled,
    _props$fieldType = props.fieldType,
    fieldType = _props$fieldType === void 0 ? 'text' : _props$fieldType,
    value = props.value,
    ariaLabel = props.ariaLabel,
    ariaDescribedBy = props.ariaDescribedBy,
    ariaLabelledBy = props.ariaLabelledBy;
  var ids = id ? generateFormFieldIds(id) : generateFormFieldIds('field');
  var describedByIds = [];
  if (errorMessage) describedByIds.push(ids.errorId);
  if (helperText) describedByIds.push(ids.helperId);
  if (ariaDescribedBy) describedByIds.push(ariaDescribedBy);
  var labelledByIds = [];
  if (label) labelledByIds.push(ids.labelId);
  if (ariaLabelledBy) labelledByIds.push(ariaLabelledBy);
  var accessibilityLabel = generateAccessibilityLabel({
    label: label,
    required: required,
    fieldType: fieldType,
    errorMessage: errorMessage,
    customLabel: ariaLabel
  });
  var accessibilityHint = generateAccessibilityHint({
    helperText: helperText,
    fieldType: fieldType,
    errorMessage: errorMessage,
    required: required
  });
  var baseProps = Object.assign({
    accessibilityLabel: accessibilityLabel,
    accessibilityHint: accessibilityHint,
    accessibilityState: Object.assign({
      disabled: disabled,
      invalid: !!errorMessage,
      required: required
    }, fieldType === 'checkbox' || fieldType === 'radio' ? {
      checked: !!value
    } : {}),
    accessibilityValue: getAccessibilityValue(fieldType, value)
  }, _reactNative.Platform.OS === 'web' && {
    'aria-label': accessibilityLabel,
    'aria-describedby': describedByIds.length > 0 ? describedByIds.join(' ') : undefined,
    'aria-labelledby': labelledByIds.length > 0 ? labelledByIds.join(' ') : undefined,
    'aria-required': required,
    'aria-invalid': !!errorMessage
  });
  return Object.assign({}, baseProps, {
    ids: ids
  });
};
var generateAccessibilityLabel = exports.generateAccessibilityLabel = function generateAccessibilityLabel(options) {
  var label = options.label,
    required = options.required,
    fieldType = options.fieldType,
    errorMessage = options.errorMessage,
    customLabel = options.customLabel;
  if (customLabel) return customLabel;
  var accessibilityLabel = label || '';
  if (fieldType && label && !label.toLowerCase().includes(fieldType)) {
    var typeLabels = {
      email: 'email address',
      password: 'password',
      phone: 'phone number',
      search: 'search',
      url: 'website URL',
      number: 'number',
      date: 'date',
      time: 'time'
    };
    var typeLabel = typeLabels[fieldType];
    if (typeLabel && !label.toLowerCase().includes(typeLabel)) {
      accessibilityLabel += ` ${typeLabel}`;
    }
  }
  if (required) {
    accessibilityLabel += ', required';
  }
  if (errorMessage) {
    accessibilityLabel += ', has error';
  }
  return accessibilityLabel.trim();
};
var generateAccessibilityHint = exports.generateAccessibilityHint = function generateAccessibilityHint(options) {
  var helperText = options.helperText,
    fieldType = options.fieldType,
    errorMessage = options.errorMessage,
    required = options.required;
  var hints = [];
  if (helperText && !errorMessage) {
    hints.push(helperText);
  }
  var fieldHints = {
    password: 'Double tap to toggle password visibility',
    select: 'Double tap to open options',
    checkbox: 'Double tap to toggle selection',
    radio: 'Double tap to select option',
    date: 'Double tap to open date picker',
    time: 'Double tap to open time picker'
  };
  if (fieldType && fieldHints[fieldType]) {
    hints.push(fieldHints[fieldType]);
  }
  if (errorMessage) {
    hints.push(`Error: ${errorMessage}`);
  }
  return hints.join('. ');
};
var getAccessibilityValue = exports.getAccessibilityValue = function getAccessibilityValue(fieldType, value) {
  if (value === undefined || value === null) return undefined;
  switch (fieldType) {
    case 'checkbox':
    case 'radio':
      return undefined;
    case 'number':
      return {
        now: Number(value)
      };
    case 'text':
    case 'email':
    case 'password':
    case 'phone':
    case 'name':
    case 'search':
    case 'url':
    case 'textarea':
    default:
      return {
        text: String(value)
      };
  }
};
var generateErrorMessageProps = exports.generateErrorMessageProps = function generateErrorMessageProps(errorMessage, errorId) {
  return Object.assign({
    accessibilityRole: 'alert',
    accessibilityLiveRegion: 'polite',
    accessibilityLabel: `Error: ${errorMessage}`
  }, _reactNative.Platform.OS === 'web' && {
    role: 'alert',
    'aria-live': 'polite',
    id: errorId
  });
};
var generateHelperTextProps = exports.generateHelperTextProps = function generateHelperTextProps(helperText, helperId) {
  return Object.assign({
    accessibilityLabel: helperText
  }, _reactNative.Platform.OS === 'web' && {
    id: helperId
  });
};
var generateLabelProps = exports.generateLabelProps = function generateLabelProps(label, labelId) {
  var required = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
  return Object.assign({
    accessibilityLabel: required ? `${label}, required` : label
  }, _reactNative.Platform.OS === 'web' && {
    id: labelId,
    htmlFor: labelId.replace('_label', '')
  });
};
var validateFormAccessibility = exports.validateFormAccessibility = function validateFormAccessibility(formFields) {
  var errors = [];
  var warnings = [];
  formFields.forEach(function (field, index) {
    var _field$label, _field$ariaLabel;
    var fieldName = field.label || `Field ${index + 1}`;
    if (!field.label && !field.ariaLabel && !field.placeholder) {
      errors.push(`${fieldName}: Missing accessible label`);
    }
    if (field.required && !((_field$label = field.label) != null && _field$label.includes('*')) && !((_field$ariaLabel = field.ariaLabel) != null && _field$ariaLabel.includes('required'))) {
      warnings.push(`${fieldName}: Required field should have clear indicator`);
    }
    if (field.errorMessage && !field.ariaDescribedBy) {
      warnings.push(`${fieldName}: Error message should be programmatically associated`);
    }
    if (field.helperText && !field.ariaDescribedBy) {
      warnings.push(`${fieldName}: Helper text should be programmatically associated`);
    }
  });
  return {
    isValid: errors.length === 0,
    errors: errors,
    warnings: warnings
  };
};
var _default = exports.default = {
  FORM_ACCESSIBILITY_CONFIG: FORM_ACCESSIBILITY_CONFIG,
  generateFormFieldIds: generateFormFieldIds,
  generateFormFieldAccessibilityProps: generateFormFieldAccessibilityProps,
  generateAccessibilityLabel: generateAccessibilityLabel,
  generateAccessibilityHint: generateAccessibilityHint,
  getAccessibilityValue: getAccessibilityValue,
  generateErrorMessageProps: generateErrorMessageProps,
  generateHelperTextProps: generateHelperTextProps,
  generateLabelProps: generateLabelProps,
  validateFormAccessibility: validateFormAccessibility
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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