{"version": 3, "names": ["_interopRequireDefault", "require", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_inherits2", "_callSuper", "t", "o", "e", "default", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "module", "exports", "moduleName", "instanceMethods", "isESModule", "_Class", "RealComponent", "jest", "requireActual", "React", "SuperClass", "Component", "name", "displayName", "render", "nameWithoutPrefix", "replace", "_SuperClass", "arguments", "key", "value", "_this", "props", "Object", "assign", "defaultProps", "keys", "for<PERSON>ach", "prop", "undefined", "createElement", "children", "defineProperty", "writable", "enumerable", "configurable", "classStatic"], "sources": ["mockComponent.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n */\n\n'use strict';\n\nmodule.exports = (moduleName, instanceMethods, isESModule) => {\n  const RealComponent = isESModule\n    ? jest.requireActual(moduleName).default\n    : jest.requireActual(moduleName);\n  const React = require('react');\n\n  const SuperClass =\n    typeof RealComponent === 'function' &&\n    RealComponent.prototype.constructor instanceof React.Component\n      ? RealComponent\n      : React.Component;\n\n  const name =\n    RealComponent.displayName ||\n    RealComponent.name ||\n    (RealComponent.render // handle React.forwardRef\n      ? RealComponent.render.displayName || RealComponent.render.name\n      : 'Unknown');\n\n  const nameWithoutPrefix = name.replace(/^(RCT|RK)/, '');\n\n  const Component = class extends SuperClass {\n    static displayName = 'Component';\n\n    render() {\n      const props = Object.assign({}, RealComponent.defaultProps);\n\n      if (this.props) {\n        Object.keys(this.props).forEach(prop => {\n          // We can't just assign props on top of defaultProps\n          // because React treats undefined as special and different from null.\n          // If a prop is specified but set to undefined it is ignored and the\n          // default prop is used instead. If it is set to null, then the\n          // null value overwrites the default value.\n          if (this.props[prop] !== undefined) {\n            props[prop] = this.props[prop];\n          }\n        });\n      }\n\n      return React.createElement(nameWithoutPrefix, props, this.props.children);\n    }\n  };\n\n  Object.defineProperty(Component, 'name', {\n    value: name,\n    writable: false,\n    enumerable: false,\n    configurable: true,\n  });\n\n  Component.displayName = nameWithoutPrefix;\n\n  Object.keys(RealComponent).forEach(classStatic => {\n    Component[classStatic] = RealComponent[classStatic];\n  });\n\n  if (instanceMethods != null) {\n    Object.assign(Component.prototype, instanceMethods);\n  }\n\n  return Component;\n};\n"], "mappings": "AASA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAAA,IAAAC,gBAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAA,IAAAE,aAAA,GAAAH,sBAAA,CAAAC,OAAA;AAAA,IAAAG,2BAAA,GAAAJ,sBAAA,CAAAC,OAAA;AAAA,IAAAI,gBAAA,GAAAL,sBAAA,CAAAC,OAAA;AAAA,IAAAK,UAAA,GAAAN,sBAAA,CAAAC,OAAA;AAAA,SAAAM,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAJ,gBAAA,CAAAM,OAAA,EAAAF,CAAA,OAAAL,2BAAA,CAAAO,OAAA,EAAAH,CAAA,EAAAI,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAL,CAAA,EAAAC,CAAA,YAAAL,gBAAA,CAAAM,OAAA,EAAAH,CAAA,EAAAO,WAAA,IAAAN,CAAA,CAAAO,KAAA,CAAAR,CAAA,EAAAE,CAAA;AAAA,SAAAE,0BAAA,cAAAJ,CAAA,IAAAS,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAT,CAAA,aAAAI,yBAAA,YAAAA,0BAAA,aAAAJ,CAAA;AAEba,MAAM,CAACC,OAAO,GAAG,UAACC,UAAU,EAAEC,eAAe,EAAEC,UAAU,EAAK;EAAA,IAAAC,MAAA;EAC5D,IAAMC,aAAa,GAAGF,UAAU,GAC5BG,IAAI,CAACC,aAAa,CAACN,UAAU,CAAC,CAACZ,OAAO,GACtCiB,IAAI,CAACC,aAAa,CAACN,UAAU,CAAC;EAClC,IAAMO,KAAK,GAAG7B,OAAO,CAAC,OAAO,CAAC;EAE9B,IAAM8B,UAAU,GACd,OAAOJ,aAAa,KAAK,UAAU,IACnCA,aAAa,CAACT,SAAS,CAACH,WAAW,YAAYe,KAAK,CAACE,SAAS,GAC1DL,aAAa,GACbG,KAAK,CAACE,SAAS;EAErB,IAAMC,IAAI,GACRN,aAAa,CAACO,WAAW,IACzBP,aAAa,CAACM,IAAI,KACjBN,aAAa,CAACQ,MAAM,GACjBR,aAAa,CAACQ,MAAM,CAACD,WAAW,IAAIP,aAAa,CAACQ,MAAM,CAACF,IAAI,GAC7D,SAAS,CAAC;EAEhB,IAAMG,iBAAiB,GAAGH,IAAI,CAACI,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;EAEvD,IAAML,SAAS,IAAAN,MAAA,aAAAY,WAAA;IAAA,SAAAN,UAAA;MAAA,IAAA9B,gBAAA,CAAAS,OAAA,QAAAqB,SAAA;MAAA,OAAAzB,UAAA,OAAAyB,SAAA,EAAAO,SAAA;IAAA;IAAA,IAAAjC,UAAA,CAAAK,OAAA,EAAAqB,SAAA,EAAAM,WAAA;IAAA,WAAAnC,aAAA,CAAAQ,OAAA,EAAAqB,SAAA;MAAAQ,GAAA;MAAAC,KAAA,EAGb,SAAAN,MAAMA,CAAA,EAAG;QAAA,IAAAO,KAAA;QACP,IAAMC,KAAK,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAElB,aAAa,CAACmB,YAAY,CAAC;QAE3D,IAAI,IAAI,CAACH,KAAK,EAAE;UACdC,MAAM,CAACG,IAAI,CAAC,IAAI,CAACJ,KAAK,CAAC,CAACK,OAAO,CAAC,UAAAC,IAAI,EAAI;YAMtC,IAAIP,KAAI,CAACC,KAAK,CAACM,IAAI,CAAC,KAAKC,SAAS,EAAE;cAClCP,KAAK,CAACM,IAAI,CAAC,GAAGP,KAAI,CAACC,KAAK,CAACM,IAAI,CAAC;YAChC;UACF,CAAC,CAAC;QACJ;QAEA,OAAOnB,KAAK,CAACqB,aAAa,CAACf,iBAAiB,EAAEO,KAAK,EAAE,IAAI,CAACA,KAAK,CAACS,QAAQ,CAAC;MAC3E;IAAC;EAAA,EApB6BrB,UAAU,GAAAL,MAAA,CACjCQ,WAAW,GAAG,WAAW,EAAAR,MAAA,CAoBjC;EAEDkB,MAAM,CAACS,cAAc,CAACrB,SAAS,EAAE,MAAM,EAAE;IACvCS,KAAK,EAAER,IAAI;IACXqB,QAAQ,EAAE,KAAK;IACfC,UAAU,EAAE,KAAK;IACjBC,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFxB,SAAS,CAACE,WAAW,GAAGE,iBAAiB;EAEzCQ,MAAM,CAACG,IAAI,CAACpB,aAAa,CAAC,CAACqB,OAAO,CAAC,UAAAS,WAAW,EAAI;IAChDzB,SAAS,CAACyB,WAAW,CAAC,GAAG9B,aAAa,CAAC8B,WAAW,CAAC;EACrD,CAAC,CAAC;EAEF,IAAIjC,eAAe,IAAI,IAAI,EAAE;IAC3BoB,MAAM,CAACC,MAAM,CAACb,SAAS,CAACd,SAAS,EAAEM,eAAe,CAAC;EACrD;EAEA,OAAOQ,SAAS;AAClB,CAAC", "ignoreList": []}