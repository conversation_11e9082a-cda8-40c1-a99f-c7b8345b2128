c9dbbd8327c6d2a9709112cd16a1e8f1
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.navigationGuards = exports.default = void 0;
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _authSlice = require("../store/authSlice");
var _navigationAnalytics = _interopRequireDefault(require("./navigationAnalytics"));
var NavigationGuardsService = function () {
  function NavigationGuardsService() {
    (0, _classCallCheck2.default)(this, NavigationGuardsService);
    this.routeConfigs = new Map();
    this.initializeRouteConfigs();
  }
  return (0, _createClass2.default)(NavigationGuardsService, [{
    key: "initializeRouteConfigs",
    value: function initializeRouteConfigs() {
      this.addRouteConfig({
        name: 'Welcome',
        requiresAuth: false
      });
      this.addRouteConfig({
        name: 'Login',
        requiresAuth: false
      });
      this.addRouteConfig({
        name: 'Register',
        requiresAuth: false
      });
      this.addRouteConfig({
        name: 'ForgotPassword',
        requiresAuth: false
      });
      this.addRouteConfig({
        name: 'CustomerTabs',
        requiresAuth: true,
        allowedRoles: ['customer']
      });
      this.addRouteConfig({
        name: 'Home',
        requiresAuth: true,
        allowedRoles: ['customer']
      });
      this.addRouteConfig({
        name: 'Search',
        requiresAuth: true,
        allowedRoles: ['customer']
      });
      this.addRouteConfig({
        name: 'Bookings',
        requiresAuth: true,
        allowedRoles: ['customer']
      });
      this.addRouteConfig({
        name: 'Messages',
        requiresAuth: true,
        allowedRoles: ['customer']
      });
      this.addRouteConfig({
        name: 'Profile',
        requiresAuth: true,
        allowedRoles: ['customer']
      });
      this.addRouteConfig({
        name: 'ProviderDetails',
        requiresAuth: true,
        allowedRoles: ['customer']
      });
      this.addRouteConfig({
        name: 'ServiceDetails',
        requiresAuth: true,
        allowedRoles: ['customer']
      });
      this.addRouteConfig({
        name: 'BookingScreen',
        requiresAuth: true,
        allowedRoles: ['customer']
      });
      this.addRouteConfig({
        name: 'Checkout',
        requiresAuth: true,
        allowedRoles: ['customer'],
        requiresVerification: true
      });
      this.addRouteConfig({
        name: 'Payment',
        requiresAuth: true,
        allowedRoles: ['customer'],
        requiresVerification: true
      });
      this.addRouteConfig({
        name: 'ProviderTabs',
        requiresAuth: true,
        allowedRoles: ['provider']
      });
      this.addRouteConfig({
        name: 'ProviderDashboard',
        requiresAuth: true,
        allowedRoles: ['provider']
      });
      this.addRouteConfig({
        name: 'ProviderBookings',
        requiresAuth: true,
        allowedRoles: ['provider']
      });
      this.addRouteConfig({
        name: 'ProviderServices',
        requiresAuth: true,
        allowedRoles: ['provider']
      });
      this.addRouteConfig({
        name: 'ProviderProfile',
        requiresAuth: true,
        allowedRoles: ['provider']
      });
      this.addRouteConfig({
        name: 'Conversation',
        requiresAuth: true,
        allowedRoles: ['customer', 'provider']
      });
      this.addRouteConfig({
        name: 'Notifications',
        requiresAuth: true,
        allowedRoles: ['customer', 'provider']
      });
      this.addRouteConfig({
        name: 'AccountSettings',
        requiresAuth: true,
        allowedRoles: ['customer', 'provider']
      });
      this.addRouteConfig({
        name: 'EditProfile',
        requiresAuth: true,
        allowedRoles: ['customer', 'provider']
      });
    }
  }, {
    key: "addRouteConfig",
    value: function addRouteConfig(config) {
      this.routeConfigs.set(config.name, config);
    }
  }, {
    key: "canNavigate",
    value: function canNavigate(routeName, params) {
      var config = this.routeConfigs.get(routeName);
      if (!config) {
        console.warn(`Navigation guard: Route '${routeName}' not configured`);
        return {
          allowed: true
        };
      }
      var authStore = _authSlice.useAuthStore.getState();
      var isAuthenticated = authStore.isAuthenticated,
        userRole = authStore.userRole,
        user = authStore.user;
      if (config.requiresAuth && !isAuthenticated) {
        _navigationAnalytics.default.trackNavigationError('Authentication required', routeName, {
          requiresAuth: true
        });
        return {
          allowed: false,
          redirectTo: 'Login',
          reason: 'Authentication required',
          requiresAuth: true
        };
      }
      if (config.allowedRoles && config.allowedRoles.length > 0) {
        if (!userRole || !config.allowedRoles.includes(userRole)) {
          _navigationAnalytics.default.trackNavigationError('Insufficient role permissions', routeName, {
            userRole: userRole,
            allowedRoles: config.allowedRoles
          });
          return {
            allowed: false,
            redirectTo: this.getDefaultRouteForRole(userRole),
            reason: 'Insufficient role permissions',
            requiresRole: config.allowedRoles[0]
          };
        }
      }
      if (config.requiresOnboarding && user && !user.hasCompletedOnboarding) {
        return {
          allowed: false,
          redirectTo: userRole === 'customer' ? 'CustomerOnboarding' : 'ProviderOnboarding',
          reason: 'Onboarding required'
        };
      }
      if (config.requiresVerification && user && !user.isVerified) {
        return {
          allowed: false,
          redirectTo: 'VerificationRequired',
          reason: 'Account verification required'
        };
      }
      if (config.customValidator) {
        var customResult = config.customValidator({
          user: user,
          userRole: userRole,
          isAuthenticated: isAuthenticated
        });
        if (!customResult.allowed) {
          _navigationAnalytics.default.trackNavigationError(customResult.reason || 'Custom validation failed', routeName, {
            customValidator: true
          });
          return customResult;
        }
      }
      return {
        allowed: true
      };
    }
  }, {
    key: "getDefaultRouteForRole",
    value: function getDefaultRouteForRole(userRole) {
      switch (userRole) {
        case 'customer':
          return 'CustomerTabs';
        case 'provider':
          return 'ProviderTabs';
        default:
          return 'Login';
      }
    }
  }, {
    key: "validateNavigationFlow",
    value: function validateNavigationFlow(currentRoute, targetRoute, flowContext) {
      var validFlows = {
        'ProviderDetails': ['ServiceDetails', 'BookingScreen'],
        'ServiceDetails': ['BookingScreen', 'ProviderDetails'],
        'BookingScreen': ['Checkout', 'ServiceDetails'],
        'Checkout': ['Payment', 'BookingScreen'],
        'Payment': ['BookingConfirmation', 'Checkout'],
        'Profile': ['EditProfile', 'AccountSettings'],
        'EditProfile': ['Profile'],
        'AccountSettings': ['Profile'],
        'Messages': ['Conversation'],
        'Conversation': ['Messages']
      };
      var allowedTargets = validFlows[currentRoute];
      if (allowedTargets && !allowedTargets.includes(targetRoute)) {
        var backNavigation = this.isValidBackNavigation(currentRoute, targetRoute);
        if (!backNavigation) {
          _navigationAnalytics.default.trackNavigationError('Invalid navigation flow', targetRoute, {
            currentRoute: currentRoute,
            validTargets: allowedTargets
          });
          return {
            allowed: false,
            reason: 'Invalid navigation flow'
          };
        }
      }
      return {
        allowed: true
      };
    }
  }, {
    key: "isValidBackNavigation",
    value: function isValidBackNavigation(currentRoute, targetRoute) {
      var backNavigationMap = {
        'ServiceDetails': ['ProviderDetails', 'Search', 'Home'],
        'BookingScreen': ['ServiceDetails', 'ProviderDetails'],
        'Checkout': ['BookingScreen'],
        'Payment': ['Checkout'],
        'BookingConfirmation': ['Home', 'Bookings'],
        'EditProfile': ['Profile'],
        'AccountSettings': ['Profile'],
        'Conversation': ['Messages'],
        'ProviderDetails': ['Search', 'Home']
      };
      var validBackTargets = backNavigationMap[currentRoute];
      return validBackTargets ? validBackTargets.includes(targetRoute) : true;
    }
  }, {
    key: "handleNavigationGuardFailure",
    value: function handleNavigationGuardFailure(result, originalRoute, navigation) {
      if (result.redirectTo) {
        navigation.reset({
          index: 0,
          routes: [{
            name: result.redirectTo
          }]
        });
      } else {
        console.error('Navigation blocked:', result.reason);
      }
      _navigationAnalytics.default.trackNavigationError(result.reason || 'Navigation guard failure', originalRoute, {
        redirectTo: result.redirectTo,
        requiresAuth: result.requiresAuth,
        requiresRole: result.requiresRole
      });
    }
  }, {
    key: "getRouteConfig",
    value: function getRouteConfig(routeName) {
      return this.routeConfigs.get(routeName);
    }
  }, {
    key: "requiresAuth",
    value: function requiresAuth(routeName) {
      var config = this.routeConfigs.get(routeName);
      return (config == null ? void 0 : config.requiresAuth) || false;
    }
  }, {
    key: "isAllowedForRole",
    value: function isAllowedForRole(routeName, userRole) {
      var config = this.routeConfigs.get(routeName);
      if (!config || !config.allowedRoles) return true;
      return config.allowedRoles.includes(userRole);
    }
  }]);
}();
var navigationGuards = exports.navigationGuards = new NavigationGuardsService();
var _default = exports.default = navigationGuards;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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