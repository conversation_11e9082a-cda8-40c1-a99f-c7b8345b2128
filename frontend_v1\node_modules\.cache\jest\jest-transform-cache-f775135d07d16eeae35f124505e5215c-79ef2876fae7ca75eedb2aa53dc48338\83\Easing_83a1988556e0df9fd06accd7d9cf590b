40afd748569caa4150a98e12e9191900
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _ease;
var Easing = {
  step0: function step0(n) {
    return n > 0 ? 1 : 0;
  },
  step1: function step1(n) {
    return n >= 1 ? 1 : 0;
  },
  linear: function linear(t) {
    return t;
  },
  ease: function ease(t) {
    if (!_ease) {
      _ease = Easing.bezier(0.42, 0, 1, 1);
    }
    return _ease(t);
  },
  quad: function quad(t) {
    return t * t;
  },
  cubic: function cubic(t) {
    return t * t * t;
  },
  poly: function poly(n) {
    return function (t) {
      return Math.pow(t, n);
    };
  },
  sin: function sin(t) {
    return 1 - Math.cos(t * Math.PI / 2);
  },
  circle: function circle(t) {
    return 1 - Math.sqrt(1 - t * t);
  },
  exp: function exp(t) {
    return Math.pow(2, 10 * (t - 1));
  },
  elastic: function elastic() {
    var bounciness = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 1;
    var p = bounciness * Math.PI;
    return function (t) {
      return 1 - Math.pow(Math.cos(t * Math.PI / 2), 3) * Math.cos(t * p);
    };
  },
  back: function back() {
    var s = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 1.70158;
    return function (t) {
      return t * t * ((s + 1) * t - s);
    };
  },
  bounce: function bounce(t) {
    if (t < 1 / 2.75) {
      return 7.5625 * t * t;
    }
    if (t < 2 / 2.75) {
      var _t = t - 1.5 / 2.75;
      return 7.5625 * _t * _t + 0.75;
    }
    if (t < 2.5 / 2.75) {
      var _t2 = t - 2.25 / 2.75;
      return 7.5625 * _t2 * _t2 + 0.9375;
    }
    var t2 = t - 2.625 / 2.75;
    return 7.5625 * t2 * t2 + 0.984375;
  },
  bezier: function bezier(x1, y1, x2, y2) {
    var _bezier = require("./bezier").default;
    return _bezier(x1, y1, x2, y2);
  },
  in: function _in(easing) {
    return easing;
  },
  out: function out(easing) {
    return function (t) {
      return 1 - easing(1 - t);
    };
  },
  inOut: function inOut(easing) {
    return function (t) {
      if (t < 0.5) {
        return easing(t * 2) / 2;
      }
      return 1 - easing((1 - t) * 2) / 2;
    };
  }
};
var _default = exports.default = Easing;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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