/**
 * Profile Service - User Profile Management
 *
 * Service Contract:
 * - Manages user profile data and preferences
 * - <PERSON>les profile updates and avatar uploads
 * - Provides preference management functionality
 * - Supports role switching and account settings
 * - Implements proper error handling and caching
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { apiClient } from './apiClient';

export interface UserProfile {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  phone?: string;
  avatar?: string;
  role: 'customer' | 'service_provider';
  is_verified: boolean;
  date_joined: string;
  last_login?: string;
}

export interface UserProfileDetails {
  // Location Information
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  country?: string;
  latitude?: number;
  longitude?: number;
  
  // Business Information (for providers)
  business_name?: string;
  business_description?: string;
  years_of_experience?: number;
  website?: string;
  instagram?: string;
  facebook?: string;
  
  // Preferences
  search_radius?: number;
  auto_accept_bookings?: boolean;
  show_phone_publicly?: boolean;
  show_email_publicly?: boolean;
  allow_reviews?: boolean;
  
  // Computed fields
  full_address?: string;
  has_location?: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface ProfileUpdateRequest {
  first_name?: string;
  last_name?: string;
  phone?: string;
  avatar?: File | string;
}

export interface ProfileDetailsUpdateRequest extends Partial<UserProfileDetails> {}

export interface PreferencesUpdateRequest {
  search_radius?: number;
  auto_accept_bookings?: boolean;
  show_phone_publicly?: boolean;
  show_email_publicly?: boolean;
  allow_reviews?: boolean;
}

export interface PasswordChangeRequest {
  old_password: string;
  new_password: string;
  confirm_password: string;
}

class ProfileService {
  private baseUrl = '/api/auth';

  /**
   * Get user profile
   */
  async getProfile(): Promise<UserProfile> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/profile/`);
      return response.data;
    } catch (error) {
      console.error('Failed to get profile:', error);
      throw new Error('Failed to get profile');
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(profileData: ProfileUpdateRequest): Promise<UserProfile> {
    try {
      const response = await apiClient.patch(`${this.baseUrl}/profile/`, profileData);
      return response.data;
    } catch (error) {
      console.error('Failed to update profile:', error);
      throw new Error('Failed to update profile');
    }
  }

  /**
   * Get user profile details (extended information)
   */
  async getProfileDetails(): Promise<UserProfileDetails> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/profile/details/`);
      return response.data;
    } catch (error) {
      console.error('Failed to get profile details:', error);
      throw new Error('Failed to get profile details');
    }
  }

  /**
   * Update user profile details
   */
  async updateProfileDetails(profileData: ProfileDetailsUpdateRequest): Promise<UserProfileDetails> {
    try {
      const response = await apiClient.patch(`${this.baseUrl}/profile/details/`, profileData);
      return response.data;
    } catch (error) {
      console.error('Failed to update profile details:', error);
      throw new Error('Failed to update profile details');
    }
  }

  /**
   * Upload profile avatar
   */
  async uploadAvatar(imageUri: string): Promise<UserProfile> {
    try {
      const formData = new FormData();
      formData.append('avatar', {
        uri: imageUri,
        type: 'image/jpeg',
        name: 'avatar.jpg',
      } as any);

      const response = await apiClient.patch(`${this.baseUrl}/profile/`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    } catch (error) {
      console.error('Failed to upload avatar:', error);
      throw new Error('Failed to upload avatar');
    }
  }

  /**
   * Update user preferences
   */
  async updatePreferences(preferences: PreferencesUpdateRequest): Promise<UserProfileDetails> {
    try {
      const response = await apiClient.patch(`${this.baseUrl}/profile/details/`, preferences);
      return response.data;
    } catch (error) {
      console.error('Failed to update preferences:', error);
      throw new Error('Failed to update preferences');
    }
  }

  /**
   * Change password
   */
  async changePassword(passwordData: PasswordChangeRequest): Promise<{ message: string }> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/change-password/`, passwordData);
      return response.data;
    } catch (error) {
      console.error('Failed to change password:', error);
      throw new Error('Failed to change password');
    }
  }

  /**
   * Delete account
   */
  async deleteAccount(password: string): Promise<{ message: string }> {
    try {
      const response = await apiClient.delete(`${this.baseUrl}/profile/`, {
        data: { password },
      });
      return response.data;
    } catch (error) {
      console.error('Failed to delete account:', error);
      throw new Error('Failed to delete account');
    }
  }

  /**
   * Get account statistics
   */
  async getAccountStats(): Promise<{
    total_bookings: number;
    completed_bookings: number;
    cancelled_bookings: number;
    total_spent?: number;
    total_earned?: number;
    average_rating?: number;
    total_reviews?: number;
  }> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/profile/stats/`);
      return response.data;
    } catch (error) {
      console.error('Failed to get account stats:', error);
      // Return default stats if API fails
      return {
        total_bookings: 0,
        completed_bookings: 0,
        cancelled_bookings: 0,
        total_spent: 0,
        average_rating: 0,
        total_reviews: 0,
      };
    }
  }

  /**
   * Request role switch (customer to provider or vice versa)
   */
  async requestRoleSwitch(targetRole: 'customer' | 'service_provider'): Promise<{ message: string }> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/role-switch/`, {
        target_role: targetRole,
      });
      return response.data;
    } catch (error) {
      console.error('Failed to request role switch:', error);
      throw new Error('Failed to request role switch');
    }
  }

  /**
   * Get notification preferences
   */
  async getNotificationPreferences(): Promise<{
    email_notifications: boolean;
    sms_notifications: boolean;
    push_notifications: boolean;
    booking_reminders: boolean;
    marketing_emails: boolean;
  }> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/notifications/preferences/`);
      return response.data;
    } catch (error) {
      console.error('Failed to get notification preferences:', error);
      // Return default preferences if API fails
      return {
        email_notifications: true,
        sms_notifications: false,
        push_notifications: true,
        booking_reminders: true,
        marketing_emails: false,
      };
    }
  }

  /**
   * Update notification preferences
   */
  async updateNotificationPreferences(preferences: {
    email_notifications?: boolean;
    sms_notifications?: boolean;
    push_notifications?: boolean;
    booking_reminders?: boolean;
    marketing_emails?: boolean;
  }): Promise<{ message: string }> {
    try {
      const response = await apiClient.patch(`${this.baseUrl}/notifications/preferences/`, preferences);
      return response.data;
    } catch (error) {
      console.error('Failed to update notification preferences:', error);
      throw new Error('Failed to update notification preferences');
    }
  }
}

export const profileService = new ProfileService();
export default profileService;
