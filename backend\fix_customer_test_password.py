#!/usr/bin/env python
"""
Script to fix the <NAME_EMAIL>
"""
import os
import sys
import django

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from apps.authentication.models import User

def fix_customer_test_password():
    """Fix <NAME_EMAIL>"""
    
    try:
        user = User.objects.get(email='<EMAIL>')
        user.set_password('testpass123')
        user.save()
        print(f"✅ Password updated for {user.email}")
        
        # Verify the password works
        if user.check_password('testpass123'):
            print(f"✅ Password verification successful")
        else:
            print(f"❌ Password verification failed")
            
    except User.DoesNotExist:
        print(f"❌ User <EMAIL> not found")

if __name__ == '__main__':
    fix_customer_test_password()
