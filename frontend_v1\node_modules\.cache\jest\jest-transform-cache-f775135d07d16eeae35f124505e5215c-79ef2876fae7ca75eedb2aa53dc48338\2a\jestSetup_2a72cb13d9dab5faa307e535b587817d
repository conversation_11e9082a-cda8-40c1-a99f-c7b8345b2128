9462dd18015741fd81bb6631b015e7c1
var originalConsoleError = console.error;
var originalConsoleWarn = console.warn;
console.error = function () {
  var message = arguments.length <= 0 ? undefined : arguments[0];
  if (typeof message === 'string' && (message.includes('Warning: ReactDOM.render is no longer supported') || message.includes('Warning: componentWillMount has been renamed') || message.includes('Warning: componentWillReceiveProps has been renamed') || message.includes('VirtualizedLists should never be nested'))) {
    return;
  }
  originalConsoleError.apply(void 0, arguments);
};
console.warn = function () {
  var message = arguments.length <= 0 ? undefined : arguments[0];
  if (typeof message === 'string' && (message.includes('Animated: `useNativeDriver`') || message.includes('source.uri should not be an empty string'))) {
    return;
  }
  originalConsoleWarn.apply(void 0, arguments);
};
global.mockNavigate = jest.fn();
global.mockGoBack = jest.fn();
global.mockReset = jest.fn();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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