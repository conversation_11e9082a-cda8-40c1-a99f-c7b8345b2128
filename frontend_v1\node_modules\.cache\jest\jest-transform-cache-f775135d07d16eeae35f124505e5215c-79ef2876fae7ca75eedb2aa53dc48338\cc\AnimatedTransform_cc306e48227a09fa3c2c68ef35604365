7b6f521e3e6f7bb159a91ea8e6a7c34b
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _get2 = _interopRequireDefault(require("@babel/runtime/helpers/get"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _classPrivateFieldLooseBase2 = _interopRequireDefault(require("@babel/runtime/helpers/classPrivateFieldLooseBase"));
var _classPrivateFieldLooseKey2 = _interopRequireDefault(require("@babel/runtime/helpers/classPrivateFieldLooseKey"));
var _NativeAnimatedHelper = _interopRequireDefault(require("../../../src/private/animated/NativeAnimatedHelper"));
var _NativeAnimatedValidation = require("../../../src/private/animated/NativeAnimatedValidation");
var _AnimatedNode = _interopRequireDefault(require("./AnimatedNode"));
var _AnimatedWithChildren2 = _interopRequireDefault(require("./AnimatedWithChildren"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && "function" == typeof p ? function (t) { return p.apply(e, t); } : p; }
function flatAnimatedNodes(transforms) {
  var nodes = [];
  for (var ii = 0, length = transforms.length; ii < length; ii++) {
    var transform = transforms[ii];
    for (var key in transform) {
      var value = transform[key];
      if (value instanceof _AnimatedNode.default) {
        nodes.push(value);
      }
    }
  }
  return nodes;
}
var _nodes = (0, _classPrivateFieldLooseKey2.default)("nodes");
var AnimatedTransform = exports.default = function (_AnimatedWithChildren) {
  function AnimatedTransform(nodes, transforms, config) {
    var _this;
    (0, _classCallCheck2.default)(this, AnimatedTransform);
    _this = _callSuper(this, AnimatedTransform, [config]);
    Object.defineProperty(_this, _nodes, {
      writable: true,
      value: void 0
    });
    (0, _classPrivateFieldLooseBase2.default)(_this, _nodes)[_nodes] = nodes;
    _this._transforms = transforms;
    return _this;
  }
  (0, _inherits2.default)(AnimatedTransform, _AnimatedWithChildren);
  return (0, _createClass2.default)(AnimatedTransform, [{
    key: "__makeNative",
    value: function __makeNative(platformConfig) {
      var nodes = (0, _classPrivateFieldLooseBase2.default)(this, _nodes)[_nodes];
      for (var ii = 0, length = nodes.length; ii < length; ii++) {
        var node = nodes[ii];
        node.__makeNative(platformConfig);
      }
      _superPropGet(AnimatedTransform, "__makeNative", this, 3)([platformConfig]);
    }
  }, {
    key: "__getValue",
    value: function __getValue() {
      return mapTransforms(this._transforms, function (animatedNode) {
        return animatedNode.__getValue();
      });
    }
  }, {
    key: "__getValueWithStaticTransforms",
    value: function __getValueWithStaticTransforms(staticTransforms) {
      var values = [];
      mapTransforms(this._transforms, function (node) {
        values.push(node.__getValue());
      });
      return mapTransforms(staticTransforms, function () {
        return values.shift();
      });
    }
  }, {
    key: "__getAnimatedValue",
    value: function __getAnimatedValue() {
      return mapTransforms(this._transforms, function (animatedNode) {
        return animatedNode.__getAnimatedValue();
      });
    }
  }, {
    key: "__attach",
    value: function __attach() {
      var nodes = (0, _classPrivateFieldLooseBase2.default)(this, _nodes)[_nodes];
      for (var ii = 0, length = nodes.length; ii < length; ii++) {
        var node = nodes[ii];
        node.__addChild(this);
      }
      _superPropGet(AnimatedTransform, "__attach", this, 3)([]);
    }
  }, {
    key: "__detach",
    value: function __detach() {
      var nodes = (0, _classPrivateFieldLooseBase2.default)(this, _nodes)[_nodes];
      for (var ii = 0, length = nodes.length; ii < length; ii++) {
        var node = nodes[ii];
        node.__removeChild(this);
      }
      _superPropGet(AnimatedTransform, "__detach", this, 3)([]);
    }
  }, {
    key: "__getNativeConfig",
    value: function __getNativeConfig() {
      var transformsConfig = [];
      var transforms = this._transforms;
      for (var ii = 0, length = transforms.length; ii < length; ii++) {
        var transform = transforms[ii];
        for (var key in transform) {
          var value = transform[key];
          if (value instanceof _AnimatedNode.default) {
            transformsConfig.push({
              type: 'animated',
              property: key,
              nodeTag: value.__getNativeTag()
            });
          } else {
            transformsConfig.push({
              type: 'static',
              property: key,
              value: _NativeAnimatedHelper.default.transformDataType(value)
            });
          }
        }
      }
      if (__DEV__) {
        (0, _NativeAnimatedValidation.validateTransform)(transformsConfig);
      }
      return {
        type: 'transform',
        transforms: transformsConfig,
        debugID: this.__getDebugID()
      };
    }
  }], [{
    key: "from",
    value: function from(transforms) {
      var nodes = flatAnimatedNodes(Array.isArray(transforms) ? transforms : []);
      if (nodes.length === 0) {
        return null;
      }
      return new AnimatedTransform(nodes, transforms);
    }
  }]);
}(_AnimatedWithChildren2.default);
function mapTransforms(transforms, mapFunction) {
  return transforms.map(function (transform) {
    var result = {};
    for (var key in transform) {
      var value = transform[key];
      if (value instanceof _AnimatedNode.default) {
        result[key] = mapFunction(value);
      } else if (Array.isArray(value)) {
        result[key] = value.map(function (element) {
          return element instanceof _AnimatedNode.default ? mapFunction(element) : element;
        });
      } else if (typeof value === 'object') {
        var object = {};
        for (var propertyName in value) {
          var propertyValue = value[propertyName];
          object[propertyName] = propertyValue instanceof _AnimatedNode.default ? mapFunction(propertyValue) : propertyValue;
        }
        result[key] = object;
      } else {
        result[key] = value;
      }
    }
    return result;
  });
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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