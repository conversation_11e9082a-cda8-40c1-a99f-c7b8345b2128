/**
 * Search Filters Component
 *
 * Component Contract:
 * - Provides advanced filtering options for search results
 * - Supports category, location, price range, rating, and availability filters
 * - Implements collapsible filter sections for better UX
 * - Shows active filter count and clear all functionality
 * - Follows accessibility guidelines with proper labels
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useCallback } from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { useTheme } from '../../contexts/ThemeContext';
import { usePerformance } from '../../hooks/usePerformance';
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
} from '../../utils/responsiveUtils';

interface FilterOption {
  id: string;
  label: string;
  value: any;
  count?: number;
}

interface SearchFiltersProps {
  visible: boolean;
  onClose: () => void;
  onFiltersChange: (filters: any) => void;
  activeFilters: any;
  categories?: FilterOption[];
  locations?: FilterOption[];
  priceRanges?: FilterOption[];
  ratings?: FilterOption[];
}

export const SearchFilters: React.FC<SearchFiltersProps> = ({
  visible,
  onClose,
  onFiltersChange,
  activeFilters,
  categories = [],
  locations = [],
  priceRanges = [],
  ratings = [],
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  const [expandedSections, setExpandedSections] = useState<string[]>(['category']);

  const { trackInteraction } = usePerformance({
    componentName: 'SearchFilters',
    trackInteractions: true,
  });

  // Default filter options
  const defaultCategories: FilterOption[] = [
    { id: 'hair', label: 'Hair Services', value: 'hair', count: 45 },
    { id: 'nails', label: 'Nail Services', value: 'nails', count: 32 },
    { id: 'lashes', label: 'Lash Services', value: 'lashes', count: 28 },
    { id: 'braiding', label: 'Braiding', value: 'braiding', count: 22 },
    { id: 'massage', label: 'Massage', value: 'massage', count: 18 },
    { id: 'skincare', label: 'Skincare', value: 'skincare', count: 15 },
  ];

  const defaultPriceRanges: FilterOption[] = [
    { id: 'budget', label: 'Budget ($0-$50)', value: { min: 0, max: 50 } },
    { id: 'moderate', label: 'Moderate ($50-$100)', value: { min: 50, max: 100 } },
    { id: 'premium', label: 'Premium ($100-$200)', value: { min: 100, max: 200 } },
    { id: 'luxury', label: 'Luxury ($200+)', value: { min: 200, max: null } },
  ];

  const defaultRatings: FilterOption[] = [
    { id: '4plus', label: '4+ Stars', value: 4, count: 89 },
    { id: '3plus', label: '3+ Stars', value: 3, count: 156 },
    { id: '2plus', label: '2+ Stars', value: 2, count: 203 },
  ];

  const defaultLocations: FilterOption[] = [
    { id: 'nearby', label: 'Nearby (5km)', value: { radius: 5 }, count: 67 },
    { id: 'city', label: 'City Center', value: { area: 'downtown' }, count: 45 },
    { id: 'north', label: 'North End', value: { area: 'north' }, count: 23 },
    { id: 'south', label: 'South End', value: { area: 'south' }, count: 31 },
  ];

  const toggleSection = useCallback((sectionId: string) => {
    setExpandedSections(prev => 
      prev.includes(sectionId) 
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    );
    trackInteraction('toggleFilterSection', { sectionId });
  }, [trackInteraction]);

  const handleFilterChange = useCallback((filterType: string, value: any) => {
    const newFilters = { ...activeFilters };
    
    if (filterType === 'category') {
      newFilters.categories = newFilters.categories || [];
      if (newFilters.categories.includes(value)) {
        newFilters.categories = newFilters.categories.filter((c: string) => c !== value);
      } else {
        newFilters.categories.push(value);
      }
    } else if (filterType === 'priceRange') {
      newFilters.priceRange = newFilters.priceRange === value ? null : value;
    } else if (filterType === 'rating') {
      newFilters.minRating = newFilters.minRating === value ? null : value;
    } else if (filterType === 'location') {
      newFilters.location = newFilters.location === value ? null : value;
    }

    onFiltersChange(newFilters);
    trackInteraction('filterChange', { filterType, value });
  }, [activeFilters, onFiltersChange, trackInteraction]);

  const clearAllFilters = useCallback(() => {
    onFiltersChange({});
    trackInteraction('clearAllFilters');
  }, [onFiltersChange, trackInteraction]);

  const getActiveFilterCount = useCallback(() => {
    let count = 0;
    if (activeFilters.categories?.length) count += activeFilters.categories.length;
    if (activeFilters.priceRange) count += 1;
    if (activeFilters.minRating) count += 1;
    if (activeFilters.location) count += 1;
    return count;
  }, [activeFilters]);

  const renderFilterSection = (
    title: string,
    sectionId: string,
    options: FilterOption[],
    filterType: string,
    multiSelect: boolean = false
  ) => {
    const isExpanded = expandedSections.includes(sectionId);
    
    return (
      <View style={styles.filterSection}>
        <TouchableOpacity
          style={styles.sectionHeader}
          onPress={() => toggleSection(sectionId)}
          accessibilityRole="button"
          accessibilityLabel={`${title} filter section`}
          accessibilityHint={`Tap to ${isExpanded ? 'collapse' : 'expand'} ${title} filters`}
        >
          <Text style={styles.sectionTitle}>{title}</Text>
          <Ionicons
            name={isExpanded ? 'chevron-up' : 'chevron-down'}
            size={20}
            color={colors.text.secondary}
          />
        </TouchableOpacity>
        
        {isExpanded && (
          <View style={styles.sectionContent}>
            {options.map((option) => {
              const isSelected = multiSelect
                ? activeFilters[filterType]?.includes(option.value)
                : activeFilters[filterType] === option.value;
                
              return (
                <TouchableOpacity
                  key={option.id}
                  style={[styles.filterOption, isSelected && styles.filterOptionSelected]}
                  onPress={() => handleFilterChange(filterType, option.value)}
                  accessibilityRole="checkbox"
                  accessibilityState={{ checked: isSelected }}
                  accessibilityLabel={option.label}
                >
                  <View style={styles.optionContent}>
                    <Text style={[styles.optionLabel, isSelected && styles.optionLabelSelected]}>
                      {option.label}
                    </Text>
                    {option.count && (
                      <Text style={styles.optionCount}>({option.count})</Text>
                    )}
                  </View>
                  {isSelected && (
                    <Ionicons
                      name="checkmark"
                      size={16}
                      color={colors.sage600}
                    />
                  )}
                </TouchableOpacity>
              );
            })}
          </View>
        )}
      </View>
    );
  };

  if (!visible) {
    return null;
  }

  const activeFilterCount = getActiveFilterCount();

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Filters</Text>
        <View style={styles.headerActions}>
          {activeFilterCount > 0 && (
            <TouchableOpacity
              style={styles.clearButton}
              onPress={clearAllFilters}
              accessibilityRole="button"
              accessibilityLabel="Clear all filters"
            >
              <Text style={styles.clearButtonText}>Clear All</Text>
            </TouchableOpacity>
          )}
          <TouchableOpacity
            style={styles.closeButton}
            onPress={onClose}
            accessibilityRole="button"
            accessibilityLabel="Close filters"
          >
            <Ionicons name="close" size={24} color={colors.text.primary} />
          </TouchableOpacity>
        </View>
      </View>

      {activeFilterCount > 0 && (
        <View style={styles.activeFiltersIndicator}>
          <Text style={styles.activeFiltersText}>
            {activeFilterCount} filter{activeFilterCount > 1 ? 's' : ''} active
          </Text>
        </View>
      )}

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderFilterSection('Category', 'category', categories.length ? categories : defaultCategories, 'categories', true)}
        {renderFilterSection('Price Range', 'price', priceRanges.length ? priceRanges : defaultPriceRanges, 'priceRange')}
        {renderFilterSection('Rating', 'rating', ratings.length ? ratings : defaultRatings, 'minRating')}
        {renderFilterSection('Location', 'location', locations.length ? locations : defaultLocations, 'location')}
      </ScrollView>
    </View>
  );
};

// Styles
const createStyles = (colors: any) => ({
  container: {
    backgroundColor: colors.background.primary,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: getResponsiveSpacing(20),
    paddingVertical: getResponsiveSpacing(16),
    borderBottomWidth: 1,
    borderBottomColor: colors.border.light,
  },
  headerTitle: {
    fontSize: getResponsiveFontSize(18),
    fontWeight: '600',
    color: colors.text.primary,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  clearButton: {
    marginRight: getResponsiveSpacing(16),
    paddingHorizontal: getResponsiveSpacing(8),
    paddingVertical: getResponsiveSpacing(4),
  },
  clearButtonText: {
    fontSize: getResponsiveFontSize(14),
    color: colors.sage600,
    fontWeight: '500',
  },
  closeButton: {
    padding: getResponsiveSpacing(4),
  },
  activeFiltersIndicator: {
    backgroundColor: colors.sage50,
    paddingHorizontal: getResponsiveSpacing(20),
    paddingVertical: getResponsiveSpacing(8),
  },
  activeFiltersText: {
    fontSize: getResponsiveFontSize(12),
    color: colors.sage700,
    fontWeight: '500',
  },
  content: {
    paddingHorizontal: getResponsiveSpacing(20),
  },
  filterSection: {
    marginVertical: getResponsiveSpacing(8),
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: getResponsiveSpacing(12),
  },
  sectionTitle: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '600',
    color: colors.text.primary,
  },
  sectionContent: {
    paddingLeft: getResponsiveSpacing(8),
  },
  filterOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: getResponsiveSpacing(10),
    paddingHorizontal: getResponsiveSpacing(12),
    borderRadius: 8,
    marginVertical: getResponsiveSpacing(2),
  },
  filterOptionSelected: {
    backgroundColor: colors.sage50,
  },
  optionContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  optionLabel: {
    fontSize: getResponsiveFontSize(14),
    color: colors.text.primary,
  },
  optionLabelSelected: {
    fontWeight: '500',
    color: colors.sage700,
  },
  optionCount: {
    fontSize: getResponsiveFontSize(12),
    color: colors.text.secondary,
    marginLeft: getResponsiveSpacing(8),
  },
});

export default SearchFilters;
