17b815bb2036d9f8af10cd8cffcefc1f
'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var MessageQueue = require("./MessageQueue").default;
var BatchedBridge = new MessageQueue();
Object.defineProperty(global, '__fbBatchedBridge', {
  configurable: true,
  value: BatchedBridge
});
var _default = exports.default = BatchedBridge;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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