{"version": 3, "names": ["_SoundManager", "_interopRequireDefault", "require", "_ReactNativeFeatureFlags", "_UIManager", "_Rect", "_Platform", "_HoverState", "_PressabilityPerformanceEventEmitter", "_invariant", "Transitions", "Object", "freeze", "NOT_RESPONDER", "DELAY", "RESPONDER_GRANT", "RESPONDER_RELEASE", "RESPONDER_TERMINATED", "ENTER_PRESS_RECT", "LEAVE_PRESS_RECT", "LONG_PRESS_DETECTED", "RESPONDER_INACTIVE_PRESS_IN", "RESPONDER_INACTIVE_PRESS_OUT", "RESPONDER_ACTIVE_PRESS_IN", "RESPONDER_ACTIVE_PRESS_OUT", "RESPONDER_ACTIVE_LONG_PRESS_IN", "RESPONDER_ACTIVE_LONG_PRESS_OUT", "ERROR", "isActiveSignal", "signal", "isActivationSignal", "isPressInSignal", "isTerminalSignal", "DEFAULT_LONG_PRESS_DELAY_MS", "DEFAULT_PRESS_RECT_OFFSETS", "bottom", "left", "right", "top", "DEFAULT_MIN_PRESS_DURATION", "DEFAULT_LONG_PRESS_DEACTIVATION_DISTANCE", "longPressDeactivationDistance", "Pressability", "exports", "default", "config", "_this", "_classCallCheck2", "_eventHandlers", "_hoverInDelayTimeout", "_hoverOutDelayTimeout", "_isHovered", "_longPressDelayTimeout", "_pressDelayTimeout", "_pressOutDelayTimeout", "_responderID", "_responderRegion", "_touchState", "_measure<PERSON>allback", "width", "height", "pageX", "pageY", "configure", "_createClass2", "key", "value", "_config", "reset", "_cancelHoverInDelayTimeout", "_cancelHoverOutDelayTimeout", "_cancelLongPressDelayTimeout", "_cancelPressDelayTimeout", "_cancelPressOutDelayTimeout", "getEventHandlers", "_createEventHandlers", "_this2", "focusEventHandlers", "onBlur", "event", "onFocus", "responderEventHandlers", "onStartShouldSetResponder", "_disabled", "disabled", "onResponderGrant", "persist", "currentTarget", "_receiveSignal", "delayPressIn", "normalizeDelay", "setTimeout", "delayLongPress", "_handleLongPress", "blockNativeResponder", "onResponderMove", "onPressMove", "responderRegion", "touch", "getTouchFromPressEvent", "_touchActivatePosition", "deltaX", "deltaY", "Math", "hypot", "_isTouchWithinResponderRegion", "onResponderRelease", "onResponderTerminate", "onResponderTerminationRequest", "cancelable", "onClick", "_event$nativeEvent", "nativeEvent", "hasOwnProperty", "target", "stopPropagation", "_this2$_config", "onPress", "process", "env", "NODE_ENV", "testOnly_pressabilityConfig", "ReactNativeFeatureFlags", "shouldPressibilityUseW3CPointerEventsForHover", "hoverPointerEvents", "onPointerEnter", "undefined", "onPointerLeave", "_this$_config", "onHoverIn", "onHoverOut", "delayHoverIn", "convertPointerEventToMouseEvent", "delayHoverOut", "assign", "mouseEventHandlers", "Platform", "OS", "onMouseEnter", "isHoverEnabled", "onMouseLeave", "_Transitions$prevStat", "timestamp", "PressabilityPerformanceEventEmitter", "emitEvent", "nativeTimestamp", "prevState", "nextState", "invariant", "_performTransitionSideEffects", "isInitialTransition", "isActivationTransition", "_measureResponderRegion", "onLongPress", "isPrevActive", "isNextActive", "_activate", "_deactivate", "_this$_config2", "android_disableSound", "isPressCanceledByLongPress", "SoundManager", "playTouchSound", "onPressIn", "_getTouchFromPressEve", "_touchActivateTime", "Date", "now", "onPressOut", "_this$_touchActivateT", "minPressDuration", "pressDuration", "delayPressOut", "max", "UIManager", "measure", "_pressRectOffset$bott", "_pressRectOffset$left", "_pressRectOffset$righ", "_pressRectOffset$top", "hitSlop", "normalizeRect", "pressRectOffset", "regionBottom", "regionLeft", "regionRight", "regionTop", "clearTimeout", "setLongPressDeactivationDistance", "distance", "delay", "min", "arguments", "length", "fallback", "_event$nativeEvent2", "changedTouches", "touches", "input", "_input$nativeEvent", "clientX", "clientY", "timeStamp"], "sources": ["Pressability.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\nimport type {HostInstance} from '../../src/private/types/HostInstance';\nimport type {\n  BlurEvent,\n  FocusEvent,\n  GestureResponderEvent,\n  MouseEvent,\n} from '../Types/CoreEventTypes';\n\nimport SoundManager from '../Components/Sound/SoundManager';\nimport ReactNativeFeatureFlags from '../ReactNative/ReactNativeFeatureFlags';\nimport UIManager from '../ReactNative/UIManager';\nimport {type RectOrSize, normalizeRect} from '../StyleSheet/Rect';\nimport {type PointerEvent} from '../Types/CoreEventTypes';\nimport Platform from '../Utilities/Platform';\nimport {isHoverEnabled} from './HoverState';\nimport PressabilityPerformanceEventEmitter from './PressabilityPerformanceEventEmitter.js';\nimport {type PressabilityTouchSignal as TouchSignal} from './PressabilityTypes.js';\nimport invariant from 'invariant';\n\nexport type PressabilityConfig = $ReadOnly<{\n  /**\n   * Whether a press gesture can be interrupted by a parent gesture such as a\n   * scroll event. Defaults to true.\n   */\n  cancelable?: ?boolean,\n\n  /**\n   * Whether to disable initialization of the press gesture.\n   */\n  disabled?: ?boolean,\n\n  /**\n   * Amount to extend the `VisualRect` by to create `HitRect`.\n   */\n  hitSlop?: ?RectOrSize,\n\n  /**\n   * Amount to extend the `HitRect` by to create `PressRect`.\n   */\n  pressRectOffset?: ?RectOrSize,\n\n  /**\n   * Whether to disable the systemm sound when `onPress` fires on Android.\n   **/\n  android_disableSound?: ?boolean,\n\n  /**\n   * Duration to wait after hover in before calling `onHoverIn`.\n   */\n  delayHoverIn?: ?number,\n\n  /**\n   * Duration to wait after hover out before calling `onHoverOut`.\n   */\n  delayHoverOut?: ?number,\n\n  /**\n   * Duration (in addition to `delayPressIn`) after which a press gesture is\n   * considered a long press gesture. Defaults to 500 (milliseconds).\n   */\n  delayLongPress?: ?number,\n\n  /**\n   * Duration to wait after press down before calling `onPressIn`.\n   */\n  delayPressIn?: ?number,\n\n  /**\n   * Duration to wait after letting up before calling `onPressOut`.\n   */\n  delayPressOut?: ?number,\n\n  /**\n   * Minimum duration to wait between calling `onPressIn` and `onPressOut`.\n   */\n  minPressDuration?: ?number,\n\n  /**\n   * Called after the element loses focus.\n   */\n  onBlur?: ?(event: BlurEvent) => mixed,\n\n  /**\n   * Called after the element is focused.\n   */\n  onFocus?: ?(event: FocusEvent) => mixed,\n\n  /**\n   * Called when the hover is activated to provide visual feedback.\n   */\n  onHoverIn?: ?(event: MouseEvent) => mixed,\n\n  /**\n   * Called when the hover is deactivated to undo visual feedback.\n   */\n  onHoverOut?: ?(event: MouseEvent) => mixed,\n\n  /**\n   * Called when a long press gesture has been triggered.\n   */\n  onLongPress?: ?(event: GestureResponderEvent) => mixed,\n\n  /**\n   * Called when a press gesture has been triggered.\n   */\n  onPress?: ?(event: GestureResponderEvent) => mixed,\n\n  /**\n   * Called when the press is activated to provide visual feedback.\n   */\n  onPressIn?: ?(event: GestureResponderEvent) => mixed,\n\n  /**\n   * Called when the press location moves. (This should rarely be used.)\n   */\n  onPressMove?: ?(event: GestureResponderEvent) => mixed,\n\n  /**\n   * Called when the press is deactivated to undo visual feedback.\n   */\n  onPressOut?: ?(event: GestureResponderEvent) => mixed,\n\n  /**\n   * Whether to prevent any other native components from becoming responder\n   * while this pressable is responder.\n   */\n  blockNativeResponder?: ?boolean,\n}>;\n\nexport type EventHandlers = $ReadOnly<{\n  onBlur: (event: BlurEvent) => void,\n  onClick: (event: GestureResponderEvent) => void,\n  onFocus: (event: FocusEvent) => void,\n  onMouseEnter?: (event: MouseEvent) => void,\n  onMouseLeave?: (event: MouseEvent) => void,\n  onPointerEnter?: (event: PointerEvent) => void,\n  onPointerLeave?: (event: PointerEvent) => void,\n  onResponderGrant: (event: GestureResponderEvent) => void | boolean,\n  onResponderMove: (event: GestureResponderEvent) => void,\n  onResponderRelease: (event: GestureResponderEvent) => void,\n  onResponderTerminate: (event: GestureResponderEvent) => void,\n  onResponderTerminationRequest: () => boolean,\n  onStartShouldSetResponder: () => boolean,\n}>;\n\ntype TouchState =\n  | 'NOT_RESPONDER'\n  | 'RESPONDER_INACTIVE_PRESS_IN'\n  | 'RESPONDER_INACTIVE_PRESS_OUT'\n  | 'RESPONDER_ACTIVE_PRESS_IN'\n  | 'RESPONDER_ACTIVE_PRESS_OUT'\n  | 'RESPONDER_ACTIVE_LONG_PRESS_IN'\n  | 'RESPONDER_ACTIVE_LONG_PRESS_OUT'\n  | 'ERROR';\n\nconst Transitions = Object.freeze({\n  NOT_RESPONDER: {\n    DELAY: 'ERROR',\n    RESPONDER_GRANT: 'RESPONDER_INACTIVE_PRESS_IN',\n    RESPONDER_RELEASE: 'ERROR',\n    RESPONDER_TERMINATED: 'ERROR',\n    ENTER_PRESS_RECT: 'ERROR',\n    LEAVE_PRESS_RECT: 'ERROR',\n    LONG_PRESS_DETECTED: 'ERROR',\n  },\n  RESPONDER_INACTIVE_PRESS_IN: {\n    DELAY: 'RESPONDER_ACTIVE_PRESS_IN',\n    RESPONDER_GRANT: 'ERROR',\n    RESPONDER_RELEASE: 'NOT_RESPONDER',\n    RESPONDER_TERMINATED: 'NOT_RESPONDER',\n    ENTER_PRESS_RECT: 'RESPONDER_INACTIVE_PRESS_IN',\n    LEAVE_PRESS_RECT: 'RESPONDER_INACTIVE_PRESS_OUT',\n    LONG_PRESS_DETECTED: 'ERROR',\n  },\n  RESPONDER_INACTIVE_PRESS_OUT: {\n    DELAY: 'RESPONDER_ACTIVE_PRESS_OUT',\n    RESPONDER_GRANT: 'ERROR',\n    RESPONDER_RELEASE: 'NOT_RESPONDER',\n    RESPONDER_TERMINATED: 'NOT_RESPONDER',\n    ENTER_PRESS_RECT: 'RESPONDER_INACTIVE_PRESS_IN',\n    LEAVE_PRESS_RECT: 'RESPONDER_INACTIVE_PRESS_OUT',\n    LONG_PRESS_DETECTED: 'ERROR',\n  },\n  RESPONDER_ACTIVE_PRESS_IN: {\n    DELAY: 'ERROR',\n    RESPONDER_GRANT: 'ERROR',\n    RESPONDER_RELEASE: 'NOT_RESPONDER',\n    RESPONDER_TERMINATED: 'NOT_RESPONDER',\n    ENTER_PRESS_RECT: 'RESPONDER_ACTIVE_PRESS_IN',\n    LEAVE_PRESS_RECT: 'RESPONDER_ACTIVE_PRESS_OUT',\n    LONG_PRESS_DETECTED: 'RESPONDER_ACTIVE_LONG_PRESS_IN',\n  },\n  RESPONDER_ACTIVE_PRESS_OUT: {\n    DELAY: 'ERROR',\n    RESPONDER_GRANT: 'ERROR',\n    RESPONDER_RELEASE: 'NOT_RESPONDER',\n    RESPONDER_TERMINATED: 'NOT_RESPONDER',\n    ENTER_PRESS_RECT: 'RESPONDER_ACTIVE_PRESS_IN',\n    LEAVE_PRESS_RECT: 'RESPONDER_ACTIVE_PRESS_OUT',\n    LONG_PRESS_DETECTED: 'ERROR',\n  },\n  RESPONDER_ACTIVE_LONG_PRESS_IN: {\n    DELAY: 'ERROR',\n    RESPONDER_GRANT: 'ERROR',\n    RESPONDER_RELEASE: 'NOT_RESPONDER',\n    RESPONDER_TERMINATED: 'NOT_RESPONDER',\n    ENTER_PRESS_RECT: 'RESPONDER_ACTIVE_LONG_PRESS_IN',\n    LEAVE_PRESS_RECT: 'RESPONDER_ACTIVE_LONG_PRESS_OUT',\n    LONG_PRESS_DETECTED: 'RESPONDER_ACTIVE_LONG_PRESS_IN',\n  },\n  RESPONDER_ACTIVE_LONG_PRESS_OUT: {\n    DELAY: 'ERROR',\n    RESPONDER_GRANT: 'ERROR',\n    RESPONDER_RELEASE: 'NOT_RESPONDER',\n    RESPONDER_TERMINATED: 'NOT_RESPONDER',\n    ENTER_PRESS_RECT: 'RESPONDER_ACTIVE_LONG_PRESS_IN',\n    LEAVE_PRESS_RECT: 'RESPONDER_ACTIVE_LONG_PRESS_OUT',\n    LONG_PRESS_DETECTED: 'ERROR',\n  },\n  ERROR: {\n    DELAY: 'NOT_RESPONDER',\n    RESPONDER_GRANT: 'RESPONDER_INACTIVE_PRESS_IN',\n    RESPONDER_RELEASE: 'NOT_RESPONDER',\n    RESPONDER_TERMINATED: 'NOT_RESPONDER',\n    ENTER_PRESS_RECT: 'NOT_RESPONDER',\n    LEAVE_PRESS_RECT: 'NOT_RESPONDER',\n    LONG_PRESS_DETECTED: 'NOT_RESPONDER',\n  },\n});\n\nconst isActiveSignal = (signal: TouchState) =>\n  signal === 'RESPONDER_ACTIVE_PRESS_IN' ||\n  signal === 'RESPONDER_ACTIVE_LONG_PRESS_IN';\n\nconst isActivationSignal = (signal: TouchState) =>\n  signal === 'RESPONDER_ACTIVE_PRESS_OUT' ||\n  signal === 'RESPONDER_ACTIVE_PRESS_IN';\n\nconst isPressInSignal = (signal: TouchState) =>\n  signal === 'RESPONDER_INACTIVE_PRESS_IN' ||\n  signal === 'RESPONDER_ACTIVE_PRESS_IN' ||\n  signal === 'RESPONDER_ACTIVE_LONG_PRESS_IN';\n\nconst isTerminalSignal = (signal: TouchSignal) =>\n  signal === 'RESPONDER_TERMINATED' || signal === 'RESPONDER_RELEASE';\n\nconst DEFAULT_LONG_PRESS_DELAY_MS = 500;\nconst DEFAULT_PRESS_RECT_OFFSETS = {\n  bottom: 30,\n  left: 20,\n  right: 20,\n  top: 20,\n};\nconst DEFAULT_MIN_PRESS_DURATION = 130;\n\nconst DEFAULT_LONG_PRESS_DEACTIVATION_DISTANCE = 10;\nlet longPressDeactivationDistance = DEFAULT_LONG_PRESS_DEACTIVATION_DISTANCE;\n\n/**\n * Pressability implements press handling capabilities.\n *\n * =========================== Pressability Tutorial ===========================\n *\n * The `Pressability` class helps you create press interactions by analyzing the\n * geometry of elements and observing when another responder (e.g. ScrollView)\n * has stolen the touch lock. It offers hooks for your component to provide\n * interaction feedback to the user:\n *\n * - When a press has activated (e.g. highlight an element)\n * - When a press has deactivated (e.g. un-highlight an element)\n * - When a press should trigger an action, meaning it activated and deactivated\n *   while within the geometry of the element without the lock being stolen.\n *\n * A high quality interaction isn't as simple as you might think. There should\n * be a slight delay before activation. Moving your finger beyond an element's\n * bounds should trigger deactivation, but moving the same finger back within an\n * element's bounds should trigger reactivation.\n *\n * This should be consumed by functional components using `usePressability`. The\n * following steps are only relevant for using `Pressability` in classes:\n *\n * 1. Instantiate `Pressability` and store it on your component's state.\n *\n *    state = {\n *      pressability: new Pressability({\n *        // ...\n *      }),\n *    };\n *\n * 2. Choose the rendered component who should collect the press events. On that\n *    element, spread `pressability.getEventHandlers()` into its props.\n *\n *    return (\n *      <View {...this.state.pressability.getEventHandlers()} />\n *    );\n *\n * 3. Update `Pressability` when your component mounts, updates, and unmounts.\n *\n *    componentDidMount() {\n *      this.state.pressability.configure(...);\n *    }\n *\n *    componentDidUpdate() {\n *      this.state.pressability.configure(...);\n *    }\n *\n *    componentWillUnmount() {\n *      this.state.pressability.reset();\n *    }\n *\n * ==================== Pressability Implementation Details ====================\n *\n * `Pressability` only assumes that there exists a `HitRect` node. The `PressRect`\n * is an abstract box that is extended beyond the `HitRect`.\n *\n * # Geometry\n *\n *  ┌────────────────────────┐\n *  │  ┌──────────────────┐  │ - Presses start anywhere within `HitRect`, which\n *  │  │  ┌────────────┐  │  │   is expanded via the prop `hitSlop`.\n *  │  │  │ VisualRect │  │  │\n *  │  │  └────────────┘  │  │ - When pressed down for sufficient amount of time\n *  │  │    HitRect       │  │   before letting up, `VisualRect` activates for\n *  │  └──────────────────┘  │   as long as the press stays within `PressRect`.\n *  │       PressRect    o   │\n *  └────────────────────│───┘\n *          Out Region   └────── `PressRect`, which is expanded via the prop\n *                               `pressRectOffset`, allows presses to move\n *                               beyond `HitRect` while maintaining activation\n *                               and being eligible for a \"press\".\n *\n * # State Machine\n *\n * ┌───────────────┐ ◀──── RESPONDER_RELEASE\n * │ NOT_RESPONDER │\n * └───┬───────────┘ ◀──── RESPONDER_TERMINATED\n *     │\n *     │ RESPONDER_GRANT (HitRect)\n *     │\n *     ▼\n * ┌─────────────────────┐          ┌───────────────────┐              ┌───────────────────┐\n * │ RESPONDER_INACTIVE_ │  DELAY   │ RESPONDER_ACTIVE_ │  T + DELAY   │ RESPONDER_ACTIVE_ │\n * │ PRESS_IN            ├────────▶ │ PRESS_IN          ├────────────▶ │ LONG_PRESS_IN     │\n * └─┬───────────────────┘          └─┬─────────────────┘              └─┬─────────────────┘\n *   │           ▲                    │           ▲                      │           ▲\n *   │LEAVE_     │                    │LEAVE_     │                      │LEAVE_     │\n *   │PRESS_RECT │ENTER_              │PRESS_RECT │ENTER_                │PRESS_RECT │ENTER_\n *   │           │PRESS_RECT          │           │PRESS_RECT            │           │PRESS_RECT\n *   ▼           │                    ▼           │                      ▼           │\n * ┌─────────────┴───────┐          ┌─────────────┴─────┐              ┌─────────────┴─────┐\n * │ RESPONDER_INACTIVE_ │  DELAY   │ RESPONDER_ACTIVE_ │              │ RESPONDER_ACTIVE_ │\n * │ PRESS_OUT           ├────────▶ │ PRESS_OUT         │              │ LONG_PRESS_OUT    │\n * └─────────────────────┘          └───────────────────┘              └───────────────────┘\n *\n * T + DELAY => LONG_PRESS_DELAY + DELAY\n *\n * Not drawn are the side effects of each transition. The most important side\n * effect is the invocation of `onPress` and `onLongPress` that occur when a\n * responder is release while in the \"press in\" states.\n */\nexport default class Pressability {\n  _config: PressabilityConfig;\n  _eventHandlers: ?EventHandlers = null;\n  _hoverInDelayTimeout: ?TimeoutID = null;\n  _hoverOutDelayTimeout: ?TimeoutID = null;\n  _isHovered: boolean = false;\n  _longPressDelayTimeout: ?TimeoutID = null;\n  _pressDelayTimeout: ?TimeoutID = null;\n  _pressOutDelayTimeout: ?TimeoutID = null;\n  _responderID: ?number | HostInstance = null;\n  _responderRegion: ?$ReadOnly<{\n    bottom: number,\n    left: number,\n    right: number,\n    top: number,\n  }> = null;\n  _touchActivatePosition: ?$ReadOnly<{\n    pageX: number,\n    pageY: number,\n  }>;\n  _touchActivateTime: ?number;\n  _touchState: TouchState = 'NOT_RESPONDER';\n\n  constructor(config: PressabilityConfig) {\n    this.configure(config);\n  }\n\n  configure(config: PressabilityConfig): void {\n    this._config = config;\n  }\n\n  /**\n   * Resets any pending timers. This should be called on unmount.\n   */\n  reset(): void {\n    this._cancelHoverInDelayTimeout();\n    this._cancelHoverOutDelayTimeout();\n    this._cancelLongPressDelayTimeout();\n    this._cancelPressDelayTimeout();\n    this._cancelPressOutDelayTimeout();\n\n    // Ensure that, if any async event handlers are fired after unmount\n    // due to a race, we don't call any configured callbacks.\n    this._config = Object.freeze({});\n  }\n\n  /**\n   * Returns a set of props to spread into the interactive element.\n   */\n  getEventHandlers(): EventHandlers {\n    if (this._eventHandlers == null) {\n      this._eventHandlers = this._createEventHandlers();\n    }\n    return this._eventHandlers;\n  }\n\n  static setLongPressDeactivationDistance(distance: number): void {\n    longPressDeactivationDistance = distance;\n  }\n\n  _createEventHandlers(): EventHandlers {\n    const focusEventHandlers = {\n      onBlur: (event: BlurEvent): void => {\n        const {onBlur} = this._config;\n        if (onBlur != null) {\n          onBlur(event);\n        }\n      },\n      onFocus: (event: FocusEvent): void => {\n        const {onFocus} = this._config;\n        if (onFocus != null) {\n          onFocus(event);\n        }\n      },\n    };\n\n    const responderEventHandlers = {\n      onStartShouldSetResponder: (): boolean => {\n        const {disabled} = this._config;\n        return !disabled ?? true;\n      },\n\n      onResponderGrant: (event: GestureResponderEvent): void | boolean => {\n        event.persist();\n\n        this._cancelPressOutDelayTimeout();\n\n        this._responderID = event.currentTarget;\n        this._touchState = 'NOT_RESPONDER';\n        this._receiveSignal('RESPONDER_GRANT', event);\n\n        const delayPressIn = normalizeDelay(this._config.delayPressIn);\n        if (delayPressIn > 0) {\n          this._pressDelayTimeout = setTimeout(() => {\n            this._receiveSignal('DELAY', event);\n          }, delayPressIn);\n        } else {\n          this._receiveSignal('DELAY', event);\n        }\n\n        const delayLongPress = normalizeDelay(\n          this._config.delayLongPress,\n          10,\n          DEFAULT_LONG_PRESS_DELAY_MS - delayPressIn,\n        );\n        this._longPressDelayTimeout = setTimeout(() => {\n          this._handleLongPress(event);\n        }, delayLongPress + delayPressIn);\n\n        return this._config.blockNativeResponder === true;\n      },\n\n      onResponderMove: (event: GestureResponderEvent): void => {\n        const {onPressMove} = this._config;\n        if (onPressMove != null) {\n          onPressMove(event);\n        }\n\n        // Region may not have finished being measured, yet.\n        const responderRegion = this._responderRegion;\n        if (responderRegion == null) {\n          return;\n        }\n\n        const touch = getTouchFromPressEvent(event);\n        if (touch == null) {\n          this._cancelLongPressDelayTimeout();\n          this._receiveSignal('LEAVE_PRESS_RECT', event);\n          return;\n        }\n\n        if (this._touchActivatePosition != null) {\n          const deltaX = this._touchActivatePosition.pageX - touch.pageX;\n          const deltaY = this._touchActivatePosition.pageY - touch.pageY;\n          if (Math.hypot(deltaX, deltaY) > longPressDeactivationDistance) {\n            this._cancelLongPressDelayTimeout();\n          }\n        }\n\n        if (this._isTouchWithinResponderRegion(touch, responderRegion)) {\n          this._receiveSignal('ENTER_PRESS_RECT', event);\n        } else {\n          this._cancelLongPressDelayTimeout();\n          this._receiveSignal('LEAVE_PRESS_RECT', event);\n        }\n      },\n\n      onResponderRelease: (event: GestureResponderEvent): void => {\n        this._receiveSignal('RESPONDER_RELEASE', event);\n      },\n\n      onResponderTerminate: (event: GestureResponderEvent): void => {\n        this._receiveSignal('RESPONDER_TERMINATED', event);\n      },\n\n      onResponderTerminationRequest: (): boolean => {\n        const {cancelable} = this._config;\n        return cancelable ?? true;\n      },\n\n      onClick: (event: GestureResponderEvent): void => {\n        // If event has `pointerType`, it was emitted from a PointerEvent and\n        // we should ignore it to avoid triggering `onPress` twice.\n        if (event?.nativeEvent?.hasOwnProperty?.('pointerType')) {\n          return;\n        }\n\n        // for non-pointer click events (e.g. accessibility clicks), we should only dispatch when we're the \"real\" target\n        // in particular, we shouldn't respond to clicks from nested pressables\n        if (event?.currentTarget !== event?.target) {\n          event?.stopPropagation();\n          return;\n        }\n\n        const {onPress, disabled} = this._config;\n        if (onPress != null && disabled !== true) {\n          onPress(event);\n        }\n      },\n    };\n\n    if (process.env.NODE_ENV === 'test') {\n      // We are setting this in order to find this node in ReactNativeTestTools\n      // $FlowFixMe[prop-missing]\n      responderEventHandlers.onStartShouldSetResponder.testOnly_pressabilityConfig =\n        () => this._config;\n    }\n\n    if (\n      ReactNativeFeatureFlags.shouldPressibilityUseW3CPointerEventsForHover()\n    ) {\n      const hoverPointerEvents = {\n        onPointerEnter: (undefined: void | (PointerEvent => void)),\n        onPointerLeave: (undefined: void | (PointerEvent => void)),\n      };\n      const {onHoverIn, onHoverOut} = this._config;\n      if (onHoverIn != null) {\n        hoverPointerEvents.onPointerEnter = (event: PointerEvent) => {\n          this._isHovered = true;\n          this._cancelHoverOutDelayTimeout();\n          if (onHoverIn != null) {\n            const delayHoverIn = normalizeDelay(this._config.delayHoverIn);\n            if (delayHoverIn > 0) {\n              event.persist();\n              this._hoverInDelayTimeout = setTimeout(() => {\n                onHoverIn(convertPointerEventToMouseEvent(event));\n              }, delayHoverIn);\n            } else {\n              onHoverIn(convertPointerEventToMouseEvent(event));\n            }\n          }\n        };\n      }\n      if (onHoverOut != null) {\n        hoverPointerEvents.onPointerLeave = (event: PointerEvent) => {\n          if (this._isHovered) {\n            this._isHovered = false;\n            this._cancelHoverInDelayTimeout();\n            if (onHoverOut != null) {\n              const delayHoverOut = normalizeDelay(this._config.delayHoverOut);\n              if (delayHoverOut > 0) {\n                event.persist();\n                this._hoverOutDelayTimeout = setTimeout(() => {\n                  onHoverOut(convertPointerEventToMouseEvent(event));\n                }, delayHoverOut);\n              } else {\n                onHoverOut(convertPointerEventToMouseEvent(event));\n              }\n            }\n          }\n        };\n      }\n      return {\n        ...focusEventHandlers,\n        ...responderEventHandlers,\n        ...hoverPointerEvents,\n      };\n    } else {\n      const mouseEventHandlers =\n        Platform.OS === 'ios' || Platform.OS === 'android'\n          ? null\n          : {\n              onMouseEnter: (event: MouseEvent): void => {\n                if (isHoverEnabled()) {\n                  this._isHovered = true;\n                  this._cancelHoverOutDelayTimeout();\n                  const {onHoverIn} = this._config;\n                  if (onHoverIn != null) {\n                    const delayHoverIn = normalizeDelay(\n                      this._config.delayHoverIn,\n                    );\n                    if (delayHoverIn > 0) {\n                      event.persist();\n                      this._hoverInDelayTimeout = setTimeout(() => {\n                        onHoverIn(event);\n                      }, delayHoverIn);\n                    } else {\n                      onHoverIn(event);\n                    }\n                  }\n                }\n              },\n\n              onMouseLeave: (event: MouseEvent): void => {\n                if (this._isHovered) {\n                  this._isHovered = false;\n                  this._cancelHoverInDelayTimeout();\n                  const {onHoverOut} = this._config;\n                  if (onHoverOut != null) {\n                    const delayHoverOut = normalizeDelay(\n                      this._config.delayHoverOut,\n                    );\n                    if (delayHoverOut > 0) {\n                      event.persist();\n                      this._hoverInDelayTimeout = setTimeout(() => {\n                        onHoverOut(event);\n                      }, delayHoverOut);\n                    } else {\n                      onHoverOut(event);\n                    }\n                  }\n                }\n              },\n            };\n      return {\n        ...focusEventHandlers,\n        ...responderEventHandlers,\n        ...mouseEventHandlers,\n      };\n    }\n  }\n\n  /**\n   * Receives a state machine signal, performs side effects of the transition\n   * and stores the new state. Validates the transition as well.\n   */\n  _receiveSignal(signal: TouchSignal, event: GestureResponderEvent): void {\n    // Especially on iOS, not all events have timestamps associated.\n    // For telemetry purposes, this doesn't matter too much, as long as *some* do.\n    // Since the native timestamp is integral for logging telemetry, just skip\n    // events if they don't have a timestamp attached.\n    if (event.nativeEvent.timestamp != null) {\n      PressabilityPerformanceEventEmitter.emitEvent(() => {\n        return {\n          signal,\n          nativeTimestamp: event.nativeEvent.timestamp,\n        };\n      });\n    }\n\n    const prevState = this._touchState;\n    const nextState = Transitions[prevState]?.[signal];\n    if (this._responderID == null && signal === 'RESPONDER_RELEASE') {\n      return;\n    }\n    invariant(\n      nextState != null && nextState !== 'ERROR',\n      'Pressability: Invalid signal `%s` for state `%s` on responder: %s',\n      signal,\n      prevState,\n      typeof this._responderID === 'number'\n        ? this._responderID\n        : '<<host component>>',\n    );\n    if (prevState !== nextState) {\n      this._performTransitionSideEffects(prevState, nextState, signal, event);\n      this._touchState = nextState;\n    }\n  }\n\n  /**\n   * Performs a transition between touchable states and identify any activations\n   * or deactivations (and callback invocations).\n   */\n  _performTransitionSideEffects(\n    prevState: TouchState,\n    nextState: TouchState,\n    signal: TouchSignal,\n    event: GestureResponderEvent,\n  ): void {\n    if (isTerminalSignal(signal)) {\n      this._touchActivatePosition = null;\n      this._cancelLongPressDelayTimeout();\n    }\n\n    const isInitialTransition =\n      prevState === 'NOT_RESPONDER' &&\n      nextState === 'RESPONDER_INACTIVE_PRESS_IN';\n\n    const isActivationTransition =\n      !isActivationSignal(prevState) && isActivationSignal(nextState);\n\n    if (isInitialTransition || isActivationTransition) {\n      this._measureResponderRegion();\n    }\n\n    if (isPressInSignal(prevState) && signal === 'LONG_PRESS_DETECTED') {\n      const {onLongPress} = this._config;\n      if (onLongPress != null) {\n        onLongPress(event);\n      }\n    }\n\n    const isPrevActive = isActiveSignal(prevState);\n    const isNextActive = isActiveSignal(nextState);\n\n    if (!isPrevActive && isNextActive) {\n      this._activate(event);\n    } else if (isPrevActive && !isNextActive) {\n      this._deactivate(event);\n    }\n\n    if (isPressInSignal(prevState) && signal === 'RESPONDER_RELEASE') {\n      // If we never activated (due to delays), activate and deactivate now.\n      if (!isNextActive && !isPrevActive) {\n        this._activate(event);\n        this._deactivate(event);\n      }\n      const {onLongPress, onPress, android_disableSound} = this._config;\n      if (onPress != null) {\n        const isPressCanceledByLongPress =\n          onLongPress != null && prevState === 'RESPONDER_ACTIVE_LONG_PRESS_IN';\n        if (!isPressCanceledByLongPress) {\n          if (Platform.OS === 'android' && android_disableSound !== true) {\n            SoundManager.playTouchSound();\n          }\n          onPress(event);\n        }\n      }\n    }\n\n    this._cancelPressDelayTimeout();\n  }\n\n  _activate(event: GestureResponderEvent): void {\n    const {onPressIn} = this._config;\n    const {pageX, pageY} = getTouchFromPressEvent(event);\n    this._touchActivatePosition = {pageX, pageY};\n    this._touchActivateTime = Date.now();\n    if (onPressIn != null) {\n      onPressIn(event);\n    }\n  }\n\n  _deactivate(event: GestureResponderEvent): void {\n    const {onPressOut} = this._config;\n    if (onPressOut != null) {\n      const minPressDuration = normalizeDelay(\n        this._config.minPressDuration,\n        0,\n        DEFAULT_MIN_PRESS_DURATION,\n      );\n      const pressDuration = Date.now() - (this._touchActivateTime ?? 0);\n      const delayPressOut = Math.max(\n        minPressDuration - pressDuration,\n        normalizeDelay(this._config.delayPressOut),\n      );\n      if (delayPressOut > 0) {\n        event.persist();\n        this._pressOutDelayTimeout = setTimeout(() => {\n          onPressOut(event);\n        }, delayPressOut);\n      } else {\n        onPressOut(event);\n      }\n    }\n    this._touchActivateTime = null;\n  }\n\n  _measureResponderRegion(): void {\n    if (this._responderID == null) {\n      return;\n    }\n\n    if (typeof this._responderID === 'number') {\n      UIManager.measure(this._responderID, this._measureCallback);\n    } else {\n      this._responderID.measure(this._measureCallback);\n    }\n  }\n\n  _measureCallback = (\n    left: number,\n    top: number,\n    width: number,\n    height: number,\n    pageX: number,\n    pageY: number,\n  ) => {\n    if (!left && !top && !width && !height && !pageX && !pageY) {\n      return;\n    }\n    this._responderRegion = {\n      bottom: pageY + height,\n      left: pageX,\n      right: pageX + width,\n      top: pageY,\n    };\n  };\n\n  _isTouchWithinResponderRegion(\n    touch: $PropertyType<GestureResponderEvent, 'nativeEvent'>,\n    responderRegion: $ReadOnly<{\n      bottom: number,\n      left: number,\n      right: number,\n      top: number,\n    }>,\n  ): boolean {\n    const hitSlop = normalizeRect(this._config.hitSlop);\n    const pressRectOffset = normalizeRect(this._config.pressRectOffset);\n\n    let regionBottom = responderRegion.bottom;\n    let regionLeft = responderRegion.left;\n    let regionRight = responderRegion.right;\n    let regionTop = responderRegion.top;\n\n    if (hitSlop != null) {\n      if (hitSlop.bottom != null) {\n        regionBottom += hitSlop.bottom;\n      }\n      if (hitSlop.left != null) {\n        regionLeft -= hitSlop.left;\n      }\n      if (hitSlop.right != null) {\n        regionRight += hitSlop.right;\n      }\n      if (hitSlop.top != null) {\n        regionTop -= hitSlop.top;\n      }\n    }\n\n    regionBottom +=\n      pressRectOffset?.bottom ?? DEFAULT_PRESS_RECT_OFFSETS.bottom;\n    regionLeft -= pressRectOffset?.left ?? DEFAULT_PRESS_RECT_OFFSETS.left;\n    regionRight += pressRectOffset?.right ?? DEFAULT_PRESS_RECT_OFFSETS.right;\n    regionTop -= pressRectOffset?.top ?? DEFAULT_PRESS_RECT_OFFSETS.top;\n\n    return (\n      touch.pageX > regionLeft &&\n      touch.pageX < regionRight &&\n      touch.pageY > regionTop &&\n      touch.pageY < regionBottom\n    );\n  }\n\n  _handleLongPress(event: GestureResponderEvent): void {\n    if (\n      this._touchState === 'RESPONDER_ACTIVE_PRESS_IN' ||\n      this._touchState === 'RESPONDER_ACTIVE_LONG_PRESS_IN'\n    ) {\n      this._receiveSignal('LONG_PRESS_DETECTED', event);\n    }\n  }\n\n  _cancelHoverInDelayTimeout(): void {\n    if (this._hoverInDelayTimeout != null) {\n      clearTimeout(this._hoverInDelayTimeout);\n      this._hoverInDelayTimeout = null;\n    }\n  }\n\n  _cancelHoverOutDelayTimeout(): void {\n    if (this._hoverOutDelayTimeout != null) {\n      clearTimeout(this._hoverOutDelayTimeout);\n      this._hoverOutDelayTimeout = null;\n    }\n  }\n\n  _cancelLongPressDelayTimeout(): void {\n    if (this._longPressDelayTimeout != null) {\n      clearTimeout(this._longPressDelayTimeout);\n      this._longPressDelayTimeout = null;\n    }\n  }\n\n  _cancelPressDelayTimeout(): void {\n    if (this._pressDelayTimeout != null) {\n      clearTimeout(this._pressDelayTimeout);\n      this._pressDelayTimeout = null;\n    }\n  }\n\n  _cancelPressOutDelayTimeout(): void {\n    if (this._pressOutDelayTimeout != null) {\n      clearTimeout(this._pressOutDelayTimeout);\n      this._pressOutDelayTimeout = null;\n    }\n  }\n}\n\nfunction normalizeDelay(\n  delay: ?number,\n  min: number = 0,\n  fallback: number = 0,\n): number {\n  return Math.max(min, delay ?? fallback);\n}\n\nconst getTouchFromPressEvent = (event: GestureResponderEvent) => {\n  const {changedTouches, touches} = event.nativeEvent;\n\n  if (touches != null && touches.length > 0) {\n    return touches[0];\n  }\n  if (changedTouches != null && changedTouches.length > 0) {\n    return changedTouches[0];\n  }\n  return event.nativeEvent;\n};\n\nfunction convertPointerEventToMouseEvent(input: PointerEvent): MouseEvent {\n  const {clientX, clientY} = input.nativeEvent;\n  return {\n    ...input,\n    nativeEvent: {\n      clientX,\n      clientY,\n      pageX: clientX,\n      pageY: clientY,\n      timestamp: input.timeStamp,\n    },\n  };\n}\n"], "mappings": ";;;;;;;AAkBA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,wBAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,UAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AAEA,IAAAI,SAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,WAAA,GAAAL,OAAA;AACA,IAAAM,oCAAA,GAAAP,sBAAA,CAAAC,OAAA;AAEA,IAAAO,UAAA,GAAAR,sBAAA,CAAAC,OAAA;AA0IA,IAAMQ,WAAW,GAAGC,MAAM,CAACC,MAAM,CAAC;EAChCC,aAAa,EAAE;IACbC,KAAK,EAAE,OAAO;IACdC,eAAe,EAAE,6BAA6B;IAC9CC,iBAAiB,EAAE,OAAO;IAC1BC,oBAAoB,EAAE,OAAO;IAC7BC,gBAAgB,EAAE,OAAO;IACzBC,gBAAgB,EAAE,OAAO;IACzBC,mBAAmB,EAAE;EACvB,CAAC;EACDC,2BAA2B,EAAE;IAC3BP,KAAK,EAAE,2BAA2B;IAClCC,eAAe,EAAE,OAAO;IACxBC,iBAAiB,EAAE,eAAe;IAClCC,oBAAoB,EAAE,eAAe;IACrCC,gBAAgB,EAAE,6BAA6B;IAC/CC,gBAAgB,EAAE,8BAA8B;IAChDC,mBAAmB,EAAE;EACvB,CAAC;EACDE,4BAA4B,EAAE;IAC5BR,KAAK,EAAE,4BAA4B;IACnCC,eAAe,EAAE,OAAO;IACxBC,iBAAiB,EAAE,eAAe;IAClCC,oBAAoB,EAAE,eAAe;IACrCC,gBAAgB,EAAE,6BAA6B;IAC/CC,gBAAgB,EAAE,8BAA8B;IAChDC,mBAAmB,EAAE;EACvB,CAAC;EACDG,yBAAyB,EAAE;IACzBT,KAAK,EAAE,OAAO;IACdC,eAAe,EAAE,OAAO;IACxBC,iBAAiB,EAAE,eAAe;IAClCC,oBAAoB,EAAE,eAAe;IACrCC,gBAAgB,EAAE,2BAA2B;IAC7CC,gBAAgB,EAAE,4BAA4B;IAC9CC,mBAAmB,EAAE;EACvB,CAAC;EACDI,0BAA0B,EAAE;IAC1BV,KAAK,EAAE,OAAO;IACdC,eAAe,EAAE,OAAO;IACxBC,iBAAiB,EAAE,eAAe;IAClCC,oBAAoB,EAAE,eAAe;IACrCC,gBAAgB,EAAE,2BAA2B;IAC7CC,gBAAgB,EAAE,4BAA4B;IAC9CC,mBAAmB,EAAE;EACvB,CAAC;EACDK,8BAA8B,EAAE;IAC9BX,KAAK,EAAE,OAAO;IACdC,eAAe,EAAE,OAAO;IACxBC,iBAAiB,EAAE,eAAe;IAClCC,oBAAoB,EAAE,eAAe;IACrCC,gBAAgB,EAAE,gCAAgC;IAClDC,gBAAgB,EAAE,iCAAiC;IACnDC,mBAAmB,EAAE;EACvB,CAAC;EACDM,+BAA+B,EAAE;IAC/BZ,KAAK,EAAE,OAAO;IACdC,eAAe,EAAE,OAAO;IACxBC,iBAAiB,EAAE,eAAe;IAClCC,oBAAoB,EAAE,eAAe;IACrCC,gBAAgB,EAAE,gCAAgC;IAClDC,gBAAgB,EAAE,iCAAiC;IACnDC,mBAAmB,EAAE;EACvB,CAAC;EACDO,KAAK,EAAE;IACLb,KAAK,EAAE,eAAe;IACtBC,eAAe,EAAE,6BAA6B;IAC9CC,iBAAiB,EAAE,eAAe;IAClCC,oBAAoB,EAAE,eAAe;IACrCC,gBAAgB,EAAE,eAAe;IACjCC,gBAAgB,EAAE,eAAe;IACjCC,mBAAmB,EAAE;EACvB;AACF,CAAC,CAAC;AAEF,IAAMQ,cAAc,GAAG,SAAjBA,cAAcA,CAAIC,MAAkB;EAAA,OACxCA,MAAM,KAAK,2BAA2B,IACtCA,MAAM,KAAK,gCAAgC;AAAA;AAE7C,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAID,MAAkB;EAAA,OAC5CA,MAAM,KAAK,4BAA4B,IACvCA,MAAM,KAAK,2BAA2B;AAAA;AAExC,IAAME,eAAe,GAAG,SAAlBA,eAAeA,CAAIF,MAAkB;EAAA,OACzCA,MAAM,KAAK,6BAA6B,IACxCA,MAAM,KAAK,2BAA2B,IACtCA,MAAM,KAAK,gCAAgC;AAAA;AAE7C,IAAMG,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIH,MAAmB;EAAA,OAC3CA,MAAM,KAAK,sBAAsB,IAAIA,MAAM,KAAK,mBAAmB;AAAA;AAErE,IAAMI,2BAA2B,GAAG,GAAG;AACvC,IAAMC,0BAA0B,GAAG;EACjCC,MAAM,EAAE,EAAE;EACVC,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,EAAE;EACTC,GAAG,EAAE;AACP,CAAC;AACD,IAAMC,0BAA0B,GAAG,GAAG;AAEtC,IAAMC,wCAAwC,GAAG,EAAE;AACnD,IAAIC,6BAA6B,GAAGD,wCAAwC;AAAC,IAwGxDE,YAAY,GAAAC,OAAA,CAAAC,OAAA;EAuB/B,SAAAF,aAAYG,MAA0B,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAAH,OAAA,QAAAF,YAAA;IAAA,KArBxCM,cAAc,GAAmB,IAAI;IAAA,KACrCC,oBAAoB,GAAe,IAAI;IAAA,KACvCC,qBAAqB,GAAe,IAAI;IAAA,KACxCC,UAAU,GAAY,KAAK;IAAA,KAC3BC,sBAAsB,GAAe,IAAI;IAAA,KACzCC,kBAAkB,GAAe,IAAI;IAAA,KACrCC,qBAAqB,GAAe,IAAI;IAAA,KACxCC,YAAY,GAA2B,IAAI;IAAA,KAC3CC,gBAAgB,GAKX,IAAI;IAAA,KAMTC,WAAW,GAAe,eAAe;IAAA,KAoazCC,gBAAgB,GAAG,UACjBtB,IAAY,EACZE,GAAW,EACXqB,KAAa,EACbC,MAAc,EACdC,KAAa,EACbC,KAAa,EACV;MACH,IAAI,CAAC1B,IAAI,IAAI,CAACE,GAAG,IAAI,CAACqB,KAAK,IAAI,CAACC,MAAM,IAAI,CAACC,KAAK,IAAI,CAACC,KAAK,EAAE;QAC1D;MACF;MACAhB,KAAI,CAACU,gBAAgB,GAAG;QACtBrB,MAAM,EAAE2B,KAAK,GAAGF,MAAM;QACtBxB,IAAI,EAAEyB,KAAK;QACXxB,KAAK,EAAEwB,KAAK,GAAGF,KAAK;QACpBrB,GAAG,EAAEwB;MACP,CAAC;IACH,CAAC;IAlbC,IAAI,CAACC,SAAS,CAAClB,MAAM,CAAC;EACxB;EAAC,WAAAmB,aAAA,CAAApB,OAAA,EAAAF,YAAA;IAAAuB,GAAA;IAAAC,KAAA,EAED,SAAAH,SAASA,CAAClB,MAA0B,EAAQ;MAC1C,IAAI,CAACsB,OAAO,GAAGtB,MAAM;IACvB;EAAC;IAAAoB,GAAA;IAAAC,KAAA,EAKD,SAAAE,KAAKA,CAAA,EAAS;MACZ,IAAI,CAACC,0BAA0B,CAAC,CAAC;MACjC,IAAI,CAACC,2BAA2B,CAAC,CAAC;MAClC,IAAI,CAACC,4BAA4B,CAAC,CAAC;MACnC,IAAI,CAACC,wBAAwB,CAAC,CAAC;MAC/B,IAAI,CAACC,2BAA2B,CAAC,CAAC;MAIlC,IAAI,CAACN,OAAO,GAAGxD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC;IAClC;EAAC;IAAAqD,GAAA;IAAAC,KAAA,EAKD,SAAAQ,gBAAgBA,CAAA,EAAkB;MAChC,IAAI,IAAI,CAAC1B,cAAc,IAAI,IAAI,EAAE;QAC/B,IAAI,CAACA,cAAc,GAAG,IAAI,CAAC2B,oBAAoB,CAAC,CAAC;MACnD;MACA,OAAO,IAAI,CAAC3B,cAAc;IAC5B;EAAC;IAAAiB,GAAA;IAAAC,KAAA,EAMD,SAAAS,oBAAoBA,CAAA,EAAkB;MAAA,IAAAC,MAAA;MACpC,IAAMC,kBAAkB,GAAG;QACzBC,MAAM,EAAE,SAARA,MAAMA,CAAGC,KAAgB,EAAW;UAClC,IAAOD,MAAM,GAAIF,MAAI,CAACT,OAAO,CAAtBW,MAAM;UACb,IAAIA,MAAM,IAAI,IAAI,EAAE;YAClBA,MAAM,CAACC,KAAK,CAAC;UACf;QACF,CAAC;QACDC,OAAO,EAAE,SAATA,OAAOA,CAAGD,KAAiB,EAAW;UACpC,IAAOC,OAAO,GAAIJ,MAAI,CAACT,OAAO,CAAvBa,OAAO;UACd,IAAIA,OAAO,IAAI,IAAI,EAAE;YACnBA,OAAO,CAACD,KAAK,CAAC;UAChB;QACF;MACF,CAAC;MAED,IAAME,sBAAsB,GAAG;QAC7BC,yBAAyB,EAAE,SAA3BA,yBAAyBA,CAAA,EAAiB;UAAA,IAAAC,SAAA;UACxC,IAAOC,QAAQ,GAAIR,MAAI,CAACT,OAAO,CAAxBiB,QAAQ;UACf,QAAAD,SAAA,GAAO,CAACC,QAAQ,YAAAD,SAAA,GAAI,IAAI;QAC1B,CAAC;QAEDE,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAGN,KAA4B,EAAqB;UAClEA,KAAK,CAACO,OAAO,CAAC,CAAC;UAEfV,MAAI,CAACH,2BAA2B,CAAC,CAAC;UAElCG,MAAI,CAACrB,YAAY,GAAGwB,KAAK,CAACQ,aAAa;UACvCX,MAAI,CAACnB,WAAW,GAAG,eAAe;UAClCmB,MAAI,CAACY,cAAc,CAAC,iBAAiB,EAAET,KAAK,CAAC;UAE7C,IAAMU,YAAY,GAAGC,cAAc,CAACd,MAAI,CAACT,OAAO,CAACsB,YAAY,CAAC;UAC9D,IAAIA,YAAY,GAAG,CAAC,EAAE;YACpBb,MAAI,CAACvB,kBAAkB,GAAGsC,UAAU,CAAC,YAAM;cACzCf,MAAI,CAACY,cAAc,CAAC,OAAO,EAAET,KAAK,CAAC;YACrC,CAAC,EAAEU,YAAY,CAAC;UAClB,CAAC,MAAM;YACLb,MAAI,CAACY,cAAc,CAAC,OAAO,EAAET,KAAK,CAAC;UACrC;UAEA,IAAMa,cAAc,GAAGF,cAAc,CACnCd,MAAI,CAACT,OAAO,CAACyB,cAAc,EAC3B,EAAE,EACF3D,2BAA2B,GAAGwD,YAChC,CAAC;UACDb,MAAI,CAACxB,sBAAsB,GAAGuC,UAAU,CAAC,YAAM;YAC7Cf,MAAI,CAACiB,gBAAgB,CAACd,KAAK,CAAC;UAC9B,CAAC,EAAEa,cAAc,GAAGH,YAAY,CAAC;UAEjC,OAAOb,MAAI,CAACT,OAAO,CAAC2B,oBAAoB,KAAK,IAAI;QACnD,CAAC;QAEDC,eAAe,EAAE,SAAjBA,eAAeA,CAAGhB,KAA4B,EAAW;UACvD,IAAOiB,WAAW,GAAIpB,MAAI,CAACT,OAAO,CAA3B6B,WAAW;UAClB,IAAIA,WAAW,IAAI,IAAI,EAAE;YACvBA,WAAW,CAACjB,KAAK,CAAC;UACpB;UAGA,IAAMkB,eAAe,GAAGrB,MAAI,CAACpB,gBAAgB;UAC7C,IAAIyC,eAAe,IAAI,IAAI,EAAE;YAC3B;UACF;UAEA,IAAMC,KAAK,GAAGC,sBAAsB,CAACpB,KAAK,CAAC;UAC3C,IAAImB,KAAK,IAAI,IAAI,EAAE;YACjBtB,MAAI,CAACL,4BAA4B,CAAC,CAAC;YACnCK,MAAI,CAACY,cAAc,CAAC,kBAAkB,EAAET,KAAK,CAAC;YAC9C;UACF;UAEA,IAAIH,MAAI,CAACwB,sBAAsB,IAAI,IAAI,EAAE;YACvC,IAAMC,MAAM,GAAGzB,MAAI,CAACwB,sBAAsB,CAACvC,KAAK,GAAGqC,KAAK,CAACrC,KAAK;YAC9D,IAAMyC,MAAM,GAAG1B,MAAI,CAACwB,sBAAsB,CAACtC,KAAK,GAAGoC,KAAK,CAACpC,KAAK;YAC9D,IAAIyC,IAAI,CAACC,KAAK,CAACH,MAAM,EAAEC,MAAM,CAAC,GAAG7D,6BAA6B,EAAE;cAC9DmC,MAAI,CAACL,4BAA4B,CAAC,CAAC;YACrC;UACF;UAEA,IAAIK,MAAI,CAAC6B,6BAA6B,CAACP,KAAK,EAAED,eAAe,CAAC,EAAE;YAC9DrB,MAAI,CAACY,cAAc,CAAC,kBAAkB,EAAET,KAAK,CAAC;UAChD,CAAC,MAAM;YACLH,MAAI,CAACL,4BAA4B,CAAC,CAAC;YACnCK,MAAI,CAACY,cAAc,CAAC,kBAAkB,EAAET,KAAK,CAAC;UAChD;QACF,CAAC;QAED2B,kBAAkB,EAAE,SAApBA,kBAAkBA,CAAG3B,KAA4B,EAAW;UAC1DH,MAAI,CAACY,cAAc,CAAC,mBAAmB,EAAET,KAAK,CAAC;QACjD,CAAC;QAED4B,oBAAoB,EAAE,SAAtBA,oBAAoBA,CAAG5B,KAA4B,EAAW;UAC5DH,MAAI,CAACY,cAAc,CAAC,sBAAsB,EAAET,KAAK,CAAC;QACpD,CAAC;QAED6B,6BAA6B,EAAE,SAA/BA,6BAA6BA,CAAA,EAAiB;UAC5C,IAAOC,UAAU,GAAIjC,MAAI,CAACT,OAAO,CAA1B0C,UAAU;UACjB,OAAOA,UAAU,WAAVA,UAAU,GAAI,IAAI;QAC3B,CAAC;QAEDC,OAAO,EAAE,SAATA,OAAOA,CAAG/B,KAA4B,EAAW;UAAA,IAAAgC,kBAAA;UAG/C,IAAIhC,KAAK,aAAAgC,kBAAA,GAALhC,KAAK,CAAEiC,WAAW,aAAlBD,kBAAA,CAAoBE,cAAc,YAAlCF,kBAAA,CAAoBE,cAAc,CAAG,aAAa,CAAC,EAAE;YACvD;UACF;UAIA,IAAI,CAAAlC,KAAK,oBAALA,KAAK,CAAEQ,aAAa,OAAKR,KAAK,oBAALA,KAAK,CAAEmC,MAAM,GAAE;YAC1CnC,KAAK,YAALA,KAAK,CAAEoC,eAAe,CAAC,CAAC;YACxB;UACF;UAEA,IAAAC,cAAA,GAA4BxC,MAAI,CAACT,OAAO;YAAjCkD,OAAO,GAAAD,cAAA,CAAPC,OAAO;YAAEjC,QAAQ,GAAAgC,cAAA,CAARhC,QAAQ;UACxB,IAAIiC,OAAO,IAAI,IAAI,IAAIjC,QAAQ,KAAK,IAAI,EAAE;YACxCiC,OAAO,CAACtC,KAAK,CAAC;UAChB;QACF;MACF,CAAC;MAED,IAAIuC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,EAAE;QAGnCvC,sBAAsB,CAACC,yBAAyB,CAACuC,2BAA2B,GAC1E;UAAA,OAAM7C,MAAI,CAACT,OAAO;QAAA;MACtB;MAEA,IACEuD,gCAAuB,CAACC,6CAA6C,CAAC,CAAC,EACvE;QACA,IAAMC,kBAAkB,GAAG;UACzBC,cAAc,EAAGC,SAAyC;UAC1DC,cAAc,EAAGD;QACnB,CAAC;QACD,IAAAE,aAAA,GAAgC,IAAI,CAAC7D,OAAO;UAArC8D,SAAS,GAAAD,aAAA,CAATC,SAAS;UAAEC,UAAU,GAAAF,aAAA,CAAVE,UAAU;QAC5B,IAAID,SAAS,IAAI,IAAI,EAAE;UACrBL,kBAAkB,CAACC,cAAc,GAAG,UAAC9C,KAAmB,EAAK;YAC3DH,MAAI,CAACzB,UAAU,GAAG,IAAI;YACtByB,MAAI,CAACN,2BAA2B,CAAC,CAAC;YAClC,IAAI2D,SAAS,IAAI,IAAI,EAAE;cACrB,IAAME,YAAY,GAAGzC,cAAc,CAACd,MAAI,CAACT,OAAO,CAACgE,YAAY,CAAC;cAC9D,IAAIA,YAAY,GAAG,CAAC,EAAE;gBACpBpD,KAAK,CAACO,OAAO,CAAC,CAAC;gBACfV,MAAI,CAAC3B,oBAAoB,GAAG0C,UAAU,CAAC,YAAM;kBAC3CsC,SAAS,CAACG,+BAA+B,CAACrD,KAAK,CAAC,CAAC;gBACnD,CAAC,EAAEoD,YAAY,CAAC;cAClB,CAAC,MAAM;gBACLF,SAAS,CAACG,+BAA+B,CAACrD,KAAK,CAAC,CAAC;cACnD;YACF;UACF,CAAC;QACH;QACA,IAAImD,UAAU,IAAI,IAAI,EAAE;UACtBN,kBAAkB,CAACG,cAAc,GAAG,UAAChD,KAAmB,EAAK;YAC3D,IAAIH,MAAI,CAACzB,UAAU,EAAE;cACnByB,MAAI,CAACzB,UAAU,GAAG,KAAK;cACvByB,MAAI,CAACP,0BAA0B,CAAC,CAAC;cACjC,IAAI6D,UAAU,IAAI,IAAI,EAAE;gBACtB,IAAMG,aAAa,GAAG3C,cAAc,CAACd,MAAI,CAACT,OAAO,CAACkE,aAAa,CAAC;gBAChE,IAAIA,aAAa,GAAG,CAAC,EAAE;kBACrBtD,KAAK,CAACO,OAAO,CAAC,CAAC;kBACfV,MAAI,CAAC1B,qBAAqB,GAAGyC,UAAU,CAAC,YAAM;oBAC5CuC,UAAU,CAACE,+BAA+B,CAACrD,KAAK,CAAC,CAAC;kBACpD,CAAC,EAAEsD,aAAa,CAAC;gBACnB,CAAC,MAAM;kBACLH,UAAU,CAACE,+BAA+B,CAACrD,KAAK,CAAC,CAAC;gBACpD;cACF;YACF;UACF,CAAC;QACH;QACA,OAAApE,MAAA,CAAA2H,MAAA,KACKzD,kBAAkB,EAClBI,sBAAsB,EACtB2C,kBAAkB;MAEzB,CAAC,MAAM;QACL,IAAMW,kBAAkB,GACtBC,iBAAQ,CAACC,EAAE,KAAK,KAAK,IAAID,iBAAQ,CAACC,EAAE,KAAK,SAAS,GAC9C,IAAI,GACJ;UACEC,YAAY,EAAE,SAAdA,YAAYA,CAAG3D,KAAiB,EAAW;YACzC,IAAI,IAAA4D,0BAAc,EAAC,CAAC,EAAE;cACpB/D,MAAI,CAACzB,UAAU,GAAG,IAAI;cACtByB,MAAI,CAACN,2BAA2B,CAAC,CAAC;cAClC,IAAO2D,UAAS,GAAIrD,MAAI,CAACT,OAAO,CAAzB8D,SAAS;cAChB,IAAIA,UAAS,IAAI,IAAI,EAAE;gBACrB,IAAME,YAAY,GAAGzC,cAAc,CACjCd,MAAI,CAACT,OAAO,CAACgE,YACf,CAAC;gBACD,IAAIA,YAAY,GAAG,CAAC,EAAE;kBACpBpD,KAAK,CAACO,OAAO,CAAC,CAAC;kBACfV,MAAI,CAAC3B,oBAAoB,GAAG0C,UAAU,CAAC,YAAM;oBAC3CsC,UAAS,CAAClD,KAAK,CAAC;kBAClB,CAAC,EAAEoD,YAAY,CAAC;gBAClB,CAAC,MAAM;kBACLF,UAAS,CAAClD,KAAK,CAAC;gBAClB;cACF;YACF;UACF,CAAC;UAED6D,YAAY,EAAE,SAAdA,YAAYA,CAAG7D,KAAiB,EAAW;YACzC,IAAIH,MAAI,CAACzB,UAAU,EAAE;cACnByB,MAAI,CAACzB,UAAU,GAAG,KAAK;cACvByB,MAAI,CAACP,0BAA0B,CAAC,CAAC;cACjC,IAAO6D,WAAU,GAAItD,MAAI,CAACT,OAAO,CAA1B+D,UAAU;cACjB,IAAIA,WAAU,IAAI,IAAI,EAAE;gBACtB,IAAMG,aAAa,GAAG3C,cAAc,CAClCd,MAAI,CAACT,OAAO,CAACkE,aACf,CAAC;gBACD,IAAIA,aAAa,GAAG,CAAC,EAAE;kBACrBtD,KAAK,CAACO,OAAO,CAAC,CAAC;kBACfV,MAAI,CAAC3B,oBAAoB,GAAG0C,UAAU,CAAC,YAAM;oBAC3CuC,WAAU,CAACnD,KAAK,CAAC;kBACnB,CAAC,EAAEsD,aAAa,CAAC;gBACnB,CAAC,MAAM;kBACLH,WAAU,CAACnD,KAAK,CAAC;gBACnB;cACF;YACF;UACF;QACF,CAAC;QACP,OAAApE,MAAA,CAAA2H,MAAA,KACKzD,kBAAkB,EAClBI,sBAAsB,EACtBsD,kBAAkB;MAEzB;IACF;EAAC;IAAAtE,GAAA;IAAAC,KAAA,EAMD,SAAAsB,cAAcA,CAAC3D,MAAmB,EAAEkD,KAA4B,EAAQ;MAAA,IAAA8D,qBAAA;MAKtE,IAAI9D,KAAK,CAACiC,WAAW,CAAC8B,SAAS,IAAI,IAAI,EAAE;QACvCC,4CAAmC,CAACC,SAAS,CAAC,YAAM;UAClD,OAAO;YACLnH,MAAM,EAANA,MAAM;YACNoH,eAAe,EAAElE,KAAK,CAACiC,WAAW,CAAC8B;UACrC,CAAC;QACH,CAAC,CAAC;MACJ;MAEA,IAAMI,SAAS,GAAG,IAAI,CAACzF,WAAW;MAClC,IAAM0F,SAAS,IAAAN,qBAAA,GAAGnI,WAAW,CAACwI,SAAS,CAAC,qBAAtBL,qBAAA,CAAyBhH,MAAM,CAAC;MAClD,IAAI,IAAI,CAAC0B,YAAY,IAAI,IAAI,IAAI1B,MAAM,KAAK,mBAAmB,EAAE;QAC/D;MACF;MACA,IAAAuH,kBAAS,EACPD,SAAS,IAAI,IAAI,IAAIA,SAAS,KAAK,OAAO,EAC1C,mEAAmE,EACnEtH,MAAM,EACNqH,SAAS,EACT,OAAO,IAAI,CAAC3F,YAAY,KAAK,QAAQ,GACjC,IAAI,CAACA,YAAY,GACjB,oBACN,CAAC;MACD,IAAI2F,SAAS,KAAKC,SAAS,EAAE;QAC3B,IAAI,CAACE,6BAA6B,CAACH,SAAS,EAAEC,SAAS,EAAEtH,MAAM,EAAEkD,KAAK,CAAC;QACvE,IAAI,CAACtB,WAAW,GAAG0F,SAAS;MAC9B;IACF;EAAC;IAAAlF,GAAA;IAAAC,KAAA,EAMD,SAAAmF,6BAA6BA,CAC3BH,SAAqB,EACrBC,SAAqB,EACrBtH,MAAmB,EACnBkD,KAA4B,EACtB;MACN,IAAI/C,gBAAgB,CAACH,MAAM,CAAC,EAAE;QAC5B,IAAI,CAACuE,sBAAsB,GAAG,IAAI;QAClC,IAAI,CAAC7B,4BAA4B,CAAC,CAAC;MACrC;MAEA,IAAM+E,mBAAmB,GACvBJ,SAAS,KAAK,eAAe,IAC7BC,SAAS,KAAK,6BAA6B;MAE7C,IAAMI,sBAAsB,GAC1B,CAACzH,kBAAkB,CAACoH,SAAS,CAAC,IAAIpH,kBAAkB,CAACqH,SAAS,CAAC;MAEjE,IAAIG,mBAAmB,IAAIC,sBAAsB,EAAE;QACjD,IAAI,CAACC,uBAAuB,CAAC,CAAC;MAChC;MAEA,IAAIzH,eAAe,CAACmH,SAAS,CAAC,IAAIrH,MAAM,KAAK,qBAAqB,EAAE;QAClE,IAAO4H,WAAW,GAAI,IAAI,CAACtF,OAAO,CAA3BsF,WAAW;QAClB,IAAIA,WAAW,IAAI,IAAI,EAAE;UACvBA,WAAW,CAAC1E,KAAK,CAAC;QACpB;MACF;MAEA,IAAM2E,YAAY,GAAG9H,cAAc,CAACsH,SAAS,CAAC;MAC9C,IAAMS,YAAY,GAAG/H,cAAc,CAACuH,SAAS,CAAC;MAE9C,IAAI,CAACO,YAAY,IAAIC,YAAY,EAAE;QACjC,IAAI,CAACC,SAAS,CAAC7E,KAAK,CAAC;MACvB,CAAC,MAAM,IAAI2E,YAAY,IAAI,CAACC,YAAY,EAAE;QACxC,IAAI,CAACE,WAAW,CAAC9E,KAAK,CAAC;MACzB;MAEA,IAAIhD,eAAe,CAACmH,SAAS,CAAC,IAAIrH,MAAM,KAAK,mBAAmB,EAAE;QAEhE,IAAI,CAAC8H,YAAY,IAAI,CAACD,YAAY,EAAE;UAClC,IAAI,CAACE,SAAS,CAAC7E,KAAK,CAAC;UACrB,IAAI,CAAC8E,WAAW,CAAC9E,KAAK,CAAC;QACzB;QACA,IAAA+E,cAAA,GAAqD,IAAI,CAAC3F,OAAO;UAA1DsF,YAAW,GAAAK,cAAA,CAAXL,WAAW;UAAEpC,OAAO,GAAAyC,cAAA,CAAPzC,OAAO;UAAE0C,oBAAoB,GAAAD,cAAA,CAApBC,oBAAoB;QACjD,IAAI1C,OAAO,IAAI,IAAI,EAAE;UACnB,IAAM2C,0BAA0B,GAC9BP,YAAW,IAAI,IAAI,IAAIP,SAAS,KAAK,gCAAgC;UACvE,IAAI,CAACc,0BAA0B,EAAE;YAC/B,IAAIxB,iBAAQ,CAACC,EAAE,KAAK,SAAS,IAAIsB,oBAAoB,KAAK,IAAI,EAAE;cAC9DE,qBAAY,CAACC,cAAc,CAAC,CAAC;YAC/B;YACA7C,OAAO,CAACtC,KAAK,CAAC;UAChB;QACF;MACF;MAEA,IAAI,CAACP,wBAAwB,CAAC,CAAC;IACjC;EAAC;IAAAP,GAAA;IAAAC,KAAA,EAED,SAAA0F,SAASA,CAAC7E,KAA4B,EAAQ;MAC5C,IAAOoF,SAAS,GAAI,IAAI,CAAChG,OAAO,CAAzBgG,SAAS;MAChB,IAAAC,qBAAA,GAAuBjE,sBAAsB,CAACpB,KAAK,CAAC;QAA7ClB,KAAK,GAAAuG,qBAAA,CAALvG,KAAK;QAAEC,KAAK,GAAAsG,qBAAA,CAALtG,KAAK;MACnB,IAAI,CAACsC,sBAAsB,GAAG;QAACvC,KAAK,EAALA,KAAK;QAAEC,KAAK,EAALA;MAAK,CAAC;MAC5C,IAAI,CAACuG,kBAAkB,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;MACpC,IAAIJ,SAAS,IAAI,IAAI,EAAE;QACrBA,SAAS,CAACpF,KAAK,CAAC;MAClB;IACF;EAAC;IAAAd,GAAA;IAAAC,KAAA,EAED,SAAA2F,WAAWA,CAAC9E,KAA4B,EAAQ;MAC9C,IAAOyF,UAAU,GAAI,IAAI,CAACrG,OAAO,CAA1BqG,UAAU;MACjB,IAAIA,UAAU,IAAI,IAAI,EAAE;QAAA,IAAAC,qBAAA;QACtB,IAAMC,gBAAgB,GAAGhF,cAAc,CACrC,IAAI,CAACvB,OAAO,CAACuG,gBAAgB,EAC7B,CAAC,EACDnI,0BACF,CAAC;QACD,IAAMoI,aAAa,GAAGL,IAAI,CAACC,GAAG,CAAC,CAAC,KAAAE,qBAAA,GAAI,IAAI,CAACJ,kBAAkB,YAAAI,qBAAA,GAAI,CAAC,CAAC;QACjE,IAAMG,aAAa,GAAGrE,IAAI,CAACsE,GAAG,CAC5BH,gBAAgB,GAAGC,aAAa,EAChCjF,cAAc,CAAC,IAAI,CAACvB,OAAO,CAACyG,aAAa,CAC3C,CAAC;QACD,IAAIA,aAAa,GAAG,CAAC,EAAE;UACrB7F,KAAK,CAACO,OAAO,CAAC,CAAC;UACf,IAAI,CAAChC,qBAAqB,GAAGqC,UAAU,CAAC,YAAM;YAC5C6E,UAAU,CAACzF,KAAK,CAAC;UACnB,CAAC,EAAE6F,aAAa,CAAC;QACnB,CAAC,MAAM;UACLJ,UAAU,CAACzF,KAAK,CAAC;QACnB;MACF;MACA,IAAI,CAACsF,kBAAkB,GAAG,IAAI;IAChC;EAAC;IAAApG,GAAA;IAAAC,KAAA,EAED,SAAAsF,uBAAuBA,CAAA,EAAS;MAC9B,IAAI,IAAI,CAACjG,YAAY,IAAI,IAAI,EAAE;QAC7B;MACF;MAEA,IAAI,OAAO,IAAI,CAACA,YAAY,KAAK,QAAQ,EAAE;QACzCuH,kBAAS,CAACC,OAAO,CAAC,IAAI,CAACxH,YAAY,EAAE,IAAI,CAACG,gBAAgB,CAAC;MAC7D,CAAC,MAAM;QACL,IAAI,CAACH,YAAY,CAACwH,OAAO,CAAC,IAAI,CAACrH,gBAAgB,CAAC;MAClD;IACF;EAAC;IAAAO,GAAA;IAAAC,KAAA,EAqBD,SAAAuC,6BAA6BA,CAC3BP,KAA0D,EAC1DD,eAKE,EACO;MAAA,IAAA+E,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,oBAAA;MACT,IAAMC,OAAO,GAAG,IAAAC,mBAAa,EAAC,IAAI,CAAClH,OAAO,CAACiH,OAAO,CAAC;MACnD,IAAME,eAAe,GAAG,IAAAD,mBAAa,EAAC,IAAI,CAAClH,OAAO,CAACmH,eAAe,CAAC;MAEnE,IAAIC,YAAY,GAAGtF,eAAe,CAAC9D,MAAM;MACzC,IAAIqJ,UAAU,GAAGvF,eAAe,CAAC7D,IAAI;MACrC,IAAIqJ,WAAW,GAAGxF,eAAe,CAAC5D,KAAK;MACvC,IAAIqJ,SAAS,GAAGzF,eAAe,CAAC3D,GAAG;MAEnC,IAAI8I,OAAO,IAAI,IAAI,EAAE;QACnB,IAAIA,OAAO,CAACjJ,MAAM,IAAI,IAAI,EAAE;UAC1BoJ,YAAY,IAAIH,OAAO,CAACjJ,MAAM;QAChC;QACA,IAAIiJ,OAAO,CAAChJ,IAAI,IAAI,IAAI,EAAE;UACxBoJ,UAAU,IAAIJ,OAAO,CAAChJ,IAAI;QAC5B;QACA,IAAIgJ,OAAO,CAAC/I,KAAK,IAAI,IAAI,EAAE;UACzBoJ,WAAW,IAAIL,OAAO,CAAC/I,KAAK;QAC9B;QACA,IAAI+I,OAAO,CAAC9I,GAAG,IAAI,IAAI,EAAE;UACvBoJ,SAAS,IAAIN,OAAO,CAAC9I,GAAG;QAC1B;MACF;MAEAiJ,YAAY,KAAAP,qBAAA,GACVM,eAAe,oBAAfA,eAAe,CAAEnJ,MAAM,YAAA6I,qBAAA,GAAI9I,0BAA0B,CAACC,MAAM;MAC9DqJ,UAAU,KAAAP,qBAAA,GAAIK,eAAe,oBAAfA,eAAe,CAAElJ,IAAI,YAAA6I,qBAAA,GAAI/I,0BAA0B,CAACE,IAAI;MACtEqJ,WAAW,KAAAP,qBAAA,GAAII,eAAe,oBAAfA,eAAe,CAAEjJ,KAAK,YAAA6I,qBAAA,GAAIhJ,0BAA0B,CAACG,KAAK;MACzEqJ,SAAS,KAAAP,oBAAA,GAAIG,eAAe,oBAAfA,eAAe,CAAEhJ,GAAG,YAAA6I,oBAAA,GAAIjJ,0BAA0B,CAACI,GAAG;MAEnE,OACE4D,KAAK,CAACrC,KAAK,GAAG2H,UAAU,IACxBtF,KAAK,CAACrC,KAAK,GAAG4H,WAAW,IACzBvF,KAAK,CAACpC,KAAK,GAAG4H,SAAS,IACvBxF,KAAK,CAACpC,KAAK,GAAGyH,YAAY;IAE9B;EAAC;IAAAtH,GAAA;IAAAC,KAAA,EAED,SAAA2B,gBAAgBA,CAACd,KAA4B,EAAQ;MACnD,IACE,IAAI,CAACtB,WAAW,KAAK,2BAA2B,IAChD,IAAI,CAACA,WAAW,KAAK,gCAAgC,EACrD;QACA,IAAI,CAAC+B,cAAc,CAAC,qBAAqB,EAAET,KAAK,CAAC;MACnD;IACF;EAAC;IAAAd,GAAA;IAAAC,KAAA,EAED,SAAAG,0BAA0BA,CAAA,EAAS;MACjC,IAAI,IAAI,CAACpB,oBAAoB,IAAI,IAAI,EAAE;QACrC0I,YAAY,CAAC,IAAI,CAAC1I,oBAAoB,CAAC;QACvC,IAAI,CAACA,oBAAoB,GAAG,IAAI;MAClC;IACF;EAAC;IAAAgB,GAAA;IAAAC,KAAA,EAED,SAAAI,2BAA2BA,CAAA,EAAS;MAClC,IAAI,IAAI,CAACpB,qBAAqB,IAAI,IAAI,EAAE;QACtCyI,YAAY,CAAC,IAAI,CAACzI,qBAAqB,CAAC;QACxC,IAAI,CAACA,qBAAqB,GAAG,IAAI;MACnC;IACF;EAAC;IAAAe,GAAA;IAAAC,KAAA,EAED,SAAAK,4BAA4BA,CAAA,EAAS;MACnC,IAAI,IAAI,CAACnB,sBAAsB,IAAI,IAAI,EAAE;QACvCuI,YAAY,CAAC,IAAI,CAACvI,sBAAsB,CAAC;QACzC,IAAI,CAACA,sBAAsB,GAAG,IAAI;MACpC;IACF;EAAC;IAAAa,GAAA;IAAAC,KAAA,EAED,SAAAM,wBAAwBA,CAAA,EAAS;MAC/B,IAAI,IAAI,CAACnB,kBAAkB,IAAI,IAAI,EAAE;QACnCsI,YAAY,CAAC,IAAI,CAACtI,kBAAkB,CAAC;QACrC,IAAI,CAACA,kBAAkB,GAAG,IAAI;MAChC;IACF;EAAC;IAAAY,GAAA;IAAAC,KAAA,EAED,SAAAO,2BAA2BA,CAAA,EAAS;MAClC,IAAI,IAAI,CAACnB,qBAAqB,IAAI,IAAI,EAAE;QACtCqI,YAAY,CAAC,IAAI,CAACrI,qBAAqB,CAAC;QACxC,IAAI,CAACA,qBAAqB,GAAG,IAAI;MACnC;IACF;EAAC;IAAAW,GAAA;IAAAC,KAAA,EA5eD,SAAO0H,gCAAgCA,CAACC,QAAgB,EAAQ;MAC9DpJ,6BAA6B,GAAGoJ,QAAQ;IAC1C;EAAC;AAAA;AA6eH,SAASnG,cAAcA,CACrBoG,KAAc,EAGN;EAAA,IAFRC,GAAW,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAlE,SAAA,GAAAkE,SAAA,MAAG,CAAC;EAAA,IACfE,QAAgB,GAAAF,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAlE,SAAA,GAAAkE,SAAA,MAAG,CAAC;EAEpB,OAAOzF,IAAI,CAACsE,GAAG,CAACkB,GAAG,EAAED,KAAK,WAALA,KAAK,GAAII,QAAQ,CAAC;AACzC;AAEA,IAAM/F,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAIpB,KAA4B,EAAK;EAC/D,IAAAoH,mBAAA,GAAkCpH,KAAK,CAACiC,WAAW;IAA5CoF,cAAc,GAAAD,mBAAA,CAAdC,cAAc;IAAEC,OAAO,GAAAF,mBAAA,CAAPE,OAAO;EAE9B,IAAIA,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACJ,MAAM,GAAG,CAAC,EAAE;IACzC,OAAOI,OAAO,CAAC,CAAC,CAAC;EACnB;EACA,IAAID,cAAc,IAAI,IAAI,IAAIA,cAAc,CAACH,MAAM,GAAG,CAAC,EAAE;IACvD,OAAOG,cAAc,CAAC,CAAC,CAAC;EAC1B;EACA,OAAOrH,KAAK,CAACiC,WAAW;AAC1B,CAAC;AAED,SAASoB,+BAA+BA,CAACkE,KAAmB,EAAc;EACxE,IAAAC,kBAAA,GAA2BD,KAAK,CAACtF,WAAW;IAArCwF,OAAO,GAAAD,kBAAA,CAAPC,OAAO;IAAEC,OAAO,GAAAF,kBAAA,CAAPE,OAAO;EACvB,OAAA9L,MAAA,CAAA2H,MAAA,KACKgE,KAAK;IACRtF,WAAW,EAAE;MACXwF,OAAO,EAAPA,OAAO;MACPC,OAAO,EAAPA,OAAO;MACP5I,KAAK,EAAE2I,OAAO;MACd1I,KAAK,EAAE2I,OAAO;MACd3D,SAAS,EAAEwD,KAAK,CAACI;IACnB;EAAC;AAEL", "ignoreList": []}