/**
 * Navigation Guards Service - Handle navigation permissions and validation
 *
 * Service Contract:
 * - Validates navigation permissions based on user role and state
 * - <PERSON><PERSON> authentication requirements for protected routes
 * - Implements navigation flow validation
 * - Provides error handling for invalid navigation attempts
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { useAuthStore } from '../store/authSlice';
import navigationAnalytics from './navigationAnalytics';

export interface NavigationGuardResult {
  allowed: boolean;
  redirectTo?: string;
  reason?: string;
  requiresAuth?: boolean;
  requiresRole?: 'customer' | 'provider';
}

export interface RouteConfig {
  name: string;
  requiresAuth: boolean;
  allowedRoles?: ('customer' | 'provider')[];
  requiresOnboarding?: boolean;
  requiresVerification?: boolean;
  customValidator?: (userState: any) => NavigationGuardResult;
}

class NavigationGuardsService {
  private routeConfigs: Map<string, RouteConfig> = new Map();

  constructor() {
    this.initializeRouteConfigs();
  }

  /**
   * Initialize route configurations
   */
  private initializeRouteConfigs(): void {
    // Public routes (no authentication required)
    this.addRouteConfig({
      name: 'Welcome',
      requiresAuth: false,
    });

    this.addRouteConfig({
      name: 'Login',
      requiresAuth: false,
    });

    this.addRouteConfig({
      name: 'Register',
      requiresAuth: false,
    });

    this.addRouteConfig({
      name: 'ForgotPassword',
      requiresAuth: false,
    });

    // Customer routes
    this.addRouteConfig({
      name: 'CustomerTabs',
      requiresAuth: true,
      allowedRoles: ['customer'],
    });

    this.addRouteConfig({
      name: 'Home',
      requiresAuth: true,
      allowedRoles: ['customer'],
    });

    this.addRouteConfig({
      name: 'Search',
      requiresAuth: true,
      allowedRoles: ['customer'],
    });

    this.addRouteConfig({
      name: 'Bookings',
      requiresAuth: true,
      allowedRoles: ['customer'],
    });

    this.addRouteConfig({
      name: 'Messages',
      requiresAuth: true,
      allowedRoles: ['customer'],
    });

    this.addRouteConfig({
      name: 'Profile',
      requiresAuth: true,
      allowedRoles: ['customer'],
    });

    this.addRouteConfig({
      name: 'ProviderDetails',
      requiresAuth: true,
      allowedRoles: ['customer'],
    });

    this.addRouteConfig({
      name: 'ServiceDetails',
      requiresAuth: true,
      allowedRoles: ['customer'],
    });

    this.addRouteConfig({
      name: 'BookingScreen',
      requiresAuth: true,
      allowedRoles: ['customer'],
    });

    this.addRouteConfig({
      name: 'Checkout',
      requiresAuth: true,
      allowedRoles: ['customer'],
      requiresVerification: true,
    });

    this.addRouteConfig({
      name: 'Payment',
      requiresAuth: true,
      allowedRoles: ['customer'],
      requiresVerification: true,
    });

    // Provider routes
    this.addRouteConfig({
      name: 'ProviderTabs',
      requiresAuth: true,
      allowedRoles: ['provider'],
    });

    this.addRouteConfig({
      name: 'ProviderDashboard',
      requiresAuth: true,
      allowedRoles: ['provider'],
    });

    this.addRouteConfig({
      name: 'ProviderBookings',
      requiresAuth: true,
      allowedRoles: ['provider'],
    });

    this.addRouteConfig({
      name: 'ProviderServices',
      requiresAuth: true,
      allowedRoles: ['provider'],
    });

    this.addRouteConfig({
      name: 'ProviderProfile',
      requiresAuth: true,
      allowedRoles: ['provider'],
    });

    // Shared authenticated routes
    this.addRouteConfig({
      name: 'Conversation',
      requiresAuth: true,
      allowedRoles: ['customer', 'provider'],
    });

    this.addRouteConfig({
      name: 'Notifications',
      requiresAuth: true,
      allowedRoles: ['customer', 'provider'],
    });

    this.addRouteConfig({
      name: 'AccountSettings',
      requiresAuth: true,
      allowedRoles: ['customer', 'provider'],
    });

    this.addRouteConfig({
      name: 'EditProfile',
      requiresAuth: true,
      allowedRoles: ['customer', 'provider'],
    });
  }

  /**
   * Add a route configuration
   */
  addRouteConfig(config: RouteConfig): void {
    this.routeConfigs.set(config.name, config);
  }

  /**
   * Check if navigation to a route is allowed
   */
  canNavigate(routeName: string, params?: any): NavigationGuardResult {
    const config = this.routeConfigs.get(routeName);
    
    if (!config) {
      // Route not configured - allow by default but log warning
      console.warn(`Navigation guard: Route '${routeName}' not configured`);
      return { allowed: true };
    }

    const authStore = useAuthStore.getState();
    const { isAuthenticated, userRole, user } = authStore;

    // Check authentication requirement
    if (config.requiresAuth && !isAuthenticated) {
      navigationAnalytics.trackNavigationError(
        'Authentication required',
        routeName,
        { requiresAuth: true }
      );
      
      return {
        allowed: false,
        redirectTo: 'Login',
        reason: 'Authentication required',
        requiresAuth: true,
      };
    }

    // Check role requirement
    if (config.allowedRoles && config.allowedRoles.length > 0) {
      if (!userRole || !config.allowedRoles.includes(userRole)) {
        navigationAnalytics.trackNavigationError(
          'Insufficient role permissions',
          routeName,
          { userRole, allowedRoles: config.allowedRoles }
        );

        return {
          allowed: false,
          redirectTo: this.getDefaultRouteForRole(userRole),
          reason: 'Insufficient role permissions',
          requiresRole: config.allowedRoles[0],
        };
      }
    }

    // Check onboarding requirement
    if (config.requiresOnboarding && user && !user.hasCompletedOnboarding) {
      return {
        allowed: false,
        redirectTo: userRole === 'customer' ? 'CustomerOnboarding' : 'ProviderOnboarding',
        reason: 'Onboarding required',
      };
    }

    // Check verification requirement
    if (config.requiresVerification && user && !user.isVerified) {
      return {
        allowed: false,
        redirectTo: 'VerificationRequired',
        reason: 'Account verification required',
      };
    }

    // Run custom validator if provided
    if (config.customValidator) {
      const customResult = config.customValidator({ user, userRole, isAuthenticated });
      if (!customResult.allowed) {
        navigationAnalytics.trackNavigationError(
          customResult.reason || 'Custom validation failed',
          routeName,
          { customValidator: true }
        );
        return customResult;
      }
    }

    // All checks passed
    return { allowed: true };
  }

  /**
   * Get default route for a user role
   */
  private getDefaultRouteForRole(userRole: string | null): string {
    switch (userRole) {
      case 'customer':
        return 'CustomerTabs';
      case 'provider':
        return 'ProviderTabs';
      default:
        return 'Login';
    }
  }

  /**
   * Validate navigation flow (e.g., booking flow)
   */
  validateNavigationFlow(
    currentRoute: string,
    targetRoute: string,
    flowContext?: any
  ): NavigationGuardResult {
    // Define valid navigation flows
    const validFlows: Record<string, string[]> = {
      // Booking flow
      'ProviderDetails': ['ServiceDetails', 'BookingScreen'],
      'ServiceDetails': ['BookingScreen', 'ProviderDetails'],
      'BookingScreen': ['Checkout', 'ServiceDetails'],
      'Checkout': ['Payment', 'BookingScreen'],
      'Payment': ['BookingConfirmation', 'Checkout'],
      
      // Profile flow
      'Profile': ['EditProfile', 'AccountSettings'],
      'EditProfile': ['Profile'],
      'AccountSettings': ['Profile'],
      
      // Messaging flow
      'Messages': ['Conversation'],
      'Conversation': ['Messages'],
    };

    const allowedTargets = validFlows[currentRoute];
    
    if (allowedTargets && !allowedTargets.includes(targetRoute)) {
      // Check if it's a valid back navigation
      const backNavigation = this.isValidBackNavigation(currentRoute, targetRoute);
      if (!backNavigation) {
        navigationAnalytics.trackNavigationError(
          'Invalid navigation flow',
          targetRoute,
          { currentRoute, validTargets: allowedTargets }
        );
        
        return {
          allowed: false,
          reason: 'Invalid navigation flow',
        };
      }
    }

    return { allowed: true };
  }

  /**
   * Check if navigation is a valid back navigation
   */
  private isValidBackNavigation(currentRoute: string, targetRoute: string): boolean {
    // Define valid back navigation patterns
    const backNavigationMap: Record<string, string[]> = {
      'ServiceDetails': ['ProviderDetails', 'Search', 'Home'],
      'BookingScreen': ['ServiceDetails', 'ProviderDetails'],
      'Checkout': ['BookingScreen'],
      'Payment': ['Checkout'],
      'BookingConfirmation': ['Home', 'Bookings'],
      'EditProfile': ['Profile'],
      'AccountSettings': ['Profile'],
      'Conversation': ['Messages'],
      'ProviderDetails': ['Search', 'Home'],
    };

    const validBackTargets = backNavigationMap[currentRoute];
    return validBackTargets ? validBackTargets.includes(targetRoute) : true;
  }

  /**
   * Handle navigation guard failure
   */
  handleNavigationGuardFailure(
    result: NavigationGuardResult,
    originalRoute: string,
    navigation: any
  ): void {
    if (result.redirectTo) {
      // Redirect to appropriate route
      navigation.reset({
        index: 0,
        routes: [{ name: result.redirectTo }],
      });
    } else {
      // Show error message or handle appropriately
      console.error('Navigation blocked:', result.reason);
    }

    // Track the navigation failure
    navigationAnalytics.trackNavigationError(
      result.reason || 'Navigation guard failure',
      originalRoute,
      {
        redirectTo: result.redirectTo,
        requiresAuth: result.requiresAuth,
        requiresRole: result.requiresRole,
      }
    );
  }

  /**
   * Get route configuration
   */
  getRouteConfig(routeName: string): RouteConfig | undefined {
    return this.routeConfigs.get(routeName);
  }

  /**
   * Check if route requires authentication
   */
  requiresAuth(routeName: string): boolean {
    const config = this.routeConfigs.get(routeName);
    return config?.requiresAuth || false;
  }

  /**
   * Check if route is allowed for user role
   */
  isAllowedForRole(routeName: string, userRole: string): boolean {
    const config = this.routeConfigs.get(routeName);
    if (!config || !config.allowedRoles) return true;
    return config.allowedRoles.includes(userRole as 'customer' | 'provider');
  }
}

// Export singleton instance
export const navigationGuards = new NavigationGuardsService();
export default navigationGuards;
