{"version": 3, "names": ["_Animation2", "_interopRequireDefault", "require", "_callSuper", "t", "o", "e", "_getPrototypeOf2", "default", "_possibleConstructorReturn2", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_superPropGet", "r", "p", "_get2", "DecayAnimation", "exports", "_Animation", "config", "_config$deceleration", "_this", "_classCallCheck2", "_deceleration", "deceleration", "_velocity", "velocity", "_platformConfig", "platformConfig", "_inherits2", "_createClass2", "key", "value", "__getNativeAnimationConfig", "type", "iterations", "__iterations", "debugID", "__getDebugID", "start", "fromValue", "onUpdate", "onEnd", "previousAnimation", "animatedValue", "_this2", "_lastValue", "_fromValue", "_onUpdate", "_startTime", "Date", "now", "useNativeDriver", "__startAnimationIfNative", "_animationFrame", "requestAnimationFrame", "Math", "exp", "abs", "__notifyAnimationEnd", "finished", "__active", "bind", "stop", "global", "cancelAnimationFrame", "Animation"], "sources": ["DecayAnimation.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\nimport type {PlatformConfig} from '../AnimatedPlatformConfig';\nimport type AnimatedValue from '../nodes/AnimatedValue';\nimport type {AnimationConfig, EndCallback} from './Animation';\n\nimport Animation from './Animation';\n\nexport type DecayAnimationConfig = $ReadOnly<{\n  ...AnimationConfig,\n  velocity:\n    | number\n    | $ReadOnly<{\n        x: number,\n        y: number,\n        ...\n      }>,\n  deceleration?: number,\n  ...\n}>;\n\nexport type DecayAnimationConfigSingle = $ReadOnly<{\n  ...AnimationConfig,\n  velocity: number,\n  deceleration?: number,\n  ...\n}>;\n\nexport default class DecayAnimation extends Animation {\n  _startTime: number;\n  _lastValue: number;\n  _fromValue: number;\n  _deceleration: number;\n  _velocity: number;\n  _onUpdate: (value: number) => void;\n  _animationFrame: ?AnimationFrameID;\n  _platformConfig: ?PlatformConfig;\n\n  constructor(config: DecayAnimationConfigSingle) {\n    super(config);\n\n    this._deceleration = config.deceleration ?? 0.998;\n    this._velocity = config.velocity;\n    this._platformConfig = config.platformConfig;\n  }\n\n  __getNativeAnimationConfig(): $ReadOnly<{\n    deceleration: number,\n    iterations: number,\n    platformConfig: ?PlatformConfig,\n    type: 'decay',\n    velocity: number,\n    ...\n  }> {\n    return {\n      type: 'decay',\n      deceleration: this._deceleration,\n      velocity: this._velocity,\n      iterations: this.__iterations,\n      platformConfig: this._platformConfig,\n      debugID: this.__getDebugID(),\n    };\n  }\n\n  start(\n    fromValue: number,\n    onUpdate: (value: number) => void,\n    onEnd: ?EndCallback,\n    previousAnimation: ?Animation,\n    animatedValue: AnimatedValue,\n  ): void {\n    super.start(fromValue, onUpdate, onEnd, previousAnimation, animatedValue);\n\n    this._lastValue = fromValue;\n    this._fromValue = fromValue;\n    this._onUpdate = onUpdate;\n    this._startTime = Date.now();\n\n    const useNativeDriver = this.__startAnimationIfNative(animatedValue);\n    if (!useNativeDriver) {\n      this._animationFrame = requestAnimationFrame(() => this.onUpdate());\n    }\n  }\n\n  onUpdate(): void {\n    const now = Date.now();\n\n    const value =\n      this._fromValue +\n      (this._velocity / (1 - this._deceleration)) *\n        (1 - Math.exp(-(1 - this._deceleration) * (now - this._startTime)));\n\n    this._onUpdate(value);\n\n    if (Math.abs(this._lastValue - value) < 0.1) {\n      this.__notifyAnimationEnd({finished: true});\n      return;\n    }\n\n    this._lastValue = value;\n    if (this.__active) {\n      // $FlowFixMe[method-unbinding] added when improving typing for this parameters\n      this._animationFrame = requestAnimationFrame(this.onUpdate.bind(this));\n    }\n  }\n\n  stop(): void {\n    super.stop();\n    if (this._animationFrame != null) {\n      global.cancelAnimationFrame(this._animationFrame);\n    }\n    this.__notifyAnimationEnd({finished: false});\n  }\n}\n"], "mappings": ";;;;;;;;;;;AAcA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAoC,SAAAC,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAE,gBAAA,CAAAC,OAAA,EAAAH,CAAA,OAAAI,2BAAA,CAAAD,OAAA,EAAAJ,CAAA,EAAAM,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAP,CAAA,EAAAC,CAAA,YAAAC,gBAAA,CAAAC,OAAA,EAAAJ,CAAA,EAAAS,WAAA,IAAAR,CAAA,CAAAS,KAAA,CAAAV,CAAA,EAAAE,CAAA;AAAA,SAAAI,0BAAA,cAAAN,CAAA,IAAAW,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAX,CAAA,aAAAM,yBAAA,YAAAA,0BAAA,aAAAN,CAAA;AAAA,SAAAe,cAAAf,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAc,CAAA,QAAAC,CAAA,OAAAC,KAAA,CAAAd,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAY,CAAA,GAAAhB,CAAA,CAAAY,SAAA,GAAAZ,CAAA,GAAAC,CAAA,EAAAC,CAAA,cAAAc,CAAA,yBAAAC,CAAA,aAAAjB,CAAA,WAAAiB,CAAA,CAAAP,KAAA,CAAAR,CAAA,EAAAF,CAAA,OAAAiB,CAAA;AAAA,IAsBfE,cAAc,GAAAC,OAAA,CAAAhB,OAAA,aAAAiB,UAAA;EAUjC,SAAAF,eAAYG,MAAkC,EAAE;IAAA,IAAAC,oBAAA;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAArB,OAAA,QAAAe,cAAA;IAC9CK,KAAA,GAAAzB,UAAA,OAAAoB,cAAA,GAAMG,MAAM;IAEZE,KAAA,CAAKE,aAAa,IAAAH,oBAAA,GAAGD,MAAM,CAACK,YAAY,YAAAJ,oBAAA,GAAI,KAAK;IACjDC,KAAA,CAAKI,SAAS,GAAGN,MAAM,CAACO,QAAQ;IAChCL,KAAA,CAAKM,eAAe,GAAGR,MAAM,CAACS,cAAc;IAAC,OAAAP,KAAA;EAC/C;EAAC,IAAAQ,UAAA,CAAA5B,OAAA,EAAAe,cAAA,EAAAE,UAAA;EAAA,WAAAY,aAAA,CAAA7B,OAAA,EAAAe,cAAA;IAAAe,GAAA;IAAAC,KAAA,EAED,SAAAC,0BAA0BA,CAAA,EAOvB;MACD,OAAO;QACLC,IAAI,EAAE,OAAO;QACbV,YAAY,EAAE,IAAI,CAACD,aAAa;QAChCG,QAAQ,EAAE,IAAI,CAACD,SAAS;QACxBU,UAAU,EAAE,IAAI,CAACC,YAAY;QAC7BR,cAAc,EAAE,IAAI,CAACD,eAAe;QACpCU,OAAO,EAAE,IAAI,CAACC,YAAY,CAAC;MAC7B,CAAC;IACH;EAAC;IAAAP,GAAA;IAAAC,KAAA,EAED,SAAAO,KAAKA,CACHC,SAAiB,EACjBC,QAAiC,EACjCC,KAAmB,EACnBC,iBAA6B,EAC7BC,aAA4B,EACtB;MAAA,IAAAC,MAAA;MACNjC,aAAA,CAAAI,cAAA,qBAAYwB,SAAS,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,iBAAiB,EAAEC,aAAa;MAExE,IAAI,CAACE,UAAU,GAAGN,SAAS;MAC3B,IAAI,CAACO,UAAU,GAAGP,SAAS;MAC3B,IAAI,CAACQ,SAAS,GAAGP,QAAQ;MACzB,IAAI,CAACQ,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;MAE5B,IAAMC,eAAe,GAAG,IAAI,CAACC,wBAAwB,CAACT,aAAa,CAAC;MACpE,IAAI,CAACQ,eAAe,EAAE;QACpB,IAAI,CAACE,eAAe,GAAGC,qBAAqB,CAAC;UAAA,OAAMV,MAAI,CAACJ,QAAQ,CAAC,CAAC;QAAA,EAAC;MACrE;IACF;EAAC;IAAAV,GAAA;IAAAC,KAAA,EAED,SAAAS,QAAQA,CAAA,EAAS;MACf,IAAMU,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;MAEtB,IAAMnB,KAAK,GACT,IAAI,CAACe,UAAU,GACd,IAAI,CAACtB,SAAS,IAAI,CAAC,GAAG,IAAI,CAACF,aAAa,CAAC,IACvC,CAAC,GAAGiC,IAAI,CAACC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAClC,aAAa,CAAC,IAAI4B,GAAG,GAAG,IAAI,CAACF,UAAU,CAAC,CAAC,CAAC;MAEvE,IAAI,CAACD,SAAS,CAAChB,KAAK,CAAC;MAErB,IAAIwB,IAAI,CAACE,GAAG,CAAC,IAAI,CAACZ,UAAU,GAAGd,KAAK,CAAC,GAAG,GAAG,EAAE;QAC3C,IAAI,CAAC2B,oBAAoB,CAAC;UAACC,QAAQ,EAAE;QAAI,CAAC,CAAC;QAC3C;MACF;MAEA,IAAI,CAACd,UAAU,GAAGd,KAAK;MACvB,IAAI,IAAI,CAAC6B,QAAQ,EAAE;QAEjB,IAAI,CAACP,eAAe,GAAGC,qBAAqB,CAAC,IAAI,CAACd,QAAQ,CAACqB,IAAI,CAAC,IAAI,CAAC,CAAC;MACxE;IACF;EAAC;IAAA/B,GAAA;IAAAC,KAAA,EAED,SAAA+B,IAAIA,CAAA,EAAS;MACXnD,aAAA,CAAAI,cAAA;MACA,IAAI,IAAI,CAACsC,eAAe,IAAI,IAAI,EAAE;QAChCU,MAAM,CAACC,oBAAoB,CAAC,IAAI,CAACX,eAAe,CAAC;MACnD;MACA,IAAI,CAACK,oBAAoB,CAAC;QAACC,QAAQ,EAAE;MAAK,CAAC,CAAC;IAC9C;EAAC;AAAA,EApFyCM,mBAAS", "ignoreList": []}