/**
 * Edit Profile Screen - Profile Management Interface
 *
 * Component Contract:
 * - Allows customers to edit their profile information
 * - Supports profile image upload and form validation
 * - Integrates with user management system
 * - Follows accessibility guidelines and testing standards
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity } from 'react-native';
import { Platform } from 'react-native';
import { StyleSheet, TextInput, Alert, KeyboardAvoidingView,  } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Button } from '../components/atoms/Button';
import { FormValidator, CommonValidations, ValidationResult } from '../utils/formValidation';
import { HeaderHelpButton } from '../components/help';
import { SafeAreaWrapper } from '../components/ui/SafeAreaWrapper';
import { useTheme } from '../contexts/ThemeContext';
import { useAuthStore } from '../store/authSlice';
import { useUserStore } from '../store/userSlice';
import type { CustomerStackParamList } from '../navigation/types';
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
} from '../utils/responsiveUtils';
import { profileService, ProfileUpdateRequest, ProfileDetailsUpdateRequest } from '../services/profileService';

type EditProfileScreenNavigationProp = StackNavigationProp<CustomerStackParamList>;

interface FormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  dateOfBirth: string;
  preferredLocation: string;
  // Extended profile fields
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  // Business fields for providers
  businessName: string;
  businessDescription: string;
  yearsOfExperience: string;
  website: string;
}

interface ValidationErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  dateOfBirth?: string;
}

export const EditProfileScreen: React.FC = () => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  const navigation = useNavigation<EditProfileScreenNavigationProp>();
  const { userRole } = useAuthStore();
  const { profile } = useUserStore();

  const [formData, setFormData] = useState<FormData>({
    firstName: profile?.firstName || '',
    lastName: profile?.lastName || '',
    email: profile?.email || '',
    phone: profile?.phone || '',
    dateOfBirth: profile?.dateOfBirth || '',
    preferredLocation: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'Canada',
    businessName: '',
    businessDescription: '',
    yearsOfExperience: '',
    website: '',
  });

  const [validationErrors, setValidationErrors] = useState<ValidationErrors>({});
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  // Load extended profile data on component mount
  useEffect(() => {
    loadExtendedProfileData();
  }, []);

  const loadExtendedProfileData = async () => {
    try {
      const profileDetails = await profileService.getProfileDetails();

      setFormData(prev => ({
        ...prev,
        address: profileDetails.address || '',
        city: profileDetails.city || '',
        state: profileDetails.state || '',
        zipCode: profileDetails.zip_code || '',
        country: profileDetails.country || 'Canada',
        businessName: profileDetails.business_name || '',
        businessDescription: profileDetails.business_description || '',
        yearsOfExperience: profileDetails.years_of_experience?.toString() || '',
        website: profileDetails.website || '',
      }));
    } catch (error) {
      console.error('Failed to load extended profile data:', error);
      // Don't show error to user as extended data is not critical
    }
  };

  // Enhanced form validation with centralized validator
  const formValidator = new FormValidator({
    firstName: CommonValidations.name,
    lastName: CommonValidations.name,
    email: CommonValidations.email,
    phone: {
      required: false, // Phone is optional
      phone: true,
    },
    dateOfBirth: {
      required: false,
      date: true,
      custom: (value: string) => {
        if (!value) return { isValid: true }; // Optional field
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (!dateRegex.test(value)) {
          return { isValid: false, error: 'Date must be in YYYY-MM-DD format' };
        }
        const date = new Date(value);
        const today = new Date();
        if (date > today) {
          return { isValid: false, error: 'Date of birth cannot be in the future' };
        }
        const minDate = new Date();
        minDate.setFullYear(today.getFullYear() - 120);
        if (date < minDate) {
          return { isValid: false, error: 'Please enter a valid date of birth' };
        }
        return { isValid: true };
      }
    },
    preferredLocation: {
      required: false,
      maxLength: 100,
    }
  });

  const validateForm = (): boolean => {
    const validationResult = formValidator.validateForm(formData);

    // Convert FormValidationErrors to ValidationErrors format
    const errors: ValidationErrors = {};
    Object.keys(validationResult.errors).forEach(key => {
      if (key in formData) {
        errors[key as keyof ValidationErrors] = validationResult.errors[key];
      }
    });

    setValidationErrors(errors);
    return validationResult.isValid;
  };

  // Enhanced field validation for real-time feedback
  const validateField = (fieldName: keyof FormData, value: string): string | undefined => {
    const result = formValidator.validateField(fieldName, value, formData);
    return result.isValid ? undefined : result.error;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      Alert.alert('Validation Error', 'Please fix the errors before saving.');
      return;
    }

    setSaving(true);
    try {
      // Update basic user information
      const basicUpdateData: ProfileUpdateRequest = {
        first_name: formData.firstName,
        last_name: formData.lastName,
        phone: formData.phone,
      };

      const updatedUser = await profileService.updateProfile(basicUpdateData);

      // Update extended profile information
      const detailsUpdateData: ProfileDetailsUpdateRequest = {
        address: formData.address,
        city: formData.city,
        state: formData.state,
        zip_code: formData.zipCode,
        country: formData.country,
        business_name: formData.businessName,
        business_description: formData.businessDescription,
        years_of_experience: formData.yearsOfExperience ? Number(formData.yearsOfExperience) : undefined,
        website: formData.website,
      };

      await profileService.updateProfileDetails(detailsUpdateData);

      Alert.alert(
        'Success',
        'Your profile has been updated successfully.',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      console.error('Failed to update profile:', error);
      Alert.alert('Error', 'Failed to update profile. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    const updatedFormData = { ...formData, [field]: value };
    setFormData(updatedFormData);

    // Real-time validation for better user experience
    const fieldError = validateField(field, value);
    setValidationErrors(prev => ({
      ...prev,
      [field]: fieldError
    }));
  };

  const renderInput = (
    field: keyof FormData,
    label: string,
    placeholder: string,
    keyboardType: 'default' | 'email-address' | 'phone-pad' = 'default',
    required: boolean = false
  ) => (
    <View style={styles.inputContainer}>
      <Text style={styles.inputLabel}>
        {label} {required && <Text style={styles.required}>*</Text>}
      </Text>
      <TextInput
        style={[
          styles.textInput,
          validationErrors[field as keyof ValidationErrors] && styles.inputError,
        ]}
        value={formData[field]}
        onChangeText={(value) => handleInputChange(field, value)}
        placeholder={placeholder}
        placeholderTextColor={colors.text.tertiary}
        keyboardType={keyboardType}
        autoCapitalize={keyboardType === 'email-address' ? 'none' : 'words'}
        testID={`input-${field}`}
      />
      {validationErrors[field as keyof ValidationErrors] && (
        <Text style={styles.errorText}>
          {validationErrors[field as keyof ValidationErrors]}
        </Text>
      )}
    </View>
  );

  return (
    <SafeAreaWrapper backgroundColor={colors.background.primary}>
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={styles.backButton}
            testID="back-button">
            <Ionicons name="arrow-back" size={24} color={colors.text.primary} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Edit Profile</Text>
          <HeaderHelpButton
            size="medium"
            testID="edit-profile-help-button"
          />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Profile Image Section */}
          <View style={styles.profileImageSection}>
            <View style={styles.avatarContainer}>
              <View style={styles.avatar}>
                <Text style={styles.avatarText}>
                  {formData.firstName?.charAt(0)?.toUpperCase() || 'U'}
                </Text>
              </View>
              <TouchableOpacity
                style={styles.editAvatarButton}
                onPress={() => Alert.alert('Coming Soon', 'Photo upload will be available soon.')}
                testID="edit-avatar-button">
                <Ionicons name="camera" size={16} color="#FFFFFF" />
              </TouchableOpacity>
            </View>
            <Text style={styles.changePhotoText}>Tap to change photo</Text>
          </View>

          {/* Form Fields */}
          <View style={styles.formSection}>
            {renderInput('firstName', 'First Name', 'Enter your first name', 'default', true)}
            {renderInput('lastName', 'Last Name', 'Enter your last name', 'default', true)}
            {renderInput('email', 'Email', 'Enter your email address', 'email-address', true)}
            {renderInput('phone', 'Phone Number', 'Enter your phone number', 'phone-pad')}
            {renderInput('dateOfBirth', 'Date of Birth', 'YYYY-MM-DD', 'default')}
            {renderInput('preferredLocation', 'Preferred Location', 'Enter your preferred location')}
          </View>
        </ScrollView>

        {/* Save Button */}
        <View style={styles.footer}>
          <Button
            onPress={handleSave}
            loading={saving}
            style={styles.saveButton}
            testID="save-button">
            Save Changes
          </Button>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaWrapper>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: getResponsiveSpacing(16),
    paddingVertical: getResponsiveSpacing(12),
    borderBottomWidth: 1,
    borderBottomColor: colors.border.light,
    backgroundColor: colors.surface.primary,
  },
  backButton: {
    padding: getResponsiveSpacing(8),
  },
  headerTitle: {
    flex: 1,
    fontSize: getResponsiveFontSize(18),
    fontWeight: '600',
    color: colors.text.primary,
    textAlign: 'center',
  },
  placeholder: {
    width: getResponsiveSpacing(40),
  },
  content: {
    flex: 1,
    paddingHorizontal: getResponsiveSpacing(16),
  },
  profileImageSection: {
    alignItems: 'center',
    paddingVertical: getResponsiveSpacing(24),
    borderBottomWidth: 1,
    borderBottomColor: colors.border.light,
    marginBottom: getResponsiveSpacing(24),
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: getResponsiveSpacing(8),
  },
  avatar: {
    width: getResponsiveSpacing(80),
    height: getResponsiveSpacing(80),
    borderRadius: getResponsiveSpacing(40),
    backgroundColor: colors.sage400,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: getResponsiveFontSize(32),
    fontWeight: '600',
    color: '#FFFFFF',
  },
  editAvatarButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: getResponsiveSpacing(28),
    height: getResponsiveSpacing(28),
    borderRadius: getResponsiveSpacing(14),
    backgroundColor: colors.sage400,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: colors.surface.primary,
  },
  changePhotoText: {
    fontSize: getResponsiveFontSize(14),
    color: colors.text.secondary,
  },
  formSection: {
    paddingBottom: getResponsiveSpacing(20),
  },
  inputContainer: {
    marginBottom: getResponsiveSpacing(20),
  },
  inputLabel: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '500',
    color: colors.text.primary,
    marginBottom: getResponsiveSpacing(8),
  },
  required: {
    color: colors.error?.text || '#EF4444',
  },
  textInput: {
    borderWidth: 1,
    borderColor: colors.border.light,
    borderRadius: getResponsiveSpacing(8),
    paddingHorizontal: getResponsiveSpacing(16),
    paddingVertical: getResponsiveSpacing(12),
    fontSize: getResponsiveFontSize(16),
    color: colors.text.primary,
    backgroundColor: colors.surface.primary,
  },
  inputError: {
    borderColor: colors.error?.text || '#EF4444',
  },
  errorText: {
    fontSize: getResponsiveFontSize(12),
    color: colors.error?.text || '#EF4444',
    marginTop: getResponsiveSpacing(4),
  },
  footer: {
    paddingHorizontal: getResponsiveSpacing(16),
    paddingVertical: getResponsiveSpacing(16),
    borderTopWidth: 1,
    borderTopColor: colors.border.light,
    backgroundColor: colors.surface.primary,
  },
  saveButton: {
    width: '100%',
  },
});
