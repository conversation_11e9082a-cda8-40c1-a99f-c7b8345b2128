4bb404bddd6c3cc3c95a47405c8542c3
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.staggerAnimations = exports.slideOut = exports.slideIn = exports.shouldReduceMotion = exports.shake = exports.scaleOut = exports.scaleIn = exports.rotate = exports.pulse = exports.fadeOut = exports.fadeIn = exports.default = exports.createTimingAnimation = exports.createSpringAnimation = exports.createProgressAnimation = exports.createPressAnimation = exports.createLoadingAnimation = exports.createExitAnimation = exports.createEntranceAnimation = exports.createAccessibleAnimation = exports.bounce = exports.EASING_PRESETS = exports.ANIMATION_PRESETS = exports.ANIMATION_DURATIONS = void 0;
var _reactNative = require("react-native");
var ANIMATION_DURATIONS = exports.ANIMATION_DURATIONS = {
  instant: 0,
  fast: 150,
  normal: 250,
  slow: 350,
  slower: 500,
  slowest: 750
};
var EASING_PRESETS = exports.EASING_PRESETS = {
  linear: _reactNative.Easing.linear,
  ease: _reactNative.Easing.ease,
  easeIn: _reactNative.Easing.in(_reactNative.Easing.ease),
  easeOut: _reactNative.Easing.out(_reactNative.Easing.ease),
  easeInOut: _reactNative.Easing.inOut(_reactNative.Easing.ease),
  easeInQuad: _reactNative.Easing.in(_reactNative.Easing.quad),
  easeOutQuad: _reactNative.Easing.out(_reactNative.Easing.quad),
  easeInOutQuad: _reactNative.Easing.inOut(_reactNative.Easing.quad),
  easeInCubic: _reactNative.Easing.in(_reactNative.Easing.cubic),
  easeOutCubic: _reactNative.Easing.out(_reactNative.Easing.cubic),
  easeInOutCubic: _reactNative.Easing.inOut(_reactNative.Easing.cubic),
  easeOutBack: _reactNative.Easing.out(_reactNative.Easing.back(1.7)),
  easeInBack: _reactNative.Easing.in(_reactNative.Easing.back(1.7)),
  easeInOutBack: _reactNative.Easing.inOut(_reactNative.Easing.back(1.7)),
  easeOutBounce: _reactNative.Easing.bounce,
  easeInBounce: _reactNative.Easing.in(_reactNative.Easing.bounce),
  easeInOutBounce: _reactNative.Easing.inOut(_reactNative.Easing.bounce)
};
var createTimingAnimation = exports.createTimingAnimation = function createTimingAnimation(animatedValue, toValue) {
  var config = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
  var _config$duration = config.duration,
    duration = _config$duration === void 0 ? ANIMATION_DURATIONS.normal : _config$duration,
    _config$easing = config.easing,
    easing = _config$easing === void 0 ? 'easeInOut' : _config$easing,
    _config$delay = config.delay,
    delay = _config$delay === void 0 ? 0 : _config$delay,
    _config$useNativeDriv = config.useNativeDriver,
    useNativeDriver = _config$useNativeDriv === void 0 ? true : _config$useNativeDriv;
  var animation = _reactNative.Animated.timing(animatedValue, {
    toValue: toValue,
    duration: duration,
    easing: EASING_PRESETS[easing],
    useNativeDriver: useNativeDriver,
    delay: delay
  });
  return animation;
};
var createSpringAnimation = exports.createSpringAnimation = function createSpringAnimation(animatedValue, toValue) {
  var config = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
  var _config$tension = config.tension,
    tension = _config$tension === void 0 ? 100 : _config$tension,
    _config$friction = config.friction,
    friction = _config$friction === void 0 ? 8 : _config$friction,
    _config$speed = config.speed,
    speed = _config$speed === void 0 ? 12 : _config$speed,
    _config$bounciness = config.bounciness,
    bounciness = _config$bounciness === void 0 ? 8 : _config$bounciness,
    _config$useNativeDriv2 = config.useNativeDriver,
    useNativeDriver = _config$useNativeDriv2 === void 0 ? true : _config$useNativeDriv2;
  return _reactNative.Animated.spring(animatedValue, {
    toValue: toValue,
    tension: tension,
    friction: friction,
    speed: speed,
    bounciness: bounciness,
    useNativeDriver: useNativeDriver
  });
};
var fadeIn = exports.fadeIn = function fadeIn(animatedValue) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return createTimingAnimation(animatedValue, 1, Object.assign({
    duration: ANIMATION_DURATIONS.normal,
    easing: 'easeOut'
  }, config));
};
var fadeOut = exports.fadeOut = function fadeOut(animatedValue) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return createTimingAnimation(animatedValue, 0, Object.assign({
    duration: ANIMATION_DURATIONS.normal,
    easing: 'easeIn'
  }, config));
};
var scaleIn = exports.scaleIn = function scaleIn(animatedValue) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return createSpringAnimation(animatedValue, 1, Object.assign({
    tension: 150,
    friction: 8
  }, config));
};
var scaleOut = exports.scaleOut = function scaleOut(animatedValue) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return createTimingAnimation(animatedValue, 0, Object.assign({
    duration: ANIMATION_DURATIONS.fast,
    easing: 'easeIn'
  }, config));
};
var slideIn = exports.slideIn = function slideIn(animatedValue) {
  var direction = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'up';
  var distance = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 50;
  var config = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};
  return createTimingAnimation(animatedValue, 0, Object.assign({
    duration: ANIMATION_DURATIONS.normal,
    easing: 'easeOut'
  }, config));
};
var slideOut = exports.slideOut = function slideOut(animatedValue) {
  var direction = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'down';
  var distance = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 50;
  var config = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};
  var targetValue = direction === 'up' ? -distance : direction === 'down' ? distance : direction === 'left' ? -distance : distance;
  return createTimingAnimation(animatedValue, targetValue, Object.assign({
    duration: ANIMATION_DURATIONS.normal,
    easing: 'easeIn'
  }, config));
};
var pulse = exports.pulse = function pulse(animatedValue) {
  var scale = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1.1;
  var config = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
  return _reactNative.Animated.sequence([createTimingAnimation(animatedValue, scale, Object.assign({
    duration: ANIMATION_DURATIONS.fast,
    easing: 'easeOut'
  }, config)), createTimingAnimation(animatedValue, 1, Object.assign({
    duration: ANIMATION_DURATIONS.fast,
    easing: 'easeIn'
  }, config))]);
};
var shake = exports.shake = function shake(animatedValue) {
  var intensity = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 10;
  var config = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
  return _reactNative.Animated.sequence([createTimingAnimation(animatedValue, intensity, Object.assign({
    duration: ANIMATION_DURATIONS.fast / 4,
    easing: 'linear'
  }, config)), createTimingAnimation(animatedValue, -intensity, Object.assign({
    duration: ANIMATION_DURATIONS.fast / 2,
    easing: 'linear'
  }, config)), createTimingAnimation(animatedValue, intensity, Object.assign({
    duration: ANIMATION_DURATIONS.fast / 2,
    easing: 'linear'
  }, config)), createTimingAnimation(animatedValue, 0, Object.assign({
    duration: ANIMATION_DURATIONS.fast / 4,
    easing: 'linear'
  }, config))]);
};
var bounce = exports.bounce = function bounce(animatedValue) {
  var height = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 20;
  var config = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
  return _reactNative.Animated.sequence([createTimingAnimation(animatedValue, -height, Object.assign({
    duration: ANIMATION_DURATIONS.fast,
    easing: 'easeOut'
  }, config)), createTimingAnimation(animatedValue, 0, Object.assign({
    duration: ANIMATION_DURATIONS.fast,
    easing: 'easeOutBounce'
  }, config))]);
};
var rotate = exports.rotate = function rotate(animatedValue) {
  var rotations = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;
  var config = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
  return createTimingAnimation(animatedValue, rotations, Object.assign({
    duration: ANIMATION_DURATIONS.slow,
    easing: 'linear'
  }, config));
};
var staggerAnimations = exports.staggerAnimations = function staggerAnimations(animations) {
  var staggerDelay = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 100;
  var staggeredAnimations = animations.map(function (animation, index) {
    return _reactNative.Animated.sequence([_reactNative.Animated.delay(index * staggerDelay), animation]);
  });
  return _reactNative.Animated.parallel(staggeredAnimations);
};
var createEntranceAnimation = exports.createEntranceAnimation = function createEntranceAnimation(opacity, scale, translateY) {
  var config = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};
  return _reactNative.Animated.parallel([fadeIn(opacity, config), scaleIn(scale, config), slideIn(translateY, 'up', 30, config)]);
};
var createExitAnimation = exports.createExitAnimation = function createExitAnimation(opacity, scale, translateY) {
  var config = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};
  return _reactNative.Animated.parallel([fadeOut(opacity, config), scaleOut(scale, config), slideOut(translateY, 'down', 30, config)]);
};
var createPressAnimation = exports.createPressAnimation = function createPressAnimation(scale) {
  var pressScale = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0.95;
  var pressIn = function pressIn() {
    createTimingAnimation(scale, pressScale, {
      duration: ANIMATION_DURATIONS.fast,
      easing: 'easeOut'
    }).start();
  };
  var pressOut = function pressOut() {
    createSpringAnimation(scale, 1, {
      tension: 150,
      friction: 8
    }).start();
  };
  return {
    pressIn: pressIn,
    pressOut: pressOut
  };
};
var createLoadingAnimation = exports.createLoadingAnimation = function createLoadingAnimation(rotation) {
  return _reactNative.Animated.loop(createTimingAnimation(rotation, 1, {
    duration: 1000,
    easing: 'linear'
  }));
};
var createProgressAnimation = exports.createProgressAnimation = function createProgressAnimation(progress, targetProgress) {
  var config = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
  return createTimingAnimation(progress, targetProgress, Object.assign({
    duration: ANIMATION_DURATIONS.slow,
    easing: 'easeOut'
  }, config));
};
var shouldReduceMotion = exports.shouldReduceMotion = function shouldReduceMotion() {
  return false;
};
var createAccessibleAnimation = exports.createAccessibleAnimation = function createAccessibleAnimation(animatedValue, toValue) {
  var config = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
  if (shouldReduceMotion()) {
    return createTimingAnimation(animatedValue, toValue, Object.assign({}, config, {
      duration: ANIMATION_DURATIONS.instant
    }));
  }
  return createTimingAnimation(animatedValue, toValue, config);
};
var ANIMATION_PRESETS = exports.ANIMATION_PRESETS = {
  buttonPress: function buttonPress(scale) {
    return createPressAnimation(scale, 0.95);
  },
  buttonHover: function buttonHover(scale) {
    return createPressAnimation(scale, 1.05);
  },
  modalEnter: function modalEnter(opacity, scale) {
    return _reactNative.Animated.parallel([fadeIn(opacity, {
      duration: ANIMATION_DURATIONS.normal
    }), scaleIn(scale, {
      tension: 120,
      friction: 8
    })]);
  },
  modalExit: function modalExit(opacity, scale) {
    return _reactNative.Animated.parallel([fadeOut(opacity, {
      duration: ANIMATION_DURATIONS.fast
    }), scaleOut(scale, {
      duration: ANIMATION_DURATIONS.fast
    })]);
  },
  listItemEnter: function listItemEnter(opacity, translateX) {
    return _reactNative.Animated.parallel([fadeIn(opacity, {
      duration: ANIMATION_DURATIONS.normal
    }), slideIn(translateX, 'right', 50, {
      duration: ANIMATION_DURATIONS.normal
    })]);
  },
  skeletonPulse: function skeletonPulse(opacity) {
    return _reactNative.Animated.loop(_reactNative.Animated.sequence([fadeOut(opacity, {
      duration: ANIMATION_DURATIONS.slow
    }), fadeIn(opacity, {
      duration: ANIMATION_DURATIONS.slow
    })]));
  },
  successPulse: function successPulse(scale) {
    return pulse(scale, 1.2);
  },
  errorShake: function errorShake(translateX) {
    return shake(translateX, 8);
  }
};
var _default = exports.default = {
  ANIMATION_DURATIONS: ANIMATION_DURATIONS,
  EASING_PRESETS: EASING_PRESETS,
  createTimingAnimation: createTimingAnimation,
  createSpringAnimation: createSpringAnimation,
  fadeIn: fadeIn,
  fadeOut: fadeOut,
  scaleIn: scaleIn,
  scaleOut: scaleOut,
  slideIn: slideIn,
  slideOut: slideOut,
  pulse: pulse,
  shake: shake,
  bounce: bounce,
  rotate: rotate,
  staggerAnimations: staggerAnimations,
  createEntranceAnimation: createEntranceAnimation,
  createExitAnimation: createExitAnimation,
  createPressAnimation: createPressAnimation,
  createLoadingAnimation: createLoadingAnimation,
  createProgressAnimation: createProgressAnimation,
  shouldReduceMotion: shouldReduceMotion,
  createAccessibleAnimation: createAccessibleAnimation,
  ANIMATION_PRESETS: ANIMATION_PRESETS
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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