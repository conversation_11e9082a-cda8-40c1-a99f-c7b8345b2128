{"version": 3, "names": ["_performanceMonitor", "require", "describe", "beforeEach", "performanceMonitor", "clearMetrics", "startMonitoring", "after<PERSON>ach", "stopMonitoring", "jest", "clearAllMocks", "it", "expect", "toBe", "consoleSpy", "spyOn", "console", "mockImplementation", "toHaveBeenCalledTimes", "mockRestore", "_metrics$0$metadata", "componentName", "renderTime", "trackRender", "metrics", "getMetricsByCategory", "toHave<PERSON>ength", "name", "value", "metadata", "renderMetrics", "componentMetrics", "get", "reRenders", "toHaveBeenCalledWith", "stringContaining", "propsCount", "stateUpdates", "_metrics$0$metadata2", "_metrics$0$metadata3", "_metrics$0$metadata4", "url", "method", "responseTime", "statusCode", "trackNetworkRequest", "networkMetrics", "cached", "maxMetrics", "i", "length", "toBeLessThanOrEqual", "_metrics$0$metadata5", "_metrics$0$metadata6", "interactionType", "buttonId", "trackUserInteraction", "_metrics$0$metadata7", "_metrics$0$metadata8", "fromScreen", "toScreen", "navigationTime", "trackNavigation", "report", "getPerformanceReport", "summary", "toBeDefined", "averageRenderTime", "toBeGreaterThan", "averageNetworkTime", "cacheHitRate", "slowComponents", "slowNetworkRequests", "recommendations", "slowComponent", "find", "c", "slowRequest", "r", "toContain", "mockMemory", "usedJSHeapSize", "totalJSHeapSize", "jsHeapSizeLimit", "global", "performance", "memory", "memoryMetrics", "push", "timestamp", "Date", "now", "leaks", "reRenderLeak", "leak", "includes", "oldTimestamp", "category", "oldMetric", "m", "toBeUndefined", "clearIntervalSpy", "destroy", "toHaveBeenCalled", "size"], "sources": ["performanceMonitor.test.ts"], "sourcesContent": ["/**\n * Performance Monitor Service Tests\n *\n * Test Coverage:\n * - Performance metric tracking\n * - Memory monitoring\n * - Network performance tracking\n * - Report generation\n * - Error handling\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\nimport { performanceMonitor } from '../performanceMonitor';\n\ndescribe('PerformanceMonitorService', () => {\n  beforeEach(() => {\n    performanceMonitor.clearMetrics();\n    performanceMonitor.startMonitoring();\n  });\n\n  afterEach(() => {\n    performanceMonitor.stopMonitoring();\n    jest.clearAllMocks();\n  });\n\n  describe('Initialization', () => {\n    it('starts monitoring correctly', () => {\n      expect(performanceMonitor['isMonitoring']).toBe(true);\n    });\n\n    it('stops monitoring correctly', () => {\n      performanceMonitor.stopMonitoring();\n      expect(performanceMonitor['isMonitoring']).toBe(false);\n    });\n\n    it('prevents double initialization', () => {\n      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();\n      \n      performanceMonitor.startMonitoring();\n      performanceMonitor.startMonitoring();\n      \n      // Should only log start message once\n      expect(consoleSpy).toHaveBeenCalledTimes(1);\n      \n      consoleSpy.mockRestore();\n    });\n  });\n\n  describe('Render Performance Tracking', () => {\n    it('tracks component render times', () => {\n      const componentName = 'TestComponent';\n      const renderTime = 50;\n      \n      performanceMonitor.trackRender(componentName, renderTime);\n      \n      const metrics = performanceMonitor.getMetricsByCategory('render');\n      expect(metrics).toHaveLength(1);\n      expect(metrics[0].name).toBe('component_render');\n      expect(metrics[0].value).toBe(renderTime);\n      expect(metrics[0].metadata?.componentName).toBe(componentName);\n    });\n\n    it('calculates average render times for components', () => {\n      const componentName = 'TestComponent';\n      \n      performanceMonitor.trackRender(componentName, 40);\n      performanceMonitor.trackRender(componentName, 60);\n      \n      const renderMetrics = performanceMonitor['renderMetrics'];\n      const componentMetrics = renderMetrics.get(componentName);\n      \n      expect(componentMetrics?.renderTime).toBe(50); // Average of 40 and 60\n      expect(componentMetrics?.reRenders).toBe(2);\n    });\n\n    it('warns about slow renders', () => {\n      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();\n      \n      performanceMonitor.trackRender('SlowComponent', 100); // Above 16ms threshold\n      \n      expect(consoleSpy).toHaveBeenCalledWith(\n        expect.stringContaining('Slow render detected: SlowComponent took 100ms')\n      );\n      \n      consoleSpy.mockRestore();\n    });\n\n    it('tracks render metadata correctly', () => {\n      const metadata = { propsCount: 5, stateUpdates: 2 };\n      \n      performanceMonitor.trackRender('TestComponent', 30, metadata);\n      \n      const renderMetrics = performanceMonitor['renderMetrics'];\n      const componentMetrics = renderMetrics.get('TestComponent');\n      \n      expect(componentMetrics?.propsCount).toBe(5);\n      expect(componentMetrics?.stateUpdates).toBe(2);\n    });\n  });\n\n  describe('Network Performance Tracking', () => {\n    it('tracks network request performance', () => {\n      const url = '/api/test';\n      const method = 'GET';\n      const responseTime = 200;\n      const statusCode = 200;\n      \n      performanceMonitor.trackNetworkRequest(url, method, responseTime, statusCode);\n      \n      const metrics = performanceMonitor.getMetricsByCategory('network');\n      expect(metrics).toHaveLength(1);\n      expect(metrics[0].value).toBe(responseTime);\n      expect(metrics[0].metadata?.url).toBe(url);\n      expect(metrics[0].metadata?.method).toBe(method);\n      expect(metrics[0].metadata?.statusCode).toBe(statusCode);\n    });\n\n    it('warns about slow network requests', () => {\n      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();\n      \n      performanceMonitor.trackNetworkRequest('/api/slow', 'GET', 3000, 200); // Above 2s threshold\n      \n      expect(consoleSpy).toHaveBeenCalledWith(\n        expect.stringContaining('Slow network request: GET /api/slow took 3000ms')\n      );\n      \n      consoleSpy.mockRestore();\n    });\n\n    it('tracks cached vs non-cached requests', () => {\n      performanceMonitor.trackNetworkRequest('/api/cached', 'GET', 50, 200, 0, 0, true);\n      performanceMonitor.trackNetworkRequest('/api/fresh', 'GET', 200, 200, 0, 0, false);\n      \n      const networkMetrics = performanceMonitor['networkMetrics'];\n      expect(networkMetrics[0].cached).toBe(true);\n      expect(networkMetrics[1].cached).toBe(false);\n    });\n\n    it('limits stored network metrics', () => {\n      const maxMetrics = performanceMonitor['MAX_METRICS'];\n      \n      // Add more than max metrics\n      for (let i = 0; i < maxMetrics + 100; i++) {\n        performanceMonitor.trackNetworkRequest(`/api/test${i}`, 'GET', 100, 200);\n      }\n      \n      const networkMetrics = performanceMonitor['networkMetrics'];\n      expect(networkMetrics.length).toBeLessThanOrEqual(maxMetrics);\n    });\n  });\n\n  describe('User Interaction Tracking', () => {\n    it('tracks user interaction performance', () => {\n      const interactionType = 'button_click';\n      const responseTime = 50;\n      const metadata = { buttonId: 'submit' };\n      \n      performanceMonitor.trackUserInteraction(interactionType, responseTime, metadata);\n      \n      const metrics = performanceMonitor.getMetricsByCategory('user_interaction');\n      expect(metrics).toHaveLength(1);\n      expect(metrics[0].value).toBe(responseTime);\n      expect(metrics[0].metadata?.interactionType).toBe(interactionType);\n      expect(metrics[0].metadata?.buttonId).toBe('submit');\n    });\n\n    it('warns about slow interactions', () => {\n      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();\n      \n      performanceMonitor.trackUserInteraction('slow_interaction', 150); // Above 100ms threshold\n      \n      expect(consoleSpy).toHaveBeenCalledWith(\n        expect.stringContaining('Slow interaction: slow_interaction took 150ms')\n      );\n      \n      consoleSpy.mockRestore();\n    });\n  });\n\n  describe('Navigation Performance Tracking', () => {\n    it('tracks navigation performance', () => {\n      const fromScreen = 'Home';\n      const toScreen = 'Profile';\n      const navigationTime = 300;\n      \n      performanceMonitor.trackNavigation(fromScreen, toScreen, navigationTime);\n      \n      const metrics = performanceMonitor.getMetricsByCategory('navigation');\n      expect(metrics).toHaveLength(1);\n      expect(metrics[0].value).toBe(navigationTime);\n      expect(metrics[0].metadata?.fromScreen).toBe(fromScreen);\n      expect(metrics[0].metadata?.toScreen).toBe(toScreen);\n    });\n  });\n\n  describe('Performance Reports', () => {\n    beforeEach(() => {\n      // Add some test data\n      performanceMonitor.trackRender('FastComponent', 10);\n      performanceMonitor.trackRender('SlowComponent', 50);\n      performanceMonitor.trackNetworkRequest('/api/fast', 'GET', 100, 200, 0, 0, true);\n      performanceMonitor.trackNetworkRequest('/api/slow', 'GET', 3000, 200, 0, 0, false);\n      performanceMonitor.trackUserInteraction('click', 25);\n    });\n\n    it('generates comprehensive performance report', () => {\n      const report = performanceMonitor.getPerformanceReport();\n      \n      expect(report.summary).toBeDefined();\n      expect(report.summary.averageRenderTime).toBeGreaterThan(0);\n      expect(report.summary.averageNetworkTime).toBeGreaterThan(0);\n      expect(report.summary.cacheHitRate).toBeDefined();\n      \n      expect(report.slowComponents).toBeDefined();\n      expect(report.slowNetworkRequests).toBeDefined();\n      expect(report.recommendations).toBeDefined();\n    });\n\n    it('calculates cache hit rate correctly', () => {\n      const report = performanceMonitor.getPerformanceReport();\n      \n      // 1 cached out of 2 total requests = 50%\n      expect(report.summary.cacheHitRate).toBe(0.5);\n    });\n\n    it('identifies slow components', () => {\n      const report = performanceMonitor.getPerformanceReport();\n      \n      const slowComponent = report.slowComponents.find(c => c.componentName === 'SlowComponent');\n      expect(slowComponent).toBeDefined();\n      expect(slowComponent?.renderTime).toBe(50);\n    });\n\n    it('identifies slow network requests', () => {\n      const report = performanceMonitor.getPerformanceReport();\n      \n      const slowRequest = report.slowNetworkRequests.find(r => r.url === '/api/slow');\n      expect(slowRequest).toBeDefined();\n      expect(slowRequest?.responseTime).toBe(3000);\n    });\n\n    it('generates performance recommendations', () => {\n      const report = performanceMonitor.getPerformanceReport();\n      \n      expect(report.recommendations).toContain(\n        expect.stringContaining('Optimize slow components')\n      );\n      expect(report.recommendations).toContain(\n        expect.stringContaining('request caching')\n      );\n    });\n  });\n\n  describe('Memory Monitoring', () => {\n    it('collects memory metrics when available', () => {\n      // Mock performance.memory\n      const mockMemory = {\n        usedJSHeapSize: 1000000,\n        totalJSHeapSize: 2000000,\n        jsHeapSizeLimit: 4000000,\n      };\n      \n      (global as any).performance = { memory: mockMemory };\n      \n      performanceMonitor['collectMemoryMetrics']();\n      \n      const memoryMetrics = performanceMonitor['memoryMetrics'];\n      expect(memoryMetrics).toHaveLength(1);\n      expect(memoryMetrics[0].usedJSHeapSize).toBe(1000000);\n    });\n\n    it('detects potential memory leaks', () => {\n      // Mock memory growth\n      const memoryMetrics = performanceMonitor['memoryMetrics'];\n      \n      // Add metrics showing memory growth\n      for (let i = 0; i < 10; i++) {\n        memoryMetrics.push({\n          usedJSHeapSize: 1000000 + (i * 10000000), // Growing memory\n          totalJSHeapSize: 2000000,\n          jsHeapSizeLimit: 4000000,\n          timestamp: Date.now() + i,\n        });\n      }\n      \n      const leaks = performanceMonitor['detectMemoryLeaks']();\n      expect(leaks.length).toBeGreaterThan(0);\n      expect(leaks[0]).toContain('Memory usage increased');\n    });\n\n    it('detects excessive re-renders', () => {\n      // Create component with many re-renders\n      for (let i = 0; i < 150; i++) {\n        performanceMonitor.trackRender('ExcessiveComponent', 10);\n      }\n      \n      const leaks = performanceMonitor['detectMemoryLeaks']();\n      const reRenderLeak = leaks.find(leak => leak.includes('ExcessiveComponent'));\n      \n      expect(reRenderLeak).toBeDefined();\n      expect(reRenderLeak).toContain('150 re-renders');\n    });\n  });\n\n  describe('Cleanup and Maintenance', () => {\n    it('clears all metrics', () => {\n      performanceMonitor.trackRender('TestComponent', 50);\n      performanceMonitor.trackNetworkRequest('/api/test', 'GET', 100, 200);\n      \n      performanceMonitor.clearMetrics();\n      \n      expect(performanceMonitor.getMetricsByCategory('render')).toHaveLength(0);\n      expect(performanceMonitor.getMetricsByCategory('network')).toHaveLength(0);\n    });\n\n    it('cleans up old metrics automatically', () => {\n      const oldTimestamp = Date.now() - 700000; // 11+ minutes ago\n      \n      // Manually add old metric\n      performanceMonitor['metrics'].push({\n        name: 'old_metric',\n        value: 100,\n        timestamp: oldTimestamp,\n        category: 'render',\n      });\n      \n      performanceMonitor['cleanupOldMetrics']();\n      \n      const oldMetric = performanceMonitor['metrics'].find(m => m.timestamp === oldTimestamp);\n      expect(oldMetric).toBeUndefined();\n    });\n\n    it('destroys service correctly', () => {\n      const clearIntervalSpy = jest.spyOn(global, 'clearInterval');\n      \n      performanceMonitor.destroy();\n      \n      expect(clearIntervalSpy).toHaveBeenCalled();\n      expect(performanceMonitor['memoryCache'].size).toBe(0);\n      \n      clearIntervalSpy.mockRestore();\n    });\n  });\n});\n"], "mappings": "AAcA,IAAAA,mBAAA,GAAAC,OAAA;AAEAC,QAAQ,CAAC,2BAA2B,EAAE,YAAM;EAC1CC,UAAU,CAAC,YAAM;IACfC,sCAAkB,CAACC,YAAY,CAAC,CAAC;IACjCD,sCAAkB,CAACE,eAAe,CAAC,CAAC;EACtC,CAAC,CAAC;EAEFC,SAAS,CAAC,YAAM;IACdH,sCAAkB,CAACI,cAAc,CAAC,CAAC;IACnCC,IAAI,CAACC,aAAa,CAAC,CAAC;EACtB,CAAC,CAAC;EAEFR,QAAQ,CAAC,gBAAgB,EAAE,YAAM;IAC/BS,EAAE,CAAC,6BAA6B,EAAE,YAAM;MACtCC,MAAM,CAACR,sCAAkB,CAAC,cAAc,CAAC,CAAC,CAACS,IAAI,CAAC,IAAI,CAAC;IACvD,CAAC,CAAC;IAEFF,EAAE,CAAC,4BAA4B,EAAE,YAAM;MACrCP,sCAAkB,CAACI,cAAc,CAAC,CAAC;MACnCI,MAAM,CAACR,sCAAkB,CAAC,cAAc,CAAC,CAAC,CAACS,IAAI,CAAC,KAAK,CAAC;IACxD,CAAC,CAAC;IAEFF,EAAE,CAAC,gCAAgC,EAAE,YAAM;MACzC,IAAMG,UAAU,GAAGL,IAAI,CAACM,KAAK,CAACC,OAAO,EAAE,KAAK,CAAC,CAACC,kBAAkB,CAAC,CAAC;MAElEb,sCAAkB,CAACE,eAAe,CAAC,CAAC;MACpCF,sCAAkB,CAACE,eAAe,CAAC,CAAC;MAGpCM,MAAM,CAACE,UAAU,CAAC,CAACI,qBAAqB,CAAC,CAAC,CAAC;MAE3CJ,UAAU,CAACK,WAAW,CAAC,CAAC;IAC1B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFjB,QAAQ,CAAC,6BAA6B,EAAE,YAAM;IAC5CS,EAAE,CAAC,+BAA+B,EAAE,YAAM;MAAA,IAAAS,mBAAA;MACxC,IAAMC,aAAa,GAAG,eAAe;MACrC,IAAMC,UAAU,GAAG,EAAE;MAErBlB,sCAAkB,CAACmB,WAAW,CAACF,aAAa,EAAEC,UAAU,CAAC;MAEzD,IAAME,OAAO,GAAGpB,sCAAkB,CAACqB,oBAAoB,CAAC,QAAQ,CAAC;MACjEb,MAAM,CAACY,OAAO,CAAC,CAACE,YAAY,CAAC,CAAC,CAAC;MAC/Bd,MAAM,CAACY,OAAO,CAAC,CAAC,CAAC,CAACG,IAAI,CAAC,CAACd,IAAI,CAAC,kBAAkB,CAAC;MAChDD,MAAM,CAACY,OAAO,CAAC,CAAC,CAAC,CAACI,KAAK,CAAC,CAACf,IAAI,CAACS,UAAU,CAAC;MACzCV,MAAM,EAAAQ,mBAAA,GAACI,OAAO,CAAC,CAAC,CAAC,CAACK,QAAQ,qBAAnBT,mBAAA,CAAqBC,aAAa,CAAC,CAACR,IAAI,CAACQ,aAAa,CAAC;IAChE,CAAC,CAAC;IAEFV,EAAE,CAAC,gDAAgD,EAAE,YAAM;MACzD,IAAMU,aAAa,GAAG,eAAe;MAErCjB,sCAAkB,CAACmB,WAAW,CAACF,aAAa,EAAE,EAAE,CAAC;MACjDjB,sCAAkB,CAACmB,WAAW,CAACF,aAAa,EAAE,EAAE,CAAC;MAEjD,IAAMS,aAAa,GAAG1B,sCAAkB,CAAC,eAAe,CAAC;MACzD,IAAM2B,gBAAgB,GAAGD,aAAa,CAACE,GAAG,CAACX,aAAa,CAAC;MAEzDT,MAAM,CAACmB,gBAAgB,oBAAhBA,gBAAgB,CAAET,UAAU,CAAC,CAACT,IAAI,CAAC,EAAE,CAAC;MAC7CD,MAAM,CAACmB,gBAAgB,oBAAhBA,gBAAgB,CAAEE,SAAS,CAAC,CAACpB,IAAI,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC;IAEFF,EAAE,CAAC,0BAA0B,EAAE,YAAM;MACnC,IAAMG,UAAU,GAAGL,IAAI,CAACM,KAAK,CAACC,OAAO,EAAE,MAAM,CAAC,CAACC,kBAAkB,CAAC,CAAC;MAEnEb,sCAAkB,CAACmB,WAAW,CAAC,eAAe,EAAE,GAAG,CAAC;MAEpDX,MAAM,CAACE,UAAU,CAAC,CAACoB,oBAAoB,CACrCtB,MAAM,CAACuB,gBAAgB,CAAC,gDAAgD,CAC1E,CAAC;MAEDrB,UAAU,CAACK,WAAW,CAAC,CAAC;IAC1B,CAAC,CAAC;IAEFR,EAAE,CAAC,kCAAkC,EAAE,YAAM;MAC3C,IAAMkB,QAAQ,GAAG;QAAEO,UAAU,EAAE,CAAC;QAAEC,YAAY,EAAE;MAAE,CAAC;MAEnDjC,sCAAkB,CAACmB,WAAW,CAAC,eAAe,EAAE,EAAE,EAAEM,QAAQ,CAAC;MAE7D,IAAMC,aAAa,GAAG1B,sCAAkB,CAAC,eAAe,CAAC;MACzD,IAAM2B,gBAAgB,GAAGD,aAAa,CAACE,GAAG,CAAC,eAAe,CAAC;MAE3DpB,MAAM,CAACmB,gBAAgB,oBAAhBA,gBAAgB,CAAEK,UAAU,CAAC,CAACvB,IAAI,CAAC,CAAC,CAAC;MAC5CD,MAAM,CAACmB,gBAAgB,oBAAhBA,gBAAgB,CAAEM,YAAY,CAAC,CAACxB,IAAI,CAAC,CAAC,CAAC;IAChD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFX,QAAQ,CAAC,8BAA8B,EAAE,YAAM;IAC7CS,EAAE,CAAC,oCAAoC,EAAE,YAAM;MAAA,IAAA2B,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA;MAC7C,IAAMC,GAAG,GAAG,WAAW;MACvB,IAAMC,MAAM,GAAG,KAAK;MACpB,IAAMC,YAAY,GAAG,GAAG;MACxB,IAAMC,UAAU,GAAG,GAAG;MAEtBxC,sCAAkB,CAACyC,mBAAmB,CAACJ,GAAG,EAAEC,MAAM,EAAEC,YAAY,EAAEC,UAAU,CAAC;MAE7E,IAAMpB,OAAO,GAAGpB,sCAAkB,CAACqB,oBAAoB,CAAC,SAAS,CAAC;MAClEb,MAAM,CAACY,OAAO,CAAC,CAACE,YAAY,CAAC,CAAC,CAAC;MAC/Bd,MAAM,CAACY,OAAO,CAAC,CAAC,CAAC,CAACI,KAAK,CAAC,CAACf,IAAI,CAAC8B,YAAY,CAAC;MAC3C/B,MAAM,EAAA0B,oBAAA,GAACd,OAAO,CAAC,CAAC,CAAC,CAACK,QAAQ,qBAAnBS,oBAAA,CAAqBG,GAAG,CAAC,CAAC5B,IAAI,CAAC4B,GAAG,CAAC;MAC1C7B,MAAM,EAAA2B,oBAAA,GAACf,OAAO,CAAC,CAAC,CAAC,CAACK,QAAQ,qBAAnBU,oBAAA,CAAqBG,MAAM,CAAC,CAAC7B,IAAI,CAAC6B,MAAM,CAAC;MAChD9B,MAAM,EAAA4B,oBAAA,GAAChB,OAAO,CAAC,CAAC,CAAC,CAACK,QAAQ,qBAAnBW,oBAAA,CAAqBI,UAAU,CAAC,CAAC/B,IAAI,CAAC+B,UAAU,CAAC;IAC1D,CAAC,CAAC;IAEFjC,EAAE,CAAC,mCAAmC,EAAE,YAAM;MAC5C,IAAMG,UAAU,GAAGL,IAAI,CAACM,KAAK,CAACC,OAAO,EAAE,MAAM,CAAC,CAACC,kBAAkB,CAAC,CAAC;MAEnEb,sCAAkB,CAACyC,mBAAmB,CAAC,WAAW,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,CAAC;MAErEjC,MAAM,CAACE,UAAU,CAAC,CAACoB,oBAAoB,CACrCtB,MAAM,CAACuB,gBAAgB,CAAC,iDAAiD,CAC3E,CAAC;MAEDrB,UAAU,CAACK,WAAW,CAAC,CAAC;IAC1B,CAAC,CAAC;IAEFR,EAAE,CAAC,sCAAsC,EAAE,YAAM;MAC/CP,sCAAkB,CAACyC,mBAAmB,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACjFzC,sCAAkB,CAACyC,mBAAmB,CAAC,YAAY,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC;MAElF,IAAMC,cAAc,GAAG1C,sCAAkB,CAAC,gBAAgB,CAAC;MAC3DQ,MAAM,CAACkC,cAAc,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAAClC,IAAI,CAAC,IAAI,CAAC;MAC3CD,MAAM,CAACkC,cAAc,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAAClC,IAAI,CAAC,KAAK,CAAC;IAC9C,CAAC,CAAC;IAEFF,EAAE,CAAC,+BAA+B,EAAE,YAAM;MACxC,IAAMqC,UAAU,GAAG5C,sCAAkB,CAAC,aAAa,CAAC;MAGpD,KAAK,IAAI6C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,UAAU,GAAG,GAAG,EAAEC,CAAC,EAAE,EAAE;QACzC7C,sCAAkB,CAACyC,mBAAmB,CAAC,YAAYI,CAAC,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC;MAC1E;MAEA,IAAMH,cAAc,GAAG1C,sCAAkB,CAAC,gBAAgB,CAAC;MAC3DQ,MAAM,CAACkC,cAAc,CAACI,MAAM,CAAC,CAACC,mBAAmB,CAACH,UAAU,CAAC;IAC/D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF9C,QAAQ,CAAC,2BAA2B,EAAE,YAAM;IAC1CS,EAAE,CAAC,qCAAqC,EAAE,YAAM;MAAA,IAAAyC,oBAAA,EAAAC,oBAAA;MAC9C,IAAMC,eAAe,GAAG,cAAc;MACtC,IAAMX,YAAY,GAAG,EAAE;MACvB,IAAMd,QAAQ,GAAG;QAAE0B,QAAQ,EAAE;MAAS,CAAC;MAEvCnD,sCAAkB,CAACoD,oBAAoB,CAACF,eAAe,EAAEX,YAAY,EAAEd,QAAQ,CAAC;MAEhF,IAAML,OAAO,GAAGpB,sCAAkB,CAACqB,oBAAoB,CAAC,kBAAkB,CAAC;MAC3Eb,MAAM,CAACY,OAAO,CAAC,CAACE,YAAY,CAAC,CAAC,CAAC;MAC/Bd,MAAM,CAACY,OAAO,CAAC,CAAC,CAAC,CAACI,KAAK,CAAC,CAACf,IAAI,CAAC8B,YAAY,CAAC;MAC3C/B,MAAM,EAAAwC,oBAAA,GAAC5B,OAAO,CAAC,CAAC,CAAC,CAACK,QAAQ,qBAAnBuB,oBAAA,CAAqBE,eAAe,CAAC,CAACzC,IAAI,CAACyC,eAAe,CAAC;MAClE1C,MAAM,EAAAyC,oBAAA,GAAC7B,OAAO,CAAC,CAAC,CAAC,CAACK,QAAQ,qBAAnBwB,oBAAA,CAAqBE,QAAQ,CAAC,CAAC1C,IAAI,CAAC,QAAQ,CAAC;IACtD,CAAC,CAAC;IAEFF,EAAE,CAAC,+BAA+B,EAAE,YAAM;MACxC,IAAMG,UAAU,GAAGL,IAAI,CAACM,KAAK,CAACC,OAAO,EAAE,MAAM,CAAC,CAACC,kBAAkB,CAAC,CAAC;MAEnEb,sCAAkB,CAACoD,oBAAoB,CAAC,kBAAkB,EAAE,GAAG,CAAC;MAEhE5C,MAAM,CAACE,UAAU,CAAC,CAACoB,oBAAoB,CACrCtB,MAAM,CAACuB,gBAAgB,CAAC,+CAA+C,CACzE,CAAC;MAEDrB,UAAU,CAACK,WAAW,CAAC,CAAC;IAC1B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFjB,QAAQ,CAAC,iCAAiC,EAAE,YAAM;IAChDS,EAAE,CAAC,+BAA+B,EAAE,YAAM;MAAA,IAAA8C,oBAAA,EAAAC,oBAAA;MACxC,IAAMC,UAAU,GAAG,MAAM;MACzB,IAAMC,QAAQ,GAAG,SAAS;MAC1B,IAAMC,cAAc,GAAG,GAAG;MAE1BzD,sCAAkB,CAAC0D,eAAe,CAACH,UAAU,EAAEC,QAAQ,EAAEC,cAAc,CAAC;MAExE,IAAMrC,OAAO,GAAGpB,sCAAkB,CAACqB,oBAAoB,CAAC,YAAY,CAAC;MACrEb,MAAM,CAACY,OAAO,CAAC,CAACE,YAAY,CAAC,CAAC,CAAC;MAC/Bd,MAAM,CAACY,OAAO,CAAC,CAAC,CAAC,CAACI,KAAK,CAAC,CAACf,IAAI,CAACgD,cAAc,CAAC;MAC7CjD,MAAM,EAAA6C,oBAAA,GAACjC,OAAO,CAAC,CAAC,CAAC,CAACK,QAAQ,qBAAnB4B,oBAAA,CAAqBE,UAAU,CAAC,CAAC9C,IAAI,CAAC8C,UAAU,CAAC;MACxD/C,MAAM,EAAA8C,oBAAA,GAAClC,OAAO,CAAC,CAAC,CAAC,CAACK,QAAQ,qBAAnB6B,oBAAA,CAAqBE,QAAQ,CAAC,CAAC/C,IAAI,CAAC+C,QAAQ,CAAC;IACtD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF1D,QAAQ,CAAC,qBAAqB,EAAE,YAAM;IACpCC,UAAU,CAAC,YAAM;MAEfC,sCAAkB,CAACmB,WAAW,CAAC,eAAe,EAAE,EAAE,CAAC;MACnDnB,sCAAkB,CAACmB,WAAW,CAAC,eAAe,EAAE,EAAE,CAAC;MACnDnB,sCAAkB,CAACyC,mBAAmB,CAAC,WAAW,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MAChFzC,sCAAkB,CAACyC,mBAAmB,CAAC,WAAW,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC;MAClFzC,sCAAkB,CAACoD,oBAAoB,CAAC,OAAO,EAAE,EAAE,CAAC;IACtD,CAAC,CAAC;IAEF7C,EAAE,CAAC,4CAA4C,EAAE,YAAM;MACrD,IAAMoD,MAAM,GAAG3D,sCAAkB,CAAC4D,oBAAoB,CAAC,CAAC;MAExDpD,MAAM,CAACmD,MAAM,CAACE,OAAO,CAAC,CAACC,WAAW,CAAC,CAAC;MACpCtD,MAAM,CAACmD,MAAM,CAACE,OAAO,CAACE,iBAAiB,CAAC,CAACC,eAAe,CAAC,CAAC,CAAC;MAC3DxD,MAAM,CAACmD,MAAM,CAACE,OAAO,CAACI,kBAAkB,CAAC,CAACD,eAAe,CAAC,CAAC,CAAC;MAC5DxD,MAAM,CAACmD,MAAM,CAACE,OAAO,CAACK,YAAY,CAAC,CAACJ,WAAW,CAAC,CAAC;MAEjDtD,MAAM,CAACmD,MAAM,CAACQ,cAAc,CAAC,CAACL,WAAW,CAAC,CAAC;MAC3CtD,MAAM,CAACmD,MAAM,CAACS,mBAAmB,CAAC,CAACN,WAAW,CAAC,CAAC;MAChDtD,MAAM,CAACmD,MAAM,CAACU,eAAe,CAAC,CAACP,WAAW,CAAC,CAAC;IAC9C,CAAC,CAAC;IAEFvD,EAAE,CAAC,qCAAqC,EAAE,YAAM;MAC9C,IAAMoD,MAAM,GAAG3D,sCAAkB,CAAC4D,oBAAoB,CAAC,CAAC;MAGxDpD,MAAM,CAACmD,MAAM,CAACE,OAAO,CAACK,YAAY,CAAC,CAACzD,IAAI,CAAC,GAAG,CAAC;IAC/C,CAAC,CAAC;IAEFF,EAAE,CAAC,4BAA4B,EAAE,YAAM;MACrC,IAAMoD,MAAM,GAAG3D,sCAAkB,CAAC4D,oBAAoB,CAAC,CAAC;MAExD,IAAMU,aAAa,GAAGX,MAAM,CAACQ,cAAc,CAACI,IAAI,CAAC,UAAAC,CAAC;QAAA,OAAIA,CAAC,CAACvD,aAAa,KAAK,eAAe;MAAA,EAAC;MAC1FT,MAAM,CAAC8D,aAAa,CAAC,CAACR,WAAW,CAAC,CAAC;MACnCtD,MAAM,CAAC8D,aAAa,oBAAbA,aAAa,CAAEpD,UAAU,CAAC,CAACT,IAAI,CAAC,EAAE,CAAC;IAC5C,CAAC,CAAC;IAEFF,EAAE,CAAC,kCAAkC,EAAE,YAAM;MAC3C,IAAMoD,MAAM,GAAG3D,sCAAkB,CAAC4D,oBAAoB,CAAC,CAAC;MAExD,IAAMa,WAAW,GAAGd,MAAM,CAACS,mBAAmB,CAACG,IAAI,CAAC,UAAAG,CAAC;QAAA,OAAIA,CAAC,CAACrC,GAAG,KAAK,WAAW;MAAA,EAAC;MAC/E7B,MAAM,CAACiE,WAAW,CAAC,CAACX,WAAW,CAAC,CAAC;MACjCtD,MAAM,CAACiE,WAAW,oBAAXA,WAAW,CAAElC,YAAY,CAAC,CAAC9B,IAAI,CAAC,IAAI,CAAC;IAC9C,CAAC,CAAC;IAEFF,EAAE,CAAC,uCAAuC,EAAE,YAAM;MAChD,IAAMoD,MAAM,GAAG3D,sCAAkB,CAAC4D,oBAAoB,CAAC,CAAC;MAExDpD,MAAM,CAACmD,MAAM,CAACU,eAAe,CAAC,CAACM,SAAS,CACtCnE,MAAM,CAACuB,gBAAgB,CAAC,0BAA0B,CACpD,CAAC;MACDvB,MAAM,CAACmD,MAAM,CAACU,eAAe,CAAC,CAACM,SAAS,CACtCnE,MAAM,CAACuB,gBAAgB,CAAC,iBAAiB,CAC3C,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFjC,QAAQ,CAAC,mBAAmB,EAAE,YAAM;IAClCS,EAAE,CAAC,wCAAwC,EAAE,YAAM;MAEjD,IAAMqE,UAAU,GAAG;QACjBC,cAAc,EAAE,OAAO;QACvBC,eAAe,EAAE,OAAO;QACxBC,eAAe,EAAE;MACnB,CAAC;MAEAC,MAAM,CAASC,WAAW,GAAG;QAAEC,MAAM,EAAEN;MAAW,CAAC;MAEpD5E,sCAAkB,CAAC,sBAAsB,CAAC,CAAC,CAAC;MAE5C,IAAMmF,aAAa,GAAGnF,sCAAkB,CAAC,eAAe,CAAC;MACzDQ,MAAM,CAAC2E,aAAa,CAAC,CAAC7D,YAAY,CAAC,CAAC,CAAC;MACrCd,MAAM,CAAC2E,aAAa,CAAC,CAAC,CAAC,CAACN,cAAc,CAAC,CAACpE,IAAI,CAAC,OAAO,CAAC;IACvD,CAAC,CAAC;IAEFF,EAAE,CAAC,gCAAgC,EAAE,YAAM;MAEzC,IAAM4E,aAAa,GAAGnF,sCAAkB,CAAC,eAAe,CAAC;MAGzD,KAAK,IAAI6C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QAC3BsC,aAAa,CAACC,IAAI,CAAC;UACjBP,cAAc,EAAE,OAAO,GAAIhC,CAAC,GAAG,QAAS;UACxCiC,eAAe,EAAE,OAAO;UACxBC,eAAe,EAAE,OAAO;UACxBM,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG1C;QAC1B,CAAC,CAAC;MACJ;MAEA,IAAM2C,KAAK,GAAGxF,sCAAkB,CAAC,mBAAmB,CAAC,CAAC,CAAC;MACvDQ,MAAM,CAACgF,KAAK,CAAC1C,MAAM,CAAC,CAACkB,eAAe,CAAC,CAAC,CAAC;MACvCxD,MAAM,CAACgF,KAAK,CAAC,CAAC,CAAC,CAAC,CAACb,SAAS,CAAC,wBAAwB,CAAC;IACtD,CAAC,CAAC;IAEFpE,EAAE,CAAC,8BAA8B,EAAE,YAAM;MAEvC,KAAK,IAAIsC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;QAC5B7C,sCAAkB,CAACmB,WAAW,CAAC,oBAAoB,EAAE,EAAE,CAAC;MAC1D;MAEA,IAAMqE,KAAK,GAAGxF,sCAAkB,CAAC,mBAAmB,CAAC,CAAC,CAAC;MACvD,IAAMyF,YAAY,GAAGD,KAAK,CAACjB,IAAI,CAAC,UAAAmB,IAAI;QAAA,OAAIA,IAAI,CAACC,QAAQ,CAAC,oBAAoB,CAAC;MAAA,EAAC;MAE5EnF,MAAM,CAACiF,YAAY,CAAC,CAAC3B,WAAW,CAAC,CAAC;MAClCtD,MAAM,CAACiF,YAAY,CAAC,CAACd,SAAS,CAAC,gBAAgB,CAAC;IAClD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7E,QAAQ,CAAC,yBAAyB,EAAE,YAAM;IACxCS,EAAE,CAAC,oBAAoB,EAAE,YAAM;MAC7BP,sCAAkB,CAACmB,WAAW,CAAC,eAAe,EAAE,EAAE,CAAC;MACnDnB,sCAAkB,CAACyC,mBAAmB,CAAC,WAAW,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC;MAEpEzC,sCAAkB,CAACC,YAAY,CAAC,CAAC;MAEjCO,MAAM,CAACR,sCAAkB,CAACqB,oBAAoB,CAAC,QAAQ,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC,CAAC;MACzEd,MAAM,CAACR,sCAAkB,CAACqB,oBAAoB,CAAC,SAAS,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC,CAAC;IAC5E,CAAC,CAAC;IAEFf,EAAE,CAAC,qCAAqC,EAAE,YAAM;MAC9C,IAAMqF,YAAY,GAAGN,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,MAAM;MAGxCvF,sCAAkB,CAAC,SAAS,CAAC,CAACoF,IAAI,CAAC;QACjC7D,IAAI,EAAE,YAAY;QAClBC,KAAK,EAAE,GAAG;QACV6D,SAAS,EAAEO,YAAY;QACvBC,QAAQ,EAAE;MACZ,CAAC,CAAC;MAEF7F,sCAAkB,CAAC,mBAAmB,CAAC,CAAC,CAAC;MAEzC,IAAM8F,SAAS,GAAG9F,sCAAkB,CAAC,SAAS,CAAC,CAACuE,IAAI,CAAC,UAAAwB,CAAC;QAAA,OAAIA,CAAC,CAACV,SAAS,KAAKO,YAAY;MAAA,EAAC;MACvFpF,MAAM,CAACsF,SAAS,CAAC,CAACE,aAAa,CAAC,CAAC;IACnC,CAAC,CAAC;IAEFzF,EAAE,CAAC,4BAA4B,EAAE,YAAM;MACrC,IAAM0F,gBAAgB,GAAG5F,IAAI,CAACM,KAAK,CAACqE,MAAM,EAAE,eAAe,CAAC;MAE5DhF,sCAAkB,CAACkG,OAAO,CAAC,CAAC;MAE5B1F,MAAM,CAACyF,gBAAgB,CAAC,CAACE,gBAAgB,CAAC,CAAC;MAC3C3F,MAAM,CAACR,sCAAkB,CAAC,aAAa,CAAC,CAACoG,IAAI,CAAC,CAAC3F,IAAI,CAAC,CAAC,CAAC;MAEtDwF,gBAAgB,CAAClF,WAAW,CAAC,CAAC;IAChC,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}