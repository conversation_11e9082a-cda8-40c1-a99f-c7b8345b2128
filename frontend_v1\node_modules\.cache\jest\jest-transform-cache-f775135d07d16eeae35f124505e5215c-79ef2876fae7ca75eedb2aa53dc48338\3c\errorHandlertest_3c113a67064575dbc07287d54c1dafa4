6d18a1479f00d3f7b415541ff9cf03d5
_getJestObj().mock('expo-haptics', function () {
  return {
    notificationAsync: jest.fn(),
    NotificationFeedbackType: {
      Warning: 'warning',
      Error: 'error'
    }
  };
});
_getJestObj().mock('react-native', function () {
  return {
    Alert: {
      alert: jest.fn()
    }
  };
});
var _errorHandler = require("../errorHandler");
function _getJestObj() {
  var _require = require("@jest/globals"),
    jest = _require.jest;
  _getJestObj = function _getJestObj() {
    return jest;
  };
  return jest;
}
describe('ErrorHandler', function () {
  beforeEach(function () {
    jest.clearAllMocks();
    _errorHandler.errorHandler.clearErrors();
  });
  describe('handleError', function () {
    it('should handle basic errors correctly', function () {
      var error = new Error('Test error');
      var result = (0, _errorHandler.handleError)(error);
      expect(result).toMatchObject({
        type: _errorHandler.ErrorType.UNKNOWN,
        message: 'Test error',
        severity: _errorHandler.ErrorSeverity.LOW
      });
      expect(result.id).toBeDefined();
      expect(result.timestamp).toBeInstanceOf(Date);
    });
    it('should handle errors with context', function () {
      var error = new Error('Test error');
      var context = {
        component: 'TestComponent',
        action: 'testAction'
      };
      var result = (0, _errorHandler.handleError)(error, context);
      expect(result.context).toEqual(context);
    });
    it('should preserve AppError properties', function () {
      var appError = {
        id: 'test-id',
        type: _errorHandler.ErrorType.VALIDATION,
        severity: _errorHandler.ErrorSeverity.HIGH,
        message: 'Validation error',
        userMessage: 'Please fix the form',
        timestamp: new Date()
      };
      var result = (0, _errorHandler.handleError)(appError);
      expect(result).toMatchObject(appError);
    });
  });
  describe('handleNetworkError', function () {
    it('should handle network errors correctly', function () {
      var error = new Error('Network request failed');
      var result = (0, _errorHandler.handleNetworkError)(error);
      expect(result).toMatchObject({
        type: _errorHandler.ErrorType.NETWORK,
        severity: _errorHandler.ErrorSeverity.MEDIUM,
        message: 'Network request failed',
        userMessage: 'Network connection issue. Please check your internet connection and try again.'
      });
    });
  });
  describe('error type detection', function () {
    it('should detect network errors', function () {
      var error = new Error('fetch failed');
      var result = (0, _errorHandler.handleError)(error);
      expect(result.type).toBe(_errorHandler.ErrorType.NETWORK);
    });
    it('should detect authentication errors', function () {
      var error = new Error('unauthorized access');
      var result = (0, _errorHandler.handleError)(error);
      expect(result.type).toBe(_errorHandler.ErrorType.AUTHENTICATION);
    });
    it('should detect authorization errors', function () {
      var error = new Error('forbidden resource');
      var result = (0, _errorHandler.handleError)(error);
      expect(result.type).toBe(_errorHandler.ErrorType.AUTHORIZATION);
    });
    it('should detect not found errors', function () {
      var error = new Error('resource not found');
      var result = (0, _errorHandler.handleError)(error);
      expect(result.type).toBe(_errorHandler.ErrorType.NOT_FOUND);
    });
    it('should detect server errors', function () {
      var error = new Error('internal server error');
      var result = (0, _errorHandler.handleError)(error);
      expect(result.type).toBe(_errorHandler.ErrorType.SERVER);
    });
  });
  describe('severity detection', function () {
    it('should detect critical errors', function () {
      var error = new Error('critical system failure');
      var result = (0, _errorHandler.handleError)(error);
      expect(result.severity).toBe(_errorHandler.ErrorSeverity.CRITICAL);
    });
    it('should detect high severity errors', function () {
      var error = new Error('unauthorized access');
      var result = (0, _errorHandler.handleError)(error);
      expect(result.severity).toBe(_errorHandler.ErrorSeverity.HIGH);
    });
    it('should detect medium severity errors', function () {
      var error = new Error('network timeout');
      var result = (0, _errorHandler.handleError)(error);
      expect(result.severity).toBe(_errorHandler.ErrorSeverity.MEDIUM);
    });
    it('should default to low severity', function () {
      var error = new Error('minor issue');
      var result = (0, _errorHandler.handleError)(error);
      expect(result.severity).toBe(_errorHandler.ErrorSeverity.LOW);
    });
  });
  describe('user message generation', function () {
    it('should generate appropriate network error messages', function () {
      var error = new Error('fetch failed');
      var result = (0, _errorHandler.handleError)(error);
      expect(result.userMessage).toBe('Please check your internet connection and try again.');
    });
    it('should generate appropriate auth error messages', function () {
      var error = new Error('unauthorized');
      var result = (0, _errorHandler.handleError)(error);
      expect(result.userMessage).toBe('Please log in to continue.');
    });
    it('should generate appropriate authorization error messages', function () {
      var error = new Error('forbidden');
      var result = (0, _errorHandler.handleError)(error);
      expect(result.userMessage).toBe('You don\'t have permission to perform this action.');
    });
    it('should generate appropriate not found error messages', function () {
      var error = new Error('not found');
      var result = (0, _errorHandler.handleError)(error);
      expect(result.userMessage).toBe('The requested item could not be found.');
    });
    it('should generate appropriate server error messages', function () {
      var error = new Error('server error');
      var result = (0, _errorHandler.handleError)(error);
      expect(result.userMessage).toBe('Server is temporarily unavailable. Please try again later.');
    });
    it('should generate default error messages', function () {
      var error = new Error('unknown error');
      var result = (0, _errorHandler.handleError)(error);
      expect(result.userMessage).toBe('An unexpected error occurred. Please try again.');
    });
  });
  describe('error statistics', function () {
    it('should track error statistics correctly', function () {
      (0, _errorHandler.handleError)(new Error('network error'));
      (0, _errorHandler.handleError)(new Error('validation error'));
      (0, _errorHandler.handleError)(new Error('critical error'));
      var stats = _errorHandler.errorHandler.getErrorStats();
      expect(stats.total).toBe(3);
      expect(stats.byType[_errorHandler.ErrorType.NETWORK]).toBe(1);
      expect(stats.byType[_errorHandler.ErrorType.UNKNOWN]).toBe(2);
      expect(stats.bySeverity[_errorHandler.ErrorSeverity.MEDIUM]).toBe(1);
      expect(stats.bySeverity[_errorHandler.ErrorSeverity.CRITICAL]).toBe(1);
      expect(stats.bySeverity[_errorHandler.ErrorSeverity.LOW]).toBe(1);
    });
    it('should clear errors correctly', function () {
      (0, _errorHandler.handleError)(new Error('test error'));
      expect(_errorHandler.errorHandler.getErrorStats().total).toBe(1);
      _errorHandler.errorHandler.clearErrors();
      expect(_errorHandler.errorHandler.getErrorStats().total).toBe(0);
    });
  });
  describe('error queue management', function () {
    it('should maintain queue size limit', function () {
      for (var i = 0; i < 105; i++) {
        (0, _errorHandler.handleError)(new Error(`Error ${i}`));
      }
      var stats = _errorHandler.errorHandler.getErrorStats();
      expect(stats.total).toBe(100);
    });
  });
  describe('error ID generation', function () {
    it('should generate unique error IDs', function () {
      var error1 = (0, _errorHandler.handleError)(new Error('Error 1'));
      var error2 = (0, _errorHandler.handleError)(new Error('Error 2'));
      expect(error1.id).toBeDefined();
      expect(error2.id).toBeDefined();
      expect(error1.id).not.toBe(error2.id);
    });
    it('should generate IDs with correct format', function () {
      var error = (0, _errorHandler.handleError)(new Error('Test error'));
      expect(error.id).toMatch(/^error_\d+_[a-z0-9]+$/);
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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