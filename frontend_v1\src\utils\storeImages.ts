/**
 * Store Images Utility
 *
 * Provides store/provider images for the Vierla application.
 * Includes placeholder images and real store images for different service categories.
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

// Store image URLs - Using placeholder images from Unsplash for different service categories
export const STORE_IMAGES = {
  // Hair Services
  hair_salon_1: 'https://images.unsplash.com/photo-**********-138dadb4c035?w=400&h=400&fit=crop&crop=center',
  hair_salon_2: 'https://images.unsplash.com/photo-1521590832167-7bcbfaa6381f?w=400&h=400&fit=crop&crop=center',
  hair_salon_3: 'https://images.unsplash.com/photo-**********-8baeececf3df?w=400&h=400&fit=crop&crop=center',
  hair_salon_4: 'https://images.unsplash.com/photo-**********-f09722fb4948?w=400&h=400&fit=crop&crop=center',
  hair_salon_5: 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400&h=400&fit=crop&crop=center',

  // Nail Services
  nail_salon_1: 'https://images.unsplash.com/photo-1604654894610-df63bc536371?w=400&h=400&fit=crop&crop=center',
  nail_salon_2: 'https://images.unsplash.com/photo-1607779097040-26e80aa78e66?w=400&h=400&fit=crop&crop=center',
  nail_salon_3: 'https://images.unsplash.com/photo-1610992015732-2449b76344bc?w=400&h=400&fit=crop&crop=center',
  nail_salon_4: 'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400&h=400&fit=crop&crop=center',
  nail_salon_5: 'https://images.unsplash.com/photo-1607779097040-26e80aa78e66?w=400&h=400&fit=crop&crop=center',

  // Lash Services
  lash_salon_1: 'https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?w=400&h=400&fit=crop&crop=center',
  lash_salon_2: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=400&fit=crop&crop=center',
  lash_salon_3: 'https://images.unsplash.com/photo-1598300042247-d088f8ab3a91?w=400&h=400&fit=crop&crop=center',
  lash_salon_4: 'https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?w=400&h=400&fit=crop&crop=center',
  lash_salon_5: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=400&fit=crop&crop=center',

  // Braiding Services
  braiding_salon_1: 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400&h=400&fit=crop&crop=center',
  braiding_salon_2: 'https://images.unsplash.com/photo-**********-138dadb4c035?w=400&h=400&fit=crop&crop=center',
  braiding_salon_3: 'https://images.unsplash.com/photo-1521590832167-7bcbfaa6381f?w=400&h=400&fit=crop&crop=center',
  braiding_salon_4: 'https://images.unsplash.com/photo-**********-8baeececf3df?w=400&h=400&fit=crop&crop=center',
  braiding_salon_5: 'https://images.unsplash.com/photo-**********-f09722fb4948?w=400&h=400&fit=crop&crop=center',

  // Massage Services
  massage_salon_1: 'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=400&h=400&fit=crop&crop=center',
  massage_salon_2: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=400&fit=crop&crop=center',
  massage_salon_3: 'https://images.unsplash.com/photo-1540555700478-4be289fbecef?w=400&h=400&fit=crop&crop=center',
  massage_salon_4: 'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=400&h=400&fit=crop&crop=center',
  massage_salon_5: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=400&fit=crop&crop=center',

  // Skincare Services
  skincare_salon_1: 'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=400&h=400&fit=crop&crop=center',
  skincare_salon_2: 'https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?w=400&h=400&fit=crop&crop=center',
  skincare_salon_3: 'https://images.unsplash.com/photo-1598300042247-d088f8ab3a91?w=400&h=400&fit=crop&crop=center',
  skincare_salon_4: 'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=400&h=400&fit=crop&crop=center',
  skincare_salon_5: 'https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?w=400&h=400&fit=crop&crop=center',
};

// Category to image mapping
const CATEGORY_IMAGES = {
  'Barber': ['barber_shop_1', 'barber_shop_2', 'barber_shop_3', 'barber_shop_4', 'barber_shop_5'],
  'Salon': ['hair_salon_1', 'hair_salon_2', 'hair_salon_3', 'hair_salon_4', 'hair_salon_5'],
  'Nail Services': ['nail_salon_1', 'nail_salon_2', 'nail_salon_3', 'nail_salon_4', 'nail_salon_5'],
  'Lash Services': ['lash_salon_1', 'lash_salon_2', 'lash_salon_3', 'lash_salon_4', 'lash_salon_5'],
  'Braiding': ['braiding_salon_1', 'braiding_salon_2', 'braiding_salon_3', 'braiding_salon_4', 'braiding_salon_5'],
  'Massage': ['massage_salon_1', 'massage_salon_2', 'massage_salon_3', 'massage_salon_4', 'massage_salon_5'],
  'Skincare': ['skincare_salon_1', 'skincare_salon_2', 'skincare_salon_3', 'skincare_salon_4', 'skincare_salon_5'],
};

/**
 * Get a store image URL based on provider ID and category
 */
export const getStoreImage = (providerId: string, category?: string): string => {
  // If no category provided, use a default image
  if (!category) {
    return STORE_IMAGES.hair_salon_1;
  }

  // Get images for the category
  const categoryImages = CATEGORY_IMAGES[category as keyof typeof CATEGORY_IMAGES];
  if (!categoryImages) {
    return STORE_IMAGES.hair_salon_1;
  }

  // Use provider ID to consistently select an image
  const safeProviderId = providerId || 'default';
  const hash = safeProviderId.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
  const imageIndex = hash % categoryImages.length;
  const imageKey = categoryImages[imageIndex];

  return STORE_IMAGES[imageKey as keyof typeof STORE_IMAGES];
};

/**
 * Get a random store image for a specific category
 */
export const getRandomStoreImage = (category?: string): string => {
  if (!category) {
    const allImages = Object.values(STORE_IMAGES);
    return allImages[Math.floor(Math.random() * allImages.length)];
  }

  const categoryImages = CATEGORY_IMAGES[category as keyof typeof CATEGORY_IMAGES];
  if (!categoryImages) {
    return STORE_IMAGES.hair_salon_1;
  }

  const randomIndex = Math.floor(Math.random() * categoryImages.length);
  const imageKey = categoryImages[randomIndex];
  return STORE_IMAGES[imageKey as keyof typeof STORE_IMAGES];
};

/**
 * Get all available categories
 */
export const getAvailableCategories = (): string[] => {
  return Object.keys(CATEGORY_IMAGES);
};

/**
 * Check if an image URL is valid
 */
export const isValidImageUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

/**
 * Get fallback image for a provider
 */
export const getFallbackImage = (providerName?: string): string => {
  // Use the first letter of the provider name to select a consistent image
  const safeName = providerName || 'Provider';
  const firstLetter = safeName.charAt(0).toUpperCase();
  const charCode = firstLetter.charCodeAt(0);
  const allImages = Object.values(STORE_IMAGES);
  const imageIndex = charCode % allImages.length;

  return allImages[imageIndex];
};
