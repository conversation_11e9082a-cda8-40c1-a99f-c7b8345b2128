{"version": 3, "names": ["_NativeAnimatedHelper", "_interopRequireDefault", "require", "_InteractionManager", "_AnimatedInterpolation", "_AnimatedWithChildren2", "_callSuper", "t", "o", "e", "_getPrototypeOf2", "default", "_possibleConstructorReturn2", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_superPropGet", "r", "p", "_get2", "NativeAnimatedAPI", "NativeAnimatedHelper", "API", "flushValue", "rootNode", "leaves", "Set", "findAnimatedStyles", "node", "update", "add", "__get<PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "leaf", "_executeAsAnimatedBatch", "id", "operation", "setWaitingForIdentifier", "unsetWaitingForIdentifier", "_listenerCount", "_classPrivateFieldLooseKey2", "_updateSubscription", "_ensureUpdateSubscriptionExists", "AnimatedValue", "exports", "_Animated<PERSON>ith<PERSON><PERSON><PERSON><PERSON>", "value", "config", "_this", "_classCallCheck2", "Object", "defineProperty", "_ensureUpdateSubscriptionExists2", "writable", "Error", "_startingValue", "_value", "_offset", "_animation", "useNativeDriver", "__makeNative", "_inherits2", "_createClass2", "key", "__detach", "_this2", "__isNative", "getValue", "__getNativeTag", "stopAnimation", "__getValue", "platformConfig", "_classPrivateFieldLooseBase2", "addListener", "callback", "removeListener", "_classPrivateFieldLoo", "remove", "removeAllListeners", "_classPrivateFieldLoo2", "setValue", "_this3", "stop", "_updateValue", "toString", "setAnimatedNodeValue", "setOffset", "offset", "setAnimatedNodeOffset", "flattenOffset", "flattenAnimatedNodeOffset", "extractOffset", "extractAnimatedNodeOffset", "stopTracking", "resetAnimation", "__onAnimatedValueUpdateReceived", "interpolate", "AnimatedInterpolation", "animate", "animation", "_this4", "handle", "__isInteraction", "InteractionManager", "createInteractionHandle", "previousAnimation", "start", "result", "clearInteractionHandle", "_tracking", "track", "tracking", "flush", "undefined", "__callListeners", "__getNativeConfig", "type", "debugID", "__getDebugID", "AnimatedWithChildren", "_this5", "nativeTag", "startListeningToAnimatedNodeValue", "subscription", "nativeEventEmitter", "data", "tag", "stopListeningToAnimatedNodeValue"], "sources": ["AnimatedValue.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n * @format\n */\n\nimport type {EventSubscription} from '../../vendor/emitter/EventEmitter';\nimport type {PlatformConfig} from '../AnimatedPlatformConfig';\nimport type Animation, {EndCallback} from '../animations/Animation';\nimport type {InterpolationConfigType} from './AnimatedInterpolation';\nimport type AnimatedNode from './AnimatedNode';\nimport type {AnimatedNodeConfig} from './AnimatedNode';\nimport type AnimatedTracking from './AnimatedTracking';\n\nimport NativeAnimatedHelper from '../../../src/private/animated/NativeAnimatedHelper';\nimport InteractionManager from '../../Interaction/InteractionManager';\nimport AnimatedInterpolation from './AnimatedInterpolation';\nimport AnimatedWithChildren from './AnimatedWithChildren';\n\nexport type AnimatedValueConfig = $ReadOnly<{\n  ...AnimatedNodeConfig,\n  useNativeDriver: boolean,\n}>;\n\nconst NativeAnimatedAPI = NativeAnimatedHelper.API;\n\n/**\n * Animated works by building a directed acyclic graph of dependencies\n * transparently when you render your Animated components.\n *\n *               new Animated.Value(0)\n *     .interpolate()        .interpolate()    new Animated.Value(1)\n *         opacity               translateY      scale\n *          style                         transform\n *         View#234                         style\n *                                         View#123\n *\n * A) Top Down phase\n * When an Animated.Value is updated, we recursively go down through this\n * graph in order to find leaf nodes: the views that we flag as needing\n * an update.\n *\n * B) Bottom Up phase\n * When a view is flagged as needing an update, we recursively go back up\n * in order to build the new value that it needs. The reason why we need\n * this two-phases process is to deal with composite props such as\n * transform which can receive values from multiple parents.\n */\nexport function flushValue(rootNode: AnimatedNode): void {\n  // eslint-disable-next-line func-call-spacing\n  const leaves = new Set<{update: () => void, ...}>();\n  function findAnimatedStyles(node: AnimatedNode) {\n    // $FlowFixMe[prop-missing]\n    if (typeof node.update === 'function') {\n      leaves.add((node: any));\n    } else {\n      node.__getChildren().forEach(findAnimatedStyles);\n    }\n  }\n  findAnimatedStyles(rootNode);\n  leaves.forEach(leaf => leaf.update());\n}\n\n/**\n * Some operations are executed only on batch end, which is _mostly_ scheduled when\n * Animated component props change. For some of the changes which require immediate execution\n * (e.g. setValue), we create a separate batch in case none is scheduled.\n */\nfunction _executeAsAnimatedBatch(id: string, operation: () => void) {\n  NativeAnimatedAPI.setWaitingForIdentifier(id);\n  operation();\n  NativeAnimatedAPI.unsetWaitingForIdentifier(id);\n}\n\n/**\n * Standard value for driving animations.  One `Animated.Value` can drive\n * multiple properties in a synchronized fashion, but can only be driven by one\n * mechanism at a time.  Using a new mechanism (e.g. starting a new animation,\n * or calling `setValue`) will stop any previous ones.\n *\n * See https://reactnative.dev/docs/animatedvalue\n */\nexport default class AnimatedValue extends AnimatedWithChildren {\n  #listenerCount: number = 0;\n  #updateSubscription: ?EventSubscription = null;\n\n  _value: number;\n  _startingValue: number;\n  _offset: number;\n  _animation: ?Animation;\n  _tracking: ?AnimatedTracking;\n\n  constructor(value: number, config?: ?AnimatedValueConfig) {\n    super(config);\n    if (typeof value !== 'number') {\n      throw new Error('AnimatedValue: Attempting to set value to undefined');\n    }\n    this._startingValue = this._value = value;\n    this._offset = 0;\n    this._animation = null;\n    if (config && config.useNativeDriver) {\n      this.__makeNative();\n    }\n  }\n\n  __detach() {\n    if (this.__isNative) {\n      NativeAnimatedAPI.getValue(this.__getNativeTag(), value => {\n        this._value = value - this._offset;\n      });\n    }\n    this.stopAnimation();\n    super.__detach();\n  }\n\n  __getValue(): number {\n    return this._value + this._offset;\n  }\n\n  __makeNative(platformConfig: ?PlatformConfig): void {\n    super.__makeNative(platformConfig);\n    if (this.#listenerCount > 0) {\n      this.#ensureUpdateSubscriptionExists();\n    }\n  }\n\n  addListener(callback: (value: any) => mixed): string {\n    const id = super.addListener(callback);\n    this.#listenerCount++;\n    if (this.__isNative) {\n      this.#ensureUpdateSubscriptionExists();\n    }\n    return id;\n  }\n\n  removeListener(id: string): void {\n    super.removeListener(id);\n    this.#listenerCount--;\n    if (this.__isNative && this.#listenerCount === 0) {\n      this.#updateSubscription?.remove();\n    }\n  }\n\n  removeAllListeners(): void {\n    super.removeAllListeners();\n    this.#listenerCount = 0;\n    if (this.__isNative) {\n      this.#updateSubscription?.remove();\n    }\n  }\n\n  #ensureUpdateSubscriptionExists(): void {\n    if (this.#updateSubscription != null) {\n      return;\n    }\n    const nativeTag = this.__getNativeTag();\n    NativeAnimatedAPI.startListeningToAnimatedNodeValue(nativeTag);\n    const subscription: EventSubscription =\n      NativeAnimatedHelper.nativeEventEmitter.addListener(\n        'onAnimatedValueUpdate',\n        data => {\n          if (data.tag === nativeTag) {\n            this.__onAnimatedValueUpdateReceived(data.value);\n          }\n        },\n      );\n\n    this.#updateSubscription = {\n      remove: () => {\n        // Only this function assigns to `this.#updateSubscription`.\n        if (this.#updateSubscription == null) {\n          return;\n        }\n        this.#updateSubscription = null;\n        subscription.remove();\n        NativeAnimatedAPI.stopListeningToAnimatedNodeValue(nativeTag);\n      },\n    };\n  }\n\n  /**\n   * Directly set the value.  This will stop any animations running on the value\n   * and update all the bound properties.\n   *\n   * See https://reactnative.dev/docs/animatedvalue#setvalue\n   */\n  setValue(value: number): void {\n    if (this._animation) {\n      this._animation.stop();\n      this._animation = null;\n    }\n    this._updateValue(\n      value,\n      !this.__isNative /* don't perform a flush for natively driven values */,\n    );\n    if (this.__isNative) {\n      _executeAsAnimatedBatch(this.__getNativeTag().toString(), () =>\n        NativeAnimatedAPI.setAnimatedNodeValue(this.__getNativeTag(), value),\n      );\n    }\n  }\n\n  /**\n   * Sets an offset that is applied on top of whatever value is set, whether via\n   * `setValue`, an animation, or `Animated.event`.  Useful for compensating\n   * things like the start of a pan gesture.\n   *\n   * See https://reactnative.dev/docs/animatedvalue#setoffset\n   */\n  setOffset(offset: number): void {\n    this._offset = offset;\n    if (this.__isNative) {\n      NativeAnimatedAPI.setAnimatedNodeOffset(this.__getNativeTag(), offset);\n    }\n  }\n\n  /**\n   * Merges the offset value into the base value and resets the offset to zero.\n   * The final output of the value is unchanged.\n   *\n   * See https://reactnative.dev/docs/animatedvalue#flattenoffset\n   */\n  flattenOffset(): void {\n    this._value += this._offset;\n    this._offset = 0;\n    if (this.__isNative) {\n      NativeAnimatedAPI.flattenAnimatedNodeOffset(this.__getNativeTag());\n    }\n  }\n\n  /**\n   * Sets the offset value to the base value, and resets the base value to zero.\n   * The final output of the value is unchanged.\n   *\n   * See https://reactnative.dev/docs/animatedvalue#extractoffset\n   */\n  extractOffset(): void {\n    this._offset += this._value;\n    this._value = 0;\n    if (this.__isNative) {\n      NativeAnimatedAPI.extractAnimatedNodeOffset(this.__getNativeTag());\n    }\n  }\n\n  /**\n   * Stops any running animation or tracking. `callback` is invoked with the\n   * final value after stopping the animation, which is useful for updating\n   * state to match the animation position with layout.\n   *\n   * See https://reactnative.dev/docs/animatedvalue#stopanimation\n   */\n  stopAnimation(callback?: ?(value: number) => void): void {\n    this.stopTracking();\n    this._animation && this._animation.stop();\n    this._animation = null;\n    if (callback) {\n      if (this.__isNative) {\n        NativeAnimatedAPI.getValue(this.__getNativeTag(), callback);\n      } else {\n        callback(this.__getValue());\n      }\n    }\n  }\n\n  /**\n   * Stops any animation and resets the value to its original.\n   *\n   * See https://reactnative.dev/docs/animatedvalue#resetanimation\n   */\n  resetAnimation(callback?: ?(value: number) => void): void {\n    this.stopAnimation(callback);\n    this._value = this._startingValue;\n    if (this.__isNative) {\n      NativeAnimatedAPI.setAnimatedNodeValue(\n        this.__getNativeTag(),\n        this._startingValue,\n      );\n    }\n  }\n\n  __onAnimatedValueUpdateReceived(value: number): void {\n    this._updateValue(value, false /*flush*/);\n  }\n\n  /**\n   * Interpolates the value before updating the property, e.g. mapping 0-1 to\n   * 0-10.\n   */\n  interpolate<OutputT: number | string>(\n    config: InterpolationConfigType<OutputT>,\n  ): AnimatedInterpolation<OutputT> {\n    return new AnimatedInterpolation(this, config);\n  }\n\n  /**\n   * Typically only used internally, but could be used by a custom Animation\n   * class.\n   *\n   * See https://reactnative.dev/docs/animatedvalue#animate\n   */\n  animate(animation: Animation, callback: ?EndCallback): void {\n    let handle = null;\n    if (animation.__isInteraction) {\n      handle = InteractionManager.createInteractionHandle();\n    }\n    const previousAnimation = this._animation;\n    this._animation && this._animation.stop();\n    this._animation = animation;\n    animation.start(\n      this._value,\n      value => {\n        // Natively driven animations will never call into that callback, therefore we can always\n        // pass flush = true to allow the updated value to propagate to native with setNativeProps\n        this._updateValue(value, true /* flush */);\n      },\n      result => {\n        this._animation = null;\n        if (handle !== null) {\n          InteractionManager.clearInteractionHandle(handle);\n        }\n        callback && callback(result);\n      },\n      previousAnimation,\n      this,\n    );\n  }\n\n  /**\n   * Typically only used internally.\n   */\n  stopTracking(): void {\n    this._tracking && this._tracking.__detach();\n    this._tracking = null;\n  }\n\n  /**\n   * Typically only used internally.\n   */\n  track(tracking: AnimatedTracking): void {\n    this.stopTracking();\n    this._tracking = tracking;\n    // Make sure that the tracking animation starts executing\n    this._tracking && this._tracking.update();\n  }\n\n  _updateValue(value: number, flush: boolean): void {\n    if (value === undefined) {\n      throw new Error('AnimatedValue: Attempting to set value to undefined');\n    }\n\n    this._value = value;\n    if (flush) {\n      flushValue(this);\n    }\n    this.__callListeners(this.__getValue());\n  }\n\n  __getNativeConfig(): Object {\n    return {\n      type: 'value',\n      value: this._value,\n      offset: this._offset,\n      debugID: this.__getDebugID(),\n    };\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;AAkBA,IAAAA,qBAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,mBAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,sBAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,sBAAA,GAAAJ,sBAAA,CAAAC,OAAA;AAA0D,SAAAI,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAE,gBAAA,CAAAC,OAAA,EAAAH,CAAA,OAAAI,2BAAA,CAAAD,OAAA,EAAAJ,CAAA,EAAAM,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAP,CAAA,EAAAC,CAAA,YAAAC,gBAAA,CAAAC,OAAA,EAAAJ,CAAA,EAAAS,WAAA,IAAAR,CAAA,CAAAS,KAAA,CAAAV,CAAA,EAAAE,CAAA;AAAA,SAAAI,0BAAA,cAAAN,CAAA,IAAAW,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAX,CAAA,aAAAM,yBAAA,YAAAA,0BAAA,aAAAN,CAAA;AAAA,SAAAe,cAAAf,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAc,CAAA,QAAAC,CAAA,OAAAC,KAAA,CAAAd,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAY,CAAA,GAAAhB,CAAA,CAAAY,SAAA,GAAAZ,CAAA,GAAAC,CAAA,EAAAC,CAAA,cAAAc,CAAA,yBAAAC,CAAA,aAAAjB,CAAA,WAAAiB,CAAA,CAAAP,KAAA,CAAAR,CAAA,EAAAF,CAAA,OAAAiB,CAAA;AAO1D,IAAME,iBAAiB,GAAGC,6BAAoB,CAACC,GAAG;AAwB3C,SAASC,UAAUA,CAACC,QAAsB,EAAQ;EAEvD,IAAMC,MAAM,GAAG,IAAIC,GAAG,CAA4B,CAAC;EACnD,SAASC,kBAAkBA,CAACC,IAAkB,EAAE;IAE9C,IAAI,OAAOA,IAAI,CAACC,MAAM,KAAK,UAAU,EAAE;MACrCJ,MAAM,CAACK,GAAG,CAAEF,IAAU,CAAC;IACzB,CAAC,MAAM;MACLA,IAAI,CAACG,aAAa,CAAC,CAAC,CAACC,OAAO,CAACL,kBAAkB,CAAC;IAClD;EACF;EACAA,kBAAkB,CAACH,QAAQ,CAAC;EAC5BC,MAAM,CAACO,OAAO,CAAC,UAAAC,IAAI;IAAA,OAAIA,IAAI,CAACJ,MAAM,CAAC,CAAC;EAAA,EAAC;AACvC;AAOA,SAASK,uBAAuBA,CAACC,EAAU,EAAEC,SAAqB,EAAE;EAClEhB,iBAAiB,CAACiB,uBAAuB,CAACF,EAAE,CAAC;EAC7CC,SAAS,CAAC,CAAC;EACXhB,iBAAiB,CAACkB,yBAAyB,CAACH,EAAE,CAAC;AACjD;AAAC,IAAAI,cAAA,OAAAC,2BAAA,CAAAnC,OAAA;AAAA,IAAAoC,mBAAA,OAAAD,2BAAA,CAAAnC,OAAA;AAAA,IAAAqC,+BAAA,OAAAF,2BAAA,CAAAnC,OAAA;AAAA,IAUoBsC,aAAa,GAAAC,OAAA,CAAAvC,OAAA,aAAAwC,qBAAA;EAUhC,SAAAF,cAAYG,KAAa,EAAEC,MAA6B,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAA5C,OAAA,QAAAsC,aAAA;IACxDK,KAAA,GAAAhD,UAAA,OAAA2C,aAAA,GAAMI,MAAM;IAAEG,MAAA,CAAAC,cAAA,CAAAH,KAAA,EAAAN,+BAAA;MAAAI,KAAA,EAAAM;IAAA;IAAAF,MAAA,CAAAC,cAAA,CAAAH,KAAA,EAAAT,cAAA;MAAAc,QAAA;MAAAP,KAAA,EAVS;IAAC;IAAAI,MAAA,CAAAC,cAAA,CAAAH,KAAA,EAAAP,mBAAA;MAAAY,QAAA;MAAAP,KAAA,EACgB;IAAI;IAU5C,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7B,MAAM,IAAIQ,KAAK,CAAC,qDAAqD,CAAC;IACxE;IACAN,KAAA,CAAKO,cAAc,GAAGP,KAAA,CAAKQ,MAAM,GAAGV,KAAK;IACzCE,KAAA,CAAKS,OAAO,GAAG,CAAC;IAChBT,KAAA,CAAKU,UAAU,GAAG,IAAI;IACtB,IAAIX,MAAM,IAAIA,MAAM,CAACY,eAAe,EAAE;MACpCX,KAAA,CAAKY,YAAY,CAAC,CAAC;IACrB;IAAC,OAAAZ,KAAA;EACH;EAAC,IAAAa,UAAA,CAAAxD,OAAA,EAAAsC,aAAA,EAAAE,qBAAA;EAAA,WAAAiB,aAAA,CAAAzD,OAAA,EAAAsC,aAAA;IAAAoB,GAAA;IAAAjB,KAAA,EAED,SAAAkB,QAAQA,CAAA,EAAG;MAAA,IAAAC,MAAA;MACT,IAAI,IAAI,CAACC,UAAU,EAAE;QACnB9C,iBAAiB,CAAC+C,QAAQ,CAAC,IAAI,CAACC,cAAc,CAAC,CAAC,EAAE,UAAAtB,KAAK,EAAI;UACzDmB,MAAI,CAACT,MAAM,GAAGV,KAAK,GAAGmB,MAAI,CAACR,OAAO;QACpC,CAAC,CAAC;MACJ;MACA,IAAI,CAACY,aAAa,CAAC,CAAC;MACpBrD,aAAA,CAAA2B,aAAA;IACF;EAAC;IAAAoB,GAAA;IAAAjB,KAAA,EAED,SAAAwB,UAAUA,CAAA,EAAW;MACnB,OAAO,IAAI,CAACd,MAAM,GAAG,IAAI,CAACC,OAAO;IACnC;EAAC;IAAAM,GAAA;IAAAjB,KAAA,EAED,SAAAc,YAAYA,CAACW,cAA+B,EAAQ;MAClDvD,aAAA,CAAA2B,aAAA,4BAAmB4B,cAAc;MACjC,IAAI,IAAAC,4BAAA,CAAAnE,OAAA,MAAI,EAAAkC,cAAA,EAAAA,cAAA,IAAkB,CAAC,EAAE;QAC3B,IAAAiC,4BAAA,CAAAnE,OAAA,MAAI,EAAAqC,+BAAA,EAAAA,+BAAA;MACN;IACF;EAAC;IAAAqB,GAAA;IAAAjB,KAAA,EAED,SAAA2B,WAAWA,CAACC,QAA+B,EAAU;MACnD,IAAMvC,EAAE,GAAAnB,aAAA,CAAA2B,aAAA,2BAAqB+B,QAAQ,EAAC;MACtC,IAAAF,4BAAA,CAAAnE,OAAA,MAAI,EAAAkC,cAAA,EAAAA,cAAA,GAAiB;MACrB,IAAI,IAAI,CAAC2B,UAAU,EAAE;QACnB,IAAAM,4BAAA,CAAAnE,OAAA,MAAI,EAAAqC,+BAAA,EAAAA,+BAAA;MACN;MACA,OAAOP,EAAE;IACX;EAAC;IAAA4B,GAAA;IAAAjB,KAAA,EAED,SAAA6B,cAAcA,CAACxC,EAAU,EAAQ;MAC/BnB,aAAA,CAAA2B,aAAA,8BAAqBR,EAAE;MACvB,IAAAqC,4BAAA,CAAAnE,OAAA,MAAI,EAAAkC,cAAA,EAAAA,cAAA,GAAiB;MACrB,IAAI,IAAI,CAAC2B,UAAU,IAAI,IAAAM,4BAAA,CAAAnE,OAAA,MAAI,EAAAkC,cAAA,EAAAA,cAAA,MAAoB,CAAC,EAAE;QAAA,IAAAqC,qBAAA;QAChD,CAAAA,qBAAA,OAAAJ,4BAAA,CAAAnE,OAAA,MAAI,EAAAoC,mBAAA,EAAAA,mBAAA,cAAJmC,qBAAA,CAA0BC,MAAM,CAAC,CAAC;MACpC;IACF;EAAC;IAAAd,GAAA;IAAAjB,KAAA,EAED,SAAAgC,kBAAkBA,CAAA,EAAS;MACzB9D,aAAA,CAAA2B,aAAA;MACA,IAAA6B,4BAAA,CAAAnE,OAAA,MAAI,EAAAkC,cAAA,EAAAA,cAAA,IAAkB,CAAC;MACvB,IAAI,IAAI,CAAC2B,UAAU,EAAE;QAAA,IAAAa,sBAAA;QACnB,CAAAA,sBAAA,OAAAP,4BAAA,CAAAnE,OAAA,MAAI,EAAAoC,mBAAA,EAAAA,mBAAA,cAAJsC,sBAAA,CAA0BF,MAAM,CAAC,CAAC;MACpC;IACF;EAAC;IAAAd,GAAA;IAAAjB,KAAA,EAqCD,SAAAkC,QAAQA,CAAClC,KAAa,EAAQ;MAAA,IAAAmC,MAAA;MAC5B,IAAI,IAAI,CAACvB,UAAU,EAAE;QACnB,IAAI,CAACA,UAAU,CAACwB,IAAI,CAAC,CAAC;QACtB,IAAI,CAACxB,UAAU,GAAG,IAAI;MACxB;MACA,IAAI,CAACyB,YAAY,CACfrC,KAAK,EACL,CAAC,IAAI,CAACoB,UACR,CAAC;MACD,IAAI,IAAI,CAACA,UAAU,EAAE;QACnBhC,uBAAuB,CAAC,IAAI,CAACkC,cAAc,CAAC,CAAC,CAACgB,QAAQ,CAAC,CAAC,EAAE;UAAA,OACxDhE,iBAAiB,CAACiE,oBAAoB,CAACJ,MAAI,CAACb,cAAc,CAAC,CAAC,EAAEtB,KAAK,CAAC;QAAA,CACtE,CAAC;MACH;IACF;EAAC;IAAAiB,GAAA;IAAAjB,KAAA,EASD,SAAAwC,SAASA,CAACC,MAAc,EAAQ;MAC9B,IAAI,CAAC9B,OAAO,GAAG8B,MAAM;MACrB,IAAI,IAAI,CAACrB,UAAU,EAAE;QACnB9C,iBAAiB,CAACoE,qBAAqB,CAAC,IAAI,CAACpB,cAAc,CAAC,CAAC,EAAEmB,MAAM,CAAC;MACxE;IACF;EAAC;IAAAxB,GAAA;IAAAjB,KAAA,EAQD,SAAA2C,aAAaA,CAAA,EAAS;MACpB,IAAI,CAACjC,MAAM,IAAI,IAAI,CAACC,OAAO;MAC3B,IAAI,CAACA,OAAO,GAAG,CAAC;MAChB,IAAI,IAAI,CAACS,UAAU,EAAE;QACnB9C,iBAAiB,CAACsE,yBAAyB,CAAC,IAAI,CAACtB,cAAc,CAAC,CAAC,CAAC;MACpE;IACF;EAAC;IAAAL,GAAA;IAAAjB,KAAA,EAQD,SAAA6C,aAAaA,CAAA,EAAS;MACpB,IAAI,CAAClC,OAAO,IAAI,IAAI,CAACD,MAAM;MAC3B,IAAI,CAACA,MAAM,GAAG,CAAC;MACf,IAAI,IAAI,CAACU,UAAU,EAAE;QACnB9C,iBAAiB,CAACwE,yBAAyB,CAAC,IAAI,CAACxB,cAAc,CAAC,CAAC,CAAC;MACpE;IACF;EAAC;IAAAL,GAAA;IAAAjB,KAAA,EASD,SAAAuB,aAAaA,CAACK,QAAmC,EAAQ;MACvD,IAAI,CAACmB,YAAY,CAAC,CAAC;MACnB,IAAI,CAACnC,UAAU,IAAI,IAAI,CAACA,UAAU,CAACwB,IAAI,CAAC,CAAC;MACzC,IAAI,CAACxB,UAAU,GAAG,IAAI;MACtB,IAAIgB,QAAQ,EAAE;QACZ,IAAI,IAAI,CAACR,UAAU,EAAE;UACnB9C,iBAAiB,CAAC+C,QAAQ,CAAC,IAAI,CAACC,cAAc,CAAC,CAAC,EAAEM,QAAQ,CAAC;QAC7D,CAAC,MAAM;UACLA,QAAQ,CAAC,IAAI,CAACJ,UAAU,CAAC,CAAC,CAAC;QAC7B;MACF;IACF;EAAC;IAAAP,GAAA;IAAAjB,KAAA,EAOD,SAAAgD,cAAcA,CAACpB,QAAmC,EAAQ;MACxD,IAAI,CAACL,aAAa,CAACK,QAAQ,CAAC;MAC5B,IAAI,CAAClB,MAAM,GAAG,IAAI,CAACD,cAAc;MACjC,IAAI,IAAI,CAACW,UAAU,EAAE;QACnB9C,iBAAiB,CAACiE,oBAAoB,CACpC,IAAI,CAACjB,cAAc,CAAC,CAAC,EACrB,IAAI,CAACb,cACP,CAAC;MACH;IACF;EAAC;IAAAQ,GAAA;IAAAjB,KAAA,EAED,SAAAiD,+BAA+BA,CAACjD,KAAa,EAAQ;MACnD,IAAI,CAACqC,YAAY,CAACrC,KAAK,EAAE,KAAe,CAAC;IAC3C;EAAC;IAAAiB,GAAA;IAAAjB,KAAA,EAMD,SAAAkD,WAAWA,CACTjD,MAAwC,EACR;MAChC,OAAO,IAAIkD,8BAAqB,CAAC,IAAI,EAAElD,MAAM,CAAC;IAChD;EAAC;IAAAgB,GAAA;IAAAjB,KAAA,EAQD,SAAAoD,OAAOA,CAACC,SAAoB,EAAEzB,QAAsB,EAAQ;MAAA,IAAA0B,MAAA;MAC1D,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAIF,SAAS,CAACG,eAAe,EAAE;QAC7BD,MAAM,GAAGE,2BAAkB,CAACC,uBAAuB,CAAC,CAAC;MACvD;MACA,IAAMC,iBAAiB,GAAG,IAAI,CAAC/C,UAAU;MACzC,IAAI,CAACA,UAAU,IAAI,IAAI,CAACA,UAAU,CAACwB,IAAI,CAAC,CAAC;MACzC,IAAI,CAACxB,UAAU,GAAGyC,SAAS;MAC3BA,SAAS,CAACO,KAAK,CACb,IAAI,CAAClD,MAAM,EACX,UAAAV,KAAK,EAAI;QAGPsD,MAAI,CAACjB,YAAY,CAACrC,KAAK,EAAE,IAAgB,CAAC;MAC5C,CAAC,EACD,UAAA6D,MAAM,EAAI;QACRP,MAAI,CAAC1C,UAAU,GAAG,IAAI;QACtB,IAAI2C,MAAM,KAAK,IAAI,EAAE;UACnBE,2BAAkB,CAACK,sBAAsB,CAACP,MAAM,CAAC;QACnD;QACA3B,QAAQ,IAAIA,QAAQ,CAACiC,MAAM,CAAC;MAC9B,CAAC,EACDF,iBAAiB,EACjB,IACF,CAAC;IACH;EAAC;IAAA1C,GAAA;IAAAjB,KAAA,EAKD,SAAA+C,YAAYA,CAAA,EAAS;MACnB,IAAI,CAACgB,SAAS,IAAI,IAAI,CAACA,SAAS,CAAC7C,QAAQ,CAAC,CAAC;MAC3C,IAAI,CAAC6C,SAAS,GAAG,IAAI;IACvB;EAAC;IAAA9C,GAAA;IAAAjB,KAAA,EAKD,SAAAgE,KAAKA,CAACC,QAA0B,EAAQ;MACtC,IAAI,CAAClB,YAAY,CAAC,CAAC;MACnB,IAAI,CAACgB,SAAS,GAAGE,QAAQ;MAEzB,IAAI,CAACF,SAAS,IAAI,IAAI,CAACA,SAAS,CAAChF,MAAM,CAAC,CAAC;IAC3C;EAAC;IAAAkC,GAAA;IAAAjB,KAAA,EAED,SAAAqC,YAAYA,CAACrC,KAAa,EAAEkE,KAAc,EAAQ;MAChD,IAAIlE,KAAK,KAAKmE,SAAS,EAAE;QACvB,MAAM,IAAI3D,KAAK,CAAC,qDAAqD,CAAC;MACxE;MAEA,IAAI,CAACE,MAAM,GAAGV,KAAK;MACnB,IAAIkE,KAAK,EAAE;QACTzF,UAAU,CAAC,IAAI,CAAC;MAClB;MACA,IAAI,CAAC2F,eAAe,CAAC,IAAI,CAAC5C,UAAU,CAAC,CAAC,CAAC;IACzC;EAAC;IAAAP,GAAA;IAAAjB,KAAA,EAED,SAAAqE,iBAAiBA,CAAA,EAAW;MAC1B,OAAO;QACLC,IAAI,EAAE,OAAO;QACbtE,KAAK,EAAE,IAAI,CAACU,MAAM;QAClB+B,MAAM,EAAE,IAAI,CAAC9B,OAAO;QACpB4D,OAAO,EAAE,IAAI,CAACC,YAAY,CAAC;MAC7B,CAAC;IACH;EAAC;AAAA,EA1RwCC,8BAAoB;AAAA,SAAAnE,iCAAA,EAqErB;EAAA,IAAAoE,MAAA;EACtC,IAAI,IAAAhD,4BAAA,CAAAnE,OAAA,MAAI,EAAAoC,mBAAA,EAAAA,mBAAA,KAAwB,IAAI,EAAE;IACpC;EACF;EACA,IAAMgF,SAAS,GAAG,IAAI,CAACrD,cAAc,CAAC,CAAC;EACvChD,iBAAiB,CAACsG,iCAAiC,CAACD,SAAS,CAAC;EAC9D,IAAME,YAA+B,GACnCtG,6BAAoB,CAACuG,kBAAkB,CAACnD,WAAW,CACjD,uBAAuB,EACvB,UAAAoD,IAAI,EAAI;IACN,IAAIA,IAAI,CAACC,GAAG,KAAKL,SAAS,EAAE;MAC1BD,MAAI,CAACzB,+BAA+B,CAAC8B,IAAI,CAAC/E,KAAK,CAAC;IAClD;EACF,CACF,CAAC;EAEH,IAAA0B,4BAAA,CAAAnE,OAAA,MAAI,EAAAoC,mBAAA,EAAAA,mBAAA,IAAuB;IACzBoC,MAAM,EAAE,SAARA,MAAMA,CAAA,EAAQ;MAEZ,IAAI,IAAAL,4BAAA,CAAAnE,OAAA,EAAAmH,MAAI,EAAA/E,mBAAA,EAAAA,mBAAA,KAAwB,IAAI,EAAE;QACpC;MACF;MACA,IAAA+B,4BAAA,CAAAnE,OAAA,EAAAmH,MAAI,EAAA/E,mBAAA,EAAAA,mBAAA,IAAuB,IAAI;MAC/BkF,YAAY,CAAC9C,MAAM,CAAC,CAAC;MACrBzD,iBAAiB,CAAC2G,gCAAgC,CAACN,SAAS,CAAC;IAC/D;EACF,CAAC;AACH", "ignoreList": []}