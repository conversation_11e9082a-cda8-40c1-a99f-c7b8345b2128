36ca6aef8405aa33955aeb8bae098b3a
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useHighContrastColors = exports.useHighContrast = exports.useAccessibleColor = exports.default = exports.HighContrastProvider = exports.HIGH_CONTRAST_COLORS = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _react = _interopRequireWildcard(require("react"));
var _reactNative = require("react-native");
var _asyncStorage = _interopRequireDefault(require("@react-native-async-storage/async-storage"));
var _Colors = require("../constants/Colors");
var _colorContrastUtils = require("../utils/colorContrastUtils");
var _jsxRuntime = require("react/jsx-runtime");
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var HIGH_CONTRAST_COLORS = exports.HIGH_CONTRAST_COLORS = {
  light: {
    background: {
      primary: '#FFFFFF',
      secondary: '#F0F0F0',
      elevated: '#FFFFFF',
      overlay: 'rgba(0, 0, 0, 0.8)'
    },
    text: {
      primary: '#000000',
      secondary: '#000000',
      tertiary: '#000000',
      inverse: '#FFFFFF',
      disabled: '#666666'
    },
    primary: {
      default: '#000000',
      light: '#333333',
      dark: '#000000',
      text: '#FFFFFF'
    },
    interactive: {
      primary: {
        default: '#000000',
        hover: '#333333',
        pressed: '#666666',
        disabled: '#CCCCCC',
        text: '#FFFFFF',
        textDisabled: '#999999'
      },
      secondary: {
        default: '#FFFFFF',
        hover: '#F0F0F0',
        pressed: '#E0E0E0',
        disabled: '#F8F8F8',
        border: '#000000',
        borderDisabled: '#CCCCCC',
        text: '#000000',
        textDisabled: '#999999'
      }
    },
    status: {
      success: '#000000',
      warning: '#000000',
      error: '#000000',
      info: '#000000'
    },
    border: {
      primary: '#000000',
      secondary: '#666666',
      tertiary: '#CCCCCC'
    }
  },
  dark: {
    background: {
      primary: '#000000',
      secondary: '#1A1A1A',
      elevated: '#000000',
      overlay: 'rgba(255, 255, 255, 0.8)'
    },
    text: {
      primary: '#FFFFFF',
      secondary: '#FFFFFF',
      tertiary: '#FFFFFF',
      inverse: '#000000',
      disabled: '#999999'
    },
    primary: {
      default: '#FFFFFF',
      light: '#CCCCCC',
      dark: '#FFFFFF',
      text: '#000000'
    },
    interactive: {
      primary: {
        default: '#FFFFFF',
        hover: '#CCCCCC',
        pressed: '#999999',
        disabled: '#333333',
        text: '#000000',
        textDisabled: '#666666'
      },
      secondary: {
        default: '#000000',
        hover: '#1A1A1A',
        pressed: '#333333',
        disabled: '#0A0A0A',
        border: '#FFFFFF',
        borderDisabled: '#333333',
        text: '#FFFFFF',
        textDisabled: '#666666'
      }
    },
    status: {
      success: '#FFFFFF',
      warning: '#FFFFFF',
      error: '#FFFFFF',
      info: '#FFFFFF'
    },
    border: {
      primary: '#FFFFFF',
      secondary: '#999999',
      tertiary: '#333333'
    }
  }
};
var HighContrastContext = (0, _react.createContext)(undefined);
var HIGH_CONTRAST_STORAGE_KEY = '@vierla_high_contrast_mode';
var HIGH_CONTRAST_SCHEME_STORAGE_KEY = '@vierla_high_contrast_scheme';
var HighContrastProvider = exports.HighContrastProvider = function HighContrastProvider(_ref) {
  var children = _ref.children;
  var _useState = (0, _react.useState)(false),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    isHighContrastEnabled = _useState2[0],
    setIsHighContrastEnabled = _useState2[1];
  var _useState3 = (0, _react.useState)('light'),
    _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
    highContrastScheme = _useState4[0],
    setHighContrastSchemeState = _useState4[1];
  var _useState5 = (0, _react.useState)('light'),
    _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
    systemColorScheme = _useState6[0],
    setSystemColorScheme = _useState6[1];
  (0, _react.useEffect)(function () {
    var loadPreferences = function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        try {
          var savedHighContrast = yield _asyncStorage.default.getItem(HIGH_CONTRAST_STORAGE_KEY);
          if (savedHighContrast !== null) {
            setIsHighContrastEnabled(JSON.parse(savedHighContrast));
          } else {
            if (_reactNative.Platform.OS === 'ios' || _reactNative.Platform.OS === 'android') {
              var isReduceTransparencyEnabled = yield _reactNative.AccessibilityInfo.isReduceTransparencyEnabled == null ? void 0 : _reactNative.AccessibilityInfo.isReduceTransparencyEnabled();
              if (isReduceTransparencyEnabled) {
                setIsHighContrastEnabled(true);
              }
            }
          }
          var savedScheme = yield _asyncStorage.default.getItem(HIGH_CONTRAST_SCHEME_STORAGE_KEY);
          if (savedScheme) {
            setHighContrastSchemeState(savedScheme);
          } else {
            var systemScheme = _reactNative.Appearance.getColorScheme() || 'light';
            setSystemColorScheme(systemScheme);
            setHighContrastSchemeState(systemScheme);
          }
        } catch (error) {
          console.warn('Failed to load high contrast preferences:', error);
        }
      });
      return function loadPreferences() {
        return _ref2.apply(this, arguments);
      };
    }();
    loadPreferences();
  }, []);
  (0, _react.useEffect)(function () {
    var subscription = _reactNative.Appearance.addChangeListener(function (_ref3) {
      var colorScheme = _ref3.colorScheme;
      var scheme = colorScheme || 'light';
      setSystemColorScheme(scheme);
      _asyncStorage.default.getItem(HIGH_CONTRAST_SCHEME_STORAGE_KEY).then(function (savedScheme) {
        if (!savedScheme) {
          setHighContrastSchemeState(scheme);
        }
      });
    });
    return function () {
      return subscription == null ? void 0 : subscription.remove();
    };
  }, []);
  var saveHighContrastPreference = (0, _react.useCallback)(function () {
    var _ref4 = (0, _asyncToGenerator2.default)(function* (enabled) {
      try {
        yield _asyncStorage.default.setItem(HIGH_CONTRAST_STORAGE_KEY, JSON.stringify(enabled));
      } catch (error) {
        console.warn('Failed to save high contrast preference:', error);
      }
    });
    return function (_x) {
      return _ref4.apply(this, arguments);
    };
  }(), []);
  var saveSchemePreference = (0, _react.useCallback)(function () {
    var _ref5 = (0, _asyncToGenerator2.default)(function* (scheme) {
      try {
        yield _asyncStorage.default.setItem(HIGH_CONTRAST_SCHEME_STORAGE_KEY, scheme);
      } catch (error) {
        console.warn('Failed to save color scheme preference:', error);
      }
    });
    return function (_x2) {
      return _ref5.apply(this, arguments);
    };
  }(), []);
  var toggleHighContrast = (0, _react.useCallback)(function () {
    var newValue = !isHighContrastEnabled;
    setIsHighContrastEnabled(newValue);
    saveHighContrastPreference(newValue);
    if (_reactNative.Platform.OS === 'ios' || _reactNative.Platform.OS === 'android') {
      _reactNative.AccessibilityInfo.announceForAccessibility(newValue ? 'High contrast mode enabled' : 'High contrast mode disabled');
    }
  }, [isHighContrastEnabled, saveHighContrastPreference]);
  var setHighContrastScheme = (0, _react.useCallback)(function (scheme) {
    setHighContrastSchemeState(scheme);
    saveSchemePreference(scheme);
    if (_reactNative.Platform.OS === 'ios' || _reactNative.Platform.OS === 'android') {
      _reactNative.AccessibilityInfo.announceForAccessibility(`High contrast ${scheme} mode enabled`);
    }
  }, [saveSchemePreference]);
  var enableHighContrast = (0, _react.useCallback)(function () {
    setIsHighContrastEnabled(true);
    saveHighContrastPreference(true);
  }, [saveHighContrastPreference]);
  var disableHighContrast = (0, _react.useCallback)(function () {
    setIsHighContrastEnabled(false);
    saveHighContrastPreference(false);
  }, [saveHighContrastPreference]);
  var colors = _react.default.useMemo(function () {
    if (isHighContrastEnabled) {
      return HIGH_CONTRAST_COLORS[highContrastScheme];
    }
    return _Colors.Colors;
  }, [isHighContrastEnabled, highContrastScheme]);
  var getHighContrastColorUtil = (0, _react.useCallback)(function (color) {
    var background = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '#FFFFFF';
    if (isHighContrastEnabled) {
      return (0, _colorContrastUtils.getHighContrastColor)(color, background);
    }
    return color;
  }, [isHighContrastEnabled]);
  var adjustColorForAccessibility = (0, _react.useCallback)(function (color, background) {
    if (isHighContrastEnabled) {
      return (0, _colorContrastUtils.adjustColorForContrast)(color, background, 7.0);
    }
    return (0, _colorContrastUtils.adjustColorForContrast)(color, background, 4.5);
  }, [isHighContrastEnabled]);
  var contextValue = {
    isHighContrastEnabled: isHighContrastEnabled,
    highContrastScheme: highContrastScheme,
    colors: colors,
    toggleHighContrast: toggleHighContrast,
    setHighContrastScheme: setHighContrastScheme,
    enableHighContrast: enableHighContrast,
    disableHighContrast: disableHighContrast,
    getHighContrastColor: getHighContrastColorUtil,
    adjustColorForAccessibility: adjustColorForAccessibility
  };
  return (0, _jsxRuntime.jsx)(HighContrastContext.Provider, {
    value: contextValue,
    children: children
  });
};
var useHighContrast = exports.useHighContrast = function useHighContrast() {
  var context = (0, _react.useContext)(HighContrastContext);
  if (context === undefined) {
    console.warn('useHighContrast used outside HighContrastProvider, using fallback values');
    return {
      isHighContrastEnabled: false,
      highContrastScheme: 'light',
      colors: {
        background: '#FFFFFF',
        surface: '#F5F5F5',
        primary: '#000000',
        secondary: '#666666',
        accent: '#007AFF',
        text: {
          primary: '#000000',
          secondary: '#666666',
          tertiary: '#999999',
          inverse: '#FFFFFF'
        },
        border: '#E0E0E0',
        error: '#FF3B30',
        warning: '#FF9500',
        success: '#34C759'
      },
      toggleHighContrast: function toggleHighContrast() {},
      setHighContrastScheme: function setHighContrastScheme() {},
      enableHighContrast: function enableHighContrast() {},
      disableHighContrast: function disableHighContrast() {},
      getHighContrastColor: function getHighContrastColor(color) {
        return color;
      },
      adjustColorForAccessibility: function adjustColorForAccessibility(color) {
        return color;
      }
    };
  }
  return context;
};
var useHighContrastColors = exports.useHighContrastColors = function useHighContrastColors() {
  var _useHighContrast = useHighContrast(),
    colors = _useHighContrast.colors,
    isHighContrastEnabled = _useHighContrast.isHighContrastEnabled;
  return {
    colors: colors,
    isHighContrastEnabled: isHighContrastEnabled
  };
};
var useAccessibleColor = exports.useAccessibleColor = function useAccessibleColor(color, background) {
  var _useHighContrast2 = useHighContrast(),
    getHighContrastColor = _useHighContrast2.getHighContrastColor,
    adjustColorForAccessibility = _useHighContrast2.adjustColorForAccessibility;
  return _react.default.useMemo(function () {
    if (background) {
      return adjustColorForAccessibility(color, background);
    }
    return getHighContrastColor(color);
  }, [color, background, getHighContrastColor, adjustColorForAccessibility]);
};
var _default = exports.default = HighContrastProvider;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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