/**
 * Error Handler Hook
 * Provides comprehensive error handling capabilities for React components
 */

import { useCallback, useRef } from 'react';
import { Alert } from 'react-native';
import * as Haptics from 'expo-haptics';
import { errorHandler, ErrorType, ErrorSeverity, AppError } from '../utils/errorHandler';

interface UseErrorHandlerOptions {
  enableHaptics?: boolean;
  enableUserNotification?: boolean;
  context?: Record<string, any>;
}

interface ErrorHandlerMethods {
  handleError: (error: Error | AppError, additionalContext?: Record<string, any>) => AppError;
  handleNetworkError: (error: Error, additionalContext?: Record<string, any>) => AppError;
  handleValidationError: (message: string, field?: string, additionalContext?: Record<string, any>) => AppError;
  handleAuthError: (error: Error, additionalContext?: Record<string, any>) => AppError;
  handleAsyncError: <T>(
    asyncFn: () => Promise<T>,
    errorContext?: Record<string, any>
  ) => Promise<T | null>;
  showErrorAlert: (title: string, message: string, actions?: Array<{ text: string; onPress?: () => void }>) => void;
  clearErrors: () => void;
}

export const useErrorHandler = (options: UseErrorHandlerOptions = {}): ErrorHandlerMethods => {
  const {
    enableHaptics = true,
    enableUserNotification = true,
    context = {},
  } = options;

  const componentContext = useRef(context);

  // Update context when it changes
  componentContext.current = { ...componentContext.current, ...context };

  const handleError = useCallback((
    error: Error | AppError,
    additionalContext?: Record<string, any>
  ): AppError => {
    const fullContext = {
      ...componentContext.current,
      ...additionalContext,
    };

    return errorHandler.handleError(error, fullContext);
  }, []);

  const handleNetworkError = useCallback((
    error: Error,
    additionalContext?: Record<string, any>
  ): AppError => {
    const fullContext = {
      ...componentContext.current,
      ...additionalContext,
      errorType: 'network',
    };

    return errorHandler.handleNetworkError(error, fullContext);
  }, []);

  const handleValidationError = useCallback((
    message: string,
    field?: string,
    additionalContext?: Record<string, any>
  ): AppError => {
    const fullContext = {
      ...componentContext.current,
      ...additionalContext,
      field,
      errorType: 'validation',
    };

    return errorHandler.handleValidationError(message, field, fullContext);
  }, []);

  const handleAuthError = useCallback((
    error: Error,
    additionalContext?: Record<string, any>
  ): AppError => {
    const fullContext = {
      ...componentContext.current,
      ...additionalContext,
      errorType: 'authentication',
    };

    return errorHandler.handleAuthError(error, fullContext);
  }, []);

  const handleAsyncError = useCallback(async <T>(
    asyncFn: () => Promise<T>,
    errorContext?: Record<string, any>
  ): Promise<T | null> => {
    try {
      return await asyncFn();
    } catch (error) {
      const fullContext = {
        ...componentContext.current,
        ...errorContext,
        asyncOperation: true,
      };

      if (error instanceof Error) {
        handleError(error, fullContext);
      } else {
        handleError(new Error(String(error)), fullContext);
      }

      return null;
    }
  }, [handleError]);

  const showErrorAlert = useCallback((
    title: string,
    message: string,
    actions: Array<{ text: string; onPress?: () => void }> = [{ text: 'OK' }]
  ) => {
    if (enableHaptics) {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    }

    Alert.alert(title, message, actions);
  }, [enableHaptics]);

  const clearErrors = useCallback(() => {
    errorHandler.clearErrors();
  }, []);

  return {
    handleError,
    handleNetworkError,
    handleValidationError,
    handleAuthError,
    handleAsyncError,
    showErrorAlert,
    clearErrors,
  };
};

/**
 * Hook for handling form validation errors
 */
export const useFormErrorHandler = () => {
  const { handleValidationError, showErrorAlert } = useErrorHandler({
    context: { component: 'form' },
  });

  const validateField = useCallback((
    value: any,
    rules: Array<(value: any) => string | null>,
    fieldName: string
  ): string | null => {
    for (const rule of rules) {
      const error = rule(value);
      if (error) {
        handleValidationError(error, fieldName, { validation: true });
        return error;
      }
    }
    return null;
  }, [handleValidationError]);

  const validateForm = useCallback((
    formData: Record<string, any>,
    validationRules: Record<string, Array<(value: any) => string | null>>
  ): Record<string, string> => {
    const errors: Record<string, string> = {};

    Object.entries(validationRules).forEach(([fieldName, rules]) => {
      const value = formData[fieldName];
      const error = validateField(value, rules, fieldName);
      if (error) {
        errors[fieldName] = error;
      }
    });

    return errors;
  }, [validateField]);

  return {
    validateField,
    validateForm,
    showErrorAlert,
  };
};

/**
 * Hook for handling API errors
 */
export const useApiErrorHandler = () => {
  const { handleNetworkError, handleAuthError, handleError } = useErrorHandler({
    context: { component: 'api' },
  });

  const handleApiError = useCallback((error: any, endpoint?: string) => {
    const context = { endpoint, apiCall: true };

    if (error?.response?.status === 401) {
      return handleAuthError(new Error('Unauthorized'), context);
    }

    if (error?.response?.status >= 500) {
      return handleError(new Error('Server error'), context);
    }

    if (error?.code === 'NETWORK_ERROR' || !error?.response) {
      return handleNetworkError(new Error('Network error'), context);
    }

    return handleError(error instanceof Error ? error : new Error(String(error)), context);
  }, [handleNetworkError, handleAuthError, handleError]);

  return { handleApiError };
};

/**
 * Validation rules for common use cases
 */
export const validationRules = {
  required: (value: any) => {
    if (!value || (typeof value === 'string' && value.trim() === '')) {
      return 'This field is required';
    }
    return null;
  },

  email: (value: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (value && !emailRegex.test(value)) {
      return 'Please enter a valid email address';
    }
    return null;
  },

  minLength: (min: number) => (value: string) => {
    if (value && value.length < min) {
      return `Must be at least ${min} characters long`;
    }
    return null;
  },

  maxLength: (max: number) => (value: string) => {
    if (value && value.length > max) {
      return `Must be no more than ${max} characters long`;
    }
    return null;
  },

  phone: (value: string) => {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    if (value && !phoneRegex.test(value.replace(/[\s\-\(\)]/g, ''))) {
      return 'Please enter a valid phone number';
    }
    return null;
  },

  password: (value: string) => {
    if (value && value.length < 8) {
      return 'Password must be at least 8 characters long';
    }
    if (value && !/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(value)) {
      return 'Password must contain at least one uppercase letter, one lowercase letter, and one number';
    }
    return null;
  },
};
