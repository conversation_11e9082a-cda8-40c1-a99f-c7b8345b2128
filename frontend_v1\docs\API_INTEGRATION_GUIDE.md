# API Integration Guide

## Overview

This guide provides comprehensive documentation for integrating with the Vierla backend APIs. It covers authentication, service endpoints, real-time features, and best practices for frontend-backend communication.

## Authentication

### JWT Token Authentication

All API requests require authentication using JWT tokens.

**Headers Required**:
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Token Management**:
```typescript
import { useAuthStore } from '../store/authSlice';

const { authToken, refreshToken } = useAuthStore();

// Add token to API requests
const apiClient = axios.create({
  baseURL: process.env.API_BASE_URL,
  headers: {
    'Authorization': `Bearer ${authToken}`,
    'Content-Type': 'application/json',
  },
});

// Handle token refresh
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Token expired, refresh it
      await refreshAuthToken();
      return apiClient.request(error.config);
    }
    return Promise.reject(error);
  }
);
```

## Core API Endpoints

### User Management

#### Get User Profile
```http
GET /api/users/profile/
```

**Response**:
```json
{
  "id": "user_123",
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "phone": "+**********",
  "profileImage": "https://example.com/avatar.jpg",
  "preferences": {
    "notifications": true,
    "location": true
  }
}
```

#### Update User Profile
```http
PUT /api/users/profile/
```

**Request Body**:
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "phone": "+**********",
  "preferences": {
    "notifications": true,
    "location": true
  }
}
```

### Provider Management

#### Get Providers
```http
GET /api/providers/
```

**Query Parameters**:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20)
- `category`: Service category filter
- `location`: Location filter (lat,lng,radius)
- `rating`: Minimum rating filter
- `search`: Search query

**Response**:
```json
{
  "providers": [
    {
      "id": "provider_123",
      "name": "John's Barbershop",
      "businessName": "John's Professional Services",
      "description": "Professional barber services",
      "rating": 4.8,
      "reviewCount": 125,
      "isVerified": true,
      "isOnline": true,
      "avatar": "https://example.com/avatar.jpg",
      "coverImage": "https://example.com/cover.jpg",
      "contact": {
        "phone": "+**********",
        "email": "<EMAIL>"
      },
      "location": {
        "latitude": 45.4215,
        "longitude": -75.6972,
        "address": "123 Main St, Ottawa, ON"
      },
      "services": ["haircut", "beard_trim", "styling"]
    }
  ],
  "total": 150,
  "page": 1,
  "limit": 20,
  "hasNext": true
}
```

#### Get Provider Details
```http
GET /api/providers/{provider_id}/
```

**Response**: Single provider object with additional details including portfolio, certifications, and reviews.

#### Get Provider Services
```http
GET /api/providers/{provider_id}/services/
```

**Response**:
```json
{
  "services": [
    {
      "id": "service_123",
      "name": "Haircut",
      "description": "Professional haircut service",
      "price": 30.00,
      "duration": 45,
      "category": "Hair",
      "image": "https://example.com/service.jpg"
    }
  ]
}
```

#### Get Provider Availability
```http
GET /api/providers/{provider_id}/availability/
```

**Query Parameters**:
- `date`: Date in YYYY-MM-DD format
- `service_id`: Optional service ID for service-specific availability

**Response**:
```json
{
  "date": "2024-01-15",
  "availableSlots": [
    "09:00", "09:30", "10:00", "10:30", "11:00",
    "14:00", "14:30", "15:00", "15:30", "16:00"
  ],
  "bookedSlots": ["11:30", "12:00", "13:00"],
  "unavailableSlots": ["12:30", "13:30"]
}
```

### Booking Management

#### Create Booking
```http
POST /api/bookings/
```

**Request Body**:
```json
{
  "providerId": "provider_123",
  "serviceId": "service_123",
  "scheduledDate": "2024-01-15",
  "scheduledTime": "10:00",
  "customerInfo": {
    "firstName": "Jane",
    "lastName": "Smith",
    "email": "<EMAIL>",
    "phone": "+**********"
  },
  "notes": "Please call when you arrive"
}
```

**Response**:
```json
{
  "id": "booking_123",
  "status": "confirmed",
  "providerId": "provider_123",
  "serviceId": "service_123",
  "scheduledDate": "2024-01-15",
  "scheduledTime": "10:00",
  "totalAmount": 30.00,
  "createdAt": "2024-01-10T10:00:00Z",
  "customerInfo": {
    "firstName": "Jane",
    "lastName": "Smith",
    "email": "<EMAIL>",
    "phone": "+**********"
  }
}
```

#### Get Bookings
```http
GET /api/bookings/
```

**Query Parameters**:
- `status`: Filter by status (upcoming, completed, cancelled)
- `page`: Page number
- `limit`: Items per page

**Response**:
```json
{
  "bookings": [
    {
      "id": "booking_123",
      "status": "confirmed",
      "provider": {
        "id": "provider_123",
        "name": "John's Barbershop",
        "avatar": "https://example.com/avatar.jpg"
      },
      "service": {
        "id": "service_123",
        "name": "Haircut",
        "duration": 45,
        "price": 30.00
      },
      "scheduledDate": "2024-01-15",
      "scheduledTime": "10:00",
      "totalAmount": 30.00
    }
  ],
  "total": 25,
  "page": 1,
  "limit": 20
}
```

#### Get Booking Details
```http
GET /api/bookings/{booking_id}/
```

**Response**: Detailed booking object with provider, service, and customer information.

#### Update Booking Status
```http
PUT /api/bookings/{booking_id}/status/
```

**Request Body**:
```json
{
  "status": "cancelled",
  "reason": "Customer requested cancellation"
}
```

#### Cancel Booking
```http
DELETE /api/bookings/{booking_id}/
```

**Request Body**:
```json
{
  "reason": "Schedule conflict",
  "refundRequested": true
}
```

### Search and Discovery

#### Search Services
```http
GET /api/search/services/
```

**Query Parameters**:
- `q`: Search query
- `category`: Service category
- `location`: Location (lat,lng,radius)
- `price_min`: Minimum price
- `price_max`: Maximum price
- `rating`: Minimum rating
- `page`: Page number
- `limit`: Items per page

**Response**:
```json
{
  "services": [
    {
      "id": "service_123",
      "name": "Haircut",
      "description": "Professional haircut",
      "price": 30.00,
      "duration": 45,
      "category": "Hair",
      "provider": {
        "id": "provider_123",
        "name": "John's Barbershop",
        "rating": 4.8,
        "location": {
          "latitude": 45.4215,
          "longitude": -75.6972
        }
      }
    }
  ],
  "total": 50,
  "page": 1,
  "limit": 20
}
```

#### Search Providers
```http
GET /api/search/providers/
```

Similar structure to services search but returns provider objects.

#### Get Search Suggestions
```http
GET /api/search/suggestions/
```

**Query Parameters**:
- `q`: Partial search query

**Response**:
```json
{
  "suggestions": [
    "haircut",
    "hair styling",
    "beard trim",
    "massage therapy"
  ]
}
```

### Payment Processing

#### Get Payment Methods
```http
GET /api/payments/methods/
```

**Response**:
```json
{
  "paymentMethods": [
    {
      "id": "pm_123",
      "type": "card",
      "last4": "4242",
      "brand": "visa",
      "expiryMonth": 12,
      "expiryYear": 2025,
      "isDefault": true
    }
  ]
}
```

#### Add Payment Method
```http
POST /api/payments/methods/
```

**Request Body**:
```json
{
  "type": "card",
  "token": "stripe_token_123",
  "setAsDefault": true
}
```

#### Process Payment
```http
POST /api/payments/
```

**Request Body**:
```json
{
  "bookingId": "booking_123",
  "paymentMethodId": "pm_123",
  "amount": 30.00,
  "currency": "CAD"
}
```

**Response**:
```json
{
  "id": "payment_123",
  "status": "succeeded",
  "amount": 30.00,
  "currency": "CAD",
  "transactionId": "txn_123",
  "createdAt": "2024-01-10T10:00:00Z"
}
```

### Messaging

#### Get Conversations
```http
GET /api/conversations/
```

**Response**:
```json
{
  "conversations": [
    {
      "id": "conv_123",
      "participantId": "provider_123",
      "participantName": "John's Barbershop",
      "participantAvatar": "https://example.com/avatar.jpg",
      "lastMessage": {
        "content": "I'll be there in 10 minutes",
        "timestamp": "2024-01-10T10:00:00Z",
        "senderId": "provider_123"
      },
      "unreadCount": 2
    }
  ]
}
```

#### Get Messages
```http
GET /api/conversations/{conversation_id}/messages/
```

**Query Parameters**:
- `page`: Page number
- `limit`: Items per page

**Response**:
```json
{
  "messages": [
    {
      "id": "msg_123",
      "content": "Hello, I'll be there soon",
      "senderId": "provider_123",
      "timestamp": "2024-01-10T10:00:00Z",
      "type": "text",
      "status": "read"
    }
  ],
  "total": 15,
  "page": 1,
  "limit": 20
}
```

#### Send Message
```http
POST /api/conversations/{conversation_id}/messages/
```

**Request Body**:
```json
{
  "content": "Thank you, see you soon!",
  "type": "text"
}
```

## WebSocket Integration

### Connection Setup

```typescript
import { createWebSocketService } from '../services/websocketService';

const wsService = createWebSocketService({
  url: 'ws://localhost:8000/ws/notifications/',
  reconnectAttempts: 5,
  reconnectInterval: 3000,
});

// Connect with authentication
await wsService.connect(authToken);
```

### Event Handling

#### Booking Updates
```typescript
wsService.on('booking_update', (data) => {
  console.log('Booking update:', data);
  // {
  //   bookingId: 'booking_123',
  //   status: 'provider_en_route',
  //   message: 'Provider is on their way',
  //   estimatedArrival: '2024-01-10T10:30:00Z'
  // }
});
```

#### New Messages
```typescript
wsService.on('new_message', (data) => {
  console.log('New message:', data);
  // {
  //   conversationId: 'conv_123',
  //   senderId: 'provider_123',
  //   senderName: 'John',
  //   message: 'I'm running 5 minutes late',
  //   messageType: 'text'
  // }
});
```

#### Provider Location Updates
```typescript
wsService.on('provider_location_update', (data) => {
  console.log('Provider location:', data);
  // {
  //   bookingId: 'booking_123',
  //   location: {
  //     latitude: 45.4215,
  //     longitude: -75.6972
  //   },
  //   estimatedArrival: '2024-01-10T10:25:00Z'
  // }
});
```

### Sending Events

#### Typing Indicators
```typescript
wsService.send({
  type: 'typing_indicator',
  conversationId: 'conv_123',
  isTyping: true
});
```

#### Location Updates (for providers)
```typescript
wsService.send({
  type: 'location_update',
  bookingId: 'booking_123',
  location: {
    latitude: 45.4215,
    longitude: -75.6972
  }
});
```

## Error Handling

### Standard Error Response
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field": "email",
      "reason": "Invalid email format"
    }
  }
}
```

### Common Error Codes
- `VALIDATION_ERROR`: Invalid input data
- `AUTHENTICATION_ERROR`: Invalid or expired token
- `PERMISSION_ERROR`: Insufficient permissions
- `NOT_FOUND`: Resource not found
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `SERVER_ERROR`: Internal server error

### Error Handling Implementation
```typescript
import { errorHandlingService } from '../services/errorHandlingService';

try {
  const response = await apiClient.get('/api/bookings/');
  return response.data;
} catch (error) {
  const errorReport = await errorHandlingService.handleError(
    error,
    { component: 'BookingService', action: 'getBookings' }
  );
  
  // Handle specific error types
  if (errorReport.type === 'authentication') {
    // Redirect to login
    navigation.navigate('Login');
  } else if (errorReport.type === 'network') {
    // Show retry option
    showRetryDialog();
  }
  
  throw error;
}
```

## Rate Limiting

### Limits
- **General API**: 1000 requests per hour per user
- **Search API**: 100 requests per minute per user
- **WebSocket**: 50 messages per minute per connection

### Headers
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1641811200
```

### Handling Rate Limits
```typescript
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 429) {
      const resetTime = error.response.headers['x-ratelimit-reset'];
      const waitTime = (resetTime * 1000) - Date.now();
      
      // Wait and retry
      setTimeout(() => {
        return apiClient.request(error.config);
      }, waitTime);
    }
    return Promise.reject(error);
  }
);
```

## Best Practices

### 1. Request Optimization
- Use pagination for large datasets
- Implement request caching
- Batch multiple requests when possible
- Use appropriate HTTP methods

### 2. Error Handling
- Always handle network errors gracefully
- Provide meaningful error messages to users
- Implement retry mechanisms for transient errors
- Log errors for debugging

### 3. Security
- Never expose sensitive data in logs
- Validate all user inputs
- Use HTTPS for all API calls
- Implement proper authentication

### 4. Performance
- Cache frequently accessed data
- Use compression for large payloads
- Implement request timeouts
- Monitor API response times

### 5. Real-time Features
- Handle WebSocket disconnections gracefully
- Implement reconnection logic
- Validate incoming WebSocket messages
- Limit message frequency to prevent spam
