{"version": 3, "names": ["_apiClient", "require", "CustomerService", "_classCallCheck2", "default", "_createClass2", "key", "value", "_getServiceCategories", "_asyncToGenerator2", "response", "apiClient", "get", "data", "error", "console", "getFallbackCategories", "getServiceCategories", "apply", "arguments", "_getFeaturedProviders", "limit", "length", "undefined", "results", "getFeaturedProviders", "_getNearbyProviders", "latitude", "longitude", "radius", "lat", "lng", "getNearbyProviders", "_x", "_x2", "_getCustomerDashboard", "getFallbackDashboard", "getCustomerDashboard", "_getCustomerProfile", "getCustomerProfile", "_getPersonalizedRecommendations", "getPersonalizedRecommendations", "_getFavoriteProviders", "getFavoriteProviders", "_createQuickBooking", "bookingData", "post", "createQuickBooking", "_x3", "id", "name", "slug", "description", "icon", "color", "serviceCount", "isActive", "displayOrder", "greeting", "upcomingBookings", "favoriteProviders", "recentActivity", "recommendations", "customerService", "_default", "exports"], "sources": ["customerService.ts"], "sourcesContent": ["/**\n * Customer Service - Backend Integration for Customer Features\n *\n * Service Contract:\n * - Handles all customer-related API calls\n * - Provides home screen data (categories, featured providers, recommendations)\n * - Manages customer profile and preferences\n * - Implements proper error handling and caching\n * - Supports offline functionality with graceful degradation\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\nimport { apiClient } from './apiClient';\nimport type { ApiResponse } from './apiClient';\n\n// Types for Customer Service\nexport interface ServiceCategory {\n  id: string;\n  name: string;\n  slug: string;\n  description: string;\n  icon: string;\n  color: string;\n  serviceCount: number;\n  isActive: boolean;\n  displayOrder: number;\n}\n\nexport interface FeaturedProvider {\n  id: string;\n  name: string;\n  businessName: string;\n  description: string;\n  avatar: string | null;\n  coverImage: string | null;\n  rating: number;\n  reviewCount: number;\n  isVerified: boolean;\n  isOnline: boolean;\n  categories: string[];\n  location: {\n    address: string;\n    city: string;\n    distance?: number;\n  };\n  services: {\n    id: string;\n    name: string;\n    price: number;\n    duration: number;\n  }[];\n  nextAvailableSlot?: string;\n}\n\nexport interface NearbyProvider extends FeaturedProvider {\n  distance: number;\n  estimatedTravelTime: number;\n}\n\nexport interface CustomerDashboard {\n  greeting: string;\n  upcomingBookings: number;\n  favoriteProviders: number;\n  recentActivity: {\n    type: 'booking' | 'review' | 'favorite';\n    message: string;\n    timestamp: string;\n  }[];\n  recommendations: {\n    type: 'service' | 'provider';\n    title: string;\n    subtitle: string;\n    imageUrl: string;\n    actionUrl: string;\n  }[];\n}\n\nexport interface CustomerProfile {\n  id: string;\n  firstName: string;\n  lastName: string;\n  email: string;\n  phone: string | null;\n  avatar: string | null;\n  dateOfBirth: string | null;\n  location: {\n    address: string;\n    city: string;\n    coordinates: {\n      latitude: number;\n      longitude: number;\n    };\n  } | null;\n  preferences: {\n    favoriteCategories: string[];\n    maxTravelDistance: number;\n    preferredPaymentMethod: string;\n    notifications: {\n      push: boolean;\n      email: boolean;\n      sms: boolean;\n    };\n  };\n  stats: {\n    totalBookings: number;\n    totalSpent: number;\n    memberSince: string;\n  };\n}\n\nexport interface QuickBooking {\n  providerId: string;\n  serviceId: string;\n  timeSlot: string;\n  notes?: string;\n}\n\nclass CustomerService {\n  /**\n   * Get service categories for home screen\n   */\n  async getServiceCategories(): Promise<ServiceCategory[]> {\n    try {\n      const response = await apiClient.get<ServiceCategory[]>('/api/v1/catalog/categories/');\n      return response.data;\n    } catch (error: any) {\n      console.error('Failed to fetch service categories:', error);\n      // Return fallback data for offline support\n      return this.getFallbackCategories();\n    }\n  }\n\n  /**\n   * Get featured providers for home screen\n   */\n  async getFeaturedProviders(limit: number = 10): Promise<FeaturedProvider[]> {\n    try {\n      const response = await apiClient.get<{ results: FeaturedProvider[] }>(\n        '/api/v1/catalog/providers/featured/',\n        { limit }\n      );\n      return response.data.results;\n    } catch (error: any) {\n      console.error('Failed to fetch featured providers:', error);\n      return [];\n    }\n  }\n\n  /**\n   * Get nearby providers based on user location\n   */\n  async getNearbyProviders(\n    latitude: number,\n    longitude: number,\n    radius: number = 10,\n    limit: number = 10\n  ): Promise<NearbyProvider[]> {\n    try {\n      const response = await apiClient.get<{ results: NearbyProvider[] }>(\n        '/api/v1/customer/nearby/providers/',\n        {\n          lat: latitude,\n          lng: longitude,\n          radius,\n          limit,\n        }\n      );\n      return response.data.results;\n    } catch (error: any) {\n      console.error('Failed to fetch nearby providers:', error);\n      return [];\n    }\n  }\n\n  /**\n   * Get customer dashboard data\n   */\n  async getCustomerDashboard(): Promise<CustomerDashboard> {\n    try {\n      const response = await apiClient.get<CustomerDashboard>('/api/v1/customer/dashboard/');\n      return response.data;\n    } catch (error: any) {\n      console.error('Failed to fetch customer dashboard:', error);\n      return this.getFallbackDashboard();\n    }\n  }\n\n  /**\n   * Get customer profile\n   */\n  async getCustomerProfile(): Promise<CustomerProfile> {\n    try {\n      const response = await apiClient.get<CustomerProfile>('/api/v1/customer/profile/');\n      return response.data;\n    } catch (error: any) {\n      console.error('Failed to fetch customer profile:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get personalized recommendations\n   */\n  async getPersonalizedRecommendations(): Promise<FeaturedProvider[]> {\n    try {\n      const response = await apiClient.get<{ results: FeaturedProvider[] }>(\n        '/api/v1/customer/recommendations/personalized/'\n      );\n      return response.data.results;\n    } catch (error: any) {\n      console.error('Failed to fetch personalized recommendations:', error);\n      return [];\n    }\n  }\n\n  /**\n   * Get customer's favorite providers\n   */\n  async getFavoriteProviders(): Promise<FeaturedProvider[]> {\n    try {\n      const response = await apiClient.get<{ results: FeaturedProvider[] }>(\n        '/api/v1/customer/favorites/'\n      );\n      return response.data.results;\n    } catch (error: any) {\n      console.error('Failed to fetch favorite providers:', error);\n      return [];\n    }\n  }\n\n  /**\n   * Quick booking functionality\n   */\n  async createQuickBooking(bookingData: QuickBooking): Promise<{ bookingId: string; status: string }> {\n    try {\n      const response = await apiClient.post<{ bookingId: string; status: string }>(\n        '/api/v1/customer/bookings/quick-book/',\n        bookingData\n      );\n      return response.data;\n    } catch (error: any) {\n      console.error('Failed to create quick booking:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Fallback categories for offline support\n   */\n  private getFallbackCategories(): ServiceCategory[] {\n    return [\n      {\n        id: '1',\n        name: 'Barber',\n        slug: 'barber',\n        description: 'Professional barber services',\n        icon: 'cut-outline',\n        color: '#5A7A63',\n        serviceCount: 12,\n        isActive: true,\n        displayOrder: 1,\n      },\n      {\n        id: '2',\n        name: 'Salon',\n        slug: 'salon',\n        description: 'Hair salon services',\n        icon: 'brush-outline',\n        color: '#6B8A74',\n        serviceCount: 8,\n        isActive: true,\n        displayOrder: 2,\n      },\n      {\n        id: '3',\n        name: 'Nail Services',\n        slug: 'nail-services',\n        description: 'Professional nail care',\n        icon: 'hand-left-outline',\n        color: '#5A7A63',\n        serviceCount: 15,\n        isActive: true,\n        displayOrder: 3,\n      },\n      {\n        id: '4',\n        name: 'Lash Services',\n        slug: 'lash-services',\n        description: 'Eyelash extensions and care',\n        icon: 'eye-outline',\n        color: '#4A6B52',\n        serviceCount: 6,\n        isActive: true,\n        displayOrder: 4,\n      },\n      {\n        id: '5',\n        name: 'Braiding',\n        slug: 'braiding',\n        description: 'Hair braiding services',\n        icon: 'flower-outline',\n        color: '#3A5B42',\n        serviceCount: 10,\n        isActive: true,\n        displayOrder: 5,\n      },\n      {\n        id: '6',\n        name: 'Skincare',\n        slug: 'skincare',\n        description: 'Facial and skincare treatments',\n        icon: 'heart-outline',\n        color: '#6B8A74',\n        serviceCount: 7,\n        isActive: true,\n        displayOrder: 6,\n      },\n      {\n        id: '7',\n        name: 'Massage',\n        slug: 'massage',\n        description: 'Therapeutic massage services',\n        icon: 'hand-right-outline',\n        color: '#5A7A63',\n        serviceCount: 8,\n        isActive: true,\n        displayOrder: 7,\n      },\n    ];\n  }\n\n  /**\n   * Fallback dashboard data for offline support\n   */\n  private getFallbackDashboard(): CustomerDashboard {\n    return {\n      greeting: 'Good morning',\n      upcomingBookings: 0,\n      favoriteProviders: 0,\n      recentActivity: [],\n      recommendations: [],\n    };\n  }\n}\n\n// Export singleton instance\nconst customerService = new CustomerService();\nexport default customerService;\n"], "mappings": ";;;;;;;;AAcA,IAAAA,UAAA,GAAAC,OAAA;AAAwC,IAyGlCC,eAAe;EAAA,SAAAA,gBAAA;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAF,eAAA;EAAA;EAAA,WAAAG,aAAA,CAAAD,OAAA,EAAAF,eAAA;IAAAI,GAAA;IAAAC,KAAA;MAAA,IAAAC,qBAAA,OAAAC,kBAAA,CAAAL,OAAA,EAInB,aAAyD;QACvD,IAAI;UACF,IAAMM,QAAQ,SAASC,oBAAS,CAACC,GAAG,CAAoB,6BAA6B,CAAC;UACtF,OAAOF,QAAQ,CAACG,IAAI;QACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;UACnBC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;UAE3D,OAAO,IAAI,CAACE,qBAAqB,CAAC,CAAC;QACrC;MACF,CAAC;MAAA,SATKC,oBAAoBA,CAAA;QAAA,OAAAT,qBAAA,CAAAU,KAAA,OAAAC,SAAA;MAAA;MAAA,OAApBF,oBAAoB;IAAA;EAAA;IAAAX,GAAA;IAAAC,KAAA;MAAA,IAAAa,qBAAA,OAAAX,kBAAA,CAAAL,OAAA,EAc1B,aAA4E;QAAA,IAAjDiB,KAAa,GAAAF,SAAA,CAAAG,MAAA,QAAAH,SAAA,QAAAI,SAAA,GAAAJ,SAAA,MAAG,EAAE;QAC3C,IAAI;UACF,IAAMT,QAAQ,SAASC,oBAAS,CAACC,GAAG,CAClC,qCAAqC,EACrC;YAAES,KAAK,EAALA;UAAM,CACV,CAAC;UACD,OAAOX,QAAQ,CAACG,IAAI,CAACW,OAAO;QAC9B,CAAC,CAAC,OAAOV,KAAU,EAAE;UACnBC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;UAC3D,OAAO,EAAE;QACX;MACF,CAAC;MAAA,SAXKW,oBAAoBA,CAAA;QAAA,OAAAL,qBAAA,CAAAF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAApBM,oBAAoB;IAAA;EAAA;IAAAnB,GAAA;IAAAC,KAAA;MAAA,IAAAmB,mBAAA,OAAAjB,kBAAA,CAAAL,OAAA,EAgB1B,WACEuB,QAAgB,EAChBC,SAAiB,EAGU;QAAA,IAF3BC,MAAc,GAAAV,SAAA,CAAAG,MAAA,QAAAH,SAAA,QAAAI,SAAA,GAAAJ,SAAA,MAAG,EAAE;QAAA,IACnBE,KAAa,GAAAF,SAAA,CAAAG,MAAA,QAAAH,SAAA,QAAAI,SAAA,GAAAJ,SAAA,MAAG,EAAE;QAElB,IAAI;UACF,IAAMT,QAAQ,SAASC,oBAAS,CAACC,GAAG,CAClC,oCAAoC,EACpC;YACEkB,GAAG,EAAEH,QAAQ;YACbI,GAAG,EAAEH,SAAS;YACdC,MAAM,EAANA,MAAM;YACNR,KAAK,EAALA;UACF,CACF,CAAC;UACD,OAAOX,QAAQ,CAACG,IAAI,CAACW,OAAO;QAC9B,CAAC,CAAC,OAAOV,KAAU,EAAE;UACnBC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;UACzD,OAAO,EAAE;QACX;MACF,CAAC;MAAA,SArBKkB,kBAAkBA,CAAAC,EAAA,EAAAC,GAAA;QAAA,OAAAR,mBAAA,CAAAR,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAlBa,kBAAkB;IAAA;EAAA;IAAA1B,GAAA;IAAAC,KAAA;MAAA,IAAA4B,qBAAA,OAAA1B,kBAAA,CAAAL,OAAA,EA0BxB,aAAyD;QACvD,IAAI;UACF,IAAMM,QAAQ,SAASC,oBAAS,CAACC,GAAG,CAAoB,6BAA6B,CAAC;UACtF,OAAOF,QAAQ,CAACG,IAAI;QACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;UACnBC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;UAC3D,OAAO,IAAI,CAACsB,oBAAoB,CAAC,CAAC;QACpC;MACF,CAAC;MAAA,SARKC,oBAAoBA,CAAA;QAAA,OAAAF,qBAAA,CAAAjB,KAAA,OAAAC,SAAA;MAAA;MAAA,OAApBkB,oBAAoB;IAAA;EAAA;IAAA/B,GAAA;IAAAC,KAAA;MAAA,IAAA+B,mBAAA,OAAA7B,kBAAA,CAAAL,OAAA,EAa1B,aAAqD;QACnD,IAAI;UACF,IAAMM,QAAQ,SAASC,oBAAS,CAACC,GAAG,CAAkB,2BAA2B,CAAC;UAClF,OAAOF,QAAQ,CAACG,IAAI;QACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;UACnBC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;UACzD,MAAMA,KAAK;QACb;MACF,CAAC;MAAA,SARKyB,kBAAkBA,CAAA;QAAA,OAAAD,mBAAA,CAAApB,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAlBoB,kBAAkB;IAAA;EAAA;IAAAjC,GAAA;IAAAC,KAAA;MAAA,IAAAiC,+BAAA,OAAA/B,kBAAA,CAAAL,OAAA,EAaxB,aAAoE;QAClE,IAAI;UACF,IAAMM,QAAQ,SAASC,oBAAS,CAACC,GAAG,CAClC,gDACF,CAAC;UACD,OAAOF,QAAQ,CAACG,IAAI,CAACW,OAAO;QAC9B,CAAC,CAAC,OAAOV,KAAU,EAAE;UACnBC,OAAO,CAACD,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;UACrE,OAAO,EAAE;QACX;MACF,CAAC;MAAA,SAVK2B,8BAA8BA,CAAA;QAAA,OAAAD,+BAAA,CAAAtB,KAAA,OAAAC,SAAA;MAAA;MAAA,OAA9BsB,8BAA8B;IAAA;EAAA;IAAAnC,GAAA;IAAAC,KAAA;MAAA,IAAAmC,qBAAA,OAAAjC,kBAAA,CAAAL,OAAA,EAepC,aAA0D;QACxD,IAAI;UACF,IAAMM,QAAQ,SAASC,oBAAS,CAACC,GAAG,CAClC,6BACF,CAAC;UACD,OAAOF,QAAQ,CAACG,IAAI,CAACW,OAAO;QAC9B,CAAC,CAAC,OAAOV,KAAU,EAAE;UACnBC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;UAC3D,OAAO,EAAE;QACX;MACF,CAAC;MAAA,SAVK6B,oBAAoBA,CAAA;QAAA,OAAAD,qBAAA,CAAAxB,KAAA,OAAAC,SAAA;MAAA;MAAA,OAApBwB,oBAAoB;IAAA;EAAA;IAAArC,GAAA;IAAAC,KAAA;MAAA,IAAAqC,mBAAA,OAAAnC,kBAAA,CAAAL,OAAA,EAe1B,WAAyByC,WAAyB,EAAkD;QAClG,IAAI;UACF,IAAMnC,QAAQ,SAASC,oBAAS,CAACmC,IAAI,CACnC,uCAAuC,EACvCD,WACF,CAAC;UACD,OAAOnC,QAAQ,CAACG,IAAI;QACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;UACnBC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;UACvD,MAAMA,KAAK;QACb;MACF,CAAC;MAAA,SAXKiC,kBAAkBA,CAAAC,GAAA;QAAA,OAAAJ,mBAAA,CAAA1B,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAlB4B,kBAAkB;IAAA;EAAA;IAAAzC,GAAA;IAAAC,KAAA,EAgBxB,SAAQS,qBAAqBA,CAAA,EAAsB;MACjD,OAAO,CACL;QACEiC,EAAE,EAAE,GAAG;QACPC,IAAI,EAAE,QAAQ;QACdC,IAAI,EAAE,QAAQ;QACdC,WAAW,EAAE,8BAA8B;QAC3CC,IAAI,EAAE,aAAa;QACnBC,KAAK,EAAE,SAAS;QAChBC,YAAY,EAAE,EAAE;QAChBC,QAAQ,EAAE,IAAI;QACdC,YAAY,EAAE;MAChB,CAAC,EACD;QACER,EAAE,EAAE,GAAG;QACPC,IAAI,EAAE,OAAO;QACbC,IAAI,EAAE,OAAO;QACbC,WAAW,EAAE,qBAAqB;QAClCC,IAAI,EAAE,eAAe;QACrBC,KAAK,EAAE,SAAS;QAChBC,YAAY,EAAE,CAAC;QACfC,QAAQ,EAAE,IAAI;QACdC,YAAY,EAAE;MAChB,CAAC,EACD;QACER,EAAE,EAAE,GAAG;QACPC,IAAI,EAAE,eAAe;QACrBC,IAAI,EAAE,eAAe;QACrBC,WAAW,EAAE,wBAAwB;QACrCC,IAAI,EAAE,mBAAmB;QACzBC,KAAK,EAAE,SAAS;QAChBC,YAAY,EAAE,EAAE;QAChBC,QAAQ,EAAE,IAAI;QACdC,YAAY,EAAE;MAChB,CAAC,EACD;QACER,EAAE,EAAE,GAAG;QACPC,IAAI,EAAE,eAAe;QACrBC,IAAI,EAAE,eAAe;QACrBC,WAAW,EAAE,6BAA6B;QAC1CC,IAAI,EAAE,aAAa;QACnBC,KAAK,EAAE,SAAS;QAChBC,YAAY,EAAE,CAAC;QACfC,QAAQ,EAAE,IAAI;QACdC,YAAY,EAAE;MAChB,CAAC,EACD;QACER,EAAE,EAAE,GAAG;QACPC,IAAI,EAAE,UAAU;QAChBC,IAAI,EAAE,UAAU;QAChBC,WAAW,EAAE,wBAAwB;QACrCC,IAAI,EAAE,gBAAgB;QACtBC,KAAK,EAAE,SAAS;QAChBC,YAAY,EAAE,EAAE;QAChBC,QAAQ,EAAE,IAAI;QACdC,YAAY,EAAE;MAChB,CAAC,EACD;QACER,EAAE,EAAE,GAAG;QACPC,IAAI,EAAE,UAAU;QAChBC,IAAI,EAAE,UAAU;QAChBC,WAAW,EAAE,gCAAgC;QAC7CC,IAAI,EAAE,eAAe;QACrBC,KAAK,EAAE,SAAS;QAChBC,YAAY,EAAE,CAAC;QACfC,QAAQ,EAAE,IAAI;QACdC,YAAY,EAAE;MAChB,CAAC,EACD;QACER,EAAE,EAAE,GAAG;QACPC,IAAI,EAAE,SAAS;QACfC,IAAI,EAAE,SAAS;QACfC,WAAW,EAAE,8BAA8B;QAC3CC,IAAI,EAAE,oBAAoB;QAC1BC,KAAK,EAAE,SAAS;QAChBC,YAAY,EAAE,CAAC;QACfC,QAAQ,EAAE,IAAI;QACdC,YAAY,EAAE;MAChB,CAAC,CACF;IACH;EAAC;IAAAnD,GAAA;IAAAC,KAAA,EAKD,SAAQ6B,oBAAoBA,CAAA,EAAsB;MAChD,OAAO;QACLsB,QAAQ,EAAE,cAAc;QACxBC,gBAAgB,EAAE,CAAC;QACnBC,iBAAiB,EAAE,CAAC;QACpBC,cAAc,EAAE,EAAE;QAClBC,eAAe,EAAE;MACnB,CAAC;IACH;EAAC;AAAA;AAIH,IAAMC,eAAe,GAAG,IAAI7D,eAAe,CAAC,CAAC;AAAC,IAAA8D,QAAA,GAAAC,OAAA,CAAA7D,OAAA,GAC/B2D,eAAe", "ignoreList": []}