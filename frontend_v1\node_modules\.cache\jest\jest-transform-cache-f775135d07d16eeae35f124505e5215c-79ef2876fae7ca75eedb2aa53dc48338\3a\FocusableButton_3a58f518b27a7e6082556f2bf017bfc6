d37a8b2f490e8ee227aa69b2c4e41524
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.FocusableButton = void 0;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _react = _interopRequireWildcard(require("react"));
var _reactNative = require("react-native");
var _vectorIcons = require("@expo/vector-icons");
var _MotorAccessibilityContext = require("../../contexts/MotorAccessibilityContext");
var _jsxRuntime = require("react/jsx-runtime");
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var FocusableButton = exports.FocusableButton = function FocusableButton(_ref) {
  var title = _ref.title,
    onPress = _ref.onPress,
    _ref$variant = _ref.variant,
    variant = _ref$variant === void 0 ? 'primary' : _ref$variant,
    _ref$size = _ref.size,
    size = _ref$size === void 0 ? 'medium' : _ref$size,
    _ref$disabled = _ref.disabled,
    disabled = _ref$disabled === void 0 ? false : _ref$disabled,
    icon = _ref.icon,
    _ref$iconPosition = _ref.iconPosition,
    iconPosition = _ref$iconPosition === void 0 ? 'left' : _ref$iconPosition,
    accessibilityLabel = _ref.accessibilityLabel,
    accessibilityHint = _ref.accessibilityHint,
    _ref$accessibilityRol = _ref.accessibilityRole,
    accessibilityRole = _ref$accessibilityRol === void 0 ? 'button' : _ref$accessibilityRol,
    _ref$focusable = _ref.focusable,
    focusable = _ref$focusable === void 0 ? true : _ref$focusable,
    _ref$autoFocus = _ref.autoFocus,
    autoFocus = _ref$autoFocus === void 0 ? false : _ref$autoFocus,
    onFocus = _ref.onFocus,
    onBlur = _ref.onBlur,
    testID = _ref.testID,
    style = _ref.style,
    textStyle = _ref.textStyle;
  var _useState = (0, _react.useState)(false),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    isFocused = _useState2[0],
    setIsFocused = _useState2[1];
  var _useState3 = (0, _react.useState)(false),
    _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
    isPressed = _useState4[0],
    setIsPressed = _useState4[1];
  var buttonRef = (0, _react.useRef)(null);
  var touchTargetStyles = (0, _MotorAccessibilityContext.useTouchTargetStyles)({
    width: 100,
    height: 40
  });
  var triggerHapticFeedback = (0, _MotorAccessibilityContext.useHapticFeedback)();
  var handlePress = (0, _react.useCallback)(function () {
    if (!disabled) {
      triggerHapticFeedback('light');
      onPress();
    }
  }, [disabled, onPress, triggerHapticFeedback]);
  var handleFocus = (0, _react.useCallback)(function () {
    setIsFocused(true);
    if (onFocus) {
      onFocus();
    }
  }, [onFocus]);
  var handleBlur = (0, _react.useCallback)(function () {
    setIsFocused(false);
    if (onBlur) {
      onBlur();
    }
  }, [onBlur]);
  var handlePressIn = (0, _react.useCallback)(function () {
    setIsPressed(true);
  }, []);
  var handlePressOut = (0, _react.useCallback)(function () {
    setIsPressed(false);
  }, []);
  _react.default.useEffect(function () {
    if (autoFocus && buttonRef.current && buttonRef.current.focus) {
      buttonRef.current.focus();
    }
  }, [autoFocus]);
  var buttonStyles = _react.default.useMemo(function () {
    var baseStyles = [styles.button, styles[`${variant}Button`], styles[`${size}Button`], touchTargetStyles];
    if (disabled) {
      baseStyles.push(styles.disabledButton);
    }
    if (isFocused) {
      baseStyles.push(styles.focusedButton);
    }
    if (isPressed) {
      baseStyles.push(styles.pressedButton);
    }
    if (style) {
      baseStyles.push(style);
    }
    return baseStyles;
  }, [variant, size, disabled, isFocused, isPressed, style, touchTargetStyles]);
  var textStyles = _react.default.useMemo(function () {
    var baseStyles = [styles.text, styles[`${variant}Text`], styles[`${size}Text`]];
    if (disabled) {
      baseStyles.push(styles.disabledText);
    }
    if (textStyle) {
      baseStyles.push(textStyle);
    }
    return baseStyles;
  }, [variant, size, disabled, textStyle]);
  var iconColor = _react.default.useMemo(function () {
    if (disabled) {
      return '#999';
    }
    switch (variant) {
      case 'primary':
        return '#FFFFFF';
      case 'secondary':
        return '#5A7A63';
      case 'ghost':
        return '#5A7A63';
      default:
        return '#5A7A63';
    }
  }, [variant, disabled]);
  var iconSize = _react.default.useMemo(function () {
    switch (size) {
      case 'small':
        return 16;
      case 'medium':
        return 20;
      case 'large':
        return 24;
      default:
        return 20;
    }
  }, [size]);
  var renderIcon = function renderIcon() {
    if (!icon) return null;
    return (0, _jsxRuntime.jsx)(_vectorIcons.Ionicons, {
      name: icon,
      size: iconSize,
      color: iconColor,
      style: [iconPosition === 'left' ? styles.iconLeft : styles.iconRight],
      accessibilityElementsHidden: true,
      importantForAccessibility: "no"
    });
  };
  return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
    ref: buttonRef,
    style: buttonStyles,
    onPress: handlePress,
    onPressIn: handlePressIn,
    onPressOut: handlePressOut,
    onFocus: handleFocus,
    onBlur: handleBlur,
    disabled: disabled,
    accessible: focusable,
    accessibilityRole: accessibilityRole,
    accessibilityLabel: accessibilityLabel || title,
    accessibilityHint: accessibilityHint,
    accessibilityState: {
      disabled: disabled,
      busy: false
    },
    testID: testID,
    activeOpacity: 0.7,
    children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.content,
      children: [icon && iconPosition === 'left' && renderIcon(), (0, _jsxRuntime.jsx)(_reactNative.Text, {
        style: textStyles,
        numberOfLines: 1,
        children: title
      }), icon && iconPosition === 'right' && renderIcon()]
    }), isFocused && _reactNative.Platform.OS === 'web' && (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: styles.focusIndicator
    })]
  });
};
var styles = _reactNative.StyleSheet.create({
  button: {
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    minHeight: 44,
    position: 'relative'
  },
  primaryButton: {
    backgroundColor: '#5A7A63',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#5A7A63'
  },
  ghostButton: {
    backgroundColor: 'transparent'
  },
  smallButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    minHeight: 32
  },
  mediumButton: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    minHeight: 44
  },
  largeButton: {
    paddingHorizontal: 24,
    paddingVertical: 16,
    minHeight: 48
  },
  disabledButton: {
    opacity: 0.5
  },
  focusedButton: {
    borderWidth: 2,
    borderColor: '#007AFF',
    shadowColor: '#007AFF',
    shadowOffset: {
      width: 0,
      height: 0
    },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4
  },
  pressedButton: {
    transform: [{
      scale: 0.98
    }]
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center'
  },
  text: {
    fontWeight: '600',
    textAlign: 'center'
  },
  primaryText: {
    color: '#FFFFFF'
  },
  secondaryText: {
    color: '#5A7A63'
  },
  ghostText: {
    color: '#5A7A63'
  },
  smallText: {
    fontSize: 14
  },
  mediumText: {
    fontSize: 16
  },
  largeText: {
    fontSize: 18
  },
  disabledText: {
    opacity: 0.7
  },
  iconLeft: {
    marginRight: 8
  },
  iconRight: {
    marginLeft: 8
  },
  focusIndicator: {
    position: 'absolute',
    top: -2,
    left: -2,
    right: -2,
    bottom: -2,
    borderWidth: 2,
    borderColor: '#007AFF',
    borderRadius: 10,
    pointerEvents: 'none'
  }
});
var _default = exports.default = FocusableButton;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfcmVhY3QiLCJfaW50ZXJvcFJlcXVpcmVXaWxkY2FyZCIsInJlcXVpcmUiLCJfcmVhY3ROYXRpdmUiLCJfdmVjdG9ySWNvbnMiLCJfTW90b3JBY2Nlc3NpYmlsaXR5Q29udGV4dCIsIl9qc3hSdW50aW1lIiwiZSIsInQiLCJXZWFrTWFwIiwiciIsIm4iLCJfX2VzTW9kdWxlIiwibyIsImkiLCJmIiwiX19wcm90b19fIiwiZGVmYXVsdCIsImhhcyIsImdldCIsInNldCIsIl90IiwiaGFzT3duUHJvcGVydHkiLCJjYWxsIiwiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJnZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IiLCJGb2N1c2FibGVCdXR0b24iLCJleHBvcnRzIiwiX3JlZiIsInRpdGxlIiwib25QcmVzcyIsIl9yZWYkdmFyaWFudCIsInZhcmlhbnQiLCJfcmVmJHNpemUiLCJzaXplIiwiX3JlZiRkaXNhYmxlZCIsImRpc2FibGVkIiwiaWNvbiIsIl9yZWYkaWNvblBvc2l0aW9uIiwiaWNvblBvc2l0aW9uIiwiYWNjZXNzaWJpbGl0eUxhYmVsIiwiYWNjZXNzaWJpbGl0eUhpbnQiLCJfcmVmJGFjY2Vzc2liaWxpdHlSb2wiLCJhY2Nlc3NpYmlsaXR5Um9sZSIsIl9yZWYkZm9jdXNhYmxlIiwiZm9jdXNhYmxlIiwiX3JlZiRhdXRvRm9jdXMiLCJhdXRvRm9jdXMiLCJvbkZvY3VzIiwib25CbHVyIiwidGVzdElEIiwic3R5bGUiLCJ0ZXh0U3R5bGUiLCJfdXNlU3RhdGUiLCJ1c2VTdGF0ZSIsIl91c2VTdGF0ZTIiLCJfc2xpY2VkVG9BcnJheTIiLCJpc0ZvY3VzZWQiLCJzZXRJc0ZvY3VzZWQiLCJfdXNlU3RhdGUzIiwiX3VzZVN0YXRlNCIsImlzUHJlc3NlZCIsInNldElzUHJlc3NlZCIsImJ1dHRvblJlZiIsInVzZVJlZiIsInRvdWNoVGFyZ2V0U3R5bGVzIiwidXNlVG91Y2hUYXJnZXRTdHlsZXMiLCJ3aWR0aCIsImhlaWdodCIsInRyaWdnZXJIYXB0aWNGZWVkYmFjayIsInVzZUhhcHRpY0ZlZWRiYWNrIiwiaGFuZGxlUHJlc3MiLCJ1c2VDYWxsYmFjayIsImhhbmRsZUZvY3VzIiwiaGFuZGxlQmx1ciIsImhhbmRsZVByZXNzSW4iLCJoYW5kbGVQcmVzc091dCIsIlJlYWN0IiwidXNlRWZmZWN0IiwiY3VycmVudCIsImZvY3VzIiwiYnV0dG9uU3R5bGVzIiwidXNlTWVtbyIsImJhc2VTdHlsZXMiLCJzdHlsZXMiLCJidXR0b24iLCJwdXNoIiwiZGlzYWJsZWRCdXR0b24iLCJmb2N1c2VkQnV0dG9uIiwicHJlc3NlZEJ1dHRvbiIsInRleHRTdHlsZXMiLCJ0ZXh0IiwiZGlzYWJsZWRUZXh0IiwiaWNvbkNvbG9yIiwiaWNvblNpemUiLCJyZW5kZXJJY29uIiwianN4IiwiSW9uaWNvbnMiLCJuYW1lIiwiY29sb3IiLCJpY29uTGVmdCIsImljb25SaWdodCIsImFjY2Vzc2liaWxpdHlFbGVtZW50c0hpZGRlbiIsImltcG9ydGFudEZvckFjY2Vzc2liaWxpdHkiLCJqc3hzIiwiVG91Y2hhYmxlT3BhY2l0eSIsInJlZiIsIm9uUHJlc3NJbiIsIm9uUHJlc3NPdXQiLCJhY2Nlc3NpYmxlIiwiYWNjZXNzaWJpbGl0eVN0YXRlIiwiYnVzeSIsImFjdGl2ZU9wYWNpdHkiLCJjaGlsZHJlbiIsIlZpZXciLCJjb250ZW50IiwiVGV4dCIsIm51bWJlck9mTGluZXMiLCJQbGF0Zm9ybSIsIk9TIiwiZm9jdXNJbmRpY2F0b3IiLCJTdHlsZVNoZWV0IiwiY3JlYXRlIiwiYm9yZGVyUmFkaXVzIiwiYWxpZ25JdGVtcyIsImp1c3RpZnlDb250ZW50IiwiZmxleERpcmVjdGlvbiIsIm1pbkhlaWdodCIsInBvc2l0aW9uIiwicHJpbWFyeUJ1dHRvbiIsImJhY2tncm91bmRDb2xvciIsInNoYWRvd0NvbG9yIiwic2hhZG93T2Zmc2V0Iiwic2hhZG93T3BhY2l0eSIsInNoYWRvd1JhZGl1cyIsImVsZXZhdGlvbiIsInNlY29uZGFyeUJ1dHRvbiIsImJvcmRlcldpZHRoIiwiYm9yZGVyQ29sb3IiLCJnaG9zdEJ1dHRvbiIsInNtYWxsQnV0dG9uIiwicGFkZGluZ0hvcml6b250YWwiLCJwYWRkaW5nVmVydGljYWwiLCJtZWRpdW1CdXR0b24iLCJsYXJnZUJ1dHRvbiIsIm9wYWNpdHkiLCJ0cmFuc2Zvcm0iLCJzY2FsZSIsImZvbnRXZWlnaHQiLCJ0ZXh0QWxpZ24iLCJwcmltYXJ5VGV4dCIsInNlY29uZGFyeVRleHQiLCJnaG9zdFRleHQiLCJzbWFsbFRleHQiLCJmb250U2l6ZSIsIm1lZGl1bVRleHQiLCJsYXJnZVRleHQiLCJtYXJnaW5SaWdodCIsIm1hcmdpbkxlZnQiLCJ0b3AiLCJsZWZ0IiwicmlnaHQiLCJib3R0b20iLCJwb2ludGVyRXZlbnRzIiwiX2RlZmF1bHQiXSwic291cmNlcyI6WyJGb2N1c2FibGVCdXR0b24udHN4Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogRm9jdXNhYmxlIEJ1dHRvbiBDb21wb25lbnRcbiAqXG4gKiBFbmhhbmNlZCBidXR0b24gY29tcG9uZW50IHdpdGggY29tcHJlaGVuc2l2ZSBrZXlib2FyZCBuYXZpZ2F0aW9uXG4gKiBhbmQgYWNjZXNzaWJpbGl0eSBzdXBwb3J0IGZvciBwb3dlciB1c2VycyBhbmQgYXNzaXN0aXZlIHRlY2hub2xvZ2llcy5cbiAqXG4gKiBGZWF0dXJlczpcbiAqIC0gS2V5Ym9hcmQgbmF2aWdhdGlvbiBzdXBwb3J0XG4gKiAtIEZvY3VzIG1hbmFnZW1lbnRcbiAqIC0gU2NyZWVuIHJlYWRlciBvcHRpbWl6YXRpb25cbiAqIC0gVG91Y2ggYW5kIGtleWJvYXJkIGFjdGl2YXRpb25cbiAqIC0gVmlzdWFsIGZvY3VzIGluZGljYXRvcnNcbiAqXG4gKiBAdmVyc2lvbiAxLjAuMFxuICogQGF1dGhvciBWaWVybGEgRGV2ZWxvcG1lbnQgVGVhbVxuICovXG5cbmltcG9ydCBSZWFjdCwgeyB1c2VSZWYsIHVzZUNhbGxiYWNrLCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IFRvdWNoYWJsZU9wYWNpdHksIFZpZXcsIFRleHQgfSBmcm9tICdyZWFjdC1uYXRpdmUnO1xuaW1wb3J0IHsgUGxhdGZvcm0gfSBmcm9tICdyZWFjdC1uYXRpdmUnO1xuaW1wb3J0IHsgU3R5bGVTaGVldCwgQWNjZXNzaWJpbGl0eVJvbGUsICB9IGZyb20gJ3JlYWN0LW5hdGl2ZSc7XG5pbXBvcnQgeyBJb25pY29ucyB9IGZyb20gJ0BleHBvL3ZlY3Rvci1pY29ucyc7XG5pbXBvcnQgeyB1c2VUb3VjaFRhcmdldFN0eWxlcywgdXNlSGFwdGljRmVlZGJhY2sgfSBmcm9tICcuLi8uLi9jb250ZXh0cy9Nb3RvckFjY2Vzc2liaWxpdHlDb250ZXh0JztcblxuLy8gQnV0dG9uIHByb3BzIGludGVyZmFjZVxuZXhwb3J0IGludGVyZmFjZSBGb2N1c2FibGVCdXR0b25Qcm9wcyB7XG4gIC8vIENvbnRlbnRcbiAgdGl0bGU6IHN0cmluZztcbiAgb25QcmVzczogKCkgPT4gdm9pZDtcbiAgXG4gIC8vIFN0eWxpbmdcbiAgdmFyaWFudD86ICdwcmltYXJ5JyB8ICdzZWNvbmRhcnknIHwgJ2dob3N0JztcbiAgc2l6ZT86ICdzbWFsbCcgfCAnbWVkaXVtJyB8ICdsYXJnZSc7XG4gIGRpc2FibGVkPzogYm9vbGVhbjtcbiAgXG4gIC8vIEljb25cbiAgaWNvbj86IHN0cmluZztcbiAgaWNvblBvc2l0aW9uPzogJ2xlZnQnIHwgJ3JpZ2h0JztcbiAgXG4gIC8vIEFjY2Vzc2liaWxpdHlcbiAgYWNjZXNzaWJpbGl0eUxhYmVsPzogc3RyaW5nO1xuICBhY2Nlc3NpYmlsaXR5SGludD86IHN0cmluZztcbiAgYWNjZXNzaWJpbGl0eVJvbGU/OiBBY2Nlc3NpYmlsaXR5Um9sZTtcbiAgXG4gIC8vIEtleWJvYXJkIG5hdmlnYXRpb25cbiAgZm9jdXNhYmxlPzogYm9vbGVhbjtcbiAgYXV0b0ZvY3VzPzogYm9vbGVhbjtcbiAgb25Gb2N1cz86ICgpID0+IHZvaWQ7XG4gIG9uQmx1cj86ICgpID0+IHZvaWQ7XG4gIFxuICAvLyBUZXN0aW5nXG4gIHRlc3RJRD86IHN0cmluZztcbiAgXG4gIC8vIEN1c3RvbSBzdHlsaW5nXG4gIHN0eWxlPzogYW55O1xuICB0ZXh0U3R5bGU/OiBhbnk7XG59XG5cbmV4cG9ydCBjb25zdCBGb2N1c2FibGVCdXR0b246IFJlYWN0LkZDPEZvY3VzYWJsZUJ1dHRvblByb3BzPiA9ICh7XG4gIHRpdGxlLFxuICBvblByZXNzLFxuICB2YXJpYW50ID0gJ3ByaW1hcnknLFxuICBzaXplID0gJ21lZGl1bScsXG4gIGRpc2FibGVkID0gZmFsc2UsXG4gIGljb24sXG4gIGljb25Qb3NpdGlvbiA9ICdsZWZ0JyxcbiAgYWNjZXNzaWJpbGl0eUxhYmVsLFxuICBhY2Nlc3NpYmlsaXR5SGludCxcbiAgYWNjZXNzaWJpbGl0eVJvbGUgPSAnYnV0dG9uJyxcbiAgZm9jdXNhYmxlID0gdHJ1ZSxcbiAgYXV0b0ZvY3VzID0gZmFsc2UsXG4gIG9uRm9jdXMsXG4gIG9uQmx1cixcbiAgdGVzdElELFxuICBzdHlsZSxcbiAgdGV4dFN0eWxlLFxufSkgPT4ge1xuICAvLyBTdGF0ZVxuICBjb25zdCBbaXNGb2N1c2VkLCBzZXRJc0ZvY3VzZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbaXNQcmVzc2VkLCBzZXRJc1ByZXNzZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIC8vIFJlZnNcbiAgY29uc3QgYnV0dG9uUmVmID0gdXNlUmVmPGFueT4obnVsbCk7XG5cbiAgLy8gTW90b3IgYWNjZXNzaWJpbGl0eSBob29rc1xuICBjb25zdCB0b3VjaFRhcmdldFN0eWxlcyA9IHVzZVRvdWNoVGFyZ2V0U3R5bGVzKHsgd2lkdGg6IDEwMCwgaGVpZ2h0OiA0MCB9KTtcbiAgY29uc3QgdHJpZ2dlckhhcHRpY0ZlZWRiYWNrID0gdXNlSGFwdGljRmVlZGJhY2soKTtcblxuICAvLyBIYW5kbGUgcHJlc3NcbiAgY29uc3QgaGFuZGxlUHJlc3MgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgaWYgKCFkaXNhYmxlZCkge1xuICAgICAgdHJpZ2dlckhhcHRpY0ZlZWRiYWNrKCdsaWdodCcpO1xuICAgICAgb25QcmVzcygpO1xuICAgIH1cbiAgfSwgW2Rpc2FibGVkLCBvblByZXNzLCB0cmlnZ2VySGFwdGljRmVlZGJhY2tdKTtcblxuICAvLyBIYW5kbGUgZm9jdXNcbiAgY29uc3QgaGFuZGxlRm9jdXMgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgc2V0SXNGb2N1c2VkKHRydWUpO1xuICAgIGlmIChvbkZvY3VzKSB7XG4gICAgICBvbkZvY3VzKCk7XG4gICAgfVxuICB9LCBbb25Gb2N1c10pO1xuXG4gIC8vIEhhbmRsZSBibHVyXG4gIGNvbnN0IGhhbmRsZUJsdXIgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgc2V0SXNGb2N1c2VkKGZhbHNlKTtcbiAgICBpZiAob25CbHVyKSB7XG4gICAgICBvbkJsdXIoKTtcbiAgICB9XG4gIH0sIFtvbkJsdXJdKTtcblxuICAvLyBIYW5kbGUgcHJlc3MgaW5cbiAgY29uc3QgaGFuZGxlUHJlc3NJbiA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBzZXRJc1ByZXNzZWQodHJ1ZSk7XG4gIH0sIFtdKTtcblxuICAvLyBIYW5kbGUgcHJlc3Mgb3V0XG4gIGNvbnN0IGhhbmRsZVByZXNzT3V0ID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIHNldElzUHJlc3NlZChmYWxzZSk7XG4gIH0sIFtdKTtcblxuICAvLyBBdXRvIGZvY3VzIGVmZmVjdFxuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChhdXRvRm9jdXMgJiYgYnV0dG9uUmVmLmN1cnJlbnQgJiYgYnV0dG9uUmVmLmN1cnJlbnQuZm9jdXMpIHtcbiAgICAgIGJ1dHRvblJlZi5jdXJyZW50LmZvY3VzKCk7XG4gICAgfVxuICB9LCBbYXV0b0ZvY3VzXSk7XG5cbiAgLy8gR2V0IGJ1dHRvbiBzdHlsZXNcbiAgY29uc3QgYnV0dG9uU3R5bGVzID0gUmVhY3QudXNlTWVtbygoKSA9PiB7XG4gICAgY29uc3QgYmFzZVN0eWxlcyA9IFtcbiAgICAgIHN0eWxlcy5idXR0b24sXG4gICAgICBzdHlsZXNbYCR7dmFyaWFudH1CdXR0b25gXSxcbiAgICAgIHN0eWxlc1tgJHtzaXplfUJ1dHRvbmBdLFxuICAgICAgdG91Y2hUYXJnZXRTdHlsZXMsIC8vIEFwcGx5IG1vdG9yIGFjY2Vzc2liaWxpdHkgc2l6aW5nXG4gICAgXTtcblxuICAgIGlmIChkaXNhYmxlZCkge1xuICAgICAgYmFzZVN0eWxlcy5wdXNoKHN0eWxlcy5kaXNhYmxlZEJ1dHRvbik7XG4gICAgfVxuXG4gICAgaWYgKGlzRm9jdXNlZCkge1xuICAgICAgYmFzZVN0eWxlcy5wdXNoKHN0eWxlcy5mb2N1c2VkQnV0dG9uKTtcbiAgICB9XG5cbiAgICBpZiAoaXNQcmVzc2VkKSB7XG4gICAgICBiYXNlU3R5bGVzLnB1c2goc3R5bGVzLnByZXNzZWRCdXR0b24pO1xuICAgIH1cblxuICAgIGlmIChzdHlsZSkge1xuICAgICAgYmFzZVN0eWxlcy5wdXNoKHN0eWxlKTtcbiAgICB9XG5cbiAgICByZXR1cm4gYmFzZVN0eWxlcztcbiAgfSwgW3ZhcmlhbnQsIHNpemUsIGRpc2FibGVkLCBpc0ZvY3VzZWQsIGlzUHJlc3NlZCwgc3R5bGUsIHRvdWNoVGFyZ2V0U3R5bGVzXSk7XG5cbiAgLy8gR2V0IHRleHQgc3R5bGVzXG4gIGNvbnN0IHRleHRTdHlsZXMgPSBSZWFjdC51c2VNZW1vKCgpID0+IHtcbiAgICBjb25zdCBiYXNlU3R5bGVzID0gW3N0eWxlcy50ZXh0LCBzdHlsZXNbYCR7dmFyaWFudH1UZXh0YF0sIHN0eWxlc1tgJHtzaXplfVRleHRgXV07XG4gICAgXG4gICAgaWYgKGRpc2FibGVkKSB7XG4gICAgICBiYXNlU3R5bGVzLnB1c2goc3R5bGVzLmRpc2FibGVkVGV4dCk7XG4gICAgfVxuICAgIFxuICAgIGlmICh0ZXh0U3R5bGUpIHtcbiAgICAgIGJhc2VTdHlsZXMucHVzaCh0ZXh0U3R5bGUpO1xuICAgIH1cbiAgICBcbiAgICByZXR1cm4gYmFzZVN0eWxlcztcbiAgfSwgW3ZhcmlhbnQsIHNpemUsIGRpc2FibGVkLCB0ZXh0U3R5bGVdKTtcblxuICAvLyBHZXQgaWNvbiBjb2xvclxuICBjb25zdCBpY29uQ29sb3IgPSBSZWFjdC51c2VNZW1vKCgpID0+IHtcbiAgICBpZiAoZGlzYWJsZWQpIHtcbiAgICAgIHJldHVybiAnIzk5OSc7XG4gICAgfVxuICAgIFxuICAgIHN3aXRjaCAodmFyaWFudCkge1xuICAgICAgY2FzZSAncHJpbWFyeSc6XG4gICAgICAgIHJldHVybiAnI0ZGRkZGRic7XG4gICAgICBjYXNlICdzZWNvbmRhcnknOlxuICAgICAgICByZXR1cm4gJyM1QTdBNjMnO1xuICAgICAgY2FzZSAnZ2hvc3QnOlxuICAgICAgICByZXR1cm4gJyM1QTdBNjMnO1xuICAgICAgZGVmYXVsdDpcbiAgICAgICAgcmV0dXJuICcjNUE3QTYzJztcbiAgICB9XG4gIH0sIFt2YXJpYW50LCBkaXNhYmxlZF0pO1xuXG4gIC8vIEdldCBpY29uIHNpemVcbiAgY29uc3QgaWNvblNpemUgPSBSZWFjdC51c2VNZW1vKCgpID0+IHtcbiAgICBzd2l0Y2ggKHNpemUpIHtcbiAgICAgIGNhc2UgJ3NtYWxsJzpcbiAgICAgICAgcmV0dXJuIDE2O1xuICAgICAgY2FzZSAnbWVkaXVtJzpcbiAgICAgICAgcmV0dXJuIDIwO1xuICAgICAgY2FzZSAnbGFyZ2UnOlxuICAgICAgICByZXR1cm4gMjQ7XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gMjA7XG4gICAgfVxuICB9LCBbc2l6ZV0pO1xuXG4gIC8vIFJlbmRlciBpY29uXG4gIGNvbnN0IHJlbmRlckljb24gPSAoKSA9PiB7XG4gICAgaWYgKCFpY29uKSByZXR1cm4gbnVsbDtcbiAgICBcbiAgICByZXR1cm4gKFxuICAgICAgPElvbmljb25zXG4gICAgICAgIG5hbWU9e2ljb24gYXMgYW55fVxuICAgICAgICBzaXplPXtpY29uU2l6ZX1cbiAgICAgICAgY29sb3I9e2ljb25Db2xvcn1cbiAgICAgICAgc3R5bGU9e1tcbiAgICAgICAgICBpY29uUG9zaXRpb24gPT09ICdsZWZ0JyA/IHN0eWxlcy5pY29uTGVmdCA6IHN0eWxlcy5pY29uUmlnaHQsXG4gICAgICAgIF19XG4gICAgICAgIGFjY2Vzc2liaWxpdHlFbGVtZW50c0hpZGRlbj17dHJ1ZX1cbiAgICAgICAgaW1wb3J0YW50Rm9yQWNjZXNzaWJpbGl0eT1cIm5vXCJcbiAgICAgIC8+XG4gICAgKTtcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxUb3VjaGFibGVPcGFjaXR5XG4gICAgICByZWY9e2J1dHRvblJlZn1cbiAgICAgIHN0eWxlPXtidXR0b25TdHlsZXN9XG4gICAgICBvblByZXNzPXtoYW5kbGVQcmVzc31cbiAgICAgIG9uUHJlc3NJbj17aGFuZGxlUHJlc3NJbn1cbiAgICAgIG9uUHJlc3NPdXQ9e2hhbmRsZVByZXNzT3V0fVxuICAgICAgb25Gb2N1cz17aGFuZGxlRm9jdXN9XG4gICAgICBvbkJsdXI9e2hhbmRsZUJsdXJ9XG4gICAgICBkaXNhYmxlZD17ZGlzYWJsZWR9XG4gICAgICBhY2Nlc3NpYmxlPXtmb2N1c2FibGV9XG4gICAgICBhY2Nlc3NpYmlsaXR5Um9sZT17YWNjZXNzaWJpbGl0eVJvbGV9XG4gICAgICBhY2Nlc3NpYmlsaXR5TGFiZWw9e2FjY2Vzc2liaWxpdHlMYWJlbCB8fCB0aXRsZX1cbiAgICAgIGFjY2Vzc2liaWxpdHlIaW50PXthY2Nlc3NpYmlsaXR5SGludH1cbiAgICAgIGFjY2Vzc2liaWxpdHlTdGF0ZT17e1xuICAgICAgICBkaXNhYmxlZCxcbiAgICAgICAgYnVzeTogZmFsc2UsXG4gICAgICB9fVxuICAgICAgdGVzdElEPXt0ZXN0SUR9XG4gICAgICBhY3RpdmVPcGFjaXR5PXswLjd9XG4gICAgPlxuICAgICAgPFZpZXcgc3R5bGU9e3N0eWxlcy5jb250ZW50fT5cbiAgICAgICAge2ljb24gJiYgaWNvblBvc2l0aW9uID09PSAnbGVmdCcgJiYgcmVuZGVySWNvbigpfVxuICAgICAgICA8VGV4dCBzdHlsZT17dGV4dFN0eWxlc30gbnVtYmVyT2ZMaW5lcz17MX0+XG4gICAgICAgICAge3RpdGxlfVxuICAgICAgICA8L1RleHQ+XG4gICAgICAgIHtpY29uICYmIGljb25Qb3NpdGlvbiA9PT0gJ3JpZ2h0JyAmJiByZW5kZXJJY29uKCl9XG4gICAgICA8L1ZpZXc+XG4gICAgICBcbiAgICAgIHsvKiBGb2N1cyBpbmRpY2F0b3IgZm9yIGtleWJvYXJkIG5hdmlnYXRpb24gKi99XG4gICAgICB7aXNGb2N1c2VkICYmIFBsYXRmb3JtLk9TID09PSAnd2ViJyAmJiAoXG4gICAgICAgIDxWaWV3IHN0eWxlPXtzdHlsZXMuZm9jdXNJbmRpY2F0b3J9IC8+XG4gICAgICApfVxuICAgIDwvVG91Y2hhYmxlT3BhY2l0eT5cbiAgKTtcbn07XG5cbmNvbnN0IHN0eWxlcyA9IFN0eWxlU2hlZXQuY3JlYXRlKHtcbiAgYnV0dG9uOiB7XG4gICAgYm9yZGVyUmFkaXVzOiA4LFxuICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJyxcbiAgICBmbGV4RGlyZWN0aW9uOiAncm93JyxcbiAgICBtaW5IZWlnaHQ6IDQ0LCAvLyBNaW5pbXVtIHRvdWNoIHRhcmdldFxuICAgIHBvc2l0aW9uOiAncmVsYXRpdmUnLFxuICB9LFxuICBcbiAgLy8gVmFyaWFudCBzdHlsZXNcbiAgcHJpbWFyeUJ1dHRvbjoge1xuICAgIGJhY2tncm91bmRDb2xvcjogJyM1QTdBNjMnLFxuICAgIHNoYWRvd0NvbG9yOiAnIzAwMCcsXG4gICAgc2hhZG93T2Zmc2V0OiB7IHdpZHRoOiAwLCBoZWlnaHQ6IDIgfSxcbiAgICBzaGFkb3dPcGFjaXR5OiAwLjEsXG4gICAgc2hhZG93UmFkaXVzOiA0LFxuICAgIGVsZXZhdGlvbjogMixcbiAgfSxcbiAgc2Vjb25kYXJ5QnV0dG9uOiB7XG4gICAgYmFja2dyb3VuZENvbG9yOiAndHJhbnNwYXJlbnQnLFxuICAgIGJvcmRlcldpZHRoOiAxLFxuICAgIGJvcmRlckNvbG9yOiAnIzVBN0E2MycsXG4gIH0sXG4gIGdob3N0QnV0dG9uOiB7XG4gICAgYmFja2dyb3VuZENvbG9yOiAndHJhbnNwYXJlbnQnLFxuICB9LFxuICBcbiAgLy8gU2l6ZSBzdHlsZXNcbiAgc21hbGxCdXR0b246IHtcbiAgICBwYWRkaW5nSG9yaXpvbnRhbDogMTIsXG4gICAgcGFkZGluZ1ZlcnRpY2FsOiA4LFxuICAgIG1pbkhlaWdodDogMzIsXG4gIH0sXG4gIG1lZGl1bUJ1dHRvbjoge1xuICAgIHBhZGRpbmdIb3Jpem9udGFsOiAxNixcbiAgICBwYWRkaW5nVmVydGljYWw6IDEyLFxuICAgIG1pbkhlaWdodDogNDQsXG4gIH0sXG4gIGxhcmdlQnV0dG9uOiB7XG4gICAgcGFkZGluZ0hvcml6b250YWw6IDI0LFxuICAgIHBhZGRpbmdWZXJ0aWNhbDogMTYsXG4gICAgbWluSGVpZ2h0OiA0OCxcbiAgfSxcbiAgXG4gIC8vIFN0YXRlIHN0eWxlc1xuICBkaXNhYmxlZEJ1dHRvbjoge1xuICAgIG9wYWNpdHk6IDAuNSxcbiAgfSxcbiAgZm9jdXNlZEJ1dHRvbjoge1xuICAgIGJvcmRlcldpZHRoOiAyLFxuICAgIGJvcmRlckNvbG9yOiAnIzAwN0FGRicsXG4gICAgc2hhZG93Q29sb3I6ICcjMDA3QUZGJyxcbiAgICBzaGFkb3dPZmZzZXQ6IHsgd2lkdGg6IDAsIGhlaWdodDogMCB9LFxuICAgIHNoYWRvd09wYWNpdHk6IDAuMyxcbiAgICBzaGFkb3dSYWRpdXM6IDQsXG4gICAgZWxldmF0aW9uOiA0LFxuICB9LFxuICBwcmVzc2VkQnV0dG9uOiB7XG4gICAgdHJhbnNmb3JtOiBbeyBzY2FsZTogMC45OCB9XSxcbiAgfSxcbiAgXG4gIC8vIENvbnRlbnRcbiAgY29udGVudDoge1xuICAgIGZsZXhEaXJlY3Rpb246ICdyb3cnLFxuICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJyxcbiAgfSxcbiAgXG4gIC8vIFRleHQgc3R5bGVzXG4gIHRleHQ6IHtcbiAgICBmb250V2VpZ2h0OiAnNjAwJyxcbiAgICB0ZXh0QWxpZ246ICdjZW50ZXInLFxuICB9LFxuICBwcmltYXJ5VGV4dDoge1xuICAgIGNvbG9yOiAnI0ZGRkZGRicsXG4gIH0sXG4gIHNlY29uZGFyeVRleHQ6IHtcbiAgICBjb2xvcjogJyM1QTdBNjMnLFxuICB9LFxuICBnaG9zdFRleHQ6IHtcbiAgICBjb2xvcjogJyM1QTdBNjMnLFxuICB9LFxuICBzbWFsbFRleHQ6IHtcbiAgICBmb250U2l6ZTogMTQsXG4gIH0sXG4gIG1lZGl1bVRleHQ6IHtcbiAgICBmb250U2l6ZTogMTYsXG4gIH0sXG4gIGxhcmdlVGV4dDoge1xuICAgIGZvbnRTaXplOiAxOCxcbiAgfSxcbiAgZGlzYWJsZWRUZXh0OiB7XG4gICAgb3BhY2l0eTogMC43LFxuICB9LFxuICBcbiAgLy8gSWNvbiBzdHlsZXNcbiAgaWNvbkxlZnQ6IHtcbiAgICBtYXJnaW5SaWdodDogOCxcbiAgfSxcbiAgaWNvblJpZ2h0OiB7XG4gICAgbWFyZ2luTGVmdDogOCxcbiAgfSxcbiAgXG4gIC8vIEZvY3VzIGluZGljYXRvclxuICBmb2N1c0luZGljYXRvcjoge1xuICAgIHBvc2l0aW9uOiAnYWJzb2x1dGUnLFxuICAgIHRvcDogLTIsXG4gICAgbGVmdDogLTIsXG4gICAgcmlnaHQ6IC0yLFxuICAgIGJvdHRvbTogLTIsXG4gICAgYm9yZGVyV2lkdGg6IDIsXG4gICAgYm9yZGVyQ29sb3I6ICcjMDA3QUZGJyxcbiAgICBib3JkZXJSYWRpdXM6IDEwLFxuICAgIHBvaW50ZXJFdmVudHM6ICdub25lJyxcbiAgfSxcbn0pO1xuXG5leHBvcnQgZGVmYXVsdCBGb2N1c2FibGVCdXR0b247XG4iXSwibWFwcGluZ3MiOiI7Ozs7OztBQWlCQSxJQUFBQSxNQUFBLEdBQUFDLHVCQUFBLENBQUFDLE9BQUE7QUFDQSxJQUFBQyxZQUFBLEdBQUFELE9BQUE7QUFHQSxJQUFBRSxZQUFBLEdBQUFGLE9BQUE7QUFDQSxJQUFBRywwQkFBQSxHQUFBSCxPQUFBO0FBQW1HLElBQUFJLFdBQUEsR0FBQUosT0FBQTtBQUFBLFNBQUFELHdCQUFBTSxDQUFBLEVBQUFDLENBQUEsNkJBQUFDLE9BQUEsTUFBQUMsQ0FBQSxPQUFBRCxPQUFBLElBQUFFLENBQUEsT0FBQUYsT0FBQSxZQUFBUix1QkFBQSxZQUFBQSx3QkFBQU0sQ0FBQSxFQUFBQyxDQUFBLFNBQUFBLENBQUEsSUFBQUQsQ0FBQSxJQUFBQSxDQUFBLENBQUFLLFVBQUEsU0FBQUwsQ0FBQSxNQUFBTSxDQUFBLEVBQUFDLENBQUEsRUFBQUMsQ0FBQSxLQUFBQyxTQUFBLFFBQUFDLE9BQUEsRUFBQVYsQ0FBQSxpQkFBQUEsQ0FBQSx1QkFBQUEsQ0FBQSx5QkFBQUEsQ0FBQSxTQUFBUSxDQUFBLE1BQUFGLENBQUEsR0FBQUwsQ0FBQSxHQUFBRyxDQUFBLEdBQUFELENBQUEsUUFBQUcsQ0FBQSxDQUFBSyxHQUFBLENBQUFYLENBQUEsVUFBQU0sQ0FBQSxDQUFBTSxHQUFBLENBQUFaLENBQUEsR0FBQU0sQ0FBQSxDQUFBTyxHQUFBLENBQUFiLENBQUEsRUFBQVEsQ0FBQSxjQUFBTSxFQUFBLElBQUFkLENBQUEsZ0JBQUFjLEVBQUEsT0FBQUMsY0FBQSxDQUFBQyxJQUFBLENBQUFoQixDQUFBLEVBQUFjLEVBQUEsT0FBQVAsQ0FBQSxJQUFBRCxDQUFBLEdBQUFXLE1BQUEsQ0FBQUMsY0FBQSxLQUFBRCxNQUFBLENBQUFFLHdCQUFBLENBQUFuQixDQUFBLEVBQUFjLEVBQUEsT0FBQVAsQ0FBQSxDQUFBSyxHQUFBLElBQUFMLENBQUEsQ0FBQU0sR0FBQSxJQUFBUCxDQUFBLENBQUFFLENBQUEsRUFBQU0sRUFBQSxFQUFBUCxDQUFBLElBQUFDLENBQUEsQ0FBQU0sRUFBQSxJQUFBZCxDQUFBLENBQUFjLEVBQUEsV0FBQU4sQ0FBQSxLQUFBUixDQUFBLEVBQUFDLENBQUE7QUFvQzVGLElBQU1tQixlQUErQyxHQUFBQyxPQUFBLENBQUFELGVBQUEsR0FBRyxTQUFsREEsZUFBK0NBLENBQUFFLElBQUEsRUFrQnREO0VBQUEsSUFqQkpDLEtBQUssR0FBQUQsSUFBQSxDQUFMQyxLQUFLO0lBQ0xDLE9BQU8sR0FBQUYsSUFBQSxDQUFQRSxPQUFPO0lBQUFDLFlBQUEsR0FBQUgsSUFBQSxDQUNQSSxPQUFPO0lBQVBBLE9BQU8sR0FBQUQsWUFBQSxjQUFHLFNBQVMsR0FBQUEsWUFBQTtJQUFBRSxTQUFBLEdBQUFMLElBQUEsQ0FDbkJNLElBQUk7SUFBSkEsSUFBSSxHQUFBRCxTQUFBLGNBQUcsUUFBUSxHQUFBQSxTQUFBO0lBQUFFLGFBQUEsR0FBQVAsSUFBQSxDQUNmUSxRQUFRO0lBQVJBLFFBQVEsR0FBQUQsYUFBQSxjQUFHLEtBQUssR0FBQUEsYUFBQTtJQUNoQkUsSUFBSSxHQUFBVCxJQUFBLENBQUpTLElBQUk7SUFBQUMsaUJBQUEsR0FBQVYsSUFBQSxDQUNKVyxZQUFZO0lBQVpBLFlBQVksR0FBQUQsaUJBQUEsY0FBRyxNQUFNLEdBQUFBLGlCQUFBO0lBQ3JCRSxrQkFBa0IsR0FBQVosSUFBQSxDQUFsQlksa0JBQWtCO0lBQ2xCQyxpQkFBaUIsR0FBQWIsSUFBQSxDQUFqQmEsaUJBQWlCO0lBQUFDLHFCQUFBLEdBQUFkLElBQUEsQ0FDakJlLGlCQUFpQjtJQUFqQkEsaUJBQWlCLEdBQUFELHFCQUFBLGNBQUcsUUFBUSxHQUFBQSxxQkFBQTtJQUFBRSxjQUFBLEdBQUFoQixJQUFBLENBQzVCaUIsU0FBUztJQUFUQSxTQUFTLEdBQUFELGNBQUEsY0FBRyxJQUFJLEdBQUFBLGNBQUE7SUFBQUUsY0FBQSxHQUFBbEIsSUFBQSxDQUNoQm1CLFNBQVM7SUFBVEEsU0FBUyxHQUFBRCxjQUFBLGNBQUcsS0FBSyxHQUFBQSxjQUFBO0lBQ2pCRSxPQUFPLEdBQUFwQixJQUFBLENBQVBvQixPQUFPO0lBQ1BDLE1BQU0sR0FBQXJCLElBQUEsQ0FBTnFCLE1BQU07SUFDTkMsTUFBTSxHQUFBdEIsSUFBQSxDQUFOc0IsTUFBTTtJQUNOQyxLQUFLLEdBQUF2QixJQUFBLENBQUx1QixLQUFLO0lBQ0xDLFNBQVMsR0FBQXhCLElBQUEsQ0FBVHdCLFNBQVM7RUFHVCxJQUFBQyxTQUFBLEdBQWtDLElBQUFDLGVBQVEsRUFBQyxLQUFLLENBQUM7SUFBQUMsVUFBQSxPQUFBQyxlQUFBLENBQUF4QyxPQUFBLEVBQUFxQyxTQUFBO0lBQTFDSSxTQUFTLEdBQUFGLFVBQUE7SUFBRUcsWUFBWSxHQUFBSCxVQUFBO0VBQzlCLElBQUFJLFVBQUEsR0FBa0MsSUFBQUwsZUFBUSxFQUFDLEtBQUssQ0FBQztJQUFBTSxVQUFBLE9BQUFKLGVBQUEsQ0FBQXhDLE9BQUEsRUFBQTJDLFVBQUE7SUFBMUNFLFNBQVMsR0FBQUQsVUFBQTtJQUFFRSxZQUFZLEdBQUFGLFVBQUE7RUFHOUIsSUFBTUcsU0FBUyxHQUFHLElBQUFDLGFBQU0sRUFBTSxJQUFJLENBQUM7RUFHbkMsSUFBTUMsaUJBQWlCLEdBQUcsSUFBQUMsK0NBQW9CLEVBQUM7SUFBRUMsS0FBSyxFQUFFLEdBQUc7SUFBRUMsTUFBTSxFQUFFO0VBQUcsQ0FBQyxDQUFDO0VBQzFFLElBQU1DLHFCQUFxQixHQUFHLElBQUFDLDRDQUFpQixFQUFDLENBQUM7RUFHakQsSUFBTUMsV0FBVyxHQUFHLElBQUFDLGtCQUFXLEVBQUMsWUFBTTtJQUNwQyxJQUFJLENBQUNwQyxRQUFRLEVBQUU7TUFDYmlDLHFCQUFxQixDQUFDLE9BQU8sQ0FBQztNQUM5QnZDLE9BQU8sQ0FBQyxDQUFDO0lBQ1g7RUFDRixDQUFDLEVBQUUsQ0FBQ00sUUFBUSxFQUFFTixPQUFPLEVBQUV1QyxxQkFBcUIsQ0FBQyxDQUFDO0VBRzlDLElBQU1JLFdBQVcsR0FBRyxJQUFBRCxrQkFBVyxFQUFDLFlBQU07SUFDcENkLFlBQVksQ0FBQyxJQUFJLENBQUM7SUFDbEIsSUFBSVYsT0FBTyxFQUFFO01BQ1hBLE9BQU8sQ0FBQyxDQUFDO0lBQ1g7RUFDRixDQUFDLEVBQUUsQ0FBQ0EsT0FBTyxDQUFDLENBQUM7RUFHYixJQUFNMEIsVUFBVSxHQUFHLElBQUFGLGtCQUFXLEVBQUMsWUFBTTtJQUNuQ2QsWUFBWSxDQUFDLEtBQUssQ0FBQztJQUNuQixJQUFJVCxNQUFNLEVBQUU7TUFDVkEsTUFBTSxDQUFDLENBQUM7SUFDVjtFQUNGLENBQUMsRUFBRSxDQUFDQSxNQUFNLENBQUMsQ0FBQztFQUdaLElBQU0wQixhQUFhLEdBQUcsSUFBQUgsa0JBQVcsRUFBQyxZQUFNO0lBQ3RDVixZQUFZLENBQUMsSUFBSSxDQUFDO0VBQ3BCLENBQUMsRUFBRSxFQUFFLENBQUM7RUFHTixJQUFNYyxjQUFjLEdBQUcsSUFBQUosa0JBQVcsRUFBQyxZQUFNO0lBQ3ZDVixZQUFZLENBQUMsS0FBSyxDQUFDO0VBQ3JCLENBQUMsRUFBRSxFQUFFLENBQUM7RUFHTmUsY0FBSyxDQUFDQyxTQUFTLENBQUMsWUFBTTtJQUNwQixJQUFJL0IsU0FBUyxJQUFJZ0IsU0FBUyxDQUFDZ0IsT0FBTyxJQUFJaEIsU0FBUyxDQUFDZ0IsT0FBTyxDQUFDQyxLQUFLLEVBQUU7TUFDN0RqQixTQUFTLENBQUNnQixPQUFPLENBQUNDLEtBQUssQ0FBQyxDQUFDO0lBQzNCO0VBQ0YsQ0FBQyxFQUFFLENBQUNqQyxTQUFTLENBQUMsQ0FBQztFQUdmLElBQU1rQyxZQUFZLEdBQUdKLGNBQUssQ0FBQ0ssT0FBTyxDQUFDLFlBQU07SUFDdkMsSUFBTUMsVUFBVSxHQUFHLENBQ2pCQyxNQUFNLENBQUNDLE1BQU0sRUFDYkQsTUFBTSxDQUFDLEdBQUdwRCxPQUFPLFFBQVEsQ0FBQyxFQUMxQm9ELE1BQU0sQ0FBQyxHQUFHbEQsSUFBSSxRQUFRLENBQUMsRUFDdkIrQixpQkFBaUIsQ0FDbEI7SUFFRCxJQUFJN0IsUUFBUSxFQUFFO01BQ1orQyxVQUFVLENBQUNHLElBQUksQ0FBQ0YsTUFBTSxDQUFDRyxjQUFjLENBQUM7SUFDeEM7SUFFQSxJQUFJOUIsU0FBUyxFQUFFO01BQ2IwQixVQUFVLENBQUNHLElBQUksQ0FBQ0YsTUFBTSxDQUFDSSxhQUFhLENBQUM7SUFDdkM7SUFFQSxJQUFJM0IsU0FBUyxFQUFFO01BQ2JzQixVQUFVLENBQUNHLElBQUksQ0FBQ0YsTUFBTSxDQUFDSyxhQUFhLENBQUM7SUFDdkM7SUFFQSxJQUFJdEMsS0FBSyxFQUFFO01BQ1RnQyxVQUFVLENBQUNHLElBQUksQ0FBQ25DLEtBQUssQ0FBQztJQUN4QjtJQUVBLE9BQU9nQyxVQUFVO0VBQ25CLENBQUMsRUFBRSxDQUFDbkQsT0FBTyxFQUFFRSxJQUFJLEVBQUVFLFFBQVEsRUFBRXFCLFNBQVMsRUFBRUksU0FBUyxFQUFFVixLQUFLLEVBQUVjLGlCQUFpQixDQUFDLENBQUM7RUFHN0UsSUFBTXlCLFVBQVUsR0FBR2IsY0FBSyxDQUFDSyxPQUFPLENBQUMsWUFBTTtJQUNyQyxJQUFNQyxVQUFVLEdBQUcsQ0FBQ0MsTUFBTSxDQUFDTyxJQUFJLEVBQUVQLE1BQU0sQ0FBQyxHQUFHcEQsT0FBTyxNQUFNLENBQUMsRUFBRW9ELE1BQU0sQ0FBQyxHQUFHbEQsSUFBSSxNQUFNLENBQUMsQ0FBQztJQUVqRixJQUFJRSxRQUFRLEVBQUU7TUFDWitDLFVBQVUsQ0FBQ0csSUFBSSxDQUFDRixNQUFNLENBQUNRLFlBQVksQ0FBQztJQUN0QztJQUVBLElBQUl4QyxTQUFTLEVBQUU7TUFDYitCLFVBQVUsQ0FBQ0csSUFBSSxDQUFDbEMsU0FBUyxDQUFDO0lBQzVCO0lBRUEsT0FBTytCLFVBQVU7RUFDbkIsQ0FBQyxFQUFFLENBQUNuRCxPQUFPLEVBQUVFLElBQUksRUFBRUUsUUFBUSxFQUFFZ0IsU0FBUyxDQUFDLENBQUM7RUFHeEMsSUFBTXlDLFNBQVMsR0FBR2hCLGNBQUssQ0FBQ0ssT0FBTyxDQUFDLFlBQU07SUFDcEMsSUFBSTlDLFFBQVEsRUFBRTtNQUNaLE9BQU8sTUFBTTtJQUNmO0lBRUEsUUFBUUosT0FBTztNQUNiLEtBQUssU0FBUztRQUNaLE9BQU8sU0FBUztNQUNsQixLQUFLLFdBQVc7UUFDZCxPQUFPLFNBQVM7TUFDbEIsS0FBSyxPQUFPO1FBQ1YsT0FBTyxTQUFTO01BQ2xCO1FBQ0UsT0FBTyxTQUFTO0lBQ3BCO0VBQ0YsQ0FBQyxFQUFFLENBQUNBLE9BQU8sRUFBRUksUUFBUSxDQUFDLENBQUM7RUFHdkIsSUFBTTBELFFBQVEsR0FBR2pCLGNBQUssQ0FBQ0ssT0FBTyxDQUFDLFlBQU07SUFDbkMsUUFBUWhELElBQUk7TUFDVixLQUFLLE9BQU87UUFDVixPQUFPLEVBQUU7TUFDWCxLQUFLLFFBQVE7UUFDWCxPQUFPLEVBQUU7TUFDWCxLQUFLLE9BQU87UUFDVixPQUFPLEVBQUU7TUFDWDtRQUNFLE9BQU8sRUFBRTtJQUNiO0VBQ0YsQ0FBQyxFQUFFLENBQUNBLElBQUksQ0FBQyxDQUFDO0VBR1YsSUFBTTZELFVBQVUsR0FBRyxTQUFiQSxVQUFVQSxDQUFBLEVBQVM7SUFDdkIsSUFBSSxDQUFDMUQsSUFBSSxFQUFFLE9BQU8sSUFBSTtJQUV0QixPQUNFLElBQUFoQyxXQUFBLENBQUEyRixHQUFBLEVBQUM3RixZQUFBLENBQUE4RixRQUFRO01BQ1BDLElBQUksRUFBRTdELElBQVk7TUFDbEJILElBQUksRUFBRTRELFFBQVM7TUFDZkssS0FBSyxFQUFFTixTQUFVO01BQ2pCMUMsS0FBSyxFQUFFLENBQ0xaLFlBQVksS0FBSyxNQUFNLEdBQUc2QyxNQUFNLENBQUNnQixRQUFRLEdBQUdoQixNQUFNLENBQUNpQixTQUFTLENBQzVEO01BQ0ZDLDJCQUEyQixFQUFFLElBQUs7TUFDbENDLHlCQUF5QixFQUFDO0lBQUksQ0FDL0IsQ0FBQztFQUVOLENBQUM7RUFFRCxPQUNFLElBQUFsRyxXQUFBLENBQUFtRyxJQUFBLEVBQUN0RyxZQUFBLENBQUF1RyxnQkFBZ0I7SUFDZkMsR0FBRyxFQUFFM0MsU0FBVTtJQUNmWixLQUFLLEVBQUU4QixZQUFhO0lBQ3BCbkQsT0FBTyxFQUFFeUMsV0FBWTtJQUNyQm9DLFNBQVMsRUFBRWhDLGFBQWM7SUFDekJpQyxVQUFVLEVBQUVoQyxjQUFlO0lBQzNCNUIsT0FBTyxFQUFFeUIsV0FBWTtJQUNyQnhCLE1BQU0sRUFBRXlCLFVBQVc7SUFDbkJ0QyxRQUFRLEVBQUVBLFFBQVM7SUFDbkJ5RSxVQUFVLEVBQUVoRSxTQUFVO0lBQ3RCRixpQkFBaUIsRUFBRUEsaUJBQWtCO0lBQ3JDSCxrQkFBa0IsRUFBRUEsa0JBQWtCLElBQUlYLEtBQU07SUFDaERZLGlCQUFpQixFQUFFQSxpQkFBa0I7SUFDckNxRSxrQkFBa0IsRUFBRTtNQUNsQjFFLFFBQVEsRUFBUkEsUUFBUTtNQUNSMkUsSUFBSSxFQUFFO0lBQ1IsQ0FBRTtJQUNGN0QsTUFBTSxFQUFFQSxNQUFPO0lBQ2Y4RCxhQUFhLEVBQUUsR0FBSTtJQUFBQyxRQUFBLEdBRW5CLElBQUE1RyxXQUFBLENBQUFtRyxJQUFBLEVBQUN0RyxZQUFBLENBQUFnSCxJQUFJO01BQUMvRCxLQUFLLEVBQUVpQyxNQUFNLENBQUMrQixPQUFRO01BQUFGLFFBQUEsR0FDekI1RSxJQUFJLElBQUlFLFlBQVksS0FBSyxNQUFNLElBQUl3RCxVQUFVLENBQUMsQ0FBQyxFQUNoRCxJQUFBMUYsV0FBQSxDQUFBMkYsR0FBQSxFQUFDOUYsWUFBQSxDQUFBa0gsSUFBSTtRQUFDakUsS0FBSyxFQUFFdUMsVUFBVztRQUFDMkIsYUFBYSxFQUFFLENBQUU7UUFBQUosUUFBQSxFQUN2Q3BGO01BQUssQ0FDRixDQUFDLEVBQ05RLElBQUksSUFBSUUsWUFBWSxLQUFLLE9BQU8sSUFBSXdELFVBQVUsQ0FBQyxDQUFDO0lBQUEsQ0FDN0MsQ0FBQyxFQUdOdEMsU0FBUyxJQUFJNkQscUJBQVEsQ0FBQ0MsRUFBRSxLQUFLLEtBQUssSUFDakMsSUFBQWxILFdBQUEsQ0FBQTJGLEdBQUEsRUFBQzlGLFlBQUEsQ0FBQWdILElBQUk7TUFBQy9ELEtBQUssRUFBRWlDLE1BQU0sQ0FBQ29DO0lBQWUsQ0FBRSxDQUN0QztFQUFBLENBQ2UsQ0FBQztBQUV2QixDQUFDO0FBRUQsSUFBTXBDLE1BQU0sR0FBR3FDLHVCQUFVLENBQUNDLE1BQU0sQ0FBQztFQUMvQnJDLE1BQU0sRUFBRTtJQUNOc0MsWUFBWSxFQUFFLENBQUM7SUFDZkMsVUFBVSxFQUFFLFFBQVE7SUFDcEJDLGNBQWMsRUFBRSxRQUFRO0lBQ3hCQyxhQUFhLEVBQUUsS0FBSztJQUNwQkMsU0FBUyxFQUFFLEVBQUU7SUFDYkMsUUFBUSxFQUFFO0VBQ1osQ0FBQztFQUdEQyxhQUFhLEVBQUU7SUFDYkMsZUFBZSxFQUFFLFNBQVM7SUFDMUJDLFdBQVcsRUFBRSxNQUFNO0lBQ25CQyxZQUFZLEVBQUU7TUFBRWpFLEtBQUssRUFBRSxDQUFDO01BQUVDLE1BQU0sRUFBRTtJQUFFLENBQUM7SUFDckNpRSxhQUFhLEVBQUUsR0FBRztJQUNsQkMsWUFBWSxFQUFFLENBQUM7SUFDZkMsU0FBUyxFQUFFO0VBQ2IsQ0FBQztFQUNEQyxlQUFlLEVBQUU7SUFDZk4sZUFBZSxFQUFFLGFBQWE7SUFDOUJPLFdBQVcsRUFBRSxDQUFDO0lBQ2RDLFdBQVcsRUFBRTtFQUNmLENBQUM7RUFDREMsV0FBVyxFQUFFO0lBQ1hULGVBQWUsRUFBRTtFQUNuQixDQUFDO0VBR0RVLFdBQVcsRUFBRTtJQUNYQyxpQkFBaUIsRUFBRSxFQUFFO0lBQ3JCQyxlQUFlLEVBQUUsQ0FBQztJQUNsQmYsU0FBUyxFQUFFO0VBQ2IsQ0FBQztFQUNEZ0IsWUFBWSxFQUFFO0lBQ1pGLGlCQUFpQixFQUFFLEVBQUU7SUFDckJDLGVBQWUsRUFBRSxFQUFFO0lBQ25CZixTQUFTLEVBQUU7RUFDYixDQUFDO0VBQ0RpQixXQUFXLEVBQUU7SUFDWEgsaUJBQWlCLEVBQUUsRUFBRTtJQUNyQkMsZUFBZSxFQUFFLEVBQUU7SUFDbkJmLFNBQVMsRUFBRTtFQUNiLENBQUM7RUFHRHhDLGNBQWMsRUFBRTtJQUNkMEQsT0FBTyxFQUFFO0VBQ1gsQ0FBQztFQUNEekQsYUFBYSxFQUFFO0lBQ2JpRCxXQUFXLEVBQUUsQ0FBQztJQUNkQyxXQUFXLEVBQUUsU0FBUztJQUN0QlAsV0FBVyxFQUFFLFNBQVM7SUFDdEJDLFlBQVksRUFBRTtNQUFFakUsS0FBSyxFQUFFLENBQUM7TUFBRUMsTUFBTSxFQUFFO0lBQUUsQ0FBQztJQUNyQ2lFLGFBQWEsRUFBRSxHQUFHO0lBQ2xCQyxZQUFZLEVBQUUsQ0FBQztJQUNmQyxTQUFTLEVBQUU7RUFDYixDQUFDO0VBQ0Q5QyxhQUFhLEVBQUU7SUFDYnlELFNBQVMsRUFBRSxDQUFDO01BQUVDLEtBQUssRUFBRTtJQUFLLENBQUM7RUFDN0IsQ0FBQztFQUdEaEMsT0FBTyxFQUFFO0lBQ1BXLGFBQWEsRUFBRSxLQUFLO0lBQ3BCRixVQUFVLEVBQUUsUUFBUTtJQUNwQkMsY0FBYyxFQUFFO0VBQ2xCLENBQUM7RUFHRGxDLElBQUksRUFBRTtJQUNKeUQsVUFBVSxFQUFFLEtBQUs7SUFDakJDLFNBQVMsRUFBRTtFQUNiLENBQUM7RUFDREMsV0FBVyxFQUFFO0lBQ1huRCxLQUFLLEVBQUU7RUFDVCxDQUFDO0VBQ0RvRCxhQUFhLEVBQUU7SUFDYnBELEtBQUssRUFBRTtFQUNULENBQUM7RUFDRHFELFNBQVMsRUFBRTtJQUNUckQsS0FBSyxFQUFFO0VBQ1QsQ0FBQztFQUNEc0QsU0FBUyxFQUFFO0lBQ1RDLFFBQVEsRUFBRTtFQUNaLENBQUM7RUFDREMsVUFBVSxFQUFFO0lBQ1ZELFFBQVEsRUFBRTtFQUNaLENBQUM7RUFDREUsU0FBUyxFQUFFO0lBQ1RGLFFBQVEsRUFBRTtFQUNaLENBQUM7RUFDRDlELFlBQVksRUFBRTtJQUNacUQsT0FBTyxFQUFFO0VBQ1gsQ0FBQztFQUdEN0MsUUFBUSxFQUFFO0lBQ1J5RCxXQUFXLEVBQUU7RUFDZixDQUFDO0VBQ0R4RCxTQUFTLEVBQUU7SUFDVHlELFVBQVUsRUFBRTtFQUNkLENBQUM7RUFHRHRDLGNBQWMsRUFBRTtJQUNkUSxRQUFRLEVBQUUsVUFBVTtJQUNwQitCLEdBQUcsRUFBRSxDQUFDLENBQUM7SUFDUEMsSUFBSSxFQUFFLENBQUMsQ0FBQztJQUNSQyxLQUFLLEVBQUUsQ0FBQyxDQUFDO0lBQ1RDLE1BQU0sRUFBRSxDQUFDLENBQUM7SUFDVnpCLFdBQVcsRUFBRSxDQUFDO0lBQ2RDLFdBQVcsRUFBRSxTQUFTO0lBQ3RCZixZQUFZLEVBQUUsRUFBRTtJQUNoQndDLGFBQWEsRUFBRTtFQUNqQjtBQUNGLENBQUMsQ0FBQztBQUFDLElBQUFDLFFBQUEsR0FBQXpJLE9BQUEsQ0FBQVgsT0FBQSxHQUVZVSxlQUFlIiwiaWdub3JlTGlzdCI6W119