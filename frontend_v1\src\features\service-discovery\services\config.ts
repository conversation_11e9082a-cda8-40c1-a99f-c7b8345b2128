/**
 * Service Discovery API Configuration
 *
 * Configuration for API endpoints, timeouts, and other settings
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { Platform } from 'react-native';

import { ServiceDiscoveryApiConfig } from '../types';

// Android emulator uses ******** to access host machine's localhost
// iOS simulator uses localhost or the machine's actual IP
const getBaseUrl = (): string => {
  if (process.env.NODE_ENV === 'test') {
    return 'http://localhost:8000';
  }

  if (Platform.OS === 'android') {
    return 'http://********:8000';
  }

  // For iOS or web, use localhost
  return 'http://127.0.0.1:8000';
};

export const apiConfig: ServiceDiscoveryApiConfig = {
  baseUrl: `${getBaseUrl()}/api/catalog`,
  timeout: 10000, // 10 seconds
  retryAttempts: 3,
  cacheTimeout: 5 * 60 * 1000, // 5 minutes
};

// API endpoints
export const endpoints = {
  services: '/services/',
  serviceDetails: (id: string) => `/services/${id}/`,
  providers: '/providers/',
  providerDetails: (id: string) => `/providers/${id}/`,
  providerServices: (id: string) => `/providers/${id}/services/`,
  categories: '/categories/',
  popularCategories: '/categories/popular/',
  search: '/search/',
  reviews: '/reviews/',
  serviceReviews: (id: string) => `/services/${id}/reviews/`,
};

// Error codes
export const errorCodes = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT: 'TIMEOUT',
  NOT_FOUND: 'NOT_FOUND',
  SERVER_ERROR: 'SERVER_ERROR',
  UNAUTHORIZED: 'UNAUTHORIZED',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
};

// Cache keys
export const cacheKeys = {
  services: 'services',
  serviceDetails: (id: string) => `service_${id}`,
  providers: 'providers',
  providerDetails: (id: string) => `provider_${id}`,
  categories: 'categories',
  popularCategories: 'popular_categories',
  search: (query: string) => `search_${query}`,
};
