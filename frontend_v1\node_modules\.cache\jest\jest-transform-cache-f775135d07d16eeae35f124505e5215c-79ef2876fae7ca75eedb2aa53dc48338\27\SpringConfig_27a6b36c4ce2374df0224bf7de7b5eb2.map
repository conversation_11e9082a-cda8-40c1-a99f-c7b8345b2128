{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "fromBouncinessAndSpeed", "fromOrigamiTensionAndFriction", "stiffnessFromOrigamiValue", "oValue", "dampingFromOrigamiValue", "tension", "friction", "stiffness", "damping", "bounciness", "speed", "normalize", "startValue", "endValue", "projectNormal", "n", "start", "end", "linearInterpolation", "t", "quadraticOutInterpolation", "b3Friction1", "x", "Math", "pow", "b3Friction2", "b3Friction3", "b3Nobounce", "b", "s", "bouncyTension", "bouncyFriction"], "sources": ["SpringConfig.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict\n */\n\n'use strict';\n\ntype SpringConfigType = {\n  stiffness: number,\n  damping: number,\n  ...\n};\n\nfunction stiffnessFromOrigamiValue(oValue: number) {\n  return (oValue - 30) * 3.62 + 194;\n}\n\nfunction dampingFromOrigamiValue(oValue: number) {\n  return (oValue - 8) * 3 + 25;\n}\n\nexport function fromOrigamiTensionAndFriction(\n  tension: number,\n  friction: number,\n): SpringConfigType {\n  return {\n    stiffness: stiffnessFromOrigamiValue(tension),\n    damping: dampingFromOrigamiValue(friction),\n  };\n}\n\nexport function fromBouncinessAndSpeed(\n  bounciness: number,\n  speed: number,\n): SpringConfigType {\n  function normalize(value: number, startValue: number, endValue: number) {\n    return (value - startValue) / (endValue - startValue);\n  }\n\n  function projectNormal(n: number, start: number, end: number) {\n    return start + n * (end - start);\n  }\n\n  function linearInterpolation(t: number, start: number, end: number) {\n    return t * end + (1 - t) * start;\n  }\n\n  function quadraticOutInterpolation(t: number, start: number, end: number) {\n    return linearInterpolation(2 * t - t * t, start, end);\n  }\n\n  function b3Friction1(x: number) {\n    return 0.0007 * Math.pow(x, 3) - 0.031 * Math.pow(x, 2) + 0.64 * x + 1.28;\n  }\n\n  function b3Friction2(x: number) {\n    return 0.000044 * Math.pow(x, 3) - 0.006 * Math.pow(x, 2) + 0.36 * x + 2;\n  }\n\n  function b3Friction3(x: number) {\n    return (\n      0.00000045 * Math.pow(x, 3) -\n      0.000332 * Math.pow(x, 2) +\n      0.1078 * x +\n      5.84\n    );\n  }\n\n  function b3Nobounce(tension: number) {\n    if (tension <= 18) {\n      return b3Friction1(tension);\n    } else if (tension > 18 && tension <= 44) {\n      return b3Friction2(tension);\n    } else {\n      return b3Friction3(tension);\n    }\n  }\n\n  let b = normalize(bounciness / 1.7, 0, 20);\n  b = projectNormal(b, 0, 0.8);\n  const s = normalize(speed / 1.7, 0, 20);\n  const bouncyTension = projectNormal(s, 0.5, 200);\n  const bouncyFriction = quadraticOutInterpolation(\n    b,\n    b3Nobounce(bouncyTension),\n    0.01,\n  );\n\n  return {\n    stiffness: stiffnessFromOrigamiValue(bouncyTension),\n    damping: dampingFromOrigamiValue(bouncyFriction),\n  };\n}\n"], "mappings": "AAUA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,sBAAA,GAAAA,sBAAA;AAAAF,OAAA,CAAAG,6BAAA,GAAAA,6BAAA;AAQb,SAASC,yBAAyBA,CAACC,MAAc,EAAE;EACjD,OAAO,CAACA,MAAM,GAAG,EAAE,IAAI,IAAI,GAAG,GAAG;AACnC;AAEA,SAASC,uBAAuBA,CAACD,MAAc,EAAE;EAC/C,OAAO,CAACA,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE;AAC9B;AAEO,SAASF,6BAA6BA,CAC3CI,OAAe,EACfC,QAAgB,EACE;EAClB,OAAO;IACLC,SAAS,EAAEL,yBAAyB,CAACG,OAAO,CAAC;IAC7CG,OAAO,EAAEJ,uBAAuB,CAACE,QAAQ;EAC3C,CAAC;AACH;AAEO,SAASN,sBAAsBA,CACpCS,UAAkB,EAClBC,KAAa,EACK;EAClB,SAASC,SAASA,CAACZ,KAAa,EAAEa,UAAkB,EAAEC,QAAgB,EAAE;IACtE,OAAO,CAACd,KAAK,GAAGa,UAAU,KAAKC,QAAQ,GAAGD,UAAU,CAAC;EACvD;EAEA,SAASE,aAAaA,CAACC,CAAS,EAAEC,KAAa,EAAEC,GAAW,EAAE;IAC5D,OAAOD,KAAK,GAAGD,CAAC,IAAIE,GAAG,GAAGD,KAAK,CAAC;EAClC;EAEA,SAASE,mBAAmBA,CAACC,CAAS,EAAEH,KAAa,EAAEC,GAAW,EAAE;IAClE,OAAOE,CAAC,GAAGF,GAAG,GAAG,CAAC,CAAC,GAAGE,CAAC,IAAIH,KAAK;EAClC;EAEA,SAASI,yBAAyBA,CAACD,CAAS,EAAEH,KAAa,EAAEC,GAAW,EAAE;IACxE,OAAOC,mBAAmB,CAAC,CAAC,GAAGC,CAAC,GAAGA,CAAC,GAAGA,CAAC,EAAEH,KAAK,EAAEC,GAAG,CAAC;EACvD;EAEA,SAASI,WAAWA,CAACC,CAAS,EAAE;IAC9B,OAAO,MAAM,GAAGC,IAAI,CAACC,GAAG,CAACF,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACF,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,GAAGA,CAAC,GAAG,IAAI;EAC3E;EAEA,SAASG,WAAWA,CAACH,CAAS,EAAE;IAC9B,OAAO,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACF,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACF,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,GAAGA,CAAC,GAAG,CAAC;EAC1E;EAEA,SAASI,WAAWA,CAACJ,CAAS,EAAE;IAC9B,OACE,UAAU,GAAGC,IAAI,CAACC,GAAG,CAACF,CAAC,EAAE,CAAC,CAAC,GAC3B,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACF,CAAC,EAAE,CAAC,CAAC,GACzB,MAAM,GAAGA,CAAC,GACV,IAAI;EAER;EAEA,SAASK,UAAUA,CAACtB,OAAe,EAAE;IACnC,IAAIA,OAAO,IAAI,EAAE,EAAE;MACjB,OAAOgB,WAAW,CAAChB,OAAO,CAAC;IAC7B,CAAC,MAAM,IAAIA,OAAO,GAAG,EAAE,IAAIA,OAAO,IAAI,EAAE,EAAE;MACxC,OAAOoB,WAAW,CAACpB,OAAO,CAAC;IAC7B,CAAC,MAAM;MACL,OAAOqB,WAAW,CAACrB,OAAO,CAAC;IAC7B;EACF;EAEA,IAAIuB,CAAC,GAAGjB,SAAS,CAACF,UAAU,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;EAC1CmB,CAAC,GAAGd,aAAa,CAACc,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;EAC5B,IAAMC,CAAC,GAAGlB,SAAS,CAACD,KAAK,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;EACvC,IAAMoB,aAAa,GAAGhB,aAAa,CAACe,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;EAChD,IAAME,cAAc,GAAGX,yBAAyB,CAC9CQ,CAAC,EACDD,UAAU,CAACG,aAAa,CAAC,EACzB,IACF,CAAC;EAED,OAAO;IACLvB,SAAS,EAAEL,yBAAyB,CAAC4B,aAAa,CAAC;IACnDtB,OAAO,EAAEJ,uBAAuB,CAAC2B,cAAc;EACjD,CAAC;AACH", "ignoreList": []}