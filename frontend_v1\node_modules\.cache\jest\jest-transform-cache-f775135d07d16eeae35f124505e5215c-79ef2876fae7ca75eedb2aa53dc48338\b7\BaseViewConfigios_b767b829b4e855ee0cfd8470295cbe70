425b8d0e3b906e2a13d0fc8b4e7110b1
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var ReactNativeFeatureFlags = _interopRequireWildcard(require("../../src/private/featureflags/ReactNativeFeatureFlags"));
var _NativeReactNativeFeatureFlags = _interopRequireDefault(require("../../src/private/featureflags/specs/NativeReactNativeFeatureFlags"));
var _ReactNativeStyleAttributes = _interopRequireDefault(require("../Components/View/ReactNativeStyleAttributes"));
var _ViewConfigIgnore = require("./ViewConfigIgnore");
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var bubblingEventTypes = {
  topPress: {
    phasedRegistrationNames: {
      bubbled: 'onPress',
      captured: 'onPressCapture'
    }
  },
  topChange: {
    phasedRegistrationNames: {
      bubbled: 'onChange',
      captured: 'onChangeCapture'
    }
  },
  topFocus: {
    phasedRegistrationNames: {
      bubbled: 'onFocus',
      captured: 'onFocusCapture'
    }
  },
  topBlur: {
    phasedRegistrationNames: {
      bubbled: 'onBlur',
      captured: 'onBlurCapture'
    }
  },
  topSubmitEditing: {
    phasedRegistrationNames: {
      bubbled: 'onSubmitEditing',
      captured: 'onSubmitEditingCapture'
    }
  },
  topEndEditing: {
    phasedRegistrationNames: {
      bubbled: 'onEndEditing',
      captured: 'onEndEditingCapture'
    }
  },
  topKeyPress: {
    phasedRegistrationNames: {
      bubbled: 'onKeyPress',
      captured: 'onKeyPressCapture'
    }
  },
  topTouchStart: {
    phasedRegistrationNames: {
      bubbled: 'onTouchStart',
      captured: 'onTouchStartCapture'
    }
  },
  topTouchMove: {
    phasedRegistrationNames: {
      bubbled: 'onTouchMove',
      captured: 'onTouchMoveCapture'
    }
  },
  topTouchCancel: {
    phasedRegistrationNames: {
      bubbled: 'onTouchCancel',
      captured: 'onTouchCancelCapture'
    }
  },
  topTouchEnd: {
    phasedRegistrationNames: {
      bubbled: 'onTouchEnd',
      captured: 'onTouchEndCapture'
    }
  },
  topClick: {
    phasedRegistrationNames: {
      captured: 'onClickCapture',
      bubbled: 'onClick'
    }
  },
  topPointerCancel: {
    phasedRegistrationNames: {
      captured: 'onPointerCancelCapture',
      bubbled: 'onPointerCancel'
    }
  },
  topPointerDown: {
    phasedRegistrationNames: {
      captured: 'onPointerDownCapture',
      bubbled: 'onPointerDown'
    }
  },
  topPointerMove: {
    phasedRegistrationNames: {
      captured: 'onPointerMoveCapture',
      bubbled: 'onPointerMove'
    }
  },
  topPointerUp: {
    phasedRegistrationNames: {
      captured: 'onPointerUpCapture',
      bubbled: 'onPointerUp'
    }
  },
  topPointerEnter: {
    phasedRegistrationNames: {
      captured: 'onPointerEnterCapture',
      bubbled: 'onPointerEnter',
      skipBubbling: true
    }
  },
  topPointerLeave: {
    phasedRegistrationNames: {
      captured: 'onPointerLeaveCapture',
      bubbled: 'onPointerLeave',
      skipBubbling: true
    }
  },
  topPointerOver: {
    phasedRegistrationNames: {
      captured: 'onPointerOverCapture',
      bubbled: 'onPointerOver'
    }
  },
  topPointerOut: {
    phasedRegistrationNames: {
      captured: 'onPointerOutCapture',
      bubbled: 'onPointerOut'
    }
  },
  topGotPointerCapture: {
    phasedRegistrationNames: {
      captured: 'onGotPointerCaptureCapture',
      bubbled: 'onGotPointerCapture'
    }
  },
  topLostPointerCapture: {
    phasedRegistrationNames: {
      captured: 'onLostPointerCaptureCapture',
      bubbled: 'onLostPointerCapture'
    }
  }
};
var directEventTypes = {
  topAccessibilityAction: {
    registrationName: 'onAccessibilityAction'
  },
  topAccessibilityTap: {
    registrationName: 'onAccessibilityTap'
  },
  topMagicTap: {
    registrationName: 'onMagicTap'
  },
  topAccessibilityEscape: {
    registrationName: 'onAccessibilityEscape'
  },
  topLayout: {
    registrationName: 'onLayout'
  },
  onGestureHandlerEvent: (0, _ViewConfigIgnore.DynamicallyInjectedByGestureHandler)({
    registrationName: 'onGestureHandlerEvent'
  }),
  onGestureHandlerStateChange: (0, _ViewConfigIgnore.DynamicallyInjectedByGestureHandler)({
    registrationName: 'onGestureHandlerStateChange'
  })
};
var validAttributesForNonEventProps = {
  accessible: true,
  accessibilityActions: true,
  accessibilityLabel: true,
  accessibilityHint: true,
  accessibilityLanguage: true,
  accessibilityValue: true,
  accessibilityViewIsModal: true,
  accessibilityElementsHidden: true,
  accessibilityIgnoresInvertColors: true,
  accessibilityShowsLargeContentViewer: true,
  accessibilityLargeContentTitle: true,
  testID: true,
  backgroundColor: {
    process: require("../StyleSheet/processColor").default
  },
  backfaceVisibility: true,
  cursor: true,
  opacity: true,
  shadowColor: {
    process: require("../StyleSheet/processColor").default
  },
  shadowOffset: {
    diff: require("../Utilities/differ/sizesDiffer").default
  },
  shadowOpacity: true,
  shadowRadius: true,
  needsOffscreenAlphaCompositing: true,
  overflow: true,
  shouldRasterizeIOS: true,
  transform: {
    diff: require("../Utilities/differ/matricesDiffer").default
  },
  transformOrigin: true,
  accessibilityRole: true,
  accessibilityState: true,
  nativeID: true,
  pointerEvents: true,
  removeClippedSubviews: true,
  role: true,
  borderRadius: true,
  borderColor: {
    process: require("../StyleSheet/processColor").default
  },
  borderBlockColor: {
    process: require("../StyleSheet/processColor").default
  },
  borderCurve: true,
  borderWidth: true,
  borderBlockWidth: true,
  borderStyle: true,
  hitSlop: {
    diff: require("../Utilities/differ/insetsDiffer").default
  },
  collapsable: true,
  collapsableChildren: true,
  filter: _NativeReactNativeFeatureFlags.default != null && ReactNativeFeatureFlags.enableNativeCSSParsing() ? true : {
    process: require("../StyleSheet/processFilter").default
  },
  boxShadow: _NativeReactNativeFeatureFlags.default != null && ReactNativeFeatureFlags.enableNativeCSSParsing() ? true : {
    process: require("../StyleSheet/processBoxShadow").default
  },
  mixBlendMode: true,
  isolation: true,
  borderTopWidth: true,
  borderTopColor: {
    process: require("../StyleSheet/processColor").default
  },
  borderRightWidth: true,
  borderRightColor: {
    process: require("../StyleSheet/processColor").default
  },
  borderBottomWidth: true,
  borderBottomColor: {
    process: require("../StyleSheet/processColor").default
  },
  borderLeftWidth: true,
  borderLeftColor: {
    process: require("../StyleSheet/processColor").default
  },
  borderStartWidth: true,
  borderBlockStartWidth: true,
  borderStartColor: {
    process: require("../StyleSheet/processColor").default
  },
  borderBlockStartColor: {
    process: require("../StyleSheet/processColor").default
  },
  borderEndWidth: true,
  borderBlockEndWidth: true,
  borderEndColor: {
    process: require("../StyleSheet/processColor").default
  },
  borderBlockEndColor: {
    process: require("../StyleSheet/processColor").default
  },
  borderTopLeftRadius: true,
  borderTopRightRadius: true,
  borderTopStartRadius: true,
  borderTopEndRadius: true,
  borderBottomLeftRadius: true,
  borderBottomRightRadius: true,
  borderBottomStartRadius: true,
  borderBottomEndRadius: true,
  borderEndEndRadius: true,
  borderEndStartRadius: true,
  borderStartEndRadius: true,
  borderStartStartRadius: true,
  display: true,
  zIndex: true,
  top: true,
  right: true,
  start: true,
  end: true,
  bottom: true,
  left: true,
  inset: true,
  insetBlock: true,
  insetBlockEnd: true,
  insetBlockStart: true,
  insetInline: true,
  insetInlineEnd: true,
  insetInlineStart: true,
  width: true,
  height: true,
  minWidth: true,
  maxWidth: true,
  minHeight: true,
  maxHeight: true,
  margin: true,
  marginBlock: true,
  marginBlockEnd: true,
  marginBlockStart: true,
  marginBottom: true,
  marginEnd: true,
  marginHorizontal: true,
  marginInline: true,
  marginInlineEnd: true,
  marginInlineStart: true,
  marginLeft: true,
  marginRight: true,
  marginStart: true,
  marginTop: true,
  marginVertical: true,
  padding: true,
  paddingBlock: true,
  paddingBlockEnd: true,
  paddingBlockStart: true,
  paddingBottom: true,
  paddingEnd: true,
  paddingHorizontal: true,
  paddingInline: true,
  paddingInlineEnd: true,
  paddingInlineStart: true,
  paddingLeft: true,
  paddingRight: true,
  paddingStart: true,
  paddingTop: true,
  paddingVertical: true,
  flex: true,
  flexGrow: true,
  rowGap: true,
  columnGap: true,
  gap: true,
  flexShrink: true,
  flexBasis: true,
  flexDirection: true,
  flexWrap: true,
  justifyContent: true,
  alignItems: true,
  alignSelf: true,
  alignContent: true,
  position: true,
  aspectRatio: true,
  boxSizing: true,
  direction: true,
  style: _ReactNativeStyleAttributes.default
};
var validAttributesForEventProps = (0, _ViewConfigIgnore.ConditionallyIgnoredEventHandlers)({
  onLayout: true,
  onMagicTap: true,
  onAccessibilityAction: true,
  onAccessibilityEscape: true,
  onAccessibilityTap: true,
  onMoveShouldSetResponder: true,
  onMoveShouldSetResponderCapture: true,
  onStartShouldSetResponder: true,
  onStartShouldSetResponderCapture: true,
  onResponderGrant: true,
  onResponderReject: true,
  onResponderStart: true,
  onResponderEnd: true,
  onResponderRelease: true,
  onResponderMove: true,
  onResponderTerminate: true,
  onResponderTerminationRequest: true,
  onShouldBlockNativeResponder: true,
  onTouchStart: true,
  onTouchMove: true,
  onTouchEnd: true,
  onTouchCancel: true,
  onClick: true,
  onClickCapture: true,
  onPointerUp: true,
  onPointerDown: true,
  onPointerCancel: true,
  onPointerEnter: true,
  onPointerMove: true,
  onPointerLeave: true,
  onPointerOver: true,
  onPointerOut: true,
  onGotPointerCapture: true,
  onLostPointerCapture: true
});
var PlatformBaseViewConfigIos = {
  bubblingEventTypes: bubblingEventTypes,
  directEventTypes: directEventTypes,
  validAttributes: Object.assign({}, validAttributesForNonEventProps, validAttributesForEventProps)
};
var _default = exports.default = PlatformBaseViewConfigIos;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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