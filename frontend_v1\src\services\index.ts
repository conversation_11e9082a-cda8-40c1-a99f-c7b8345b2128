/**
 * Services Export Index
 *
 * Centralized export for all service modules in the Vierla application.
 * Services provide business logic and external API interactions.
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

// Core Services
export { apiClient } from './apiClient';
export { default as authService } from './authService';
export { default as customerService } from './customerService';
export { default as providerService } from './providerService';
export { default as bookingService } from './bookingService';
export { default as messagingService } from './messagingService';

// Undo Service
export { default as undoService, UndoHelpers } from './undoService';
export type { UndoOperation, UndoServiceConfig } from './undoService';

// Service Categories for Documentation
export const SERVICE_CATEGORIES = {
  UNDO: 'Undo & Destructive Action Management',
  AUTH: 'Authentication & Authorization',
  DATA: 'Data Management & API',
  STORAGE: 'Local Storage & Caching',
  NOTIFICATION: 'Push Notifications & Alerts',
  CUSTOMER: 'Customer Data & Dashboard',
  PROVIDER: 'Provider Data & Services',
  BOOKING: 'Booking Management',
  MESSAGING: 'Real-time Communication',
} as const;
