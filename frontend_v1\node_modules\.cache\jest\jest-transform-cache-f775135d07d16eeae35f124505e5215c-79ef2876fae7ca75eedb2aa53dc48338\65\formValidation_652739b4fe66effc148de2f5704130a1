8b304e9839b55604d0fba3ef2155bc14
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.validateForm = exports.validateField = exports.createDebouncedValidator = exports.ValidationRules = exports.FormValidator = exports.CommonValidations = void 0;
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var ValidationRules = exports.ValidationRules = {
  required: function required(value) {
    var isValid = value !== null && value !== undefined && String(value).trim().length > 0;
    return {
      isValid: isValid,
      error: isValid ? undefined : 'Please fill in this field to continue'
    };
  },
  email: function email(value) {
    if (!value) return {
      isValid: true
    };
    var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    var isValid = emailRegex.test(value);
    return {
      isValid: isValid,
      error: isValid ? undefined : 'Please enter your email in <NAME_EMAIL>'
    };
  },
  phone: function phone(value) {
    if (!value) return {
      isValid: true
    };
    var cleanPhone = value.replace(/[\s\-\(\)]/g, '');
    var phoneRegex = /^[\+]?[1-9][\d]{9,14}$/;
    var isValid = phoneRegex.test(cleanPhone);
    return {
      isValid: isValid,
      error: isValid ? undefined : 'Please enter your phone number with area code (e.g., ************)'
    };
  },
  password: function password(value) {
    if (!value) return {
      isValid: true
    };
    var minLength = 8;
    var hasUpperCase = /[A-Z]/.test(value);
    var hasLowerCase = /[a-z]/.test(value);
    var hasNumbers = /\d/.test(value);
    var hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(value);
    if (value.length < minLength) {
      return {
        isValid: false,
        error: `Your password needs at least ${minLength} characters. Please add ${minLength - value.length} more character${minLength - value.length > 1 ? 's' : ''}.`
      };
    }
    if (!hasUpperCase) {
      return {
        isValid: false,
        error: 'Your password needs at least one uppercase letter (A-Z). Please add one to continue.'
      };
    }
    if (!hasLowerCase) {
      return {
        isValid: false,
        error: 'Your password needs at least one lowercase letter (a-z). Please add one to continue.'
      };
    }
    if (!hasNumbers) {
      return {
        isValid: false,
        error: 'Your password needs at least one number (0-9). Please add one to continue.'
      };
    }
    return {
      isValid: true
    };
  },
  name: function name(value) {
    if (!value) return {
      isValid: true
    };
    var nameRegex = /^[a-zA-Z\s\-']{2,50}$/;
    var isValid = nameRegex.test(value.trim());
    return {
      isValid: isValid,
      error: isValid ? undefined : 'Please enter your name using only letters, spaces, hyphens, and apostrophes (2-50 characters)'
    };
  },
  url: function url(value) {
    if (!value) return {
      isValid: true
    };
    try {
      new URL(value);
      return {
        isValid: true
      };
    } catch (_unused) {
      return {
        isValid: false,
        error: 'Please enter a valid URL'
      };
    }
  },
  number: function number(value) {
    if (!value) return {
      isValid: true
    };
    var isValid = !isNaN(Number(value)) && isFinite(Number(value));
    return {
      isValid: isValid,
      error: isValid ? undefined : 'Please enter a valid number'
    };
  },
  date: function date(value) {
    if (!value) return {
      isValid: true
    };
    var date = new Date(value);
    var isValid = date instanceof Date && !isNaN(date.getTime());
    return {
      isValid: isValid,
      error: isValid ? undefined : 'Please enter a valid date'
    };
  },
  minLength: function minLength(value, _minLength) {
    if (!value) return {
      isValid: true
    };
    var isValid = value.length >= _minLength;
    return {
      isValid: isValid,
      error: isValid ? undefined : `Must be at least ${_minLength} characters long`
    };
  },
  maxLength: function maxLength(value, _maxLength) {
    if (!value) return {
      isValid: true
    };
    var isValid = value.length <= _maxLength;
    return {
      isValid: isValid,
      error: isValid ? undefined : `Must be no more than ${_maxLength} characters long`
    };
  },
  pattern: function pattern(value, _pattern, errorMessage) {
    if (!value) return {
      isValid: true
    };
    var isValid = _pattern.test(value);
    return {
      isValid: isValid,
      error: isValid ? undefined : errorMessage || 'Invalid format'
    };
  },
  confirmPassword: function confirmPassword(value, originalPassword) {
    var isValid = value === originalPassword;
    return {
      isValid: isValid,
      error: isValid ? undefined : 'Passwords do not match'
    };
  }
};
var _validateField = exports.validateField = function validateField(value, validation, formData) {
  if (validation.required) {
    var requiredResult = ValidationRules.required(value);
    if (!requiredResult.isValid) {
      return requiredResult;
    }
  }
  if (!value && !validation.required) {
    return {
      isValid: true
    };
  }
  if (validation.email) {
    var emailResult = ValidationRules.email(value);
    if (!emailResult.isValid) {
      return emailResult;
    }
  }
  if (validation.phone) {
    var phoneResult = ValidationRules.phone(value);
    if (!phoneResult.isValid) {
      return phoneResult;
    }
  }
  if (validation.password) {
    var passwordResult = ValidationRules.password(value);
    if (!passwordResult.isValid) {
      return passwordResult;
    }
  }
  if (validation.name) {
    var nameResult = ValidationRules.name(value);
    if (!nameResult.isValid) {
      return nameResult;
    }
  }
  if (validation.url) {
    var urlResult = ValidationRules.url(value);
    if (!urlResult.isValid) {
      return urlResult;
    }
  }
  if (validation.number) {
    var numberResult = ValidationRules.number(value);
    if (!numberResult.isValid) {
      return numberResult;
    }
  }
  if (validation.date) {
    var dateResult = ValidationRules.date(value);
    if (!dateResult.isValid) {
      return dateResult;
    }
  }
  if (validation.minLength !== undefined) {
    var minLengthResult = ValidationRules.minLength(value, validation.minLength);
    if (!minLengthResult.isValid) {
      return minLengthResult;
    }
  }
  if (validation.maxLength !== undefined) {
    var maxLengthResult = ValidationRules.maxLength(value, validation.maxLength);
    if (!maxLengthResult.isValid) {
      return maxLengthResult;
    }
  }
  if (validation.pattern) {
    var patternResult = ValidationRules.pattern(value, validation.pattern);
    if (!patternResult.isValid) {
      return patternResult;
    }
  }
  if (validation.confirmPassword && formData) {
    var originalPassword = formData[validation.confirmPassword];
    var confirmResult = ValidationRules.confirmPassword(value, originalPassword);
    if (!confirmResult.isValid) {
      return confirmResult;
    }
  }
  if (validation.custom) {
    var customResult = validation.custom(value);
    if (!customResult.isValid) {
      return customResult;
    }
  }
  return {
    isValid: true
  };
};
var _validateForm = exports.validateForm = function validateForm(formData, validationConfig) {
  var errors = {};
  var isValid = true;
  Object.keys(validationConfig).forEach(function (fieldName) {
    var fieldValue = formData[fieldName];
    var fieldValidation = validationConfig[fieldName];
    var result = _validateField(fieldValue, fieldValidation, formData);
    if (!result.isValid) {
      errors[fieldName] = result.error;
      isValid = false;
    }
  });
  return {
    isValid: isValid,
    errors: errors
  };
};
var createDebouncedValidator = exports.createDebouncedValidator = function createDebouncedValidator(validationFn) {
  var delay = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 300;
  var timeoutId;
  return function () {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(validationFn, delay);
  };
};
var CommonValidations = exports.CommonValidations = {
  email: {
    required: true,
    email: true
  },
  password: {
    required: true,
    password: true,
    minLength: 8
  },
  confirmPassword: function confirmPassword(passwordFieldName) {
    return {
      required: true,
      confirmPassword: passwordFieldName
    };
  },
  name: {
    required: true,
    name: true,
    minLength: 2,
    maxLength: 50
  },
  phone: {
    required: true,
    phone: true
  },
  required: {
    required: true
  },
  optional: {}
};
var FormValidator = exports.FormValidator = function () {
  function FormValidator(validationConfig) {
    (0, _classCallCheck2.default)(this, FormValidator);
    this.errors = {};
    this.touched = {};
    this.validationConfig = validationConfig;
  }
  return (0, _createClass2.default)(FormValidator, [{
    key: "validateField",
    value: function validateField(fieldName, value, formData) {
      var result = _validateField(value, this.validationConfig[fieldName], formData);
      if (result.isValid) {
        delete this.errors[fieldName];
      } else {
        this.errors[fieldName] = result.error;
      }
      return result;
    }
  }, {
    key: "validateForm",
    value: function validateForm(formData) {
      var result = _validateForm(formData, this.validationConfig);
      this.errors = result.errors;
      return result;
    }
  }, {
    key: "setFieldTouched",
    value: function setFieldTouched(fieldName) {
      var touched = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;
      this.touched[fieldName] = touched;
    }
  }, {
    key: "getFieldError",
    value: function getFieldError(fieldName) {
      return this.touched[fieldName] ? this.errors[fieldName] : undefined;
    }
  }, {
    key: "getErrors",
    value: function getErrors() {
      return this.errors;
    }
  }, {
    key: "isFieldTouched",
    value: function isFieldTouched(fieldName) {
      return this.touched[fieldName] || false;
    }
  }, {
    key: "reset",
    value: function reset() {
      this.errors = {};
      this.touched = {};
    }
  }]);
}();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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