/**
 * Checkout Service
 *
 * Handles checkout flow, payment processing, and Stripe integration
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import {
  CreateCheckoutSessionRequest,
  CreateCheckoutSessionResponse,
  ConfirmPaymentRequest,
  ConfirmPaymentResponse,
  CheckoutSession,
  PaymentIntentResponse,
  CheckoutError,
} from '../types';

// API configuration
const API_BASE_URL =
  process.env.EXPO_PUBLIC_API_URL || 'http://************:8000';

class CheckoutService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = `${API_BASE_URL}/api`;
  }

  /**
   * Create a new checkout session
   */
  async createCheckoutSession(
    request: CreateCheckoutSessionRequest,
  ): Promise<CreateCheckoutSessionResponse> {
    try {
      // First create the booking/order
      const bookingResponse = await fetch(`${this.baseUrl}/orders/checkout/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${await this.getAuthToken()}`,
        },
        body: JSON.stringify({
          booking_date: request.bookingDate,
          booking_time: request.bookingTime,
          customer_notes: request.customerNotes,
          service_id: request.serviceId,
          provider_id: request.providerId,
        }),
      });

      if (!bookingResponse.ok) {
        const errorData = await bookingResponse.json();
        throw new Error(errorData.error || `HTTP error! status: ${bookingResponse.status}`);
      }

      const bookingData = await bookingResponse.json();

      if (!bookingData.success || !bookingData.bookings?.length) {
        throw new Error('Failed to create booking');
      }

      const booking = bookingData.bookings[0];

      // Create payment intent for the booking
      const paymentResponse = await fetch(`${this.baseUrl}/payments/intents/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${await this.getAuthToken()}`,
        },
        body: JSON.stringify({
          amount: booking.total_amount,
          currency: 'CAD',
          booking_id: booking.id,
        }),
      });

      if (!paymentResponse.ok) {
        throw new Error(`Payment intent creation failed! status: ${paymentResponse.status}`);
      }

      const paymentData = await paymentResponse.json();

      return {
        sessionId: booking.id,
        clientSecret: paymentData.client_secret,
        amount: booking.total_amount,
        currency: 'CAD',
        booking: booking,
        paymentIntentId: paymentData.id,
      };
    } catch (error) {
      console.error('Failed to create checkout session:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Get checkout session by ID
   */
  async getCheckoutSession(sessionId: string): Promise<CheckoutSession> {
    try {
      const response = await fetch(
        `${this.baseUrl}/checkout/sessions/${sessionId}/`,
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Failed to get checkout session:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Create Stripe Payment Intent
   */
  async createPaymentIntent(sessionId: string): Promise<PaymentIntentResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/checkout/payment-intent/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ sessionId }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Failed to create payment intent:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Confirm payment and complete booking
   */
  async confirmPayment(
    request: ConfirmPaymentRequest,
  ): Promise<ConfirmPaymentResponse> {
    try {
      const response = await fetch(
        `${this.baseUrl}/checkout/confirm-payment/`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(request),
        },
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Failed to confirm payment:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Cancel checkout session
   */
  async cancelCheckoutSession(sessionId: string): Promise<void> {
    try {
      const response = await fetch(
        `${this.baseUrl}/checkout/sessions/${sessionId}/cancel/`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        },
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    } catch (error) {
      console.error('Failed to cancel checkout session:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Get authentication token
   */
  private async getAuthToken(): Promise<string> {
    // This should integrate with your auth system
    // For now, we'll use a placeholder
    return 'your-auth-token';
  }

  /**
   * Handle API errors
   */
  private handleError(error: any): CheckoutError {
    if (error instanceof Error) {
      return {
        code: 'CHECKOUT_ERROR',
        message: error.message,
        details: { originalError: error },
      };
    }

    return {
      code: 'UNKNOWN_ERROR',
      message: 'An unknown error occurred during checkout',
      details: { error },
    };
  }
}

// Export singleton instance
export const checkoutService = new CheckoutService();
