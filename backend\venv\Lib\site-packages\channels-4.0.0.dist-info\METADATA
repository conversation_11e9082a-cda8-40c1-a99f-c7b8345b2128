Metadata-Version: 2.1
Name: channels
Version: 4.0.0
Summary: Brings async, event-driven capabilities to Django 3.2 and up.
Home-page: http://github.com/django/channels
Author: Django Software Foundation
Author-email: <EMAIL>
License: BSD
Platform: UNKNOWN
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Framework :: Django
Classifier: Framework :: Django :: 3
Classifier: Framework :: Django :: 3.2
Classifier: Framework :: Django :: 4
Classifier: Framework :: Django :: 4.0
Classifier: Framework :: Django :: 4.1
Classifier: Topic :: Internet :: WWW/HTTP
Requires-Python: >=3.7
License-File: LICENSE
Requires-Dist: Django (>=3.2)
Requires-Dist: asgiref (<4,>=3.5.0)
Provides-Extra: daphne
Requires-Dist: daphne (>=4.0.0) ; extra == 'daphne'
Provides-Extra: tests
Requires-Dist: pytest ; extra == 'tests'
Requires-Dist: pytest-django ; extra == 'tests'
Requires-Dist: pytest-asyncio ; extra == 'tests'
Requires-Dist: async-timeout ; extra == 'tests'
Requires-Dist: coverage (~=4.5) ; extra == 'tests'

UNKNOWN

