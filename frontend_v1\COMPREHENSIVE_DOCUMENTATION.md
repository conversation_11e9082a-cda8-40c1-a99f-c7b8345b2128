# Vierla Frontend V2 - Comprehensive Documentation

## Table of Contents

1. [Project Overview](#project-overview)
2. [Architecture](#architecture)
3. [Getting Started](#getting-started)
4. [Development Guidelines](#development-guidelines)
5. [Testing Strategy](#testing-strategy)
6. [Performance Optimization](#performance-optimization)
7. [Accessibility](#accessibility)
8. [Error Handling](#error-handling)
9. [Bundle Optimization](#bundle-optimization)
10. [Deployment](#deployment)
11. [Troubleshooting](#troubleshooting)
12. [Contributing](#contributing)

## Project Overview

Vierla Frontend V2 is a comprehensive React Native application built with Expo, designed to provide a seamless service marketplace experience. The application follows modern development practices with a focus on performance, accessibility, and maintainability.

### Key Features

- **Service Marketplace**: Browse and book various services
- **User Authentication**: Secure login and registration system
- **Real-time Communication**: Chat and notifications
- **Accessibility First**: WCAG 2.2 AA compliant
- **Performance Optimized**: Bundle size optimized and fast loading
- **Comprehensive Testing**: Unit, integration, and accessibility tests
- **Error Handling**: Robust error management and user feedback
- **Multi-language Support**: Internationalization ready

### Technology Stack

- **Framework**: React Native with Expo SDK 53
- **Language**: TypeScript
- **State Management**: Redux Toolkit
- **Navigation**: React Navigation v6
- **Styling**: StyleSheet with design tokens
- **Testing**: Jest + React Native Testing Library
- **Build Tools**: Metro bundler with optimizations
- **Development**: ESLint, Prettier, Husky

## Architecture

### Project Structure

```
frontend_v1/
├── src/
│   ├── components/          # Reusable UI components
│   │   ├── ui/             # Basic UI components
│   │   ├── forms/          # Form components
│   │   ├── navigation/     # Navigation components
│   │   ├── error/          # Error handling components
│   │   └── accessibility/  # Accessibility testing tools
│   ├── screens/            # Screen components
│   ├── navigation/         # Navigation configuration
│   ├── contexts/           # React contexts
│   ├── hooks/              # Custom hooks
│   ├── utils/              # Utility functions
│   ├── constants/          # App constants
│   ├── types/              # TypeScript type definitions
│   └── __tests__/          # Test files
├── assets/                 # Static assets
├── scripts/                # Build and optimization scripts
├── docs/                   # Additional documentation
└── old_code/              # Archived unused code
```

### Design Patterns

1. **Component Composition**: Reusable components with clear interfaces
2. **Custom Hooks**: Business logic separated into reusable hooks
3. **Context Providers**: Global state management for themes, i18n, etc.
4. **Error Boundaries**: Comprehensive error catching and handling
5. **Accessibility First**: All components built with accessibility in mind

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn
- Expo CLI
- iOS Simulator (for iOS development)
- Android Studio (for Android development)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd vierla-codebase/frontend_v1
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm start
   ```

4. **Run on specific platform**
   ```bash
   npm run ios     # iOS simulator
   npm run android # Android emulator
   npm run web     # Web browser
   ```

### Environment Setup

1. **Create environment file**
   ```bash
   cp .env.example .env
   ```

2. **Configure environment variables**
   ```
   API_BASE_URL=http://************:8000
   EXPO_PUBLIC_API_URL=http://************:8000
   ```

## Development Guidelines

### Code Style

- **TypeScript**: Strict mode enabled with comprehensive type checking
- **ESLint**: Enforced code quality rules
- **Prettier**: Consistent code formatting
- **Naming Conventions**: 
  - Components: PascalCase
  - Files: camelCase or kebab-case
  - Constants: UPPER_SNAKE_CASE

### Component Development

1. **Create accessible components**
   ```typescript
   import { createAccessibleProps } from '../utils/accessibility';
   
   const MyButton = ({ label, onPress, disabled }) => (
     <TouchableOpacity
       {...createAccessibleProps.button(label, 'Double tap to activate', disabled)}
       onPress={onPress}
       disabled={disabled}
     >
       <Text>{label}</Text>
     </TouchableOpacity>
   );
   ```

2. **Use error handling**
   ```typescript
   import { useErrorHandler } from '../hooks/useErrorHandler';
   
   const MyComponent = () => {
     const { handleAsyncError } = useErrorHandler();
     
     const fetchData = () => handleAsyncError(async () => {
       const response = await api.getData();
       return response.data;
     });
   };
   ```

3. **Implement proper testing**
   ```typescript
   import { render, fireEvent } from '@testing-library/react-native';
   import { MyComponent } from '../MyComponent';
   
   describe('MyComponent', () => {
     it('should render correctly', () => {
       const { getByText } = render(<MyComponent />);
       expect(getByText('Expected Text')).toBeTruthy();
     });
   });
   ```

## Testing Strategy

### Test Types

1. **Unit Tests**: Individual component and utility testing
2. **Integration Tests**: Component interaction testing
3. **Accessibility Tests**: WCAG compliance validation
4. **Performance Tests**: Bundle size and load time validation

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test -- MyComponent.test.tsx

# Run accessibility tests
npm test -- accessibility.test.ts
```

### Test Coverage Requirements

- **Minimum Coverage**: 80% for all code
- **Critical Components**: 95% coverage required
- **Accessibility**: 100% coverage for accessibility utilities

## Performance Optimization

### Bundle Optimization

The project includes comprehensive bundle optimization:

1. **Dependency Analysis**
   ```bash
   npm run analyze:bundle
   ```

2. **Bundle Optimization**
   ```bash
   npm run optimize:bundle
   ```

3. **Image Optimization**
   ```bash
   npm run optimize:images
   ```

### Performance Monitoring

- **Bundle Size**: Monitored and optimized (reduced from 806MB to 368MB)
- **Load Times**: Measured and optimized
- **Memory Usage**: Profiled and optimized
- **Render Performance**: React DevTools profiling

### Optimization Techniques

1. **Code Splitting**: Dynamic imports for large components
2. **Tree Shaking**: Unused code elimination
3. **Image Optimization**: WebP format and proper sizing
4. **Lazy Loading**: Components loaded on demand
5. **Memoization**: React.memo and useMemo for expensive operations

## Accessibility

### WCAG 2.2 AA Compliance

The application is built with accessibility as a first-class citizen:

1. **Screen Reader Support**: Full VoiceOver and TalkBack compatibility
2. **Keyboard Navigation**: Complete keyboard accessibility
3. **Color Contrast**: WCAG AA compliant color ratios
4. **Touch Targets**: Minimum 44x44 pixel touch targets
5. **Focus Management**: Proper focus handling and indication

### Accessibility Testing

```bash
# Run accessibility tests
npm test -- accessibility.test.ts

# Use accessibility tester component
// Available in development builds for manual testing
```

### Accessibility Features

1. **Live Regions**: Dynamic content announcements
2. **Form Validation**: Accessible error messaging
3. **Progress Indicators**: Screen reader progress updates
4. **Semantic Markup**: Proper ARIA roles and labels

## Error Handling

### Comprehensive Error Management

1. **Error Boundaries**: React error boundary implementation
2. **Global Error Handler**: Centralized error processing
3. **User Feedback**: Accessible error messages and recovery options
4. **Logging**: Comprehensive error logging and reporting

### Error Types

- **Network Errors**: Connection and API failures
- **Validation Errors**: Form and input validation
- **Authentication Errors**: Login and session management
- **Critical Errors**: System failures with recovery options

### Error Handling Usage

```typescript
import { useErrorHandler } from '../hooks/useErrorHandler';

const MyComponent = () => {
  const { handleNetworkError, handleValidationError } = useErrorHandler();
  
  const submitForm = async (data) => {
    try {
      await api.submit(data);
    } catch (error) {
      if (error.code === 'NETWORK_ERROR') {
        handleNetworkError(error);
      } else {
        handleValidationError('Please check your input');
      }
    }
  };
};
```

## Bundle Optimization

### Optimization Results

- **Before**: 806.29 MB total bundle size
- **After**: 368.73 MB total bundle size
- **Reduction**: 54% size reduction

### Key Optimizations

1. **Removed Unused Dependencies**: Eliminated @shopify/react-native-skia (411MB)
2. **Tree Shaking**: Optimized imports and exports
3. **Code Splitting**: Dynamic component loading
4. **Asset Optimization**: Image compression and format optimization

### Monitoring Tools

- **Bundle Analyzer**: Detailed dependency analysis
- **Performance Metrics**: Load time and memory usage tracking
- **Optimization Reports**: Automated optimization suggestions

## Deployment

### Build Process

1. **Pre-build Checks**
   ```bash
   npm run prebuild  # Runs type-check, lint, test, and optimization
   ```

2. **Production Build**
   ```bash
   npm run build:production
   ```

3. **Platform-specific Builds**
   ```bash
   expo build:ios
   expo build:android
   ```

### Environment Configuration

- **Development**: Local development with hot reloading
- **Staging**: Pre-production testing environment
- **Production**: Optimized production build

## Troubleshooting

### Common Issues

1. **Metro Bundle Issues**
   ```bash
   npm run clean  # Clear cache and reinstall
   ```

2. **iOS Build Issues**
   ```bash
   cd ios && pod install  # Reinstall iOS dependencies
   ```

3. **Android Build Issues**
   ```bash
   cd android && ./gradlew clean  # Clean Android build
   ```

### Performance Issues

1. **Slow Bundle Loading**: Run bundle optimization
2. **Memory Leaks**: Check component cleanup and subscriptions
3. **Render Performance**: Use React DevTools profiler

### Accessibility Issues

1. **Screen Reader Problems**: Test with accessibility tester component
2. **Focus Issues**: Verify focus management implementation
3. **Color Contrast**: Use built-in contrast validation tools

## Contributing

### Development Workflow

1. **Create Feature Branch**
   ```bash
   git checkout -b feature/new-feature
   ```

2. **Follow Development Guidelines**
   - Write tests for new features
   - Ensure accessibility compliance
   - Add proper error handling
   - Update documentation

3. **Run Quality Checks**
   ```bash
   npm run prebuild  # Runs all quality checks
   ```

4. **Submit Pull Request**
   - Include comprehensive description
   - Add screenshots for UI changes
   - Ensure all tests pass

### Code Review Checklist

- [ ] TypeScript types are properly defined
- [ ] Components are accessible
- [ ] Error handling is implemented
- [ ] Tests are written and passing
- [ ] Performance impact is considered
- [ ] Documentation is updated

---

For more detailed information, see the individual documentation files in the `docs/` directory.
