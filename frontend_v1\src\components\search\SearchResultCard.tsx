/**
 * Search Result Card Component
 *
 * Component Contract:
 * - Displays search results in an optimized card format
 * - Shows essential information without clutter (minimalist design)
 * - Provides quick actions like favorite, book, and view details
 * - Implements accessibility features and proper touch targets
 * - Supports different result types (providers, services)
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useCallback } from 'react';
import { View, Text, TouchableOpacity, Image } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { useTheme } from '../../contexts/ThemeContext';
import { usePerformance } from '../../hooks/usePerformance';
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
} from '../../utils/responsiveUtils';

interface SearchResult {
  id: string;
  type: 'provider' | 'service';
  title: string;
  subtitle?: string;
  description?: string;
  image?: string;
  rating?: number;
  reviewCount?: number;
  price?: number | { min: number; max: number };
  distance?: number;
  isAvailable?: boolean;
  isFavorite?: boolean;
  badges?: string[];
}

interface SearchResultCardProps {
  result: SearchResult;
  onPress: (result: SearchResult) => void;
  onFavoritePress?: (result: SearchResult) => void;
  onQuickBookPress?: (result: SearchResult) => void;
  showQuickActions?: boolean;
  compact?: boolean;
}

export const SearchResultCard: React.FC<SearchResultCardProps> = ({
  result,
  onPress,
  onFavoritePress,
  onQuickBookPress,
  showQuickActions = true,
  compact = false,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors, compact);
  const [imageError, setImageError] = useState(false);

  const { trackInteraction } = usePerformance({
    componentName: 'SearchResultCard',
    trackInteractions: true,
  });

  const handlePress = useCallback(() => {
    trackInteraction('resultPress', { resultId: result.id, type: result.type });
    onPress(result);
  }, [onPress, result, trackInteraction]);

  const handleFavoritePress = useCallback(() => {
    trackInteraction('favoritePress', { resultId: result.id, isFavorite: result.isFavorite });
    onFavoritePress?.(result);
  }, [onFavoritePress, result, trackInteraction]);

  const handleQuickBookPress = useCallback(() => {
    trackInteraction('quickBookPress', { resultId: result.id });
    onQuickBookPress?.(result);
  }, [onQuickBookPress, result, trackInteraction]);

  const formatPrice = (price: number | { min: number; max: number } | undefined) => {
    if (!price) return null;
    if (typeof price === 'number') {
      return `$${price}`;
    }
    return `$${price.min}-${price.max}`;
  };

  const renderRating = () => {
    if (!result.rating) return null;
    
    return (
      <View style={styles.ratingContainer}>
        <Ionicons name="star" size={14} color={colors.accent500} />
        <Text style={styles.ratingText}>{result.rating.toFixed(1)}</Text>
        {result.reviewCount && (
          <Text style={styles.reviewCountText}>({result.reviewCount})</Text>
        )}
      </View>
    );
  };

  const renderBadges = () => {
    if (!result.badges?.length) return null;
    
    return (
      <View style={styles.badgesContainer}>
        {result.badges.slice(0, 2).map((badge, index) => (
          <View key={index} style={styles.badge}>
            <Text style={styles.badgeText}>{badge}</Text>
          </View>
        ))}
      </View>
    );
  };

  const renderImage = () => {
    if (!result.image || imageError) {
      return (
        <View style={styles.placeholderImage}>
          <Ionicons
            name={result.type === 'provider' ? 'person' : 'cut'}
            size={compact ? 20 : 24}
            color={colors.text.tertiary}
          />
        </View>
      );
    }

    return (
      <Image
        source={{ uri: result.image }}
        style={styles.image}
        onError={() => setImageError(true)}
        accessibilityIgnoresInvertColors
      />
    );
  };

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={handlePress}
      accessibilityRole="button"
      accessibilityLabel={`${result.title}${result.subtitle ? `, ${result.subtitle}` : ''}`}
      accessibilityHint={`Tap to view details for ${result.title}`}
    >
      <View style={styles.content}>
        {/* Image */}
        <View style={styles.imageContainer}>
          {renderImage()}
          {result.isAvailable !== undefined && (
            <View style={[styles.availabilityIndicator, result.isAvailable ? styles.available : styles.unavailable]}>
              <View style={styles.availabilityDot} />
            </View>
          )}
        </View>

        {/* Main Content */}
        <View style={styles.mainContent}>
          <View style={styles.titleRow}>
            <Text style={styles.title} numberOfLines={1}>
              {result.title}
            </Text>
            {showQuickActions && onFavoritePress && (
              <TouchableOpacity
                style={styles.favoriteButton}
                onPress={handleFavoritePress}
                accessibilityRole="button"
                accessibilityLabel={result.isFavorite ? 'Remove from favorites' : 'Add to favorites'}
              >
                <Ionicons
                  name={result.isFavorite ? 'heart' : 'heart-outline'}
                  size={20}
                  color={result.isFavorite ? colors.error500 : colors.text.secondary}
                />
              </TouchableOpacity>
            )}
          </View>

          {result.subtitle && (
            <Text style={styles.subtitle} numberOfLines={1}>
              {result.subtitle}
            </Text>
          )}

          {!compact && result.description && (
            <Text style={styles.description} numberOfLines={2}>
              {result.description}
            </Text>
          )}

          {/* Metadata Row */}
          <View style={styles.metadataRow}>
            {renderRating()}
            
            {result.distance && (
              <View style={styles.distanceContainer}>
                <Ionicons name="location-outline" size={12} color={colors.text.secondary} />
                <Text style={styles.distanceText}>{result.distance}km</Text>
              </View>
            )}

            {formatPrice(result.price) && (
              <Text style={styles.priceText}>{formatPrice(result.price)}</Text>
            )}
          </View>

          {renderBadges()}
        </View>
      </View>

      {/* Quick Actions */}
      {showQuickActions && onQuickBookPress && result.type === 'provider' && (
        <TouchableOpacity
          style={styles.quickBookButton}
          onPress={handleQuickBookPress}
          accessibilityRole="button"
          accessibilityLabel={`Quick book with ${result.title}`}
        >
          <Text style={styles.quickBookText}>Book</Text>
        </TouchableOpacity>
      )}
    </TouchableOpacity>
  );
};

// Styles
const createStyles = (colors: any, compact: boolean) => ({
  container: {
    backgroundColor: colors.background.primary,
    borderRadius: 12,
    marginVertical: getResponsiveSpacing(6),
    marginHorizontal: getResponsiveSpacing(16),
    shadowColor: colors.shadow.default,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  content: {
    flexDirection: 'row',
    padding: getResponsiveSpacing(compact ? 12 : 16),
  },
  imageContainer: {
    position: 'relative',
    marginRight: getResponsiveSpacing(12),
  },
  image: {
    width: compact ? 60 : 80,
    height: compact ? 60 : 80,
    borderRadius: 8,
  },
  placeholderImage: {
    width: compact ? 60 : 80,
    height: compact ? 60 : 80,
    borderRadius: 8,
    backgroundColor: colors.background.secondary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  availabilityIndicator: {
    position: 'absolute',
    top: 6,
    right: 6,
    width: 12,
    height: 12,
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
  },
  available: {
    backgroundColor: colors.success500,
  },
  unavailable: {
    backgroundColor: colors.error500,
  },
  availabilityDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: colors.background.primary,
  },
  mainContent: {
    flex: 1,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: getResponsiveSpacing(4),
  },
  title: {
    flex: 1,
    fontSize: getResponsiveFontSize(compact ? 14 : 16),
    fontWeight: '600',
    color: colors.text.primary,
    marginRight: getResponsiveSpacing(8),
  },
  favoriteButton: {
    padding: getResponsiveSpacing(4),
  },
  subtitle: {
    fontSize: getResponsiveFontSize(compact ? 12 : 14),
    color: colors.text.secondary,
    marginBottom: getResponsiveSpacing(compact ? 4 : 6),
  },
  description: {
    fontSize: getResponsiveFontSize(12),
    color: colors.text.secondary,
    lineHeight: 16,
    marginBottom: getResponsiveSpacing(8),
  },
  metadataRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: getResponsiveSpacing(8),
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: getResponsiveSpacing(12),
  },
  ratingText: {
    fontSize: getResponsiveFontSize(12),
    fontWeight: '500',
    color: colors.text.primary,
    marginLeft: getResponsiveSpacing(2),
  },
  reviewCountText: {
    fontSize: getResponsiveFontSize(12),
    color: colors.text.secondary,
    marginLeft: getResponsiveSpacing(2),
  },
  distanceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: getResponsiveSpacing(12),
  },
  distanceText: {
    fontSize: getResponsiveFontSize(12),
    color: colors.text.secondary,
    marginLeft: getResponsiveSpacing(2),
  },
  priceText: {
    fontSize: getResponsiveFontSize(12),
    fontWeight: '600',
    color: colors.sage600,
    marginLeft: 'auto',
  },
  badgesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  badge: {
    backgroundColor: colors.sage100,
    paddingHorizontal: getResponsiveSpacing(6),
    paddingVertical: getResponsiveSpacing(2),
    borderRadius: 4,
    marginRight: getResponsiveSpacing(4),
    marginBottom: getResponsiveSpacing(2),
  },
  badgeText: {
    fontSize: getResponsiveFontSize(10),
    color: colors.sage700,
    fontWeight: '500',
  },
  quickBookButton: {
    backgroundColor: colors.sage600,
    paddingHorizontal: getResponsiveSpacing(16),
    paddingVertical: getResponsiveSpacing(8),
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
    alignItems: 'center',
  },
  quickBookText: {
    fontSize: getResponsiveFontSize(14),
    fontWeight: '600',
    color: colors.background.primary,
  },
});

export default SearchResultCard;
