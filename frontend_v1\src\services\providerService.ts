/**
 * Provider Service - Backend Integration for Provider Features
 *
 * Service Contract:
 * - Handles all provider-related API calls
 * - Manages provider profiles and services
 * - Provides provider search and filtering
 * - Implements proper error handling and caching
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { apiClient } from './apiClient';
import type { ApiResponse } from './apiClient';

// Types for Provider Service
export interface Provider {
  id: string;
  name: string;
  businessName: string;
  description: string;
  avatar: string | null;
  coverImage: string | null;
  rating: number;
  reviewCount: number;
  isVerified: boolean;
  isOnline: boolean;
  categories: string[];
  location: {
    address: string;
    city: string;
    state: string;
    zipCode: string;
    coordinates: {
      latitude: number;
      longitude: number;
    };
  };
  contact: {
    phone: string;
    email: string;
    website?: string;
  };
  businessHours: {
    [key: string]: {
      open: string;
      close: string;
      isOpen: boolean;
    };
  };
  services: ProviderService[];
  portfolio: {
    id: string;
    imageUrl: string;
    caption: string;
  }[];
  stats: {
    totalBookings: number;
    responseTime: string;
    completionRate: number;
  };
}

export interface ProviderService {
  id: string;
  name: string;
  description: string;
  category: string;
  price: number;
  duration: number;
  isActive: boolean;
  images: string[];
  requirements?: string[];
  addOns?: {
    id: string;
    name: string;
    price: number;
  }[];
}

export interface ProviderSearchFilters {
  category?: string;
  location?: {
    latitude: number;
    longitude: number;
    radius: number;
  };
  priceRange?: {
    min: number;
    max: number;
  };
  rating?: number;
  availability?: {
    date: string;
    time?: string;
  };
  isVerified?: boolean;
  sortBy?: 'rating' | 'distance' | 'price' | 'popularity';
  sortOrder?: 'asc' | 'desc';
}

export interface ProviderSearchResult {
  providers: Provider[];
  totalCount: number;
  hasMore: boolean;
  filters: {
    categories: { id: string; name: string; count: number }[];
    priceRanges: { min: number; max: number; count: number }[];
    ratings: { rating: number; count: number }[];
  };
}

class ProviderService {
  /**
   * Search providers with filters
   */
  async searchProviders(
    query?: string,
    filters?: ProviderSearchFilters,
    page: number = 1,
    limit: number = 20
  ): Promise<ProviderSearchResult> {
    try {
      const params: any = {
        page,
        limit,
      };

      if (query) {
        params.search = query;
      }

      if (filters) {
        if (filters.category) {
          params.category = filters.category;
        }
        if (filters.location) {
          params.lat = filters.location.latitude;
          params.lng = filters.location.longitude;
          params.radius = filters.location.radius;
        }
        if (filters.priceRange) {
          params.price_min = filters.priceRange.min;
          params.price_max = filters.priceRange.max;
        }
        if (filters.rating) {
          params.rating_min = filters.rating;
        }
        if (filters.isVerified !== undefined) {
          params.is_verified = filters.isVerified;
        }
        if (filters.sortBy) {
          params.sort_by = filters.sortBy;
          params.sort_order = filters.sortOrder || 'desc';
        }
      }

      const response = await apiClient.get<ProviderSearchResult>(
        '/api/v1/catalog/providers/',
        params
      );
      return response.data;
    } catch (error: any) {
      console.error('Failed to search providers:', error);
      throw error;
    }
  }

  /**
   * Get provider details by ID
   */
  async getProviderById(providerId: string): Promise<Provider> {
    try {
      const response = await apiClient.get<Provider>(`/api/v1/catalog/providers/${providerId}/`);
      return response.data;
    } catch (error: any) {
      console.error('Failed to fetch provider details:', error);
      throw error;
    }
  }

  /**
   * Get featured providers
   */
  async getFeaturedProviders(limit: number = 10): Promise<Provider[]> {
    try {
      const response = await apiClient.get<{ results: Provider[] }>(
        '/api/v1/catalog/providers/featured/',
        { limit }
      );
      return response.data.results;
    } catch (error: any) {
      console.error('Failed to fetch featured providers:', error);
      return [];
    }
  }

  /**
   * Get providers by category
   */
  async getProvidersByCategory(
    categorySlug: string,
    page: number = 1,
    limit: number = 20
  ): Promise<ProviderSearchResult> {
    try {
      const response = await apiClient.get<ProviderSearchResult>(
        '/api/v1/catalog/providers/',
        {
          category: categorySlug,
          page,
          limit,
        }
      );
      return response.data;
    } catch (error: any) {
      console.error('Failed to fetch providers by category:', error);
      throw error;
    }
  }

  /**
   * Get provider services
   */
  async getProviderServices(providerId: string): Promise<ProviderService[]> {
    try {
      const response = await apiClient.get<{ results: ProviderService[] }>(
        `/api/v1/catalog/providers/${providerId}/services/`
      );
      return response.data.results;
    } catch (error: any) {
      console.error('Failed to fetch provider services:', error);
      return [];
    }
  }

  /**
   * Get provider availability
   */
  async getProviderAvailability(
    providerId: string,
    date: string,
    serviceId?: string
  ): Promise<{
    date: string;
    slots: {
      time: string;
      available: boolean;
      price?: number;
    }[];
  }> {
    try {
      const params: any = { date };
      if (serviceId) {
        params.service_id = serviceId;
      }

      const response = await apiClient.get(
        `/api/v1/catalog/providers/${providerId}/availability/`,
        params
      );
      return response.data;
    } catch (error: any) {
      console.error('Failed to fetch provider availability:', error);
      throw error;
    }
  }

  /**
   * Get provider reviews
   */
  async getProviderReviews(
    providerId: string,
    page: number = 1,
    limit: number = 10
  ): Promise<{
    reviews: {
      id: string;
      customerName: string;
      customerAvatar: string | null;
      rating: number;
      comment: string;
      date: string;
      serviceName: string;
    }[];
    totalCount: number;
    averageRating: number;
    ratingDistribution: { [key: number]: number };
  }> {
    try {
      const response = await apiClient.get(
        `/api/v1/catalog/providers/${providerId}/reviews/`,
        { page, limit }
      );
      return response.data;
    } catch (error: any) {
      console.error('Failed to fetch provider reviews:', error);
      throw error;
    }
  }

  /**
   * Add provider to favorites
   */
  async addToFavorites(providerId: string): Promise<void> {
    try {
      await apiClient.post('/api/v1/customer/favorites/', { provider_id: providerId });
    } catch (error: any) {
      console.error('Failed to add provider to favorites:', error);
      throw error;
    }
  }

  /**
   * Remove provider from favorites
   */
  async removeFromFavorites(providerId: string): Promise<void> {
    try {
      await apiClient.delete(`/api/v1/customer/favorites/${providerId}/`);
    } catch (error: any) {
      console.error('Failed to remove provider from favorites:', error);
      throw error;
    }
  }

  /**
   * Get nearby providers
   */
  async getNearbyProviders(
    latitude: number,
    longitude: number,
    radius: number = 10,
    limit: number = 20
  ): Promise<Provider[]> {
    try {
      const response = await apiClient.get<{ results: Provider[] }>(
        '/api/v1/customer/nearby/providers/',
        {
          lat: latitude,
          lng: longitude,
          radius,
          limit,
        }
      );
      return response.data.results;
    } catch (error: any) {
      console.error('Failed to fetch nearby providers:', error);
      return [];
    }
  }
}

// Export singleton instance
const providerService = new ProviderService();
export default providerService;
