{"version": 3, "names": ["global", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["ErrorUtils.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict\n */\n\nimport type {ErrorUtilsT} from '@react-native/js-polyfills/error-guard';\n\n/**\n * The particular require runtime that we are using looks for a global\n * `ErrorUtils` object and if it exists, then it requires modules with the\n * error handler specified via ErrorUtils.setGlobalHandler by calling the\n * require function with applyWithGuard. Since the require module is loaded\n * before any of the modules, this ErrorUtils must be defined (and the handler\n * set) globally before requiring anything.\n *\n * However, we still want to treat ErrorUtils as a module so that other modules\n * that use it aren't just using a global variable, so simply export the global\n * variable here. ErrorUtils is originally defined in a file named error-guard.js.\n */\nexport default (global.ErrorUtils: ErrorUtilsT);\n"], "mappings": ";;;;iCAwBgBA,MAAM,CAACC,UAAU", "ignoreList": []}