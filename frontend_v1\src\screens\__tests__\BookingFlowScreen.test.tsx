/**
 * Booking Flow Screen Tests - Comprehensive Test Suite
 *
 * Test Coverage:
 * - Complete booking flow from service selection to confirmation
 * - Error handling and recovery scenarios
 * - Performance testing for critical user flows
 * - Accessibility compliance testing
 * - Real-time features integration testing
 * - Payment processing integration testing
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React from 'react';
import { fireEvent, waitFor, act } from '@testing-library/react-native';
import { Alert } from 'react-native';

import { BookingFlowScreen } from '../BookingFlowScreen';
import {
  renderWithIntegrationSetup,
  testBookingFlow,
  performanceTestUtils,
  errorTestUtils,
  accessibilityTestUtils,
  mockApiResponses,
  createTestUser,
  createTestProvider,
} from '../../utils/integrationTestUtils';
import { bookingService } from '../../services/bookingService';
import { paymentService } from '../../services/paymentService';

// Mock services
jest.mock('../../services/bookingService');
jest.mock('../../services/paymentService');
jest.mock('react-native/Libraries/Alert/Alert', () => ({
  alert: jest.fn(),
}));

const mockBookingService = bookingService as jest.Mocked<typeof bookingService>;
const mockPaymentService = paymentService as jest.Mocked<typeof paymentService>;

describe('BookingFlowScreen', () => {
  const mockRoute = {
    params: {
      providerId: 'test_provider_123',
      serviceId: 'test_service_123',
      serviceName: 'Test Service',
      servicePrice: 50.00,
      serviceDuration: 60,
    },
  };

  const mockNavigation = {
    navigate: jest.fn(),
    goBack: jest.fn(),
    replace: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default mock responses
    mockBookingService.createBooking.mockResolvedValue(mockApiResponses.createBooking);
    mockBookingService.getProviderAvailability.mockResolvedValue({
      availableSlots: ['09:00', '10:00', '11:00', '14:00', '15:00'],
      date: '2024-01-15',
    });
    
    mockPaymentService.processPayment.mockResolvedValue({
      success: true,
      transactionId: 'test_transaction_123',
    });
  });

  describe('Service Selection Step', () => {
    it('should display service selection when no service is pre-selected', async () => {
      const routeWithoutService = {
        params: {
          providerId: 'test_provider_123',
        },
      };

      const { getByTestId } = renderWithIntegrationSetup(
        <BookingFlowScreen route={routeWithoutService} navigation={mockNavigation} />
      );

      await waitFor(() => {
        expect(getByTestId('service-selection-step')).toBeTruthy();
      });
    });

    it('should skip service selection when service is pre-selected', async () => {
      const { getByTestId } = renderWithIntegrationSetup(
        <BookingFlowScreen route={mockRoute} navigation={mockNavigation} />
      );

      await waitFor(() => {
        expect(getByTestId('time-slot-selection-step')).toBeTruthy();
      });
    });

    it('should handle service selection and proceed to time slot selection', async () => {
      const routeWithoutService = {
        params: {
          providerId: 'test_provider_123',
        },
      };

      const { getByTestId } = renderWithIntegrationSetup(
        <BookingFlowScreen route={routeWithoutService} navigation={mockNavigation} />
      );

      await testBookingFlow.selectService(getByTestId, 'test_service_123');
      
      expect(getByTestId('time-slot-selection-step')).toBeTruthy();
    });
  });

  describe('Time Slot Selection Step', () => {
    it('should display available time slots', async () => {
      const { getByTestId } = renderWithIntegrationSetup(
        <BookingFlowScreen route={mockRoute} navigation={mockNavigation} />
      );

      await waitFor(() => {
        expect(getByTestId('time-slot-09:00')).toBeTruthy();
        expect(getByTestId('time-slot-10:00')).toBeTruthy();
        expect(getByTestId('time-slot-11:00')).toBeTruthy();
      });
    });

    it('should handle time slot selection and proceed to customer info', async () => {
      const { getByTestId } = renderWithIntegrationSetup(
        <BookingFlowScreen route={mockRoute} navigation={mockNavigation} />
      );

      await testBookingFlow.selectTimeSlot(getByTestId, '10:00');
      
      expect(getByTestId('customer-info-step')).toBeTruthy();
    });

    it('should handle availability loading errors', async () => {
      mockBookingService.getProviderAvailability.mockRejectedValue(
        new Error('Failed to load availability')
      );

      const { getByTestId } = renderWithIntegrationSetup(
        <BookingFlowScreen route={mockRoute} navigation={mockNavigation} />
      );

      await waitFor(() => {
        expect(getByTestId('error-message')).toBeTruthy();
      });
    });
  });

  describe('Customer Information Step', () => {
    it('should pre-fill customer information for logged-in users', async () => {
      const testUser = createTestUser();
      
      const { getByTestId } = renderWithIntegrationSetup(
        <BookingFlowScreen route={mockRoute} navigation={mockNavigation} />,
        {
          store: {
            getState: () => ({
              auth: {
                user: testUser,
                authToken: 'test_token',
                isAuthenticated: true,
              },
            }),
          },
        }
      );

      // Navigate to customer info step
      await testBookingFlow.selectTimeSlot(getByTestId, '10:00');

      await waitFor(() => {
        const firstNameInput = getByTestId('first-name-input');
        expect(firstNameInput.props.value).toBe(testUser.firstName);
      });
    });

    it('should validate required fields', async () => {
      const { getByTestId } = renderWithIntegrationSetup(
        <BookingFlowScreen route={mockRoute} navigation={mockNavigation} />
      );

      // Navigate to customer info step
      await testBookingFlow.selectTimeSlot(getByTestId, '10:00');

      // Try to continue without filling required fields
      const continueButton = getByTestId('continue-button');
      fireEvent.press(continueButton);

      await waitFor(() => {
        expect(getByTestId('validation-error')).toBeTruthy();
      });
    });

    it('should proceed to payment step with valid information', async () => {
      const { getByTestId } = renderWithIntegrationSetup(
        <BookingFlowScreen route={mockRoute} navigation={mockNavigation} />
      );

      // Navigate through steps
      await testBookingFlow.selectTimeSlot(getByTestId, '10:00');
      await testBookingFlow.fillCustomerInfo(getByTestId, {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+**********',
      });

      expect(getByTestId('payment-step')).toBeTruthy();
    });
  });

  describe('Payment Step', () => {
    it('should display payment methods', async () => {
      const { getByTestId } = renderWithIntegrationSetup(
        <BookingFlowScreen route={mockRoute} navigation={mockNavigation} />
      );

      // Navigate to payment step
      await testBookingFlow.selectTimeSlot(getByTestId, '10:00');
      await testBookingFlow.fillCustomerInfo(getByTestId, {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+**********',
      });

      await waitFor(() => {
        expect(getByTestId('payment-methods-list')).toBeTruthy();
      });
    });

    it('should proceed to summary with selected payment method', async () => {
      const { getByTestId } = renderWithIntegrationSetup(
        <BookingFlowScreen route={mockRoute} navigation={mockNavigation} />
      );

      // Navigate through all steps
      await testBookingFlow.selectTimeSlot(getByTestId, '10:00');
      await testBookingFlow.fillCustomerInfo(getByTestId, {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+**********',
      });
      await testBookingFlow.selectPaymentMethod(getByTestId, 'test_payment_method');

      expect(getByTestId('booking-summary-step')).toBeTruthy();
    });
  });

  describe('Booking Confirmation', () => {
    it('should create booking and process payment successfully', async () => {
      const { getByTestId } = renderWithIntegrationSetup(
        <BookingFlowScreen route={mockRoute} navigation={mockNavigation} />
      );

      // Complete entire booking flow
      await testBookingFlow.selectTimeSlot(getByTestId, '10:00');
      await testBookingFlow.fillCustomerInfo(getByTestId, {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+**********',
      });
      await testBookingFlow.selectPaymentMethod(getByTestId, 'test_payment_method');
      await testBookingFlow.confirmBooking(getByTestId);

      expect(mockBookingService.createBooking).toHaveBeenCalledWith({
        providerId: 'test_provider_123',
        serviceId: 'test_service_123',
        scheduledDate: expect.any(String),
        scheduledTime: '10:00',
        customerInfo: expect.objectContaining({
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone: '+**********',
        }),
      });

      expect(mockPaymentService.processPayment).toHaveBeenCalled();
      expect(mockNavigation.replace).toHaveBeenCalledWith('BookingConfirmation', expect.any(Object));
    });

    it('should handle booking creation errors', async () => {
      mockBookingService.createBooking.mockRejectedValue(
        new Error('Failed to create booking')
      );

      const { getByTestId } = renderWithIntegrationSetup(
        <BookingFlowScreen route={mockRoute} navigation={mockNavigation} />
      );

      // Complete booking flow
      await testBookingFlow.selectTimeSlot(getByTestId, '10:00');
      await testBookingFlow.fillCustomerInfo(getByTestId, {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+**********',
      });
      await testBookingFlow.selectPaymentMethod(getByTestId, 'test_payment_method');

      const confirmButton = getByTestId('confirm-booking-button');
      fireEvent.press(confirmButton);

      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith(
          'Booking Failed',
          expect.stringContaining('Failed to create booking')
        );
      });
    });

    it('should handle payment processing errors', async () => {
      mockPaymentService.processPayment.mockRejectedValue(
        new Error('Payment failed')
      );

      const { getByTestId } = renderWithIntegrationSetup(
        <BookingFlowScreen route={mockRoute} navigation={mockNavigation} />
      );

      // Complete booking flow
      await testBookingFlow.selectTimeSlot(getByTestId, '10:00');
      await testBookingFlow.fillCustomerInfo(getByTestId, {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+**********',
      });
      await testBookingFlow.selectPaymentMethod(getByTestId, 'test_payment_method');

      const confirmButton = getByTestId('confirm-booking-button');
      fireEvent.press(confirmButton);

      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith(
          'Booking Failed',
          expect.stringContaining('Payment failed')
        );
      });
    });
  });

  describe('Navigation and Step Management', () => {
    it('should handle back navigation correctly', async () => {
      const { getByTestId } = renderWithIntegrationSetup(
        <BookingFlowScreen route={mockRoute} navigation={mockNavigation} />
      );

      // Navigate to customer info step
      await testBookingFlow.selectTimeSlot(getByTestId, '10:00');

      // Go back
      const backButton = getByTestId('back-button');
      fireEvent.press(backButton);

      await waitFor(() => {
        expect(getByTestId('time-slot-selection-step')).toBeTruthy();
      });
    });

    it('should handle close navigation', async () => {
      const { getByTestId } = renderWithIntegrationSetup(
        <BookingFlowScreen route={mockRoute} navigation={mockNavigation} />
      );

      const closeButton = getByTestId('close-button');
      fireEvent.press(closeButton);

      expect(mockNavigation.goBack).toHaveBeenCalled();
    });
  });

  describe('Performance Testing', () => {
    it('should render within performance bounds', async () => {
      const renderTime = await performanceTestUtils.measureRenderTime(
        <BookingFlowScreen route={mockRoute} navigation={mockNavigation} />
      );

      performanceTestUtils.expectPerformanceWithinBounds(renderTime, 1000); // 1 second
    });

    it('should handle step transitions efficiently', async () => {
      const { getByTestId } = renderWithIntegrationSetup(
        <BookingFlowScreen route={mockRoute} navigation={mockNavigation} />
      );

      const interactionTime = await performanceTestUtils.measureInteractionTime(
        getByTestId,
        'time-slot-10:00'
      );

      performanceTestUtils.expectPerformanceWithinBounds(interactionTime, 500); // 500ms
    });
  });

  describe('Accessibility Testing', () => {
    it('should have proper accessibility labels', async () => {
      const { getByTestId } = renderWithIntegrationSetup(
        <BookingFlowScreen route={mockRoute} navigation={mockNavigation} />
      );

      const accessibilityTestIds = [
        'close-button',
        'back-button',
        'next-button',
        'booking-flow-screen',
      ];

      accessibilityTestUtils.checkAccessibilityLabels(getByTestId, accessibilityTestIds);
    });

    it('should have proper accessibility roles', async () => {
      const { getByTestId } = renderWithIntegrationSetup(
        <BookingFlowScreen route={mockRoute} navigation={mockNavigation} />
      );

      const accessibilityTestIds = [
        'close-button',
        'back-button',
        'next-button',
      ];

      accessibilityTestUtils.checkAccessibilityRoles(getByTestId, accessibilityTestIds);
    });

    it('should meet minimum touch target requirements', async () => {
      const { getByTestId } = renderWithIntegrationSetup(
        <BookingFlowScreen route={mockRoute} navigation={mockNavigation} />
      );

      const touchTargetTestIds = [
        'close-button',
        'back-button',
        'next-button',
      ];

      accessibilityTestUtils.checkMinimumTouchTargets(getByTestId, touchTargetTestIds);
    });
  });
});
