068f12c74b3df1eb90271401e8e39d94
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.performanceMonitor = exports.default = void 0;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var PerformanceMonitorService = function () {
  function PerformanceMonitorService() {
    (0, _classCallCheck2.default)(this, PerformanceMonitorService);
    this.metrics = [];
    this.renderMetrics = new Map();
    this.networkMetrics = [];
    this.memoryMetrics = [];
    this.frameDropCount = 0;
    this.isMonitoring = false;
    this.MAX_METRICS = 1000;
    this.SLOW_RENDER_THRESHOLD = 16;
    this.SLOW_NETWORK_THRESHOLD = 2000;
    this.MEMORY_LEAK_THRESHOLD = 50 * 1024 * 1024;
  }
  return (0, _createClass2.default)(PerformanceMonitorService, [{
    key: "startMonitoring",
    value: function startMonitoring() {
      var _this = this;
      if (this.isMonitoring) return;
      this.isMonitoring = true;
      console.log('📊 Performance Monitor: Started');
      this.monitoringInterval = setInterval(function () {
        _this.collectMemoryMetrics();
        _this.detectMemoryLeaks();
        _this.cleanupOldMetrics();
      }, 5000);
      this.startFrameMonitoring();
    }
  }, {
    key: "stopMonitoring",
    value: function stopMonitoring() {
      if (!this.isMonitoring) return;
      this.isMonitoring = false;
      console.log('📊 Performance Monitor: Stopped');
      if (this.monitoringInterval) {
        clearInterval(this.monitoringInterval);
      }
    }
  }, {
    key: "trackRender",
    value: function trackRender(componentName, renderTime, metadata) {
      var existing = this.renderMetrics.get(componentName);
      if (existing) {
        existing.renderTime = (existing.renderTime + renderTime) / 2;
        existing.reRenders++;
        existing.lastRenderTime = Date.now();
        if (metadata != null && metadata.propsCount) existing.propsCount = metadata.propsCount;
        if (metadata != null && metadata.stateUpdates) existing.stateUpdates += metadata.stateUpdates;
      } else {
        this.renderMetrics.set(componentName, {
          componentName: componentName,
          renderTime: renderTime,
          propsCount: (metadata == null ? void 0 : metadata.propsCount) || 0,
          stateUpdates: (metadata == null ? void 0 : metadata.stateUpdates) || 0,
          reRenders: 1,
          lastRenderTime: Date.now()
        });
      }
      this.addMetric({
        name: 'component_render',
        value: renderTime,
        timestamp: Date.now(),
        category: 'render',
        metadata: Object.assign({
          componentName: componentName
        }, metadata)
      });
      if (renderTime > this.SLOW_RENDER_THRESHOLD) {
        console.warn(`🐌 Slow render detected: ${componentName} took ${renderTime}ms`);
      }
    }
  }, {
    key: "trackNetworkRequest",
    value: function trackNetworkRequest(url, method, responseTime, statusCode) {
      var requestSize = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 0;
      var responseSize = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : 0;
      var cached = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : false;
      var metric = {
        url: url,
        method: method,
        responseTime: responseTime,
        statusCode: statusCode,
        requestSize: requestSize,
        responseSize: responseSize,
        cached: cached,
        timestamp: Date.now()
      };
      this.networkMetrics.push(metric);
      this.addMetric({
        name: 'network_request',
        value: responseTime,
        timestamp: Date.now(),
        category: 'network',
        metadata: {
          url: url,
          method: method,
          statusCode: statusCode,
          cached: cached
        }
      });
      if (responseTime > this.SLOW_NETWORK_THRESHOLD) {
        console.warn(`🐌 Slow network request: ${method} ${url} took ${responseTime}ms`);
      }
      if (this.networkMetrics.length > this.MAX_METRICS) {
        this.networkMetrics = this.networkMetrics.slice(-this.MAX_METRICS / 2);
      }
    }
  }, {
    key: "trackUserInteraction",
    value: function trackUserInteraction(interactionType, responseTime, metadata) {
      this.addMetric({
        name: 'user_interaction',
        value: responseTime,
        timestamp: Date.now(),
        category: 'user_interaction',
        metadata: Object.assign({
          interactionType: interactionType
        }, metadata)
      });
      if (responseTime > 100) {
        console.warn(`🐌 Slow interaction: ${interactionType} took ${responseTime}ms`);
      }
    }
  }, {
    key: "trackNavigation",
    value: function trackNavigation(fromScreen, toScreen, navigationTime, metadata) {
      this.addMetric({
        name: 'navigation',
        value: navigationTime,
        timestamp: Date.now(),
        category: 'navigation',
        metadata: Object.assign({
          fromScreen: fromScreen,
          toScreen: toScreen
        }, metadata)
      });
    }
  }, {
    key: "getPerformanceReport",
    value: function getPerformanceReport() {
      var _this2 = this;
      var now = Date.now();
      var recentMetrics = this.metrics.filter(function (m) {
        return now - m.timestamp < 300000;
      });
      var renderMetrics = recentMetrics.filter(function (m) {
        return m.category === 'render';
      });
      var networkMetrics = recentMetrics.filter(function (m) {
        return m.category === 'network';
      });
      var averageRenderTime = renderMetrics.length > 0 ? renderMetrics.reduce(function (sum, m) {
        return sum + m.value;
      }, 0) / renderMetrics.length : 0;
      var averageNetworkTime = networkMetrics.length > 0 ? networkMetrics.reduce(function (sum, m) {
        return sum + m.value;
      }, 0) / networkMetrics.length : 0;
      var slowComponents = Array.from(this.renderMetrics.values()).filter(function (c) {
        return c.renderTime > _this2.SLOW_RENDER_THRESHOLD;
      }).sort(function (a, b) {
        return b.renderTime - a.renderTime;
      }).slice(0, 10);
      var slowNetworkRequests = this.networkMetrics.filter(function (n) {
        return n.responseTime > _this2.SLOW_NETWORK_THRESHOLD;
      }).sort(function (a, b) {
        return b.responseTime - a.responseTime;
      }).slice(0, 10);
      var cachedRequests = this.networkMetrics.filter(function (n) {
        return n.cached;
      }).length;
      var totalRequests = this.networkMetrics.length;
      var cacheHitRate = totalRequests > 0 ? cachedRequests / totalRequests : 0;
      var latestMemory = this.memoryMetrics[this.memoryMetrics.length - 1];
      var memoryUsage = latestMemory ? latestMemory.usedJSHeapSize : 0;
      var recommendations = this.generateRecommendations({
        averageRenderTime: averageRenderTime,
        averageNetworkTime: averageNetworkTime,
        slowComponents: slowComponents,
        slowNetworkRequests: slowNetworkRequests,
        cacheHitRate: cacheHitRate,
        memoryUsage: memoryUsage
      });
      return {
        summary: {
          averageRenderTime: averageRenderTime,
          averageNetworkTime: averageNetworkTime,
          memoryUsage: memoryUsage,
          frameDrops: this.frameDropCount,
          cacheHitRate: cacheHitRate
        },
        slowComponents: slowComponents,
        slowNetworkRequests: slowNetworkRequests,
        memoryLeaks: this.detectMemoryLeaks(),
        recommendations: recommendations
      };
    }
  }, {
    key: "getMetricsByCategory",
    value: function getMetricsByCategory(category) {
      return this.metrics.filter(function (m) {
        return m.category === category;
      });
    }
  }, {
    key: "clearMetrics",
    value: function clearMetrics() {
      this.metrics = [];
      this.renderMetrics.clear();
      this.networkMetrics = [];
      this.memoryMetrics = [];
      this.frameDropCount = 0;
    }
  }, {
    key: "addMetric",
    value: function addMetric(metric) {
      this.metrics.push(metric);
      if (this.metrics.length > this.MAX_METRICS) {
        this.metrics = this.metrics.slice(-this.MAX_METRICS / 2);
      }
    }
  }, {
    key: "collectMemoryMetrics",
    value: function collectMemoryMetrics() {
      if (typeof performance !== 'undefined' && performance.memory) {
        var memory = performance.memory;
        this.memoryMetrics.push({
          usedJSHeapSize: memory.usedJSHeapSize,
          totalJSHeapSize: memory.totalJSHeapSize,
          jsHeapSizeLimit: memory.jsHeapSizeLimit,
          timestamp: Date.now()
        });
        if (this.memoryMetrics.length > 100) {
          this.memoryMetrics = this.memoryMetrics.slice(-50);
        }
      }
    }
  }, {
    key: "detectMemoryLeaks",
    value: function detectMemoryLeaks() {
      var leaks = [];
      if (this.memoryMetrics.length < 10) return leaks;
      var recent = this.memoryMetrics.slice(-10);
      var growth = recent[recent.length - 1].usedJSHeapSize - recent[0].usedJSHeapSize;
      if (growth > this.MEMORY_LEAK_THRESHOLD) {
        leaks.push(`Memory usage increased by ${Math.round(growth / 1024 / 1024)}MB in recent measurements`);
      }
      for (var _ref of this.renderMetrics.entries()) {
        var _ref2 = (0, _slicedToArray2.default)(_ref, 2);
        var name = _ref2[0];
        var metrics = _ref2[1];
        if (metrics.reRenders > 100) {
          leaks.push(`Component ${name} has ${metrics.reRenders} re-renders`);
        }
      }
      return leaks;
    }
  }, {
    key: "startFrameMonitoring",
    value: function startFrameMonitoring() {
      if (__DEV__) {
        console.log('📊 Frame monitoring started (placeholder)');
      }
    }
  }, {
    key: "cleanupOldMetrics",
    value: function cleanupOldMetrics() {
      var cutoff = Date.now() - 600000;
      this.metrics = this.metrics.filter(function (m) {
        return m.timestamp > cutoff;
      });
    }
  }, {
    key: "generateRecommendations",
    value: function generateRecommendations(data) {
      var recommendations = [];
      if (data.averageRenderTime > this.SLOW_RENDER_THRESHOLD) {
        recommendations.push('Consider optimizing component renders with React.memo or useMemo');
      }
      if (data.slowComponents.length > 0) {
        recommendations.push(`Optimize slow components: ${data.slowComponents.slice(0, 3).map(function (c) {
          return c.componentName;
        }).join(', ')}`);
      }
      if (data.averageNetworkTime > 1000) {
        recommendations.push('Consider implementing request caching or optimizing API endpoints');
      }
      if (data.cacheHitRate < 0.5) {
        recommendations.push('Improve cache hit rate by implementing better caching strategies');
      }
      if (data.memoryUsage > 100 * 1024 * 1024) {
        recommendations.push('High memory usage detected - check for memory leaks');
      }
      if (data.slowNetworkRequests.length > 5) {
        recommendations.push('Multiple slow network requests detected - consider request batching');
      }
      return recommendations;
    }
  }]);
}();
var performanceMonitor = exports.performanceMonitor = new PerformanceMonitorService();
var _default = exports.default = performanceMonitor;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJQZXJmb3JtYW5jZU1vbml0b3JTZXJ2aWNlIiwiX2NsYXNzQ2FsbENoZWNrMiIsImRlZmF1bHQiLCJtZXRyaWNzIiwicmVuZGVyTWV0cmljcyIsIk1hcCIsIm5ldHdvcmtNZXRyaWNzIiwibWVtb3J5TWV0cmljcyIsImZyYW1lRHJvcENvdW50IiwiaXNNb25pdG9yaW5nIiwiTUFYX01FVFJJQ1MiLCJTTE9XX1JFTkRFUl9USFJFU0hPTEQiLCJTTE9XX05FVFdPUktfVEhSRVNIT0xEIiwiTUVNT1JZX0xFQUtfVEhSRVNIT0xEIiwiX2NyZWF0ZUNsYXNzMiIsImtleSIsInZhbHVlIiwic3RhcnRNb25pdG9yaW5nIiwiX3RoaXMiLCJjb25zb2xlIiwibG9nIiwibW9uaXRvcmluZ0ludGVydmFsIiwic2V0SW50ZXJ2YWwiLCJjb2xsZWN0TWVtb3J5TWV0cmljcyIsImRldGVjdE1lbW9yeUxlYWtzIiwiY2xlYW51cE9sZE1ldHJpY3MiLCJzdGFydEZyYW1lTW9uaXRvcmluZyIsInN0b3BNb25pdG9yaW5nIiwiY2xlYXJJbnRlcnZhbCIsInRyYWNrUmVuZGVyIiwiY29tcG9uZW50TmFtZSIsInJlbmRlclRpbWUiLCJtZXRhZGF0YSIsImV4aXN0aW5nIiwiZ2V0IiwicmVSZW5kZXJzIiwibGFzdFJlbmRlclRpbWUiLCJEYXRlIiwibm93IiwicHJvcHNDb3VudCIsInN0YXRlVXBkYXRlcyIsInNldCIsImFkZE1ldHJpYyIsIm5hbWUiLCJ0aW1lc3RhbXAiLCJjYXRlZ29yeSIsIk9iamVjdCIsImFzc2lnbiIsIndhcm4iLCJ0cmFja05ldHdvcmtSZXF1ZXN0IiwidXJsIiwibWV0aG9kIiwicmVzcG9uc2VUaW1lIiwic3RhdHVzQ29kZSIsInJlcXVlc3RTaXplIiwiYXJndW1lbnRzIiwibGVuZ3RoIiwidW5kZWZpbmVkIiwicmVzcG9uc2VTaXplIiwiY2FjaGVkIiwibWV0cmljIiwicHVzaCIsInNsaWNlIiwidHJhY2tVc2VySW50ZXJhY3Rpb24iLCJpbnRlcmFjdGlvblR5cGUiLCJ0cmFja05hdmlnYXRpb24iLCJmcm9tU2NyZWVuIiwidG9TY3JlZW4iLCJuYXZpZ2F0aW9uVGltZSIsImdldFBlcmZvcm1hbmNlUmVwb3J0IiwiX3RoaXMyIiwicmVjZW50TWV0cmljcyIsImZpbHRlciIsIm0iLCJhdmVyYWdlUmVuZGVyVGltZSIsInJlZHVjZSIsInN1bSIsImF2ZXJhZ2VOZXR3b3JrVGltZSIsInNsb3dDb21wb25lbnRzIiwiQXJyYXkiLCJmcm9tIiwidmFsdWVzIiwiYyIsInNvcnQiLCJhIiwiYiIsInNsb3dOZXR3b3JrUmVxdWVzdHMiLCJuIiwiY2FjaGVkUmVxdWVzdHMiLCJ0b3RhbFJlcXVlc3RzIiwiY2FjaGVIaXRSYXRlIiwibGF0ZXN0TWVtb3J5IiwibWVtb3J5VXNhZ2UiLCJ1c2VkSlNIZWFwU2l6ZSIsInJlY29tbWVuZGF0aW9ucyIsImdlbmVyYXRlUmVjb21tZW5kYXRpb25zIiwic3VtbWFyeSIsImZyYW1lRHJvcHMiLCJtZW1vcnlMZWFrcyIsImdldE1ldHJpY3NCeUNhdGVnb3J5IiwiY2xlYXJNZXRyaWNzIiwiY2xlYXIiLCJwZXJmb3JtYW5jZSIsIm1lbW9yeSIsInRvdGFsSlNIZWFwU2l6ZSIsImpzSGVhcFNpemVMaW1pdCIsImxlYWtzIiwicmVjZW50IiwiZ3Jvd3RoIiwiTWF0aCIsInJvdW5kIiwiX3JlZiIsImVudHJpZXMiLCJfcmVmMiIsIl9zbGljZWRUb0FycmF5MiIsIl9fREVWX18iLCJjdXRvZmYiLCJkYXRhIiwibWFwIiwiam9pbiIsInBlcmZvcm1hbmNlTW9uaXRvciIsImV4cG9ydHMiLCJfZGVmYXVsdCJdLCJzb3VyY2VzIjpbInBlcmZvcm1hbmNlTW9uaXRvci50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFBlcmZvcm1hbmNlIE1vbml0b3IgU2VydmljZSAtIFRyYWNrIGFuZCBvcHRpbWl6ZSBhcHAgcGVyZm9ybWFuY2VcbiAqXG4gKiBTZXJ2aWNlIENvbnRyYWN0OlxuICogLSBNb25pdG9ycyByZW5kZXIgcGVyZm9ybWFuY2UgYW5kIGZyYW1lIGRyb3BzXG4gKiAtIFRyYWNrcyBtZW1vcnkgdXNhZ2UgYW5kIGdhcmJhZ2UgY29sbGVjdGlvblxuICogLSBNZWFzdXJlcyBBUEkgcmVzcG9uc2UgdGltZXMgYW5kIG5ldHdvcmsgcGVyZm9ybWFuY2VcbiAqIC0gUHJvdmlkZXMgcGVyZm9ybWFuY2UgYW5hbHl0aWNzIGFuZCBhbGVydHNcbiAqIC0gSW1wbGVtZW50cyBwZXJmb3JtYW5jZSBvcHRpbWl6YXRpb24gc3VnZ2VzdGlvbnNcbiAqXG4gKiBAdmVyc2lvbiAxLjAuMFxuICogQGF1dGhvciBWaWVybGEgRGV2ZWxvcG1lbnQgVGVhbVxuICovXG5cbmludGVyZmFjZSBQZXJmb3JtYW5jZU1ldHJpYyB7XG4gIG5hbWU6IHN0cmluZztcbiAgdmFsdWU6IG51bWJlcjtcbiAgdGltZXN0YW1wOiBudW1iZXI7XG4gIGNhdGVnb3J5OiAncmVuZGVyJyB8ICduZXR3b3JrJyB8ICdtZW1vcnknIHwgJ25hdmlnYXRpb24nIHwgJ3VzZXJfaW50ZXJhY3Rpb24nO1xuICBtZXRhZGF0YT86IFJlY29yZDxzdHJpbmcsIGFueT47XG59XG5cbmludGVyZmFjZSBSZW5kZXJNZXRyaWNzIHtcbiAgY29tcG9uZW50TmFtZTogc3RyaW5nO1xuICByZW5kZXJUaW1lOiBudW1iZXI7XG4gIHByb3BzQ291bnQ6IG51bWJlcjtcbiAgc3RhdGVVcGRhdGVzOiBudW1iZXI7XG4gIHJlUmVuZGVyczogbnVtYmVyO1xuICBsYXN0UmVuZGVyVGltZTogbnVtYmVyO1xufVxuXG5pbnRlcmZhY2UgTmV0d29ya01ldHJpY3Mge1xuICB1cmw6IHN0cmluZztcbiAgbWV0aG9kOiBzdHJpbmc7XG4gIHJlc3BvbnNlVGltZTogbnVtYmVyO1xuICBzdGF0dXNDb2RlOiBudW1iZXI7XG4gIHJlcXVlc3RTaXplOiBudW1iZXI7XG4gIHJlc3BvbnNlU2l6ZTogbnVtYmVyO1xuICBjYWNoZWQ6IGJvb2xlYW47XG4gIHRpbWVzdGFtcDogbnVtYmVyO1xufVxuXG5pbnRlcmZhY2UgTWVtb3J5TWV0cmljcyB7XG4gIHVzZWRKU0hlYXBTaXplOiBudW1iZXI7XG4gIHRvdGFsSlNIZWFwU2l6ZTogbnVtYmVyO1xuICBqc0hlYXBTaXplTGltaXQ6IG51bWJlcjtcbiAgdGltZXN0YW1wOiBudW1iZXI7XG59XG5cbmludGVyZmFjZSBQZXJmb3JtYW5jZVJlcG9ydCB7XG4gIHN1bW1hcnk6IHtcbiAgICBhdmVyYWdlUmVuZGVyVGltZTogbnVtYmVyO1xuICAgIGF2ZXJhZ2VOZXR3b3JrVGltZTogbnVtYmVyO1xuICAgIG1lbW9yeVVzYWdlOiBudW1iZXI7XG4gICAgZnJhbWVEcm9wczogbnVtYmVyO1xuICAgIGNhY2hlSGl0UmF0ZTogbnVtYmVyO1xuICB9O1xuICBzbG93Q29tcG9uZW50czogUmVuZGVyTWV0cmljc1tdO1xuICBzbG93TmV0d29ya1JlcXVlc3RzOiBOZXR3b3JrTWV0cmljc1tdO1xuICBtZW1vcnlMZWFrczogc3RyaW5nW107XG4gIHJlY29tbWVuZGF0aW9uczogc3RyaW5nW107XG59XG5cbmNsYXNzIFBlcmZvcm1hbmNlTW9uaXRvclNlcnZpY2Uge1xuICBwcml2YXRlIG1ldHJpY3M6IFBlcmZvcm1hbmNlTWV0cmljW10gPSBbXTtcbiAgcHJpdmF0ZSByZW5kZXJNZXRyaWNzID0gbmV3IE1hcDxzdHJpbmcsIFJlbmRlck1ldHJpY3M+KCk7XG4gIHByaXZhdGUgbmV0d29ya01ldHJpY3M6IE5ldHdvcmtNZXRyaWNzW10gPSBbXTtcbiAgcHJpdmF0ZSBtZW1vcnlNZXRyaWNzOiBNZW1vcnlNZXRyaWNzW10gPSBbXTtcbiAgcHJpdmF0ZSBmcmFtZURyb3BDb3VudCA9IDA7XG4gIHByaXZhdGUgaXNNb25pdG9yaW5nID0gZmFsc2U7XG4gIHByaXZhdGUgbW9uaXRvcmluZ0ludGVydmFsPzogTm9kZUpTLlRpbWVvdXQ7XG5cbiAgcHJpdmF0ZSByZWFkb25seSBNQVhfTUVUUklDUyA9IDEwMDA7XG4gIHByaXZhdGUgcmVhZG9ubHkgU0xPV19SRU5ERVJfVEhSRVNIT0xEID0gMTY7IC8vIDE2bXMgZm9yIDYwZnBzXG4gIHByaXZhdGUgcmVhZG9ubHkgU0xPV19ORVRXT1JLX1RIUkVTSE9MRCA9IDIwMDA7IC8vIDIgc2Vjb25kc1xuICBwcml2YXRlIHJlYWRvbmx5IE1FTU9SWV9MRUFLX1RIUkVTSE9MRCA9IDUwICogMTAyNCAqIDEwMjQ7IC8vIDUwTUJcblxuICAvKipcbiAgICogU3RhcnQgcGVyZm9ybWFuY2UgbW9uaXRvcmluZ1xuICAgKi9cbiAgc3RhcnRNb25pdG9yaW5nKCk6IHZvaWQge1xuICAgIGlmICh0aGlzLmlzTW9uaXRvcmluZykgcmV0dXJuO1xuXG4gICAgdGhpcy5pc01vbml0b3JpbmcgPSB0cnVlO1xuICAgIGNvbnNvbGUubG9nKCfwn5OKIFBlcmZvcm1hbmNlIE1vbml0b3I6IFN0YXJ0ZWQnKTtcblxuICAgIC8vIE1vbml0b3IgbWVtb3J5IHVzYWdlIHBlcmlvZGljYWxseVxuICAgIHRoaXMubW9uaXRvcmluZ0ludGVydmFsID0gc2V0SW50ZXJ2YWwoKCkgPT4ge1xuICAgICAgdGhpcy5jb2xsZWN0TWVtb3J5TWV0cmljcygpO1xuICAgICAgdGhpcy5kZXRlY3RNZW1vcnlMZWFrcygpO1xuICAgICAgdGhpcy5jbGVhbnVwT2xkTWV0cmljcygpO1xuICAgIH0sIDUwMDApOyAvLyBFdmVyeSA1IHNlY29uZHNcblxuICAgIC8vIE1vbml0b3IgZnJhbWUgZHJvcHMgKGlmIGF2YWlsYWJsZSlcbiAgICB0aGlzLnN0YXJ0RnJhbWVNb25pdG9yaW5nKCk7XG4gIH1cblxuICAvKipcbiAgICogU3RvcCBwZXJmb3JtYW5jZSBtb25pdG9yaW5nXG4gICAqL1xuICBzdG9wTW9uaXRvcmluZygpOiB2b2lkIHtcbiAgICBpZiAoIXRoaXMuaXNNb25pdG9yaW5nKSByZXR1cm47XG5cbiAgICB0aGlzLmlzTW9uaXRvcmluZyA9IGZhbHNlO1xuICAgIGNvbnNvbGUubG9nKCfwn5OKIFBlcmZvcm1hbmNlIE1vbml0b3I6IFN0b3BwZWQnKTtcblxuICAgIGlmICh0aGlzLm1vbml0b3JpbmdJbnRlcnZhbCkge1xuICAgICAgY2xlYXJJbnRlcnZhbCh0aGlzLm1vbml0b3JpbmdJbnRlcnZhbCk7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIFRyYWNrIGNvbXBvbmVudCByZW5kZXIgcGVyZm9ybWFuY2VcbiAgICovXG4gIHRyYWNrUmVuZGVyKGNvbXBvbmVudE5hbWU6IHN0cmluZywgcmVuZGVyVGltZTogbnVtYmVyLCBtZXRhZGF0YT86IFJlY29yZDxzdHJpbmcsIGFueT4pOiB2b2lkIHtcbiAgICBjb25zdCBleGlzdGluZyA9IHRoaXMucmVuZGVyTWV0cmljcy5nZXQoY29tcG9uZW50TmFtZSk7XG4gICAgXG4gICAgaWYgKGV4aXN0aW5nKSB7XG4gICAgICBleGlzdGluZy5yZW5kZXJUaW1lID0gKGV4aXN0aW5nLnJlbmRlclRpbWUgKyByZW5kZXJUaW1lKSAvIDI7IC8vIE1vdmluZyBhdmVyYWdlXG4gICAgICBleGlzdGluZy5yZVJlbmRlcnMrKztcbiAgICAgIGV4aXN0aW5nLmxhc3RSZW5kZXJUaW1lID0gRGF0ZS5ub3coKTtcbiAgICAgIGlmIChtZXRhZGF0YT8ucHJvcHNDb3VudCkgZXhpc3RpbmcucHJvcHNDb3VudCA9IG1ldGFkYXRhLnByb3BzQ291bnQ7XG4gICAgICBpZiAobWV0YWRhdGE/LnN0YXRlVXBkYXRlcykgZXhpc3Rpbmcuc3RhdGVVcGRhdGVzICs9IG1ldGFkYXRhLnN0YXRlVXBkYXRlcztcbiAgICB9IGVsc2Uge1xuICAgICAgdGhpcy5yZW5kZXJNZXRyaWNzLnNldChjb21wb25lbnROYW1lLCB7XG4gICAgICAgIGNvbXBvbmVudE5hbWUsXG4gICAgICAgIHJlbmRlclRpbWUsXG4gICAgICAgIHByb3BzQ291bnQ6IG1ldGFkYXRhPy5wcm9wc0NvdW50IHx8IDAsXG4gICAgICAgIHN0YXRlVXBkYXRlczogbWV0YWRhdGE/LnN0YXRlVXBkYXRlcyB8fCAwLFxuICAgICAgICByZVJlbmRlcnM6IDEsXG4gICAgICAgIGxhc3RSZW5kZXJUaW1lOiBEYXRlLm5vdygpLFxuICAgICAgfSk7XG4gICAgfVxuXG4gICAgLy8gVHJhY2sgYXMgZ2VuZXJhbCBtZXRyaWNcbiAgICB0aGlzLmFkZE1ldHJpYyh7XG4gICAgICBuYW1lOiAnY29tcG9uZW50X3JlbmRlcicsXG4gICAgICB2YWx1ZTogcmVuZGVyVGltZSxcbiAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKSxcbiAgICAgIGNhdGVnb3J5OiAncmVuZGVyJyxcbiAgICAgIG1ldGFkYXRhOiB7IGNvbXBvbmVudE5hbWUsIC4uLm1ldGFkYXRhIH0sXG4gICAgfSk7XG5cbiAgICAvLyBBbGVydCBvbiBzbG93IHJlbmRlcnNcbiAgICBpZiAocmVuZGVyVGltZSA+IHRoaXMuU0xPV19SRU5ERVJfVEhSRVNIT0xEKSB7XG4gICAgICBjb25zb2xlLndhcm4oYPCfkIwgU2xvdyByZW5kZXIgZGV0ZWN0ZWQ6ICR7Y29tcG9uZW50TmFtZX0gdG9vayAke3JlbmRlclRpbWV9bXNgKTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogVHJhY2sgbmV0d29yayByZXF1ZXN0IHBlcmZvcm1hbmNlXG4gICAqL1xuICB0cmFja05ldHdvcmtSZXF1ZXN0KFxuICAgIHVybDogc3RyaW5nLFxuICAgIG1ldGhvZDogc3RyaW5nLFxuICAgIHJlc3BvbnNlVGltZTogbnVtYmVyLFxuICAgIHN0YXR1c0NvZGU6IG51bWJlcixcbiAgICByZXF1ZXN0U2l6ZTogbnVtYmVyID0gMCxcbiAgICByZXNwb25zZVNpemU6IG51bWJlciA9IDAsXG4gICAgY2FjaGVkOiBib29sZWFuID0gZmFsc2VcbiAgKTogdm9pZCB7XG4gICAgY29uc3QgbWV0cmljOiBOZXR3b3JrTWV0cmljcyA9IHtcbiAgICAgIHVybCxcbiAgICAgIG1ldGhvZCxcbiAgICAgIHJlc3BvbnNlVGltZSxcbiAgICAgIHN0YXR1c0NvZGUsXG4gICAgICByZXF1ZXN0U2l6ZSxcbiAgICAgIHJlc3BvbnNlU2l6ZSxcbiAgICAgIGNhY2hlZCxcbiAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKSxcbiAgICB9O1xuXG4gICAgdGhpcy5uZXR3b3JrTWV0cmljcy5wdXNoKG1ldHJpYyk7XG5cbiAgICAvLyBUcmFjayBhcyBnZW5lcmFsIG1ldHJpY1xuICAgIHRoaXMuYWRkTWV0cmljKHtcbiAgICAgIG5hbWU6ICduZXR3b3JrX3JlcXVlc3QnLFxuICAgICAgdmFsdWU6IHJlc3BvbnNlVGltZSxcbiAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKSxcbiAgICAgIGNhdGVnb3J5OiAnbmV0d29yaycsXG4gICAgICBtZXRhZGF0YTogeyB1cmwsIG1ldGhvZCwgc3RhdHVzQ29kZSwgY2FjaGVkIH0sXG4gICAgfSk7XG5cbiAgICAvLyBBbGVydCBvbiBzbG93IHJlcXVlc3RzXG4gICAgaWYgKHJlc3BvbnNlVGltZSA+IHRoaXMuU0xPV19ORVRXT1JLX1RIUkVTSE9MRCkge1xuICAgICAgY29uc29sZS53YXJuKGDwn5CMIFNsb3cgbmV0d29yayByZXF1ZXN0OiAke21ldGhvZH0gJHt1cmx9IHRvb2sgJHtyZXNwb25zZVRpbWV9bXNgKTtcbiAgICB9XG5cbiAgICAvLyBLZWVwIG9ubHkgcmVjZW50IG5ldHdvcmsgbWV0cmljc1xuICAgIGlmICh0aGlzLm5ldHdvcmtNZXRyaWNzLmxlbmd0aCA+IHRoaXMuTUFYX01FVFJJQ1MpIHtcbiAgICAgIHRoaXMubmV0d29ya01ldHJpY3MgPSB0aGlzLm5ldHdvcmtNZXRyaWNzLnNsaWNlKC10aGlzLk1BWF9NRVRSSUNTIC8gMik7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIFRyYWNrIHVzZXIgaW50ZXJhY3Rpb24gcGVyZm9ybWFuY2VcbiAgICovXG4gIHRyYWNrVXNlckludGVyYWN0aW9uKFxuICAgIGludGVyYWN0aW9uVHlwZTogc3RyaW5nLFxuICAgIHJlc3BvbnNlVGltZTogbnVtYmVyLFxuICAgIG1ldGFkYXRhPzogUmVjb3JkPHN0cmluZywgYW55PlxuICApOiB2b2lkIHtcbiAgICB0aGlzLmFkZE1ldHJpYyh7XG4gICAgICBuYW1lOiAndXNlcl9pbnRlcmFjdGlvbicsXG4gICAgICB2YWx1ZTogcmVzcG9uc2VUaW1lLFxuICAgICAgdGltZXN0YW1wOiBEYXRlLm5vdygpLFxuICAgICAgY2F0ZWdvcnk6ICd1c2VyX2ludGVyYWN0aW9uJyxcbiAgICAgIG1ldGFkYXRhOiB7IGludGVyYWN0aW9uVHlwZSwgLi4ubWV0YWRhdGEgfSxcbiAgICB9KTtcblxuICAgIC8vIEFsZXJ0IG9uIHNsb3cgaW50ZXJhY3Rpb25zXG4gICAgaWYgKHJlc3BvbnNlVGltZSA+IDEwMCkgeyAvLyAxMDBtcyB0aHJlc2hvbGQgZm9yIGludGVyYWN0aW9uc1xuICAgICAgY29uc29sZS53YXJuKGDwn5CMIFNsb3cgaW50ZXJhY3Rpb246ICR7aW50ZXJhY3Rpb25UeXBlfSB0b29rICR7cmVzcG9uc2VUaW1lfW1zYCk7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIFRyYWNrIG5hdmlnYXRpb24gcGVyZm9ybWFuY2VcbiAgICovXG4gIHRyYWNrTmF2aWdhdGlvbihcbiAgICBmcm9tU2NyZWVuOiBzdHJpbmcsXG4gICAgdG9TY3JlZW46IHN0cmluZyxcbiAgICBuYXZpZ2F0aW9uVGltZTogbnVtYmVyLFxuICAgIG1ldGFkYXRhPzogUmVjb3JkPHN0cmluZywgYW55PlxuICApOiB2b2lkIHtcbiAgICB0aGlzLmFkZE1ldHJpYyh7XG4gICAgICBuYW1lOiAnbmF2aWdhdGlvbicsXG4gICAgICB2YWx1ZTogbmF2aWdhdGlvblRpbWUsXG4gICAgICB0aW1lc3RhbXA6IERhdGUubm93KCksXG4gICAgICBjYXRlZ29yeTogJ25hdmlnYXRpb24nLFxuICAgICAgbWV0YWRhdGE6IHsgZnJvbVNjcmVlbiwgdG9TY3JlZW4sIC4uLm1ldGFkYXRhIH0sXG4gICAgfSk7XG4gIH1cblxuICAvKipcbiAgICogR2V0IHBlcmZvcm1hbmNlIHJlcG9ydFxuICAgKi9cbiAgZ2V0UGVyZm9ybWFuY2VSZXBvcnQoKTogUGVyZm9ybWFuY2VSZXBvcnQge1xuICAgIGNvbnN0IG5vdyA9IERhdGUubm93KCk7XG4gICAgY29uc3QgcmVjZW50TWV0cmljcyA9IHRoaXMubWV0cmljcy5maWx0ZXIobSA9PiBub3cgLSBtLnRpbWVzdGFtcCA8IDMwMDAwMCk7IC8vIExhc3QgNSBtaW51dGVzXG5cbiAgICAvLyBDYWxjdWxhdGUgYXZlcmFnZXNcbiAgICBjb25zdCByZW5kZXJNZXRyaWNzID0gcmVjZW50TWV0cmljcy5maWx0ZXIobSA9PiBtLmNhdGVnb3J5ID09PSAncmVuZGVyJyk7XG4gICAgY29uc3QgbmV0d29ya01ldHJpY3MgPSByZWNlbnRNZXRyaWNzLmZpbHRlcihtID0+IG0uY2F0ZWdvcnkgPT09ICduZXR3b3JrJyk7XG4gICAgXG4gICAgY29uc3QgYXZlcmFnZVJlbmRlclRpbWUgPSByZW5kZXJNZXRyaWNzLmxlbmd0aCA+IDAgXG4gICAgICA/IHJlbmRlck1ldHJpY3MucmVkdWNlKChzdW0sIG0pID0+IHN1bSArIG0udmFsdWUsIDApIC8gcmVuZGVyTWV0cmljcy5sZW5ndGggXG4gICAgICA6IDA7XG5cbiAgICBjb25zdCBhdmVyYWdlTmV0d29ya1RpbWUgPSBuZXR3b3JrTWV0cmljcy5sZW5ndGggPiAwXG4gICAgICA/IG5ldHdvcmtNZXRyaWNzLnJlZHVjZSgoc3VtLCBtKSA9PiBzdW0gKyBtLnZhbHVlLCAwKSAvIG5ldHdvcmtNZXRyaWNzLmxlbmd0aFxuICAgICAgOiAwO1xuXG4gICAgLy8gR2V0IHNsb3cgY29tcG9uZW50c1xuICAgIGNvbnN0IHNsb3dDb21wb25lbnRzID0gQXJyYXkuZnJvbSh0aGlzLnJlbmRlck1ldHJpY3MudmFsdWVzKCkpXG4gICAgICAuZmlsdGVyKGMgPT4gYy5yZW5kZXJUaW1lID4gdGhpcy5TTE9XX1JFTkRFUl9USFJFU0hPTEQpXG4gICAgICAuc29ydCgoYSwgYikgPT4gYi5yZW5kZXJUaW1lIC0gYS5yZW5kZXJUaW1lKVxuICAgICAgLnNsaWNlKDAsIDEwKTtcblxuICAgIC8vIEdldCBzbG93IG5ldHdvcmsgcmVxdWVzdHNcbiAgICBjb25zdCBzbG93TmV0d29ya1JlcXVlc3RzID0gdGhpcy5uZXR3b3JrTWV0cmljc1xuICAgICAgLmZpbHRlcihuID0+IG4ucmVzcG9uc2VUaW1lID4gdGhpcy5TTE9XX05FVFdPUktfVEhSRVNIT0xEKVxuICAgICAgLnNvcnQoKGEsIGIpID0+IGIucmVzcG9uc2VUaW1lIC0gYS5yZXNwb25zZVRpbWUpXG4gICAgICAuc2xpY2UoMCwgMTApO1xuXG4gICAgLy8gQ2FsY3VsYXRlIGNhY2hlIGhpdCByYXRlXG4gICAgY29uc3QgY2FjaGVkUmVxdWVzdHMgPSB0aGlzLm5ldHdvcmtNZXRyaWNzLmZpbHRlcihuID0+IG4uY2FjaGVkKS5sZW5ndGg7XG4gICAgY29uc3QgdG90YWxSZXF1ZXN0cyA9IHRoaXMubmV0d29ya01ldHJpY3MubGVuZ3RoO1xuICAgIGNvbnN0IGNhY2hlSGl0UmF0ZSA9IHRvdGFsUmVxdWVzdHMgPiAwID8gY2FjaGVkUmVxdWVzdHMgLyB0b3RhbFJlcXVlc3RzIDogMDtcblxuICAgIC8vIEdldCBjdXJyZW50IG1lbW9yeSB1c2FnZVxuICAgIGNvbnN0IGxhdGVzdE1lbW9yeSA9IHRoaXMubWVtb3J5TWV0cmljc1t0aGlzLm1lbW9yeU1ldHJpY3MubGVuZ3RoIC0gMV07XG4gICAgY29uc3QgbWVtb3J5VXNhZ2UgPSBsYXRlc3RNZW1vcnkgPyBsYXRlc3RNZW1vcnkudXNlZEpTSGVhcFNpemUgOiAwO1xuXG4gICAgLy8gR2VuZXJhdGUgcmVjb21tZW5kYXRpb25zXG4gICAgY29uc3QgcmVjb21tZW5kYXRpb25zID0gdGhpcy5nZW5lcmF0ZVJlY29tbWVuZGF0aW9ucyh7XG4gICAgICBhdmVyYWdlUmVuZGVyVGltZSxcbiAgICAgIGF2ZXJhZ2VOZXR3b3JrVGltZSxcbiAgICAgIHNsb3dDb21wb25lbnRzLFxuICAgICAgc2xvd05ldHdvcmtSZXF1ZXN0cyxcbiAgICAgIGNhY2hlSGl0UmF0ZSxcbiAgICAgIG1lbW9yeVVzYWdlLFxuICAgIH0pO1xuXG4gICAgcmV0dXJuIHtcbiAgICAgIHN1bW1hcnk6IHtcbiAgICAgICAgYXZlcmFnZVJlbmRlclRpbWUsXG4gICAgICAgIGF2ZXJhZ2VOZXR3b3JrVGltZSxcbiAgICAgICAgbWVtb3J5VXNhZ2UsXG4gICAgICAgIGZyYW1lRHJvcHM6IHRoaXMuZnJhbWVEcm9wQ291bnQsXG4gICAgICAgIGNhY2hlSGl0UmF0ZSxcbiAgICAgIH0sXG4gICAgICBzbG93Q29tcG9uZW50cyxcbiAgICAgIHNsb3dOZXR3b3JrUmVxdWVzdHMsXG4gICAgICBtZW1vcnlMZWFrczogdGhpcy5kZXRlY3RNZW1vcnlMZWFrcygpLFxuICAgICAgcmVjb21tZW5kYXRpb25zLFxuICAgIH07XG4gIH1cblxuICAvKipcbiAgICogR2V0IG1ldHJpY3MgYnkgY2F0ZWdvcnlcbiAgICovXG4gIGdldE1ldHJpY3NCeUNhdGVnb3J5KGNhdGVnb3J5OiBQZXJmb3JtYW5jZU1ldHJpY1snY2F0ZWdvcnknXSk6IFBlcmZvcm1hbmNlTWV0cmljW10ge1xuICAgIHJldHVybiB0aGlzLm1ldHJpY3MuZmlsdGVyKG0gPT4gbS5jYXRlZ29yeSA9PT0gY2F0ZWdvcnkpO1xuICB9XG5cbiAgLyoqXG4gICAqIENsZWFyIGFsbCBtZXRyaWNzXG4gICAqL1xuICBjbGVhck1ldHJpY3MoKTogdm9pZCB7XG4gICAgdGhpcy5tZXRyaWNzID0gW107XG4gICAgdGhpcy5yZW5kZXJNZXRyaWNzLmNsZWFyKCk7XG4gICAgdGhpcy5uZXR3b3JrTWV0cmljcyA9IFtdO1xuICAgIHRoaXMubWVtb3J5TWV0cmljcyA9IFtdO1xuICAgIHRoaXMuZnJhbWVEcm9wQ291bnQgPSAwO1xuICB9XG5cbiAgLyoqXG4gICAqIEFkZCBhIHBlcmZvcm1hbmNlIG1ldHJpY1xuICAgKi9cbiAgcHJpdmF0ZSBhZGRNZXRyaWMobWV0cmljOiBQZXJmb3JtYW5jZU1ldHJpYyk6IHZvaWQge1xuICAgIHRoaXMubWV0cmljcy5wdXNoKG1ldHJpYyk7XG5cbiAgICAvLyBLZWVwIG9ubHkgcmVjZW50IG1ldHJpY3NcbiAgICBpZiAodGhpcy5tZXRyaWNzLmxlbmd0aCA+IHRoaXMuTUFYX01FVFJJQ1MpIHtcbiAgICAgIHRoaXMubWV0cmljcyA9IHRoaXMubWV0cmljcy5zbGljZSgtdGhpcy5NQVhfTUVUUklDUyAvIDIpO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBDb2xsZWN0IG1lbW9yeSBtZXRyaWNzXG4gICAqL1xuICBwcml2YXRlIGNvbGxlY3RNZW1vcnlNZXRyaWNzKCk6IHZvaWQge1xuICAgIC8vIEluIFJlYWN0IE5hdGl2ZSwgbWVtb3J5IG1ldHJpY3MgYXJlIGxpbWl0ZWRcbiAgICAvLyBUaGlzIGlzIGEgcGxhY2Vob2xkZXIgZm9yIHdoZW4gcGVyZm9ybWFuY2UubWVtb3J5IGlzIGF2YWlsYWJsZVxuICAgIGlmICh0eXBlb2YgcGVyZm9ybWFuY2UgIT09ICd1bmRlZmluZWQnICYmIChwZXJmb3JtYW5jZSBhcyBhbnkpLm1lbW9yeSkge1xuICAgICAgY29uc3QgbWVtb3J5ID0gKHBlcmZvcm1hbmNlIGFzIGFueSkubWVtb3J5O1xuICAgICAgdGhpcy5tZW1vcnlNZXRyaWNzLnB1c2goe1xuICAgICAgICB1c2VkSlNIZWFwU2l6ZTogbWVtb3J5LnVzZWRKU0hlYXBTaXplLFxuICAgICAgICB0b3RhbEpTSGVhcFNpemU6IG1lbW9yeS50b3RhbEpTSGVhcFNpemUsXG4gICAgICAgIGpzSGVhcFNpemVMaW1pdDogbWVtb3J5LmpzSGVhcFNpemVMaW1pdCxcbiAgICAgICAgdGltZXN0YW1wOiBEYXRlLm5vdygpLFxuICAgICAgfSk7XG5cbiAgICAgIC8vIEtlZXAgb25seSByZWNlbnQgbWVtb3J5IG1ldHJpY3NcbiAgICAgIGlmICh0aGlzLm1lbW9yeU1ldHJpY3MubGVuZ3RoID4gMTAwKSB7XG4gICAgICAgIHRoaXMubWVtb3J5TWV0cmljcyA9IHRoaXMubWVtb3J5TWV0cmljcy5zbGljZSgtNTApO1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBEZXRlY3QgcG90ZW50aWFsIG1lbW9yeSBsZWFrc1xuICAgKi9cbiAgcHJpdmF0ZSBkZXRlY3RNZW1vcnlMZWFrcygpOiBzdHJpbmdbXSB7XG4gICAgY29uc3QgbGVha3M6IHN0cmluZ1tdID0gW107XG5cbiAgICBpZiAodGhpcy5tZW1vcnlNZXRyaWNzLmxlbmd0aCA8IDEwKSByZXR1cm4gbGVha3M7XG5cbiAgICAvLyBDaGVjayBmb3IgY29uc2lzdGVudCBtZW1vcnkgZ3Jvd3RoXG4gICAgY29uc3QgcmVjZW50ID0gdGhpcy5tZW1vcnlNZXRyaWNzLnNsaWNlKC0xMCk7XG4gICAgY29uc3QgZ3Jvd3RoID0gcmVjZW50W3JlY2VudC5sZW5ndGggLSAxXS51c2VkSlNIZWFwU2l6ZSAtIHJlY2VudFswXS51c2VkSlNIZWFwU2l6ZTtcbiAgICBcbiAgICBpZiAoZ3Jvd3RoID4gdGhpcy5NRU1PUllfTEVBS19USFJFU0hPTEQpIHtcbiAgICAgIGxlYWtzLnB1c2goYE1lbW9yeSB1c2FnZSBpbmNyZWFzZWQgYnkgJHtNYXRoLnJvdW5kKGdyb3d0aCAvIDEwMjQgLyAxMDI0KX1NQiBpbiByZWNlbnQgbWVhc3VyZW1lbnRzYCk7XG4gICAgfVxuXG4gICAgLy8gQ2hlY2sgZm9yIGNvbXBvbmVudHMgd2l0aCBleGNlc3NpdmUgcmUtcmVuZGVyc1xuICAgIGZvciAoY29uc3QgW25hbWUsIG1ldHJpY3NdIG9mIHRoaXMucmVuZGVyTWV0cmljcy5lbnRyaWVzKCkpIHtcbiAgICAgIGlmIChtZXRyaWNzLnJlUmVuZGVycyA+IDEwMCkge1xuICAgICAgICBsZWFrcy5wdXNoKGBDb21wb25lbnQgJHtuYW1lfSBoYXMgJHttZXRyaWNzLnJlUmVuZGVyc30gcmUtcmVuZGVyc2ApO1xuICAgICAgfVxuICAgIH1cblxuICAgIHJldHVybiBsZWFrcztcbiAgfVxuXG4gIC8qKlxuICAgKiBTdGFydCBmcmFtZSBtb25pdG9yaW5nIChpZiBhdmFpbGFibGUpXG4gICAqL1xuICBwcml2YXRlIHN0YXJ0RnJhbWVNb25pdG9yaW5nKCk6IHZvaWQge1xuICAgIC8vIFRoaXMgd291bGQgdXNlIHBsYXRmb3JtLXNwZWNpZmljIEFQSXMgdG8gbW9uaXRvciBmcmFtZSBkcm9wc1xuICAgIC8vIEZvciBub3csIGl0J3MgYSBwbGFjZWhvbGRlclxuICAgIGlmIChfX0RFVl9fKSB7XG4gICAgICBjb25zb2xlLmxvZygn8J+TiiBGcmFtZSBtb25pdG9yaW5nIHN0YXJ0ZWQgKHBsYWNlaG9sZGVyKScpO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBDbGVhbiB1cCBvbGQgbWV0cmljc1xuICAgKi9cbiAgcHJpdmF0ZSBjbGVhbnVwT2xkTWV0cmljcygpOiB2b2lkIHtcbiAgICBjb25zdCBjdXRvZmYgPSBEYXRlLm5vdygpIC0gNjAwMDAwOyAvLyAxMCBtaW51dGVzIGFnb1xuICAgIHRoaXMubWV0cmljcyA9IHRoaXMubWV0cmljcy5maWx0ZXIobSA9PiBtLnRpbWVzdGFtcCA+IGN1dG9mZik7XG4gIH1cblxuICAvKipcbiAgICogR2VuZXJhdGUgcGVyZm9ybWFuY2UgcmVjb21tZW5kYXRpb25zXG4gICAqL1xuICBwcml2YXRlIGdlbmVyYXRlUmVjb21tZW5kYXRpb25zKGRhdGE6IHtcbiAgICBhdmVyYWdlUmVuZGVyVGltZTogbnVtYmVyO1xuICAgIGF2ZXJhZ2VOZXR3b3JrVGltZTogbnVtYmVyO1xuICAgIHNsb3dDb21wb25lbnRzOiBSZW5kZXJNZXRyaWNzW107XG4gICAgc2xvd05ldHdvcmtSZXF1ZXN0czogTmV0d29ya01ldHJpY3NbXTtcbiAgICBjYWNoZUhpdFJhdGU6IG51bWJlcjtcbiAgICBtZW1vcnlVc2FnZTogbnVtYmVyO1xuICB9KTogc3RyaW5nW10ge1xuICAgIGNvbnN0IHJlY29tbWVuZGF0aW9uczogc3RyaW5nW10gPSBbXTtcblxuICAgIGlmIChkYXRhLmF2ZXJhZ2VSZW5kZXJUaW1lID4gdGhpcy5TTE9XX1JFTkRFUl9USFJFU0hPTEQpIHtcbiAgICAgIHJlY29tbWVuZGF0aW9ucy5wdXNoKCdDb25zaWRlciBvcHRpbWl6aW5nIGNvbXBvbmVudCByZW5kZXJzIHdpdGggUmVhY3QubWVtbyBvciB1c2VNZW1vJyk7XG4gICAgfVxuXG4gICAgaWYgKGRhdGEuc2xvd0NvbXBvbmVudHMubGVuZ3RoID4gMCkge1xuICAgICAgcmVjb21tZW5kYXRpb25zLnB1c2goYE9wdGltaXplIHNsb3cgY29tcG9uZW50czogJHtkYXRhLnNsb3dDb21wb25lbnRzLnNsaWNlKDAsIDMpLm1hcChjID0+IGMuY29tcG9uZW50TmFtZSkuam9pbignLCAnKX1gKTtcbiAgICB9XG5cbiAgICBpZiAoZGF0YS5hdmVyYWdlTmV0d29ya1RpbWUgPiAxMDAwKSB7XG4gICAgICByZWNvbW1lbmRhdGlvbnMucHVzaCgnQ29uc2lkZXIgaW1wbGVtZW50aW5nIHJlcXVlc3QgY2FjaGluZyBvciBvcHRpbWl6aW5nIEFQSSBlbmRwb2ludHMnKTtcbiAgICB9XG5cbiAgICBpZiAoZGF0YS5jYWNoZUhpdFJhdGUgPCAwLjUpIHtcbiAgICAgIHJlY29tbWVuZGF0aW9ucy5wdXNoKCdJbXByb3ZlIGNhY2hlIGhpdCByYXRlIGJ5IGltcGxlbWVudGluZyBiZXR0ZXIgY2FjaGluZyBzdHJhdGVnaWVzJyk7XG4gICAgfVxuXG4gICAgaWYgKGRhdGEubWVtb3J5VXNhZ2UgPiAxMDAgKiAxMDI0ICogMTAyNCkgeyAvLyAxMDBNQlxuICAgICAgcmVjb21tZW5kYXRpb25zLnB1c2goJ0hpZ2ggbWVtb3J5IHVzYWdlIGRldGVjdGVkIC0gY2hlY2sgZm9yIG1lbW9yeSBsZWFrcycpO1xuICAgIH1cblxuICAgIGlmIChkYXRhLnNsb3dOZXR3b3JrUmVxdWVzdHMubGVuZ3RoID4gNSkge1xuICAgICAgcmVjb21tZW5kYXRpb25zLnB1c2goJ011bHRpcGxlIHNsb3cgbmV0d29yayByZXF1ZXN0cyBkZXRlY3RlZCAtIGNvbnNpZGVyIHJlcXVlc3QgYmF0Y2hpbmcnKTtcbiAgICB9XG5cbiAgICByZXR1cm4gcmVjb21tZW5kYXRpb25zO1xuICB9XG59XG5cbi8vIEV4cG9ydCBzaW5nbGV0b24gaW5zdGFuY2VcbmV4cG9ydCBjb25zdCBwZXJmb3JtYW5jZU1vbml0b3IgPSBuZXcgUGVyZm9ybWFuY2VNb25pdG9yU2VydmljZSgpO1xuZXhwb3J0IGRlZmF1bHQgcGVyZm9ybWFuY2VNb25pdG9yO1xuIl0sIm1hcHBpbmdzIjoiOzs7Ozs7OztJQStETUEseUJBQXlCO0VBQUEsU0FBQUEsMEJBQUE7SUFBQSxJQUFBQyxnQkFBQSxDQUFBQyxPQUFBLFFBQUFGLHlCQUFBO0lBQUEsS0FDckJHLE9BQU8sR0FBd0IsRUFBRTtJQUFBLEtBQ2pDQyxhQUFhLEdBQUcsSUFBSUMsR0FBRyxDQUF3QixDQUFDO0lBQUEsS0FDaERDLGNBQWMsR0FBcUIsRUFBRTtJQUFBLEtBQ3JDQyxhQUFhLEdBQW9CLEVBQUU7SUFBQSxLQUNuQ0MsY0FBYyxHQUFHLENBQUM7SUFBQSxLQUNsQkMsWUFBWSxHQUFHLEtBQUs7SUFBQSxLQUdYQyxXQUFXLEdBQUcsSUFBSTtJQUFBLEtBQ2xCQyxxQkFBcUIsR0FBRyxFQUFFO0lBQUEsS0FDMUJDLHNCQUFzQixHQUFHLElBQUk7SUFBQSxLQUM3QkMscUJBQXFCLEdBQUcsRUFBRSxHQUFHLElBQUksR0FBRyxJQUFJO0VBQUE7RUFBQSxXQUFBQyxhQUFBLENBQUFaLE9BQUEsRUFBQUYseUJBQUE7SUFBQWUsR0FBQTtJQUFBQyxLQUFBLEVBS3pELFNBQUFDLGVBQWVBLENBQUEsRUFBUztNQUFBLElBQUFDLEtBQUE7TUFDdEIsSUFBSSxJQUFJLENBQUNULFlBQVksRUFBRTtNQUV2QixJQUFJLENBQUNBLFlBQVksR0FBRyxJQUFJO01BQ3hCVSxPQUFPLENBQUNDLEdBQUcsQ0FBQyxpQ0FBaUMsQ0FBQztNQUc5QyxJQUFJLENBQUNDLGtCQUFrQixHQUFHQyxXQUFXLENBQUMsWUFBTTtRQUMxQ0osS0FBSSxDQUFDSyxvQkFBb0IsQ0FBQyxDQUFDO1FBQzNCTCxLQUFJLENBQUNNLGlCQUFpQixDQUFDLENBQUM7UUFDeEJOLEtBQUksQ0FBQ08saUJBQWlCLENBQUMsQ0FBQztNQUMxQixDQUFDLEVBQUUsSUFBSSxDQUFDO01BR1IsSUFBSSxDQUFDQyxvQkFBb0IsQ0FBQyxDQUFDO0lBQzdCO0VBQUM7SUFBQVgsR0FBQTtJQUFBQyxLQUFBLEVBS0QsU0FBQVcsY0FBY0EsQ0FBQSxFQUFTO01BQ3JCLElBQUksQ0FBQyxJQUFJLENBQUNsQixZQUFZLEVBQUU7TUFFeEIsSUFBSSxDQUFDQSxZQUFZLEdBQUcsS0FBSztNQUN6QlUsT0FBTyxDQUFDQyxHQUFHLENBQUMsaUNBQWlDLENBQUM7TUFFOUMsSUFBSSxJQUFJLENBQUNDLGtCQUFrQixFQUFFO1FBQzNCTyxhQUFhLENBQUMsSUFBSSxDQUFDUCxrQkFBa0IsQ0FBQztNQUN4QztJQUNGO0VBQUM7SUFBQU4sR0FBQTtJQUFBQyxLQUFBLEVBS0QsU0FBQWEsV0FBV0EsQ0FBQ0MsYUFBcUIsRUFBRUMsVUFBa0IsRUFBRUMsUUFBOEIsRUFBUTtNQUMzRixJQUFNQyxRQUFRLEdBQUcsSUFBSSxDQUFDN0IsYUFBYSxDQUFDOEIsR0FBRyxDQUFDSixhQUFhLENBQUM7TUFFdEQsSUFBSUcsUUFBUSxFQUFFO1FBQ1pBLFFBQVEsQ0FBQ0YsVUFBVSxHQUFHLENBQUNFLFFBQVEsQ0FBQ0YsVUFBVSxHQUFHQSxVQUFVLElBQUksQ0FBQztRQUM1REUsUUFBUSxDQUFDRSxTQUFTLEVBQUU7UUFDcEJGLFFBQVEsQ0FBQ0csY0FBYyxHQUFHQyxJQUFJLENBQUNDLEdBQUcsQ0FBQyxDQUFDO1FBQ3BDLElBQUlOLFFBQVEsWUFBUkEsUUFBUSxDQUFFTyxVQUFVLEVBQUVOLFFBQVEsQ0FBQ00sVUFBVSxHQUFHUCxRQUFRLENBQUNPLFVBQVU7UUFDbkUsSUFBSVAsUUFBUSxZQUFSQSxRQUFRLENBQUVRLFlBQVksRUFBRVAsUUFBUSxDQUFDTyxZQUFZLElBQUlSLFFBQVEsQ0FBQ1EsWUFBWTtNQUM1RSxDQUFDLE1BQU07UUFDTCxJQUFJLENBQUNwQyxhQUFhLENBQUNxQyxHQUFHLENBQUNYLGFBQWEsRUFBRTtVQUNwQ0EsYUFBYSxFQUFiQSxhQUFhO1VBQ2JDLFVBQVUsRUFBVkEsVUFBVTtVQUNWUSxVQUFVLEVBQUUsQ0FBQVAsUUFBUSxvQkFBUkEsUUFBUSxDQUFFTyxVQUFVLEtBQUksQ0FBQztVQUNyQ0MsWUFBWSxFQUFFLENBQUFSLFFBQVEsb0JBQVJBLFFBQVEsQ0FBRVEsWUFBWSxLQUFJLENBQUM7VUFDekNMLFNBQVMsRUFBRSxDQUFDO1VBQ1pDLGNBQWMsRUFBRUMsSUFBSSxDQUFDQyxHQUFHLENBQUM7UUFDM0IsQ0FBQyxDQUFDO01BQ0o7TUFHQSxJQUFJLENBQUNJLFNBQVMsQ0FBQztRQUNiQyxJQUFJLEVBQUUsa0JBQWtCO1FBQ3hCM0IsS0FBSyxFQUFFZSxVQUFVO1FBQ2pCYSxTQUFTLEVBQUVQLElBQUksQ0FBQ0MsR0FBRyxDQUFDLENBQUM7UUFDckJPLFFBQVEsRUFBRSxRQUFRO1FBQ2xCYixRQUFRLEVBQUFjLE1BQUEsQ0FBQUMsTUFBQTtVQUFJakIsYUFBYSxFQUFiQTtRQUFhLEdBQUtFLFFBQVE7TUFDeEMsQ0FBQyxDQUFDO01BR0YsSUFBSUQsVUFBVSxHQUFHLElBQUksQ0FBQ3BCLHFCQUFxQixFQUFFO1FBQzNDUSxPQUFPLENBQUM2QixJQUFJLENBQUMsNEJBQTRCbEIsYUFBYSxTQUFTQyxVQUFVLElBQUksQ0FBQztNQUNoRjtJQUNGO0VBQUM7SUFBQWhCLEdBQUE7SUFBQUMsS0FBQSxFQUtELFNBQUFpQyxtQkFBbUJBLENBQ2pCQyxHQUFXLEVBQ1hDLE1BQWMsRUFDZEMsWUFBb0IsRUFDcEJDLFVBQWtCLEVBSVo7TUFBQSxJQUhOQyxXQUFtQixHQUFBQyxTQUFBLENBQUFDLE1BQUEsUUFBQUQsU0FBQSxRQUFBRSxTQUFBLEdBQUFGLFNBQUEsTUFBRyxDQUFDO01BQUEsSUFDdkJHLFlBQW9CLEdBQUFILFNBQUEsQ0FBQUMsTUFBQSxRQUFBRCxTQUFBLFFBQUFFLFNBQUEsR0FBQUYsU0FBQSxNQUFHLENBQUM7TUFBQSxJQUN4QkksTUFBZSxHQUFBSixTQUFBLENBQUFDLE1BQUEsUUFBQUQsU0FBQSxRQUFBRSxTQUFBLEdBQUFGLFNBQUEsTUFBRyxLQUFLO01BRXZCLElBQU1LLE1BQXNCLEdBQUc7UUFDN0JWLEdBQUcsRUFBSEEsR0FBRztRQUNIQyxNQUFNLEVBQU5BLE1BQU07UUFDTkMsWUFBWSxFQUFaQSxZQUFZO1FBQ1pDLFVBQVUsRUFBVkEsVUFBVTtRQUNWQyxXQUFXLEVBQVhBLFdBQVc7UUFDWEksWUFBWSxFQUFaQSxZQUFZO1FBQ1pDLE1BQU0sRUFBTkEsTUFBTTtRQUNOZixTQUFTLEVBQUVQLElBQUksQ0FBQ0MsR0FBRyxDQUFDO01BQ3RCLENBQUM7TUFFRCxJQUFJLENBQUNoQyxjQUFjLENBQUN1RCxJQUFJLENBQUNELE1BQU0sQ0FBQztNQUdoQyxJQUFJLENBQUNsQixTQUFTLENBQUM7UUFDYkMsSUFBSSxFQUFFLGlCQUFpQjtRQUN2QjNCLEtBQUssRUFBRW9DLFlBQVk7UUFDbkJSLFNBQVMsRUFBRVAsSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQztRQUNyQk8sUUFBUSxFQUFFLFNBQVM7UUFDbkJiLFFBQVEsRUFBRTtVQUFFa0IsR0FBRyxFQUFIQSxHQUFHO1VBQUVDLE1BQU0sRUFBTkEsTUFBTTtVQUFFRSxVQUFVLEVBQVZBLFVBQVU7VUFBRU0sTUFBTSxFQUFOQTtRQUFPO01BQzlDLENBQUMsQ0FBQztNQUdGLElBQUlQLFlBQVksR0FBRyxJQUFJLENBQUN4QyxzQkFBc0IsRUFBRTtRQUM5Q08sT0FBTyxDQUFDNkIsSUFBSSxDQUFDLDRCQUE0QkcsTUFBTSxJQUFJRCxHQUFHLFNBQVNFLFlBQVksSUFBSSxDQUFDO01BQ2xGO01BR0EsSUFBSSxJQUFJLENBQUM5QyxjQUFjLENBQUNrRCxNQUFNLEdBQUcsSUFBSSxDQUFDOUMsV0FBVyxFQUFFO1FBQ2pELElBQUksQ0FBQ0osY0FBYyxHQUFHLElBQUksQ0FBQ0EsY0FBYyxDQUFDd0QsS0FBSyxDQUFDLENBQUMsSUFBSSxDQUFDcEQsV0FBVyxHQUFHLENBQUMsQ0FBQztNQUN4RTtJQUNGO0VBQUM7SUFBQUssR0FBQTtJQUFBQyxLQUFBLEVBS0QsU0FBQStDLG9CQUFvQkEsQ0FDbEJDLGVBQXVCLEVBQ3ZCWixZQUFvQixFQUNwQnBCLFFBQThCLEVBQ3hCO01BQ04sSUFBSSxDQUFDVSxTQUFTLENBQUM7UUFDYkMsSUFBSSxFQUFFLGtCQUFrQjtRQUN4QjNCLEtBQUssRUFBRW9DLFlBQVk7UUFDbkJSLFNBQVMsRUFBRVAsSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQztRQUNyQk8sUUFBUSxFQUFFLGtCQUFrQjtRQUM1QmIsUUFBUSxFQUFBYyxNQUFBLENBQUFDLE1BQUE7VUFBSWlCLGVBQWUsRUFBZkE7UUFBZSxHQUFLaEMsUUFBUTtNQUMxQyxDQUFDLENBQUM7TUFHRixJQUFJb0IsWUFBWSxHQUFHLEdBQUcsRUFBRTtRQUN0QmpDLE9BQU8sQ0FBQzZCLElBQUksQ0FBQyx3QkFBd0JnQixlQUFlLFNBQVNaLFlBQVksSUFBSSxDQUFDO01BQ2hGO0lBQ0Y7RUFBQztJQUFBckMsR0FBQTtJQUFBQyxLQUFBLEVBS0QsU0FBQWlELGVBQWVBLENBQ2JDLFVBQWtCLEVBQ2xCQyxRQUFnQixFQUNoQkMsY0FBc0IsRUFDdEJwQyxRQUE4QixFQUN4QjtNQUNOLElBQUksQ0FBQ1UsU0FBUyxDQUFDO1FBQ2JDLElBQUksRUFBRSxZQUFZO1FBQ2xCM0IsS0FBSyxFQUFFb0QsY0FBYztRQUNyQnhCLFNBQVMsRUFBRVAsSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQztRQUNyQk8sUUFBUSxFQUFFLFlBQVk7UUFDdEJiLFFBQVEsRUFBQWMsTUFBQSxDQUFBQyxNQUFBO1VBQUltQixVQUFVLEVBQVZBLFVBQVU7VUFBRUMsUUFBUSxFQUFSQTtRQUFRLEdBQUtuQyxRQUFRO01BQy9DLENBQUMsQ0FBQztJQUNKO0VBQUM7SUFBQWpCLEdBQUE7SUFBQUMsS0FBQSxFQUtELFNBQUFxRCxvQkFBb0JBLENBQUEsRUFBc0I7TUFBQSxJQUFBQyxNQUFBO01BQ3hDLElBQU1oQyxHQUFHLEdBQUdELElBQUksQ0FBQ0MsR0FBRyxDQUFDLENBQUM7TUFDdEIsSUFBTWlDLGFBQWEsR0FBRyxJQUFJLENBQUNwRSxPQUFPLENBQUNxRSxNQUFNLENBQUMsVUFBQUMsQ0FBQztRQUFBLE9BQUluQyxHQUFHLEdBQUdtQyxDQUFDLENBQUM3QixTQUFTLEdBQUcsTUFBTTtNQUFBLEVBQUM7TUFHMUUsSUFBTXhDLGFBQWEsR0FBR21FLGFBQWEsQ0FBQ0MsTUFBTSxDQUFDLFVBQUFDLENBQUM7UUFBQSxPQUFJQSxDQUFDLENBQUM1QixRQUFRLEtBQUssUUFBUTtNQUFBLEVBQUM7TUFDeEUsSUFBTXZDLGNBQWMsR0FBR2lFLGFBQWEsQ0FBQ0MsTUFBTSxDQUFDLFVBQUFDLENBQUM7UUFBQSxPQUFJQSxDQUFDLENBQUM1QixRQUFRLEtBQUssU0FBUztNQUFBLEVBQUM7TUFFMUUsSUFBTTZCLGlCQUFpQixHQUFHdEUsYUFBYSxDQUFDb0QsTUFBTSxHQUFHLENBQUMsR0FDOUNwRCxhQUFhLENBQUN1RSxNQUFNLENBQUMsVUFBQ0MsR0FBRyxFQUFFSCxDQUFDO1FBQUEsT0FBS0csR0FBRyxHQUFHSCxDQUFDLENBQUN6RCxLQUFLO01BQUEsR0FBRSxDQUFDLENBQUMsR0FBR1osYUFBYSxDQUFDb0QsTUFBTSxHQUN6RSxDQUFDO01BRUwsSUFBTXFCLGtCQUFrQixHQUFHdkUsY0FBYyxDQUFDa0QsTUFBTSxHQUFHLENBQUMsR0FDaERsRCxjQUFjLENBQUNxRSxNQUFNLENBQUMsVUFBQ0MsR0FBRyxFQUFFSCxDQUFDO1FBQUEsT0FBS0csR0FBRyxHQUFHSCxDQUFDLENBQUN6RCxLQUFLO01BQUEsR0FBRSxDQUFDLENBQUMsR0FBR1YsY0FBYyxDQUFDa0QsTUFBTSxHQUMzRSxDQUFDO01BR0wsSUFBTXNCLGNBQWMsR0FBR0MsS0FBSyxDQUFDQyxJQUFJLENBQUMsSUFBSSxDQUFDNUUsYUFBYSxDQUFDNkUsTUFBTSxDQUFDLENBQUMsQ0FBQyxDQUMzRFQsTUFBTSxDQUFDLFVBQUFVLENBQUM7UUFBQSxPQUFJQSxDQUFDLENBQUNuRCxVQUFVLEdBQUd1QyxNQUFJLENBQUMzRCxxQkFBcUI7TUFBQSxFQUFDLENBQ3REd0UsSUFBSSxDQUFDLFVBQUNDLENBQUMsRUFBRUMsQ0FBQztRQUFBLE9BQUtBLENBQUMsQ0FBQ3RELFVBQVUsR0FBR3FELENBQUMsQ0FBQ3JELFVBQVU7TUFBQSxFQUFDLENBQzNDK0IsS0FBSyxDQUFDLENBQUMsRUFBRSxFQUFFLENBQUM7TUFHZixJQUFNd0IsbUJBQW1CLEdBQUcsSUFBSSxDQUFDaEYsY0FBYyxDQUM1Q2tFLE1BQU0sQ0FBQyxVQUFBZSxDQUFDO1FBQUEsT0FBSUEsQ0FBQyxDQUFDbkMsWUFBWSxHQUFHa0IsTUFBSSxDQUFDMUQsc0JBQXNCO01BQUEsRUFBQyxDQUN6RHVFLElBQUksQ0FBQyxVQUFDQyxDQUFDLEVBQUVDLENBQUM7UUFBQSxPQUFLQSxDQUFDLENBQUNqQyxZQUFZLEdBQUdnQyxDQUFDLENBQUNoQyxZQUFZO01BQUEsRUFBQyxDQUMvQ1UsS0FBSyxDQUFDLENBQUMsRUFBRSxFQUFFLENBQUM7TUFHZixJQUFNMEIsY0FBYyxHQUFHLElBQUksQ0FBQ2xGLGNBQWMsQ0FBQ2tFLE1BQU0sQ0FBQyxVQUFBZSxDQUFDO1FBQUEsT0FBSUEsQ0FBQyxDQUFDNUIsTUFBTTtNQUFBLEVBQUMsQ0FBQ0gsTUFBTTtNQUN2RSxJQUFNaUMsYUFBYSxHQUFHLElBQUksQ0FBQ25GLGNBQWMsQ0FBQ2tELE1BQU07TUFDaEQsSUFBTWtDLFlBQVksR0FBR0QsYUFBYSxHQUFHLENBQUMsR0FBR0QsY0FBYyxHQUFHQyxhQUFhLEdBQUcsQ0FBQztNQUczRSxJQUFNRSxZQUFZLEdBQUcsSUFBSSxDQUFDcEYsYUFBYSxDQUFDLElBQUksQ0FBQ0EsYUFBYSxDQUFDaUQsTUFBTSxHQUFHLENBQUMsQ0FBQztNQUN0RSxJQUFNb0MsV0FBVyxHQUFHRCxZQUFZLEdBQUdBLFlBQVksQ0FBQ0UsY0FBYyxHQUFHLENBQUM7TUFHbEUsSUFBTUMsZUFBZSxHQUFHLElBQUksQ0FBQ0MsdUJBQXVCLENBQUM7UUFDbkRyQixpQkFBaUIsRUFBakJBLGlCQUFpQjtRQUNqQkcsa0JBQWtCLEVBQWxCQSxrQkFBa0I7UUFDbEJDLGNBQWMsRUFBZEEsY0FBYztRQUNkUSxtQkFBbUIsRUFBbkJBLG1CQUFtQjtRQUNuQkksWUFBWSxFQUFaQSxZQUFZO1FBQ1pFLFdBQVcsRUFBWEE7TUFDRixDQUFDLENBQUM7TUFFRixPQUFPO1FBQ0xJLE9BQU8sRUFBRTtVQUNQdEIsaUJBQWlCLEVBQWpCQSxpQkFBaUI7VUFDakJHLGtCQUFrQixFQUFsQkEsa0JBQWtCO1VBQ2xCZSxXQUFXLEVBQVhBLFdBQVc7VUFDWEssVUFBVSxFQUFFLElBQUksQ0FBQ3pGLGNBQWM7VUFDL0JrRixZQUFZLEVBQVpBO1FBQ0YsQ0FBQztRQUNEWixjQUFjLEVBQWRBLGNBQWM7UUFDZFEsbUJBQW1CLEVBQW5CQSxtQkFBbUI7UUFDbkJZLFdBQVcsRUFBRSxJQUFJLENBQUMxRSxpQkFBaUIsQ0FBQyxDQUFDO1FBQ3JDc0UsZUFBZSxFQUFmQTtNQUNGLENBQUM7SUFDSDtFQUFDO0lBQUEvRSxHQUFBO0lBQUFDLEtBQUEsRUFLRCxTQUFBbUYsb0JBQW9CQSxDQUFDdEQsUUFBdUMsRUFBdUI7TUFDakYsT0FBTyxJQUFJLENBQUMxQyxPQUFPLENBQUNxRSxNQUFNLENBQUMsVUFBQUMsQ0FBQztRQUFBLE9BQUlBLENBQUMsQ0FBQzVCLFFBQVEsS0FBS0EsUUFBUTtNQUFBLEVBQUM7SUFDMUQ7RUFBQztJQUFBOUIsR0FBQTtJQUFBQyxLQUFBLEVBS0QsU0FBQW9GLFlBQVlBLENBQUEsRUFBUztNQUNuQixJQUFJLENBQUNqRyxPQUFPLEdBQUcsRUFBRTtNQUNqQixJQUFJLENBQUNDLGFBQWEsQ0FBQ2lHLEtBQUssQ0FBQyxDQUFDO01BQzFCLElBQUksQ0FBQy9GLGNBQWMsR0FBRyxFQUFFO01BQ3hCLElBQUksQ0FBQ0MsYUFBYSxHQUFHLEVBQUU7TUFDdkIsSUFBSSxDQUFDQyxjQUFjLEdBQUcsQ0FBQztJQUN6QjtFQUFDO0lBQUFPLEdBQUE7SUFBQUMsS0FBQSxFQUtELFNBQVEwQixTQUFTQSxDQUFDa0IsTUFBeUIsRUFBUTtNQUNqRCxJQUFJLENBQUN6RCxPQUFPLENBQUMwRCxJQUFJLENBQUNELE1BQU0sQ0FBQztNQUd6QixJQUFJLElBQUksQ0FBQ3pELE9BQU8sQ0FBQ3FELE1BQU0sR0FBRyxJQUFJLENBQUM5QyxXQUFXLEVBQUU7UUFDMUMsSUFBSSxDQUFDUCxPQUFPLEdBQUcsSUFBSSxDQUFDQSxPQUFPLENBQUMyRCxLQUFLLENBQUMsQ0FBQyxJQUFJLENBQUNwRCxXQUFXLEdBQUcsQ0FBQyxDQUFDO01BQzFEO0lBQ0Y7RUFBQztJQUFBSyxHQUFBO0lBQUFDLEtBQUEsRUFLRCxTQUFRTyxvQkFBb0JBLENBQUEsRUFBUztNQUduQyxJQUFJLE9BQU8rRSxXQUFXLEtBQUssV0FBVyxJQUFLQSxXQUFXLENBQVNDLE1BQU0sRUFBRTtRQUNyRSxJQUFNQSxNQUFNLEdBQUlELFdBQVcsQ0FBU0MsTUFBTTtRQUMxQyxJQUFJLENBQUNoRyxhQUFhLENBQUNzRCxJQUFJLENBQUM7VUFDdEJnQyxjQUFjLEVBQUVVLE1BQU0sQ0FBQ1YsY0FBYztVQUNyQ1csZUFBZSxFQUFFRCxNQUFNLENBQUNDLGVBQWU7VUFDdkNDLGVBQWUsRUFBRUYsTUFBTSxDQUFDRSxlQUFlO1VBQ3ZDN0QsU0FBUyxFQUFFUCxJQUFJLENBQUNDLEdBQUcsQ0FBQztRQUN0QixDQUFDLENBQUM7UUFHRixJQUFJLElBQUksQ0FBQy9CLGFBQWEsQ0FBQ2lELE1BQU0sR0FBRyxHQUFHLEVBQUU7VUFDbkMsSUFBSSxDQUFDakQsYUFBYSxHQUFHLElBQUksQ0FBQ0EsYUFBYSxDQUFDdUQsS0FBSyxDQUFDLENBQUMsRUFBRSxDQUFDO1FBQ3BEO01BQ0Y7SUFDRjtFQUFDO0lBQUEvQyxHQUFBO0lBQUFDLEtBQUEsRUFLRCxTQUFRUSxpQkFBaUJBLENBQUEsRUFBYTtNQUNwQyxJQUFNa0YsS0FBZSxHQUFHLEVBQUU7TUFFMUIsSUFBSSxJQUFJLENBQUNuRyxhQUFhLENBQUNpRCxNQUFNLEdBQUcsRUFBRSxFQUFFLE9BQU9rRCxLQUFLO01BR2hELElBQU1DLE1BQU0sR0FBRyxJQUFJLENBQUNwRyxhQUFhLENBQUN1RCxLQUFLLENBQUMsQ0FBQyxFQUFFLENBQUM7TUFDNUMsSUFBTThDLE1BQU0sR0FBR0QsTUFBTSxDQUFDQSxNQUFNLENBQUNuRCxNQUFNLEdBQUcsQ0FBQyxDQUFDLENBQUNxQyxjQUFjLEdBQUdjLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQ2QsY0FBYztNQUVsRixJQUFJZSxNQUFNLEdBQUcsSUFBSSxDQUFDL0YscUJBQXFCLEVBQUU7UUFDdkM2RixLQUFLLENBQUM3QyxJQUFJLENBQUMsNkJBQTZCZ0QsSUFBSSxDQUFDQyxLQUFLLENBQUNGLE1BQU0sR0FBRyxJQUFJLEdBQUcsSUFBSSxDQUFDLDJCQUEyQixDQUFDO01BQ3RHO01BR0EsU0FBQUcsSUFBQSxJQUE4QixJQUFJLENBQUMzRyxhQUFhLENBQUM0RyxPQUFPLENBQUMsQ0FBQyxFQUFFO1FBQUEsSUFBQUMsS0FBQSxPQUFBQyxlQUFBLENBQUFoSCxPQUFBLEVBQUE2RyxJQUFBO1FBQUEsSUFBaERwRSxJQUFJLEdBQUFzRSxLQUFBO1FBQUEsSUFBRTlHLE9BQU8sR0FBQThHLEtBQUE7UUFDdkIsSUFBSTlHLE9BQU8sQ0FBQ2dDLFNBQVMsR0FBRyxHQUFHLEVBQUU7VUFDM0J1RSxLQUFLLENBQUM3QyxJQUFJLENBQUMsYUFBYWxCLElBQUksUUFBUXhDLE9BQU8sQ0FBQ2dDLFNBQVMsYUFBYSxDQUFDO1FBQ3JFO01BQ0Y7TUFFQSxPQUFPdUUsS0FBSztJQUNkO0VBQUM7SUFBQTNGLEdBQUE7SUFBQUMsS0FBQSxFQUtELFNBQVFVLG9CQUFvQkEsQ0FBQSxFQUFTO01BR25DLElBQUl5RixPQUFPLEVBQUU7UUFDWGhHLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLDJDQUEyQyxDQUFDO01BQzFEO0lBQ0Y7RUFBQztJQUFBTCxHQUFBO0lBQUFDLEtBQUEsRUFLRCxTQUFRUyxpQkFBaUJBLENBQUEsRUFBUztNQUNoQyxJQUFNMkYsTUFBTSxHQUFHL0UsSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQyxHQUFHLE1BQU07TUFDbEMsSUFBSSxDQUFDbkMsT0FBTyxHQUFHLElBQUksQ0FBQ0EsT0FBTyxDQUFDcUUsTUFBTSxDQUFDLFVBQUFDLENBQUM7UUFBQSxPQUFJQSxDQUFDLENBQUM3QixTQUFTLEdBQUd3RSxNQUFNO01BQUEsRUFBQztJQUMvRDtFQUFDO0lBQUFyRyxHQUFBO0lBQUFDLEtBQUEsRUFLRCxTQUFRK0UsdUJBQXVCQSxDQUFDc0IsSUFPL0IsRUFBWTtNQUNYLElBQU12QixlQUF5QixHQUFHLEVBQUU7TUFFcEMsSUFBSXVCLElBQUksQ0FBQzNDLGlCQUFpQixHQUFHLElBQUksQ0FBQy9ELHFCQUFxQixFQUFFO1FBQ3ZEbUYsZUFBZSxDQUFDakMsSUFBSSxDQUFDLGtFQUFrRSxDQUFDO01BQzFGO01BRUEsSUFBSXdELElBQUksQ0FBQ3ZDLGNBQWMsQ0FBQ3RCLE1BQU0sR0FBRyxDQUFDLEVBQUU7UUFDbENzQyxlQUFlLENBQUNqQyxJQUFJLENBQUMsNkJBQTZCd0QsSUFBSSxDQUFDdkMsY0FBYyxDQUFDaEIsS0FBSyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQ3dELEdBQUcsQ0FBQyxVQUFBcEMsQ0FBQztVQUFBLE9BQUlBLENBQUMsQ0FBQ3BELGFBQWE7UUFBQSxFQUFDLENBQUN5RixJQUFJLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQztNQUMzSDtNQUVBLElBQUlGLElBQUksQ0FBQ3hDLGtCQUFrQixHQUFHLElBQUksRUFBRTtRQUNsQ2lCLGVBQWUsQ0FBQ2pDLElBQUksQ0FBQyxtRUFBbUUsQ0FBQztNQUMzRjtNQUVBLElBQUl3RCxJQUFJLENBQUMzQixZQUFZLEdBQUcsR0FBRyxFQUFFO1FBQzNCSSxlQUFlLENBQUNqQyxJQUFJLENBQUMsa0VBQWtFLENBQUM7TUFDMUY7TUFFQSxJQUFJd0QsSUFBSSxDQUFDekIsV0FBVyxHQUFHLEdBQUcsR0FBRyxJQUFJLEdBQUcsSUFBSSxFQUFFO1FBQ3hDRSxlQUFlLENBQUNqQyxJQUFJLENBQUMscURBQXFELENBQUM7TUFDN0U7TUFFQSxJQUFJd0QsSUFBSSxDQUFDL0IsbUJBQW1CLENBQUM5QixNQUFNLEdBQUcsQ0FBQyxFQUFFO1FBQ3ZDc0MsZUFBZSxDQUFDakMsSUFBSSxDQUFDLHFFQUFxRSxDQUFDO01BQzdGO01BRUEsT0FBT2lDLGVBQWU7SUFDeEI7RUFBQztBQUFBO0FBSUksSUFBTTBCLGtCQUFrQixHQUFBQyxPQUFBLENBQUFELGtCQUFBLEdBQUcsSUFBSXhILHlCQUF5QixDQUFDLENBQUM7QUFBQyxJQUFBMEgsUUFBQSxHQUFBRCxPQUFBLENBQUF2SCxPQUFBLEdBQ25Ec0gsa0JBQWtCIiwiaWdub3JlTGlzdCI6W119