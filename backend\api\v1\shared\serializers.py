# Shared API Serializers
from rest_framework import serializers
from django.contrib.auth import get_user_model

User = get_user_model()


class AuthSerializer(serializers.Serializer):
    """Serializer for user authentication"""
    email = serializers.EmailField()
    password = serializers.CharField(write_only=True)
    device_info = serializers.JSONField(required=False, default=dict)
    remember_me = serializers.BooleanField(default=False)


class UserProfileSerializer(serializers.ModelSerializer):
    """Serializer for user profile information"""
    full_name = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            'id', 'email', 'first_name', 'last_name', 'full_name',
            'phone', 'role', 'is_active', 'date_joined'
        ]
        read_only_fields = ['id', 'date_joined', 'is_active']
    
    def get_full_name(self, obj):
        return f"{obj.first_name} {obj.last_name}".strip()


class NotificationSerializer(serializers.Serializer):
    """Serializer for notifications"""
    id = serializers.UUIDField(read_only=True)
    title = serializers.CharField(max_length=255)
    message = serializers.CharField()
    notification_type = serializers.CharField(max_length=50)
    is_read = serializers.BooleanField(default=False)
    created_at = serializers.DateTimeField(read_only=True)


class MessagingSerializer(serializers.Serializer):
    """Serializer for messaging"""
    id = serializers.UUIDField(read_only=True)
    content = serializers.CharField()
    sender_id = serializers.UUIDField()
    recipient_id = serializers.UUIDField()
    created_at = serializers.DateTimeField(read_only=True)
