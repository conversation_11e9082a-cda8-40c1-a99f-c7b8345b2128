/**
 * Profile Screen - Customer Profile Management
 *
 * Component Contract:
 * - Displays customer profile information with avatar
 * - Provides access to profile editing and settings
 * - Shows account statistics and preferences
 * - <PERSON>les role switching and logout functionality
 * - Follows responsive design and accessibility guidelines
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Image } from 'react-native';
import { StyleSheet, Alert, Switch } from 'react-native';
import * as ImagePicker from 'expo-image-picker';

import { StandardizedButton } from '../components/ui/StandardizedButton';
import { AccessibleTouchable } from '../components/accessibility/AccessibleTouchable';
import { Card } from '../components/atoms/Card';
import { SafeAreaScreen } from '../components/ui/SafeAreaWrapper';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import type { CustomerStackParamList } from '../navigation/types';
import { useAuthStore } from '../store/authSlice';
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
  getMinimumTouchTarget,
} from '../utils/responsiveUtils';

interface ProfileMenuItem {
  id: string;
  title: string;
  subtitle?: string;
  icon: string;
  action: () => void;
  showChevron?: boolean;
  showSwitch?: boolean;
  switchValue?: boolean;
  onSwitchChange?: (value: boolean) => void;
}

interface ProfileSection {
  title: string;
  items: ProfileMenuItem[];
}

type ProfileScreenNavigationProp = StackNavigationProp<CustomerStackParamList>;

export const ProfileScreen: React.FC = () => {
  const { logout, userRole, user, updateProfile } = useAuthStore();
  const { colors, isDark, setTheme } = useTheme();
  const navigation = useNavigation<ProfileScreenNavigationProp>();
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [locationEnabled, setLocationEnabled] = useState(true);
  const [loading, setLoading] = useState(false);
  const [profileData, setProfileData] = useState<any>(null);

  // Create theme-aware styles
  const styles = createStyles(colors);

  // Use real user data or fallback to defaults
  const userData = user || {
    firstName: 'User',
    lastName: '',
    email: '<EMAIL>',
    profileImage: null,
  };

  // Load profile data on component mount
  useEffect(() => {
    loadProfileData();
  }, []);

  const loadProfileData = async () => {
    try {
      setLoading(true);
      // Import profile service dynamically to avoid circular dependencies
      const { profileService } = await import('../services/profileService');

      // Load extended profile data
      const profileDetails = await profileService.getProfileDetails();
      setProfileData(profileDetails);

      // Also load notification preferences
      const notificationPrefs = await profileService.getNotificationPreferences();
      setNotificationsEnabled(notificationPrefs.push_notifications);

    } catch (error) {
      console.error('Failed to load profile data:', error);
      // Don't show error to user for profile details as it's not critical
    } finally {
      setLoading(false);
    }
  };

  const handleAvatarChange = async () => {
    try {
      // Request permission to access media library
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (permissionResult.granted === false) {
        Alert.alert('Permission Required', 'Permission to access camera roll is required!');
        return;
      }

      // Show action sheet for image selection
      Alert.alert(
        'Change Profile Picture',
        'Choose an option',
        [
          { text: 'Camera', onPress: () => openCamera() },
          { text: 'Photo Library', onPress: () => openImagePicker() },
          { text: 'Cancel', style: 'cancel' },
        ]
      );
    } catch (error) {
      console.error('Error changing avatar:', error);
      Alert.alert('Error', 'Failed to change profile picture');
    }
  };

  const openCamera = async () => {
    try {
      const cameraPermission = await ImagePicker.requestCameraPermissionsAsync();
      if (cameraPermission.granted === false) {
        Alert.alert('Permission Required', 'Permission to access camera is required!');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        await uploadAvatar(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error opening camera:', error);
      Alert.alert('Error', 'Failed to open camera');
    }
  };

  const openImagePicker = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        await uploadAvatar(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error opening image picker:', error);
      Alert.alert('Error', 'Failed to open image picker');
    }
  };

  const uploadAvatar = async (imageUri: string) => {
    try {
      setLoading(true);

      // Import profile service dynamically
      const { profileService } = await import('../services/profileService');

      // Upload avatar using profile service
      const updatedUser = await profileService.uploadAvatar(imageUri);

      // Update user data in auth store
      updateProfile(updatedUser);

      Alert.alert('Success', 'Profile picture updated successfully');
    } catch (error) {
      console.error('Error uploading avatar:', error);
      Alert.alert('Error', 'Failed to update profile picture');
    } finally {
      setLoading(false);
    }
  };

  const handleEditProfile = () => {
    navigation.navigate('EditProfile');
  };

  const handleAccountSettings = () => {
    navigation.navigate('AccountSettings');
  };

  const handleNotificationSettings = () => {
    // TODO: Navigate to notification settings
    console.log('Navigate to notification settings');
  };

  const handlePaymentMethods = () => {
    // TODO: Navigate to payment methods
    console.log('Navigate to payment methods');
  };

  const handleAddresses = () => {
    // TODO: Navigate to addresses
    console.log('Navigate to addresses');
  };

  const handleFavorites = () => {
    // TODO: Navigate to favorites
    console.log('Navigate to favorites');
  };

  const handlePrivacySettings = () => {
    // TODO: Navigate to privacy settings
    console.log('Navigate to privacy settings');
  };

  const handleSupport = () => {
    // Navigate to support
    Alert.alert(
      'Support',
      'Contact <NAME_EMAIL> or call 1-800-VIERLA',
      [{ text: 'OK' }],
    );
  };

  const handleHelpSupport = () => {
    // TODO: Navigate to help & support
    console.log('Navigate to help & support');
  };

  const handleAbout = () => {
    // Navigate to about screen
    Alert.alert(
      'About Vierla',
      'Vierla v1.0.0\nSelf-care, simplified.\n\nMaking beauty and wellness services accessible to everyone.',
      [{ text: 'OK' }],
    );
  };

  const handleRoleSwitch = () => {
    Alert.alert(
      'Switch to Provider',
      'Would you like to switch to service provider mode to manage your business?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Switch',
          onPress: () => {
            // TODO: Implement role switching
            console.log('Switch to provider role');
          },
        },
      ],
    );
  };

  const handleLogout = () => {
    Alert.alert('Logout', 'Are you sure you want to logout?', [
      { text: 'Cancel', style: 'cancel' },
      {
        text: 'Logout',
        style: 'destructive',
        onPress: () => logout(),
      },
    ]);
  };

  const profileSections: ProfileSection[] = [
    {
      title: 'Account',
      items: [
        {
          id: 'edit-profile',
          title: 'Edit Profile',
          subtitle: 'Update your personal information',
          icon: 'person-outline',
          action: handleEditProfile,
          showChevron: true,
        },
        {
          id: 'account-settings',
          title: 'Account Settings',
          subtitle: 'Manage your account preferences',
          icon: 'settings-outline',
          action: handleAccountSettings,
          showChevron: true,
        },
        {
          id: 'role-switch',
          title: 'Become a Provider',
          subtitle: 'Offer your services to customers',
          icon: 'business-outline',
          action: handleRoleSwitch,
          showChevron: true,
        },
      ],
    },
    {
      title: 'Preferences',
      items: [
        {
          id: 'notifications',
          title: 'Push Notifications',
          subtitle: 'Receive booking and message alerts',
          icon: 'notifications-outline',
          action: handleNotificationSettings,
          showSwitch: true,
          switchValue: notificationsEnabled,
          onSwitchChange: setNotificationsEnabled,
        },
        {
          id: 'location',
          title: 'Location Services',
          subtitle: 'Find nearby service providers',
          icon: 'location-outline',
          action: () => {},
          showSwitch: true,
          switchValue: locationEnabled,
          onSwitchChange: setLocationEnabled,
        },
        {
          id: 'dark-mode',
          title: 'Dark Mode',
          subtitle: 'Use dark theme for the app',
          icon: 'moon-outline',
          action: () => {},
          showSwitch: true,
          switchValue: isDark,
          onSwitchChange: setTheme,
        },
      ],
    },
    {
      title: 'Services',
      items: [
        {
          id: 'payment-methods',
          title: 'Payment Methods',
          subtitle: 'Manage your payment options',
          icon: 'card-outline',
          action: handlePaymentMethods,
          showChevron: true,
        },
        {
          id: 'addresses',
          title: 'Addresses',
          subtitle: 'Manage your saved addresses',
          icon: 'location-outline',
          action: handleAddresses,
          showChevron: true,
        },
        {
          id: 'favorites',
          title: 'Favorites',
          subtitle: 'Your favorite service providers',
          icon: 'heart-outline',
          action: handleFavorites,
          showChevron: true,
        },
      ],
    },
    {
      title: 'Privacy & Security',
      items: [
        {
          id: 'privacy-settings',
          title: 'Privacy Settings',
          subtitle: 'Control your privacy preferences',
          icon: 'shield-outline',
          action: handlePrivacySettings,
          showChevron: true,
        },
      ],
    },
    {
      title: 'Support',
      items: [
        {
          id: 'help',
          title: 'Help & Support',
          subtitle: 'Get help with using the app',
          icon: 'help-circle-outline',
          action: handleHelpSupport,
          showChevron: true,
        },
        {
          id: 'contact-support',
          title: 'Contact Support',
          subtitle: 'Chat with our support team',
          icon: 'chatbubble-outline',
          action: handleSupport,
          showChevron: true,
        },
        {
          id: 'privacy',
          title: 'Privacy Policy',
          subtitle: 'Learn about our privacy practices',
          icon: 'shield-outline',
          action: handlePrivacySettings,
          showChevron: true,
        },
        {
          id: 'about',
          title: 'About Vierla',
          subtitle: 'App version and information',
          icon: 'information-circle-outline',
          action: handleAbout,
          showChevron: true,
        },
      ],
    },
  ];

  const renderProfileHeader = () => (
    <Card style={styles.profileHeader}>
      <View style={styles.profileInfo}>
        <View style={styles.avatarContainer}>
          <View style={styles.avatar}>
            {userData.profileImage ? (
              <Image
                source={{ uri: userData.profileImage }}
                style={styles.avatarImage}
                testID="profile-avatar-image"
              />
            ) : (
              <Text style={styles.avatarText}>
                {userData.firstName?.charAt(0)?.toUpperCase() || 'U'}
              </Text>
            )}
          </View>
          <AccessibleTouchable
            style={styles.editAvatarButton}
            onPress={handleAvatarChange}
            accessibilityLabel="Change profile picture"
            accessibilityHint="Tap to update your profile picture"
            accessibilityRole="button"
            minTouchTarget={44}
            enforceMinimumSize={true}
            showFocusIndicator={true}
            hapticFeedback={true}
            testID="edit-avatar-button">
            <Ionicons name="camera" size={16} color={colors.text.onPrimary} />
          </AccessibleTouchable>
        </View>

        <View style={styles.userInfo}>
          <Text style={styles.userName}>
            {userData.firstName && userData.lastName
              ? `${userData.firstName} ${userData.lastName}`
              : userData.email || 'User'}
          </Text>
          <Text style={styles.userEmail}>{userData.email}</Text>
          <View style={styles.userRole}>
            <Ionicons
              name="person-circle-outline"
              size={16}
              color={colors.sage400}
            />
            <Text style={styles.roleText}>
              {userRole === 'provider' ? 'Service Provider' : 'Customer'}
            </Text>
          </View>
        </View>
      </View>
    </Card>
  );

  const renderMenuItem = (item: ProfileMenuItem) => (
    <AccessibleTouchable
      key={item.id}
      style={styles.menuItem}
      onPress={item.action}
      accessibilityLabel={item.title}
      accessibilityHint={item.subtitle || `Tap to access ${item.title.toLowerCase()}`}
      accessibilityRole="button"
      minTouchTarget={44}
      enforceMinimumSize={true}
      showFocusIndicator={true}
      hapticFeedback={true}
      testID={`menu-${item.id}`}>
      <View style={styles.menuItemContent}>
        <View style={styles.menuItemLeft}>
          <View style={styles.menuIconContainer}>
            <Ionicons
              name={item.icon as any}
              size={20}
              color={colors.sage400}
            />
          </View>
          <View style={styles.menuItemText}>
            <Text style={styles.menuItemTitle}>{item.title}</Text>
            {item.subtitle && (
              <Text style={styles.menuItemSubtitle}>{item.subtitle}</Text>
            )}
          </View>
        </View>

        <View style={styles.menuItemRight}>
          {item.showSwitch && (
            <Switch
              value={item.switchValue}
              onValueChange={item.onSwitchChange}
              trackColor={{
                false: colors.background.tertiary,
                true: colors.sage200,
              }}
              thumbColor={
                item.switchValue ? colors.sage400 : colors.text.tertiary
              }
              testID={`switch-${item.id}`}
              accessibilityLabel={`${item.title} toggle`}
              accessibilityHint={`Currently ${item.switchValue ? 'enabled' : 'disabled'}`}
            />
          )}
          {item.showChevron && (
            <Ionicons
              name="chevron-forward"
              size={16}
              color={colors.text.tertiary}
            />
          )}
        </View>
      </View>
    </AccessibleTouchable>
  );

  const renderSection = (section: ProfileSection) => (
    <View key={section.title} style={styles.section}>
      <Text style={styles.sectionTitle}>{section.title}</Text>
      <Card style={styles.sectionCard}>
        {section.items.map((item, index) => (
          <View key={item.id}>
            {renderMenuItem(item)}
            {index < section.items.length - 1 && (
              <View style={styles.separator} />
            )}
          </View>
        ))}
      </Card>
    </View>
  );

  return (
    <SafeAreaScreen
      backgroundColor={colors.background.secondary}
      statusBarStyle={isDark ? 'light-content' : 'dark-content'}
      respectNotch={true}
      respectGestures={true}
      testID="profile-screen">
      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}>
        {/* Profile Header */}
        {renderProfileHeader()}

        {/* Profile Sections */}
        {profileSections.map(renderSection)}

        {/* Logout Button */}
        <View style={styles.logoutSection}>
          <StandardizedButton
            action="logout"
            onPress={handleLogout}
            variant="secondary"
            style={styles.logoutButton}
            testID="logout-button"
          />
        </View>
      </ScrollView>
    </SafeAreaScreen>
  );
};

// Create theme-aware styles function
const createStyles = (colors: typeof Colors) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    content: {
      padding: getResponsiveSpacing(20),
    },
    profileHeader: {
      marginBottom: getResponsiveSpacing(24),
      padding: getResponsiveSpacing(20),
    },
    profileInfo: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    avatarContainer: {
      position: 'relative',
      marginRight: getResponsiveSpacing(16),
    },
    avatar: {
      width: 80,
      height: 80,
      borderRadius: 40,
      backgroundColor: colors.sage400,
      justifyContent: 'center',
      alignItems: 'center',
    },
    avatarText: {
      fontSize: getResponsiveFontSize(28),
      fontWeight: '600',
      color: colors.text.onPrimary,
    },
    avatarImage: {
      width: 80,
      height: 80,
      borderRadius: 40,
    },
    editAvatarButton: {
      position: 'absolute',
      bottom: 0,
      right: 0,
      width: 28,
      height: 28,
      borderRadius: 14,
      backgroundColor: colors.sage400,
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 2,
      borderColor: colors.background.primary,
    },
    userInfo: {
      flex: 1,
    },
    userName: {
      fontSize: getResponsiveFontSize(20),
      fontWeight: '600',
      color: colors.text.primary,
      marginBottom: getResponsiveSpacing(4),
    },
    userEmail: {
      fontSize: getResponsiveFontSize(14),
      color: colors.text.secondary,
      marginBottom: getResponsiveSpacing(8),
    },
    userRole: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    roleText: {
      fontSize: getResponsiveFontSize(14),
      color: colors.sage400,
      fontWeight: '500',
      marginLeft: getResponsiveSpacing(6),
    },
    section: {
      marginBottom: getResponsiveSpacing(24),
    },
    sectionTitle: {
      fontSize: getResponsiveFontSize(16),
      fontWeight: '600',
      color: colors.text.primary,
      marginBottom: getResponsiveSpacing(12),
      paddingHorizontal: getResponsiveSpacing(4),
    },
    sectionCard: {
      padding: 0,
    },
    menuItem: {
      paddingHorizontal: getResponsiveSpacing(16),
      paddingVertical: getResponsiveSpacing(16),
      minHeight: getMinimumTouchTarget(),
    },
    menuItemContent: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    menuItemLeft: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    menuIconContainer: {
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: colors.background.tertiary,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: getResponsiveSpacing(12),
    },
    menuItemText: {
      flex: 1,
    },
    menuItemTitle: {
      fontSize: getResponsiveFontSize(16),
      fontWeight: '500',
      color: colors.text.primary,
      marginBottom: getResponsiveSpacing(2),
    },
    menuItemSubtitle: {
      fontSize: getResponsiveFontSize(14),
      color: colors.text.secondary,
      lineHeight: getResponsiveFontSize(20),
    },
    menuItemRight: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    separator: {
      height: 1,
      backgroundColor: colors.border.light,
      marginLeft: getResponsiveSpacing(60),
    },
    logoutSection: {
      marginTop: getResponsiveSpacing(24),
      marginBottom: getResponsiveSpacing(40),
    },
    logoutButton: {
      // StandardizedButton handles its own styling
    },
  });
