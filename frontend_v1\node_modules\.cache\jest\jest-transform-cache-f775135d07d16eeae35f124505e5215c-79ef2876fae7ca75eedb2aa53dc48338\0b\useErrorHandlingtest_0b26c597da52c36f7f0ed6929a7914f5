57aee3ffebd961cc3276343a8d2066b7
_getJestObj().mock("../../services/performanceMonitor");
_getJestObj().mock("../../utils/errorHandlingUtils", function () {
  return {
    createAppError: jest.fn(function (error, context) {
      return Object.assign({}, error, {
        id: 'test-error-id',
        context: context,
        timestamp: Date.now()
      });
    }),
    logError: jest.fn(),
    AppError: function (_Error) {
      function AppError(message) {
        var _this;
        (0, _classCallCheck2.default)(this, AppError);
        _this = _callSuper(this, AppError, [message]);
        _this.name = 'AppError';
        return _this;
      }
      (0, _inherits2.default)(AppError, _Error);
      return (0, _createClass2.default)(AppError);
    }((0, _wrapNativeSuper2.default)(Error))
  };
});
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _wrapNativeSuper2 = _interopRequireDefault(require("@babel/runtime/helpers/wrapNativeSuper"));
var _reactNative = require("@testing-library/react-native");
var _reactNative2 = require("react-native");
var _useErrorHandling = require("../useErrorHandling");
var _performanceMonitor = require("../../services/performanceMonitor");
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _getJestObj() {
  var _require2 = require("@jest/globals"),
    jest = _require2.jest;
  _getJestObj = function _getJestObj() {
    return jest;
  };
  return jest;
}
var mockPerformanceMonitor = _performanceMonitor.performanceMonitor;
describe('useErrorHandling', function () {
  beforeEach(function () {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });
  afterEach(function () {
    jest.useRealTimers();
  });
  describe('Basic Error Handling', function () {
    it('initializes with no error state', function () {
      var _renderHook = (0, _reactNative.renderHook)(function () {
          return (0, _useErrorHandling.useErrorHandling)();
        }),
        result = _renderHook.result;
      expect(result.current.error).toBeNull();
      expect(result.current.isError).toBe(false);
      expect(result.current.retryCount).toBe(0);
      expect(result.current.canRetry).toBe(true);
    });
    it('handles string errors', function () {
      var _result$current$error;
      var _renderHook2 = (0, _reactNative.renderHook)(function () {
          return (0, _useErrorHandling.useErrorHandling)();
        }),
        result = _renderHook2.result;
      (0, _reactNative.act)(function () {
        result.current.handleError('Test error message');
      });
      expect(result.current.error).toBeDefined();
      expect((_result$current$error = result.current.error) == null ? void 0 : _result$current$error.message).toBe('Test error message');
      expect(result.current.isError).toBe(true);
    });
    it('handles Error objects', function () {
      var _result$current$error2;
      var _renderHook3 = (0, _reactNative.renderHook)(function () {
          return (0, _useErrorHandling.useErrorHandling)();
        }),
        result = _renderHook3.result;
      var testError = new Error('Test error object');
      (0, _reactNative.act)(function () {
        result.current.handleError(testError);
      });
      expect(result.current.error).toBeDefined();
      expect((_result$current$error2 = result.current.error) == null ? void 0 : _result$current$error2.message).toBe('Test error object');
      expect(result.current.isError).toBe(true);
    });
    it('clears error state', function () {
      var _renderHook4 = (0, _reactNative.renderHook)(function () {
          return (0, _useErrorHandling.useErrorHandling)();
        }),
        result = _renderHook4.result;
      (0, _reactNative.act)(function () {
        result.current.handleError('Test error');
      });
      expect(result.current.isError).toBe(true);
      (0, _reactNative.act)(function () {
        result.current.clearError();
      });
      expect(result.current.error).toBeNull();
      expect(result.current.isError).toBe(false);
      expect(result.current.retryCount).toBe(0);
    });
  });
  describe('Retry Logic', function () {
    it('increments retry count on retry', (0, _asyncToGenerator2.default)(function* () {
      var _renderHook5 = (0, _reactNative.renderHook)(function () {
          return (0, _useErrorHandling.useErrorHandling)();
        }),
        result = _renderHook5.result;
      (0, _reactNative.act)(function () {
        result.current.handleError('Test error');
      });
      expect(result.current.retryCount).toBe(0);
      yield (0, _reactNative.act)((0, _asyncToGenerator2.default)(function* () {
        yield result.current.retry();
        jest.advanceTimersByTime(2000);
      }));
      expect(result.current.retryCount).toBe(1);
    }));
    it('respects max retries limit', (0, _asyncToGenerator2.default)(function* () {
      var _renderHook6 = (0, _reactNative.renderHook)(function () {
          return (0, _useErrorHandling.useErrorHandling)({
            maxRetries: 2
          });
        }),
        result = _renderHook6.result;
      (0, _reactNative.act)(function () {
        result.current.handleError('Test error');
      });
      yield (0, _reactNative.act)((0, _asyncToGenerator2.default)(function* () {
        yield result.current.retry();
        jest.advanceTimersByTime(2000);
      }));
      expect(result.current.canRetry).toBe(true);
      yield (0, _reactNative.act)((0, _asyncToGenerator2.default)(function* () {
        yield result.current.retry();
        jest.advanceTimersByTime(2000);
      }));
      expect(result.current.canRetry).toBe(false);
      yield (0, _reactNative.act)((0, _asyncToGenerator2.default)(function* () {
        yield result.current.retry();
      }));
      expect(result.current.retryCount).toBe(2);
    }));
    it('uses progressive retry delays', (0, _asyncToGenerator2.default)(function* () {
      var _renderHook7 = (0, _reactNative.renderHook)(function () {
          return (0, _useErrorHandling.useErrorHandling)({
            retryDelay: 1000,
            progressiveRetryDelay: true
          });
        }),
        result = _renderHook7.result;
      (0, _reactNative.act)(function () {
        result.current.handleError('Test error');
      });
      var startTime = Date.now();
      yield (0, _reactNative.act)((0, _asyncToGenerator2.default)(function* () {
        yield result.current.retry();
        jest.advanceTimersByTime(1000);
      }));
      yield (0, _reactNative.act)((0, _asyncToGenerator2.default)(function* () {
        yield result.current.retry();
        jest.advanceTimersByTime(1500);
      }));
      expect(result.current.retryCount).toBe(2);
    }));
    it('uses fixed retry delay when progressive is disabled', (0, _asyncToGenerator2.default)(function* () {
      var _renderHook8 = (0, _reactNative.renderHook)(function () {
          return (0, _useErrorHandling.useErrorHandling)({
            retryDelay: 1000,
            progressiveRetryDelay: false
          });
        }),
        result = _renderHook8.result;
      (0, _reactNative.act)(function () {
        result.current.handleError('Test error');
      });
      yield (0, _reactNative.act)((0, _asyncToGenerator2.default)(function* () {
        yield result.current.retry();
        jest.advanceTimersByTime(1000);
      }));
      yield (0, _reactNative.act)((0, _asyncToGenerator2.default)(function* () {
        yield result.current.retry();
        jest.advanceTimersByTime(1000);
      }));
      expect(result.current.retryCount).toBe(2);
    }));
  });
  describe('Error Classification', function () {
    it('identifies network errors', function () {
      var _renderHook9 = (0, _reactNative.renderHook)(function () {
          return (0, _useErrorHandling.useErrorHandling)();
        }),
        result = _renderHook9.result;
      (0, _reactNative.act)(function () {
        result.current.handleError('Network request failed');
      });
      expect(result.current.isNetworkError()).toBe(true);
      expect(result.current.isServerError()).toBe(false);
      expect(result.current.isAuthError()).toBe(false);
    });
    it('identifies server errors', function () {
      var _renderHook0 = (0, _reactNative.renderHook)(function () {
          return (0, _useErrorHandling.useErrorHandling)();
        }),
        result = _renderHook0.result;
      (0, _reactNative.act)(function () {
        result.current.handleError('500 Internal Server Error');
      });
      expect(result.current.isServerError()).toBe(true);
      expect(result.current.isNetworkError()).toBe(false);
      expect(result.current.isAuthError()).toBe(false);
    });
    it('identifies authentication errors', function () {
      var _renderHook1 = (0, _reactNative.renderHook)(function () {
          return (0, _useErrorHandling.useErrorHandling)();
        }),
        result = _renderHook1.result;
      (0, _reactNative.act)(function () {
        result.current.handleError('401 Unauthorized');
      });
      expect(result.current.isAuthError()).toBe(true);
      expect(result.current.isNetworkError()).toBe(false);
      expect(result.current.isServerError()).toBe(false);
    });
    it('provides appropriate error messages', function () {
      var _renderHook10 = (0, _reactNative.renderHook)(function () {
          return (0, _useErrorHandling.useErrorHandling)();
        }),
        result = _renderHook10.result;
      (0, _reactNative.act)(function () {
        result.current.handleError('Network connection failed');
      });
      expect(result.current.getErrorMessage()).toContain('internet connection');
      (0, _reactNative.act)(function () {
        result.current.clearError();
        result.current.handleError('500 server error');
      });
      expect(result.current.getErrorMessage()).toContain('servers are experiencing');
      (0, _reactNative.act)(function () {
        result.current.clearError();
        result.current.handleError('401 unauthorized');
      });
      expect(result.current.getErrorMessage()).toContain('session has expired');
    });
  });
  describe('Callbacks and Options', function () {
    it('calls onError callback', function () {
      var onError = jest.fn();
      var _renderHook11 = (0, _reactNative.renderHook)(function () {
          return (0, _useErrorHandling.useErrorHandling)({
            onError: onError
          });
        }),
        result = _renderHook11.result;
      (0, _reactNative.act)(function () {
        result.current.handleError('Test error');
      });
      expect(onError).toHaveBeenCalledWith(expect.objectContaining({
        message: 'Test error'
      }));
    });
    it('calls onRetry callback', (0, _asyncToGenerator2.default)(function* () {
      var onRetry = jest.fn();
      var _renderHook12 = (0, _reactNative.renderHook)(function () {
          return (0, _useErrorHandling.useErrorHandling)({
            onRetry: onRetry
          });
        }),
        result = _renderHook12.result;
      (0, _reactNative.act)(function () {
        result.current.handleError('Test error');
      });
      yield (0, _reactNative.act)((0, _asyncToGenerator2.default)(function* () {
        yield result.current.retry();
        jest.advanceTimersByTime(2000);
      }));
      expect(onRetry).toHaveBeenCalledWith(1);
    }));
    it('calls onMaxRetriesExceeded callback', function () {
      var onMaxRetriesExceeded = jest.fn();
      var _renderHook13 = (0, _reactNative.renderHook)(function () {
          return (0, _useErrorHandling.useErrorHandling)({
            maxRetries: 1,
            onMaxRetriesExceeded: onMaxRetriesExceeded
          });
        }),
        result = _renderHook13.result;
      (0, _reactNative.act)(function () {
        result.current.handleError('Test error');
      });
      (0, _reactNative.act)(function () {
        result.current.handleError('Another error');
      });
      expect(onMaxRetriesExceeded).toHaveBeenCalled();
    });
    it('respects error reporting setting', function () {
      var _renderHook14 = (0, _reactNative.renderHook)(function () {
          return (0, _useErrorHandling.useErrorHandling)({
            reportErrors: false
          });
        }),
        result = _renderHook14.result;
      (0, _reactNative.act)(function () {
        result.current.handleError('Test error');
      });
      var _require = require("../../utils/errorHandlingUtils"),
        logError = _require.logError;
      expect(logError).not.toHaveBeenCalled();
    });
  });
  describe('Performance Tracking Integration', function () {
    it('tracks error handling performance', function () {
      var _renderHook15 = (0, _reactNative.renderHook)(function () {
          return (0, _useErrorHandling.useErrorHandling)();
        }),
        result = _renderHook15.result;
      (0, _reactNative.act)(function () {
        result.current.handleError('Test error');
      });
      expect(mockPerformanceMonitor.trackUserInteraction).toHaveBeenCalledWith('error_handled', 0, expect.objectContaining({
        errorType: expect.any(String),
        errorMessage: 'Test error'
      }));
    });
    it('tracks retry performance', (0, _asyncToGenerator2.default)(function* () {
      var _renderHook16 = (0, _reactNative.renderHook)(function () {
          return (0, _useErrorHandling.useErrorHandling)();
        }),
        result = _renderHook16.result;
      (0, _reactNative.act)(function () {
        result.current.handleError('Test error');
      });
      yield (0, _reactNative.act)((0, _asyncToGenerator2.default)(function* () {
        yield result.current.retry();
        jest.advanceTimersByTime(2000);
      }));
      expect(mockPerformanceMonitor.trackUserInteraction).toHaveBeenCalledWith('error_retry', 0, expect.objectContaining({
        retryCount: 1,
        retryDelay: expect.any(Number)
      }));
    }));
  });
  describe('App State Integration', function () {
    it('retries on app focus when enabled', (0, _asyncToGenerator2.default)(function* () {
      var _renderHook17 = (0, _reactNative.renderHook)(function () {
          return (0, _useErrorHandling.useErrorHandling)({
            autoRetryOnAppFocus: true
          });
        }),
        result = _renderHook17.result;
      (0, _reactNative.act)(function () {
        result.current.handleError('Test error');
      });
      expect(result.current.retryCount).toBe(0);
      (0, _reactNative.act)(function () {
        _reactNative2.AppState.currentState = 'background';
      });
      (0, _reactNative.act)(function () {
        _reactNative2.AppState.currentState = 'active';
        var listeners = _reactNative2.AppState.addEventListener.mock.calls;
        if (listeners.length > 0) {
          var callback = listeners[0][1];
          callback('active');
        }
      });
      yield (0, _reactNative.act)((0, _asyncToGenerator2.default)(function* () {
        jest.advanceTimersByTime(2000);
      }));
      expect(result.current.retryCount).toBe(1);
    }));
    it('does not retry on app focus when disabled', function () {
      var _renderHook18 = (0, _reactNative.renderHook)(function () {
          return (0, _useErrorHandling.useErrorHandling)({
            autoRetryOnAppFocus: false
          });
        }),
        result = _renderHook18.result;
      (0, _reactNative.act)(function () {
        result.current.handleError('Test error');
      });
      (0, _reactNative.act)(function () {
        _reactNative2.AppState.currentState = 'active';
      });
      expect(result.current.retryCount).toBe(0);
    });
  });
  describe('Cleanup', function () {
    it('cleans up timeouts on unmount', function () {
      var clearTimeoutSpy = jest.spyOn(global, 'clearTimeout');
      var _renderHook19 = (0, _reactNative.renderHook)(function () {
          return (0, _useErrorHandling.useErrorHandling)();
        }),
        result = _renderHook19.result,
        unmount = _renderHook19.unmount;
      (0, _reactNative.act)(function () {
        result.current.handleError('Test error');
      });
      (0, _reactNative.act)(function () {
        result.current.retry();
      });
      unmount();
      expect(clearTimeoutSpy).toHaveBeenCalled();
      clearTimeoutSpy.mockRestore();
    });
    it('removes app state listeners on unmount', function () {
      var removeListenerSpy = jest.fn();
      _reactNative2.AppState.addEventListener.mockReturnValue({
        remove: removeListenerSpy
      });
      var _renderHook20 = (0, _reactNative.renderHook)(function () {
          return (0, _useErrorHandling.useErrorHandling)({
            autoRetryOnAppFocus: true
          });
        }),
        unmount = _renderHook20.unmount;
      unmount();
      expect(removeListenerSpy).toHaveBeenCalled();
    });
  });
  describe('Edge Cases', function () {
    it('handles retry when no error exists', (0, _asyncToGenerator2.default)(function* () {
      var _renderHook21 = (0, _reactNative.renderHook)(function () {
          return (0, _useErrorHandling.useErrorHandling)();
        }),
        result = _renderHook21.result;
      yield (0, _reactNative.act)((0, _asyncToGenerator2.default)(function* () {
        yield result.current.retry();
      }));
      expect(result.current.retryCount).toBe(0);
    }));
    it('handles retry when max retries already exceeded', (0, _asyncToGenerator2.default)(function* () {
      var _renderHook22 = (0, _reactNative.renderHook)(function () {
          return (0, _useErrorHandling.useErrorHandling)({
            maxRetries: 1
          });
        }),
        result = _renderHook22.result;
      (0, _reactNative.act)(function () {
        result.current.handleError('Test error');
      });
      yield (0, _reactNative.act)((0, _asyncToGenerator2.default)(function* () {
        yield result.current.retry();
        jest.advanceTimersByTime(2000);
      }));
      yield (0, _reactNative.act)((0, _asyncToGenerator2.default)(function* () {
        yield result.current.retry();
        jest.advanceTimersByTime(2000);
      }));
      yield (0, _reactNative.act)((0, _asyncToGenerator2.default)(function* () {
        yield result.current.retry();
      }));
      expect(result.current.retryCount).toBe(1);
    }));
    it('handles component unmount during retry delay', (0, _asyncToGenerator2.default)(function* () {
      var _renderHook23 = (0, _reactNative.renderHook)(function () {
          return (0, _useErrorHandling.useErrorHandling)();
        }),
        result = _renderHook23.result,
        unmount = _renderHook23.unmount;
      (0, _reactNative.act)(function () {
        result.current.handleError('Test error');
      });
      (0, _reactNative.act)(function () {
        result.current.retry();
      });
      unmount();
      (0, _reactNative.act)(function () {
        jest.advanceTimersByTime(2000);
      });
    }));
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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