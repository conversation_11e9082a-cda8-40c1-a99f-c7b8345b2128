c401de903a128bf157e27442102d0f3f
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.authService = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _reactNative = require("react-native");
var getApiBaseUrl = function getApiBaseUrl() {
  if (!__DEV__) {
    return 'https://api.vierla.com';
  }
  if (_reactNative.Platform.OS === 'android') {
    return 'http://192.168.2.65:8000';
  } else {
    return 'http://192.168.2.65:8000';
  }
};
var API_BASE_URL = getApiBaseUrl();
var AuthService = function () {
  function AuthService() {
    (0, _classCallCheck2.default)(this, AuthService);
    this.baseUrl = API_BASE_URL;
  }
  return (0, _createClass2.default)(AuthService, [{
    key: "makeRequest",
    value: (function () {
      var _makeRequest = (0, _asyncToGenerator2.default)(function* (endpoint, options) {
        var url = `${this.baseUrl}${endpoint}`;
        try {
          var response = yield fetch(url, Object.assign({}, options, {
            headers: Object.assign({
              'Content-Type': 'application/json'
            }, options.headers)
          }));
          var data = yield response.json();
          if (!response.ok) {
            var error = data;
            var errorMessage = 'An error occurred';
            if (error.detail) {
              errorMessage = error.detail;
            } else if (error.message) {
              errorMessage = error.message;
            } else if (error.non_field_errors && error.non_field_errors.length > 0) {
              errorMessage = error.non_field_errors[0];
            } else if (error.errors) {
              var firstError = Object.values(error.errors)[0];
              if (firstError && firstError.length > 0) {
                errorMessage = firstError[0];
              }
            }
            throw new Error(errorMessage);
          }
          return data;
        } catch (error) {
          if (error instanceof Error) {
            throw error;
          }
          throw new Error('Network error occurred');
        }
      });
      function makeRequest(_x, _x2) {
        return _makeRequest.apply(this, arguments);
      }
      return makeRequest;
    }())
  }, {
    key: "login",
    value: (function () {
      var _login = (0, _asyncToGenerator2.default)(function* (credentials) {
        return this.makeRequest('/api/auth/login/', {
          method: 'POST',
          body: JSON.stringify(credentials)
        });
      });
      function login(_x3) {
        return _login.apply(this, arguments);
      }
      return login;
    }())
  }, {
    key: "register",
    value: (function () {
      var _register = (0, _asyncToGenerator2.default)(function* (userData) {
        return this.makeRequest('/api/auth/register/', {
          method: 'POST',
          body: JSON.stringify(userData)
        });
      });
      function register(_x4) {
        return _register.apply(this, arguments);
      }
      return register;
    }())
  }, {
    key: "passwordlessLogin",
    value: (function () {
      var _passwordlessLogin = (0, _asyncToGenerator2.default)(function* (request) {
        return new Promise(function (resolve, reject) {
          setTimeout(function () {
            if (request.method === 'email' && request.email) {
              resolve({
                access: 'mock-access-token-passwordless',
                refresh: 'mock-refresh-token-passwordless',
                user: {
                  id: '1',
                  email: request.email,
                  first_name: 'Passwordless',
                  last_name: 'User',
                  role: 'customer',
                  is_verified: true
                }
              });
            } else if (request.method === 'phone' && request.phone) {
              resolve({
                access: 'mock-access-token-passwordless',
                refresh: 'mock-refresh-token-passwordless',
                user: {
                  id: '2',
                  email: '<EMAIL>',
                  first_name: 'Phone',
                  last_name: 'User',
                  role: 'customer',
                  is_verified: true,
                  phone: request.phone
                }
              });
            } else if (request.method === 'biometric') {
              resolve({
                access: 'mock-access-token-passwordless',
                refresh: 'mock-refresh-token-passwordless',
                user: {
                  id: '3',
                  email: '<EMAIL>',
                  first_name: 'Biometric',
                  last_name: 'User',
                  role: 'customer',
                  is_verified: true
                }
              });
            } else {
              reject(new Error('Invalid passwordless authentication request'));
            }
          }, 1000);
        });
      });
      function passwordlessLogin(_x5) {
        return _passwordlessLogin.apply(this, arguments);
      }
      return passwordlessLogin;
    }())
  }, {
    key: "refreshToken",
    value: (function () {
      var _refreshToken2 = (0, _asyncToGenerator2.default)(function* (_refreshToken) {
        return this.makeRequest('/api/auth/token/refresh/', {
          method: 'POST',
          body: JSON.stringify({
            refresh: _refreshToken
          })
        });
      });
      function refreshToken(_x6) {
        return _refreshToken2.apply(this, arguments);
      }
      return refreshToken;
    }())
  }, {
    key: "authenticateWithEmail",
    value: (function () {
      var _authenticateWithEmail = (0, _asyncToGenerator2.default)(function* (email) {
        return {
          success: true,
          user: {
            id: 'email_' + Date.now(),
            email: email,
            first_name: 'Email',
            last_name: 'User',
            role: 'customer',
            is_verified: true
          },
          token: 'mock_email_token_' + Date.now(),
          refreshToken: 'mock_email_refresh_' + Date.now()
        };
      });
      function authenticateWithEmail(_x7) {
        return _authenticateWithEmail.apply(this, arguments);
      }
      return authenticateWithEmail;
    }())
  }, {
    key: "authenticateWithPhone",
    value: (function () {
      var _authenticateWithPhone = (0, _asyncToGenerator2.default)(function* (phone) {
        return {
          success: true,
          user: {
            id: 'phone_' + Date.now(),
            phone: phone,
            first_name: 'Phone',
            last_name: 'User',
            role: 'customer',
            is_verified: true
          },
          token: 'mock_phone_token_' + Date.now(),
          refreshToken: 'mock_phone_refresh_' + Date.now()
        };
      });
      function authenticateWithPhone(_x8) {
        return _authenticateWithPhone.apply(this, arguments);
      }
      return authenticateWithPhone;
    }())
  }, {
    key: "authenticateWithBiometric",
    value: (function () {
      var _authenticateWithBiometric = (0, _asyncToGenerator2.default)(function* (userData) {
        return {
          success: true,
          user: userData,
          token: 'mock_biometric_token_' + Date.now(),
          refreshToken: 'mock_biometric_refresh_' + Date.now()
        };
      });
      function authenticateWithBiometric(_x9) {
        return _authenticateWithBiometric.apply(this, arguments);
      }
      return authenticateWithBiometric;
    }())
  }, {
    key: "logout",
    value: (function () {
      var _logout = (0, _asyncToGenerator2.default)(function* (refreshToken) {
        try {
          yield this.makeRequest('/api/auth/logout/', {
            method: 'POST',
            body: JSON.stringify({
              refresh: refreshToken
            })
          });
        } catch (error) {
          console.warn('Logout API call failed:', error);
        }
      });
      function logout(_x0) {
        return _logout.apply(this, arguments);
      }
      return logout;
    }())
  }, {
    key: "getProfile",
    value: (function () {
      var _getProfile = (0, _asyncToGenerator2.default)(function* (token) {
        return this.makeRequest('/api/auth/profile/', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
      });
      function getProfile(_x1) {
        return _getProfile.apply(this, arguments);
      }
      return getProfile;
    }())
  }, {
    key: "updateProfile",
    value: (function () {
      var _updateProfile = (0, _asyncToGenerator2.default)(function* (profileData, token) {
        return this.makeRequest('/api/auth/profile/', {
          method: 'PATCH',
          body: JSON.stringify(profileData),
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
      });
      function updateProfile(_x10, _x11) {
        return _updateProfile.apply(this, arguments);
      }
      return updateProfile;
    }())
  }, {
    key: "requestPasswordReset",
    value: (function () {
      var _requestPasswordReset = (0, _asyncToGenerator2.default)(function* (data) {
        return this.makeRequest('/api/auth/password-reset/', {
          method: 'POST',
          body: JSON.stringify(data)
        });
      });
      function requestPasswordReset(_x12) {
        return _requestPasswordReset.apply(this, arguments);
      }
      return requestPasswordReset;
    }())
  }, {
    key: "confirmPasswordReset",
    value: (function () {
      var _confirmPasswordReset = (0, _asyncToGenerator2.default)(function* (data) {
        return this.makeRequest('/api/auth/password-reset/confirm/', {
          method: 'POST',
          body: JSON.stringify(data)
        });
      });
      function confirmPasswordReset(_x13) {
        return _confirmPasswordReset.apply(this, arguments);
      }
      return confirmPasswordReset;
    }())
  }, {
    key: "changePassword",
    value: (function () {
      var _changePassword = (0, _asyncToGenerator2.default)(function* (data, token) {
        return this.makeRequest('/api/auth/change-password/', {
          method: 'POST',
          body: JSON.stringify(data),
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
      });
      function changePassword(_x14, _x15) {
        return _changePassword.apply(this, arguments);
      }
      return changePassword;
    }())
  }, {
    key: "validateToken",
    value: (function () {
      var _validateToken = (0, _asyncToGenerator2.default)(function* (token) {
        try {
          yield this.makeRequest('/api/auth/validate-token/', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });
          return true;
        } catch (_unused) {
          return false;
        }
      });
      function validateToken(_x16) {
        return _validateToken.apply(this, arguments);
      }
      return validateToken;
    }())
  }]);
}();
var authService = exports.authService = new AuthService();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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