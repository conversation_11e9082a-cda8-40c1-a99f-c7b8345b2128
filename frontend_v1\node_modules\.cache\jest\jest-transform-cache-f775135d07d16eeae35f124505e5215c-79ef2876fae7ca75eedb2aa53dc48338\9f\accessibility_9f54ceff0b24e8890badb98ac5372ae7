f7059d639120607ea948e53507e2e155
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.isValidAccessibilityRole = exports.getSafeAccessibilityRole = exports.WCAG_CONSTANTS = exports.VALID_ACCESSIBILITY_ROLES = exports.SemanticMarkupUtils = exports.SemanticMarkupUtils = exports.ScreenReaderUtils = exports.ScreenReaderUtils = exports.FocusManagementUtils = exports.FocusManagementUtils = exports.ColorContrastUtils = exports.ColorContrastUtils = exports.AdvancedAccessibilityUtils = exports.AdvancedAccessibilityUtils = exports.AccessibilityTestUtils = exports.AccessibilityTestUtils = exports.AccessibilityMonitoringUtils = exports.AccessibilityMonitoringUtils = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _reactNative = require("react-native");
var WCAG_CONSTANTS = exports.WCAG_CONSTANTS = {
  CONTRAST_RATIOS: {
    NORMAL_TEXT: 4.5,
    LARGE_TEXT: 3.0,
    NON_TEXT: 3.0
  },
  TOUCH_TARGET: {
    MIN_SIZE: 44,
    RECOMMENDED_SIZE: 48
  },
  ANIMATION: {
    MAX_DURATION: 5000,
    REDUCED_MOTION_DURATION: 200
  },
  TEXT: {
    MIN_SIZE: 12,
    LARGE_TEXT_THRESHOLD: 18
  }
};
var ScreenReaderUtils = exports.ScreenReaderUtils = exports.ScreenReaderUtils = function () {
  function ScreenReaderUtils() {
    (0, _classCallCheck2.default)(this, ScreenReaderUtils);
  }
  return (0, _createClass2.default)(ScreenReaderUtils, null, [{
    key: "isScreenReaderEnabled",
    value: (function () {
      var _isScreenReaderEnabled = (0, _asyncToGenerator2.default)(function* () {
        try {
          return yield _reactNative.AccessibilityInfo.isScreenReaderEnabled();
        } catch (error) {
          console.warn('Failed to check screen reader status:', error);
          return false;
        }
      });
      function isScreenReaderEnabled() {
        return _isScreenReaderEnabled.apply(this, arguments);
      }
      return isScreenReaderEnabled;
    }())
  }, {
    key: "announceForAccessibility",
    value: function announceForAccessibility(message) {
      if (_reactNative.Platform.OS === 'ios' || _reactNative.Platform.OS === 'android') {
        _reactNative.AccessibilityInfo.announceForAccessibility(message);
      }
    }
  }, {
    key: "setAccessibilityFocus",
    value: function setAccessibilityFocus(reactTag) {
      if (_reactNative.Platform.OS === 'ios' || _reactNative.Platform.OS === 'android') {
        _reactNative.AccessibilityInfo.setAccessibilityFocus(reactTag);
      }
    }
  }, {
    key: "generateFormFieldLabel",
    value: function generateFormFieldLabel(label) {
      var required = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
      var error = arguments.length > 2 ? arguments[2] : undefined;
      var accessibleLabel = label;
      if (required) {
        accessibleLabel += ', required';
      }
      if (error) {
        accessibleLabel += `, error: ${error}`;
      }
      return accessibleLabel;
    }
  }, {
    key: "generateInteractionHint",
    value: function generateInteractionHint(action, additionalInfo) {
      var hint = `Double tap to ${action}`;
      if (additionalInfo) {
        hint += `. ${additionalInfo}`;
      }
      return hint;
    }
  }]);
}();
var ColorContrastUtils = exports.ColorContrastUtils = exports.ColorContrastUtils = function () {
  function ColorContrastUtils() {
    (0, _classCallCheck2.default)(this, ColorContrastUtils);
  }
  return (0, _createClass2.default)(ColorContrastUtils, null, [{
    key: "getRelativeLuminance",
    value: function getRelativeLuminance(color) {
      var hex = color.replace('#', '');
      var r = parseInt(hex.substr(0, 2), 16) / 255;
      var g = parseInt(hex.substr(2, 2), 16) / 255;
      var b = parseInt(hex.substr(4, 2), 16) / 255;
      var sRGB = [r, g, b].map(function (c) {
        return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
      });
      return 0.2126 * sRGB[0] + 0.7152 * sRGB[1] + 0.0722 * sRGB[2];
    }
  }, {
    key: "getContrastRatio",
    value: function getContrastRatio(color1, color2) {
      var lum1 = this.getRelativeLuminance(color1);
      var lum2 = this.getRelativeLuminance(color2);
      var lighter = Math.max(lum1, lum2);
      var darker = Math.min(lum1, lum2);
      return (lighter + 0.05) / (darker + 0.05);
    }
  }, {
    key: "meetsWCAGAA",
    value: function meetsWCAGAA(foreground, background) {
      var isLargeText = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
      var ratio = this.getContrastRatio(foreground, background);
      var requiredRatio = isLargeText ? WCAG_CONSTANTS.CONTRAST_RATIOS.LARGE_TEXT : WCAG_CONSTANTS.CONTRAST_RATIOS.NORMAL_TEXT;
      return ratio >= requiredRatio;
    }
  }, {
    key: "suggestAccessibleColor",
    value: function suggestAccessibleColor(foreground, background) {
      var isLargeText = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
      if (this.meetsWCAGAA(foreground, background, isLargeText)) {
        return null;
      }
      var hex = foreground.replace('#', '');
      var r = parseInt(hex.substr(0, 2), 16);
      var g = parseInt(hex.substr(2, 2), 16);
      var b = parseInt(hex.substr(4, 2), 16);
      var factors = [0.7, 0.5, 0.3, 0.1];
      for (var factor of factors) {
        var newR = Math.floor(r * factor);
        var newG = Math.floor(g * factor);
        var newB = Math.floor(b * factor);
        var newColor = `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
        if (this.meetsWCAGAA(newColor, background, isLargeText)) {
          return newColor;
        }
      }
      return this.meetsWCAGAA('#000000', background, isLargeText) ? '#000000' : null;
    }
  }]);
}();
var FocusManagementUtils = exports.FocusManagementUtils = exports.FocusManagementUtils = function () {
  function FocusManagementUtils() {
    (0, _classCallCheck2.default)(this, FocusManagementUtils);
  }
  return (0, _createClass2.default)(FocusManagementUtils, null, [{
    key: "pushFocus",
    value: function pushFocus(reactTag) {
      this.focusStack.push(reactTag);
      ScreenReaderUtils.setAccessibilityFocus(reactTag);
    }
  }, {
    key: "popFocus",
    value: function popFocus() {
      this.focusStack.pop();
      var previousFocus = this.focusStack[this.focusStack.length - 1];
      if (previousFocus) {
        ScreenReaderUtils.setAccessibilityFocus(previousFocus);
      }
    }
  }, {
    key: "clearFocusStack",
    value: function clearFocusStack() {
      this.focusStack = [];
    }
  }, {
    key: "createFocusTrap",
    value: function createFocusTrap(firstElement, lastElement) {
      return {
        onKeyPress: function onKeyPress(event) {
          if (event.key === 'Tab') {
            if (event.shiftKey) {
              if (event.target === firstElement) {
                event.preventDefault();
                ScreenReaderUtils.setAccessibilityFocus(lastElement);
              }
            } else {
              if (event.target === lastElement) {
                event.preventDefault();
                ScreenReaderUtils.setAccessibilityFocus(firstElement);
              }
            }
          }
        }
      };
    }
  }]);
}();
FocusManagementUtils.focusStack = [];
var SemanticMarkupUtils = exports.SemanticMarkupUtils = exports.SemanticMarkupUtils = function () {
  function SemanticMarkupUtils() {
    (0, _classCallCheck2.default)(this, SemanticMarkupUtils);
  }
  return (0, _createClass2.default)(SemanticMarkupUtils, null, [{
    key: "generateHeadingProps",
    value: function generateHeadingProps(level, text) {
      return {
        accessibilityRole: 'header',
        accessibilityLevel: level,
        accessibilityLabel: text
      };
    }
  }, {
    key: "generateListProps",
    value: function generateListProps(itemCount) {
      return {
        accessibilityRole: 'list',
        accessibilityLabel: `List with ${itemCount} items`
      };
    }
  }, {
    key: "generateListItemProps",
    value: function generateListItemProps(index, total, content) {
      return {
        accessibilityRole: 'listitem',
        accessibilityLabel: `${content}, ${index + 1} of ${total}`
      };
    }
  }, {
    key: "generateButtonProps",
    value: function generateButtonProps(label, action, state) {
      return {
        accessibilityRole: 'button',
        accessibilityLabel: label,
        accessibilityHint: action ? ScreenReaderUtils.generateInteractionHint(action) : undefined,
        accessibilityState: state
      };
    }
  }, {
    key: "generateInputProps",
    value: function generateInputProps(label, value) {
      var required = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
      var error = arguments.length > 3 ? arguments[3] : undefined;
      return {
        accessibilityLabel: ScreenReaderUtils.generateFormFieldLabel(label, required, error),
        accessibilityValue: value ? {
          text: value
        } : undefined,
        accessibilityState: {
          disabled: false
        }
      };
    }
  }]);
}();
var VALID_ACCESSIBILITY_ROLES = exports.VALID_ACCESSIBILITY_ROLES = ['button', 'link', 'search', 'image', 'keyboardkey', 'text', 'adjustable', 'imagebutton', 'header', 'summary', 'alert', 'checkbox', 'combobox', 'menu', 'menubar', 'menuitem', 'progressbar', 'radio', 'radiogroup', 'scrollbar', 'spinbutton', 'switch', 'tab', 'tablist', 'timer', 'toolbar', 'grid', 'list', 'listitem', 'none'];
var isValidAccessibilityRole = exports.isValidAccessibilityRole = function isValidAccessibilityRole(role) {
  return VALID_ACCESSIBILITY_ROLES.includes(role);
};
var getSafeAccessibilityRole = exports.getSafeAccessibilityRole = function getSafeAccessibilityRole(role) {
  return isValidAccessibilityRole(role) ? role : 'none';
};
var AccessibilityTestUtils = exports.AccessibilityTestUtils = exports.AccessibilityTestUtils = function () {
  function AccessibilityTestUtils() {
    (0, _classCallCheck2.default)(this, AccessibilityTestUtils);
  }
  return (0, _createClass2.default)(AccessibilityTestUtils, null, [{
    key: "validateAccessibilityProps",
    value: function validateAccessibilityProps(props) {
      var _props$style, _props$style2;
      var issues = [];
      if (!props.accessibilityRole && props.onPress) {
        issues.push('Interactive element missing accessibilityRole');
      }
      if (props.accessibilityRole && !isValidAccessibilityRole(props.accessibilityRole)) {
        issues.push(`Invalid accessibility role: ${props.accessibilityRole}. Use one of: ${VALID_ACCESSIBILITY_ROLES.join(', ')}`);
      }
      if (!props.accessibilityLabel && !props.children) {
        issues.push('Element missing accessibilityLabel or text content');
      }
      if ((_props$style = props.style) != null && _props$style.width && (_props$style2 = props.style) != null && _props$style2.height) {
        var width = props.style.width;
        var height = props.style.height;
        if (width < WCAG_CONSTANTS.TOUCH_TARGET.MIN_SIZE || height < WCAG_CONSTANTS.TOUCH_TARGET.MIN_SIZE) {
          issues.push(`Touch target too small: ${width}x${height}. Minimum: ${WCAG_CONSTANTS.TOUCH_TARGET.MIN_SIZE}x${WCAG_CONSTANTS.TOUCH_TARGET.MIN_SIZE}`);
        }
      }
      return issues;
    }
  }, {
    key: "generateAccessibilityReport",
    value: function generateAccessibilityReport(componentTree) {
      var _this = this;
      var passed = 0;
      var failed = 0;
      var issues = [];
      componentTree.forEach(function (component, index) {
        var componentIssues = _this.validateAccessibilityProps(component.props);
        if (componentIssues.length === 0) {
          passed++;
        } else {
          failed++;
          issues.push({
            component: component.type || `Component ${index}`,
            issues: componentIssues
          });
        }
      });
      return {
        passed: passed,
        failed: failed,
        issues: issues
      };
    }
  }]);
}();
var AdvancedAccessibilityUtils = exports.AdvancedAccessibilityUtils = exports.AdvancedAccessibilityUtils = {
  announceLiveRegion: function announceLiveRegion(message) {
    var priority = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'polite';
    if (_reactNative.Platform.OS === 'ios') {
      _reactNative.AccessibilityInfo.announceForAccessibility(message);
    } else {
      setTimeout(function () {
        _reactNative.AccessibilityInfo.announceForAccessibility(message);
      }, priority === 'assertive' ? 0 : 100);
    }
  },
  announceContentChange: function announceContentChange(element, changeType, details) {
    var message = `${element} ${changeType}${details ? `: ${details}` : ''}`;
    AdvancedAccessibilityUtils.announceLiveRegion(message, 'polite');
  },
  announceFormValidation: function announceFormValidation(fieldName, isValid, errorMessage) {
    if (isValid) {
      AdvancedAccessibilityUtils.announceLiveRegion(`${fieldName} is valid`, 'polite');
    } else {
      AdvancedAccessibilityUtils.announceLiveRegion(`${fieldName} error: ${errorMessage || 'Invalid input'}`, 'assertive');
    }
  },
  announceProgress: function announceProgress(current, total) {
    var label = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'Progress';
    var percentage = Math.round(current / total * 100);
    AdvancedAccessibilityUtils.announceLiveRegion(`${label}: ${percentage}% complete, ${current} of ${total}`, 'polite');
  },
  announceLoadingState: function announceLoadingState(isLoading) {
    var context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'Content';
    if (isLoading) {
      AdvancedAccessibilityUtils.announceLiveRegion(`${context} loading`, 'polite');
    } else {
      AdvancedAccessibilityUtils.announceLiveRegion(`${context} loaded`, 'polite');
    }
  }
};
var AccessibilityMonitoringUtils = exports.AccessibilityMonitoringUtils = exports.AccessibilityMonitoringUtils = {
  trackAccessibilityUsage: function () {
    var _trackAccessibilityUsage = (0, _asyncToGenerator2.default)(function* () {
      var stats = {
        screenReaderUsage: 0,
        keyboardNavigation: 0,
        voiceControlUsage: 0,
        reducedMotionPreference: false
      };
      try {
        stats.screenReaderUsage = (yield _reactNative.AccessibilityInfo.isScreenReaderEnabled()) ? 1 : 0;
        if (_reactNative.Platform.OS === 'ios') {
          stats.reducedMotionPreference = yield _reactNative.AccessibilityInfo.isReduceMotionEnabled();
        }
      } catch (error) {
        console.warn('Accessibility usage tracking failed:', error);
      }
      return stats;
    });
    function trackAccessibilityUsage() {
      return _trackAccessibilityUsage.apply(this, arguments);
    }
    return trackAccessibilityUsage;
  }(),
  validateCompliance: function () {
    var _validateCompliance = (0, _asyncToGenerator2.default)(function* () {
      var compliance = {
        screenReaderSupport: false,
        keyboardNavigation: true,
        colorContrast: true,
        touchTargets: true,
        textScaling: true,
        reducedMotion: false
      };
      try {
        compliance.screenReaderSupport = yield _reactNative.AccessibilityInfo.isScreenReaderEnabled();
        if (_reactNative.Platform.OS === 'ios') {
          compliance.reducedMotion = yield _reactNative.AccessibilityInfo.isReduceMotionEnabled();
        }
      } catch (error) {
        console.warn('Accessibility compliance check failed:', error);
      }
      return compliance;
    });
    function validateCompliance() {
      return _validateCompliance.apply(this, arguments);
    }
    return validateCompliance;
  }()
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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