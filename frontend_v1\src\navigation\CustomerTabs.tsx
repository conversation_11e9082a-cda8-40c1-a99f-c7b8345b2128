/**
 * Customer Tabs - Customer Role Bottom Tab Navigation
 *
 * Component Contract:
 * - Provides bottom tab navigation for customer users
 * - Includes Home, Search, Bookings, Messages, and Profile tabs
 * - Uses consistent iconography and styling
 * - Integrates with customer-specific features
 * - Follows React Navigation v6+ patterns
 * - Supports type-safe navigation
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import React from 'react';
import { Platform } from 'react-native';

import { NavigationIcon } from '../components/ui/IconLibrary';
import { useTheme } from '../contexts/ThemeContext';

// Import actual screen components (reverting lazy loading for now due to import issues)
import {
  CustomerHomeScreen,
  SearchScreen,
  BookingsScreen,
  MessagesScreen,
  ProfileScreen,
} from '../screens';
import {
  getSafeAreaBottom,
  isIPhoneWithNotch,
  getResponsiveSpacing,
  hasDynamicIsland,
  isAndroidWithGestures,
} from '../utils/responsiveUtils';

import type { CustomerTabParamList } from './types';

const Tab = createBottomTabNavigator<CustomerTabParamList>();

export const CustomerTabs: React.FC = () => {
  const { colors, isDark } = useTheme();

  // Enhanced device-specific calculations
  const safeAreaBottom = getSafeAreaBottom();
  const baseTabBarHeight = 60;

  // Enhanced tab bar height calculation for better device compatibility
  const getTabBarHeight = () => {
    if (Platform.OS === 'ios') {
      if (hasDynamicIsland()) {
        return baseTabBarHeight + Math.max(safeAreaBottom, 34);
      } else if (isIPhoneWithNotch()) {
        return baseTabBarHeight + Math.max(safeAreaBottom, 34);
      } else {
        return baseTabBarHeight + getResponsiveSpacing(8);
      }
    } else {
      // Android
      if (isAndroidWithGestures()) {
        return baseTabBarHeight + Math.max(safeAreaBottom, 16);
      } else {
        return baseTabBarHeight + getResponsiveSpacing(8);
      }
    }
  };

  const tabBarHeight = getTabBarHeight();

  return (
    <Tab.Navigator
      initialRouteName="Home"
      screenOptions={({ route }) => ({
        headerShown: false,
        tabBarActiveTintColor: colors.sage400,
        tabBarInactiveTintColor: colors.text.secondary,
        tabBarStyle: {
          backgroundColor: colors.background.primary,
          borderTopColor: colors.border.light,
          borderTopWidth: Platform.OS === 'ios' ? 0.5 : 1,
          paddingBottom: (() => {
            if (Platform.OS === 'ios') {
              if (hasDynamicIsland() || isIPhoneWithNotch()) {
                return Math.max(safeAreaBottom, 34);
              }
              return getResponsiveSpacing(8);
            } else {
              // Android
              if (isAndroidWithGestures()) {
                return Math.max(safeAreaBottom, 16);
              }
              return getResponsiveSpacing(8);
            }
          })(),
          paddingTop: getResponsiveSpacing(8),
          height: tabBarHeight,
          shadowColor: isDark ? '#000000' : colors.shadow?.light || '#000000',
          shadowOffset: { width: 0, height: Platform.OS === 'ios' ? -0.5 : -2 },
          shadowOpacity: isDark ? 0.5 : 0.1,
          shadowRadius: Platform.OS === 'ios' ? 0 : 4,
          elevation: Platform.OS === 'android' ? 8 : 0,
        },
        tabBarLabelStyle: Platform.select({
          ios: {
            fontSize: 10,
            fontWeight: '500',
            marginTop: 4,
            fontFamily: 'System',
          },
          android: {
            fontSize: 11,
            fontWeight: '600',
            marginTop: 2,
          },
          default: {
            fontSize: 12,
            fontWeight: '500',
          },
        }),
        tabBarIcon: ({ focused, size = 24 }) => {
          let iconName: 'home' | 'search' | 'bookings' | 'messages' | 'profile';

          switch (route.name) {
            case 'Home':
              iconName = 'home';
              break;
            case 'Search':
              iconName = 'search';
              break;
            case 'Bookings':
              iconName = 'bookings';
              break;
            case 'Messages':
              iconName = 'messages';
              break;
            case 'Profile':
              iconName = 'profile';
              break;
            default:
              iconName = 'home';
          }

          return (
            <NavigationIcon
              name={iconName}
              active={focused}
              size={size}
              color={focused ? colors.sage400 : colors.text.secondary}
            />
          );
        },
      })}>
      <Tab.Screen
        name="Home"
        component={CustomerHomeScreen}
        options={{
          title: 'Home',
        }}
      />

      <Tab.Screen
        name="Search"
        component={SearchScreen}
        options={{
          title: 'Search',
        }}
      />

      <Tab.Screen
        name="Bookings"
        component={BookingsScreen}
        options={{
          title: 'Bookings',
        }}
      />

      <Tab.Screen
        name="Messages"
        component={MessagesScreen}
        options={{
          title: 'Messages',
        }}
      />

      <Tab.Screen
        name="Profile"
        component={ProfileScreen}
        options={{
          title: 'Profile',
        }}
      />
    </Tab.Navigator>
  );
};
