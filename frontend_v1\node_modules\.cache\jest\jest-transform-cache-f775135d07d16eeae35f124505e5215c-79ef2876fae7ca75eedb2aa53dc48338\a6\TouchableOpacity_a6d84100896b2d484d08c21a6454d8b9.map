{"version": 3, "names": ["_Animated", "_interopRequireDefault", "require", "_Easing", "_Pressability", "_PressabilityDebug", "_flattenStyle4", "_Platform", "React", "_interopRequireWildcard", "_jsxRuntime", "_excluded", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "_callSuper", "_getPrototypeOf2", "_possibleConstructorReturn2", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "TouchableOpacity", "_React$Component", "_this", "_classCallCheck2", "_len", "arguments", "length", "args", "Array", "_key", "concat", "state", "anim", "Animated", "Value", "_getChildStyleOpacityWithDefault", "pressability", "Pressability", "_createPressabilityConfig", "_inherits2", "_createClass2", "key", "value", "_ref", "_this$props$disabled", "_this$props$accessibi", "_this2", "cancelable", "props", "rejectResponderTermination", "disabled", "accessibilityState", "hitSlop", "delayLongPress", "delayPressIn", "delayPressOut", "minPressDuration", "pressRectOffset", "pressRetentionOffset", "onBlur", "event", "Platform", "isTV", "_opacityInactive", "onFocus", "_opacityActive", "onLongPress", "onPress", "onPressIn", "dispatchConfig", "registrationName", "onPressOut", "_setOpacityTo", "toValue", "duration", "timing", "easing", "Easing", "inOut", "quad", "useNativeDriver", "start", "_this$props$activeOpa", "activeOpacity", "_flattenStyle", "opacity", "flattenStyle", "style", "render", "_this$props$ariaBusy", "_this$props$accessibi2", "_this$props$ariaChec", "_this$props$accessibi3", "_this$props$ariaDisa", "_this$props$accessibi4", "_this$props$ariaExpa", "_this$props$accessibi5", "_this$props$ariaSele", "_this$props$accessibi6", "_this$props$ariaValu", "_this$props$accessibi7", "_this$props$ariaValu2", "_this$props$accessibi8", "_this$props$ariaValu3", "_this$props$accessibi9", "_this$props$ariaValu4", "_this$props$accessibi0", "_this$props$ariaLive", "_this$props$ariaLabe", "_this$props$ariaModa", "_this$props$ariaHidd", "_this$props$id", "_this$state$pressabil", "getEventHandlers", "eventHandlersWithoutBlurAndFocus", "_objectWithoutProperties2", "_accessibilityState", "busy", "checked", "expanded", "selected", "assign", "accessibilityValue", "max", "min", "now", "text", "accessibilityLiveRegion", "accessibilityLabel", "jsxs", "View", "accessible", "accessibilityHint", "accessibilityLanguage", "accessibilityRole", "accessibilityActions", "onAccessibilityAction", "importantForAccessibility", "accessibilityViewIsModal", "accessibilityElementsHidden", "nativeID", "id", "testID", "onLayout", "nextFocusDown", "nextFocusForward", "nextFocusLeft", "nextFocusRight", "nextFocusUp", "hasTVPreferredFocus", "focusable", "undefined", "ref", "hostRef", "children", "__DEV__", "jsx", "PressabilityDebugView", "color", "componentDidUpdate", "prevProps", "prevState", "_flattenStyle2", "_flattenStyle3", "configure", "componentDidMount", "componentWillUnmount", "reset", "resetAnimation", "Component", "Touchable", "forwardRef", "displayName", "_default", "exports"], "sources": ["TouchableOpacity.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\nimport type {ViewStyleProp} from '../../StyleSheet/StyleSheet';\nimport type {TouchableWithoutFeedbackProps} from './TouchableWithoutFeedback';\n\nimport Animated from '../../Animated/Animated';\nimport Easing from '../../Animated/Easing';\nimport Pressability, {\n  type PressabilityConfig,\n} from '../../Pressability/Pressability';\nimport {PressabilityDebugView} from '../../Pressability/PressabilityDebug';\nimport flattenStyle from '../../StyleSheet/flattenStyle';\nimport Platform from '../../Utilities/Platform';\nimport * as React from 'react';\n\nexport type TVProps = $ReadOnly<{\n  /**\n   * *(Apple TV only)* TV preferred focus (see documentation for the View component).\n   *\n   * @platform ios\n   */\n  hasTVPreferredFocus?: ?boolean,\n\n  /**\n   * Designates the next view to receive focus when the user navigates down. See the Android documentation.\n   *\n   * @platform android\n   */\n  nextFocusDown?: ?number,\n\n  /**\n   * Designates the next view to receive focus when the user navigates forward. See the Android documentation.\n   *\n   * @platform android\n   */\n  nextFocusForward?: ?number,\n\n  /**\n   * Designates the next view to receive focus when the user navigates left. See the Android documentation.\n   *\n   * @platform android\n   */\n  nextFocusLeft?: ?number,\n\n  /**\n   * Designates the next view to receive focus when the user navigates right. See the Android documentation.\n   *\n   * @platform android\n   */\n  nextFocusRight?: ?number,\n\n  /**\n   * Designates the next view to receive focus when the user navigates up. See the Android documentation.\n   *\n   * @platform android\n   */\n  nextFocusUp?: ?number,\n}>;\n\ntype TouchableOpacityBaseProps = $ReadOnly<{\n  /**\n   * Determines what the opacity of the wrapped view should be when touch is active.\n   * Defaults to 0.2\n   */\n  activeOpacity?: ?number,\n  style?: ?ViewStyleProp,\n\n  hostRef?: ?React.RefSetter<React.ElementRef<typeof Animated.View>>,\n}>;\n\nexport type TouchableOpacityProps = $ReadOnly<{\n  ...TouchableWithoutFeedbackProps,\n  ...TVProps,\n  ...TouchableOpacityBaseProps,\n}>;\n\ntype State = $ReadOnly<{\n  anim: Animated.Value,\n  pressability: Pressability,\n}>;\n\n/**\n * A wrapper for making views respond properly to touches.\n * On press down, the opacity of the wrapped view is decreased, dimming it.\n *\n * Opacity is controlled by wrapping the children in an Animated.View, which is\n * added to the view hierarchy.  Be aware that this can affect layout.\n *\n * Example:\n *\n * ```\n * renderButton: function() {\n *   return (\n *     <TouchableOpacity onPress={this._onPressButton}>\n *       <Image\n *         style={styles.button}\n *         source={require('./myButton.png')}\n *       />\n *     </TouchableOpacity>\n *   );\n * },\n * ```\n * ### Example\n *\n * ```ReactNativeWebPlayer\n * import React, { Component } from 'react'\n * import {\n *   AppRegistry,\n *   StyleSheet,\n *   TouchableOpacity,\n *   Text,\n *   View,\n * } from 'react-native'\n *\n * class App extends Component {\n *   state = { count: 0 }\n *\n *   onPress = () => {\n *     this.setState(state => ({\n *       count: state.count + 1\n *     }));\n *   };\n *\n *  render() {\n *    return (\n *      <View style={styles.container}>\n *        <TouchableOpacity\n *          style={styles.button}\n *          onPress={this.onPress}>\n *          <Text> Touch Here </Text>\n *        </TouchableOpacity>\n *        <View style={[styles.countContainer]}>\n *          <Text style={[styles.countText]}>\n *             { this.state.count !== 0 ? this.state.count: null}\n *           </Text>\n *         </View>\n *       </View>\n *     )\n *   }\n * }\n *\n * const styles = StyleSheet.create({\n *   container: {\n *     flex: 1,\n *     justifyContent: 'center',\n *     paddingHorizontal: 10\n *   },\n *   button: {\n *     alignItems: 'center',\n *     backgroundColor: '#DDDDDD',\n *     padding: 10\n *   },\n *   countContainer: {\n *     alignItems: 'center',\n *     padding: 10\n *   },\n *   countText: {\n *     color: '#FF00FF'\n *   }\n * })\n *\n * AppRegistry.registerComponent('App', () => App)\n * ```\n *\n */\nclass TouchableOpacity extends React.Component<TouchableOpacityProps, State> {\n  state: State = {\n    anim: new Animated.Value(this._getChildStyleOpacityWithDefault()),\n    pressability: new Pressability(this._createPressabilityConfig()),\n  };\n\n  _createPressabilityConfig(): PressabilityConfig {\n    return {\n      cancelable: !this.props.rejectResponderTermination,\n      disabled:\n        this.props.disabled ??\n        this.props['aria-disabled'] ??\n        this.props.accessibilityState?.disabled,\n      hitSlop: this.props.hitSlop,\n      delayLongPress: this.props.delayLongPress,\n      delayPressIn: this.props.delayPressIn,\n      delayPressOut: this.props.delayPressOut,\n      minPressDuration: 0,\n      pressRectOffset: this.props.pressRetentionOffset,\n      onBlur: event => {\n        if (Platform.isTV) {\n          this._opacityInactive(250);\n        }\n        if (this.props.onBlur != null) {\n          this.props.onBlur(event);\n        }\n      },\n      onFocus: event => {\n        if (Platform.isTV) {\n          this._opacityActive(150);\n        }\n        if (this.props.onFocus != null) {\n          this.props.onFocus(event);\n        }\n      },\n      onLongPress: this.props.onLongPress,\n      onPress: this.props.onPress,\n      onPressIn: event => {\n        this._opacityActive(\n          event.dispatchConfig.registrationName === 'onResponderGrant'\n            ? 0\n            : 150,\n        );\n        if (this.props.onPressIn != null) {\n          this.props.onPressIn(event);\n        }\n      },\n      onPressOut: event => {\n        this._opacityInactive(250);\n        if (this.props.onPressOut != null) {\n          this.props.onPressOut(event);\n        }\n      },\n    };\n  }\n\n  /**\n   * Animate the touchable to a new opacity.\n   */\n  _setOpacityTo(toValue: number, duration: number): void {\n    Animated.timing(this.state.anim, {\n      toValue,\n      duration,\n      easing: Easing.inOut(Easing.quad),\n      useNativeDriver: true,\n    }).start();\n  }\n\n  _opacityActive(duration: number): void {\n    this._setOpacityTo(this.props.activeOpacity ?? 0.2, duration);\n  }\n\n  _opacityInactive(duration: number): void {\n    this._setOpacityTo(this._getChildStyleOpacityWithDefault(), duration);\n  }\n\n  _getChildStyleOpacityWithDefault(): number {\n    // $FlowFixMe[underconstrained-implicit-instantiation]\n    // $FlowFixMe[prop-missing]\n    const opacity = flattenStyle(this.props.style)?.opacity;\n    return typeof opacity === 'number' ? opacity : 1;\n  }\n\n  render(): React.Node {\n    // BACKWARD-COMPATIBILITY: Focus and blur events were never supported before\n    // adopting `Pressability`, so preserve that behavior.\n    const {onBlur, onFocus, ...eventHandlersWithoutBlurAndFocus} =\n      this.state.pressability.getEventHandlers();\n\n    let _accessibilityState = {\n      busy: this.props['aria-busy'] ?? this.props.accessibilityState?.busy,\n      checked:\n        this.props['aria-checked'] ?? this.props.accessibilityState?.checked,\n      disabled:\n        this.props['aria-disabled'] ?? this.props.accessibilityState?.disabled,\n      expanded:\n        this.props['aria-expanded'] ?? this.props.accessibilityState?.expanded,\n      selected:\n        this.props['aria-selected'] ?? this.props.accessibilityState?.selected,\n    };\n\n    _accessibilityState =\n      this.props.disabled != null\n        ? {\n            ..._accessibilityState,\n            disabled: this.props.disabled,\n          }\n        : _accessibilityState;\n\n    const accessibilityValue = {\n      max: this.props['aria-valuemax'] ?? this.props.accessibilityValue?.max,\n      min: this.props['aria-valuemin'] ?? this.props.accessibilityValue?.min,\n      now: this.props['aria-valuenow'] ?? this.props.accessibilityValue?.now,\n      text: this.props['aria-valuetext'] ?? this.props.accessibilityValue?.text,\n    };\n\n    const accessibilityLiveRegion =\n      this.props['aria-live'] === 'off'\n        ? 'none'\n        : this.props['aria-live'] ?? this.props.accessibilityLiveRegion;\n\n    const accessibilityLabel =\n      this.props['aria-label'] ?? this.props.accessibilityLabel;\n    return (\n      <Animated.View\n        accessible={this.props.accessible !== false}\n        accessibilityLabel={accessibilityLabel}\n        accessibilityHint={this.props.accessibilityHint}\n        accessibilityLanguage={this.props.accessibilityLanguage}\n        accessibilityRole={this.props.accessibilityRole}\n        accessibilityState={_accessibilityState}\n        accessibilityActions={this.props.accessibilityActions}\n        onAccessibilityAction={this.props.onAccessibilityAction}\n        accessibilityValue={accessibilityValue}\n        importantForAccessibility={\n          this.props['aria-hidden'] === true\n            ? 'no-hide-descendants'\n            : this.props.importantForAccessibility\n        }\n        accessibilityViewIsModal={\n          this.props['aria-modal'] ?? this.props.accessibilityViewIsModal\n        }\n        accessibilityLiveRegion={accessibilityLiveRegion}\n        accessibilityElementsHidden={\n          this.props['aria-hidden'] ?? this.props.accessibilityElementsHidden\n        }\n        style={[this.props.style, {opacity: this.state.anim}]}\n        nativeID={this.props.id ?? this.props.nativeID}\n        testID={this.props.testID}\n        onLayout={this.props.onLayout}\n        nextFocusDown={this.props.nextFocusDown}\n        nextFocusForward={this.props.nextFocusForward}\n        nextFocusLeft={this.props.nextFocusLeft}\n        nextFocusRight={this.props.nextFocusRight}\n        nextFocusUp={this.props.nextFocusUp}\n        hasTVPreferredFocus={this.props.hasTVPreferredFocus}\n        hitSlop={this.props.hitSlop}\n        focusable={\n          this.props.focusable !== false &&\n          this.props.onPress !== undefined &&\n          !this.props.disabled\n        }\n        // $FlowFixMe[prop-missing]\n        ref={this.props.hostRef}\n        {...eventHandlersWithoutBlurAndFocus}>\n        {this.props.children}\n        {__DEV__ ? (\n          <PressabilityDebugView color=\"cyan\" hitSlop={this.props.hitSlop} />\n        ) : null}\n      </Animated.View>\n    );\n  }\n\n  componentDidUpdate(prevProps: TouchableOpacityProps, prevState: State) {\n    this.state.pressability.configure(this._createPressabilityConfig());\n    if (\n      this.props.disabled !== prevProps.disabled ||\n      // $FlowFixMe[underconstrained-implicit-instantiation]\n      // $FlowFixMe[prop-missing]\n      flattenStyle(prevProps.style)?.opacity !==\n        // $FlowFixMe[underconstrained-implicit-instantiation]\n        // $FlowFixMe[prop-missing]\n        flattenStyle(this.props.style)?.opacity\n    ) {\n      this._opacityInactive(250);\n    }\n  }\n\n  componentDidMount(): void {\n    this.state.pressability.configure(this._createPressabilityConfig());\n  }\n\n  componentWillUnmount(): void {\n    this.state.pressability.reset();\n    this.state.anim.resetAnimation();\n  }\n}\n\nconst Touchable: component(\n  ref?: React.RefSetter<React.ElementRef<typeof Animated.View>>,\n  ...props: TouchableOpacityProps\n) = React.forwardRef((props, ref) => (\n  <TouchableOpacity {...props} hostRef={ref} />\n));\n\nTouchable.displayName = 'TouchableOpacity';\n\nexport default Touchable;\n"], "mappings": ";;;;;;;;;;;AAaA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,aAAA,GAAAH,sBAAA,CAAAC,OAAA;AAGA,IAAAG,kBAAA,GAAAH,OAAA;AACA,IAAAI,cAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,SAAA,GAAAN,sBAAA,CAAAC,OAAA;AACA,IAAAM,KAAA,GAAAC,uBAAA,CAAAP,OAAA;AAA+B,IAAAQ,WAAA,GAAAR,OAAA;AAAA,IAAAS,SAAA;AAAA,SAAAF,wBAAAG,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAL,uBAAA,YAAAA,wBAAAG,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAAA,SAAAmB,WAAAnB,CAAA,EAAAK,CAAA,EAAAN,CAAA,WAAAM,CAAA,OAAAe,gBAAA,CAAAX,OAAA,EAAAJ,CAAA,OAAAgB,2BAAA,CAAAZ,OAAA,EAAAT,CAAA,EAAAsB,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAnB,CAAA,EAAAN,CAAA,YAAAqB,gBAAA,CAAAX,OAAA,EAAAT,CAAA,EAAAyB,WAAA,IAAApB,CAAA,CAAAqB,KAAA,CAAA1B,CAAA,EAAAD,CAAA;AAAA,SAAAuB,0BAAA,cAAAtB,CAAA,IAAA2B,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAd,IAAA,CAAAQ,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAA3B,CAAA,aAAAsB,yBAAA,YAAAA,0BAAA,aAAAtB,CAAA;AAAA,IAwJzB8B,gBAAgB,aAAAC,gBAAA;EAAA,SAAAD,iBAAA;IAAA,IAAAE,KAAA;IAAA,IAAAC,gBAAA,CAAAxB,OAAA,QAAAqB,gBAAA;IAAA,SAAAI,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAAF,IAAA,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IAAAP,KAAA,GAAAb,UAAA,OAAAW,gBAAA,KAAAU,MAAA,CAAAH,IAAA;IAAAL,KAAA,CACpBS,KAAK,GAAU;MACbC,IAAI,EAAE,IAAIC,iBAAQ,CAACC,KAAK,CAACZ,KAAA,CAAKa,gCAAgC,CAAC,CAAC,CAAC;MACjEC,YAAY,EAAE,IAAIC,qBAAY,CAACf,KAAA,CAAKgB,yBAAyB,CAAC,CAAC;IACjE,CAAC;IAAA,OAAAhB,KAAA;EAAA;EAAA,IAAAiB,UAAA,CAAAxC,OAAA,EAAAqB,gBAAA,EAAAC,gBAAA;EAAA,WAAAmB,aAAA,CAAAzC,OAAA,EAAAqB,gBAAA;IAAAqB,GAAA;IAAAC,KAAA,EAED,SAAAJ,yBAAyBA,CAAA,EAAuB;MAAA,IAAAK,IAAA;QAAAC,oBAAA;QAAAC,qBAAA;QAAAC,MAAA;MAC9C,OAAO;QACLC,UAAU,EAAE,CAAC,IAAI,CAACC,KAAK,CAACC,0BAA0B;QAClDC,QAAQ,GAAAP,IAAA,IAAAC,oBAAA,GACN,IAAI,CAACI,KAAK,CAACE,QAAQ,YAAAN,oBAAA,GACnB,IAAI,CAACI,KAAK,CAAC,eAAe,CAAC,YAAAL,IAAA,IAAAE,qBAAA,GAC3B,IAAI,CAACG,KAAK,CAACG,kBAAkB,qBAA7BN,qBAAA,CAA+BK,QAAQ;QACzCE,OAAO,EAAE,IAAI,CAACJ,KAAK,CAACI,OAAO;QAC3BC,cAAc,EAAE,IAAI,CAACL,KAAK,CAACK,cAAc;QACzCC,YAAY,EAAE,IAAI,CAACN,KAAK,CAACM,YAAY;QACrCC,aAAa,EAAE,IAAI,CAACP,KAAK,CAACO,aAAa;QACvCC,gBAAgB,EAAE,CAAC;QACnBC,eAAe,EAAE,IAAI,CAACT,KAAK,CAACU,oBAAoB;QAChDC,MAAM,EAAE,SAARA,MAAMA,CAAEC,KAAK,EAAI;UACf,IAAIC,iBAAQ,CAACC,IAAI,EAAE;YACjBhB,MAAI,CAACiB,gBAAgB,CAAC,GAAG,CAAC;UAC5B;UACA,IAAIjB,MAAI,CAACE,KAAK,CAACW,MAAM,IAAI,IAAI,EAAE;YAC7Bb,MAAI,CAACE,KAAK,CAACW,MAAM,CAACC,KAAK,CAAC;UAC1B;QACF,CAAC;QACDI,OAAO,EAAE,SAATA,OAAOA,CAAEJ,KAAK,EAAI;UAChB,IAAIC,iBAAQ,CAACC,IAAI,EAAE;YACjBhB,MAAI,CAACmB,cAAc,CAAC,GAAG,CAAC;UAC1B;UACA,IAAInB,MAAI,CAACE,KAAK,CAACgB,OAAO,IAAI,IAAI,EAAE;YAC9BlB,MAAI,CAACE,KAAK,CAACgB,OAAO,CAACJ,KAAK,CAAC;UAC3B;QACF,CAAC;QACDM,WAAW,EAAE,IAAI,CAAClB,KAAK,CAACkB,WAAW;QACnCC,OAAO,EAAE,IAAI,CAACnB,KAAK,CAACmB,OAAO;QAC3BC,SAAS,EAAE,SAAXA,SAASA,CAAER,KAAK,EAAI;UAClBd,MAAI,CAACmB,cAAc,CACjBL,KAAK,CAACS,cAAc,CAACC,gBAAgB,KAAK,kBAAkB,GACxD,CAAC,GACD,GACN,CAAC;UACD,IAAIxB,MAAI,CAACE,KAAK,CAACoB,SAAS,IAAI,IAAI,EAAE;YAChCtB,MAAI,CAACE,KAAK,CAACoB,SAAS,CAACR,KAAK,CAAC;UAC7B;QACF,CAAC;QACDW,UAAU,EAAE,SAAZA,UAAUA,CAAEX,KAAK,EAAI;UACnBd,MAAI,CAACiB,gBAAgB,CAAC,GAAG,CAAC;UAC1B,IAAIjB,MAAI,CAACE,KAAK,CAACuB,UAAU,IAAI,IAAI,EAAE;YACjCzB,MAAI,CAACE,KAAK,CAACuB,UAAU,CAACX,KAAK,CAAC;UAC9B;QACF;MACF,CAAC;IACH;EAAC;IAAAnB,GAAA;IAAAC,KAAA,EAKD,SAAA8B,aAAaA,CAACC,OAAe,EAAEC,QAAgB,EAAQ;MACrDzC,iBAAQ,CAAC0C,MAAM,CAAC,IAAI,CAAC5C,KAAK,CAACC,IAAI,EAAE;QAC/ByC,OAAO,EAAPA,OAAO;QACPC,QAAQ,EAARA,QAAQ;QACRE,MAAM,EAAEC,eAAM,CAACC,KAAK,CAACD,eAAM,CAACE,IAAI,CAAC;QACjCC,eAAe,EAAE;MACnB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;IACZ;EAAC;IAAAxC,GAAA;IAAAC,KAAA,EAED,SAAAuB,cAAcA,CAACS,QAAgB,EAAQ;MAAA,IAAAQ,qBAAA;MACrC,IAAI,CAACV,aAAa,EAAAU,qBAAA,GAAC,IAAI,CAAClC,KAAK,CAACmC,aAAa,YAAAD,qBAAA,GAAI,GAAG,EAAER,QAAQ,CAAC;IAC/D;EAAC;IAAAjC,GAAA;IAAAC,KAAA,EAED,SAAAqB,gBAAgBA,CAACW,QAAgB,EAAQ;MACvC,IAAI,CAACF,aAAa,CAAC,IAAI,CAACrC,gCAAgC,CAAC,CAAC,EAAEuC,QAAQ,CAAC;IACvE;EAAC;IAAAjC,GAAA;IAAAC,KAAA,EAED,SAAAP,gCAAgCA,CAAA,EAAW;MAAA,IAAAiD,aAAA;MAGzC,IAAMC,OAAO,IAAAD,aAAA,GAAG,IAAAE,sBAAY,EAAC,IAAI,CAACtC,KAAK,CAACuC,KAAK,CAAC,qBAA9BH,aAAA,CAAgCC,OAAO;MACvD,OAAO,OAAOA,OAAO,KAAK,QAAQ,GAAGA,OAAO,GAAG,CAAC;IAClD;EAAC;IAAA5C,GAAA;IAAAC,KAAA,EAED,SAAA8C,MAAMA,CAAA,EAAe;MAAA,IAAAC,oBAAA,EAAAC,sBAAA,EAAAC,oBAAA,EAAAC,sBAAA,EAAAC,oBAAA,EAAAC,sBAAA,EAAAC,oBAAA,EAAAC,sBAAA,EAAAC,oBAAA,EAAAC,sBAAA,EAAAC,oBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,cAAA;MAGnB,IAAAC,qBAAA,GACE,IAAI,CAACjF,KAAK,CAACK,YAAY,CAAC6E,gBAAgB,CAAC,CAAC;QADrCtD,MAAM,GAAAqD,qBAAA,CAANrD,MAAM;QAAEK,OAAO,GAAAgD,qBAAA,CAAPhD,OAAO;QAAKkD,gCAAgC,OAAAC,yBAAA,CAAApH,OAAA,EAAAiH,qBAAA,EAAA5H,SAAA;MAG3D,IAAIgI,mBAAmB,GAAG;QACxBC,IAAI,GAAA5B,oBAAA,GAAE,IAAI,CAACzC,KAAK,CAAC,WAAW,CAAC,YAAAyC,oBAAA,IAAAC,sBAAA,GAAI,IAAI,CAAC1C,KAAK,CAACG,kBAAkB,qBAA7BuC,sBAAA,CAA+B2B,IAAI;QACpEC,OAAO,GAAA3B,oBAAA,GACL,IAAI,CAAC3C,KAAK,CAAC,cAAc,CAAC,YAAA2C,oBAAA,IAAAC,sBAAA,GAAI,IAAI,CAAC5C,KAAK,CAACG,kBAAkB,qBAA7ByC,sBAAA,CAA+B0B,OAAO;QACtEpE,QAAQ,GAAA2C,oBAAA,GACN,IAAI,CAAC7C,KAAK,CAAC,eAAe,CAAC,YAAA6C,oBAAA,IAAAC,sBAAA,GAAI,IAAI,CAAC9C,KAAK,CAACG,kBAAkB,qBAA7B2C,sBAAA,CAA+B5C,QAAQ;QACxEqE,QAAQ,GAAAxB,oBAAA,GACN,IAAI,CAAC/C,KAAK,CAAC,eAAe,CAAC,YAAA+C,oBAAA,IAAAC,sBAAA,GAAI,IAAI,CAAChD,KAAK,CAACG,kBAAkB,qBAA7B6C,sBAAA,CAA+BuB,QAAQ;QACxEC,QAAQ,GAAAvB,oBAAA,GACN,IAAI,CAACjD,KAAK,CAAC,eAAe,CAAC,YAAAiD,oBAAA,IAAAC,sBAAA,GAAI,IAAI,CAAClD,KAAK,CAACG,kBAAkB,qBAA7B+C,sBAAA,CAA+BsB;MAClE,CAAC;MAEDJ,mBAAmB,GACjB,IAAI,CAACpE,KAAK,CAACE,QAAQ,IAAI,IAAI,GAAA5C,MAAA,CAAAmH,MAAA,KAElBL,mBAAmB;QACtBlE,QAAQ,EAAE,IAAI,CAACF,KAAK,CAACE;MAAQ,KAE/BkE,mBAAmB;MAEzB,IAAMM,kBAAkB,GAAG;QACzBC,GAAG,GAAAxB,oBAAA,GAAE,IAAI,CAACnD,KAAK,CAAC,eAAe,CAAC,YAAAmD,oBAAA,IAAAC,sBAAA,GAAI,IAAI,CAACpD,KAAK,CAAC0E,kBAAkB,qBAA7BtB,sBAAA,CAA+BuB,GAAG;QACtEC,GAAG,GAAAvB,qBAAA,GAAE,IAAI,CAACrD,KAAK,CAAC,eAAe,CAAC,YAAAqD,qBAAA,IAAAC,sBAAA,GAAI,IAAI,CAACtD,KAAK,CAAC0E,kBAAkB,qBAA7BpB,sBAAA,CAA+BsB,GAAG;QACtEC,GAAG,GAAAtB,qBAAA,GAAE,IAAI,CAACvD,KAAK,CAAC,eAAe,CAAC,YAAAuD,qBAAA,IAAAC,sBAAA,GAAI,IAAI,CAACxD,KAAK,CAAC0E,kBAAkB,qBAA7BlB,sBAAA,CAA+BqB,GAAG;QACtEC,IAAI,GAAArB,qBAAA,GAAE,IAAI,CAACzD,KAAK,CAAC,gBAAgB,CAAC,YAAAyD,qBAAA,IAAAC,sBAAA,GAAI,IAAI,CAAC1D,KAAK,CAAC0E,kBAAkB,qBAA7BhB,sBAAA,CAA+BoB;MACvE,CAAC;MAED,IAAMC,uBAAuB,GAC3B,IAAI,CAAC/E,KAAK,CAAC,WAAW,CAAC,KAAK,KAAK,GAC7B,MAAM,IAAA2D,oBAAA,GACN,IAAI,CAAC3D,KAAK,CAAC,WAAW,CAAC,YAAA2D,oBAAA,GAAI,IAAI,CAAC3D,KAAK,CAAC+E,uBAAuB;MAEnE,IAAMC,kBAAkB,IAAApB,oBAAA,GACtB,IAAI,CAAC5D,KAAK,CAAC,YAAY,CAAC,YAAA4D,oBAAA,GAAI,IAAI,CAAC5D,KAAK,CAACgF,kBAAkB;MAC3D,OACE,IAAA7I,WAAA,CAAA8I,IAAA,EAACxJ,SAAA,CAAAsB,OAAQ,CAACmI,IAAI,EAAA5H,MAAA,CAAAmH,MAAA;QACZU,UAAU,EAAE,IAAI,CAACnF,KAAK,CAACmF,UAAU,KAAK,KAAM;QAC5CH,kBAAkB,EAAEA,kBAAmB;QACvCI,iBAAiB,EAAE,IAAI,CAACpF,KAAK,CAACoF,iBAAkB;QAChDC,qBAAqB,EAAE,IAAI,CAACrF,KAAK,CAACqF,qBAAsB;QACxDC,iBAAiB,EAAE,IAAI,CAACtF,KAAK,CAACsF,iBAAkB;QAChDnF,kBAAkB,EAAEiE,mBAAoB;QACxCmB,oBAAoB,EAAE,IAAI,CAACvF,KAAK,CAACuF,oBAAqB;QACtDC,qBAAqB,EAAE,IAAI,CAACxF,KAAK,CAACwF,qBAAsB;QACxDd,kBAAkB,EAAEA,kBAAmB;QACvCe,yBAAyB,EACvB,IAAI,CAACzF,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,GAC9B,qBAAqB,GACrB,IAAI,CAACA,KAAK,CAACyF,yBAChB;QACDC,wBAAwB,GAAA7B,oBAAA,GACtB,IAAI,CAAC7D,KAAK,CAAC,YAAY,CAAC,YAAA6D,oBAAA,GAAI,IAAI,CAAC7D,KAAK,CAAC0F,wBACxC;QACDX,uBAAuB,EAAEA,uBAAwB;QACjDY,2BAA2B,GAAA7B,oBAAA,GACzB,IAAI,CAAC9D,KAAK,CAAC,aAAa,CAAC,YAAA8D,oBAAA,GAAI,IAAI,CAAC9D,KAAK,CAAC2F,2BACzC;QACDpD,KAAK,EAAE,CAAC,IAAI,CAACvC,KAAK,CAACuC,KAAK,EAAE;UAACF,OAAO,EAAE,IAAI,CAACtD,KAAK,CAACC;QAAI,CAAC,CAAE;QACtD4G,QAAQ,GAAA7B,cAAA,GAAE,IAAI,CAAC/D,KAAK,CAAC6F,EAAE,YAAA9B,cAAA,GAAI,IAAI,CAAC/D,KAAK,CAAC4F,QAAS;QAC/CE,MAAM,EAAE,IAAI,CAAC9F,KAAK,CAAC8F,MAAO;QAC1BC,QAAQ,EAAE,IAAI,CAAC/F,KAAK,CAAC+F,QAAS;QAC9BC,aAAa,EAAE,IAAI,CAAChG,KAAK,CAACgG,aAAc;QACxCC,gBAAgB,EAAE,IAAI,CAACjG,KAAK,CAACiG,gBAAiB;QAC9CC,aAAa,EAAE,IAAI,CAAClG,KAAK,CAACkG,aAAc;QACxCC,cAAc,EAAE,IAAI,CAACnG,KAAK,CAACmG,cAAe;QAC1CC,WAAW,EAAE,IAAI,CAACpG,KAAK,CAACoG,WAAY;QACpCC,mBAAmB,EAAE,IAAI,CAACrG,KAAK,CAACqG,mBAAoB;QACpDjG,OAAO,EAAE,IAAI,CAACJ,KAAK,CAACI,OAAQ;QAC5BkG,SAAS,EACP,IAAI,CAACtG,KAAK,CAACsG,SAAS,KAAK,KAAK,IAC9B,IAAI,CAACtG,KAAK,CAACmB,OAAO,KAAKoF,SAAS,IAChC,CAAC,IAAI,CAACvG,KAAK,CAACE,QACb;QAEDsG,GAAG,EAAE,IAAI,CAACxG,KAAK,CAACyG;MAAQ,GACpBvC,gCAAgC;QAAAwC,QAAA,GACnC,IAAI,CAAC1G,KAAK,CAAC0G,QAAQ,EACnBC,OAAO,GACN,IAAAxK,WAAA,CAAAyK,GAAA,EAAC9K,kBAAA,CAAA+K,qBAAqB;UAACC,KAAK,EAAC,MAAM;UAAC1G,OAAO,EAAE,IAAI,CAACJ,KAAK,CAACI;QAAQ,CAAE,CAAC,GACjE,IAAI;MAAA,EACK,CAAC;IAEpB;EAAC;IAAAX,GAAA;IAAAC,KAAA,EAED,SAAAqH,kBAAkBA,CAACC,SAAgC,EAAEC,SAAgB,EAAE;MAAA,IAAAC,cAAA,EAAAC,cAAA;MACrE,IAAI,CAACpI,KAAK,CAACK,YAAY,CAACgI,SAAS,CAAC,IAAI,CAAC9H,yBAAyB,CAAC,CAAC,CAAC;MACnE,IACE,IAAI,CAACU,KAAK,CAACE,QAAQ,KAAK8G,SAAS,CAAC9G,QAAQ,IAG1C,EAAAgH,cAAA,OAAA5E,sBAAY,EAAC0E,SAAS,CAACzE,KAAK,CAAC,qBAA7B2E,cAAA,CAA+B7E,OAAO,QAAA8E,cAAA,GAGpC,IAAA7E,sBAAY,EAAC,IAAI,CAACtC,KAAK,CAACuC,KAAK,CAAC,qBAA9B4E,cAAA,CAAgC9E,OAAO,GACzC;QACA,IAAI,CAACtB,gBAAgB,CAAC,GAAG,CAAC;MAC5B;IACF;EAAC;IAAAtB,GAAA;IAAAC,KAAA,EAED,SAAA2H,iBAAiBA,CAAA,EAAS;MACxB,IAAI,CAACtI,KAAK,CAACK,YAAY,CAACgI,SAAS,CAAC,IAAI,CAAC9H,yBAAyB,CAAC,CAAC,CAAC;IACrE;EAAC;IAAAG,GAAA;IAAAC,KAAA,EAED,SAAA4H,oBAAoBA,CAAA,EAAS;MAC3B,IAAI,CAACvI,KAAK,CAACK,YAAY,CAACmI,KAAK,CAAC,CAAC;MAC/B,IAAI,CAACxI,KAAK,CAACC,IAAI,CAACwI,cAAc,CAAC,CAAC;IAClC;EAAC;AAAA,EAnM4BvL,KAAK,CAACwL,SAAS;AAsM9C,IAAMC,SAGL,GAAGzL,KAAK,CAAC0L,UAAU,CAAC,UAAC3H,KAAK,EAAEwG,GAAG;EAAA,OAC9B,IAAArK,WAAA,CAAAyK,GAAA,EAACxI,gBAAgB,EAAAd,MAAA,CAAAmH,MAAA,KAAKzE,KAAK;IAAEyG,OAAO,EAAED;EAAI,EAAE,CAAC;AAAA,CAC9C,CAAC;AAEFkB,SAAS,CAACE,WAAW,GAAG,kBAAkB;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAA/K,OAAA,GAE5B2K,SAAS", "ignoreList": []}