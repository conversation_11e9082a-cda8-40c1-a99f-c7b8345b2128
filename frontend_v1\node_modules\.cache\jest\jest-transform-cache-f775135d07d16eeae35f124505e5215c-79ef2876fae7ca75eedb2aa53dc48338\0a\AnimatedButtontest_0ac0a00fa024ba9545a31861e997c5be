707d0985ac0eccb48b69b9780f326bbd
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _objectWithoutProperties2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));
var _react = _interopRequireDefault(require("react"));
var _reactNative = require("react-native");
var _reactNative2 = require("@testing-library/react-native");
var _jsxRuntime = require("react/jsx-runtime");
var _excluded = ["title", "onPress", "testID", "disabled"];
var MockAnimatedButton = function MockAnimatedButton(_ref) {
  var title = _ref.title,
    onPress = _ref.onPress,
    testID = _ref.testID,
    disabled = _ref.disabled,
    props = (0, _objectWithoutProperties2.default)(_ref, _excluded);
  return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, Object.assign({
    testID: testID,
    onPress: disabled ? undefined : onPress,
    accessibilityRole: "button"
  }, props, {
    children: (0, _jsxRuntime.jsx)(_reactNative.Text, {
      children: title
    })
  }));
};
describe('AnimatedButton', function () {
  var defaultProps = {
    title: 'Test Button',
    onPress: jest.fn(),
    testID: 'animated-button'
  };
  beforeEach(function () {
    jest.clearAllMocks();
  });
  describe('Rendering', function () {
    it('renders correctly with default props', function () {
      var _render = (0, _reactNative2.render)((0, _jsxRuntime.jsx)(MockAnimatedButton, Object.assign({}, defaultProps))),
        getByTestId = _render.getByTestId,
        getByText = _render.getByText;
      expect(getByTestId('animated-button')).toBeTruthy();
      expect(getByText('Test Button')).toBeTruthy();
    });
    it('renders with different variants', function () {
      var _render2 = (0, _reactNative2.render)((0, _jsxRuntime.jsx)(MockAnimatedButton, Object.assign({}, defaultProps, {
          variant: "primary",
          testID: "button-primary"
        }))),
        getByTestId = _render2.getByTestId;
      expect(getByTestId('button-primary')).toBeTruthy();
    });
  });
  describe('Functionality', function () {
    it('calls onPress when pressed', function () {
      var onPressMock = jest.fn();
      var _render3 = (0, _reactNative2.render)((0, _jsxRuntime.jsx)(MockAnimatedButton, Object.assign({}, defaultProps, {
          onPress: onPressMock
        }))),
        getByTestId = _render3.getByTestId;
      _reactNative2.fireEvent.press(getByTestId('animated-button'));
      expect(onPressMock).toHaveBeenCalledTimes(1);
    });
    it('does not call onPress when disabled', function () {
      var onPressMock = jest.fn();
      var _render4 = (0, _reactNative2.render)((0, _jsxRuntime.jsx)(MockAnimatedButton, Object.assign({}, defaultProps, {
          onPress: onPressMock,
          disabled: true
        }))),
        getByTestId = _render4.getByTestId;
      _reactNative2.fireEvent.press(getByTestId('animated-button'));
      expect(onPressMock).not.toHaveBeenCalled();
    });
  });
  describe('Accessibility', function () {
    it('has proper accessibility properties', function () {
      var _render5 = (0, _reactNative2.render)((0, _jsxRuntime.jsx)(MockAnimatedButton, Object.assign({}, defaultProps, {
          accessibilityLabel: "Custom accessibility label"
        }))),
        getByTestId = _render5.getByTestId;
      var button = getByTestId('animated-button');
      expect(button.props.accessibilityRole).toBe('button');
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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