9bc5df09683e3395ba65fe3903d1cf77
var _formValidation = require("../formValidation");
describe('Enhanced Form Validation System', function () {
  describe('FormValidator Class', function () {
    var validator;
    beforeEach(function () {
      validator = new _formValidation.FormValidator({
        name: _formValidation.CommonValidations.name,
        email: _formValidation.CommonValidations.email,
        phone: _formValidation.CommonValidations.phone,
        password: _formValidation.CommonValidations.password,
        confirmPassword: _formValidation.CommonValidations.confirmPassword('password')
      });
    });
    it('should validate individual fields correctly', function () {
      var result = validator.validateField('name', '<PERSON>', {});
      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
    });
    it('should detect invalid fields', function () {
      var result = validator.validateField('email', 'invalid-email', {});
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Please enter your email in <NAME_EMAIL>');
    });
    it('should validate entire form', function () {
      var formData = {
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        password: 'SecurePass123!',
        confirmPassword: 'SecurePass123!'
      };
      var result = validator.validateForm(formData);
      expect(result.isValid).toBe(true);
      expect(Object.keys(result.errors)).toHaveLength(0);
    });
    it('should detect form validation errors', function () {
      var formData = {
        name: '',
        email: 'invalid-email',
        phone: '123',
        password: 'weak',
        confirmPassword: 'different'
      };
      var result = validator.validateForm(formData);
      expect(result.isValid).toBe(false);
      expect(Object.keys(result.errors).length).toBeGreaterThan(0);
    });
  });
  describe('Service Form Validation', function () {
    var serviceValidator;
    beforeEach(function () {
      serviceValidator = new _formValidation.FormValidator({
        name: {
          required: true,
          minLength: 3,
          maxLength: 100
        },
        description: {
          required: true,
          minLength: 10,
          maxLength: 500
        },
        price: {
          required: true,
          number: true,
          custom: function custom(value) {
            var numValue = Number(value);
            if (isNaN(numValue) || numValue <= 0) {
              return {
                isValid: false,
                error: 'Price must be greater than 0'
              };
            }
            return {
              isValid: true
            };
          }
        },
        duration: {
          required: true,
          number: true,
          custom: function custom(value) {
            var numValue = Number(value);
            if (isNaN(numValue) || numValue <= 0) {
              return {
                isValid: false,
                error: 'Duration must be greater than 0'
              };
            }
            return {
              isValid: true
            };
          }
        }
      });
    });
    it('should validate service name correctly', function () {
      var validResult = serviceValidator.validateField('name', 'Hair Cut & Style', {});
      expect(validResult.isValid).toBe(true);
      var invalidResult = serviceValidator.validateField('name', 'Hi', {});
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.error).toContain('at least 3 characters');
    });
    it('should validate service description', function () {
      var validResult = serviceValidator.validateField('description', 'Professional hair cutting and styling service', {});
      expect(validResult.isValid).toBe(true);
      var invalidResult = serviceValidator.validateField('description', 'Short', {});
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.error).toContain('at least 10 characters');
    });
    it('should validate price correctly', function () {
      var validResult = serviceValidator.validateField('price', '50.00', {});
      expect(validResult.isValid).toBe(true);
      var invalidResult = serviceValidator.validateField('price', '-10', {});
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.error).toContain('greater than 0');
    });
    it('should validate duration correctly', function () {
      var validResult = serviceValidator.validateField('duration', '60', {});
      expect(validResult.isValid).toBe(true);
      var invalidResult = serviceValidator.validateField('duration', '0', {});
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.error).toContain('greater than 0');
    });
  });
  describe('Profile Form Validation', function () {
    var profileValidator;
    beforeEach(function () {
      profileValidator = new _formValidation.FormValidator({
        firstName: _formValidation.CommonValidations.name,
        lastName: _formValidation.CommonValidations.name,
        email: _formValidation.CommonValidations.email,
        phone: {
          required: false,
          phone: true
        },
        dateOfBirth: {
          required: false,
          date: true,
          custom: function custom(value) {
            if (!value) return {
              isValid: true
            };
            var dateRegex = /^\d{4}-\d{2}-\d{2}$/;
            if (!dateRegex.test(value)) {
              return {
                isValid: false,
                error: 'Date must be in YYYY-MM-DD format'
              };
            }
            var date = new Date(value);
            var today = new Date();
            if (date > today) {
              return {
                isValid: false,
                error: 'Date of birth cannot be in the future'
              };
            }
            return {
              isValid: true
            };
          }
        }
      });
    });
    it('should validate profile data correctly', function () {
      var formData = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        dateOfBirth: '1990-01-01'
      };
      var result = profileValidator.validateForm(formData);
      expect(result.isValid).toBe(true);
    });
    it('should handle optional fields correctly', function () {
      var formData = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '',
        dateOfBirth: ''
      };
      var result = profileValidator.validateForm(formData);
      expect(result.isValid).toBe(true);
    });
    it('should validate date of birth format', function () {
      var invalidResult = profileValidator.validateField('dateOfBirth', '01/01/1990', {});
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.error).toContain('YYYY-MM-DD format');
      var futureResult = profileValidator.validateField('dateOfBirth', '2030-01-01', {});
      expect(futureResult.isValid).toBe(false);
      expect(futureResult.error).toContain('cannot be in the future');
    });
  });
  describe('Real-time Validation Scenarios', function () {
    it('should provide immediate feedback for email validation', function () {
      var emailSteps = ['j', 'jo', 'john', 'john@', 'john@ex', '<EMAIL>'];
      var results = emailSteps.map(function (step) {
        return (0, _formValidation.validateField)(step, _formValidation.CommonValidations.email, {});
      });
      expect(results[results.length - 1].isValid).toBe(true);
      results.slice(0, -1).forEach(function (result) {
        expect(result.isValid).toBe(false);
      });
    });
    it('should handle password confirmation validation', function () {
      var formData = {
        password: 'SecurePass123!'
      };
      var matchingResult = (0, _formValidation.validateField)('SecurePass123!', _formValidation.CommonValidations.confirmPassword('password'), formData);
      expect(matchingResult.isValid).toBe(true);
      var nonMatchingResult = (0, _formValidation.validateField)('DifferentPass123!', _formValidation.CommonValidations.confirmPassword('password'), formData);
      expect(nonMatchingResult.isValid).toBe(false);
      expect(nonMatchingResult.error).toContain('do not match');
    });
  });
  describe('Error Message Quality', function () {
    it('should provide clear, actionable error messages', function () {
      var nameResult = (0, _formValidation.validateField)('', _formValidation.CommonValidations.name, {});
      expect(nameResult.error).toBe('Please fill in this field to continue');
      var emailResult = (0, _formValidation.validateField)('invalid', _formValidation.CommonValidations.email, {});
      expect(emailResult.error).toBe('Please enter your email in <NAME_EMAIL>');
      var phoneResult = (0, _formValidation.validateField)('123', _formValidation.CommonValidations.phone, {});
      expect(phoneResult.error).toBe('Please enter your phone number with area code (e.g., ************)');
    });
    it('should provide specific validation feedback', function () {
      var shortNameResult = (0, _formValidation.validateField)('A', _formValidation.CommonValidations.name, {});
      expect(shortNameResult.error).toContain('2-50 characters');
      var weakPasswordResult = (0, _formValidation.validateField)('123', _formValidation.CommonValidations.password, {});
      expect(weakPasswordResult.error).toContain('at least 8 characters');
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfZm9ybVZhbGlkYXRpb24iLCJyZXF1aXJlIiwiZGVzY3JpYmUiLCJ2YWxpZGF0b3IiLCJiZWZvcmVFYWNoIiwiRm9ybVZhbGlkYXRvciIsIm5hbWUiLCJDb21tb25WYWxpZGF0aW9ucyIsImVtYWlsIiwicGhvbmUiLCJwYXNzd29yZCIsImNvbmZpcm1QYXNzd29yZCIsIml0IiwicmVzdWx0IiwidmFsaWRhdGVGaWVsZCIsImV4cGVjdCIsImlzVmFsaWQiLCJ0b0JlIiwiZXJyb3IiLCJ0b0JlVW5kZWZpbmVkIiwiZm9ybURhdGEiLCJ2YWxpZGF0ZUZvcm0iLCJPYmplY3QiLCJrZXlzIiwiZXJyb3JzIiwidG9IYXZlTGVuZ3RoIiwibGVuZ3RoIiwidG9CZUdyZWF0ZXJUaGFuIiwic2VydmljZVZhbGlkYXRvciIsInJlcXVpcmVkIiwibWluTGVuZ3RoIiwibWF4TGVuZ3RoIiwiZGVzY3JpcHRpb24iLCJwcmljZSIsIm51bWJlciIsImN1c3RvbSIsInZhbHVlIiwibnVtVmFsdWUiLCJOdW1iZXIiLCJpc05hTiIsImR1cmF0aW9uIiwidmFsaWRSZXN1bHQiLCJpbnZhbGlkUmVzdWx0IiwidG9Db250YWluIiwicHJvZmlsZVZhbGlkYXRvciIsImZpcnN0TmFtZSIsImxhc3ROYW1lIiwiZGF0ZU9mQmlydGgiLCJkYXRlIiwiZGF0ZVJlZ2V4IiwidGVzdCIsIkRhdGUiLCJ0b2RheSIsImZ1dHVyZVJlc3VsdCIsImVtYWlsU3RlcHMiLCJyZXN1bHRzIiwibWFwIiwic3RlcCIsInNsaWNlIiwiZm9yRWFjaCIsIm1hdGNoaW5nUmVzdWx0Iiwibm9uTWF0Y2hpbmdSZXN1bHQiLCJuYW1lUmVzdWx0IiwiZW1haWxSZXN1bHQiLCJwaG9uZVJlc3VsdCIsInNob3J0TmFtZVJlc3VsdCIsIndlYWtQYXNzd29yZFJlc3VsdCJdLCJzb3VyY2VzIjpbImZvcm1WYWxpZGF0aW9uLnRlc3QudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBFbmhhbmNlZCBGb3JtIFZhbGlkYXRpb24gVGVzdHNcbiAqIFxuICogQ29tcHJlaGVuc2l2ZSB0ZXN0IHN1aXRlIGZvciB0aGUgZW5oYW5jZWQgZm9ybSB2YWxpZGF0aW9uIHN5c3RlbVxuICogaW5jbHVkaW5nIHJlYWwtdGltZSB2YWxpZGF0aW9uLCBlcnJvciBoYW5kbGluZywgYW5kIHVzZXIgZmVlZGJhY2suXG4gKiBcbiAqIEB2ZXJzaW9uIDEuMC4wXG4gKiBAYXV0aG9yIFZpZXJsYSBEZXZlbG9wbWVudCBUZWFtXG4gKi9cblxuaW1wb3J0IHtcbiAgRm9ybVZhbGlkYXRvcixcbiAgQ29tbW9uVmFsaWRhdGlvbnMsXG4gIFZhbGlkYXRpb25SdWxlcyxcbiAgdmFsaWRhdGVGaWVsZCxcbiAgdmFsaWRhdGVGb3JtLFxufSBmcm9tICcuLi9mb3JtVmFsaWRhdGlvbic7XG5cbmRlc2NyaWJlKCdFbmhhbmNlZCBGb3JtIFZhbGlkYXRpb24gU3lzdGVtJywgKCkgPT4ge1xuICBkZXNjcmliZSgnRm9ybVZhbGlkYXRvciBDbGFzcycsICgpID0+IHtcbiAgICBsZXQgdmFsaWRhdG9yOiBGb3JtVmFsaWRhdG9yO1xuXG4gICAgYmVmb3JlRWFjaCgoKSA9PiB7XG4gICAgICB2YWxpZGF0b3IgPSBuZXcgRm9ybVZhbGlkYXRvcih7XG4gICAgICAgIG5hbWU6IENvbW1vblZhbGlkYXRpb25zLm5hbWUsXG4gICAgICAgIGVtYWlsOiBDb21tb25WYWxpZGF0aW9ucy5lbWFpbCxcbiAgICAgICAgcGhvbmU6IENvbW1vblZhbGlkYXRpb25zLnBob25lLFxuICAgICAgICBwYXNzd29yZDogQ29tbW9uVmFsaWRhdGlvbnMucGFzc3dvcmQsXG4gICAgICAgIGNvbmZpcm1QYXNzd29yZDogQ29tbW9uVmFsaWRhdGlvbnMuY29uZmlybVBhc3N3b3JkKCdwYXNzd29yZCcpLFxuICAgICAgfSk7XG4gICAgfSk7XG5cbiAgICBpdCgnc2hvdWxkIHZhbGlkYXRlIGluZGl2aWR1YWwgZmllbGRzIGNvcnJlY3RseScsICgpID0+IHtcbiAgICAgIGNvbnN0IHJlc3VsdCA9IHZhbGlkYXRvci52YWxpZGF0ZUZpZWxkKCduYW1lJywgJ0pvaG4gRG9lJywge30pO1xuICAgICAgZXhwZWN0KHJlc3VsdC5pc1ZhbGlkKS50b0JlKHRydWUpO1xuICAgICAgZXhwZWN0KHJlc3VsdC5lcnJvcikudG9CZVVuZGVmaW5lZCgpO1xuICAgIH0pO1xuXG4gICAgaXQoJ3Nob3VsZCBkZXRlY3QgaW52YWxpZCBmaWVsZHMnLCAoKSA9PiB7XG4gICAgICBjb25zdCByZXN1bHQgPSB2YWxpZGF0b3IudmFsaWRhdGVGaWVsZCgnZW1haWwnLCAnaW52YWxpZC1lbWFpbCcsIHt9KTtcbiAgICAgIGV4cGVjdChyZXN1bHQuaXNWYWxpZCkudG9CZShmYWxzZSk7XG4gICAgICBleHBlY3QocmVzdWx0LmVycm9yKS50b0JlKCdQbGVhc2UgZW50ZXIgeW91ciBlbWFpbCBpbiB0aGUgZm9ybWF0IG5hbWVAZXhhbXBsZS5jb20nKTtcbiAgICB9KTtcblxuICAgIGl0KCdzaG91bGQgdmFsaWRhdGUgZW50aXJlIGZvcm0nLCAoKSA9PiB7XG4gICAgICBjb25zdCBmb3JtRGF0YSA9IHtcbiAgICAgICAgbmFtZTogJ0pvaG4gRG9lJyxcbiAgICAgICAgZW1haWw6ICdqb2huQGV4YW1wbGUuY29tJyxcbiAgICAgICAgcGhvbmU6ICcrMTIzNDU2Nzg5MCcsXG4gICAgICAgIHBhc3N3b3JkOiAnU2VjdXJlUGFzczEyMyEnLFxuICAgICAgICBjb25maXJtUGFzc3dvcmQ6ICdTZWN1cmVQYXNzMTIzIScsXG4gICAgICB9O1xuXG4gICAgICBjb25zdCByZXN1bHQgPSB2YWxpZGF0b3IudmFsaWRhdGVGb3JtKGZvcm1EYXRhKTtcbiAgICAgIGV4cGVjdChyZXN1bHQuaXNWYWxpZCkudG9CZSh0cnVlKTtcbiAgICAgIGV4cGVjdChPYmplY3Qua2V5cyhyZXN1bHQuZXJyb3JzKSkudG9IYXZlTGVuZ3RoKDApO1xuICAgIH0pO1xuXG4gICAgaXQoJ3Nob3VsZCBkZXRlY3QgZm9ybSB2YWxpZGF0aW9uIGVycm9ycycsICgpID0+IHtcbiAgICAgIGNvbnN0IGZvcm1EYXRhID0ge1xuICAgICAgICBuYW1lOiAnJyxcbiAgICAgICAgZW1haWw6ICdpbnZhbGlkLWVtYWlsJyxcbiAgICAgICAgcGhvbmU6ICcxMjMnLFxuICAgICAgICBwYXNzd29yZDogJ3dlYWsnLFxuICAgICAgICBjb25maXJtUGFzc3dvcmQ6ICdkaWZmZXJlbnQnLFxuICAgICAgfTtcblxuICAgICAgY29uc3QgcmVzdWx0ID0gdmFsaWRhdG9yLnZhbGlkYXRlRm9ybShmb3JtRGF0YSk7XG4gICAgICBleHBlY3QocmVzdWx0LmlzVmFsaWQpLnRvQmUoZmFsc2UpO1xuICAgICAgZXhwZWN0KE9iamVjdC5rZXlzKHJlc3VsdC5lcnJvcnMpLmxlbmd0aCkudG9CZUdyZWF0ZXJUaGFuKDApO1xuICAgIH0pO1xuICB9KTtcblxuICBkZXNjcmliZSgnU2VydmljZSBGb3JtIFZhbGlkYXRpb24nLCAoKSA9PiB7XG4gICAgbGV0IHNlcnZpY2VWYWxpZGF0b3I6IEZvcm1WYWxpZGF0b3I7XG5cbiAgICBiZWZvcmVFYWNoKCgpID0+IHtcbiAgICAgIHNlcnZpY2VWYWxpZGF0b3IgPSBuZXcgRm9ybVZhbGlkYXRvcih7XG4gICAgICAgIG5hbWU6IHtcbiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSxcbiAgICAgICAgICBtaW5MZW5ndGg6IDMsXG4gICAgICAgICAgbWF4TGVuZ3RoOiAxMDAsXG4gICAgICAgIH0sXG4gICAgICAgIGRlc2NyaXB0aW9uOiB7XG4gICAgICAgICAgcmVxdWlyZWQ6IHRydWUsXG4gICAgICAgICAgbWluTGVuZ3RoOiAxMCxcbiAgICAgICAgICBtYXhMZW5ndGg6IDUwMCxcbiAgICAgICAgfSxcbiAgICAgICAgcHJpY2U6IHtcbiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSxcbiAgICAgICAgICBudW1iZXI6IHRydWUsXG4gICAgICAgICAgY3VzdG9tOiAodmFsdWU6IHN0cmluZykgPT4ge1xuICAgICAgICAgICAgY29uc3QgbnVtVmFsdWUgPSBOdW1iZXIodmFsdWUpO1xuICAgICAgICAgICAgaWYgKGlzTmFOKG51bVZhbHVlKSB8fCBudW1WYWx1ZSA8PSAwKSB7XG4gICAgICAgICAgICAgIHJldHVybiB7IGlzVmFsaWQ6IGZhbHNlLCBlcnJvcjogJ1ByaWNlIG11c3QgYmUgZ3JlYXRlciB0aGFuIDAnIH07XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4geyBpc1ZhbGlkOiB0cnVlIH07XG4gICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICBkdXJhdGlvbjoge1xuICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLFxuICAgICAgICAgIG51bWJlcjogdHJ1ZSxcbiAgICAgICAgICBjdXN0b206ICh2YWx1ZTogc3RyaW5nKSA9PiB7XG4gICAgICAgICAgICBjb25zdCBudW1WYWx1ZSA9IE51bWJlcih2YWx1ZSk7XG4gICAgICAgICAgICBpZiAoaXNOYU4obnVtVmFsdWUpIHx8IG51bVZhbHVlIDw9IDApIHtcbiAgICAgICAgICAgICAgcmV0dXJuIHsgaXNWYWxpZDogZmFsc2UsIGVycm9yOiAnRHVyYXRpb24gbXVzdCBiZSBncmVhdGVyIHRoYW4gMCcgfTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiB7IGlzVmFsaWQ6IHRydWUgfTtcbiAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICB9KTtcbiAgICB9KTtcblxuICAgIGl0KCdzaG91bGQgdmFsaWRhdGUgc2VydmljZSBuYW1lIGNvcnJlY3RseScsICgpID0+IHtcbiAgICAgIGNvbnN0IHZhbGlkUmVzdWx0ID0gc2VydmljZVZhbGlkYXRvci52YWxpZGF0ZUZpZWxkKCduYW1lJywgJ0hhaXIgQ3V0ICYgU3R5bGUnLCB7fSk7XG4gICAgICBleHBlY3QodmFsaWRSZXN1bHQuaXNWYWxpZCkudG9CZSh0cnVlKTtcblxuICAgICAgY29uc3QgaW52YWxpZFJlc3VsdCA9IHNlcnZpY2VWYWxpZGF0b3IudmFsaWRhdGVGaWVsZCgnbmFtZScsICdIaScsIHt9KTtcbiAgICAgIGV4cGVjdChpbnZhbGlkUmVzdWx0LmlzVmFsaWQpLnRvQmUoZmFsc2UpO1xuICAgICAgZXhwZWN0KGludmFsaWRSZXN1bHQuZXJyb3IpLnRvQ29udGFpbignYXQgbGVhc3QgMyBjaGFyYWN0ZXJzJyk7XG4gICAgfSk7XG5cbiAgICBpdCgnc2hvdWxkIHZhbGlkYXRlIHNlcnZpY2UgZGVzY3JpcHRpb24nLCAoKSA9PiB7XG4gICAgICBjb25zdCB2YWxpZFJlc3VsdCA9IHNlcnZpY2VWYWxpZGF0b3IudmFsaWRhdGVGaWVsZCgnZGVzY3JpcHRpb24nLCAnUHJvZmVzc2lvbmFsIGhhaXIgY3V0dGluZyBhbmQgc3R5bGluZyBzZXJ2aWNlJywge30pO1xuICAgICAgZXhwZWN0KHZhbGlkUmVzdWx0LmlzVmFsaWQpLnRvQmUodHJ1ZSk7XG5cbiAgICAgIGNvbnN0IGludmFsaWRSZXN1bHQgPSBzZXJ2aWNlVmFsaWRhdG9yLnZhbGlkYXRlRmllbGQoJ2Rlc2NyaXB0aW9uJywgJ1Nob3J0Jywge30pO1xuICAgICAgZXhwZWN0KGludmFsaWRSZXN1bHQuaXNWYWxpZCkudG9CZShmYWxzZSk7XG4gICAgICBleHBlY3QoaW52YWxpZFJlc3VsdC5lcnJvcikudG9Db250YWluKCdhdCBsZWFzdCAxMCBjaGFyYWN0ZXJzJyk7XG4gICAgfSk7XG5cbiAgICBpdCgnc2hvdWxkIHZhbGlkYXRlIHByaWNlIGNvcnJlY3RseScsICgpID0+IHtcbiAgICAgIGNvbnN0IHZhbGlkUmVzdWx0ID0gc2VydmljZVZhbGlkYXRvci52YWxpZGF0ZUZpZWxkKCdwcmljZScsICc1MC4wMCcsIHt9KTtcbiAgICAgIGV4cGVjdCh2YWxpZFJlc3VsdC5pc1ZhbGlkKS50b0JlKHRydWUpO1xuXG4gICAgICBjb25zdCBpbnZhbGlkUmVzdWx0ID0gc2VydmljZVZhbGlkYXRvci52YWxpZGF0ZUZpZWxkKCdwcmljZScsICctMTAnLCB7fSk7XG4gICAgICBleHBlY3QoaW52YWxpZFJlc3VsdC5pc1ZhbGlkKS50b0JlKGZhbHNlKTtcbiAgICAgIGV4cGVjdChpbnZhbGlkUmVzdWx0LmVycm9yKS50b0NvbnRhaW4oJ2dyZWF0ZXIgdGhhbiAwJyk7XG4gICAgfSk7XG5cbiAgICBpdCgnc2hvdWxkIHZhbGlkYXRlIGR1cmF0aW9uIGNvcnJlY3RseScsICgpID0+IHtcbiAgICAgIGNvbnN0IHZhbGlkUmVzdWx0ID0gc2VydmljZVZhbGlkYXRvci52YWxpZGF0ZUZpZWxkKCdkdXJhdGlvbicsICc2MCcsIHt9KTtcbiAgICAgIGV4cGVjdCh2YWxpZFJlc3VsdC5pc1ZhbGlkKS50b0JlKHRydWUpO1xuXG4gICAgICBjb25zdCBpbnZhbGlkUmVzdWx0ID0gc2VydmljZVZhbGlkYXRvci52YWxpZGF0ZUZpZWxkKCdkdXJhdGlvbicsICcwJywge30pO1xuICAgICAgZXhwZWN0KGludmFsaWRSZXN1bHQuaXNWYWxpZCkudG9CZShmYWxzZSk7XG4gICAgICBleHBlY3QoaW52YWxpZFJlc3VsdC5lcnJvcikudG9Db250YWluKCdncmVhdGVyIHRoYW4gMCcpO1xuICAgIH0pO1xuICB9KTtcblxuICBkZXNjcmliZSgnUHJvZmlsZSBGb3JtIFZhbGlkYXRpb24nLCAoKSA9PiB7XG4gICAgbGV0IHByb2ZpbGVWYWxpZGF0b3I6IEZvcm1WYWxpZGF0b3I7XG5cbiAgICBiZWZvcmVFYWNoKCgpID0+IHtcbiAgICAgIHByb2ZpbGVWYWxpZGF0b3IgPSBuZXcgRm9ybVZhbGlkYXRvcih7XG4gICAgICAgIGZpcnN0TmFtZTogQ29tbW9uVmFsaWRhdGlvbnMubmFtZSxcbiAgICAgICAgbGFzdE5hbWU6IENvbW1vblZhbGlkYXRpb25zLm5hbWUsXG4gICAgICAgIGVtYWlsOiBDb21tb25WYWxpZGF0aW9ucy5lbWFpbCxcbiAgICAgICAgcGhvbmU6IHtcbiAgICAgICAgICByZXF1aXJlZDogZmFsc2UsXG4gICAgICAgICAgcGhvbmU6IHRydWUsXG4gICAgICAgIH0sXG4gICAgICAgIGRhdGVPZkJpcnRoOiB7XG4gICAgICAgICAgcmVxdWlyZWQ6IGZhbHNlLFxuICAgICAgICAgIGRhdGU6IHRydWUsXG4gICAgICAgICAgY3VzdG9tOiAodmFsdWU6IHN0cmluZykgPT4ge1xuICAgICAgICAgICAgaWYgKCF2YWx1ZSkgcmV0dXJuIHsgaXNWYWxpZDogdHJ1ZSB9O1xuICAgICAgICAgICAgY29uc3QgZGF0ZVJlZ2V4ID0gL15cXGR7NH0tXFxkezJ9LVxcZHsyfSQvO1xuICAgICAgICAgICAgaWYgKCFkYXRlUmVnZXgudGVzdCh2YWx1ZSkpIHtcbiAgICAgICAgICAgICAgcmV0dXJuIHsgaXNWYWxpZDogZmFsc2UsIGVycm9yOiAnRGF0ZSBtdXN0IGJlIGluIFlZWVktTU0tREQgZm9ybWF0JyB9O1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKHZhbHVlKTtcbiAgICAgICAgICAgIGNvbnN0IHRvZGF5ID0gbmV3IERhdGUoKTtcbiAgICAgICAgICAgIGlmIChkYXRlID4gdG9kYXkpIHtcbiAgICAgICAgICAgICAgcmV0dXJuIHsgaXNWYWxpZDogZmFsc2UsIGVycm9yOiAnRGF0ZSBvZiBiaXJ0aCBjYW5ub3QgYmUgaW4gdGhlIGZ1dHVyZScgfTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiB7IGlzVmFsaWQ6IHRydWUgfTtcbiAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICB9KTtcbiAgICB9KTtcblxuICAgIGl0KCdzaG91bGQgdmFsaWRhdGUgcHJvZmlsZSBkYXRhIGNvcnJlY3RseScsICgpID0+IHtcbiAgICAgIGNvbnN0IGZvcm1EYXRhID0ge1xuICAgICAgICBmaXJzdE5hbWU6ICdKb2huJyxcbiAgICAgICAgbGFzdE5hbWU6ICdEb2UnLFxuICAgICAgICBlbWFpbDogJ2pvaG5AZXhhbXBsZS5jb20nLFxuICAgICAgICBwaG9uZTogJysxMjM0NTY3ODkwJyxcbiAgICAgICAgZGF0ZU9mQmlydGg6ICcxOTkwLTAxLTAxJyxcbiAgICAgIH07XG5cbiAgICAgIGNvbnN0IHJlc3VsdCA9IHByb2ZpbGVWYWxpZGF0b3IudmFsaWRhdGVGb3JtKGZvcm1EYXRhKTtcbiAgICAgIGV4cGVjdChyZXN1bHQuaXNWYWxpZCkudG9CZSh0cnVlKTtcbiAgICB9KTtcblxuICAgIGl0KCdzaG91bGQgaGFuZGxlIG9wdGlvbmFsIGZpZWxkcyBjb3JyZWN0bHknLCAoKSA9PiB7XG4gICAgICBjb25zdCBmb3JtRGF0YSA9IHtcbiAgICAgICAgZmlyc3ROYW1lOiAnSm9obicsXG4gICAgICAgIGxhc3ROYW1lOiAnRG9lJyxcbiAgICAgICAgZW1haWw6ICdqb2huQGV4YW1wbGUuY29tJyxcbiAgICAgICAgcGhvbmU6ICcnLFxuICAgICAgICBkYXRlT2ZCaXJ0aDogJycsXG4gICAgICB9O1xuXG4gICAgICBjb25zdCByZXN1bHQgPSBwcm9maWxlVmFsaWRhdG9yLnZhbGlkYXRlRm9ybShmb3JtRGF0YSk7XG4gICAgICBleHBlY3QocmVzdWx0LmlzVmFsaWQpLnRvQmUodHJ1ZSk7XG4gICAgfSk7XG5cbiAgICBpdCgnc2hvdWxkIHZhbGlkYXRlIGRhdGUgb2YgYmlydGggZm9ybWF0JywgKCkgPT4ge1xuICAgICAgY29uc3QgaW52YWxpZFJlc3VsdCA9IHByb2ZpbGVWYWxpZGF0b3IudmFsaWRhdGVGaWVsZCgnZGF0ZU9mQmlydGgnLCAnMDEvMDEvMTk5MCcsIHt9KTtcbiAgICAgIGV4cGVjdChpbnZhbGlkUmVzdWx0LmlzVmFsaWQpLnRvQmUoZmFsc2UpO1xuICAgICAgZXhwZWN0KGludmFsaWRSZXN1bHQuZXJyb3IpLnRvQ29udGFpbignWVlZWS1NTS1ERCBmb3JtYXQnKTtcblxuICAgICAgY29uc3QgZnV0dXJlUmVzdWx0ID0gcHJvZmlsZVZhbGlkYXRvci52YWxpZGF0ZUZpZWxkKCdkYXRlT2ZCaXJ0aCcsICcyMDMwLTAxLTAxJywge30pO1xuICAgICAgZXhwZWN0KGZ1dHVyZVJlc3VsdC5pc1ZhbGlkKS50b0JlKGZhbHNlKTtcbiAgICAgIGV4cGVjdChmdXR1cmVSZXN1bHQuZXJyb3IpLnRvQ29udGFpbignY2Fubm90IGJlIGluIHRoZSBmdXR1cmUnKTtcbiAgICB9KTtcbiAgfSk7XG5cbiAgZGVzY3JpYmUoJ1JlYWwtdGltZSBWYWxpZGF0aW9uIFNjZW5hcmlvcycsICgpID0+IHtcbiAgICBpdCgnc2hvdWxkIHByb3ZpZGUgaW1tZWRpYXRlIGZlZWRiYWNrIGZvciBlbWFpbCB2YWxpZGF0aW9uJywgKCkgPT4ge1xuICAgICAgY29uc3QgZW1haWxTdGVwcyA9IFsnaicsICdqbycsICdqb2huJywgJ2pvaG5AJywgJ2pvaG5AZXgnLCAnam9obkBleGFtcGxlLmNvbSddO1xuICAgICAgY29uc3QgcmVzdWx0cyA9IGVtYWlsU3RlcHMubWFwKHN0ZXAgPT4gXG4gICAgICAgIHZhbGlkYXRlRmllbGQoc3RlcCwgQ29tbW9uVmFsaWRhdGlvbnMuZW1haWwsIHt9KVxuICAgICAgKTtcblxuICAgICAgLy8gT25seSB0aGUgY29tcGxldGUgZW1haWwgc2hvdWxkIGJlIHZhbGlkXG4gICAgICBleHBlY3QocmVzdWx0c1tyZXN1bHRzLmxlbmd0aCAtIDFdLmlzVmFsaWQpLnRvQmUodHJ1ZSk7XG4gICAgICByZXN1bHRzLnNsaWNlKDAsIC0xKS5mb3JFYWNoKHJlc3VsdCA9PiB7XG4gICAgICAgIGV4cGVjdChyZXN1bHQuaXNWYWxpZCkudG9CZShmYWxzZSk7XG4gICAgICB9KTtcbiAgICB9KTtcblxuICAgIGl0KCdzaG91bGQgaGFuZGxlIHBhc3N3b3JkIGNvbmZpcm1hdGlvbiB2YWxpZGF0aW9uJywgKCkgPT4ge1xuICAgICAgY29uc3QgZm9ybURhdGEgPSB7IHBhc3N3b3JkOiAnU2VjdXJlUGFzczEyMyEnIH07XG4gICAgICBcbiAgICAgIGNvbnN0IG1hdGNoaW5nUmVzdWx0ID0gdmFsaWRhdGVGaWVsZCgnU2VjdXJlUGFzczEyMyEnLCBDb21tb25WYWxpZGF0aW9ucy5jb25maXJtUGFzc3dvcmQoJ3Bhc3N3b3JkJyksIGZvcm1EYXRhKTtcbiAgICAgIGV4cGVjdChtYXRjaGluZ1Jlc3VsdC5pc1ZhbGlkKS50b0JlKHRydWUpO1xuXG4gICAgICBjb25zdCBub25NYXRjaGluZ1Jlc3VsdCA9IHZhbGlkYXRlRmllbGQoJ0RpZmZlcmVudFBhc3MxMjMhJywgQ29tbW9uVmFsaWRhdGlvbnMuY29uZmlybVBhc3N3b3JkKCdwYXNzd29yZCcpLCBmb3JtRGF0YSk7XG4gICAgICBleHBlY3Qobm9uTWF0Y2hpbmdSZXN1bHQuaXNWYWxpZCkudG9CZShmYWxzZSk7XG4gICAgICBleHBlY3Qobm9uTWF0Y2hpbmdSZXN1bHQuZXJyb3IpLnRvQ29udGFpbignZG8gbm90IG1hdGNoJyk7XG4gICAgfSk7XG4gIH0pO1xuXG4gIGRlc2NyaWJlKCdFcnJvciBNZXNzYWdlIFF1YWxpdHknLCAoKSA9PiB7XG4gICAgaXQoJ3Nob3VsZCBwcm92aWRlIGNsZWFyLCBhY3Rpb25hYmxlIGVycm9yIG1lc3NhZ2VzJywgKCkgPT4ge1xuICAgICAgY29uc3QgbmFtZVJlc3VsdCA9IHZhbGlkYXRlRmllbGQoJycsIENvbW1vblZhbGlkYXRpb25zLm5hbWUsIHt9KTtcbiAgICAgIGV4cGVjdChuYW1lUmVzdWx0LmVycm9yKS50b0JlKCdQbGVhc2UgZmlsbCBpbiB0aGlzIGZpZWxkIHRvIGNvbnRpbnVlJyk7XG5cbiAgICAgIGNvbnN0IGVtYWlsUmVzdWx0ID0gdmFsaWRhdGVGaWVsZCgnaW52YWxpZCcsIENvbW1vblZhbGlkYXRpb25zLmVtYWlsLCB7fSk7XG4gICAgICBleHBlY3QoZW1haWxSZXN1bHQuZXJyb3IpLnRvQmUoJ1BsZWFzZSBlbnRlciB5b3VyIGVtYWlsIGluIHRoZSBmb3JtYXQgbmFtZUBleGFtcGxlLmNvbScpO1xuXG4gICAgICBjb25zdCBwaG9uZVJlc3VsdCA9IHZhbGlkYXRlRmllbGQoJzEyMycsIENvbW1vblZhbGlkYXRpb25zLnBob25lLCB7fSk7XG4gICAgICBleHBlY3QocGhvbmVSZXN1bHQuZXJyb3IpLnRvQmUoJ1BsZWFzZSBlbnRlciB5b3VyIHBob25lIG51bWJlciB3aXRoIGFyZWEgY29kZSAoZS5nLiwgNTU1LTEyMy00NTY3KScpO1xuICAgIH0pO1xuXG4gICAgaXQoJ3Nob3VsZCBwcm92aWRlIHNwZWNpZmljIHZhbGlkYXRpb24gZmVlZGJhY2snLCAoKSA9PiB7XG4gICAgICBjb25zdCBzaG9ydE5hbWVSZXN1bHQgPSB2YWxpZGF0ZUZpZWxkKCdBJywgQ29tbW9uVmFsaWRhdGlvbnMubmFtZSwge30pO1xuICAgICAgZXhwZWN0KHNob3J0TmFtZVJlc3VsdC5lcnJvcikudG9Db250YWluKCcyLTUwIGNoYXJhY3RlcnMnKTtcblxuICAgICAgY29uc3Qgd2Vha1Bhc3N3b3JkUmVzdWx0ID0gdmFsaWRhdGVGaWVsZCgnMTIzJywgQ29tbW9uVmFsaWRhdGlvbnMucGFzc3dvcmQsIHt9KTtcbiAgICAgIGV4cGVjdCh3ZWFrUGFzc3dvcmRSZXN1bHQuZXJyb3IpLnRvQ29udGFpbignYXQgbGVhc3QgOCBjaGFyYWN0ZXJzJyk7XG4gICAgfSk7XG4gIH0pO1xufSk7XG4iXSwibWFwcGluZ3MiOiJBQVVBLElBQUFBLGVBQUEsR0FBQUMsT0FBQTtBQVFBQyxRQUFRLENBQUMsaUNBQWlDLEVBQUUsWUFBTTtFQUNoREEsUUFBUSxDQUFDLHFCQUFxQixFQUFFLFlBQU07SUFDcEMsSUFBSUMsU0FBd0I7SUFFNUJDLFVBQVUsQ0FBQyxZQUFNO01BQ2ZELFNBQVMsR0FBRyxJQUFJRSw2QkFBYSxDQUFDO1FBQzVCQyxJQUFJLEVBQUVDLGlDQUFpQixDQUFDRCxJQUFJO1FBQzVCRSxLQUFLLEVBQUVELGlDQUFpQixDQUFDQyxLQUFLO1FBQzlCQyxLQUFLLEVBQUVGLGlDQUFpQixDQUFDRSxLQUFLO1FBQzlCQyxRQUFRLEVBQUVILGlDQUFpQixDQUFDRyxRQUFRO1FBQ3BDQyxlQUFlLEVBQUVKLGlDQUFpQixDQUFDSSxlQUFlLENBQUMsVUFBVTtNQUMvRCxDQUFDLENBQUM7SUFDSixDQUFDLENBQUM7SUFFRkMsRUFBRSxDQUFDLDZDQUE2QyxFQUFFLFlBQU07TUFDdEQsSUFBTUMsTUFBTSxHQUFHVixTQUFTLENBQUNXLGFBQWEsQ0FBQyxNQUFNLEVBQUUsVUFBVSxFQUFFLENBQUMsQ0FBQyxDQUFDO01BQzlEQyxNQUFNLENBQUNGLE1BQU0sQ0FBQ0csT0FBTyxDQUFDLENBQUNDLElBQUksQ0FBQyxJQUFJLENBQUM7TUFDakNGLE1BQU0sQ0FBQ0YsTUFBTSxDQUFDSyxLQUFLLENBQUMsQ0FBQ0MsYUFBYSxDQUFDLENBQUM7SUFDdEMsQ0FBQyxDQUFDO0lBRUZQLEVBQUUsQ0FBQyw4QkFBOEIsRUFBRSxZQUFNO01BQ3ZDLElBQU1DLE1BQU0sR0FBR1YsU0FBUyxDQUFDVyxhQUFhLENBQUMsT0FBTyxFQUFFLGVBQWUsRUFBRSxDQUFDLENBQUMsQ0FBQztNQUNwRUMsTUFBTSxDQUFDRixNQUFNLENBQUNHLE9BQU8sQ0FBQyxDQUFDQyxJQUFJLENBQUMsS0FBSyxDQUFDO01BQ2xDRixNQUFNLENBQUNGLE1BQU0sQ0FBQ0ssS0FBSyxDQUFDLENBQUNELElBQUksQ0FBQyx3REFBd0QsQ0FBQztJQUNyRixDQUFDLENBQUM7SUFFRkwsRUFBRSxDQUFDLDZCQUE2QixFQUFFLFlBQU07TUFDdEMsSUFBTVEsUUFBUSxHQUFHO1FBQ2ZkLElBQUksRUFBRSxVQUFVO1FBQ2hCRSxLQUFLLEVBQUUsa0JBQWtCO1FBQ3pCQyxLQUFLLEVBQUUsYUFBYTtRQUNwQkMsUUFBUSxFQUFFLGdCQUFnQjtRQUMxQkMsZUFBZSxFQUFFO01BQ25CLENBQUM7TUFFRCxJQUFNRSxNQUFNLEdBQUdWLFNBQVMsQ0FBQ2tCLFlBQVksQ0FBQ0QsUUFBUSxDQUFDO01BQy9DTCxNQUFNLENBQUNGLE1BQU0sQ0FBQ0csT0FBTyxDQUFDLENBQUNDLElBQUksQ0FBQyxJQUFJLENBQUM7TUFDakNGLE1BQU0sQ0FBQ08sTUFBTSxDQUFDQyxJQUFJLENBQUNWLE1BQU0sQ0FBQ1csTUFBTSxDQUFDLENBQUMsQ0FBQ0MsWUFBWSxDQUFDLENBQUMsQ0FBQztJQUNwRCxDQUFDLENBQUM7SUFFRmIsRUFBRSxDQUFDLHNDQUFzQyxFQUFFLFlBQU07TUFDL0MsSUFBTVEsUUFBUSxHQUFHO1FBQ2ZkLElBQUksRUFBRSxFQUFFO1FBQ1JFLEtBQUssRUFBRSxlQUFlO1FBQ3RCQyxLQUFLLEVBQUUsS0FBSztRQUNaQyxRQUFRLEVBQUUsTUFBTTtRQUNoQkMsZUFBZSxFQUFFO01BQ25CLENBQUM7TUFFRCxJQUFNRSxNQUFNLEdBQUdWLFNBQVMsQ0FBQ2tCLFlBQVksQ0FBQ0QsUUFBUSxDQUFDO01BQy9DTCxNQUFNLENBQUNGLE1BQU0sQ0FBQ0csT0FBTyxDQUFDLENBQUNDLElBQUksQ0FBQyxLQUFLLENBQUM7TUFDbENGLE1BQU0sQ0FBQ08sTUFBTSxDQUFDQyxJQUFJLENBQUNWLE1BQU0sQ0FBQ1csTUFBTSxDQUFDLENBQUNFLE1BQU0sQ0FBQyxDQUFDQyxlQUFlLENBQUMsQ0FBQyxDQUFDO0lBQzlELENBQUMsQ0FBQztFQUNKLENBQUMsQ0FBQztFQUVGekIsUUFBUSxDQUFDLHlCQUF5QixFQUFFLFlBQU07SUFDeEMsSUFBSTBCLGdCQUErQjtJQUVuQ3hCLFVBQVUsQ0FBQyxZQUFNO01BQ2Z3QixnQkFBZ0IsR0FBRyxJQUFJdkIsNkJBQWEsQ0FBQztRQUNuQ0MsSUFBSSxFQUFFO1VBQ0p1QixRQUFRLEVBQUUsSUFBSTtVQUNkQyxTQUFTLEVBQUUsQ0FBQztVQUNaQyxTQUFTLEVBQUU7UUFDYixDQUFDO1FBQ0RDLFdBQVcsRUFBRTtVQUNYSCxRQUFRLEVBQUUsSUFBSTtVQUNkQyxTQUFTLEVBQUUsRUFBRTtVQUNiQyxTQUFTLEVBQUU7UUFDYixDQUFDO1FBQ0RFLEtBQUssRUFBRTtVQUNMSixRQUFRLEVBQUUsSUFBSTtVQUNkSyxNQUFNLEVBQUUsSUFBSTtVQUNaQyxNQUFNLEVBQUUsU0FBUkEsTUFBTUEsQ0FBR0MsS0FBYSxFQUFLO1lBQ3pCLElBQU1DLFFBQVEsR0FBR0MsTUFBTSxDQUFDRixLQUFLLENBQUM7WUFDOUIsSUFBSUcsS0FBSyxDQUFDRixRQUFRLENBQUMsSUFBSUEsUUFBUSxJQUFJLENBQUMsRUFBRTtjQUNwQyxPQUFPO2dCQUFFckIsT0FBTyxFQUFFLEtBQUs7Z0JBQUVFLEtBQUssRUFBRTtjQUErQixDQUFDO1lBQ2xFO1lBQ0EsT0FBTztjQUFFRixPQUFPLEVBQUU7WUFBSyxDQUFDO1VBQzFCO1FBQ0YsQ0FBQztRQUNEd0IsUUFBUSxFQUFFO1VBQ1JYLFFBQVEsRUFBRSxJQUFJO1VBQ2RLLE1BQU0sRUFBRSxJQUFJO1VBQ1pDLE1BQU0sRUFBRSxTQUFSQSxNQUFNQSxDQUFHQyxLQUFhLEVBQUs7WUFDekIsSUFBTUMsUUFBUSxHQUFHQyxNQUFNLENBQUNGLEtBQUssQ0FBQztZQUM5QixJQUFJRyxLQUFLLENBQUNGLFFBQVEsQ0FBQyxJQUFJQSxRQUFRLElBQUksQ0FBQyxFQUFFO2NBQ3BDLE9BQU87Z0JBQUVyQixPQUFPLEVBQUUsS0FBSztnQkFBRUUsS0FBSyxFQUFFO2NBQWtDLENBQUM7WUFDckU7WUFDQSxPQUFPO2NBQUVGLE9BQU8sRUFBRTtZQUFLLENBQUM7VUFDMUI7UUFDRjtNQUNGLENBQUMsQ0FBQztJQUNKLENBQUMsQ0FBQztJQUVGSixFQUFFLENBQUMsd0NBQXdDLEVBQUUsWUFBTTtNQUNqRCxJQUFNNkIsV0FBVyxHQUFHYixnQkFBZ0IsQ0FBQ2QsYUFBYSxDQUFDLE1BQU0sRUFBRSxrQkFBa0IsRUFBRSxDQUFDLENBQUMsQ0FBQztNQUNsRkMsTUFBTSxDQUFDMEIsV0FBVyxDQUFDekIsT0FBTyxDQUFDLENBQUNDLElBQUksQ0FBQyxJQUFJLENBQUM7TUFFdEMsSUFBTXlCLGFBQWEsR0FBR2QsZ0JBQWdCLENBQUNkLGFBQWEsQ0FBQyxNQUFNLEVBQUUsSUFBSSxFQUFFLENBQUMsQ0FBQyxDQUFDO01BQ3RFQyxNQUFNLENBQUMyQixhQUFhLENBQUMxQixPQUFPLENBQUMsQ0FBQ0MsSUFBSSxDQUFDLEtBQUssQ0FBQztNQUN6Q0YsTUFBTSxDQUFDMkIsYUFBYSxDQUFDeEIsS0FBSyxDQUFDLENBQUN5QixTQUFTLENBQUMsdUJBQXVCLENBQUM7SUFDaEUsQ0FBQyxDQUFDO0lBRUYvQixFQUFFLENBQUMscUNBQXFDLEVBQUUsWUFBTTtNQUM5QyxJQUFNNkIsV0FBVyxHQUFHYixnQkFBZ0IsQ0FBQ2QsYUFBYSxDQUFDLGFBQWEsRUFBRSwrQ0FBK0MsRUFBRSxDQUFDLENBQUMsQ0FBQztNQUN0SEMsTUFBTSxDQUFDMEIsV0FBVyxDQUFDekIsT0FBTyxDQUFDLENBQUNDLElBQUksQ0FBQyxJQUFJLENBQUM7TUFFdEMsSUFBTXlCLGFBQWEsR0FBR2QsZ0JBQWdCLENBQUNkLGFBQWEsQ0FBQyxhQUFhLEVBQUUsT0FBTyxFQUFFLENBQUMsQ0FBQyxDQUFDO01BQ2hGQyxNQUFNLENBQUMyQixhQUFhLENBQUMxQixPQUFPLENBQUMsQ0FBQ0MsSUFBSSxDQUFDLEtBQUssQ0FBQztNQUN6Q0YsTUFBTSxDQUFDMkIsYUFBYSxDQUFDeEIsS0FBSyxDQUFDLENBQUN5QixTQUFTLENBQUMsd0JBQXdCLENBQUM7SUFDakUsQ0FBQyxDQUFDO0lBRUYvQixFQUFFLENBQUMsaUNBQWlDLEVBQUUsWUFBTTtNQUMxQyxJQUFNNkIsV0FBVyxHQUFHYixnQkFBZ0IsQ0FBQ2QsYUFBYSxDQUFDLE9BQU8sRUFBRSxPQUFPLEVBQUUsQ0FBQyxDQUFDLENBQUM7TUFDeEVDLE1BQU0sQ0FBQzBCLFdBQVcsQ0FBQ3pCLE9BQU8sQ0FBQyxDQUFDQyxJQUFJLENBQUMsSUFBSSxDQUFDO01BRXRDLElBQU15QixhQUFhLEdBQUdkLGdCQUFnQixDQUFDZCxhQUFhLENBQUMsT0FBTyxFQUFFLEtBQUssRUFBRSxDQUFDLENBQUMsQ0FBQztNQUN4RUMsTUFBTSxDQUFDMkIsYUFBYSxDQUFDMUIsT0FBTyxDQUFDLENBQUNDLElBQUksQ0FBQyxLQUFLLENBQUM7TUFDekNGLE1BQU0sQ0FBQzJCLGFBQWEsQ0FBQ3hCLEtBQUssQ0FBQyxDQUFDeUIsU0FBUyxDQUFDLGdCQUFnQixDQUFDO0lBQ3pELENBQUMsQ0FBQztJQUVGL0IsRUFBRSxDQUFDLG9DQUFvQyxFQUFFLFlBQU07TUFDN0MsSUFBTTZCLFdBQVcsR0FBR2IsZ0JBQWdCLENBQUNkLGFBQWEsQ0FBQyxVQUFVLEVBQUUsSUFBSSxFQUFFLENBQUMsQ0FBQyxDQUFDO01BQ3hFQyxNQUFNLENBQUMwQixXQUFXLENBQUN6QixPQUFPLENBQUMsQ0FBQ0MsSUFBSSxDQUFDLElBQUksQ0FBQztNQUV0QyxJQUFNeUIsYUFBYSxHQUFHZCxnQkFBZ0IsQ0FBQ2QsYUFBYSxDQUFDLFVBQVUsRUFBRSxHQUFHLEVBQUUsQ0FBQyxDQUFDLENBQUM7TUFDekVDLE1BQU0sQ0FBQzJCLGFBQWEsQ0FBQzFCLE9BQU8sQ0FBQyxDQUFDQyxJQUFJLENBQUMsS0FBSyxDQUFDO01BQ3pDRixNQUFNLENBQUMyQixhQUFhLENBQUN4QixLQUFLLENBQUMsQ0FBQ3lCLFNBQVMsQ0FBQyxnQkFBZ0IsQ0FBQztJQUN6RCxDQUFDLENBQUM7RUFDSixDQUFDLENBQUM7RUFFRnpDLFFBQVEsQ0FBQyx5QkFBeUIsRUFBRSxZQUFNO0lBQ3hDLElBQUkwQyxnQkFBK0I7SUFFbkN4QyxVQUFVLENBQUMsWUFBTTtNQUNmd0MsZ0JBQWdCLEdBQUcsSUFBSXZDLDZCQUFhLENBQUM7UUFDbkN3QyxTQUFTLEVBQUV0QyxpQ0FBaUIsQ0FBQ0QsSUFBSTtRQUNqQ3dDLFFBQVEsRUFBRXZDLGlDQUFpQixDQUFDRCxJQUFJO1FBQ2hDRSxLQUFLLEVBQUVELGlDQUFpQixDQUFDQyxLQUFLO1FBQzlCQyxLQUFLLEVBQUU7VUFDTG9CLFFBQVEsRUFBRSxLQUFLO1VBQ2ZwQixLQUFLLEVBQUU7UUFDVCxDQUFDO1FBQ0RzQyxXQUFXLEVBQUU7VUFDWGxCLFFBQVEsRUFBRSxLQUFLO1VBQ2ZtQixJQUFJLEVBQUUsSUFBSTtVQUNWYixNQUFNLEVBQUUsU0FBUkEsTUFBTUEsQ0FBR0MsS0FBYSxFQUFLO1lBQ3pCLElBQUksQ0FBQ0EsS0FBSyxFQUFFLE9BQU87Y0FBRXBCLE9BQU8sRUFBRTtZQUFLLENBQUM7WUFDcEMsSUFBTWlDLFNBQVMsR0FBRyxxQkFBcUI7WUFDdkMsSUFBSSxDQUFDQSxTQUFTLENBQUNDLElBQUksQ0FBQ2QsS0FBSyxDQUFDLEVBQUU7Y0FDMUIsT0FBTztnQkFBRXBCLE9BQU8sRUFBRSxLQUFLO2dCQUFFRSxLQUFLLEVBQUU7Y0FBb0MsQ0FBQztZQUN2RTtZQUNBLElBQU04QixJQUFJLEdBQUcsSUFBSUcsSUFBSSxDQUFDZixLQUFLLENBQUM7WUFDNUIsSUFBTWdCLEtBQUssR0FBRyxJQUFJRCxJQUFJLENBQUMsQ0FBQztZQUN4QixJQUFJSCxJQUFJLEdBQUdJLEtBQUssRUFBRTtjQUNoQixPQUFPO2dCQUFFcEMsT0FBTyxFQUFFLEtBQUs7Z0JBQUVFLEtBQUssRUFBRTtjQUF3QyxDQUFDO1lBQzNFO1lBQ0EsT0FBTztjQUFFRixPQUFPLEVBQUU7WUFBSyxDQUFDO1VBQzFCO1FBQ0Y7TUFDRixDQUFDLENBQUM7SUFDSixDQUFDLENBQUM7SUFFRkosRUFBRSxDQUFDLHdDQUF3QyxFQUFFLFlBQU07TUFDakQsSUFBTVEsUUFBUSxHQUFHO1FBQ2Z5QixTQUFTLEVBQUUsTUFBTTtRQUNqQkMsUUFBUSxFQUFFLEtBQUs7UUFDZnRDLEtBQUssRUFBRSxrQkFBa0I7UUFDekJDLEtBQUssRUFBRSxhQUFhO1FBQ3BCc0MsV0FBVyxFQUFFO01BQ2YsQ0FBQztNQUVELElBQU1sQyxNQUFNLEdBQUcrQixnQkFBZ0IsQ0FBQ3ZCLFlBQVksQ0FBQ0QsUUFBUSxDQUFDO01BQ3RETCxNQUFNLENBQUNGLE1BQU0sQ0FBQ0csT0FBTyxDQUFDLENBQUNDLElBQUksQ0FBQyxJQUFJLENBQUM7SUFDbkMsQ0FBQyxDQUFDO0lBRUZMLEVBQUUsQ0FBQyx5Q0FBeUMsRUFBRSxZQUFNO01BQ2xELElBQU1RLFFBQVEsR0FBRztRQUNmeUIsU0FBUyxFQUFFLE1BQU07UUFDakJDLFFBQVEsRUFBRSxLQUFLO1FBQ2Z0QyxLQUFLLEVBQUUsa0JBQWtCO1FBQ3pCQyxLQUFLLEVBQUUsRUFBRTtRQUNUc0MsV0FBVyxFQUFFO01BQ2YsQ0FBQztNQUVELElBQU1sQyxNQUFNLEdBQUcrQixnQkFBZ0IsQ0FBQ3ZCLFlBQVksQ0FBQ0QsUUFBUSxDQUFDO01BQ3RETCxNQUFNLENBQUNGLE1BQU0sQ0FBQ0csT0FBTyxDQUFDLENBQUNDLElBQUksQ0FBQyxJQUFJLENBQUM7SUFDbkMsQ0FBQyxDQUFDO0lBRUZMLEVBQUUsQ0FBQyxzQ0FBc0MsRUFBRSxZQUFNO01BQy9DLElBQU04QixhQUFhLEdBQUdFLGdCQUFnQixDQUFDOUIsYUFBYSxDQUFDLGFBQWEsRUFBRSxZQUFZLEVBQUUsQ0FBQyxDQUFDLENBQUM7TUFDckZDLE1BQU0sQ0FBQzJCLGFBQWEsQ0FBQzFCLE9BQU8sQ0FBQyxDQUFDQyxJQUFJLENBQUMsS0FBSyxDQUFDO01BQ3pDRixNQUFNLENBQUMyQixhQUFhLENBQUN4QixLQUFLLENBQUMsQ0FBQ3lCLFNBQVMsQ0FBQyxtQkFBbUIsQ0FBQztNQUUxRCxJQUFNVSxZQUFZLEdBQUdULGdCQUFnQixDQUFDOUIsYUFBYSxDQUFDLGFBQWEsRUFBRSxZQUFZLEVBQUUsQ0FBQyxDQUFDLENBQUM7TUFDcEZDLE1BQU0sQ0FBQ3NDLFlBQVksQ0FBQ3JDLE9BQU8sQ0FBQyxDQUFDQyxJQUFJLENBQUMsS0FBSyxDQUFDO01BQ3hDRixNQUFNLENBQUNzQyxZQUFZLENBQUNuQyxLQUFLLENBQUMsQ0FBQ3lCLFNBQVMsQ0FBQyx5QkFBeUIsQ0FBQztJQUNqRSxDQUFDLENBQUM7RUFDSixDQUFDLENBQUM7RUFFRnpDLFFBQVEsQ0FBQyxnQ0FBZ0MsRUFBRSxZQUFNO0lBQy9DVSxFQUFFLENBQUMsd0RBQXdELEVBQUUsWUFBTTtNQUNqRSxJQUFNMEMsVUFBVSxHQUFHLENBQUMsR0FBRyxFQUFFLElBQUksRUFBRSxNQUFNLEVBQUUsT0FBTyxFQUFFLFNBQVMsRUFBRSxrQkFBa0IsQ0FBQztNQUM5RSxJQUFNQyxPQUFPLEdBQUdELFVBQVUsQ0FBQ0UsR0FBRyxDQUFDLFVBQUFDLElBQUk7UUFBQSxPQUNqQyxJQUFBM0MsNkJBQWEsRUFBQzJDLElBQUksRUFBRWxELGlDQUFpQixDQUFDQyxLQUFLLEVBQUUsQ0FBQyxDQUFDLENBQUM7TUFBQSxDQUNsRCxDQUFDO01BR0RPLE1BQU0sQ0FBQ3dDLE9BQU8sQ0FBQ0EsT0FBTyxDQUFDN0IsTUFBTSxHQUFHLENBQUMsQ0FBQyxDQUFDVixPQUFPLENBQUMsQ0FBQ0MsSUFBSSxDQUFDLElBQUksQ0FBQztNQUN0RHNDLE9BQU8sQ0FBQ0csS0FBSyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDQyxPQUFPLENBQUMsVUFBQTlDLE1BQU0sRUFBSTtRQUNyQ0UsTUFBTSxDQUFDRixNQUFNLENBQUNHLE9BQU8sQ0FBQyxDQUFDQyxJQUFJLENBQUMsS0FBSyxDQUFDO01BQ3BDLENBQUMsQ0FBQztJQUNKLENBQUMsQ0FBQztJQUVGTCxFQUFFLENBQUMsZ0RBQWdELEVBQUUsWUFBTTtNQUN6RCxJQUFNUSxRQUFRLEdBQUc7UUFBRVYsUUFBUSxFQUFFO01BQWlCLENBQUM7TUFFL0MsSUFBTWtELGNBQWMsR0FBRyxJQUFBOUMsNkJBQWEsRUFBQyxnQkFBZ0IsRUFBRVAsaUNBQWlCLENBQUNJLGVBQWUsQ0FBQyxVQUFVLENBQUMsRUFBRVMsUUFBUSxDQUFDO01BQy9HTCxNQUFNLENBQUM2QyxjQUFjLENBQUM1QyxPQUFPLENBQUMsQ0FBQ0MsSUFBSSxDQUFDLElBQUksQ0FBQztNQUV6QyxJQUFNNEMsaUJBQWlCLEdBQUcsSUFBQS9DLDZCQUFhLEVBQUMsbUJBQW1CLEVBQUVQLGlDQUFpQixDQUFDSSxlQUFlLENBQUMsVUFBVSxDQUFDLEVBQUVTLFFBQVEsQ0FBQztNQUNySEwsTUFBTSxDQUFDOEMsaUJBQWlCLENBQUM3QyxPQUFPLENBQUMsQ0FBQ0MsSUFBSSxDQUFDLEtBQUssQ0FBQztNQUM3Q0YsTUFBTSxDQUFDOEMsaUJBQWlCLENBQUMzQyxLQUFLLENBQUMsQ0FBQ3lCLFNBQVMsQ0FBQyxjQUFjLENBQUM7SUFDM0QsQ0FBQyxDQUFDO0VBQ0osQ0FBQyxDQUFDO0VBRUZ6QyxRQUFRLENBQUMsdUJBQXVCLEVBQUUsWUFBTTtJQUN0Q1UsRUFBRSxDQUFDLGlEQUFpRCxFQUFFLFlBQU07TUFDMUQsSUFBTWtELFVBQVUsR0FBRyxJQUFBaEQsNkJBQWEsRUFBQyxFQUFFLEVBQUVQLGlDQUFpQixDQUFDRCxJQUFJLEVBQUUsQ0FBQyxDQUFDLENBQUM7TUFDaEVTLE1BQU0sQ0FBQytDLFVBQVUsQ0FBQzVDLEtBQUssQ0FBQyxDQUFDRCxJQUFJLENBQUMsdUNBQXVDLENBQUM7TUFFdEUsSUFBTThDLFdBQVcsR0FBRyxJQUFBakQsNkJBQWEsRUFBQyxTQUFTLEVBQUVQLGlDQUFpQixDQUFDQyxLQUFLLEVBQUUsQ0FBQyxDQUFDLENBQUM7TUFDekVPLE1BQU0sQ0FBQ2dELFdBQVcsQ0FBQzdDLEtBQUssQ0FBQyxDQUFDRCxJQUFJLENBQUMsd0RBQXdELENBQUM7TUFFeEYsSUFBTStDLFdBQVcsR0FBRyxJQUFBbEQsNkJBQWEsRUFBQyxLQUFLLEVBQUVQLGlDQUFpQixDQUFDRSxLQUFLLEVBQUUsQ0FBQyxDQUFDLENBQUM7TUFDckVNLE1BQU0sQ0FBQ2lELFdBQVcsQ0FBQzlDLEtBQUssQ0FBQyxDQUFDRCxJQUFJLENBQUMsb0VBQW9FLENBQUM7SUFDdEcsQ0FBQyxDQUFDO0lBRUZMLEVBQUUsQ0FBQyw2Q0FBNkMsRUFBRSxZQUFNO01BQ3RELElBQU1xRCxlQUFlLEdBQUcsSUFBQW5ELDZCQUFhLEVBQUMsR0FBRyxFQUFFUCxpQ0FBaUIsQ0FBQ0QsSUFBSSxFQUFFLENBQUMsQ0FBQyxDQUFDO01BQ3RFUyxNQUFNLENBQUNrRCxlQUFlLENBQUMvQyxLQUFLLENBQUMsQ0FBQ3lCLFNBQVMsQ0FBQyxpQkFBaUIsQ0FBQztNQUUxRCxJQUFNdUIsa0JBQWtCLEdBQUcsSUFBQXBELDZCQUFhLEVBQUMsS0FBSyxFQUFFUCxpQ0FBaUIsQ0FBQ0csUUFBUSxFQUFFLENBQUMsQ0FBQyxDQUFDO01BQy9FSyxNQUFNLENBQUNtRCxrQkFBa0IsQ0FBQ2hELEtBQUssQ0FBQyxDQUFDeUIsU0FBUyxDQUFDLHVCQUF1QixDQUFDO0lBQ3JFLENBQUMsQ0FBQztFQUNKLENBQUMsQ0FBQztBQUNKLENBQUMsQ0FBQyIsImlnbm9yZUxpc3QiOltdfQ==