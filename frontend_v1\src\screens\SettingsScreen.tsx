/**
 * Settings Screen - Comprehensive User Settings Management
 *
 * Component Contract:
 * - Provides comprehensive settings management interface
 * - Integrates with backend profile and notification services
 * - Handles privacy settings, notifications, and account preferences
 * - Supports theme switching and accessibility options
 * - Follows responsive design and accessibility guidelines
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Alert, Switch } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { SafeAreaScreen } from '../components/templates/SafeAreaScreen';
import { Card } from '../components/molecules/Card';
import { AccessibleTouchable } from '../components/atoms/AccessibleTouchable';
import { useTheme } from '../contexts/ThemeContext';
import { useAuthStore } from '../store/authSlice';
import { getResponsiveSpacing, getResponsiveFontSize } from '../utils/responsiveUtils';
import { profileService } from '../services/profileService';

type SettingsScreenNavigationProp = StackNavigationProp<any, 'Settings'>;

interface NotificationPreferences {
  email_notifications: boolean;
  sms_notifications: boolean;
  push_notifications: boolean;
  booking_reminders: boolean;
  marketing_emails: boolean;
}

interface PrivacySettings {
  show_phone_publicly: boolean;
  show_email_publicly: boolean;
  allow_reviews: boolean;
  search_radius: number;
}

export const SettingsScreen: React.FC = () => {
  const { colors, isDark, setTheme } = useTheme();
  const navigation = useNavigation<SettingsScreenNavigationProp>();
  const { user, userRole, logout } = useAuthStore();
  const styles = createStyles(colors);
  
  const [notificationPrefs, setNotificationPrefs] = useState<NotificationPreferences>({
    email_notifications: true,
    sms_notifications: false,
    push_notifications: true,
    booking_reminders: true,
    marketing_emails: false,
  });
  
  const [privacySettings, setPrivacySettings] = useState<PrivacySettings>({
    show_phone_publicly: false,
    show_email_publicly: false,
    allow_reviews: true,
    search_radius: 25,
  });
  
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      setLoading(true);
      
      // Load notification preferences
      const notificationPreferences = await profileService.getNotificationPreferences();
      setNotificationPrefs(notificationPreferences);
      
      // Load profile details for privacy settings
      const profileDetails = await profileService.getProfileDetails();
      setPrivacySettings({
        show_phone_publicly: profileDetails.show_phone_publicly || false,
        show_email_publicly: profileDetails.show_email_publicly || false,
        allow_reviews: profileDetails.allow_reviews !== false,
        search_radius: profileDetails.search_radius || 25,
      });
      
    } catch (error) {
      console.error('Failed to load settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateNotificationPreference = async (key: keyof NotificationPreferences, value: boolean) => {
    try {
      const updatedPrefs = { ...notificationPrefs, [key]: value };
      setNotificationPrefs(updatedPrefs);
      
      await profileService.updateNotificationPreferences({ [key]: value });
    } catch (error) {
      console.error('Failed to update notification preference:', error);
      // Revert the change
      setNotificationPrefs(notificationPrefs);
      Alert.alert('Error', 'Failed to update notification settings');
    }
  };

  const updatePrivacySetting = async (key: keyof PrivacySettings, value: boolean | number) => {
    try {
      const updatedSettings = { ...privacySettings, [key]: value };
      setPrivacySettings(updatedSettings);
      
      await profileService.updateProfileDetails({ [key]: value });
    } catch (error) {
      console.error('Failed to update privacy setting:', error);
      // Revert the change
      setPrivacySettings(privacySettings);
      Alert.alert('Error', 'Failed to update privacy settings');
    }
  };

  const handleThemeToggle = () => {
    setTheme(isDark ? 'light' : 'dark');
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: () => logout(),
        },
      ]
    );
  };

  const handleDeleteAccount = () => {
    Alert.alert(
      'Delete Account',
      'This action cannot be undone. All your data will be permanently deleted.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            // Navigate to account deletion confirmation screen
            navigation.navigate('DeleteAccount');
          },
        },
      ]
    );
  };

  const renderSettingItem = (
    title: string,
    subtitle: string,
    icon: string,
    onPress?: () => void,
    rightElement?: React.ReactNode,
    testID?: string
  ) => (
    <AccessibleTouchable
      style={styles.settingItem}
      onPress={onPress}
      disabled={!onPress}
      accessibilityLabel={title}
      accessibilityHint={subtitle}
      testID={testID}>
      <View style={styles.settingLeft}>
        <View style={styles.settingIcon}>
          <Ionicons name={icon as any} size={20} color={colors.sage400} />
        </View>
        <View style={styles.settingContent}>
          <Text style={styles.settingTitle}>{title}</Text>
          <Text style={styles.settingSubtitle}>{subtitle}</Text>
        </View>
      </View>
      {rightElement || (
        <Ionicons name="chevron-forward" size={20} color={colors.text.tertiary} />
      )}
    </AccessibleTouchable>
  );

  const renderSwitchItem = (
    title: string,
    subtitle: string,
    icon: string,
    value: boolean,
    onValueChange: (value: boolean) => void,
    testID?: string
  ) => (
    <View style={styles.settingItem}>
      <View style={styles.settingLeft}>
        <View style={styles.settingIcon}>
          <Ionicons name={icon as any} size={20} color={colors.sage400} />
        </View>
        <View style={styles.settingContent}>
          <Text style={styles.settingTitle}>{title}</Text>
          <Text style={styles.settingSubtitle}>{subtitle}</Text>
        </View>
      </View>
      <Switch
        value={value}
        onValueChange={onValueChange}
        trackColor={{ false: colors.background.tertiary, true: colors.sage400 }}
        thumbColor={value ? colors.sage500 : colors.text.tertiary}
        testID={testID}
      />
    </View>
  );

  return (
    <SafeAreaScreen
      backgroundColor={colors.background.primary}
      statusBarStyle={isDark ? 'light-content' : 'dark-content'}
      testID="settings-screen">
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={styles.backButton}
          testID="back-button">
          <Ionicons name="arrow-back" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Settings</Text>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}>
        
        {/* Account Section */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Account</Text>
          {renderSettingItem(
            'Edit Profile',
            'Update your personal information',
            'person-outline',
            () => navigation.navigate('EditProfile'),
            undefined,
            'edit-profile-button'
          )}
          {renderSettingItem(
            'Change Password',
            'Update your account password',
            'lock-closed-outline',
            () => navigation.navigate('ChangePassword'),
            undefined,
            'change-password-button'
          )}
          {userRole === 'provider' && renderSettingItem(
            'Business Settings',
            'Manage your business profile',
            'business-outline',
            () => navigation.navigate('BusinessSettings'),
            undefined,
            'business-settings-button'
          )}
        </Card>

        {/* Notifications Section */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Notifications</Text>
          {renderSwitchItem(
            'Push Notifications',
            'Receive notifications on your device',
            'notifications-outline',
            notificationPrefs.push_notifications,
            (value) => updateNotificationPreference('push_notifications', value),
            'push-notifications-switch'
          )}
          {renderSwitchItem(
            'Email Notifications',
            'Receive updates via email',
            'mail-outline',
            notificationPrefs.email_notifications,
            (value) => updateNotificationPreference('email_notifications', value),
            'email-notifications-switch'
          )}
          {renderSwitchItem(
            'SMS Notifications',
            'Receive text message updates',
            'chatbubble-outline',
            notificationPrefs.sms_notifications,
            (value) => updateNotificationPreference('sms_notifications', value),
            'sms-notifications-switch'
          )}
          {renderSwitchItem(
            'Booking Reminders',
            'Get reminded about upcoming bookings',
            'time-outline',
            notificationPrefs.booking_reminders,
            (value) => updateNotificationPreference('booking_reminders', value),
            'booking-reminders-switch'
          )}
        </Card>

        {/* Privacy Section */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Privacy</Text>
          {renderSwitchItem(
            'Show Phone Publicly',
            'Display your phone number on your profile',
            'call-outline',
            privacySettings.show_phone_publicly,
            (value) => updatePrivacySetting('show_phone_publicly', value),
            'show-phone-switch'
          )}
          {renderSwitchItem(
            'Show Email Publicly',
            'Display your email on your profile',
            'mail-outline',
            privacySettings.show_email_publicly,
            (value) => updatePrivacySetting('show_email_publicly', value),
            'show-email-switch'
          )}
          {renderSwitchItem(
            'Allow Reviews',
            'Let customers leave reviews',
            'star-outline',
            privacySettings.allow_reviews,
            (value) => updatePrivacySetting('allow_reviews', value),
            'allow-reviews-switch'
          )}
        </Card>

        {/* App Preferences Section */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>App Preferences</Text>
          {renderSwitchItem(
            'Dark Mode',
            'Use dark theme throughout the app',
            'moon-outline',
            isDark,
            handleThemeToggle,
            'dark-mode-switch'
          )}
          {renderSettingItem(
            'Language',
            'English (US)',
            'language-outline',
            () => navigation.navigate('LanguageSettings'),
            undefined,
            'language-button'
          )}
        </Card>

        {/* Support Section */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Support</Text>
          {renderSettingItem(
            'Help Center',
            'Get help and support',
            'help-circle-outline',
            () => navigation.navigate('HelpCenter'),
            undefined,
            'help-center-button'
          )}
          {renderSettingItem(
            'Contact Us',
            'Send feedback or report issues',
            'mail-outline',
            () => navigation.navigate('ContactUs'),
            undefined,
            'contact-us-button'
          )}
          {renderSettingItem(
            'Privacy Policy',
            'Read our privacy policy',
            'document-text-outline',
            () => navigation.navigate('PrivacyPolicy'),
            undefined,
            'privacy-policy-button'
          )}
          {renderSettingItem(
            'Terms of Service',
            'Read our terms of service',
            'document-outline',
            () => navigation.navigate('TermsOfService'),
            undefined,
            'terms-button'
          )}
        </Card>

        {/* Account Actions Section */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Account Actions</Text>
          {renderSettingItem(
            'Logout',
            'Sign out of your account',
            'log-out-outline',
            handleLogout,
            undefined,
            'logout-button'
          )}
          {renderSettingItem(
            'Delete Account',
            'Permanently delete your account',
            'trash-outline',
            handleDeleteAccount,
            undefined,
            'delete-account-button'
          )}
        </Card>
      </ScrollView>
    </SafeAreaScreen>
  );
};

const createStyles = (colors: any) => ({
  header: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    paddingHorizontal: getResponsiveSpacing(16),
    paddingVertical: getResponsiveSpacing(12),
    borderBottomWidth: 1,
    borderBottomColor: colors.border.primary,
  },
  backButton: {
    padding: getResponsiveSpacing(8),
    marginLeft: -getResponsiveSpacing(8),
  },
  headerTitle: {
    fontSize: getResponsiveFontSize(20),
    fontWeight: '600',
    color: colors.text.primary,
    flex: 1,
    textAlign: 'center' as const,
  },
  headerSpacer: {
    width: 40,
  },
  container: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: getResponsiveSpacing(16),
    paddingVertical: getResponsiveSpacing(16),
  },
  section: {
    marginBottom: getResponsiveSpacing(16),
    padding: getResponsiveSpacing(16),
  },
  sectionTitle: {
    fontSize: getResponsiveFontSize(18),
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: getResponsiveSpacing(16),
  },
  settingItem: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    paddingVertical: getResponsiveSpacing(12),
    borderBottomWidth: 1,
    borderBottomColor: colors.border.secondary,
  },
  settingLeft: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    flex: 1,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.background.secondary,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    marginRight: getResponsiveSpacing(12),
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '500',
    color: colors.text.primary,
    marginBottom: getResponsiveSpacing(2),
  },
  settingSubtitle: {
    fontSize: getResponsiveFontSize(14),
    color: colors.text.secondary,
  },
});
