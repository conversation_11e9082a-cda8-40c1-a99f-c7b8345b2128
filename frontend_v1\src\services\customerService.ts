/**
 * Customer Service - Backend Integration for Customer Features
 *
 * Service Contract:
 * - Handles all customer-related API calls
 * - Provides home screen data (categories, featured providers, recommendations)
 * - Manages customer profile and preferences
 * - Implements proper error handling and caching
 * - Supports offline functionality with graceful degradation
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { apiClient } from './apiClient';
import type { ApiResponse } from './apiClient';

// Types for Customer Service
export interface ServiceCategory {
  id: string;
  name: string;
  slug: string;
  description: string;
  icon: string;
  color: string;
  serviceCount: number;
  isActive: boolean;
  displayOrder: number;
}

export interface FeaturedProvider {
  id: string;
  name: string;
  businessName: string;
  description: string;
  avatar: string | null;
  coverImage: string | null;
  rating: number;
  reviewCount: number;
  isVerified: boolean;
  isOnline: boolean;
  categories: string[];
  location: {
    address: string;
    city: string;
    distance?: number;
  };
  services: {
    id: string;
    name: string;
    price: number;
    duration: number;
  }[];
  nextAvailableSlot?: string;
}

export interface NearbyProvider extends FeaturedProvider {
  distance: number;
  estimatedTravelTime: number;
}

export interface CustomerDashboard {
  greeting: string;
  upcomingBookings: number;
  favoriteProviders: number;
  recentActivity: {
    type: 'booking' | 'review' | 'favorite';
    message: string;
    timestamp: string;
  }[];
  recommendations: {
    type: 'service' | 'provider';
    title: string;
    subtitle: string;
    imageUrl: string;
    actionUrl: string;
  }[];
}

export interface CustomerProfile {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string | null;
  avatar: string | null;
  dateOfBirth: string | null;
  location: {
    address: string;
    city: string;
    coordinates: {
      latitude: number;
      longitude: number;
    };
  } | null;
  preferences: {
    favoriteCategories: string[];
    maxTravelDistance: number;
    preferredPaymentMethod: string;
    notifications: {
      push: boolean;
      email: boolean;
      sms: boolean;
    };
  };
  stats: {
    totalBookings: number;
    totalSpent: number;
    memberSince: string;
  };
}

export interface QuickBooking {
  providerId: string;
  serviceId: string;
  timeSlot: string;
  notes?: string;
}

class CustomerService {
  /**
   * Get service categories for home screen
   */
  async getServiceCategories(): Promise<ServiceCategory[]> {
    try {
      const response = await apiClient.get<ServiceCategory[]>('/api/catalog/categories/');
      return response.data;
    } catch (error: any) {
      console.error('Failed to fetch service categories:', error);
      // Return fallback data for offline support
      return this.getFallbackCategories();
    }
  }

  /**
   * Get featured providers for home screen
   */
  async getFeaturedProviders(limit: number = 10): Promise<FeaturedProvider[]> {
    try {
      const response = await apiClient.get<FeaturedProvider[]>(
        '/api/catalog/providers/featured/',
        { limit }
      );
      return response.data;
    } catch (error: any) {
      console.error('Failed to fetch featured providers:', error);
      return [];
    }
  }

  /**
   * Get nearby providers based on user location
   */
  async getNearbyProviders(
    latitude: number,
    longitude: number,
    radius: number = 10,
    limit: number = 10
  ): Promise<NearbyProvider[]> {
    try {
      const response = await apiClient.get<{ results: NearbyProvider[] }>(
        '/api/v1/customer/nearby/providers/',
        {
          lat: latitude,
          lng: longitude,
          radius,
          limit,
        }
      );
      return response.data.results;
    } catch (error: any) {
      console.error('Failed to fetch nearby providers:', error);
      return [];
    }
  }

  /**
   * Get customer dashboard data
   */
  async getCustomerDashboard(): Promise<CustomerDashboard> {
    try {
      const response = await apiClient.get<CustomerDashboard>('/api/v1/customer/dashboard/');
      return response.data;
    } catch (error: any) {
      console.error('Failed to fetch customer dashboard:', error);
      return this.getFallbackDashboard();
    }
  }

  /**
   * Get customer profile
   */
  async getCustomerProfile(): Promise<CustomerProfile> {
    try {
      const response = await apiClient.get<CustomerProfile>('/api/v1/customer/profile/');
      return response.data;
    } catch (error: any) {
      console.error('Failed to fetch customer profile:', error);
      throw error;
    }
  }

  /**
   * Get personalized recommendations
   */
  async getPersonalizedRecommendations(): Promise<FeaturedProvider[]> {
    try {
      const response = await apiClient.get<{ results: FeaturedProvider[] }>(
        '/api/v1/customer/recommendations/personalized/'
      );
      return response.data.results;
    } catch (error: any) {
      console.error('Failed to fetch personalized recommendations:', error);
      return [];
    }
  }

  /**
   * Get customer's favorite providers
   */
  async getFavoriteProviders(): Promise<FeaturedProvider[]> {
    try {
      const response = await apiClient.get<{ results: FeaturedProvider[] }>(
        '/api/v1/customer/favorites/'
      );
      return response.data.results;
    } catch (error: any) {
      console.error('Failed to fetch favorite providers:', error);
      return [];
    }
  }

  /**
   * Quick booking functionality
   */
  async createQuickBooking(bookingData: QuickBooking): Promise<{ bookingId: string; status: string }> {
    try {
      const response = await apiClient.post<{ bookingId: string; status: string }>(
        '/api/v1/customer/bookings/quick-book/',
        bookingData
      );
      return response.data;
    } catch (error: any) {
      console.error('Failed to create quick booking:', error);
      throw error;
    }
  }

  /**
   * Fallback categories for offline support
   */
  private getFallbackCategories(): ServiceCategory[] {
    return [
      {
        id: '1',
        name: 'Barber',
        slug: 'barber',
        description: 'Professional barber services',
        icon: 'cut-outline',
        color: '#5A7A63',
        serviceCount: 12,
        isActive: true,
        displayOrder: 1,
      },
      {
        id: '2',
        name: 'Salon',
        slug: 'salon',
        description: 'Hair salon services',
        icon: 'brush-outline',
        color: '#6B8A74',
        serviceCount: 8,
        isActive: true,
        displayOrder: 2,
      },
      {
        id: '3',
        name: 'Nail Services',
        slug: 'nail-services',
        description: 'Professional nail care',
        icon: 'hand-left-outline',
        color: '#5A7A63',
        serviceCount: 15,
        isActive: true,
        displayOrder: 3,
      },
      {
        id: '4',
        name: 'Lash Services',
        slug: 'lash-services',
        description: 'Eyelash extensions and care',
        icon: 'eye-outline',
        color: '#4A6B52',
        serviceCount: 6,
        isActive: true,
        displayOrder: 4,
      },
      {
        id: '5',
        name: 'Braiding',
        slug: 'braiding',
        description: 'Hair braiding services',
        icon: 'flower-outline',
        color: '#3A5B42',
        serviceCount: 10,
        isActive: true,
        displayOrder: 5,
      },
      {
        id: '6',
        name: 'Skincare',
        slug: 'skincare',
        description: 'Facial and skincare treatments',
        icon: 'heart-outline',
        color: '#6B8A74',
        serviceCount: 7,
        isActive: true,
        displayOrder: 6,
      },
      {
        id: '7',
        name: 'Massage',
        slug: 'massage',
        description: 'Therapeutic massage services',
        icon: 'hand-right-outline',
        color: '#5A7A63',
        serviceCount: 8,
        isActive: true,
        displayOrder: 7,
      },
    ];
  }

  /**
   * Fallback dashboard data for offline support
   */
  private getFallbackDashboard(): CustomerDashboard {
    return {
      greeting: 'Good morning',
      upcomingBookings: 0,
      favoriteProviders: 0,
      recentActivity: [],
      recommendations: [],
    };
  }
}

// Export singleton instance
const customerService = new CustomerService();
export default customerService;
