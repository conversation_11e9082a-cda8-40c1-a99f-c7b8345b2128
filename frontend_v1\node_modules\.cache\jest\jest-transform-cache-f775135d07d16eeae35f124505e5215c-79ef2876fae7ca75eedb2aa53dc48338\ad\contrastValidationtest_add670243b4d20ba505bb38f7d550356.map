{"version": 3, "names": ["_contrastValidator", "require", "_Colors", "_interopRequireDefault", "describe", "it", "backgroundColors", "Colors", "interactive", "primary", "default", "hover", "pressed", "textColor", "text", "for<PERSON>ach", "backgroundColor", "index", "stateName", "validation", "validateC<PERSON><PERSON>t", "expect", "<PERSON><PERSON><PERSON><PERSON>", "toBe", "ratio", "toBeGreaterThanOrEqual", "CONTRAST_STANDARDS", "AA_NORMAL", "console", "log", "toFixed", "textDisabled", "disabled", "toBeGreaterThan", "secondary", "border", "NON_TEXT", "destructive", "ghost", "focusColors", "focus", "ring", "outline", "sage", "focusColor", "colorName", "combinations", "name", "foreground", "background", "expected", "results", "_ref", "push", "passCount", "filter", "r", "length", "totalCount", "_ref2", "AA_LARGE"], "sources": ["contrastValidation.test.ts"], "sourcesContent": ["/**\n * Button Contrast Validation Tests\n * \n * Tests to ensure all button variants meet WCAG 2.1 AA contrast standards.\n * Validates REC-ACC-001 implementation.\n */\n\nimport { validateContrast, getContrastRatio, CONTRAST_STANDARDS } from '../contrastValidator';\nimport Colors from '../../constants/Colors';\n\ndescribe('Button Contrast Validation - WCAG 2.1 AA Compliance', () => {\n  describe('Primary Button Contrast', () => {\n    it('should meet WCAG AA standards for primary button text', () => {\n      const backgroundColors = [\n        Colors.interactive.primary.default,\n        Colors.interactive.primary.hover,\n        Colors.interactive.primary.pressed,\n      ];\n      \n      const textColor = Colors.interactive.primary.text;\n      \n      backgroundColors.forEach((backgroundColor, index) => {\n        const stateName = ['default', 'hover', 'pressed'][index];\n        const validation = validateContrast(textColor, backgroundColor);\n        \n        expect(validation.isValid).toBe(true);\n        expect(validation.ratio).toBeGreaterThanOrEqual(CONTRAST_STANDARDS.AA_NORMAL);\n        \n        console.log(`Primary ${stateName}: ${validation.ratio.toFixed(2)}:1 contrast`);\n      });\n    });\n    \n    it('should have appropriate disabled state contrast', () => {\n      const validation = validateContrast(\n        Colors.interactive.primary.textDisabled,\n        Colors.interactive.primary.disabled\n      );\n      \n      // Disabled states should be visually distinct but don't need to meet full contrast\n      expect(validation.ratio).toBeGreaterThan(1.5);\n      console.log(`Primary disabled: ${validation.ratio.toFixed(2)}:1 contrast`);\n    });\n  });\n  \n  describe('Secondary Button Contrast', () => {\n    it('should meet WCAG AA standards for secondary button text on white background', () => {\n      const validation = validateContrast(\n        Colors.interactive.secondary.text,\n        '#FFFFFF' // Assuming white background\n      );\n      \n      expect(validation.isValid).toBe(true);\n      expect(validation.ratio).toBeGreaterThanOrEqual(CONTRAST_STANDARDS.AA_NORMAL);\n      \n      console.log(`Secondary text: ${validation.ratio.toFixed(2)}:1 contrast`);\n    });\n    \n    it('should meet WCAG AA standards for secondary button border', () => {\n      const validation = validateContrast(\n        Colors.interactive.secondary.border,\n        '#FFFFFF' // Assuming white background\n      );\n      \n      expect(validation.isValid).toBe(true);\n      expect(validation.ratio).toBeGreaterThanOrEqual(CONTRAST_STANDARDS.NON_TEXT);\n      \n      console.log(`Secondary border: ${validation.ratio.toFixed(2)}:1 contrast`);\n    });\n  });\n  \n  describe('Destructive Button Contrast', () => {\n    it('should meet WCAG AA standards for destructive button text', () => {\n      const backgroundColors = [\n        Colors.interactive.destructive.default,\n        Colors.interactive.destructive.hover,\n        Colors.interactive.destructive.pressed,\n      ];\n      \n      const textColor = Colors.interactive.destructive.text;\n      \n      backgroundColors.forEach((backgroundColor, index) => {\n        const stateName = ['default', 'hover', 'pressed'][index];\n        const validation = validateContrast(textColor, backgroundColor);\n        \n        expect(validation.isValid).toBe(true);\n        expect(validation.ratio).toBeGreaterThanOrEqual(CONTRAST_STANDARDS.AA_NORMAL);\n        \n        console.log(`Destructive ${stateName}: ${validation.ratio.toFixed(2)}:1 contrast`);\n      });\n    });\n  });\n  \n  describe('Ghost Button Contrast', () => {\n    it('should meet WCAG AA standards for ghost button text on white background', () => {\n      const validation = validateContrast(\n        Colors.interactive.ghost.text,\n        '#FFFFFF' // Assuming white background\n      );\n      \n      expect(validation.isValid).toBe(true);\n      expect(validation.ratio).toBeGreaterThanOrEqual(CONTRAST_STANDARDS.AA_NORMAL);\n      \n      console.log(`Ghost text: ${validation.ratio.toFixed(2)}:1 contrast`);\n    });\n  });\n  \n  describe('Focus States Contrast', () => {\n    it('should meet WCAG AA standards for focus indicators', () => {\n      const focusColors = [\n        Colors.focus.ring,\n        Colors.focus.outline,\n        Colors.focus.sage,\n      ];\n      \n      focusColors.forEach((focusColor, index) => {\n        const colorName = ['ring', 'outline', 'sage'][index];\n        const validation = validateContrast(focusColor, '#FFFFFF');\n        \n        expect(validation.ratio).toBeGreaterThanOrEqual(CONTRAST_STANDARDS.NON_TEXT);\n        \n        console.log(`Focus ${colorName}: ${validation.ratio.toFixed(2)}:1 contrast`);\n      });\n    });\n  });\n  \n  describe('Comprehensive Color Combinations', () => {\n    it('should validate all interactive color combinations', () => {\n      const combinations = [\n        // Primary button combinations\n        {\n          name: 'Primary Default',\n          foreground: Colors.interactive.primary.text,\n          background: Colors.interactive.primary.default,\n          expected: 'pass',\n        },\n        {\n          name: 'Primary Hover',\n          foreground: Colors.interactive.primary.text,\n          background: Colors.interactive.primary.hover,\n          expected: 'pass',\n        },\n        {\n          name: 'Primary Pressed',\n          foreground: Colors.interactive.primary.text,\n          background: Colors.interactive.primary.pressed,\n          expected: 'pass',\n        },\n        // Secondary button combinations\n        {\n          name: 'Secondary Text',\n          foreground: Colors.interactive.secondary.text,\n          background: '#FFFFFF',\n          expected: 'pass',\n        },\n        {\n          name: 'Secondary Border',\n          foreground: Colors.interactive.secondary.border,\n          background: '#FFFFFF',\n          expected: 'pass',\n        },\n        // Destructive button combinations\n        {\n          name: 'Destructive Default',\n          foreground: Colors.interactive.destructive.text,\n          background: Colors.interactive.destructive.default,\n          expected: 'pass',\n        },\n        // Ghost button combinations\n        {\n          name: 'Ghost Text',\n          foreground: Colors.interactive.ghost.text,\n          background: '#FFFFFF',\n          expected: 'pass',\n        },\n      ];\n      \n      const results: Array<{ name: string; ratio: number; isValid: boolean }> = [];\n      \n      combinations.forEach(({ name, foreground, background, expected }) => {\n        const validation = validateContrast(foreground, background);\n        results.push({\n          name,\n          ratio: validation.ratio,\n          isValid: validation.isValid,\n        });\n        \n        if (expected === 'pass') {\n          expect(validation.isValid).toBe(true);\n        }\n        \n        console.log(`${name}: ${validation.ratio.toFixed(2)}:1 - ${validation.isValid ? 'PASS' : 'FAIL'}`);\n      });\n      \n      // Log summary\n      const passCount = results.filter(r => r.isValid).length;\n      const totalCount = results.length;\n      console.log(`\\nContrast Validation Summary: ${passCount}/${totalCount} combinations passed`);\n      \n      expect(passCount).toBe(totalCount);\n    });\n  });\n  \n  describe('Large Text Contrast', () => {\n    it('should meet WCAG AA standards for large text (18pt+)', () => {\n      const combinations = [\n        {\n          name: 'Primary Large Text',\n          foreground: Colors.interactive.primary.text,\n          background: Colors.interactive.primary.default,\n        },\n        {\n          name: 'Secondary Large Text',\n          foreground: Colors.interactive.secondary.text,\n          background: '#FFFFFF',\n        },\n      ];\n      \n      combinations.forEach(({ name, foreground, background }) => {\n        const validation = validateContrast(foreground, background, true); // isLargeText = true\n        \n        expect(validation.isValid).toBe(true);\n        expect(validation.ratio).toBeGreaterThanOrEqual(CONTRAST_STANDARDS.AA_LARGE);\n        \n        console.log(`${name} (Large): ${validation.ratio.toFixed(2)}:1 contrast`);\n      });\n    });\n  });\n});\n"], "mappings": ";AAOA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAC,sBAAA,CAAAF,OAAA;AAEAG,QAAQ,CAAC,qDAAqD,EAAE,YAAM;EACpEA,QAAQ,CAAC,yBAAyB,EAAE,YAAM;IACxCC,EAAE,CAAC,uDAAuD,EAAE,YAAM;MAChE,IAAMC,gBAAgB,GAAG,CACvBC,eAAM,CAACC,WAAW,CAACC,OAAO,CAACC,OAAO,EAClCH,eAAM,CAACC,WAAW,CAACC,OAAO,CAACE,KAAK,EAChCJ,eAAM,CAACC,WAAW,CAACC,OAAO,CAACG,OAAO,CACnC;MAED,IAAMC,SAAS,GAAGN,eAAM,CAACC,WAAW,CAACC,OAAO,CAACK,IAAI;MAEjDR,gBAAgB,CAACS,OAAO,CAAC,UAACC,eAAe,EAAEC,KAAK,EAAK;QACnD,IAAMC,SAAS,GAAG,CAAC,SAAS,EAAE,OAAO,EAAE,SAAS,CAAC,CAACD,KAAK,CAAC;QACxD,IAAME,UAAU,GAAG,IAAAC,mCAAgB,EAACP,SAAS,EAAEG,eAAe,CAAC;QAE/DK,MAAM,CAACF,UAAU,CAACG,OAAO,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;QACrCF,MAAM,CAACF,UAAU,CAACK,KAAK,CAAC,CAACC,sBAAsB,CAACC,qCAAkB,CAACC,SAAS,CAAC;QAE7EC,OAAO,CAACC,GAAG,CAAC,WAAWX,SAAS,KAAKC,UAAU,CAACK,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC;MAChF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFzB,EAAE,CAAC,iDAAiD,EAAE,YAAM;MAC1D,IAAMc,UAAU,GAAG,IAAAC,mCAAgB,EACjCb,eAAM,CAACC,WAAW,CAACC,OAAO,CAACsB,YAAY,EACvCxB,eAAM,CAACC,WAAW,CAACC,OAAO,CAACuB,QAC7B,CAAC;MAGDX,MAAM,CAACF,UAAU,CAACK,KAAK,CAAC,CAACS,eAAe,CAAC,GAAG,CAAC;MAC7CL,OAAO,CAACC,GAAG,CAAC,qBAAqBV,UAAU,CAACK,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC;IAC5E,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF1B,QAAQ,CAAC,2BAA2B,EAAE,YAAM;IAC1CC,EAAE,CAAC,6EAA6E,EAAE,YAAM;MACtF,IAAMc,UAAU,GAAG,IAAAC,mCAAgB,EACjCb,eAAM,CAACC,WAAW,CAAC0B,SAAS,CAACpB,IAAI,EACjC,SACF,CAAC;MAEDO,MAAM,CAACF,UAAU,CAACG,OAAO,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;MACrCF,MAAM,CAACF,UAAU,CAACK,KAAK,CAAC,CAACC,sBAAsB,CAACC,qCAAkB,CAACC,SAAS,CAAC;MAE7EC,OAAO,CAACC,GAAG,CAAC,mBAAmBV,UAAU,CAACK,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC;IAC1E,CAAC,CAAC;IAEFzB,EAAE,CAAC,2DAA2D,EAAE,YAAM;MACpE,IAAMc,UAAU,GAAG,IAAAC,mCAAgB,EACjCb,eAAM,CAACC,WAAW,CAAC0B,SAAS,CAACC,MAAM,EACnC,SACF,CAAC;MAEDd,MAAM,CAACF,UAAU,CAACG,OAAO,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;MACrCF,MAAM,CAACF,UAAU,CAACK,KAAK,CAAC,CAACC,sBAAsB,CAACC,qCAAkB,CAACU,QAAQ,CAAC;MAE5ER,OAAO,CAACC,GAAG,CAAC,qBAAqBV,UAAU,CAACK,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC;IAC5E,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF1B,QAAQ,CAAC,6BAA6B,EAAE,YAAM;IAC5CC,EAAE,CAAC,2DAA2D,EAAE,YAAM;MACpE,IAAMC,gBAAgB,GAAG,CACvBC,eAAM,CAACC,WAAW,CAAC6B,WAAW,CAAC3B,OAAO,EACtCH,eAAM,CAACC,WAAW,CAAC6B,WAAW,CAAC1B,KAAK,EACpCJ,eAAM,CAACC,WAAW,CAAC6B,WAAW,CAACzB,OAAO,CACvC;MAED,IAAMC,SAAS,GAAGN,eAAM,CAACC,WAAW,CAAC6B,WAAW,CAACvB,IAAI;MAErDR,gBAAgB,CAACS,OAAO,CAAC,UAACC,eAAe,EAAEC,KAAK,EAAK;QACnD,IAAMC,SAAS,GAAG,CAAC,SAAS,EAAE,OAAO,EAAE,SAAS,CAAC,CAACD,KAAK,CAAC;QACxD,IAAME,UAAU,GAAG,IAAAC,mCAAgB,EAACP,SAAS,EAAEG,eAAe,CAAC;QAE/DK,MAAM,CAACF,UAAU,CAACG,OAAO,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;QACrCF,MAAM,CAACF,UAAU,CAACK,KAAK,CAAC,CAACC,sBAAsB,CAACC,qCAAkB,CAACC,SAAS,CAAC;QAE7EC,OAAO,CAACC,GAAG,CAAC,eAAeX,SAAS,KAAKC,UAAU,CAACK,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC;MACpF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF1B,QAAQ,CAAC,uBAAuB,EAAE,YAAM;IACtCC,EAAE,CAAC,yEAAyE,EAAE,YAAM;MAClF,IAAMc,UAAU,GAAG,IAAAC,mCAAgB,EACjCb,eAAM,CAACC,WAAW,CAAC8B,KAAK,CAACxB,IAAI,EAC7B,SACF,CAAC;MAEDO,MAAM,CAACF,UAAU,CAACG,OAAO,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;MACrCF,MAAM,CAACF,UAAU,CAACK,KAAK,CAAC,CAACC,sBAAsB,CAACC,qCAAkB,CAACC,SAAS,CAAC;MAE7EC,OAAO,CAACC,GAAG,CAAC,eAAeV,UAAU,CAACK,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC;IACtE,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF1B,QAAQ,CAAC,uBAAuB,EAAE,YAAM;IACtCC,EAAE,CAAC,oDAAoD,EAAE,YAAM;MAC7D,IAAMkC,WAAW,GAAG,CAClBhC,eAAM,CAACiC,KAAK,CAACC,IAAI,EACjBlC,eAAM,CAACiC,KAAK,CAACE,OAAO,EACpBnC,eAAM,CAACiC,KAAK,CAACG,IAAI,CAClB;MAEDJ,WAAW,CAACxB,OAAO,CAAC,UAAC6B,UAAU,EAAE3B,KAAK,EAAK;QACzC,IAAM4B,SAAS,GAAG,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC5B,KAAK,CAAC;QACpD,IAAME,UAAU,GAAG,IAAAC,mCAAgB,EAACwB,UAAU,EAAE,SAAS,CAAC;QAE1DvB,MAAM,CAACF,UAAU,CAACK,KAAK,CAAC,CAACC,sBAAsB,CAACC,qCAAkB,CAACU,QAAQ,CAAC;QAE5ER,OAAO,CAACC,GAAG,CAAC,SAASgB,SAAS,KAAK1B,UAAU,CAACK,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC;MAC9E,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF1B,QAAQ,CAAC,kCAAkC,EAAE,YAAM;IACjDC,EAAE,CAAC,oDAAoD,EAAE,YAAM;MAC7D,IAAMyC,YAAY,GAAG,CAEnB;QACEC,IAAI,EAAE,iBAAiB;QACvBC,UAAU,EAAEzC,eAAM,CAACC,WAAW,CAACC,OAAO,CAACK,IAAI;QAC3CmC,UAAU,EAAE1C,eAAM,CAACC,WAAW,CAACC,OAAO,CAACC,OAAO;QAC9CwC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEH,IAAI,EAAE,eAAe;QACrBC,UAAU,EAAEzC,eAAM,CAACC,WAAW,CAACC,OAAO,CAACK,IAAI;QAC3CmC,UAAU,EAAE1C,eAAM,CAACC,WAAW,CAACC,OAAO,CAACE,KAAK;QAC5CuC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEH,IAAI,EAAE,iBAAiB;QACvBC,UAAU,EAAEzC,eAAM,CAACC,WAAW,CAACC,OAAO,CAACK,IAAI;QAC3CmC,UAAU,EAAE1C,eAAM,CAACC,WAAW,CAACC,OAAO,CAACG,OAAO;QAC9CsC,QAAQ,EAAE;MACZ,CAAC,EAED;QACEH,IAAI,EAAE,gBAAgB;QACtBC,UAAU,EAAEzC,eAAM,CAACC,WAAW,CAAC0B,SAAS,CAACpB,IAAI;QAC7CmC,UAAU,EAAE,SAAS;QACrBC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEH,IAAI,EAAE,kBAAkB;QACxBC,UAAU,EAAEzC,eAAM,CAACC,WAAW,CAAC0B,SAAS,CAACC,MAAM;QAC/Cc,UAAU,EAAE,SAAS;QACrBC,QAAQ,EAAE;MACZ,CAAC,EAED;QACEH,IAAI,EAAE,qBAAqB;QAC3BC,UAAU,EAAEzC,eAAM,CAACC,WAAW,CAAC6B,WAAW,CAACvB,IAAI;QAC/CmC,UAAU,EAAE1C,eAAM,CAACC,WAAW,CAAC6B,WAAW,CAAC3B,OAAO;QAClDwC,QAAQ,EAAE;MACZ,CAAC,EAED;QACEH,IAAI,EAAE,YAAY;QAClBC,UAAU,EAAEzC,eAAM,CAACC,WAAW,CAAC8B,KAAK,CAACxB,IAAI;QACzCmC,UAAU,EAAE,SAAS;QACrBC,QAAQ,EAAE;MACZ,CAAC,CACF;MAED,IAAMC,OAAiE,GAAG,EAAE;MAE5EL,YAAY,CAAC/B,OAAO,CAAC,UAAAqC,IAAA,EAAgD;QAAA,IAA7CL,IAAI,GAAAK,IAAA,CAAJL,IAAI;UAAEC,UAAU,GAAAI,IAAA,CAAVJ,UAAU;UAAEC,UAAU,GAAAG,IAAA,CAAVH,UAAU;UAAEC,QAAQ,GAAAE,IAAA,CAARF,QAAQ;QAC5D,IAAM/B,UAAU,GAAG,IAAAC,mCAAgB,EAAC4B,UAAU,EAAEC,UAAU,CAAC;QAC3DE,OAAO,CAACE,IAAI,CAAC;UACXN,IAAI,EAAJA,IAAI;UACJvB,KAAK,EAAEL,UAAU,CAACK,KAAK;UACvBF,OAAO,EAAEH,UAAU,CAACG;QACtB,CAAC,CAAC;QAEF,IAAI4B,QAAQ,KAAK,MAAM,EAAE;UACvB7B,MAAM,CAACF,UAAU,CAACG,OAAO,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;QACvC;QAEAK,OAAO,CAACC,GAAG,CAAC,GAAGkB,IAAI,KAAK5B,UAAU,CAACK,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,QAAQX,UAAU,CAACG,OAAO,GAAG,MAAM,GAAG,MAAM,EAAE,CAAC;MACpG,CAAC,CAAC;MAGF,IAAMgC,SAAS,GAAGH,OAAO,CAACI,MAAM,CAAC,UAAAC,CAAC;QAAA,OAAIA,CAAC,CAAClC,OAAO;MAAA,EAAC,CAACmC,MAAM;MACvD,IAAMC,UAAU,GAAGP,OAAO,CAACM,MAAM;MACjC7B,OAAO,CAACC,GAAG,CAAC,kCAAkCyB,SAAS,IAAII,UAAU,sBAAsB,CAAC;MAE5FrC,MAAM,CAACiC,SAAS,CAAC,CAAC/B,IAAI,CAACmC,UAAU,CAAC;IACpC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFtD,QAAQ,CAAC,qBAAqB,EAAE,YAAM;IACpCC,EAAE,CAAC,sDAAsD,EAAE,YAAM;MAC/D,IAAMyC,YAAY,GAAG,CACnB;QACEC,IAAI,EAAE,oBAAoB;QAC1BC,UAAU,EAAEzC,eAAM,CAACC,WAAW,CAACC,OAAO,CAACK,IAAI;QAC3CmC,UAAU,EAAE1C,eAAM,CAACC,WAAW,CAACC,OAAO,CAACC;MACzC,CAAC,EACD;QACEqC,IAAI,EAAE,sBAAsB;QAC5BC,UAAU,EAAEzC,eAAM,CAACC,WAAW,CAAC0B,SAAS,CAACpB,IAAI;QAC7CmC,UAAU,EAAE;MACd,CAAC,CACF;MAEDH,YAAY,CAAC/B,OAAO,CAAC,UAAA4C,KAAA,EAAsC;QAAA,IAAnCZ,IAAI,GAAAY,KAAA,CAAJZ,IAAI;UAAEC,UAAU,GAAAW,KAAA,CAAVX,UAAU;UAAEC,UAAU,GAAAU,KAAA,CAAVV,UAAU;QAClD,IAAM9B,UAAU,GAAG,IAAAC,mCAAgB,EAAC4B,UAAU,EAAEC,UAAU,EAAE,IAAI,CAAC;QAEjE5B,MAAM,CAACF,UAAU,CAACG,OAAO,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;QACrCF,MAAM,CAACF,UAAU,CAACK,KAAK,CAAC,CAACC,sBAAsB,CAACC,qCAAkB,CAACkC,QAAQ,CAAC;QAE5EhC,OAAO,CAACC,GAAG,CAAC,GAAGkB,IAAI,aAAa5B,UAAU,CAACK,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC;MAC3E,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}