/**
 * useErrorHandling Hook Tests
 *
 * Test Coverage:
 * - Error state management
 * - Retry logic and progressive delays
 * - Error classification and utilities
 * - App state integration
 * - Performance tracking integration
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { renderHook, act } from '@testing-library/react-native';
import { AppState } from 'react-native';
import { useErrorHandling } from '../useErrorHandling';
import { performanceMonitor } from '../../services/performanceMonitor';

// Mock dependencies
jest.mock('../../services/performanceMonitor');
jest.mock('../../utils/errorHandlingUtils', () => ({
  createAppError: jest.fn((error, context) => ({
    ...error,
    id: 'test-error-id',
    context,
    timestamp: Date.now(),
  })),
  logError: jest.fn(),
  AppError: class AppError extends Error {
    constructor(message: string) {
      super(message);
      this.name = 'AppError';
    }
  },
}));

const mockPerformanceMonitor = performanceMonitor as jest.Mocked<typeof performanceMonitor>;

describe('useErrorHandling', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe('Basic Error Handling', () => {
    it('initializes with no error state', () => {
      const { result } = renderHook(() => useErrorHandling());

      expect(result.current.error).toBeNull();
      expect(result.current.isError).toBe(false);
      expect(result.current.retryCount).toBe(0);
      expect(result.current.canRetry).toBe(true);
    });

    it('handles string errors', () => {
      const { result } = renderHook(() => useErrorHandling());

      act(() => {
        result.current.handleError('Test error message');
      });

      expect(result.current.error).toBeDefined();
      expect(result.current.error?.message).toBe('Test error message');
      expect(result.current.isError).toBe(true);
    });

    it('handles Error objects', () => {
      const { result } = renderHook(() => useErrorHandling());
      const testError = new Error('Test error object');

      act(() => {
        result.current.handleError(testError);
      });

      expect(result.current.error).toBeDefined();
      expect(result.current.error?.message).toBe('Test error object');
      expect(result.current.isError).toBe(true);
    });

    it('clears error state', () => {
      const { result } = renderHook(() => useErrorHandling());

      act(() => {
        result.current.handleError('Test error');
      });

      expect(result.current.isError).toBe(true);

      act(() => {
        result.current.clearError();
      });

      expect(result.current.error).toBeNull();
      expect(result.current.isError).toBe(false);
      expect(result.current.retryCount).toBe(0);
    });
  });

  describe('Retry Logic', () => {
    it('increments retry count on retry', async () => {
      const { result } = renderHook(() => useErrorHandling());

      act(() => {
        result.current.handleError('Test error');
      });

      expect(result.current.retryCount).toBe(0);

      await act(async () => {
        await result.current.retry();
        jest.advanceTimersByTime(2000); // Default retry delay
      });

      expect(result.current.retryCount).toBe(1);
    });

    it('respects max retries limit', async () => {
      const { result } = renderHook(() => useErrorHandling({ maxRetries: 2 }));

      act(() => {
        result.current.handleError('Test error');
      });

      // First retry
      await act(async () => {
        await result.current.retry();
        jest.advanceTimersByTime(2000);
      });

      expect(result.current.canRetry).toBe(true);

      // Second retry
      await act(async () => {
        await result.current.retry();
        jest.advanceTimersByTime(2000);
      });

      expect(result.current.canRetry).toBe(false);

      // Third retry should not work
      await act(async () => {
        await result.current.retry();
      });

      expect(result.current.retryCount).toBe(2);
    });

    it('uses progressive retry delays', async () => {
      const { result } = renderHook(() => 
        useErrorHandling({ 
          retryDelay: 1000, 
          progressiveRetryDelay: true 
        })
      );

      act(() => {
        result.current.handleError('Test error');
      });

      const startTime = Date.now();

      await act(async () => {
        await result.current.retry();
        jest.advanceTimersByTime(1000); // First retry: 1000ms
      });

      await act(async () => {
        await result.current.retry();
        jest.advanceTimersByTime(1500); // Second retry: 1000 * 1.5 = 1500ms
      });

      expect(result.current.retryCount).toBe(2);
    });

    it('uses fixed retry delay when progressive is disabled', async () => {
      const { result } = renderHook(() => 
        useErrorHandling({ 
          retryDelay: 1000, 
          progressiveRetryDelay: false 
        })
      );

      act(() => {
        result.current.handleError('Test error');
      });

      await act(async () => {
        await result.current.retry();
        jest.advanceTimersByTime(1000);
      });

      await act(async () => {
        await result.current.retry();
        jest.advanceTimersByTime(1000); // Same delay
      });

      expect(result.current.retryCount).toBe(2);
    });
  });

  describe('Error Classification', () => {
    it('identifies network errors', () => {
      const { result } = renderHook(() => useErrorHandling());

      act(() => {
        result.current.handleError('Network request failed');
      });

      expect(result.current.isNetworkError()).toBe(true);
      expect(result.current.isServerError()).toBe(false);
      expect(result.current.isAuthError()).toBe(false);
    });

    it('identifies server errors', () => {
      const { result } = renderHook(() => useErrorHandling());

      act(() => {
        result.current.handleError('500 Internal Server Error');
      });

      expect(result.current.isServerError()).toBe(true);
      expect(result.current.isNetworkError()).toBe(false);
      expect(result.current.isAuthError()).toBe(false);
    });

    it('identifies authentication errors', () => {
      const { result } = renderHook(() => useErrorHandling());

      act(() => {
        result.current.handleError('401 Unauthorized');
      });

      expect(result.current.isAuthError()).toBe(true);
      expect(result.current.isNetworkError()).toBe(false);
      expect(result.current.isServerError()).toBe(false);
    });

    it('provides appropriate error messages', () => {
      const { result } = renderHook(() => useErrorHandling());

      // Network error
      act(() => {
        result.current.handleError('Network connection failed');
      });

      expect(result.current.getErrorMessage()).toContain('internet connection');

      // Server error
      act(() => {
        result.current.clearError();
        result.current.handleError('500 server error');
      });

      expect(result.current.getErrorMessage()).toContain('servers are experiencing');

      // Auth error
      act(() => {
        result.current.clearError();
        result.current.handleError('401 unauthorized');
      });

      expect(result.current.getErrorMessage()).toContain('session has expired');
    });
  });

  describe('Callbacks and Options', () => {
    it('calls onError callback', () => {
      const onError = jest.fn();
      const { result } = renderHook(() => useErrorHandling({ onError }));

      act(() => {
        result.current.handleError('Test error');
      });

      expect(onError).toHaveBeenCalledWith(expect.objectContaining({
        message: 'Test error',
      }));
    });

    it('calls onRetry callback', async () => {
      const onRetry = jest.fn();
      const { result } = renderHook(() => useErrorHandling({ onRetry }));

      act(() => {
        result.current.handleError('Test error');
      });

      await act(async () => {
        await result.current.retry();
        jest.advanceTimersByTime(2000);
      });

      expect(onRetry).toHaveBeenCalledWith(1);
    });

    it('calls onMaxRetriesExceeded callback', () => {
      const onMaxRetriesExceeded = jest.fn();
      const { result } = renderHook(() => 
        useErrorHandling({ 
          maxRetries: 1, 
          onMaxRetriesExceeded 
        })
      );

      act(() => {
        result.current.handleError('Test error');
      });

      // Exceed max retries
      act(() => {
        result.current.handleError('Another error');
      });

      expect(onMaxRetriesExceeded).toHaveBeenCalled();
    });

    it('respects error reporting setting', () => {
      const { result } = renderHook(() => 
        useErrorHandling({ reportErrors: false })
      );

      act(() => {
        result.current.handleError('Test error');
      });

      // logError should not be called when reporting is disabled
      const { logError } = require('../../utils/errorHandlingUtils');
      expect(logError).not.toHaveBeenCalled();
    });
  });

  describe('Performance Tracking Integration', () => {
    it('tracks error handling performance', () => {
      const { result } = renderHook(() => useErrorHandling());

      act(() => {
        result.current.handleError('Test error');
      });

      expect(mockPerformanceMonitor.trackUserInteraction).toHaveBeenCalledWith(
        'error_handled',
        0,
        expect.objectContaining({
          errorType: expect.any(String),
          errorMessage: 'Test error',
        })
      );
    });

    it('tracks retry performance', async () => {
      const { result } = renderHook(() => useErrorHandling());

      act(() => {
        result.current.handleError('Test error');
      });

      await act(async () => {
        await result.current.retry();
        jest.advanceTimersByTime(2000);
      });

      expect(mockPerformanceMonitor.trackUserInteraction).toHaveBeenCalledWith(
        'error_retry',
        0,
        expect.objectContaining({
          retryCount: 1,
          retryDelay: expect.any(Number),
        })
      );
    });
  });

  describe('App State Integration', () => {
    it('retries on app focus when enabled', async () => {
      const { result } = renderHook(() => 
        useErrorHandling({ autoRetryOnAppFocus: true })
      );

      act(() => {
        result.current.handleError('Test error');
      });

      expect(result.current.retryCount).toBe(0);

      // Simulate app going to background and coming back
      act(() => {
        AppState.currentState = 'background';
      });

      act(() => {
        AppState.currentState = 'active';
        // Trigger app state change event
        const listeners = (AppState.addEventListener as jest.Mock).mock.calls;
        if (listeners.length > 0) {
          const callback = listeners[0][1];
          callback('active');
        }
      });

      await act(async () => {
        jest.advanceTimersByTime(2000);
      });

      expect(result.current.retryCount).toBe(1);
    });

    it('does not retry on app focus when disabled', () => {
      const { result } = renderHook(() => 
        useErrorHandling({ autoRetryOnAppFocus: false })
      );

      act(() => {
        result.current.handleError('Test error');
      });

      // Simulate app state change
      act(() => {
        AppState.currentState = 'active';
      });

      expect(result.current.retryCount).toBe(0);
    });
  });

  describe('Cleanup', () => {
    it('cleans up timeouts on unmount', () => {
      const clearTimeoutSpy = jest.spyOn(global, 'clearTimeout');
      const { result, unmount } = renderHook(() => useErrorHandling());

      act(() => {
        result.current.handleError('Test error');
      });

      // Start retry (which sets a timeout)
      act(() => {
        result.current.retry();
      });

      unmount();

      expect(clearTimeoutSpy).toHaveBeenCalled();
      clearTimeoutSpy.mockRestore();
    });

    it('removes app state listeners on unmount', () => {
      const removeListenerSpy = jest.fn();
      (AppState.addEventListener as jest.Mock).mockReturnValue({
        remove: removeListenerSpy,
      });

      const { unmount } = renderHook(() => 
        useErrorHandling({ autoRetryOnAppFocus: true })
      );

      unmount();

      expect(removeListenerSpy).toHaveBeenCalled();
    });
  });

  describe('Edge Cases', () => {
    it('handles retry when no error exists', async () => {
      const { result } = renderHook(() => useErrorHandling());

      // Try to retry without an error
      await act(async () => {
        await result.current.retry();
      });

      expect(result.current.retryCount).toBe(0);
    });

    it('handles retry when max retries already exceeded', async () => {
      const { result } = renderHook(() => useErrorHandling({ maxRetries: 1 }));

      act(() => {
        result.current.handleError('Test error');
      });

      // Exceed max retries
      await act(async () => {
        await result.current.retry();
        jest.advanceTimersByTime(2000);
      });

      await act(async () => {
        await result.current.retry();
        jest.advanceTimersByTime(2000);
      });

      // Try to retry again
      await act(async () => {
        await result.current.retry();
      });

      expect(result.current.retryCount).toBe(1);
    });

    it('handles component unmount during retry delay', async () => {
      const { result, unmount } = renderHook(() => useErrorHandling());

      act(() => {
        result.current.handleError('Test error');
      });

      // Start retry
      act(() => {
        result.current.retry();
      });

      // Unmount before delay completes
      unmount();

      // Advance timers - should not cause errors
      act(() => {
        jest.advanceTimersByTime(2000);
      });

      // No assertions needed - just ensuring no errors are thrown
    });
  });
});
