beac058521f3c6637f1f13262bd40e6d
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var TurboModuleRegistry = _interopRequireWildcard(require("../../../../Libraries/TurboModule/TurboModuleRegistry"));
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var NativeModule = TurboModuleRegistry.getEnforcing('DeviceInfo');
var constants = null;
var NativeDeviceInfo = {
  getConstants: function getConstants() {
    if (constants == null) {
      constants = NativeModule.getConstants();
    }
    return constants;
  }
};
var _default = exports.default = NativeDeviceInfo;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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