{"version": 3, "names": ["_NativeSoundManager", "_interopRequireDefault", "require", "SoundManager", "playTouchSound", "NativeSoundManager", "_default", "exports", "default"], "sources": ["SoundManager.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict\n */\n\nimport NativeSoundManager from './NativeSoundManager';\n\nconst SoundManager = {\n  playTouchSound: function (): void {\n    if (NativeSoundManager) {\n      NativeSoundManager.playTouchSound();\n    }\n  },\n};\n\nexport default SoundManager;\n"], "mappings": ";;;;;AAUA,IAAAA,mBAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAMC,YAAY,GAAG;EACnBC,cAAc,EAAE,SAAhBA,cAAcA,CAAA,EAAoB;IAChC,IAAIC,2BAAkB,EAAE;MACtBA,2BAAkB,CAACD,cAAc,CAAC,CAAC;IACrC;EACF;AACF,CAAC;AAAC,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEaL,YAAY", "ignoreList": []}