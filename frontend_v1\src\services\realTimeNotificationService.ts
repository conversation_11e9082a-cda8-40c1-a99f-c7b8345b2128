/**
 * Real-time Notification Service - Live Updates and Notifications
 *
 * Service Contract:
 * - Manages real-time notifications for bookings, messages, and updates
 * - Integrates with WebSocket service for live data
 * - Provides notification display and management
 * - <PERSON>les push notifications and in-app notifications
 * - Supports notification preferences and filtering
 * - Implements proper error handling and fallback mechanisms
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { createWebSocketService } from './websocketService';
import { useAuthStore } from '../store/authSlice';

export interface RealTimeNotification {
  id: string;
  type: 'booking_update' | 'new_message' | 'payment_status' | 'provider_update' | 'system_alert';
  title: string;
  message: string;
  data?: any;
  timestamp: string;
  read: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
}

export interface BookingUpdate {
  bookingId: string;
  status: 'confirmed' | 'cancelled' | 'completed' | 'in_progress';
  message: string;
  estimatedTime?: string;
  providerLocation?: {
    latitude: number;
    longitude: number;
  };
}

export interface MessageNotification {
  conversationId: string;
  senderId: string;
  senderName: string;
  message: string;
  messageType: 'text' | 'image' | 'location';
}

class RealTimeNotificationService {
  private wsService: any = null;
  private notifications: RealTimeNotification[] = [];
  private listeners: Map<string, Set<(data: any) => void>> = new Map();
  private isConnected = false;

  async initialize() {
    try {
      // Initialize WebSocket connection
      this.wsService = createWebSocketService({
        url: 'ws://192.168.2.65:8000/ws/notifications/',
        reconnectAttempts: 5,
        reconnectInterval: 3000,
      });

      // Connect with auth token
      const { authToken } = useAuthStore.getState();
      await this.wsService.connect(authToken);

      // Set up event listeners
      this.setupEventListeners();
      this.isConnected = true;

      console.log('Real-time notification service initialized');
    } catch (error) {
      console.error('Failed to initialize real-time notification service:', error);
    }
  }

  private setupEventListeners() {
    if (!this.wsService) return;

    // Booking updates
    this.wsService.on('booking_update', (data: BookingUpdate) => {
      this.handleBookingUpdate(data);
    });

    // New messages
    this.wsService.on('new_message', (data: MessageNotification) => {
      this.handleNewMessage(data);
    });

    // Payment status updates
    this.wsService.on('payment_status', (data: any) => {
      this.handlePaymentUpdate(data);
    });

    // Provider location updates
    this.wsService.on('provider_location', (data: any) => {
      this.handleProviderLocationUpdate(data);
    });

    // System alerts
    this.wsService.on('system_alert', (data: any) => {
      this.handleSystemAlert(data);
    });

    // Connection status
    this.wsService.on('connect', () => {
      this.isConnected = true;
      this.emit('connection_status', { connected: true });
    });

    this.wsService.on('disconnect', () => {
      this.isConnected = false;
      this.emit('connection_status', { connected: false });
    });
  }

  private handleBookingUpdate(data: BookingUpdate) {
    const notification: RealTimeNotification = {
      id: `booking_${data.bookingId}_${Date.now()}`,
      type: 'booking_update',
      title: 'Booking Update',
      message: data.message,
      data: data,
      timestamp: new Date().toISOString(),
      read: false,
      priority: data.status === 'cancelled' ? 'high' : 'medium',
    };

    this.addNotification(notification);
    this.emit('booking_update', data);
  }

  private handleNewMessage(data: MessageNotification) {
    const notification: RealTimeNotification = {
      id: `message_${data.conversationId}_${Date.now()}`,
      type: 'new_message',
      title: `New message from ${data.senderName}`,
      message: data.message,
      data: data,
      timestamp: new Date().toISOString(),
      read: false,
      priority: 'medium',
    };

    this.addNotification(notification);
    this.emit('new_message', data);
  }

  private handlePaymentUpdate(data: any) {
    const notification: RealTimeNotification = {
      id: `payment_${data.paymentId}_${Date.now()}`,
      type: 'payment_status',
      title: 'Payment Update',
      message: data.message,
      data: data,
      timestamp: new Date().toISOString(),
      read: false,
      priority: data.status === 'failed' ? 'high' : 'medium',
    };

    this.addNotification(notification);
    this.emit('payment_update', data);
  }

  private handleProviderLocationUpdate(data: any) {
    // Don't create notification for location updates, just emit event
    this.emit('provider_location_update', data);
  }

  private handleSystemAlert(data: any) {
    const notification: RealTimeNotification = {
      id: `system_${Date.now()}`,
      type: 'system_alert',
      title: data.title || 'System Alert',
      message: data.message,
      data: data,
      timestamp: new Date().toISOString(),
      read: false,
      priority: data.priority || 'medium',
    };

    this.addNotification(notification);
    this.emit('system_alert', data);
  }

  private addNotification(notification: RealTimeNotification) {
    this.notifications.unshift(notification);
    
    // Keep only last 50 notifications
    if (this.notifications.length > 50) {
      this.notifications = this.notifications.slice(0, 50);
    }

    this.emit('notification_added', notification);
  }

  // Public methods
  getNotifications(): RealTimeNotification[] {
    return [...this.notifications];
  }

  getUnreadNotifications(): RealTimeNotification[] {
    return this.notifications.filter(n => !n.read);
  }

  markAsRead(notificationId: string) {
    const notification = this.notifications.find(n => n.id === notificationId);
    if (notification) {
      notification.read = true;
      this.emit('notification_read', notification);
    }
  }

  markAllAsRead() {
    this.notifications.forEach(n => n.read = true);
    this.emit('all_notifications_read', {});
  }

  clearNotifications() {
    this.notifications = [];
    this.emit('notifications_cleared', {});
  }

  // Event management
  on(event: string, callback: (data: any) => void) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event)!.add(callback);
  }

  off(event: string, callback: (data: any) => void) {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.delete(callback);
    }
  }

  private emit(event: string, data: any) {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  // Connection management
  isServiceConnected(): boolean {
    return this.isConnected;
  }

  async reconnect() {
    if (this.wsService) {
      try {
        const { authToken } = useAuthStore.getState();
        await this.wsService.connect(authToken);
      } catch (error) {
        console.error('Failed to reconnect notification service:', error);
      }
    }
  }

  disconnect() {
    if (this.wsService) {
      this.wsService.disconnect();
      this.isConnected = false;
    }
  }

  // Send real-time updates
  sendTypingIndicator(conversationId: string, isTyping: boolean) {
    if (this.wsService && this.isConnected) {
      this.wsService.send({
        type: 'typing_indicator',
        conversation_id: conversationId,
        is_typing: isTyping,
      });
    }
  }

  sendLocationUpdate(bookingId: string, location: { latitude: number; longitude: number }) {
    if (this.wsService && this.isConnected) {
      this.wsService.send({
        type: 'location_update',
        booking_id: bookingId,
        location: location,
      });
    }
  }

  sendBookingStatusUpdate(bookingId: string, status: string, message?: string) {
    if (this.wsService && this.isConnected) {
      this.wsService.send({
        type: 'booking_status_update',
        booking_id: bookingId,
        status: status,
        message: message,
      });
    }
  }
}

// Create singleton instance
export const realTimeNotificationService = new RealTimeNotificationService();

// Auto-initialize when auth state changes
useAuthStore.subscribe((state) => {
  if (state.authToken && !realTimeNotificationService.isServiceConnected()) {
    realTimeNotificationService.initialize();
  } else if (!state.authToken && realTimeNotificationService.isServiceConnected()) {
    realTimeNotificationService.disconnect();
  }
});

export default realTimeNotificationService;
