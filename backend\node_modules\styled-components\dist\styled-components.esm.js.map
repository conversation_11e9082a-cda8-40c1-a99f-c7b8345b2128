{"version": 3, "file": "styled-components.esm.js", "sources": ["../src/constants.ts", "../src/utils/checkDynamicCreation.ts", "../src/utils/empties.ts", "../src/utils/determineTheme.ts", "../src/utils/domElements.ts", "../src/utils/escape.ts", "../src/utils/generateAlphabeticName.ts", "../src/utils/hash.ts", "../src/utils/generateComponentId.ts", "../src/utils/getComponentName.ts", "../src/utils/isTag.ts", "../src/utils/hoist.ts", "../src/utils/isFunction.ts", "../src/utils/isStyledComponent.ts", "../src/utils/joinStrings.ts", "../src/utils/isPlainObject.ts", "../src/utils/mixinDeep.ts", "../src/utils/setToString.ts", "../src/utils/errors.ts", "../src/utils/error.ts", "../src/sheet/GroupedTag.ts", "../src/sheet/GroupIDAllocator.ts", "../src/sheet/Rehydration.ts", "../src/utils/nonce.ts", "../src/sheet/dom.ts", "../src/sheet/Tag.ts", "../src/sheet/Sheet.ts", "../src/utils/stylis.ts", "../src/models/StyleSheetManager.tsx", "../src/models/Keyframes.ts", "../src/utils/hyphenateStyleName.ts", "../src/utils/flatten.ts", "../src/utils/addUnitIfNeeded.ts", "../src/utils/isStatelessFunction.ts", "../src/utils/isStaticRules.ts", "../src/models/ComponentStyle.ts", "../src/models/ThemeProvider.tsx", "../src/models/StyledComponent.ts", "../src/utils/generateDisplayName.ts", "../src/utils/createWarnTooManyClasses.ts", "../src/utils/interleave.ts", "../src/constructors/css.ts", "../src/constructors/constructWithOptions.ts", "../src/constructors/styled.tsx", "../src/models/GlobalStyle.ts", "../src/constructors/createGlobalStyle.ts", "../src/constructors/keyframes.ts", "../src/hoc/withTheme.tsx", "../src/models/ServerStyleSheet.tsx", "../src/secretInternals.ts", "../src/base.ts"], "sourcesContent": ["declare let SC_DISABLE_SPEEDY: boolean | null | undefined;\ndeclare let __VERSION__: string;\n\nexport const SC_ATTR: string =\n  (typeof process !== 'undefined' &&\n    typeof process.env !== 'undefined' &&\n    (process.env.REACT_APP_SC_ATTR || process.env.SC_ATTR)) ||\n  'data-styled';\n\nexport const SC_ATTR_ACTIVE = 'active';\nexport const SC_ATTR_VERSION = 'data-styled-version';\nexport const SC_VERSION = __VERSION__;\nexport const SPLITTER = '/*!sc*/\\n';\n\nexport const IS_BROWSER = typeof window !== 'undefined' && typeof document !== 'undefined';\n\nexport const DISABLE_SPEEDY = Boolean(\n  typeof SC_DISABLE_SPEEDY === 'boolean'\n    ? SC_DISABLE_SPEEDY\n    : typeof process !== 'undefined' &&\n        typeof process.env !== 'undefined' &&\n        typeof process.env.REACT_APP_SC_DISABLE_SPEEDY !== 'undefined' &&\n        process.env.REACT_APP_SC_DISABLE_SPEEDY !== ''\n      ? process.env.REACT_APP_SC_DISABLE_SPEEDY === 'false'\n        ? false\n        : process.env.REACT_APP_SC_DISABLE_SPEEDY\n      : typeof process !== 'undefined' &&\n          typeof process.env !== 'undefined' &&\n          typeof process.env.SC_DISABLE_SPEEDY !== 'undefined' &&\n          process.env.SC_DISABLE_SPEEDY !== ''\n        ? process.env.SC_DISABLE_SPEEDY === 'false'\n          ? false\n          : process.env.SC_DISABLE_SPEEDY\n        : process.env.NODE_ENV !== 'production'\n);\n\n// Shared empty execution context when generating static styles\nexport const STATIC_EXECUTION_CONTEXT = {};\n", "import { useRef } from 'react';\n\nconst invalidHookCallRe = /invalid hook call/i;\nconst seen = new Set();\n\nexport const checkDynamicCreation = (displayName: string, componentId?: string | undefined) => {\n  if (process.env.NODE_ENV !== 'production') {\n    const parsedIdString = componentId ? ` with the id of \"${componentId}\"` : '';\n    const message =\n      `The component ${displayName}${parsedIdString} has been created dynamically.\\n` +\n      \"You may see this warning because you've called styled inside another component.\\n\" +\n      'To resolve this only create new StyledComponents outside of any render method and function component.\\n' +\n      'See https://styled-components.com/docs/basics#define-styled-components-outside-of-the-render-method for more info.\\n';\n\n    // If a hook is called outside of a component:\n    // React 17 and earlier throw an error\n    // React 18 and above use console.error\n\n    const originalConsoleError = console.error;\n    try {\n      let didNotCallInvalidHook = true;\n      console.error = (consoleErrorMessage, ...consoleErrorArgs) => {\n        // The error here is expected, since we're expecting anything that uses `checkDynamicCreation` to\n        // be called outside of a React component.\n        if (invalidHookCallRe.test(consoleErrorMessage)) {\n          didNotCallInvalidHook = false;\n          // This shouldn't happen, but resets `warningSeen` if we had this error happen intermittently\n          seen.delete(message);\n        } else {\n          originalConsoleError(consoleErrorMessage, ...consoleErrorArgs);\n        }\n      };\n      // We purposefully call `useRef` outside of a component and expect it to throw\n      // If it doesn't, then we're inside another component.\n      useRef();\n\n      if (didNotCallInvalidHook && !seen.has(message)) {\n        console.warn(message);\n        seen.add(message);\n      }\n    } catch (error) {\n      // The error here is expected, since we're expecting anything that uses `checkDynamicCreation` to\n      // be called outside of a React component.\n      if (invalidHookCallRe.test((error as Error).message)) {\n        // This shouldn't happen, but resets `warningSeen` if we had this error happen intermittently\n        seen.delete(message);\n      }\n    } finally {\n      console.error = originalConsoleError;\n    }\n  }\n};\n", "import { Dict } from '../types';\n\nexport const EMPTY_ARRAY = Object.freeze([]) as Readonly<any[]>;\nexport const EMPTY_OBJECT = Object.freeze({}) as Readonly<Dict<any>>;\n", "import { DefaultTheme, ExecutionProps } from '../types';\nimport { EMPTY_OBJECT } from './empties';\n\nexport default function determineTheme(\n  props: ExecutionProps,\n  providedTheme?: DefaultTheme | undefined,\n  defaultProps: { theme?: DefaultTheme | undefined } = EMPTY_OBJECT\n): DefaultTheme | undefined {\n  return (props.theme !== defaultProps.theme && props.theme) || providedTheme || defaultProps.theme;\n}\n", "// Thanks to ReactDOMFactories for this handy list!\n\nconst elements = [\n  'a',\n  'abbr',\n  'address',\n  'area',\n  'article',\n  'aside',\n  'audio',\n  'b',\n  'base',\n  'bdi',\n  'bdo',\n  'big',\n  'blockquote',\n  'body',\n  'br',\n  'button',\n  'canvas',\n  'caption',\n  'cite',\n  'code',\n  'col',\n  'colgroup',\n  'data',\n  'datalist',\n  'dd',\n  'del',\n  'details',\n  'dfn',\n  'dialog',\n  'div',\n  'dl',\n  'dt',\n  'em',\n  'embed',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'footer',\n  'form',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'header',\n  'hgroup',\n  'hr',\n  'html',\n  'i',\n  'iframe',\n  'img',\n  'input',\n  'ins',\n  'kbd',\n  'keygen',\n  'label',\n  'legend',\n  'li',\n  'link',\n  'main',\n  'map',\n  'mark',\n  'menu',\n  'menuitem',\n  'meta',\n  'meter',\n  'nav',\n  'noscript',\n  'object',\n  'ol',\n  'optgroup',\n  'option',\n  'output',\n  'p',\n  'param',\n  'picture',\n  'pre',\n  'progress',\n  'q',\n  'rp',\n  'rt',\n  'ruby',\n  's',\n  'samp',\n  'script',\n  'section',\n  'select',\n  'small',\n  'source',\n  'span',\n  'strong',\n  'style',\n  'sub',\n  'summary',\n  'sup',\n  'table',\n  'tbody',\n  'td',\n  'textarea',\n  'tfoot',\n  'th',\n  'thead',\n  'time',\n  'tr',\n  'track',\n  'u',\n  'ul',\n  'use',\n  'var',\n  'video',\n  'wbr', // SVG\n  'circle',\n  'clipPath',\n  'defs',\n  'ellipse',\n  'foreignObject',\n  'g',\n  'image',\n  'line',\n  'linearGradient',\n  'marker',\n  'mask',\n  'path',\n  'pattern',\n  'polygon',\n  'polyline',\n  'radialGradient',\n  'rect',\n  'stop',\n  'svg',\n  'text',\n  'tspan',\n] as const;\n\nexport default new Set(elements);\nexport type SupportedHTMLElements = (typeof elements)[number];\n", "// Source: https://www.w3.org/TR/cssom-1/#serialize-an-identifier\n// Control characters and non-letter first symbols are not supported\nconst escapeRegex = /[!\"#$%&'()*+,./:;<=>?@[\\\\\\]^`{|}~-]+/g;\n\nconst dashesAtEnds = /(^-|-$)/g;\n\n/**\n * TODO: Explore using CSS.escape when it becomes more available\n * in evergreen browsers.\n */\nexport default function escape(str: string) {\n  return str // Replace all possible CSS selectors\n    .replace(escapeRegex, '-') // Remove extraneous hyphens at the start and end\n    .replace(dashesAtEnds, '');\n}\n", "const AD_REPLACER_R = /(a)(d)/gi;\n\n/* This is the \"capacity\" of our alphabet i.e. 2x26 for all letters plus their capitalised\n * counterparts */\nconst charsLength = 52;\n\n/* start at 75 for 'a' until 'z' (25) and then start at 65 for capitalised letters */\nconst getAlphabeticChar = (code: number) => String.fromCharCode(code + (code > 25 ? 39 : 97));\n\n/* input a number, usually a hash and convert it to base-52 */\nexport default function generateAlphabeticName(code: number) {\n  let name = '';\n  let x;\n\n  /* get a char and divide by alphabet-length */\n  for (x = Math.abs(code); x > charsLength; x = (x / charsLength) | 0) {\n    name = getAlphabeticChar(x % charsLength) + name;\n  }\n\n  return (getAlphabeticChar(x % charsLength) + name).replace(AD_REPLACER_R, '$1-$2');\n}\n", "export const SEED = 5381;\n\n// When we have separate strings it's useful to run a progressive\n// version of djb2 where we pretend that we're still looping over\n// the same string\nexport const phash = (h: number, x: string) => {\n  let i = x.length;\n\n  while (i) {\n    h = (h * 33) ^ x.charCodeAt(--i);\n  }\n\n  return h;\n};\n\n// This is a djb2 hashing function\nexport const hash = (x: string) => {\n  return phash(SEED, x);\n};\n", "import generateAlphabeticName from './generateAlphabeticName';\nimport { hash } from './hash';\n\nexport default function generateComponentId(str: string) {\n  return generateAlphabeticName(hash(str) >>> 0);\n}\n", "import { StyledTarget } from '../types';\n\nexport default function getComponentName(target: StyledTarget<any>) {\n  return (\n    (process.env.NODE_ENV !== 'production' ? typeof target === 'string' && target : false) ||\n    (target as Exclude<StyledTarget<any>, string>).displayName ||\n    (target as Function).name ||\n    'Component'\n  );\n}\n", "import { StyledTarget } from '../types';\n\nexport default function isTag(target: StyledTarget<'web'>): target is string {\n  return (\n    typeof target === 'string' &&\n    (process.env.NODE_ENV !== 'production'\n      ? target.charAt(0) === target.charAt(0).toLowerCase()\n      : true)\n  );\n}\n", "import React from 'react';\nimport { AnyComponent } from '../types';\n\nconst hasSymbol = typeof Symbol === 'function' && Symbol.for;\n\n// copied from react-is\nconst REACT_MEMO_TYPE = hasSymbol ? Symbol.for('react.memo') : 0xead3;\nconst REACT_FORWARD_REF_TYPE = hasSymbol ? Symbol.for('react.forward_ref') : 0xead0;\n\n/**\n * Adapted from hoist-non-react-statics to avoid the react-is dependency.\n */\nconst REACT_STATICS = {\n  childContextTypes: true,\n  contextType: true,\n  contextTypes: true,\n  defaultProps: true,\n  displayName: true,\n  getDefaultProps: true,\n  getDerivedStateFromError: true,\n  getDerivedStateFromProps: true,\n  mixins: true,\n  propTypes: true,\n  type: true,\n};\n\nconst KNOWN_STATICS = {\n  name: true,\n  length: true,\n  prototype: true,\n  caller: true,\n  callee: true,\n  arguments: true,\n  arity: true,\n};\n\nconst FORWARD_REF_STATICS = {\n  $$typeof: true,\n  render: true,\n  defaultProps: true,\n  displayName: true,\n  propTypes: true,\n};\n\nconst MEMO_STATICS = {\n  $$typeof: true,\n  compare: true,\n  defaultProps: true,\n  displayName: true,\n  propTypes: true,\n  type: true,\n};\n\nconst TYPE_STATICS = {\n  [REACT_FORWARD_REF_TYPE]: FORWARD_REF_STATICS,\n  [REACT_MEMO_TYPE]: MEMO_STATICS,\n};\n\ntype OmniComponent = AnyComponent;\n\n// adapted from react-is\nfunction isMemo(\n  object: OmniComponent | React.MemoExoticComponent<any>\n): object is React.MemoExoticComponent<any> {\n  const $$typeofType = 'type' in object && object.type.$$typeof;\n\n  return $$typeofType === REACT_MEMO_TYPE;\n}\n\nfunction getStatics(component: OmniComponent) {\n  // React v16.11 and below\n  if (isMemo(component)) {\n    return MEMO_STATICS;\n  }\n\n  // React v16.12 and above\n  return '$$typeof' in component\n    ? TYPE_STATICS[component['$$typeof'] as unknown as string]\n    : REACT_STATICS;\n}\n\nconst defineProperty = Object.defineProperty;\nconst getOwnPropertyNames = Object.getOwnPropertyNames;\nconst getOwnPropertySymbols = Object.getOwnPropertySymbols;\nconst getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\nconst getPrototypeOf = Object.getPrototypeOf;\nconst objectPrototype = Object.prototype;\n\ntype ExcludeList = {\n  [key: string]: true;\n};\n\nexport type NonReactStatics<S extends OmniComponent, C extends ExcludeList = {}> = {\n  [key in Exclude<\n    keyof S,\n    S extends React.MemoExoticComponent<any>\n      ? keyof typeof MEMO_STATICS | keyof C\n      : S extends React.ForwardRefExoticComponent<any>\n        ? keyof typeof FORWARD_REF_STATICS | keyof C\n        : keyof typeof REACT_STATICS | keyof typeof KNOWN_STATICS | keyof C\n  >]: S[key];\n};\n\nexport default function hoistNonReactStatics<\n  T extends OmniComponent,\n  S extends OmniComponent,\n  C extends ExcludeList = {},\n>(targetComponent: T, sourceComponent: S, excludelist?: C | undefined) {\n  if (typeof sourceComponent !== 'string') {\n    // don't hoist over string (html) components\n\n    if (objectPrototype) {\n      const inheritedComponent = getPrototypeOf(sourceComponent);\n      if (inheritedComponent && inheritedComponent !== objectPrototype) {\n        hoistNonReactStatics(targetComponent, inheritedComponent, excludelist);\n      }\n    }\n\n    let keys: (String | Symbol)[] = getOwnPropertyNames(sourceComponent);\n\n    if (getOwnPropertySymbols) {\n      keys = keys.concat(getOwnPropertySymbols(sourceComponent));\n    }\n\n    const targetStatics = getStatics(targetComponent);\n    const sourceStatics = getStatics(sourceComponent);\n\n    for (let i = 0; i < keys.length; ++i) {\n      const key = keys[i] as unknown as string;\n      if (\n        !(key in KNOWN_STATICS) &&\n        !(excludelist && excludelist[key]) &&\n        !(sourceStatics && key in sourceStatics) &&\n        !(targetStatics && key in targetStatics)\n      ) {\n        const descriptor = getOwnPropertyDescriptor(sourceComponent, key);\n\n        try {\n          // Avoid failures from read-only properties\n          defineProperty(targetComponent, key, descriptor!);\n        } catch (e) {\n          /* ignore */\n        }\n      }\n    }\n  }\n\n  return targetComponent as T & NonReactStatics<S, C>;\n}\n", "export default function isFunction(test: any): test is Function {\n  return typeof test === 'function';\n}\n", "import { StyledComponentBrand } from '../types';\n\nexport default function isStyledComponent(target: any): target is StyledComponentBrand {\n  return typeof target === 'object' && 'styledComponentId' in target;\n}\n", "/**\n * Convenience function for joining strings to form className chains\n */\nexport function joinStrings(a?: string | undefined, b?: string | undefined): string {\n  return a && b ? `${a} ${b}` : a || b || '';\n}\n\nexport function joinStringArray(arr: string[], sep?: string | undefined): string {\n  if (arr.length === 0) {\n    return '';\n  }\n\n  let result = arr[0];\n  for (let i = 1; i < arr.length; i++) {\n    result += sep ? sep + arr[i] : arr[i];\n  }\n  return result;\n}\n", "export default function isPlainObject(x: any): x is Record<any, any> {\n  return (\n    x !== null &&\n    typeof x === 'object' &&\n    x.constructor.name === Object.name &&\n    /* check for reasonable markers that the object isn't an element for react & preact/compat */\n    !('props' in x && x.$$typeof)\n  );\n}\n", "import isPlainObject from './isPlainObject';\n\nfunction mixinRecursively(target: any, source: any, forceMerge = false) {\n  /* only merge into POJOs, Arrays, but for top level objects only\n   * allow to merge into anything by passing forceMerge = true */\n  if (!forceMerge && !isPlainObject(target) && !Array.isArray(target)) {\n    return source;\n  }\n\n  if (Array.isArray(source)) {\n    for (let key = 0; key < source.length; key++) {\n      target[key] = mixinRecursively(target[key], source[key]);\n    }\n  } else if (isPlainObject(source)) {\n    for (const key in source) {\n      target[key] = mixinRecursively(target[key], source[key]);\n    }\n  }\n\n  return target;\n}\n\n/**\n * Arrays & POJOs merged recursively, other objects and value types are overridden\n * If target is not a POJO or an Array, it will get source properties injected via shallow merge\n * Source objects applied left to right.  Mutates & returns target.  Similar to lodash merge.\n */\nexport default function mixinDeep(target: any, ...sources: any[]) {\n  for (const source of sources) {\n    mixinRecursively(target, source, true);\n  }\n\n  return target;\n}\n", "/**\n * If the Object prototype is frozen, the \"toString\" property is non-writable. This means that any objects which inherit this property\n * cannot have the property changed using a \"=\" assignment operator. If using strict mode, attempting that will cause an error. If not using\n * strict mode, attempting that will be silently ignored.\n *\n * If the Object prototype is frozen, inherited non-writable properties can still be shadowed using one of two mechanisms:\n *\n *  1. ES6 class methods: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Classes#methods\n *  2. Using the `Object.defineProperty()` static method:\n *     https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/defineProperty\n *\n * However, this project uses Babel to transpile ES6 classes, and transforms ES6 class methods to use the assignment operator instead:\n * https://babeljs.io/docs/babel-plugin-transform-class-properties#options\n *\n * Therefore, the most compatible way to shadow the prototype's \"toString\" property is to define a new \"toString\" property on this object.\n */\nexport function setToString(object: object, toStringFn: () => string) {\n  Object.defineProperty(object, 'toString', { value: toStringFn });\n}\n", "export default {\n  '1': 'Cannot create styled-component for component: %s.\\n\\n',\n  '2': \"Can't collect styles once you've consumed a `ServerStyleSheet`'s styles! `ServerStyleSheet` is a one off instance for each server-side render cycle.\\n\\n- Are you trying to reuse it across renders?\\n- Are you accidentally calling collectStyles twice?\\n\\n\",\n  '3': 'Streaming SSR is only supported in a Node.js environment; Please do not try to call this method in the browser.\\n\\n',\n  '4': 'The `StyleSheetManager` expects a valid target or sheet prop!\\n\\n- Does this error occur on the client and is your target falsy?\\n- Does this error occur on the server and is the sheet falsy?\\n\\n',\n  '5': 'The clone method cannot be used on the client!\\n\\n- Are you running in a client-like environment on the server?\\n- Are you trying to run SSR on the client?\\n\\n',\n  '6': \"Trying to insert a new style tag, but the given Node is unmounted!\\n\\n- Are you using a custom target that isn't mounted?\\n- Does your document not have a valid head element?\\n- Have you accidentally removed a style tag manually?\\n\\n\",\n  '7': 'ThemeProvider: Please return an object from your \"theme\" prop function, e.g.\\n\\n```js\\ntheme={() => ({})}\\n```\\n\\n',\n  '8': 'ThemeProvider: Please make your \"theme\" prop an object.\\n\\n',\n  '9': 'Missing document `<head>`\\n\\n',\n  '10': 'Cannot find a StyleSheet instance. Usually this happens if there are multiple copies of styled-components loaded at once. Check out this issue for how to troubleshoot and fix the common cases where this situation can happen: https://github.com/styled-components/styled-components/issues/1941#issuecomment-417862021\\n\\n',\n  '11': '_This error was replaced with a dev-time warning, it will be deleted for v4 final._ [createGlobalStyle] received children which will not be rendered. Please use the component without passing children elements.\\n\\n',\n  '12': 'It seems you are interpolating a keyframe declaration (%s) into an untagged string. This was supported in styled-components v3, but is not longer supported in v4 as keyframes are now injected on-demand. Please wrap your string in the css\\\\`\\\\` helper which ensures the styles are injected correctly. See https://www.styled-components.com/docs/api#css\\n\\n',\n  '13': '%s is not a styled component and cannot be referred to via component selector. See https://www.styled-components.com/docs/advanced#referring-to-other-components for more details.\\n\\n',\n  '14': 'ThemeProvider: \"theme\" prop is required.\\n\\n',\n  '15': \"A stylis plugin has been supplied that is not named. We need a name for each plugin to be able to prevent styling collisions between different stylis configurations within the same app. Before you pass your plugin to `<StyleSheetManager stylisPlugins={[]}>`, please make sure each plugin is uniquely-named, e.g.\\n\\n```js\\nObject.defineProperty(importedPlugin, 'name', { value: 'some-unique-name' });\\n```\\n\\n\",\n  '16': \"Reached the limit of how many styled components may be created at group %s.\\nYou may only create up to 1,073,741,824 components. If you're creating components dynamically,\\nas for instance in your render method then you may be running into this limitation.\\n\\n\",\n  '17': \"CSSStyleSheet could not be found on HTMLStyleElement.\\nHas styled-components' style tag been unmounted or altered by another script?\\n\",\n  '18': 'ThemeProvider: Please make sure your useTheme hook is within a `<ThemeProvider>`',\n};\n", "import { Dict } from '../types';\nimport errorMap from './errors';\n\nconst ERRORS: Dict<any> = process.env.NODE_ENV !== 'production' ? errorMap : {};\n\n/**\n * super basic version of sprintf\n */\nfunction format(...args: [string, ...any]) {\n  let a = args[0];\n  const b = [];\n\n  for (let c = 1, len = args.length; c < len; c += 1) {\n    b.push(args[c]);\n  }\n\n  b.forEach(d => {\n    a = a.replace(/%[a-z]/, d);\n  });\n\n  return a;\n}\n\n/**\n * Create an error file out of errors.md for development and a simple web link to the full errors\n * in production mode.\n */\nexport default function throwStyledComponentsError(\n  code: string | number,\n  ...interpolations: any[]\n) {\n  if (process.env.NODE_ENV === 'production') {\n    return new Error(\n      `An error occurred. See https://github.com/styled-components/styled-components/blob/main/packages/styled-components/src/utils/errors.md#${code} for more information.${\n        interpolations.length > 0 ? ` Args: ${interpolations.join(', ')}` : ''\n      }`\n    );\n  } else {\n    return new Error(format(ERRORS[code], ...interpolations).trim());\n  }\n}\n", "import { SPLITTER } from '../constants';\nimport styledError from '../utils/error';\nimport { GroupedTag, Tag } from './types';\n\n/** Create a GroupedTag with an underlying Tag implementation */\nexport const makeGroupedTag = (tag: Tag) => {\n  return new DefaultGroupedTag(tag);\n};\n\nconst BASE_SIZE = 1 << 9;\n\nconst DefaultGroupedTag = class DefaultGroupedTag implements GroupedTag {\n  groupSizes: Uint32Array;\n  length: number;\n  tag: Tag;\n\n  constructor(tag: Tag) {\n    this.groupSizes = new Uint32Array(BASE_SIZE);\n    this.length = BASE_SIZE;\n    this.tag = tag;\n  }\n\n  indexOfGroup(group: number) {\n    let index = 0;\n    for (let i = 0; i < group; i++) {\n      index += this.groupSizes[i];\n    }\n\n    return index;\n  }\n\n  insertRules(group: number, rules: string[]) {\n    if (group >= this.groupSizes.length) {\n      const oldBuffer = this.groupSizes;\n      const oldSize = oldBuffer.length;\n\n      let newSize = oldSize;\n      while (group >= newSize) {\n        newSize <<= 1;\n        if (newSize < 0) {\n          throw styledError(16, `${group}`);\n        }\n      }\n\n      this.groupSizes = new Uint32Array(newSize);\n      this.groupSizes.set(oldBuffer);\n      this.length = newSize;\n\n      for (let i = oldSize; i < newSize; i++) {\n        this.groupSizes[i] = 0;\n      }\n    }\n\n    let ruleIndex = this.indexOfGroup(group + 1);\n\n    for (let i = 0, l = rules.length; i < l; i++) {\n      if (this.tag.insertRule(ruleIndex, rules[i])) {\n        this.groupSizes[group]++;\n        ruleIndex++;\n      }\n    }\n  }\n\n  clearGroup(group: number) {\n    if (group < this.length) {\n      const length = this.groupSizes[group];\n      const startIndex = this.indexOfGroup(group);\n      const endIndex = startIndex + length;\n\n      this.groupSizes[group] = 0;\n\n      for (let i = startIndex; i < endIndex; i++) {\n        this.tag.deleteRule(startIndex);\n      }\n    }\n  }\n\n  getGroup(group: number) {\n    let css = '';\n    if (group >= this.length || this.groupSizes[group] === 0) {\n      return css;\n    }\n\n    const length = this.groupSizes[group];\n    const startIndex = this.indexOfGroup(group);\n    const endIndex = startIndex + length;\n\n    for (let i = startIndex; i < endIndex; i++) {\n      css += `${this.tag.getRule(i)}${SPLITTER}`;\n    }\n\n    return css;\n  }\n};\n", "import styledError from '../utils/error';\n\nconst MAX_SMI = 1 << (31 - 1);\n\nlet groupIDRegister: Map<string, number> = new Map();\nlet reverseRegister: Map<number, string> = new Map();\nlet nextFreeGroup = 1;\n\nexport const resetGroupIds = () => {\n  groupIDRegister = new Map();\n  reverseRegister = new Map();\n  nextFreeGroup = 1;\n};\n\nexport const getGroupForId = (id: string): number => {\n  if (groupIDRegister.has(id)) {\n    return groupIDRegister.get(id) as any;\n  }\n\n  while (reverseRegister.has(nextFreeGroup)) {\n    nextFreeGroup++;\n  }\n\n  const group = nextFreeGroup++;\n\n  if (process.env.NODE_ENV !== 'production' && ((group | 0) < 0 || group > MAX_SMI)) {\n    throw styledError(16, `${group}`);\n  }\n\n  groupIDRegister.set(id, group);\n  reverseRegister.set(group, id);\n  return group;\n};\n\nexport const getIdForGroup = (group: number): void | string => {\n  return reverseRegister.get(group);\n};\n\nexport const setGroupForId = (id: string, group: number) => {\n  // move pointer\n  nextFreeGroup = group + 1;\n\n  groupIDRegister.set(id, group);\n  reverseRegister.set(group, id);\n};\n", "import { SC_ATTR, SC_ATTR_ACTIVE, SC_ATTR_VERSION, SC_VERSION, SPLITTER } from '../constants';\nimport { getIdForGroup, setGroupForId } from './GroupIDAllocator';\nimport { Sheet } from './types';\n\nconst SELECTOR = `style[${SC_ATTR}][${SC_ATTR_VERSION}=\"${SC_VERSION}\"]`;\nconst MARKER_RE = new RegExp(`^${SC_ATTR}\\\\.g(\\\\d+)\\\\[id=\"([\\\\w\\\\d-]+)\"\\\\].*?\"([^\"]*)`);\n\nexport const outputSheet = (sheet: Sheet) => {\n  const tag = sheet.getTag();\n  const { length } = tag;\n\n  let css = '';\n  for (let group = 0; group < length; group++) {\n    const id = getIdForGroup(group);\n    if (id === undefined) continue;\n\n    const names = sheet.names.get(id);\n    const rules = tag.getGroup(group);\n    if (names === undefined || !names.size || rules.length === 0) continue;\n\n    const selector = `${SC_ATTR}.g${group}[id=\"${id}\"]`;\n\n    let content = '';\n    if (names !== undefined) {\n      names.forEach(name => {\n        if (name.length > 0) {\n          content += `${name},`;\n        }\n      });\n    }\n\n    // NOTE: It's easier to collect rules and have the marker\n    // after the actual rules to simplify the rehydration\n    css += `${rules}${selector}{content:\"${content}\"}${SPLITTER}`;\n  }\n\n  return css;\n};\n\nconst rehydrateNamesFromContent = (sheet: Sheet, id: string, content: string) => {\n  const names = content.split(',');\n  let name;\n\n  for (let i = 0, l = names.length; i < l; i++) {\n    if ((name = names[i])) {\n      sheet.registerName(id, name);\n    }\n  }\n};\n\nconst rehydrateSheetFromTag = (sheet: Sheet, style: HTMLStyleElement) => {\n  const parts = (style.textContent ?? '').split(SPLITTER);\n  const rules: string[] = [];\n\n  for (let i = 0, l = parts.length; i < l; i++) {\n    const part = parts[i].trim();\n    if (!part) continue;\n\n    const marker = part.match(MARKER_RE);\n\n    if (marker) {\n      const group = parseInt(marker[1], 10) | 0;\n      const id = marker[2];\n\n      if (group !== 0) {\n        // Rehydrate componentId to group index mapping\n        setGroupForId(id, group);\n        // Rehydrate names and rules\n        // looks like: data-styled.g11[id=\"idA\"]{content:\"nameA,\"}\n        rehydrateNamesFromContent(sheet, id, marker[3]);\n        sheet.getTag().insertRules(group, rules);\n      }\n\n      rules.length = 0;\n    } else {\n      rules.push(part);\n    }\n  }\n};\n\nexport const rehydrateSheet = (sheet: Sheet) => {\n  const nodes = document.querySelectorAll(SELECTOR);\n\n  for (let i = 0, l = nodes.length; i < l; i++) {\n    const node = nodes[i] as any as HTMLStyleElement;\n    if (node && node.getAttribute(SC_ATTR) !== SC_ATTR_ACTIVE) {\n      rehydrateSheetFromTag(sheet, node);\n\n      if (node.parentNode) {\n        node.parentNode.removeChild(node);\n      }\n    }\n  }\n};\n", "declare let __webpack_nonce__: string;\n\nexport default function getNonce() {\n  return typeof __webpack_nonce__ !== 'undefined' ? __webpack_nonce__ : null;\n}\n", "import { SC_ATTR, SC_ATTR_ACTIVE, SC_ATTR_VERSION, SC_VERSION } from '../constants';\nimport { InsertionTarget } from '../types';\nimport styledError from '../utils/error';\nimport getNonce from '../utils/nonce';\n\n/** Find last style element if any inside target */\nconst findLastStyleTag = (target: InsertionTarget): void | HTMLStyleElement => {\n  const arr = Array.from(target.querySelectorAll<HTMLStyleElement>(`style[${SC_ATTR}]`));\n\n  return arr[arr.length - 1];\n};\n\n/** Create a style element inside `target` or <head> after the last */\nexport const makeStyleTag = (target?: InsertionTarget | undefined): HTMLStyleElement => {\n  const head = document.head;\n  const parent = target || head;\n  const style = document.createElement('style');\n  const prevStyle = findLastStyleTag(parent);\n  const nextSibling = prevStyle !== undefined ? prevStyle.nextSibling : null;\n\n  style.setAttribute(SC_ATTR, SC_ATTR_ACTIVE);\n  style.setAttribute(SC_ATTR_VERSION, SC_VERSION);\n\n  const nonce = getNonce();\n\n  if (nonce) style.setAttribute('nonce', nonce);\n\n  parent.insertBefore(style, nextSibling);\n\n  return style;\n};\n\n/** Get the CSSStyleSheet instance for a given style element */\nexport const getSheet = (tag: HTMLStyleElement): CSSStyleSheet => {\n  if (tag.sheet) {\n    return tag.sheet as any as CSSStyleSheet;\n  }\n\n  // Avoid Firefox quirk where the style element might not have a sheet property\n  const { styleSheets } = document;\n  for (let i = 0, l = styleSheets.length; i < l; i++) {\n    const sheet = styleSheets[i];\n    if (sheet.ownerNode === tag) {\n      return sheet as any as CSSStyleSheet;\n    }\n  }\n\n  throw styledError(17);\n};\n", "import { InsertionTarget } from '../types';\nimport { getSheet, makeStyleTag } from './dom';\nimport { SheetOptions, Tag } from './types';\n\n/** Create a CSSStyleSheet-like tag depending on the environment */\nexport const makeTag = ({ isServer, useCSSOMInjection, target }: SheetOptions) => {\n  if (isServer) {\n    return new VirtualTag(target);\n  } else if (useCSSOMInjection) {\n    return new CSSOMTag(target);\n  } else {\n    return new TextTag(target);\n  }\n};\n\nexport const CSSOMTag = class CSSOMTag implements Tag {\n  element: HTMLStyleElement;\n\n  sheet: CSSStyleSheet;\n\n  length: number;\n\n  constructor(target?: InsertionTarget | undefined) {\n    this.element = makeStyleTag(target);\n\n    // Avoid Edge bug where empty style elements don't create sheets\n    this.element.appendChild(document.createTextNode(''));\n\n    this.sheet = getSheet(this.element);\n    this.length = 0;\n  }\n\n  insertRule(index: number, rule: string): boolean {\n    try {\n      this.sheet.insertRule(rule, index);\n      this.length++;\n      return true;\n    } catch (_error) {\n      return false;\n    }\n  }\n\n  deleteRule(index: number): void {\n    this.sheet.deleteRule(index);\n    this.length--;\n  }\n\n  getRule(index: number): string {\n    const rule = this.sheet.cssRules[index];\n\n    // Avoid IE11 quirk where cssText is inaccessible on some invalid rules\n    if (rule && rule.cssText) {\n      return rule.cssText;\n    } else {\n      return '';\n    }\n  }\n};\n\n/** A Tag that emulates the CSSStyleSheet API but uses text nodes */\nexport const TextTag = class TextTag implements Tag {\n  element: HTMLStyleElement;\n  nodes: NodeListOf<Node>;\n  length: number;\n\n  constructor(target?: InsertionTarget | undefined) {\n    this.element = makeStyleTag(target);\n    this.nodes = this.element.childNodes;\n    this.length = 0;\n  }\n\n  insertRule(index: number, rule: string) {\n    if (index <= this.length && index >= 0) {\n      const node = document.createTextNode(rule);\n      const refNode = this.nodes[index];\n      this.element.insertBefore(node, refNode || null);\n      this.length++;\n      return true;\n    } else {\n      return false;\n    }\n  }\n\n  deleteRule(index: number) {\n    this.element.removeChild(this.nodes[index]);\n    this.length--;\n  }\n\n  getRule(index: number) {\n    if (index < this.length) {\n      return this.nodes[index].textContent as string;\n    } else {\n      return '';\n    }\n  }\n};\n\n/** A completely virtual (server-side) Tag that doesn't manipulate the DOM */\nexport const VirtualTag = class VirtualTag implements Tag {\n  rules: string[];\n\n  length: number;\n\n  constructor(_target?: InsertionTarget | undefined) {\n    this.rules = [];\n    this.length = 0;\n  }\n\n  insertRule(index: number, rule: string) {\n    if (index <= this.length) {\n      this.rules.splice(index, 0, rule);\n      this.length++;\n      return true;\n    } else {\n      return false;\n    }\n  }\n\n  deleteRule(index: number) {\n    this.rules.splice(index, 1);\n    this.length--;\n  }\n\n  getRule(index: number) {\n    if (index < this.length) {\n      return this.rules[index];\n    } else {\n      return '';\n    }\n  }\n};\n", "import { DISABLE_SPEEDY, IS_BROWSER } from '../constants';\nimport { InsertionTarget } from '../types';\nimport { EMPTY_OBJECT } from '../utils/empties';\nimport { setToString } from '../utils/setToString';\nimport { makeGroupedTag } from './GroupedTag';\nimport { getGroupForId } from './GroupIDAllocator';\nimport { outputSheet, rehydrateSheet } from './Rehydration';\nimport { makeTag } from './Tag';\nimport { GroupedTag, Sheet, SheetOptions } from './types';\n\nlet SHOULD_REHYDRATE = IS_BROWSER;\n\ntype SheetConstructorArgs = {\n  isServer?: boolean;\n  useCSSOMInjection?: boolean;\n  target?: InsertionTarget | undefined;\n};\n\ntype GlobalStylesAllocationMap = {\n  [key: string]: number;\n};\ntype NamesAllocationMap = Map<string, Set<string>>;\n\nconst defaultOptions: SheetOptions = {\n  isServer: !IS_BROWSER,\n  useCSSOMInjection: !DISABLE_SPEEDY,\n};\n\n/** Contains the main stylesheet logic for stringification and caching */\nexport default class StyleSheet implements Sheet {\n  gs: GlobalStylesAllocationMap;\n  names: NamesAllocationMap;\n  options: SheetOptions;\n  server: boolean;\n  tag?: GroupedTag | undefined;\n\n  /** Register a group ID to give it an index */\n  static registerId(id: string): number {\n    return getGroupForId(id);\n  }\n\n  constructor(\n    options: SheetConstructorArgs = EMPTY_OBJECT as Object,\n    globalStyles: GlobalStylesAllocationMap = {},\n    names?: NamesAllocationMap | undefined\n  ) {\n    this.options = {\n      ...defaultOptions,\n      ...options,\n    };\n\n    this.gs = globalStyles;\n    this.names = new Map(names as NamesAllocationMap);\n    this.server = !!options.isServer;\n\n    // We rehydrate only once and use the sheet that is created first\n    if (!this.server && IS_BROWSER && SHOULD_REHYDRATE) {\n      SHOULD_REHYDRATE = false;\n      rehydrateSheet(this);\n    }\n\n    setToString(this, () => outputSheet(this));\n  }\n\n  rehydrate(): void {\n    if (!this.server && IS_BROWSER) {\n      rehydrateSheet(this);\n    }\n  }\n\n  reconstructWithOptions(options: SheetConstructorArgs, withNames = true) {\n    return new StyleSheet(\n      { ...this.options, ...options },\n      this.gs,\n      (withNames && this.names) || undefined\n    );\n  }\n\n  allocateGSInstance(id: string) {\n    return (this.gs[id] = (this.gs[id] || 0) + 1);\n  }\n\n  /** Lazily initialises a GroupedTag for when it's actually needed */\n  getTag() {\n    return this.tag || (this.tag = makeGroupedTag(makeTag(this.options)));\n  }\n\n  /** Check whether a name is known for caching */\n  hasNameForId(id: string, name: string): boolean {\n    return this.names.has(id) && (this.names.get(id) as any).has(name);\n  }\n\n  /** Mark a group's name as known for caching */\n  registerName(id: string, name: string) {\n    getGroupForId(id);\n\n    if (!this.names.has(id)) {\n      const groupNames = new Set<string>();\n      groupNames.add(name);\n      this.names.set(id, groupNames);\n    } else {\n      (this.names.get(id) as any).add(name);\n    }\n  }\n\n  /** Insert new rules which also marks the name as known */\n  insertRules(id: string, name: string, rules: string | string[]) {\n    this.registerName(id, name);\n    this.getTag().insertRules(getGroupForId(id), rules);\n  }\n\n  /** Clears all cached names for a given group ID */\n  clearNames(id: string) {\n    if (this.names.has(id)) {\n      (this.names.get(id) as any).clear();\n    }\n  }\n\n  /** Clears all rules for a given group ID */\n  clearRules(id: string) {\n    this.getTag().clearGroup(getGroupForId(id));\n    this.clearNames(id);\n  }\n\n  /** Clears the entire tag which deletes all rules but not its names */\n  clearTag() {\n    // NOTE: This does not clear the names, since it's only used during SSR\n    // so that we can continuously output only new rules\n    this.tag = undefined;\n  }\n}\n", "import * as stylis from 'stylis';\nimport { Stringifier } from '../types';\nimport { EMPTY_ARRAY, EMPTY_OBJECT } from './empties';\nimport throwStyledError from './error';\nimport { SEED, phash } from './hash';\n\nconst AMP_REGEX = /&/g;\nconst COMMENT_REGEX = /^\\s*\\/\\/.*$/gm;\n\nexport type ICreateStylisInstance = {\n  options?: { namespace?: string | undefined; prefix?: boolean | undefined } | undefined;\n  plugins?: stylis.Middleware[] | undefined;\n};\n\n/**\n * Takes an element and recurses through it's rules added the namespace to the start of each selector.\n * Takes into account media queries by recursing through child rules if they are present.\n */\nfunction recursivelySetNamepace(compiled: stylis.Element[], namespace: String): stylis.Element[] {\n  return compiled.map(rule => {\n    if (rule.type === 'rule') {\n      // add the namespace to the start\n      rule.value = `${namespace} ${rule.value}`;\n      // add the namespace after each comma for subsequent selectors.\n      rule.value = rule.value.replaceAll(',', `,${namespace} `);\n      rule.props = (rule.props as string[]).map(prop => {\n        return `${namespace} ${prop}`;\n      });\n    }\n\n    if (Array.isArray(rule.children) && rule.type !== '@keyframes') {\n      rule.children = recursivelySetNamepace(rule.children, namespace);\n    }\n    return rule;\n  });\n}\n\nexport default function createStylisInstance(\n  {\n    options = EMPTY_OBJECT as object,\n    plugins = EMPTY_ARRAY as unknown as stylis.Middleware[],\n  }: ICreateStylisInstance = EMPTY_OBJECT as object\n) {\n  let _componentId: string;\n  let _selector: string;\n  let _selectorRegexp: RegExp;\n\n  const selfReferenceReplacer = (match: string, offset: number, string: string) => {\n    if (\n      /**\n       * We only want to refer to the static class directly if the selector is part of a\n       * self-reference selector `& + & { color: red; }`\n       */\n      string.startsWith(_selector) &&\n      string.endsWith(_selector) &&\n      string.replaceAll(_selector, '').length > 0\n    ) {\n      return `.${_componentId}`;\n    }\n\n    return match;\n  };\n\n  /**\n   * When writing a style like\n   *\n   * & + & {\n   *   color: red;\n   * }\n   *\n   * The second ampersand should be a reference to the static component class. stylis\n   * has no knowledge of static class so we have to intelligently replace the base selector.\n   *\n   * https://github.com/thysultan/stylis.js/tree/v4.0.2#abstract-syntax-structure\n   */\n  const selfReferenceReplacementPlugin: stylis.Middleware = element => {\n    if (element.type === stylis.RULESET && element.value.includes('&')) {\n      (element.props as string[])[0] = element.props[0]\n        // catch any hanging references that stylis missed\n        .replace(AMP_REGEX, _selector)\n        .replace(_selectorRegexp, selfReferenceReplacer);\n    }\n  };\n\n  const middlewares = plugins.slice();\n\n  middlewares.push(selfReferenceReplacementPlugin);\n\n  /**\n   * Enables automatic vendor-prefixing for styles.\n   */\n  if (options.prefix) {\n    middlewares.push(stylis.prefixer);\n  }\n\n  middlewares.push(stylis.stringify);\n\n  const stringifyRules: Stringifier = (\n    css: string,\n    selector = '',\n    /**\n     * This \"prefix\" referes to a _selector_ prefix.\n     */\n    prefix = '',\n    componentId = '&'\n  ) => {\n    // stylis has no concept of state to be passed to plugins\n    // but since JS is single-threaded, we can rely on that to ensure\n    // these properties stay in sync with the current stylis run\n    _componentId = componentId;\n    _selector = selector;\n    _selectorRegexp = new RegExp(`\\\\${_selector}\\\\b`, 'g');\n\n    const flatCSS = css.replace(COMMENT_REGEX, '');\n    let compiled = stylis.compile(\n      prefix || selector ? `${prefix} ${selector} { ${flatCSS} }` : flatCSS\n    );\n\n    if (options.namespace) {\n      compiled = recursivelySetNamepace(compiled, options.namespace);\n    }\n\n    const stack: string[] = [];\n\n    stylis.serialize(\n      compiled,\n      stylis.middleware(middlewares.concat(stylis.rulesheet(value => stack.push(value))))\n    );\n\n    return stack;\n  };\n\n  stringifyRules.hash = plugins.length\n    ? plugins\n        .reduce((acc, plugin) => {\n          if (!plugin.name) {\n            throwStyledError(15);\n          }\n\n          return phash(acc, plugin.name);\n        }, SEED)\n        .toString()\n    : '';\n\n  return stringifyRules;\n}\n", "import React, { useContext, useEffect, useMemo, useState } from 'react';\nimport shallowequal from 'shallowequal';\nimport type stylis from 'stylis';\nimport StyleSheet from '../sheet';\nimport { InsertionTarget, ShouldForwardProp, Stringifier } from '../types';\nimport createStylisInstance from '../utils/stylis';\n\nexport const mainSheet: StyleSheet = new StyleSheet();\nexport const mainStylis: Stringifier = createStylisInstance();\n\nexport type IStyleSheetContext = {\n  shouldForwardProp?: ShouldForwardProp<'web'> | undefined;\n  styleSheet: StyleSheet;\n  stylis: Stringifier;\n};\n\nexport const StyleSheetContext = React.createContext<IStyleSheetContext>({\n  shouldForwardProp: undefined,\n  styleSheet: mainSheet,\n  stylis: mainStylis,\n});\n\nexport const StyleSheetConsumer = StyleSheetContext.Consumer;\n\nexport type IStylisContext = Stringifier | void;\nexport const StylisContext = React.createContext<IStylisContext>(undefined);\nexport const StylisConsumer = StylisContext.Consumer;\n\nexport function useStyleSheetContext() {\n  return useContext(StyleSheetContext);\n}\n\nexport type IStyleSheetManager = React.PropsWithChildren<{\n  /**\n   * If desired, you can pass this prop to disable \"speedy\" insertion mode, which\n   * uses the browser [CSSOM APIs](https://developer.mozilla.org/en-US/docs/Web/API/CSSStyleSheet).\n   * When disabled, rules are inserted as simple text into style blocks.\n   */\n  disableCSSOMInjection?: undefined | boolean;\n  /**\n   * If you are working exclusively with modern browsers, vendor prefixes can often be omitted\n   * to reduce the weight of CSS on the page.\n   */\n  enableVendorPrefixes?: undefined | boolean;\n  /**\n   * Provide an optional selector to be prepended to all generated style rules.\n   */\n  namespace?: undefined | string;\n  /**\n   * Create and provide your own `StyleSheet` if necessary for advanced SSR scenarios.\n   */\n  sheet?: undefined | StyleSheet;\n  /**\n   * Starting in v6, styled-components no longer does its own prop validation\n   * and recommends use of transient props \"$prop\" to pass style-only props to\n   * components. If for some reason you are not able to use transient props, a\n   * prop validation function can be provided via `StyleSheetManager`, such as\n   * `@emotion/is-prop-valid`.\n   *\n   * When the return value is `true`, props will be forwarded to the DOM/underlying\n   * component. If return value is `false`, the prop will be discarded after styles\n   * are calculated.\n   *\n   * Manually composing `styled.{element}.withConfig({shouldForwardProp})` will\n   * override this default.\n   */\n  shouldForwardProp?: undefined | IStyleSheetContext['shouldForwardProp'];\n  /**\n   * An array of plugins to be run by stylis (style processor) during compilation.\n   * Check out [what's available on npm*](https://www.npmjs.com/search?q=keywords%3Astylis).\n   *\n   * \\* The plugin(s) must be compatible with stylis v4 or above.\n   */\n  stylisPlugins?: undefined | stylis.Middleware[];\n  /**\n   * Provide an alternate DOM node to host generated styles; useful for iframes.\n   */\n  target?: undefined | InsertionTarget;\n}>;\n\nexport function StyleSheetManager(props: IStyleSheetManager): React.JSX.Element {\n  const [plugins, setPlugins] = useState(props.stylisPlugins);\n  const { styleSheet } = useStyleSheetContext();\n\n  const resolvedStyleSheet = useMemo(() => {\n    let sheet = styleSheet;\n\n    if (props.sheet) {\n      sheet = props.sheet;\n    } else if (props.target) {\n      sheet = sheet.reconstructWithOptions({ target: props.target }, false);\n    }\n\n    if (props.disableCSSOMInjection) {\n      sheet = sheet.reconstructWithOptions({ useCSSOMInjection: false });\n    }\n\n    return sheet;\n  }, [props.disableCSSOMInjection, props.sheet, props.target, styleSheet]);\n\n  const stylis = useMemo(\n    () =>\n      createStylisInstance({\n        options: { namespace: props.namespace, prefix: props.enableVendorPrefixes },\n        plugins,\n      }),\n    [props.enableVendorPrefixes, props.namespace, plugins]\n  );\n\n  useEffect(() => {\n    if (!shallowequal(plugins, props.stylisPlugins)) setPlugins(props.stylisPlugins);\n  }, [props.stylisPlugins]);\n\n  const styleSheetContextValue = useMemo(\n    () => ({\n      shouldForwardProp: props.shouldForwardProp,\n      styleSheet: resolvedStyleSheet,\n      stylis,\n    }),\n    [props.shouldForwardProp, resolvedStyleSheet, stylis]\n  );\n\n  return (\n    <StyleSheetContext.Provider value={styleSheetContextValue}>\n      <StylisContext.Provider value={stylis}>{props.children}</StylisContext.Provider>\n    </StyleSheetContext.Provider>\n  );\n}\n", "import StyleSheet from '../sheet';\nimport { Keyframes as KeyframesType, Stringifier } from '../types';\nimport styledError from '../utils/error';\nimport { setToString } from '../utils/setToString';\nimport { mainStylis } from './StyleSheetManager';\n\nexport default class Keyframes implements KeyframesType {\n  id: string;\n  name: string;\n  rules: string;\n\n  constructor(name: string, rules: string) {\n    this.name = name;\n    this.id = `sc-keyframes-${name}`;\n    this.rules = rules;\n\n    setToString(this, () => {\n      throw styledError(12, String(this.name));\n    });\n  }\n\n  inject = (styleSheet: StyleSheet, stylisInstance: Stringifier = mainStylis): void => {\n    const resolvedName = this.name + stylisInstance.hash;\n\n    if (!styleSheet.hasNameForId(this.id, resolvedName)) {\n      styleSheet.insertRules(\n        this.id,\n        resolvedName,\n        stylisInstance(this.rules, resolvedName, '@keyframes')\n      );\n    }\n  };\n\n  getName(stylisInstance: Stringifier = mainStylis): string {\n    return this.name + stylisInstance.hash;\n  }\n}\n", "const isUpper = (c: string) => c >= 'A' && c <= 'Z';\n\n/**\n * Hyphenates a camelcased CSS property name, for example:\n *\n *   > hyphenateStyleName('backgroundColor')\n *   < \"background-color\"\n *   > hyphenateStyleName('MozTransition')\n *   < \"-moz-transition\"\n *   > hyphenateStyleName('msTransition')\n *   < \"-ms-transition\"\n *\n * As Modernizr suggests (http://modernizr.com/docs/#prefixed), an `ms` prefix\n * is converted to `-ms-`.\n */\nexport default function hyphenateStyleName(string: string): string {\n  let output = '';\n\n  for (let i = 0; i < string.length; i++) {\n    const c = string[i];\n    // Check for CSS variable prefix\n    if (i === 1 && c === '-' && string[0] === '-') {\n      return string;\n    }\n\n    if (isUpper(c)) {\n      output += '-' + c.toLowerCase();\n    } else {\n      output += c;\n    }\n  }\n\n  return output.startsWith('ms-') ? '-' + output : output;\n}\n", "import Keyframes from '../models/Keyframes';\nimport StyleSheet from '../sheet';\nimport {\n  AnyComponent,\n  Dict,\n  ExecutionContext,\n  Interpolation,\n  IStyledComponent,\n  RuleSet,\n  Stringifier,\n  StyledObject,\n} from '../types';\nimport addUnitIfNeeded from './addUnitIfNeeded';\nimport { EMPTY_ARRAY } from './empties';\nimport getComponentName from './getComponentName';\nimport hyphenate from './hyphenateStyleName';\nimport isFunction from './isFunction';\nimport isPlainObject from './isPlainObject';\nimport isStatelessFunction from './isStatelessFunction';\nimport isStyledComponent from './isStyledComponent';\n\n/**\n * It's falsish not falsy because 0 is allowed.\n */\nconst isFalsish = (chunk: any): chunk is undefined | null | false | '' =>\n  chunk === undefined || chunk === null || chunk === false || chunk === '';\n\nexport const objToCssArray = (obj: Dict<any>): string[] => {\n  const rules = [];\n\n  for (const key in obj) {\n    const val = obj[key];\n    if (!obj.hasOwnProperty(key) || isFalsish(val)) continue;\n\n    // @ts-expect-error Property 'isCss' does not exist on type 'any[]'\n    if ((Array.isArray(val) && val.isCss) || isFunction(val)) {\n      rules.push(`${hyphenate(key)}:`, val, ';');\n    } else if (isPlainObject(val)) {\n      rules.push(`${key} {`, ...objToCssArray(val), '}');\n    } else {\n      rules.push(`${hyphenate(key)}: ${addUnitIfNeeded(key, val)};`);\n    }\n  }\n\n  return rules;\n};\n\nexport default function flatten<Props extends object>(\n  chunk: Interpolation<object>,\n  executionContext?: (ExecutionContext & Props) | undefined,\n  styleSheet?: StyleSheet | undefined,\n  stylisInstance?: Stringifier | undefined\n): RuleSet<Props> {\n  if (isFalsish(chunk)) {\n    return [];\n  }\n\n  /* Handle other components */\n  if (isStyledComponent(chunk)) {\n    return [`.${(chunk as unknown as IStyledComponent<'web', any>).styledComponentId}`];\n  }\n\n  /* Either execute or defer the function */\n  if (isFunction(chunk)) {\n    if (isStatelessFunction(chunk) && executionContext) {\n      const result = chunk(executionContext);\n\n      if (\n        process.env.NODE_ENV !== 'production' &&\n        typeof result === 'object' &&\n        !Array.isArray(result) &&\n        !(result instanceof Keyframes) &&\n        !isPlainObject(result) &&\n        result !== null\n      ) {\n        console.error(\n          `${getComponentName(\n            chunk as AnyComponent\n          )} is not a styled component and cannot be referred to via component selector. See https://www.styled-components.com/docs/advanced#referring-to-other-components for more details.`\n        );\n      }\n\n      return flatten<Props>(result, executionContext, styleSheet, stylisInstance);\n    } else {\n      return [chunk as unknown as IStyledComponent<'web'>];\n    }\n  }\n\n  if (chunk instanceof Keyframes) {\n    if (styleSheet) {\n      chunk.inject(styleSheet, stylisInstance);\n      return [chunk.getName(stylisInstance)];\n    } else {\n      return [chunk];\n    }\n  }\n\n  /* Handle objects */\n  if (isPlainObject(chunk)) {\n    return objToCssArray(chunk as StyledObject<Props>);\n  }\n\n  if (!Array.isArray(chunk)) {\n    return [chunk.toString()];\n  }\n\n  return flatMap(chunk, chunklet =>\n    flatten<Props>(chunklet, executionContext, styleSheet, stylisInstance)\n  );\n}\n\nfunction flatMap<T, U>(array: T[], transform: (value: T, index: number, array: T[]) => U[]): U[] {\n  return Array.prototype.concat.apply(EMPTY_ARRAY, array.map(transform));\n}\n", "import unitless from '@emotion/unitless';\n\n// Taken from https://github.com/facebook/react/blob/b87aabdfe1b7461e7331abb3601d9e6bb27544bc/packages/react-dom/src/shared/dangerousStyleValue.js\nexport default function addUnitIfNeeded(name: string, value: any) {\n  // https://github.com/amilajack/eslint-plugin-flowtype-errors/issues/133\n  if (value == null || typeof value === 'boolean' || value === '') {\n    return '';\n  }\n\n  if (typeof value === 'number' && value !== 0 && !(name in unitless) && !name.startsWith('--')) {\n    return `${value}px`; // Presumes implicit 'px' suffix for unitless numbers except for CSS variables\n  }\n\n  return String(value).trim();\n}\n", "import isFunction from './isFunction';\n\nexport default function isStatelessFunction(test: any): test is Function {\n  return isFunction(test) && !(test.prototype && test.prototype.isReactComponent);\n}\n", "import { RuleSet } from '../types';\nimport isFunction from './isFunction';\nimport isStyledComponent from './isStyledComponent';\n\nexport default function isStaticRules<Props extends object>(rules: RuleSet<Props>) {\n  for (let i = 0; i < rules.length; i += 1) {\n    const rule = rules[i];\n\n    if (isFunction(rule) && !isStyledComponent(rule)) {\n      // functions are allowed to be static if they're just being\n      // used to get the classname of a nested styled component\n      return false;\n    }\n  }\n\n  return true;\n}\n", "import { SC_VERSION } from '../constants';\nimport StyleSheet from '../sheet';\nimport { ExecutionContext, RuleSet, Stringifier } from '../types';\nimport flatten from '../utils/flatten';\nimport generateName from '../utils/generateAlphabeticName';\nimport { hash, phash } from '../utils/hash';\nimport isStaticRules from '../utils/isStaticRules';\nimport { joinStringArray, joinStrings } from '../utils/joinStrings';\n\nconst SEED = hash(SC_VERSION);\n\n/**\n * ComponentStyle is all the CSS-specific stuff, not the React-specific stuff.\n */\nexport default class ComponentStyle {\n  baseHash: number;\n  baseStyle: ComponentStyle | null | undefined;\n  componentId: string;\n  isStatic: boolean;\n  rules: RuleSet<any>;\n  staticRulesId: string;\n\n  constructor(rules: RuleSet<any>, componentId: string, baseStyle?: ComponentStyle | undefined) {\n    this.rules = rules;\n    this.staticRulesId = '';\n    this.isStatic =\n      process.env.NODE_ENV === 'production' &&\n      (baseStyle === undefined || baseStyle.isStatic) &&\n      isStaticRules(rules);\n    this.componentId = componentId;\n    this.baseHash = phash(SEED, componentId);\n    this.baseStyle = baseStyle;\n\n    // NOTE: This registers the componentId, which ensures a consistent order\n    // for this component's styles compared to others\n    StyleSheet.registerId(componentId);\n  }\n\n  generateAndInjectStyles(\n    executionContext: ExecutionContext,\n    styleSheet: StyleSheet,\n    stylis: Stringifier\n  ): string {\n    let names = this.baseStyle\n      ? this.baseStyle.generateAndInjectStyles(executionContext, styleSheet, stylis)\n      : '';\n\n    // force dynamic classnames if user-supplied stylis plugins are in use\n    if (this.isStatic && !stylis.hash) {\n      if (this.staticRulesId && styleSheet.hasNameForId(this.componentId, this.staticRulesId)) {\n        names = joinStrings(names, this.staticRulesId);\n      } else {\n        const cssStatic = joinStringArray(\n          flatten(this.rules, executionContext, styleSheet, stylis) as string[]\n        );\n        const name = generateName(phash(this.baseHash, cssStatic) >>> 0);\n\n        if (!styleSheet.hasNameForId(this.componentId, name)) {\n          const cssStaticFormatted = stylis(cssStatic, `.${name}`, undefined, this.componentId);\n          styleSheet.insertRules(this.componentId, name, cssStaticFormatted);\n        }\n\n        names = joinStrings(names, name);\n        this.staticRulesId = name;\n      }\n    } else {\n      let dynamicHash = phash(this.baseHash, stylis.hash);\n      let css = '';\n\n      for (let i = 0; i < this.rules.length; i++) {\n        const partRule = this.rules[i];\n\n        if (typeof partRule === 'string') {\n          css += partRule;\n\n          if (process.env.NODE_ENV !== 'production') dynamicHash = phash(dynamicHash, partRule);\n        } else if (partRule) {\n          const partString = joinStringArray(\n            flatten(partRule, executionContext, styleSheet, stylis) as string[]\n          );\n          // The same value can switch positions in the array, so we include \"i\" in the hash.\n          dynamicHash = phash(dynamicHash, partString + i);\n          css += partString;\n        }\n      }\n\n      if (css) {\n        const name = generateName(dynamicHash >>> 0);\n\n        if (!styleSheet.hasNameForId(this.componentId, name)) {\n          styleSheet.insertRules(\n            this.componentId,\n            name,\n            stylis(css, `.${name}`, undefined, this.componentId)\n          );\n        }\n\n        names = joinStrings(names, name);\n      }\n    }\n\n    return names;\n  }\n}\n", "import React, { useContext, useMemo } from 'react';\nimport styledError from '../utils/error';\nimport isFunction from '../utils/isFunction';\n\n// Helper type for the `DefaultTheme` interface that enforces an object type & exclusively allows\n// for typed keys.\ntype DefaultThemeAsObject<T = object> = Record<keyof T, any>;\n\n/**\n * Override DefaultTheme to get accurate typings for your project.\n *\n * ```\n * // create styled-components.d.ts in your project source\n * // if it isn't being picked up, check tsconfig compilerOptions.types\n * import type { CSSProp } from \"styled-components\";\n * import Theme from './theme';\n *\n * type ThemeType = typeof Theme;\n *\n * declare module \"styled-components\" {\n *  export interface DefaultTheme extends ThemeType {}\n * }\n *\n * declare module \"react\" {\n *  interface DOMAttributes<T> {\n *    css?: CSSProp;\n *  }\n * }\n * ```\n */\nexport interface DefaultTheme extends DefaultThemeAsObject {}\n\ntype ThemeFn = (outerTheme?: DefaultTheme | undefined) => DefaultTheme;\ntype ThemeArgument = DefaultTheme | ThemeFn;\n\ntype Props = {\n  children?: React.ReactNode;\n  theme: ThemeArgument;\n};\n\nexport const ThemeContext = React.createContext<DefaultTheme | undefined>(undefined);\n\nexport const ThemeConsumer = ThemeContext.Consumer;\n\nfunction mergeTheme(theme: ThemeArgument, outerTheme?: DefaultTheme | undefined): DefaultTheme {\n  if (!theme) {\n    throw styledError(14);\n  }\n\n  if (isFunction(theme)) {\n    const themeFn = theme as ThemeFn;\n    const mergedTheme = themeFn(outerTheme);\n\n    if (\n      process.env.NODE_ENV !== 'production' &&\n      (mergedTheme === null || Array.isArray(mergedTheme) || typeof mergedTheme !== 'object')\n    ) {\n      throw styledError(7);\n    }\n\n    return mergedTheme;\n  }\n\n  if (Array.isArray(theme) || typeof theme !== 'object') {\n    throw styledError(8);\n  }\n\n  return outerTheme ? { ...outerTheme, ...theme } : theme;\n}\n\n/**\n * Returns the current theme (as provided by the closest ancestor `ThemeProvider`.)\n *\n * If no `ThemeProvider` is found, the function will error. If you need access to the theme in an\n * uncertain composition scenario, `React.useContext(ThemeContext)` will not emit an error if there\n * is no `ThemeProvider` ancestor.\n */\nexport function useTheme(): DefaultTheme {\n  const theme = useContext(ThemeContext);\n\n  if (!theme) {\n    throw styledError(18);\n  }\n\n  return theme;\n}\n\n/**\n * Provide a theme to an entire react component tree via context\n */\nexport default function ThemeProvider(props: Props): React.JSX.Element | null {\n  const outerTheme = React.useContext(ThemeContext);\n  const themeContext = useMemo(\n    () => mergeTheme(props.theme, outerTheme),\n    [props.theme, outerTheme]\n  );\n\n  if (!props.children) {\n    return null;\n  }\n\n  return <ThemeContext.Provider value={themeContext}>{props.children}</ThemeContext.Provider>;\n}\n", "import isPropValid from '@emotion/is-prop-valid';\nimport React, { createElement, Ref, useDebugValue } from 'react';\nimport { SC_VERSION } from '../constants';\nimport type {\n  AnyComponent,\n  Attrs,\n  BaseObject,\n  Dict,\n  ExecutionContext,\n  ExecutionProps,\n  IStyledComponent,\n  IStyledComponentFactory,\n  IStyledStatics,\n  OmitNever,\n  RuleSet,\n  StyledOptions,\n  WebTarget,\n} from '../types';\nimport { checkDynamicCreation } from '../utils/checkDynamicCreation';\nimport createWarnTooManyClasses from '../utils/createWarnTooManyClasses';\nimport determineTheme from '../utils/determineTheme';\nimport domElements from '../utils/domElements';\nimport { EMPTY_ARRAY, EMPTY_OBJECT } from '../utils/empties';\nimport escape from '../utils/escape';\nimport generateComponentId from '../utils/generateComponentId';\nimport generateDisplayName from '../utils/generateDisplayName';\nimport hoist from '../utils/hoist';\nimport isFunction from '../utils/isFunction';\nimport isStyledComponent from '../utils/isStyledComponent';\nimport isTag from '../utils/isTag';\nimport { joinStrings } from '../utils/joinStrings';\nimport merge from '../utils/mixinDeep';\nimport { setToString } from '../utils/setToString';\nimport ComponentStyle from './ComponentStyle';\nimport { useStyleSheetContext } from './StyleSheetManager';\nimport { DefaultTheme, ThemeContext } from './ThemeProvider';\n\nconst identifiers: { [key: string]: number } = {};\n\n/* We depend on components having unique IDs */\nfunction generateId(\n  displayName?: string | undefined,\n  parentComponentId?: string | undefined\n): string {\n  const name = typeof displayName !== 'string' ? 'sc' : escape(displayName);\n  // Ensure that no displayName can lead to duplicate componentIds\n  identifiers[name] = (identifiers[name] || 0) + 1;\n\n  const componentId = `${name}-${generateComponentId(\n    // SC_VERSION gives us isolation between multiple runtimes on the page at once\n    // this is improved further with use of the babel plugin \"namespace\" feature\n    SC_VERSION + name + identifiers[name]\n  )}`;\n\n  return parentComponentId ? `${parentComponentId}-${componentId}` : componentId;\n}\n\nfunction useInjectedStyle<T extends ExecutionContext>(\n  componentStyle: ComponentStyle,\n  resolvedAttrs: T\n) {\n  const ssc = useStyleSheetContext();\n\n  const className = componentStyle.generateAndInjectStyles(\n    resolvedAttrs,\n    ssc.styleSheet,\n    ssc.stylis\n  );\n\n  if (process.env.NODE_ENV !== 'production') useDebugValue(className);\n\n  return className;\n}\n\nfunction resolveContext<Props extends object>(\n  attrs: Attrs<React.HTMLAttributes<Element> & Props>[],\n  props: React.HTMLAttributes<Element> & ExecutionProps & Props,\n  theme: DefaultTheme\n) {\n  const context: React.HTMLAttributes<Element> &\n    ExecutionContext &\n    Props & { [key: string]: any; class?: string; ref?: React.Ref<any> } = {\n    ...props,\n    // unset, add `props.className` back at the end so props always \"wins\"\n    className: undefined,\n    theme,\n  };\n  let attrDef;\n\n  for (let i = 0; i < attrs.length; i += 1) {\n    attrDef = attrs[i];\n    const resolvedAttrDef = isFunction(attrDef) ? attrDef(context) : attrDef;\n\n    for (const key in resolvedAttrDef) {\n      context[key as keyof typeof context] =\n        key === 'className'\n          ? joinStrings(context[key] as string | undefined, resolvedAttrDef[key] as string)\n          : key === 'style'\n            ? { ...context[key], ...resolvedAttrDef[key] }\n            : resolvedAttrDef[key as keyof typeof resolvedAttrDef];\n    }\n  }\n\n  if (props.className) {\n    context.className = joinStrings(context.className, props.className);\n  }\n\n  return context;\n}\n\nlet seenUnknownProps = new Set();\n\nfunction useStyledComponentImpl<Props extends object>(\n  forwardedComponent: IStyledComponent<'web', Props>,\n  props: ExecutionProps & Props,\n  forwardedRef: Ref<Element>\n) {\n  const {\n    attrs: componentAttrs,\n    componentStyle,\n    defaultProps,\n    foldedComponentIds,\n    styledComponentId,\n    target,\n  } = forwardedComponent;\n\n  const contextTheme = React.useContext(ThemeContext);\n  const ssc = useStyleSheetContext();\n  const shouldForwardProp = forwardedComponent.shouldForwardProp || ssc.shouldForwardProp;\n\n  if (process.env.NODE_ENV !== 'production') useDebugValue(styledComponentId);\n\n  // NOTE: the non-hooks version only subscribes to this when !componentStyle.isStatic,\n  // but that'd be against the rules-of-hooks. We could be naughty and do it anyway as it\n  // should be an immutable value, but behave for now.\n  const theme = determineTheme(props, contextTheme, defaultProps) || EMPTY_OBJECT;\n\n  const context = resolveContext<Props>(componentAttrs, props, theme);\n  const elementToBeCreated: WebTarget = context.as || target;\n  const propsForElement: Dict<any> = {};\n\n  for (const key in context) {\n    if (context[key] === undefined) {\n      // Omit undefined values from props passed to wrapped element.\n      // This enables using .attrs() to remove props, for example.\n    } else if (key[0] === '$' || key === 'as' || (key === 'theme' && context.theme === theme)) {\n      // Omit transient props and execution props.\n    } else if (key === 'forwardedAs') {\n      propsForElement.as = context.forwardedAs;\n    } else if (!shouldForwardProp || shouldForwardProp(key, elementToBeCreated)) {\n      propsForElement[key] = context[key];\n\n      if (\n        !shouldForwardProp &&\n        process.env.NODE_ENV === 'development' &&\n        !isPropValid(key) &&\n        !seenUnknownProps.has(key) &&\n        // Only warn on DOM Element.\n        domElements.has(elementToBeCreated as any)\n      ) {\n        seenUnknownProps.add(key);\n        console.warn(\n          `styled-components: it looks like an unknown prop \"${key}\" is being sent through to the DOM, which will likely trigger a React console error. If you would like automatic filtering of unknown props, you can opt-into that behavior via \\`<StyleSheetManager shouldForwardProp={...}>\\` (connect an API like \\`@emotion/is-prop-valid\\`) or consider using transient props (\\`$\\` prefix for automatic filtering.)`\n        );\n      }\n    }\n  }\n\n  const generatedClassName = useInjectedStyle(componentStyle, context);\n\n  if (process.env.NODE_ENV !== 'production' && forwardedComponent.warnTooManyClasses) {\n    forwardedComponent.warnTooManyClasses(generatedClassName);\n  }\n\n  let classString = joinStrings(foldedComponentIds, styledComponentId);\n  if (generatedClassName) {\n    classString += ' ' + generatedClassName;\n  }\n  if (context.className) {\n    classString += ' ' + context.className;\n  }\n\n  propsForElement[\n    // handle custom elements which React doesn't properly alias\n    isTag(elementToBeCreated) &&\n    !domElements.has(elementToBeCreated as Extract<typeof domElements, string>)\n      ? 'class'\n      : 'className'\n  ] = classString;\n\n  // forwardedRef is coming from React.forwardRef.\n  // But it might not exist. Since React 19 handles `ref` like a prop, it only define it if there is a value.\n  // We don't want to inject an empty ref.\n  if (forwardedRef) {\n    propsForElement.ref = forwardedRef;\n  }\n\n  return createElement(elementToBeCreated, propsForElement);\n}\n\nfunction createStyledComponent<\n  Target extends WebTarget,\n  OuterProps extends object,\n  Statics extends object = BaseObject,\n>(\n  target: Target,\n  options: StyledOptions<'web', OuterProps>,\n  rules: RuleSet<OuterProps>\n): ReturnType<IStyledComponentFactory<'web', Target, OuterProps, Statics>> {\n  const isTargetStyledComp = isStyledComponent(target);\n  const styledComponentTarget = target as IStyledComponent<'web', OuterProps>;\n  const isCompositeComponent = !isTag(target);\n\n  const {\n    attrs = EMPTY_ARRAY,\n    componentId = generateId(options.displayName, options.parentComponentId),\n    displayName = generateDisplayName(target),\n  } = options;\n\n  const styledComponentId =\n    options.displayName && options.componentId\n      ? `${escape(options.displayName)}-${options.componentId}`\n      : options.componentId || componentId;\n\n  // fold the underlying StyledComponent attrs up (implicit extend)\n  const finalAttrs =\n    isTargetStyledComp && styledComponentTarget.attrs\n      ? styledComponentTarget.attrs.concat(attrs as unknown as Attrs<OuterProps>[]).filter(Boolean)\n      : (attrs as Attrs<OuterProps>[]);\n\n  let { shouldForwardProp } = options;\n\n  if (isTargetStyledComp && styledComponentTarget.shouldForwardProp) {\n    const shouldForwardPropFn = styledComponentTarget.shouldForwardProp;\n\n    if (options.shouldForwardProp) {\n      const passedShouldForwardPropFn = options.shouldForwardProp;\n\n      // compose nested shouldForwardProp calls\n      shouldForwardProp = (prop, elementToBeCreated) =>\n        shouldForwardPropFn(prop, elementToBeCreated) &&\n        passedShouldForwardPropFn(prop, elementToBeCreated);\n    } else {\n      shouldForwardProp = shouldForwardPropFn;\n    }\n  }\n\n  const componentStyle = new ComponentStyle(\n    rules,\n    styledComponentId,\n    isTargetStyledComp ? (styledComponentTarget.componentStyle as ComponentStyle) : undefined\n  );\n\n  function forwardRefRender(props: ExecutionProps & OuterProps, ref: Ref<Element>) {\n    return useStyledComponentImpl<OuterProps>(WrappedStyledComponent, props, ref);\n  }\n\n  forwardRefRender.displayName = displayName;\n\n  /**\n   * forwardRef creates a new interim component, which we'll take advantage of\n   * instead of extending ParentComponent to create _another_ interim class\n   */\n  let WrappedStyledComponent = React.forwardRef(forwardRefRender) as unknown as IStyledComponent<\n    'web',\n    any\n  > &\n    Statics;\n  WrappedStyledComponent.attrs = finalAttrs;\n  WrappedStyledComponent.componentStyle = componentStyle;\n  WrappedStyledComponent.displayName = displayName;\n  WrappedStyledComponent.shouldForwardProp = shouldForwardProp;\n\n  // this static is used to preserve the cascade of static classes for component selector\n  // purposes; this is especially important with usage of the css prop\n  WrappedStyledComponent.foldedComponentIds = isTargetStyledComp\n    ? joinStrings(styledComponentTarget.foldedComponentIds, styledComponentTarget.styledComponentId)\n    : '';\n\n  WrappedStyledComponent.styledComponentId = styledComponentId;\n\n  // fold the underlying StyledComponent target up since we folded the styles\n  WrappedStyledComponent.target = isTargetStyledComp ? styledComponentTarget.target : target;\n\n  Object.defineProperty(WrappedStyledComponent, 'defaultProps', {\n    get() {\n      return this._foldedDefaultProps;\n    },\n\n    set(obj) {\n      this._foldedDefaultProps = isTargetStyledComp\n        ? merge({}, styledComponentTarget.defaultProps, obj)\n        : obj;\n    },\n  });\n\n  if (process.env.NODE_ENV !== 'production') {\n    checkDynamicCreation(displayName, styledComponentId);\n\n    WrappedStyledComponent.warnTooManyClasses = createWarnTooManyClasses(\n      displayName,\n      styledComponentId\n    );\n  }\n\n  setToString(WrappedStyledComponent, () => `.${WrappedStyledComponent.styledComponentId}`);\n\n  if (isCompositeComponent) {\n    const compositeComponentTarget = target as AnyComponent;\n\n    hoist<typeof WrappedStyledComponent, typeof compositeComponentTarget>(\n      WrappedStyledComponent,\n      compositeComponentTarget,\n      {\n        // all SC-specific things should not be hoisted\n        attrs: true,\n        componentStyle: true,\n        displayName: true,\n        foldedComponentIds: true,\n        shouldForwardProp: true,\n        styledComponentId: true,\n        target: true,\n      } as { [key in keyof OmitNever<IStyledStatics<'web', OuterProps>>]: true }\n    );\n  }\n\n  return WrappedStyledComponent;\n}\n\nexport default createStyledComponent;\n", "import { StyledTarget } from '../types';\nimport getComponentName from './getComponentName';\nimport isTag from './isTag';\n\nexport default function generateDisplayName(target: StyledTarget<any>) {\n  return isTag(target) ? `styled.${target}` : `Styled(${getComponentName(target)})`;\n}\n", "import { Dict } from '../types';\n\nexport const LIMIT = 200;\n\nexport default (displayName: string, componentId: string) => {\n  let generatedClasses: Dict<any> = {};\n  let warningSeen = false;\n\n  return (className: string) => {\n    if (!warningSeen) {\n      generatedClasses[className] = true;\n      if (Object.keys(generatedClasses).length >= LIMIT) {\n        // Unable to find latestRule in test environment.\n\n        const parsedIdString = componentId ? ` with the id of \"${componentId}\"` : '';\n\n        console.warn(\n          `Over ${LIMIT} classes were generated for component ${displayName}${parsedIdString}.\\n` +\n            'Consider using the attrs method, together with a style object for frequently changed styles.\\n' +\n            'Example:\\n' +\n            '  const Component = styled.div.attrs(props => ({\\n' +\n            '    style: {\\n' +\n            '      background: props.background,\\n' +\n            '    },\\n' +\n            '  }))`width: 100%;`\\n\\n' +\n            '  <Component />'\n        );\n        warningSeen = true;\n        generatedClasses = {};\n      }\n    }\n  };\n};\n", "import { Interpolation } from '../types';\n\nexport default function interleave<Props extends object>(\n  strings: readonly string[],\n  interpolations: Interpolation<Props>[]\n): Interpolation<Props>[] {\n  const result: Interpolation<Props>[] = [strings[0]];\n\n  for (let i = 0, len = interpolations.length; i < len; i += 1) {\n    result.push(interpolations[i], strings[i + 1]);\n  }\n\n  return result;\n}\n", "import {\n  BaseObject,\n  Interpolation,\n  NoInfer,\n  RuleSet,\n  StyledObject,\n  StyleFunction,\n  Styles,\n} from '../types';\nimport { EMPTY_ARRAY } from '../utils/empties';\nimport flatten from '../utils/flatten';\nimport interleave from '../utils/interleave';\nimport isFunction from '../utils/isFunction';\nimport isPlainObject from '../utils/isPlainObject';\n\n/**\n * Used when flattening object styles to determine if we should\n * expand an array of styles.\n */\nconst addTag = <T extends RuleSet<any>>(arg: T): T & { isCss: true } =>\n  Object.assign(arg, { isCss: true } as const);\n\nfunction css(styles: Styles<object>, ...interpolations: Interpolation<object>[]): RuleSet<object>;\nfunction css<Props extends object>(\n  styles: Styles<NoInfer<Props>>,\n  ...interpolations: Interpolation<NoInfer<Props>>[]\n): RuleSet<NoInfer<Props>>;\nfunction css<Props extends object = BaseObject>(\n  styles: Styles<NoInfer<Props>>,\n  ...interpolations: Interpolation<NoInfer<Props>>[]\n): RuleSet<NoInfer<Props>> {\n  if (isFunction(styles) || isPlainObject(styles)) {\n    const styleFunctionOrObject = styles as StyleFunction<Props> | StyledObject<Props>;\n\n    return addTag(\n      flatten<Props>(\n        interleave<Props>(EMPTY_ARRAY, [\n          styleFunctionOrObject,\n          ...interpolations,\n        ]) as Interpolation<object>\n      )\n    );\n  }\n\n  const styleStringArray = styles as TemplateStringsArray;\n\n  if (\n    interpolations.length === 0 &&\n    styleStringArray.length === 1 &&\n    typeof styleStringArray[0] === 'string'\n  ) {\n    return flatten<Props>(styleStringArray);\n  }\n\n  return addTag(\n    flatten<Props>(interleave<Props>(styleStringArray, interpolations) as Interpolation<object>)\n  );\n}\n\nexport default css;\n", "import {\n  Attrs,\n  BaseObject,\n  ExecutionProps,\n  Interpolation,\n  IStyledComponent,\n  IStyledComponentFactory,\n  KnownTarget,\n  NoInfer,\n  Runtime,\n  StyledOptions,\n  StyledTarget,\n  Styles,\n  Substitute,\n} from '../types';\nimport { EMPTY_OBJECT } from '../utils/empties';\nimport styledError from '../utils/error';\nimport css from './css';\n\ntype AttrsResult<T extends Attrs<any>> = T extends (...args: any) => infer P\n  ? P extends object\n    ? P\n    : never\n  : T extends object\n    ? T\n    : never;\n\n/**\n * Based on Attrs being a simple object or function that returns\n * a prop object, inspect the attrs result and attempt to extract\n * any \"as\" prop usage to modify the runtime target.\n */\ntype AttrsTarget<\n  R extends Runtime,\n  T extends Attrs<any>,\n  FallbackTarget extends StyledTarget<R>,\n  Result extends ExecutionProps = AttrsResult<T>,\n> = Result extends { as: infer RuntimeTarget }\n  ? RuntimeTarget extends KnownTarget\n    ? RuntimeTarget\n    : FallbackTarget\n  : FallbackTarget;\n\nexport interface Styled<\n  R extends Runtime,\n  Target extends StyledTarget<R>,\n  OuterProps extends object,\n  OuterStatics extends object = BaseObject,\n> {\n  <Props extends object = BaseObject, Statics extends object = BaseObject>(\n    initialStyles: Styles<Substitute<OuterProps, NoInfer<Props>>>,\n    ...interpolations: Interpolation<Substitute<OuterProps, NoInfer<Props>>>[]\n  ): IStyledComponent<R, Substitute<OuterProps, Props>> &\n    OuterStatics &\n    Statics &\n    (R extends 'web'\n      ? Target extends string\n        ? {}\n        : Omit<Target, keyof React.Component<any>>\n      : {});\n\n  attrs: <\n    Props extends object = BaseObject,\n    PrivateMergedProps extends object = Substitute<OuterProps, Props>,\n    PrivateAttrsArg extends Attrs<PrivateMergedProps> = Attrs<PrivateMergedProps>,\n    PrivateResolvedTarget extends StyledTarget<R> = AttrsTarget<R, PrivateAttrsArg, Target>,\n  >(\n    attrs: PrivateAttrsArg\n  ) => Styled<\n    R,\n    PrivateResolvedTarget,\n    PrivateResolvedTarget extends KnownTarget\n      ? Substitute<\n          Substitute<OuterProps, React.ComponentPropsWithRef<PrivateResolvedTarget>>,\n          Props\n        >\n      : PrivateMergedProps,\n    OuterStatics\n  >;\n\n  withConfig: (config: StyledOptions<R, OuterProps>) => Styled<R, Target, OuterProps, OuterStatics>;\n}\n\nexport default function constructWithOptions<\n  R extends Runtime,\n  Target extends StyledTarget<R>,\n  OuterProps extends object = Target extends KnownTarget\n    ? React.ComponentPropsWithRef<Target>\n    : BaseObject,\n  OuterStatics extends object = BaseObject,\n>(\n  componentConstructor: IStyledComponentFactory<R, StyledTarget<R>, object, any>,\n  tag: StyledTarget<R>,\n  options: StyledOptions<R, OuterProps> = EMPTY_OBJECT\n): Styled<R, Target, OuterProps, OuterStatics> {\n  /**\n   * We trust that the tag is a valid component as long as it isn't\n   * falsish. Typically the tag here is a string or function (i.e.\n   * class or pure function component), however a component may also be\n   * an object if it uses another utility, e.g. React.memo. React will\n   * output an appropriate warning however if the `tag` isn't valid.\n   */\n  if (!tag) {\n    throw styledError(1, tag);\n  }\n\n  /* This is callable directly as a template function */\n  const templateFunction = <Props extends object = BaseObject, Statics extends object = BaseObject>(\n    initialStyles: Styles<Substitute<OuterProps, Props>>,\n    ...interpolations: Interpolation<Substitute<OuterProps, Props>>[]\n  ) =>\n    componentConstructor<Substitute<OuterProps, Props>, Statics>(\n      tag,\n      options as StyledOptions<R, Substitute<OuterProps, Props>>,\n      css<Substitute<OuterProps, Props>>(initialStyles, ...interpolations)\n    );\n\n  /**\n   * Attrs allows for accomplishing two goals:\n   *\n   * 1. Backfilling props at runtime more expressively than defaultProps\n   * 2. Amending the prop interface of a wrapped styled component\n   */\n  templateFunction.attrs = <\n    Props extends object = BaseObject,\n    PrivateMergedProps extends object = Substitute<OuterProps, Props>,\n    PrivateAttrsArg extends Attrs<PrivateMergedProps> = Attrs<PrivateMergedProps>,\n    PrivateResolvedTarget extends StyledTarget<R> = AttrsTarget<R, PrivateAttrsArg, Target>,\n  >(\n    attrs: PrivateAttrsArg\n  ) =>\n    constructWithOptions<\n      R,\n      PrivateResolvedTarget,\n      PrivateResolvedTarget extends KnownTarget\n        ? Substitute<\n            Substitute<OuterProps, React.ComponentPropsWithRef<PrivateResolvedTarget>>,\n            Props\n          >\n        : PrivateMergedProps,\n      OuterStatics\n    >(componentConstructor, tag, {\n      ...options,\n      attrs: Array.prototype.concat(options.attrs, attrs).filter(Boolean),\n    });\n\n  /**\n   * If config methods are called, wrap up a new template function\n   * and merge options.\n   */\n  templateFunction.withConfig = (config: StyledOptions<R, OuterProps>) =>\n    constructWithOptions<R, Target, OuterProps, OuterStatics>(componentConstructor, tag, {\n      ...options,\n      ...config,\n    });\n\n  return templateFunction;\n}\n", "import * as React from 'react';\nimport createStyledComponent from '../models/StyledComponent';\nimport { BaseObject, KnownTarget, WebTarget } from '../types';\nimport domElements, { SupportedHTMLElements } from '../utils/domElements';\nimport constructWithOptions, { Styled as StyledInstance } from './constructWithOptions';\n\nconst baseStyled = <Target extends WebTarget, InjectedProps extends object = BaseObject>(\n  tag: Target\n) =>\n  constructWithOptions<\n    'web',\n    Target,\n    Target extends KnownTarget ? React.ComponentPropsWithRef<Target> & InjectedProps : InjectedProps\n  >(createStyledComponent, tag);\n\nconst styled = baseStyled as typeof baseStyled & {\n  [E in SupportedHTMLElements]: StyledInstance<'web', E, React.JSX.IntrinsicElements[E]>;\n};\n\n// Shorthands for all valid HTML Elements\ndomElements.forEach(domElement => {\n  // @ts-expect-error some react typing bs\n  styled[domElement] = baseStyled<typeof domElement>(domElement);\n});\n\nexport default styled;\nexport { StyledInstance };\n\n/**\n * This is the type of the `styled` HOC.\n */\nexport type Styled = typeof styled;\n\n/**\n * Use this higher-order type for scenarios where you are wrapping `styled`\n * and providing extra props as a third-party library.\n */\nexport type LibraryStyled<LibraryProps extends object = BaseObject> = <Target extends WebTarget>(\n  tag: Target\n) => typeof baseStyled<Target, LibraryProps>;\n", "import StyleSheet from '../sheet';\nimport { ExecutionContext, RuleSet, Stringifier } from '../types';\nimport flatten from '../utils/flatten';\nimport isStaticRules from '../utils/isStaticRules';\nimport { joinStringArray } from '../utils/joinStrings';\n\nexport default class GlobalStyle<Props extends object> {\n  componentId: string;\n  isStatic: boolean;\n  rules: RuleSet<Props>;\n\n  constructor(rules: RuleSet<Props>, componentId: string) {\n    this.rules = rules;\n    this.componentId = componentId;\n    this.isStatic = isStaticRules(rules);\n\n    // pre-register the first instance to ensure global styles\n    // load before component ones\n    StyleSheet.registerId(this.componentId + 1);\n  }\n\n  createStyles(\n    instance: number,\n    executionContext: ExecutionContext & Props,\n    styleSheet: StyleSheet,\n    stylis: Stringifier\n  ): void {\n    const flatCSS = joinStringArray(\n      flatten(this.rules as RuleSet<object>, executionContext, styleSheet, stylis) as string[]\n    );\n    const css = stylis(flatCSS, '');\n    const id = this.componentId + instance;\n\n    // NOTE: We use the id as a name as well, since these rules never change\n    styleSheet.insertRules(id, id, css);\n  }\n\n  removeStyles(instance: number, styleSheet: StyleSheet): void {\n    styleSheet.clearRules(this.componentId + instance);\n  }\n\n  renderStyles(\n    instance: number,\n    executionContext: ExecutionContext & Props,\n    styleSheet: StyleSheet,\n    stylis: Stringifier\n  ): void {\n    if (instance > 2) StyleSheet.registerId(this.componentId + instance);\n\n    // NOTE: Remove old styles, then inject the new ones\n    this.removeStyles(instance, styleSheet);\n    this.createStyles(instance, executionContext, styleSheet, stylis);\n  }\n}\n", "import React from 'react';\nimport { STATIC_EXECUTION_CONTEXT } from '../constants';\nimport GlobalStyle from '../models/GlobalStyle';\nimport { useStyleSheetContext } from '../models/StyleSheetManager';\nimport { DefaultTheme, ThemeContext } from '../models/ThemeProvider';\nimport StyleSheet from '../sheet';\nimport { ExecutionContext, ExecutionProps, Interpolation, Stringifier, Styles } from '../types';\nimport { checkDynamicCreation } from '../utils/checkDynamicCreation';\nimport determineTheme from '../utils/determineTheme';\nimport generateComponentId from '../utils/generateComponentId';\nimport css from './css';\n\nexport default function createGlobalStyle<Props extends object>(\n  strings: Styles<Props>,\n  ...interpolations: Array<Interpolation<Props>>\n) {\n  const rules = css<Props>(strings, ...interpolations);\n  const styledComponentId = `sc-global-${generateComponentId(JSON.stringify(rules))}`;\n  const globalStyle = new GlobalStyle<Props>(rules, styledComponentId);\n\n  if (process.env.NODE_ENV !== 'production') {\n    checkDynamicCreation(styledComponentId);\n  }\n\n  const GlobalStyleComponent: React.ComponentType<ExecutionProps & Props> = props => {\n    const ssc = useStyleSheetContext();\n    const theme = React.useContext(ThemeContext);\n    const instanceRef = React.useRef(ssc.styleSheet.allocateGSInstance(styledComponentId));\n\n    const instance = instanceRef.current;\n\n    if (process.env.NODE_ENV !== 'production' && React.Children.count(props.children)) {\n      console.warn(\n        `The global style component ${styledComponentId} was given child JSX. createGlobalStyle does not render children.`\n      );\n    }\n\n    if (\n      process.env.NODE_ENV !== 'production' &&\n      rules.some(rule => typeof rule === 'string' && rule.indexOf('@import') !== -1)\n    ) {\n      console.warn(\n        `Please do not use @import CSS syntax in createGlobalStyle at this time, as the CSSOM APIs we use in production do not handle it well. Instead, we recommend using a library such as react-helmet to inject a typical <link> meta tag to the stylesheet, or simply embedding it manually in your index.html <head> section for a simpler app.`\n      );\n    }\n\n    if (ssc.styleSheet.server) {\n      renderStyles(instance, props, ssc.styleSheet, theme, ssc.stylis);\n    }\n\n    if (!__SERVER__) {\n      React.useLayoutEffect(() => {\n        if (!ssc.styleSheet.server) {\n          renderStyles(instance, props, ssc.styleSheet, theme, ssc.stylis);\n          return () => globalStyle.removeStyles(instance, ssc.styleSheet);\n        }\n      }, [instance, props, ssc.styleSheet, theme, ssc.stylis]);\n    }\n\n    return null;\n  };\n\n  function renderStyles(\n    instance: number,\n    props: ExecutionProps,\n    styleSheet: StyleSheet,\n    theme: DefaultTheme | undefined,\n    stylis: Stringifier\n  ) {\n    if (globalStyle.isStatic) {\n      globalStyle.renderStyles(\n        instance,\n        STATIC_EXECUTION_CONTEXT as unknown as ExecutionContext & Props,\n        styleSheet,\n        stylis\n      );\n    } else {\n      const context = {\n        ...props,\n        theme: determineTheme(props, theme, GlobalStyleComponent.defaultProps),\n      } as ExecutionContext & Props;\n\n      globalStyle.renderStyles(instance, context, styleSheet, stylis);\n    }\n  }\n\n  return React.memo(GlobalStyleComponent);\n}\n", "import Keyframes from '../models/Keyframes';\nimport { Interpolation, Styles } from '../types';\nimport generateComponentId from '../utils/generateComponentId';\nimport { joinStringArray } from '../utils/joinStrings';\nimport css from './css';\n\nexport default function keyframes<Props extends object = {}>(\n  strings: Styles<Props>,\n  ...interpolations: Array<Interpolation<Props>>\n): Keyframes {\n  /* Warning if you've used keyframes on React Native */\n  if (\n    process.env.NODE_ENV !== 'production' &&\n    typeof navigator !== 'undefined' &&\n    navigator.product === 'ReactNative'\n  ) {\n    console.warn(\n      '`keyframes` cannot be used on ReactNative, only on the web. To do animation in ReactNative please use Animated.'\n    );\n  }\n\n  const rules = joinStringArray(css<Props>(strings, ...interpolations) as string[]);\n  const name = generateComponentId(rules);\n  return new Keyframes(name, rules);\n}\n", "import React from 'react';\nimport { ThemeContext } from '../models/ThemeProvider';\nimport { AnyComponent, ExecutionProps } from '../types';\nimport determineTheme from '../utils/determineTheme';\nimport getComponentName from '../utils/getComponentName';\nimport hoist, { NonReactStatics } from '../utils/hoist';\n\nexport default function withTheme<T extends AnyComponent>(\n  Component: T\n): React.ForwardRefExoticComponent<\n  React.PropsWithoutRef<React.JSX.LibraryManagedAttributes<T, ExecutionProps>> &\n    React.RefAttributes<T>\n> &\n  NonReactStatics<T> {\n  const WithTheme = React.forwardRef<T, React.JSX.LibraryManagedAttributes<T, ExecutionProps>>(\n    (props, ref) => {\n      const theme = React.useContext(ThemeContext);\n      const themeProp = determineTheme(props, theme, Component.defaultProps);\n\n      if (process.env.NODE_ENV !== 'production' && themeProp === undefined) {\n        console.warn(\n          `[withTheme] You are not using a ThemeProvider nor passing a theme prop or a theme in defaultProps in component class \"${getComponentName(\n            Component\n          )}\"`\n        );\n      }\n\n      return <Component {...props} theme={themeProp} ref={ref} />;\n    }\n  );\n\n  WithTheme.displayName = `WithTheme(${getComponentName(Component)})`;\n\n  return hoist(WithTheme, Component);\n}\n", "import React from 'react';\nimport type * as streamInternal from 'stream';\nimport { Readable } from 'stream';\nimport { IS_BROWSER, SC_ATTR, SC_ATTR_VERSION, SC_VERSION } from '../constants';\nimport StyleSheet from '../sheet';\nimport styledError from '../utils/error';\nimport { joinStringArray } from '../utils/joinStrings';\nimport getNonce from '../utils/nonce';\nimport { StyleSheetManager } from './StyleSheetManager';\n\ndeclare const __SERVER__: boolean;\n\nconst CLOSING_TAG_R = /^\\s*<\\/[a-z]/i;\n\nexport default class ServerStyleSheet {\n  instance: StyleSheet;\n  sealed: boolean;\n\n  constructor() {\n    this.instance = new StyleSheet({ isServer: true });\n    this.sealed = false;\n  }\n\n  _emitSheetCSS = (): string => {\n    const css = this.instance.toString();\n    if (!css) return '';\n    const nonce = getNonce();\n    const attrs = [\n      nonce && `nonce=\"${nonce}\"`,\n      `${SC_ATTR}=\"true\"`,\n      `${SC_ATTR_VERSION}=\"${SC_VERSION}\"`,\n    ];\n    const htmlAttr = joinStringArray(attrs.filter(Boolean) as string[], ' ');\n\n    return `<style ${htmlAttr}>${css}</style>`;\n  };\n\n  collectStyles(children: any): React.JSX.Element {\n    if (this.sealed) {\n      throw styledError(2);\n    }\n\n    return <StyleSheetManager sheet={this.instance}>{children}</StyleSheetManager>;\n  }\n\n  getStyleTags = (): string => {\n    if (this.sealed) {\n      throw styledError(2);\n    }\n\n    return this._emitSheetCSS();\n  };\n\n  getStyleElement = () => {\n    if (this.sealed) {\n      throw styledError(2);\n    }\n\n    const css = this.instance.toString();\n    if (!css) return [];\n\n    const props = {\n      [SC_ATTR]: '',\n      [SC_ATTR_VERSION]: SC_VERSION,\n      dangerouslySetInnerHTML: {\n        __html: css,\n      },\n    };\n\n    const nonce = getNonce();\n    if (nonce) {\n      (props as any).nonce = nonce;\n    }\n\n    // v4 returned an array for this fn, so we'll do the same for v5 for backward compat\n    return [<style {...props} key=\"sc-0-0\" />];\n  };\n\n  // @ts-expect-error alternate return types are not possible due to code transformation\n  interleaveWithNodeStream(input: Readable): streamInternal.Transform {\n    if (!__SERVER__ || IS_BROWSER) {\n      throw styledError(3);\n    } else if (this.sealed) {\n      throw styledError(2);\n    }\n\n    if (__SERVER__) {\n      this.seal();\n\n      const { Transform } = require('stream');\n\n      const readableStream: Readable = input;\n      const { instance: sheet, _emitSheetCSS } = this;\n\n      const transformer: streamInternal.Transform = new Transform({\n        transform: function appendStyleChunks(\n          chunk: string,\n          /* encoding */\n          _: string,\n          callback: Function\n        ) {\n          // Get the chunk and retrieve the sheet's CSS as an HTML chunk,\n          // then reset its rules so we get only new ones for the next chunk\n          const renderedHtml = chunk.toString();\n          const html = _emitSheetCSS();\n\n          sheet.clearTag();\n\n          // prepend style html to chunk, unless the start of the chunk is a\n          // closing tag in which case append right after that\n          if (CLOSING_TAG_R.test(renderedHtml)) {\n            const endOfClosingTag = renderedHtml.indexOf('>') + 1;\n            const before = renderedHtml.slice(0, endOfClosingTag);\n            const after = renderedHtml.slice(endOfClosingTag);\n\n            this.push(before + html + after);\n          } else {\n            this.push(html + renderedHtml);\n          }\n\n          callback();\n        },\n      });\n\n      readableStream.on('error', err => {\n        // forward the error to the transform stream\n        transformer.emit('error', err);\n      });\n\n      return readableStream.pipe(transformer);\n    }\n  }\n\n  seal = (): void => {\n    this.sealed = true;\n  };\n}\n", "import { mainSheet } from './models/StyleSheetManager';\nimport StyleSheet from './sheet';\n\nexport const __PRIVATE__ = {\n  StyleSheet,\n  mainSheet,\n};\n", "/* Import singletons */\nimport { SC_ATTR, SC_VERSION } from './constants';\nimport createGlobalStyle from './constructors/createGlobalStyle';\nimport css from './constructors/css';\nimport keyframes from './constructors/keyframes';\n/* Import Higher Order Components */\nimport withTheme from './hoc/withTheme';\n/* Import hooks */\nimport ServerStyleSheet from './models/ServerStyleSheet';\nimport {\n  IStyleSheetContext,\n  IStyleSheetManager,\n  IStylisContext,\n  StyleSheetConsumer,\n  StyleSheetContext,\n  StyleSheetManager,\n} from './models/StyleSheetManager';\n/* Import components */\nimport ThemeProvider, { ThemeConsumer, ThemeContext, useTheme } from './models/ThemeProvider';\nimport isStyledComponent from './utils/isStyledComponent';\n\n/* Warning if you've imported this file on React Native */\nif (\n  process.env.NODE_ENV !== 'production' &&\n  typeof navigator !== 'undefined' &&\n  navigator.product === 'ReactNative'\n) {\n  console.warn(\n    `It looks like you've imported 'styled-components' on React Native.\\nPerhaps you're looking to import 'styled-components/native'?\\nRead more about this at https://www.styled-components.com/docs/basics#react-native`\n  );\n}\n\nconst windowGlobalKey = `__sc-${SC_ATTR}__`;\n\n/* Warning if there are several instances of styled-components */\nif (\n  process.env.NODE_ENV !== 'production' &&\n  process.env.NODE_ENV !== 'test' &&\n  typeof window !== 'undefined'\n) {\n  // @ts-expect-error dynamic key not in window object\n  window[windowGlobalKey] ||= 0;\n\n  // @ts-expect-error dynamic key not in window object\n  if (window[windowGlobalKey] === 1) {\n    console.warn(\n      `It looks like there are several instances of 'styled-components' initialized in this application. This may cause dynamic styles to not render properly, errors during the rehydration process, a missing theme prop, and makes your application bigger without good reason.\\n\\nSee https://s-c.sh/2BAXzed for more info.`\n    );\n  }\n\n  // @ts-expect-error dynamic key not in window object\n  window[windowGlobalKey] += 1;\n}\n\n/* Export everything */\nexport * from './secretInternals';\nexport { Attrs, DefaultTheme, ShouldForwardProp } from './types';\nexport {\n  IStyleSheetContext,\n  IStyleSheetManager,\n  IStylisContext,\n  ServerStyleSheet,\n  StyleSheetConsumer,\n  StyleSheetContext,\n  StyleSheetManager,\n  ThemeConsumer,\n  ThemeContext,\n  ThemeProvider,\n  createGlobalStyle,\n  css,\n  isStyledComponent,\n  keyframes,\n  useTheme,\n  SC_VERSION as version,\n  withTheme,\n};\n"], "names": ["SC_ATTR", "process", "env", "REACT_APP_SC_ATTR", "SC_ATTR_ACTIVE", "SC_ATTR_VERSION", "SC_VERSION", "SPLITTER", "IS_BROWSER", "window", "document", "DISABLE_SPEEDY", "Boolean", "SC_DISABLE_SPEEDY", "REACT_APP_SC_DISABLE_SPEEDY", "NODE_ENV", "STATIC_EXECUTION_CONTEXT", "invalidHookCallRe", "seen", "Set", "checkDynamicCreation", "displayName", "componentId", "parsedIdString", "concat", "message_1", "originalConsoleError_1", "console", "error", "didNotCallInvalidHook_1", "consoleErrorMessage", "consoleErrorArgs", "_i", "arguments", "length", "test", "delete", "apply", "__spread<PERSON><PERSON>y", "useRef", "has", "warn", "add", "message", "EMPTY_ARRAY", "Object", "freeze", "EMPTY_OBJECT", "determineTheme", "props", "providedTheme", "defaultProps", "theme", "dom<PERSON><PERSON>s", "escapeRegex", "dashesAtEnds", "escape", "str", "replace", "AD_REPLACER_R", "chars<PERSON><PERSON><PERSON>", "getAlphabeticChar", "code", "String", "fromCharCode", "generateAlphabeticName", "x", "name", "Math", "abs", "SEED", "phash", "h", "i", "charCodeAt", "hash", "generateComponentId", "getComponentName", "target", "isTag", "char<PERSON>t", "toLowerCase", "hasSymbol", "Symbol", "for", "REACT_MEMO_TYPE", "REACT_FORWARD_REF_TYPE", "REACT_STATICS", "childContextTypes", "contextType", "contextTypes", "getDefaultProps", "getDerivedStateFromError", "getDerivedStateFromProps", "mixins", "propTypes", "type", "KNOWN_STATICS", "prototype", "caller", "callee", "arity", "MEMO_STATICS", "$$typeof", "compare", "TYPE_STATICS", "_a", "render", "getStatics", "component", "object", "defineProperty", "getOwnPropertyNames", "getOwnPropertySymbols", "getOwnPropertyDescriptor", "getPrototypeOf", "objectPrototype", "hoistNonReactStatics", "targetComponent", "sourceComponent", "excludelist", "inheritedComponent", "keys", "targetStatics", "sourceStatics", "key", "descriptor", "e", "isFunction", "isStyledComponent", "joinStrings", "a", "b", "joinStringArray", "arr", "sep", "result", "isPlainObject", "constructor", "mixinRecursively", "source", "forceMerge", "Array", "isArray", "setToString", "toStringFn", "value", "ERRORS", "format", "args", "c", "len", "push", "for<PERSON>ach", "d", "throwStyledComponentsError", "interpolations", "Error", "join", "trim", "DefaultGroupedTag", "tag", "this", "groupSizes", "Uint32Array", "indexOfGroup", "group", "index", "insertRules", "rules", "<PERSON><PERSON><PERSON><PERSON>", "oldSize", "newSize", "styledError", "set", "ruleIndex", "l", "insertRule", "clearGroup", "length_1", "startIndex", "endIndex", "deleteRule", "getGroup", "css", "getRule", "MAX_SMI", "groupIDRegister", "Map", "reverseRegister", "nextFreeGroup", "getGroupForId", "id", "get", "setGroupForId", "SELECTOR", "MARKER_RE", "RegExp", "rehydrateNamesFromContent", "sheet", "content", "names", "split", "registerName", "rehydrateSheetFromTag", "style", "parts", "textContent", "part", "marker", "match", "parseInt", "getTag", "rehydrateSheet", "nodes", "querySelectorAll", "node", "getAttribute", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "getNonce", "__webpack_nonce__", "makeStyleTag", "head", "parent", "createElement", "prevStyle", "from", "findLastStyleTag", "nextS<PERSON>ling", "undefined", "setAttribute", "nonce", "insertBefore", "CSSOMTag", "element", "append<PERSON><PERSON><PERSON>", "createTextNode", "styleSheets", "ownerNode", "getSheet", "rule", "_error", "cssRules", "cssText", "TextTag", "childNodes", "VirtualTag", "_target", "splice", "SHOULD_REHYDRATE", "defaultOptions", "isServer", "useCSSOMInjection", "StyleSheet", "options", "globalStyles", "_this", "__assign", "gs", "server", "getIdForGroup", "size", "selector", "outputSheet", "registerId", "rehydrate", "reconstructWithOptions", "with<PERSON><PERSON>s", "allocateGSInstance", "makeTag", "hasNameForId", "groupNames", "clearNames", "clear", "clearRules", "clearTag", "AMP_REGEX", "COMMENT_REGEX", "recursivelySetNamepace", "compiled", "namespace", "map", "replaceAll", "prop", "children", "createStylisInstance", "_componentId", "_selector", "_selectorRegexp", "_b", "_c", "_d", "plugins", "selfReferenceReplacer", "offset", "string", "startsWith", "endsWith", "middlewares", "slice", "stylis", "RULESET", "includes", "prefix", "prefixer", "stringify", "stringifyRules", "flatCSS", "compile", "stack", "serialize", "middleware", "rulesheet", "reduce", "acc", "plugin", "throwStyledError", "toString", "mainSheet", "mainStylis", "StyleSheetContext", "React", "createContext", "shouldForwardProp", "styleSheet", "StyleSheetConsumer", "Consumer", "StylisContext", "useStyleSheetContext", "useContext", "StyleSheetManager", "useState", "stylisPlugins", "setPlugins", "resolvedStyleSheet", "useMemo", "disableCSSOMInjection", "enableVendorPrefixes", "useEffect", "shallowequal", "styleSheetContextValue", "Provider", "Keyframes", "inject", "stylisInstance", "resolvedName", "getName", "isUpper", "hyphenateStyleName", "output", "isFalsish", "chunk", "objToCssArray", "obj", "val", "hasOwnProperty", "isCss", "hyphenate", "unitless", "flatten", "executionContext", "styledComponentId", "isReactComponent", "chunklet", "isStaticRules", "ComponentStyle", "baseStyle", "staticRulesId", "isStatic", "baseHash", "generateAndInjectStyles", "cssStatic", "name_1", "generateName", "cssStaticFormatted", "dynamicHash", "partRule", "partString", "name_2", "ThemeContext", "ThemeConsumer", "useTheme", "ThemeProvider", "outerTheme", "themeContext", "mergedTheme", "mergeTheme", "identifiers", "seenUnknownProps", "createStyledComponent", "isTargetStyledComp", "styledComponentTarget", "isCompositeComponent", "attrs", "parentComponentId", "generateId", "generateDisplayName", "finalAttrs", "filter", "shouldForwardPropFn_1", "passedShouldForwardPropFn_1", "elementToBeCreated", "componentStyle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ref", "forwardedComponent", "forwardedRef", "componentAttrs", "foldedComponentIds", "contextTheme", "ssc", "useDebugValue", "context", "attrDef", "className", "resolvedAttrDef", "resolveContext", "as", "propsForElement", "forwardedAs", "isPropValid", "generatedClassName", "resolvedAttrs", "useInjectedStyle", "warnTooManyClasses", "classString", "useStyledComponentImpl", "WrappedStyledComponent", "forwardRef", "_foldedDefaultProps", "sources", "sources_1", "merge", "generatedClasses", "warningSeen", "createWarnTooManyClasses", "hoist", "interleave", "strings", "addTag", "arg", "assign", "styles", "styleStringArray", "constructWithOptions", "componentConstructor", "templateFunction", "initialStyles", "withConfig", "config", "baseStyled", "styled", "dom<PERSON>lement", "GlobalStyle", "createStyles", "instance", "removeStyles", "renderStyles", "createGlobalStyle", "JSON", "globalStyle", "GlobalStyleComponent", "current", "Children", "count", "some", "indexOf", "memo", "keyframes", "navigator", "product", "withTheme", "Component", "WithTheme", "themeProp", "CLOSING_TAG_R", "ServerStyleSheet", "_emitSheetCSS", "htmlAttr", "getStyleTags", "sealed", "getStyleElement", "dangerouslySetInnerHTML", "__html", "seal", "collectStyles", "interleaveWithNodeStream", "input", "Transform", "require", "readableStream", "sheet_1", "_emitSheetCSS_1", "transformer_1", "transform", "_", "callback", "renderedHtml", "html", "endOfClosingTag", "before", "after", "on", "err", "emit", "pipe", "__PRIVATE__", "windowGlobalKey"], "mappings": "kTAGO,IAAMA,EACS,oBAAZC,cACiB,IAAhBA,QAAQC,MACdD,QAAQC,IAAIC,mBAAqBF,QAAQC,IAAIF,UAChD,cAEWI,EAAiB,SACjBC,EAAkB,sBAClBC,EAAa,SACbC,EAAW,YAEXC,EAA+B,oBAAXC,QAA8C,oBAAbC,SAErDC,EAAiBC,QACC,kBAAtBC,kBACHA,kBACmB,oBAAZZ,cACkB,IAAhBA,QAAQC,UACoC,IAA5CD,QAAQC,IAAIY,6BACyB,KAA5Cb,QAAQC,IAAIY,4BACgC,UAA5Cb,QAAQC,IAAIY,6BAEVb,QAAQC,IAAIY,4BACK,oBAAZb,cACkB,IAAhBA,QAAQC,UAC0B,IAAlCD,QAAQC,IAAIW,mBACe,KAAlCZ,QAAQC,IAAIW,kBACsB,UAAlCZ,QAAQC,IAAIW,mBAEVZ,QAAQC,IAAIW,kBACW,eAAzBZ,QAAQC,IAAIa,UAITC,EAA2B,CAAE,ECnCpCC,EAAoB,qBACpBC,EAAO,IAAIC,IAEJC,EAAuB,SAACC,EAAqBC,GACxD,GAA6B,eAAzBrB,QAAQC,IAAIa,SAA2B,CACzC,IAAMQ,EAAiBD,EAAc,oBAAoBE,OAAAF,EAAc,KAAG,GACpEG,EACJ,iBAAAD,OAAiBH,GAAWG,OAAGD,EAAgD,oCAA/E,+SASIG,EAAuBC,QAAQC,MACrC,IACE,IAAIC,GAAwB,EAC5BF,QAAQC,MAAQ,SAACE,OAAqB,IAAmBC,EAAA,GAAAC,EAAA,EAAnBA,EAAmBC,UAAAC,OAAnBF,IAAAD,EAAmBC,EAAA,GAAAC,UAAAD,GAGnDf,EAAkBkB,KAAKL,IACzBD,GAAwB,EAExBX,EAAKkB,OAAOX,IAEZC,EAAqBW,WAAA,EAAAC,EAAA,CAAAR,GAAwBC,GAAkB,GAEnE,EAGAQ,IAEIV,IAA0BX,EAAKsB,IAAIf,KACrCE,QAAQc,KAAKhB,GACbP,EAAKwB,IAAIjB,GAEZ,CAAC,MAAOG,GAGHX,EAAkBkB,KAAMP,EAAgBe,UAE1CzB,EAAKkB,OAAOX,EAEf,CAAS,QACRE,QAAQC,MAAQF,CACjB,CACF,CACH,ECjDakB,EAAcC,OAAOC,OAAO,IAC5BC,EAAeF,OAAOC,OAAO,ICAlB,SAAAE,EACtBC,EACAC,EACAC,GAEA,YAFA,IAAAA,IAAAA,EAAiEJ,GAEzDE,EAAMG,QAAUD,EAAaC,OAASH,EAAMG,OAAUF,GAAiBC,EAAaC,KAC9F,CCPA,IAwIAC,EAAe,IAAIlC,IAxIF,CACf,IACA,OACA,UACA,OACA,UACA,QACA,QACA,IACA,OACA,MACA,MACA,MACA,aACA,OACA,KACA,SACA,SACA,UACA,OACA,OACA,MACA,WACA,OACA,WACA,KACA,MACA,UACA,MACA,SACA,MACA,KACA,KACA,KACA,QACA,WACA,aACA,SACA,SACA,OACA,KACA,KACA,KACA,KACA,KACA,KACA,SACA,SACA,KACA,OACA,IACA,SACA,MACA,QACA,MACA,MACA,SACA,QACA,SACA,KACA,OACA,OACA,MACA,OACA,OACA,WACA,OACA,QACA,MACA,WACA,SACA,KACA,WACA,SACA,SACA,IACA,QACA,UACA,MACA,WACA,IACA,KACA,KACA,OACA,IACA,OACA,SACA,UACA,SACA,QACA,SACA,OACA,SACA,QACA,MACA,UACA,MACA,QACA,QACA,KACA,WACA,QACA,KACA,QACA,OACA,KACA,QACA,IACA,KACA,MACA,MACA,QACA,MACA,SACA,WACA,OACA,UACA,gBACA,IACA,QACA,OACA,iBACA,SACA,OACA,OACA,UACA,UACA,WACA,iBACA,OACA,OACA,MACA,OACA,UCrIImC,EAAc,wCAEdC,EAAe,WAMG,SAAAC,EAAOC,GAC7B,OAAOA,EACJC,QAAQJ,EAAa,KACrBI,QAAQH,EAAc,GAC3B,CCdA,IAAMI,EAAgB,WAIhBC,EAAc,GAGdC,EAAoB,SAACC,GAAiB,OAAAC,OAAOC,aAAaF,GAAQA,EAAO,GAAK,GAAK,IAA7C,EAGpB,SAAAG,EAAuBH,GAC7C,IACII,EADAC,EAAO,GAIX,IAAKD,EAAIE,KAAKC,IAAIP,GAAOI,EAAIN,EAAaM,EAAKA,EAAIN,EAAe,EAChEO,EAAON,EAAkBK,EAAIN,GAAeO,EAG9C,OAAQN,EAAkBK,EAAIN,GAAeO,GAAMT,QAAQC,EAAe,QAC5E,CCpBO,MAAMW,EAAO,KAKPC,EAAQ,SAACC,EAAWN,GAG/B,IAFA,IAAIO,EAAIP,EAAEhC,OAEHuC,GACLD,EAAS,GAAJA,EAAUN,EAAEQ,aAAaD,GAGhC,OAAOD,CACT,EAGaG,EAAO,SAACT,GACnB,OAAOK,EAAMD,EAAMJ,EACrB,ECfwB,SAAAU,EAAoBnB,GAC1C,OAAOQ,EAAuBU,EAAKlB,KAAS,EAC9C,CCHwB,SAAAoB,EAAiBC,GACvC,MAC4B,eAAzB7E,QAAQC,IAAIa,UAA8C,iBAAX+D,GAAuBA,GACtEA,EAA8CzD,aAC9CyD,EAAoBX,MACrB,WAEJ,CCPwB,SAAAY,EAAMD,GAC5B,MACoB,iBAAXA,IACmB,eAAzB7E,QAAQC,IAAIa,UACT+D,EAAOE,OAAO,KAAOF,EAAOE,OAAO,GAAGC,cAG9C,CCNA,IAAMC,EAA8B,mBAAXC,QAAyBA,OAAOC,IAGnDC,EAAkBH,EAAYC,OAAOC,IAAI,cAAgB,MACzDE,EAAyBJ,EAAYC,OAAOC,IAAI,qBAAuB,MAKvEG,EAAgB,CACpBC,mBAAmB,EACnBC,aAAa,EACbC,cAAc,EACdvC,cAAc,EACd9B,aAAa,EACbsE,iBAAiB,EACjBC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,QAAQ,EACRC,WAAW,EACXC,MAAM,GAGFC,EAAgB,CACpB9B,MAAM,EACNjC,QAAQ,EACRgE,WAAW,EACXC,QAAQ,EACRC,QAAQ,EACRnE,WAAW,EACXoE,OAAO,GAWHC,EAAe,CACnBC,UAAU,EACVC,SAAS,EACTrD,cAAc,EACd9B,aAAa,EACb0E,WAAW,EACXC,MAAM,GAGFS,IAAYC,EAAA,CAAA,GACfpB,GAlByB,CAC1BiB,UAAU,EACVI,QAAQ,EACRxD,cAAc,EACd9B,aAAa,EACb0E,WAAW,GAcXW,EAACrB,GAAkBiB,KAcrB,SAASM,EAAWC,GAElB,OAPqB,SAFrBC,EASWD,IAP8BC,EAAOd,KAAKO,YAE7BlB,EAMfiB,EAIF,aAAcO,EACjBJ,EAAaI,EAAoB,UACjCtB,EAjBN,IACEuB,CAiBF,CAEA,IAAMC,EAAiBlE,OAAOkE,eACxBC,EAAsBnE,OAAOmE,oBAC7BC,EAAwBpE,OAAOoE,sBAC/BC,GAA2BrE,OAAOqE,yBAClCC,GAAiBtE,OAAOsE,eACxBC,GAAkBvE,OAAOqD,UAiBP,SAAAmB,GAItBC,EAAoBC,EAAoBC,GACxC,GAA+B,iBAApBD,EAA8B,CAGvC,GAAIH,GAAiB,CACnB,IAAMK,EAAqBN,GAAeI,GACtCE,GAAsBA,IAAuBL,IAC/CC,GAAqBC,EAAiBG,EAAoBD,EAE7D,CAED,IAAIE,EAA4BV,EAAoBO,GAEhDN,IACFS,EAAOA,EAAKlG,OAAOyF,EAAsBM,KAM3C,IAHA,IAAMI,EAAgBf,EAAWU,GAC3BM,EAAgBhB,EAAWW,GAExB9C,EAAI,EAAGA,EAAIiD,EAAKxF,SAAUuC,EAAG,CACpC,IAAMoD,EAAMH,EAAKjD,GACjB,KACIoD,KAAO5B,GACPuB,GAAeA,EAAYK,IAC3BD,GAAiBC,KAAOD,GACxBD,GAAiBE,KAAOF,GAC1B,CACA,IAAMG,EAAaZ,GAAyBK,EAAiBM,GAE7D,IAEEd,EAAeO,EAAiBO,EAAKC,EACtC,CAAC,MAAOC,GAER,CACF,CACF,CACF,CAED,OAAOT,CACT,CCpJwB,SAAAU,GAAW7F,GACjC,MAAuB,mBAATA,CAChB,CCAwB,SAAA8F,GAAkBnD,GACxC,MAAyB,iBAAXA,GAAuB,sBAAuBA,CAC9D,CCDgB,SAAAoD,GAAYC,EAAwBC,GAClD,OAAOD,GAAKC,EAAI,UAAGD,EAAC,KAAA3G,OAAI4G,GAAMD,GAAKC,GAAK,EAC1C,CAEgB,SAAAC,GAAgBC,EAAeC,GAC7C,GAAmB,IAAfD,EAAIpG,OACN,MAAO,GAIT,IADA,IAAIsG,EAASF,EAAI,GACR7D,EAAI,EAAGA,EAAI6D,EAAIpG,OAAQuC,IAC9B+D,GAAUD,EAAMA,EAAMD,EAAI7D,GAAK6D,EAAI7D,GAErC,OAAO+D,CACT,CCjBwB,SAAAC,GAAcvE,GACpC,OACQ,OAANA,GACa,iBAANA,GACPA,EAAEwE,YAAYvE,OAAStB,OAAOsB,QAE5B,UAAWD,GAAKA,EAAEqC,SAExB,CCNA,SAASoC,GAAiB7D,EAAa8D,EAAaC,GAGlD,QAHkD,IAAAA,IAAAA,GAAkB,IAG/DA,IAAeJ,GAAc3D,KAAYgE,MAAMC,QAAQjE,GAC1D,OAAO8D,EAGT,GAAIE,MAAMC,QAAQH,GAChB,IAAK,IAAIf,EAAM,EAAGA,EAAMe,EAAO1G,OAAQ2F,IACrC/C,EAAO+C,GAAOc,GAAiB7D,EAAO+C,GAAMe,EAAOf,SAEhD,GAAIY,GAAcG,GACvB,IAAK,IAAMf,KAAOe,EAChB9D,EAAO+C,GAAOc,GAAiB7D,EAAO+C,GAAMe,EAAOf,IAIvD,OAAO/C,CACT,CCJgB,SAAAkE,GAAYlC,EAAgBmC,GAC1CpG,OAAOkE,eAAeD,EAAQ,WAAY,CAAEoC,MAAOD,GACrD,CClBA,ICGME,GAA6C,eAAzBlJ,QAAQC,IAAIa,SDHvB,CACb,EAAK,wDACL,EAAK,gQACL,EAAK,sHACL,EAAK,sMACL,EAAK,kKACL,EAAK,4OACL,EAAK,qHACL,EAAK,8DACL,EAAK,gCACL,GAAM,iUACN,GAAM,wNACN,GAAM,qWACN,GAAM,yLACN,GAAM,+CACN,GAAM,2ZACN,GAAM,uQACN,GAAM,yIACN,GAAM,oFCfqE,GAK7E,SAASqI,SAAO,IAAyBC,EAAA,GAAArH,EAAA,EAAzBA,EAAyBC,UAAAC,OAAzBF,IAAAqH,EAAyBrH,GAAAC,UAAAD,GAIvC,IAHA,IAAImG,EAAIkB,EAAK,GACPjB,EAAI,GAEDkB,EAAI,EAAGC,EAAMF,EAAKnH,OAAQoH,EAAIC,EAAKD,GAAK,EAC/ClB,EAAEoB,KAAKH,EAAKC,IAOd,OAJAlB,EAAEqB,QAAQ,SAAAC,GACRvB,EAAIA,EAAEzE,QAAQ,SAAUgG,EAC1B,GAEOvB,CACT,CAMwB,SAAAwB,GACtB7F,OACA,IAAwB8F,EAAA,GAAA5H,EAAA,EAAxBA,EAAwBC,UAAAC,OAAxBF,IAAA4H,EAAwB5H,EAAA,GAAAC,UAAAD,GAExB,MAA6B,eAAzB/B,QAAQC,IAAIa,SACP,IAAI8I,MACT,0IAAArI,OAA0IsC,EAAI,0BAAAtC,OAC5IoI,EAAe1H,OAAS,EAAI,UAAUV,OAAAoI,EAAeE,KAAK,OAAU,KAIjE,IAAID,MAAMT,mBAAOD,GAAOrF,IAAU8F,GAAc,IAAEG,OAE7D,CCnCO,IAMDC,GAAiB,WAKrB,SAAAA,EAAYC,GACVC,KAAKC,WAAa,IAAIC,YARR,KASdF,KAAKhI,OATS,IAUdgI,KAAKD,IAAMA,CACZ,CAyEH,OAvEED,EAAY9D,UAAAmE,aAAZ,SAAaC,GAEX,IADA,IAAIC,EAAQ,EACH9F,EAAI,EAAGA,EAAI6F,EAAO7F,IACzB8F,GAASL,KAAKC,WAAW1F,GAG3B,OAAO8F,GAGTP,EAAA9D,UAAAsE,YAAA,SAAYF,EAAeG,GACzB,GAAIH,GAASJ,KAAKC,WAAWjI,OAAQ,CAKnC,IAJA,IAAMwI,EAAYR,KAAKC,WACjBQ,EAAUD,EAAUxI,OAEtB0I,EAAUD,EACPL,GAASM,GAEd,IADAA,IAAY,GACE,EACZ,MAAMC,GAAY,GAAI,UAAGP,IAI7BJ,KAAKC,WAAa,IAAIC,YAAYQ,GAClCV,KAAKC,WAAWW,IAAIJ,GACpBR,KAAKhI,OAAS0I,EAEd,IAAK,IAAInG,EAAIkG,EAASlG,EAAImG,EAASnG,IACjCyF,KAAKC,WAAW1F,GAAK,CAExB,CAID,IAFA,IAAIsG,EAAYb,KAAKG,aAAaC,EAAQ,GAE1BU,GAAPvG,EAAI,EAAOgG,EAAMvI,QAAQuC,EAAIuG,EAAGvG,IACnCyF,KAAKD,IAAIgB,WAAWF,EAAWN,EAAMhG,MACvCyF,KAAKC,WAAWG,KAChBS,MAKNf,EAAU9D,UAAAgF,WAAV,SAAWZ,GACT,GAAIA,EAAQJ,KAAKhI,OAAQ,CACvB,IAAMiJ,EAASjB,KAAKC,WAAWG,GACzBc,EAAalB,KAAKG,aAAaC,GAC/Be,EAAWD,EAAaD,EAE9BjB,KAAKC,WAAWG,GAAS,EAEzB,IAAK,IAAI7F,EAAI2G,EAAY3G,EAAI4G,EAAU5G,IACrCyF,KAAKD,IAAIqB,WAAWF,EAEvB,GAGHpB,EAAQ9D,UAAAqF,SAAR,SAASjB,GACP,IAAIkB,EAAM,GACV,GAAIlB,GAASJ,KAAKhI,QAAqC,IAA3BgI,KAAKC,WAAWG,GAC1C,OAAOkB,EAOT,IAJA,IAAMtJ,EAASgI,KAAKC,WAAWG,GACzBc,EAAalB,KAAKG,aAAaC,GAC/Be,EAAWD,EAAalJ,EAErBuC,EAAI2G,EAAY3G,EAAI4G,EAAU5G,IACrC+G,GAAO,GAAAhK,OAAG0I,KAAKD,IAAIwB,QAAQhH,IAAKjD,OAAAjB,GAGlC,OAAOiL,GAEVxB,CAAD,IC3FM0B,GAAU,GAAC,GAEbC,GAAuC,IAAIC,IAC3CC,GAAuC,IAAID,IAC3CE,GAAgB,EAQPC,GAAgB,SAACC,GAC5B,GAAIL,GAAgBnJ,IAAIwJ,GACtB,OAAOL,GAAgBM,IAAID,GAG7B,KAAOH,GAAgBrJ,IAAIsJ,KACzBA,KAGF,IAAMxB,EAAQwB,KAEd,GAA6B,eAAzB7L,QAAQC,IAAIa,YAAuC,EAARuJ,GAAa,GAAKA,EAAQoB,IACvE,MAAMb,GAAY,GAAI,UAAGP,IAK3B,OAFAqB,GAAgBb,IAAIkB,EAAI1B,GACxBuB,GAAgBf,IAAIR,EAAO0B,GACpB1B,CACT,EAMa4B,GAAgB,SAACF,EAAY1B,GAExCwB,GAAgBxB,EAAQ,EAExBqB,GAAgBb,IAAIkB,EAAI1B,GACxBuB,GAAgBf,IAAIR,EAAO0B,EAC7B,ECxCMG,GAAW,SAAS3K,OAAAxB,eAAYK,EAAe,MAAAmB,OAAKlB,EAAU,MAC9D8L,GAAY,IAAIC,OAAO,IAAI7K,OAAAxB,EAAqD,iDAkChFsM,GAA4B,SAACC,EAAcP,EAAYQ,GAI3D,IAHA,IACIrI,EADEsI,EAAQD,EAAQE,MAAM,KAGnBjI,EAAI,EAAGuG,EAAIyB,EAAMvK,OAAQuC,EAAIuG,EAAGvG,KAClCN,EAAOsI,EAAMhI,KAChB8H,EAAMI,aAAaX,EAAI7H,EAG7B,EAEMyI,GAAwB,SAACL,EAAcM,GAI3C,UAHMC,GAA8B,QAArBpG,EAAAmG,EAAME,mBAAe,IAAArG,EAAAA,EAAA,IAAIgG,MAAMnM,GACxCkK,EAAkB,GAEfhG,EAAI,EAAGuG,EAAI8B,EAAM5K,OAAQuC,EAAIuG,EAAGvG,IAAK,CAC5C,IAAMuI,EAAOF,EAAMrI,GAAGsF,OACtB,GAAKiD,EAAL,CAEA,IAAMC,EAASD,EAAKE,MAAMd,IAE1B,GAAIa,EAAQ,CACV,IAAM3C,EAAkC,EAA1B6C,SAASF,EAAO,GAAI,IAC5BjB,EAAKiB,EAAO,GAEJ,IAAV3C,IAEF4B,GAAcF,EAAI1B,GAGlBgC,GAA0BC,EAAOP,EAAIiB,EAAO,IAC5CV,EAAMa,SAAS5C,YAAYF,EAAOG,IAGpCA,EAAMvI,OAAS,CAChB,MACCuI,EAAMjB,KAAKwD,EAnBO,CAqBrB,CACH,EAEaK,GAAiB,SAACd,GAG7B,IAFA,IAAMe,EAAQ5M,SAAS6M,iBAAiBpB,IAE/B1H,EAAI,EAAGuG,EAAIsC,EAAMpL,OAAQuC,EAAIuG,EAAGvG,IAAK,CAC5C,IAAM+I,EAAOF,EAAM7I,GACf+I,GAAQA,EAAKC,aAAazN,KAAaI,IACzCwM,GAAsBL,EAAOiB,GAEzBA,EAAKE,YACPF,EAAKE,WAAWC,YAAYH,GAGjC,CACH,EC3Fc,SAAUI,KACtB,MAAoC,oBAAtBC,kBAAoCA,kBAAoB,IACxE,CCEA,IAOaC,GAAe,SAAChJ,GAC3B,IAAMiJ,EAAOrN,SAASqN,KAChBC,EAASlJ,GAAUiJ,EACnBlB,EAAQnM,SAASuN,cAAc,SAC/BC,EAXiB,SAACpJ,GACxB,IAAMwD,EAAMQ,MAAMqF,KAAKrJ,EAAOyI,iBAAmC,SAAS/L,OAAAxB,EAAU,OAEpF,OAAOsI,EAAIA,EAAIpG,OAAS,EAC1B,CAOoBkM,CAAiBJ,GAC7BK,OAA4BC,IAAdJ,EAA0BA,EAAUG,YAAc,KAEtExB,EAAM0B,aAAavO,EAASI,GAC5ByM,EAAM0B,aAAalO,EAAiBC,GAEpC,IAAMkO,EAAQZ,KAMd,OAJIY,GAAO3B,EAAM0B,aAAa,QAASC,GAEvCR,EAAOS,aAAa5B,EAAOwB,GAEpBxB,CACT,ECfa6B,GAAQ,WAOnB,SAAAA,EAAY5J,GACVoF,KAAKyE,QAAUb,GAAahJ,GAG5BoF,KAAKyE,QAAQC,YAAYlO,SAASmO,eAAe,KAEjD3E,KAAKqC,MDKe,SAACtC,GACvB,GAAIA,EAAIsC,MACN,OAAOtC,EAAIsC,MAKb,IADQ,IAAAuC,EAAgBpO,SAAQoO,YACvBrK,EAAI,EAAGuG,EAAI8D,EAAY5M,OAAQuC,EAAIuG,EAAGvG,IAAK,CAClD,IAAM8H,EAAQuC,EAAYrK,GAC1B,GAAI8H,EAAMwC,YAAc9E,EACtB,OAAOsC,CAEV,CAED,MAAM1B,GAAY,GACpB,CCpBiBmE,CAAS9E,KAAKyE,SAC3BzE,KAAKhI,OAAS,CACf,CA2BH,OAzBEwM,EAAAxI,UAAA+E,WAAA,SAAWV,EAAe0E,GACxB,IAGE,OAFA/E,KAAKqC,MAAMtB,WAAWgE,EAAM1E,GAC5BL,KAAKhI,UACE,CACR,CAAC,MAAOgN,GACP,OAAO,CACR,GAGHR,EAAUxI,UAAAoF,WAAV,SAAWf,GACTL,KAAKqC,MAAMjB,WAAWf,GACtBL,KAAKhI,UAGPwM,EAAOxI,UAAAuF,QAAP,SAAQlB,GACN,IAAM0E,EAAO/E,KAAKqC,MAAM4C,SAAS5E,GAGjC,OAAI0E,GAAQA,EAAKG,QACRH,EAAKG,QAEL,IAGZV,CAAD,IAGaW,GAAO,WAKlB,SAAAA,EAAYvK,GACVoF,KAAKyE,QAAUb,GAAahJ,GAC5BoF,KAAKoD,MAAQpD,KAAKyE,QAAQW,WAC1BpF,KAAKhI,OAAS,CACf,CA0BH,OAxBEmN,EAAAnJ,UAAA+E,WAAA,SAAWV,EAAe0E,GACxB,GAAI1E,GAASL,KAAKhI,QAAUqI,GAAS,EAAG,CACtC,IAAMiD,EAAO9M,SAASmO,eAAeI,GAIrC,OAFA/E,KAAKyE,QAAQF,aAAajB,EADVtD,KAAKoD,MAAM/C,IACgB,MAC3CL,KAAKhI,UACE,CACR,CACC,OAAO,GAIXmN,EAAUnJ,UAAAoF,WAAV,SAAWf,GACTL,KAAKyE,QAAQhB,YAAYzD,KAAKoD,MAAM/C,IACpCL,KAAKhI,UAGPmN,EAAOnJ,UAAAuF,QAAP,SAAQlB,GACN,OAAIA,EAAQL,KAAKhI,OACRgI,KAAKoD,MAAM/C,GAAOwC,YAElB,IAGZsC,CAAD,IAGaE,GAAU,WAKrB,SAAAA,EAAYC,GACVtF,KAAKO,MAAQ,GACbP,KAAKhI,OAAS,CACf,CAwBH,OAtBEqN,EAAArJ,UAAA+E,WAAA,SAAWV,EAAe0E,GACxB,OAAI1E,GAASL,KAAKhI,SAChBgI,KAAKO,MAAMgF,OAAOlF,EAAO,EAAG0E,GAC5B/E,KAAKhI,UACE,IAMXqN,EAAUrJ,UAAAoF,WAAV,SAAWf,GACTL,KAAKO,MAAMgF,OAAOlF,EAAO,GACzBL,KAAKhI,UAGPqN,EAAOrJ,UAAAuF,QAAP,SAAQlB,GACN,OAAIA,EAAQL,KAAKhI,OACRgI,KAAKO,MAAMF,GAEX,IAGZgF,CAAD,ICxHIG,GAAmBlP,EAajBmP,GAA+B,CACnCC,UAAWpP,EACXqP,mBAAoBlP,GAItBmP,GAAA,WAYE,SAAAA,EACEC,EACAC,EACAvD,QAFA,IAAAsD,IAAAA,EAAgChN,QAChC,IAAAiN,IAAAA,EAA4C,CAAA,GAF9C,IAqBCC,EAAA/F,KAhBCA,KAAK6F,QAAOG,EAAAA,EAAA,CAAA,EACPP,IACAI,GAGL7F,KAAKiG,GAAKH,EACV9F,KAAKuC,MAAQ,IAAIb,IAAIa,GACrBvC,KAAKkG,SAAWL,EAAQH,UAGnB1F,KAAKkG,QAAU5P,GAAckP,KAChCA,IAAmB,EACnBrC,GAAenD,OAGjBlB,GAAYkB,KAAM,WAAM,OJtDD,SAACqC,GAK1B,IAJA,IAAMtC,EAAMsC,EAAMa,SACVlL,EAAW+H,EAAG/H,OAElBsJ,EAAM,cACDlB,GACP,IAAM0B,EDqBmB,SAAC1B,GAC5B,OAAOuB,GAAgBI,IAAI3B,EAC7B,CCvBe+F,CAAc/F,GACzB,QAAWgE,IAAPtC,EAA2B,MAAA,WAE/B,IAAMS,EAAQF,EAAME,MAAMR,IAAID,GACxBvB,EAAQR,EAAIsB,SAASjB,GAC3B,QAAcgE,IAAV7B,IAAwBA,EAAM6D,MAAyB,IAAjB7F,EAAMvI,OAAuB,MAAA,WAEvE,IAAMqO,EAAW,GAAG/O,OAAAxB,eAAYsK,EAAK,SAAA9I,OAAQwK,EAAE,MAE3CQ,EAAU,QACA8B,IAAV7B,GACFA,EAAMhD,QAAQ,SAAAtF,GACRA,EAAKjC,OAAS,IAChBsK,GAAW,GAAAhL,OAAG2C,EAAI,KAEtB,GAKFqH,GAAO,GAAGhK,OAAAiJ,GAAQjJ,OAAA+O,uBAAqB/D,EAAO,MAAAhL,OAAKjB,IArB5C+J,EAAQ,EAAGA,EAAQpI,EAAQoI,MAA3BA,GAwBT,OAAOkB,CACT,CIwB4BgF,CAAYP,EAAK,EAC1C,CAoEH,OA7FSH,EAAUW,WAAjB,SAAkBzE,GAChB,OAAOD,GAAcC,IA0BvB8D,EAAA5J,UAAAwK,UAAA,YACOxG,KAAKkG,QAAU5P,GAClB6M,GAAenD,OAInB4F,EAAA5J,UAAAyK,uBAAA,SAAuBZ,EAA+Ba,GACpD,YADoD,IAAAA,IAAAA,GAAgB,GAC7D,IAAId,EACJI,EAAAA,EAAA,CAAA,EAAAhG,KAAK6F,SAAYA,GACtB7F,KAAKiG,GACJS,GAAa1G,KAAKuC,YAAU6B,IAIjCwB,EAAkB5J,UAAA2K,mBAAlB,SAAmB7E,GACjB,OAAQ9B,KAAKiG,GAAGnE,IAAO9B,KAAKiG,GAAGnE,IAAO,GAAK,GAI7C8D,EAAA5J,UAAAkH,OAAA,WACE,OAAOlD,KAAKD,MAAQC,KAAKD,KN/EEA,EKAR,SAACvD,GAAE,IAAUmJ,EAAiBnJ,EAAAmJ,kBAAE/K,EAAM4B,EAAA5B,OAC3D,kBACS,IAAIyK,GAAWzK,GACb+K,EACF,IAAInB,GAAS5J,GAEb,IAAIuK,GAAQvK,EAEvB,CCuEkDgM,CAAQ5G,KAAK6F,SN9EtD,IAAI/F,GAAkBC,KADD,IAACA,GMmF7B6F,EAAA5J,UAAA6K,aAAA,SAAa/E,EAAY7H,GACvB,OAAO+F,KAAKuC,MAAMjK,IAAIwJ,IAAQ9B,KAAKuC,MAAMR,IAAID,GAAYxJ,IAAI2B,IAI/D2L,EAAA5J,UAAAyG,aAAA,SAAaX,EAAY7H,GAGvB,GAFA4H,GAAcC,GAET9B,KAAKuC,MAAMjK,IAAIwJ,GAKjB9B,KAAKuC,MAAMR,IAAID,GAAYtJ,IAAIyB,OALT,CACvB,IAAM6M,EAAa,IAAI7P,IACvB6P,EAAWtO,IAAIyB,GACf+F,KAAKuC,MAAM3B,IAAIkB,EAAIgF,EACpB,GAMHlB,EAAA5J,UAAAsE,YAAA,SAAYwB,EAAY7H,EAAcsG,GACpCP,KAAKyC,aAAaX,EAAI7H,GACtB+F,KAAKkD,SAAS5C,YAAYuB,GAAcC,GAAKvB,IAI/CqF,EAAU5J,UAAA+K,WAAV,SAAWjF,GACL9B,KAAKuC,MAAMjK,IAAIwJ,IAChB9B,KAAKuC,MAAMR,IAAID,GAAYkF,SAKhCpB,EAAU5J,UAAAiL,WAAV,SAAWnF,GACT9B,KAAKkD,SAASlC,WAAWa,GAAcC,IACvC9B,KAAK+G,WAAWjF,IAIlB8D,EAAA5J,UAAAkL,SAAA,WAGElH,KAAKD,SAAMqE,GAEdwB,CAAD,IC5HMuB,GAAY,KACZC,GAAgB,gBAWtB,SAASC,GAAuBC,EAA4BC,GAC1D,OAAOD,EAASE,IAAI,SAAAzC,GAclB,MAbkB,SAAdA,EAAKjJ,OAEPiJ,EAAK/F,MAAQ,GAAG1H,OAAAiQ,cAAaxC,EAAK/F,OAElC+F,EAAK/F,MAAQ+F,EAAK/F,MAAMyI,WAAW,IAAK,IAAAnQ,OAAIiQ,EAAS,MACrDxC,EAAKhM,MAASgM,EAAKhM,MAAmByO,IAAI,SAAAE,GACxC,MAAO,GAAGpQ,OAAAiQ,EAAa,KAAAjQ,OAAAoQ,EACzB,IAGE9I,MAAMC,QAAQkG,EAAK4C,WAA2B,eAAd5C,EAAKjJ,OACvCiJ,EAAK4C,SAAWN,GAAuBtC,EAAK4C,SAAUJ,IAEjDxC,CACT,EACF,CAEwB,SAAA6C,GACtBpL,GAAA,IAKIqL,EACAC,EACAC,EAPJC,OAAA,IAAAxL,EAG2B3D,EAAsB2D,EAF/CyL,EAAAD,EAAAnC,QAAAA,OAAO,IAAAoC,EAAGpP,EAAsBoP,EAChCC,EAAuDF,EAAAG,QAAvDA,OAAO,IAAAD,EAAGxP,EAA6CwP,EAOnDE,EAAwB,SAACpF,EAAeqF,EAAgBC,GAC5D,OAKEA,EAAOC,WAAWT,IAClBQ,EAAOE,SAASV,IAChBQ,EAAOb,WAAWK,EAAW,IAAI9P,OAAS,EAEnC,IAAAV,OAAIuQ,GAGN7E,CACT,EAuBMyF,EAAcN,EAAQO,QAE5BD,EAAYnJ,KAX8C,SAAAmF,GACpDA,EAAQ3I,OAAS6M,EAAOC,SAAWnE,EAAQzF,MAAM6J,SAAS,OAC3DpE,EAAQ1L,MAAmB,GAAK0L,EAAQ1L,MAAM,GAE5CS,QAAQ2N,GAAWW,GACnBtO,QAAQuO,EAAiBK,GAEhC,GASIvC,EAAQiD,QACVL,EAAYnJ,KAAKqJ,EAAOI,UAG1BN,EAAYnJ,KAAKqJ,EAAOK,WAExB,IAAMC,EAA8B,SAClC3H,EACA+E,EAIAyC,EACA1R,QALA,IAAAiP,IAAAA,EAAa,SAIb,IAAAyC,IAAAA,EAAW,SACX,IAAA1R,IAAAA,EAAiB,KAKjByQ,EAAezQ,EACf0Q,EAAYzB,EACZ0B,EAAkB,IAAI5F,OAAO,KAAA7K,OAAKwQ,EAAc,OAAE,KAElD,IAAMoB,EAAU5H,EAAI9H,QAAQ4N,GAAe,IACvCE,EAAWqB,EAAOQ,QACpBL,GAAUzC,EAAW,UAAGyC,EAAM,KAAAxR,OAAI+O,EAAQ,OAAA/O,OAAM4R,EAAO,MAAOA,GAG5DrD,EAAQ0B,YACVD,EAAWD,GAAuBC,EAAUzB,EAAQ0B,YAGtD,IAAM6B,EAAkB,GAOxB,OALAT,EAAOU,UACL/B,EACAqB,EAAOW,WAAWb,EAAYnR,OAAOqR,EAAOY,UAAU,SAAAvK,GAAS,OAAAoK,EAAM9J,KAAKN,EAAM,MAG3EoK,CACT,EAcA,OAZAH,EAAexO,KAAO0N,EAAQnQ,OAC1BmQ,EACGqB,OAAO,SAACC,EAAKC,GAKZ,OAJKA,EAAOzP,MACV0P,GAAiB,IAGZtP,EAAMoP,EAAKC,EAAOzP,KAC1B,EAAEG,GACFwP,WACH,GAEGX,CACT,CC1IO,IAAMY,GAAwB,IAAIjE,GAC5BkE,GAA0BlC,KAQ1BmC,GAAoBC,EAAMC,cAAkC,CACvEC,uBAAmB9F,EACnB+F,WAAYN,GACZlB,OAAQmB,KAGGM,GAAqBL,GAAkBM,SAGvCC,GAAgBN,EAAMC,mBAA8B7F,YAGjDmG,KACd,OAAOC,EAAWT,GACpB,CAkDM,SAAUU,GAAkB1R,GAC1B,IAAAyD,EAAwBkO,EAAS3R,EAAM4R,eAAtCxC,EAAO3L,EAAA,GAAEoO,OACRT,EAAeI,gBAEjBM,EAAqBC,EAAQ,WACjC,IAAIzI,EAAQ8H,EAYZ,OAVIpR,EAAMsJ,MACRA,EAAQtJ,EAAMsJ,MACLtJ,EAAM6B,SACfyH,EAAQA,EAAMoE,uBAAuB,CAAE7L,OAAQ7B,EAAM6B,SAAU,IAG7D7B,EAAMgS,wBACR1I,EAAQA,EAAMoE,uBAAuB,CAAEd,mBAAmB,KAGrDtD,CACT,EAAG,CAACtJ,EAAMgS,sBAAuBhS,EAAMsJ,MAAOtJ,EAAM6B,OAAQuP,IAEtDxB,EAASmC,EACb,WACE,OAAAlD,GAAqB,CACnB/B,QAAS,CAAE0B,UAAWxO,EAAMwO,UAAWuB,OAAQ/P,EAAMiS,sBACrD7C,QAAOA,GAFT,EAIF,CAACpP,EAAMiS,qBAAsBjS,EAAMwO,UAAWY,IAGhD8C,EAAU,WACHC,EAAa/C,EAASpP,EAAM4R,gBAAgBC,EAAW7R,EAAM4R,cACpE,EAAG,CAAC5R,EAAM4R,gBAEV,IAAMQ,EAAyBL,EAC7B,WAAM,MAAC,CACLZ,kBAAmBnR,EAAMmR,kBACzBC,WAAYU,EACZlC,OAAMA,EAHF,EAKN,CAAC5P,EAAMmR,kBAAmBW,EAAoBlC,IAGhD,OACEqB,gBAACD,GAAkBqB,SAAS,CAAApM,MAAOmM,GACjCnB,EAAAjG,cAACuG,GAAcc,SAAQ,CAACpM,MAAO2J,GAAS5P,EAAM4O,UAGpD,CCzHA,IAAA0D,GAAA,WAKE,SAAYA,EAAApR,EAAcsG,GAA1B,IAQCwF,EAAA/F,KAEDA,KAAAsL,OAAS,SAACnB,EAAwBoB,QAAA,IAAAA,IAAAA,EAAwCzB,IACxE,IAAM0B,EAAezF,EAAK9L,KAAOsR,EAAe9Q,KAE3C0P,EAAWtD,aAAad,EAAKjE,GAAI0J,IACpCrB,EAAW7J,YACTyF,EAAKjE,GACL0J,EACAD,EAAexF,EAAKxF,MAAOiL,EAAc,cAG/C,EAnBExL,KAAK/F,KAAOA,EACZ+F,KAAK8B,GAAK,gBAAgBxK,OAAA2C,GAC1B+F,KAAKO,MAAQA,EAEbzB,GAAYkB,KAAM,WAChB,MAAMW,GAAY,GAAI9G,OAAOkM,EAAK9L,MACpC,EACD,CAiBH,OAHEoR,EAAOrP,UAAAyP,QAAP,SAAQF,GACN,YADM,IAAAA,IAAAA,EAAwCzB,IACvC9J,KAAK/F,KAAOsR,EAAe9Q,MAErC4Q,CAAD,ICpCMK,GAAU,SAACtM,GAAc,OAAAA,GAAK,KAAOA,GAAK,KAexB,SAAAuM,GAAmBrD,GAGzC,IAFA,IAAIsD,EAAS,GAEJrR,EAAI,EAAGA,EAAI+N,EAAOtQ,OAAQuC,IAAK,CACtC,IAAM6E,EAAIkJ,EAAO/N,GAEjB,GAAU,IAANA,GAAiB,MAAN6E,GAA2B,MAAdkJ,EAAO,GACjC,OAAOA,EAGLoD,GAAQtM,GACVwM,GAAU,IAAMxM,EAAErE,cAElB6Q,GAAUxM,CAEb,CAED,OAAOwM,EAAOrD,WAAW,OAAS,IAAMqD,EAASA,CACnD,CCTA,IAAMC,GAAY,SAACC,GACjB,OAAAA,UAAmD,IAAVA,GAA6B,KAAVA,CAA5D,EAEWC,GAAgB,SAACC,GAC5B,ICzBsC/R,EAAc+E,EDyB9CuB,EAAQ,GAEd,IAAK,IAAM5C,KAAOqO,EAAK,CACrB,IAAMC,EAAMD,EAAIrO,GACXqO,EAAIE,eAAevO,KAAQkO,GAAUI,KAGrCrN,MAAMC,QAAQoN,IAAQA,EAAIE,OAAUrO,GAAWmO,GAClD1L,EAAMjB,KAAK,GAAAhI,OAAG8U,GAAUzO,GAAI,KAAKsO,EAAK,KAC7B1N,GAAc0N,GACvB1L,EAAMjB,KAANnH,MAAAoI,OAAW,GAAGjJ,OAAAqG,EAAO,OAAKoO,GAAcE,IAAI,GAAA,CAAE,MAAK,IAEnD1L,EAAMjB,KAAK,GAAGhI,OAAA8U,GAAUzO,GAAS,MAAArG,QCrCC2C,EDqCe0D,ECnCxC,OAFuCqB,EDqCMiN,ICnCpB,kBAAVjN,GAAiC,KAAVA,EAC1C,GAGY,iBAAVA,GAAgC,IAAVA,GAAiB/E,KAAQoS,GAAcpS,EAAKsO,WAAW,MAIjF1O,OAAOmF,GAAOa,OAHZ,GAAGvI,OAAA0H,EAAS,OD8ByC,MAE7D,CAED,OAAOuB,CACT,EAEc,SAAU+L,GACtBR,EACAS,EACApC,EACAoB,GAEA,GAAIM,GAAUC,GACZ,MAAO,GAIT,GAAI/N,GAAkB+N,GACpB,MAAO,CAAC,IAAKxU,OAAAwU,EAAkDU,oBAIjE,GAAI1O,GAAWgO,GAAQ,CACrB,IE7DKhO,GADmC7F,EF8DhB6T,IE7DG7T,EAAK+D,WAAa/D,EAAK+D,UAAUyQ,mBF6D1BF,EAoBhC,MAAO,CAACT,GAnBR,IAAMxN,EAASwN,EAAMS,GAiBrB,MAd2B,eAAzBxW,QAAQC,IAAIa,UACM,iBAAXyH,GACNM,MAAMC,QAAQP,IACbA,aAAkB+M,IACnB9M,GAAcD,IACJ,OAAXA,GAEA7G,QAAQC,MACN,GAAGJ,OAAAqD,EACDmR,GACiL,qLAIhLQ,GAAehO,EAAQiO,EAAkBpC,EAAYoB,EAI/D,CEpFqB,IAAoBtT,EFsF1C,OAAI6T,aAAiBT,GACflB,GACF2B,EAAMR,OAAOnB,EAAYoB,GAClB,CAACO,EAAML,QAAQF,KAEf,CAACO,GAKRvN,GAAcuN,GACTC,GAAcD,GAGlBlN,MAAMC,QAAQiN,GAUZlN,MAAM5C,UAAU1E,OAAOa,MAAMO,EANrBoT,EAMwCtE,IANjC,SAAAkF,GACpB,OAAAJ,GAAeI,EAAUH,EAAkBpC,EAAYoB,EAAvD,IAJO,CAACO,EAAMlC,WAMlB,CGzGwB,SAAA+C,GAAoCpM,GAC1D,IAAK,IAAIhG,EAAI,EAAGA,EAAIgG,EAAMvI,OAAQuC,GAAK,EAAG,CACxC,IAAMwK,EAAOxE,EAAMhG,GAEnB,GAAIuD,GAAWiH,KAAUhH,GAAkBgH,GAGzC,OAAO,CAEV,CAED,OAAO,CACT,CCPA,IAAM3K,GAAOK,EAAKrE,GAKlBwW,GAAA,WAQE,SAAAA,EAAYrM,EAAqBnJ,EAAqByV,GACpD7M,KAAKO,MAAQA,EACbP,KAAK8M,cAAgB,GACrB9M,KAAK+M,SACsB,eAAzBhX,QAAQC,IAAIa,gBACGuN,IAAdyI,GAA2BA,EAAUE,WACtCJ,GAAcpM,GAChBP,KAAK5I,YAAcA,EACnB4I,KAAKgN,SAAW3S,EAAMD,GAAMhD,GAC5B4I,KAAK6M,UAAYA,EAIjBjH,GAAWW,WAAWnP,EACvB,CAmEH,OAjEEwV,EAAA5Q,UAAAiR,wBAAA,SACEV,EACApC,EACAxB,GAEA,IAAIpG,EAAQvC,KAAK6M,UACb7M,KAAK6M,UAAUI,wBAAwBV,EAAkBpC,EAAYxB,GACrE,GAGJ,GAAI3I,KAAK+M,WAAapE,EAAOlO,KAC3B,GAAIuF,KAAK8M,eAAiB3C,EAAWtD,aAAa7G,KAAK5I,YAAa4I,KAAK8M,eACvEvK,EAAQvE,GAAYuE,EAAOvC,KAAK8M,mBAC3B,CACL,IAAMI,EAAY/O,GAChBmO,GAAQtM,KAAKO,MAAOgM,EAAkBpC,EAAYxB,IAE9CwE,EAAOC,EAAa/S,EAAM2F,KAAKgN,SAAUE,KAAe,GAE9D,IAAK/C,EAAWtD,aAAa7G,KAAK5I,YAAa+V,GAAO,CACpD,IAAME,EAAqB1E,EAAOuE,EAAW,IAAI5V,OAAA6V,QAAQ/I,EAAWpE,KAAK5I,aACzE+S,EAAW7J,YAAYN,KAAK5I,YAAa+V,EAAME,EAChD,CAED9K,EAAQvE,GAAYuE,EAAO4K,GAC3BnN,KAAK8M,cAAgBK,CACtB,KACI,CAIL,IAHA,IAAIG,EAAcjT,EAAM2F,KAAKgN,SAAUrE,EAAOlO,MAC1C6G,EAAM,GAED/G,EAAI,EAAGA,EAAIyF,KAAKO,MAAMvI,OAAQuC,IAAK,CAC1C,IAAMgT,EAAWvN,KAAKO,MAAMhG,GAE5B,GAAwB,iBAAbgT,EACTjM,GAAOiM,EAEsB,eAAzBxX,QAAQC,IAAIa,WAA2ByW,EAAcjT,EAAMiT,EAAaC,SACvE,GAAIA,EAAU,CACnB,IAAMC,EAAarP,GACjBmO,GAAQiB,EAAUhB,EAAkBpC,EAAYxB,IAGlD2E,EAAcjT,EAAMiT,EAAaE,EAAajT,GAC9C+G,GAAOkM,CACR,CACF,CAED,GAAIlM,EAAK,CACP,IAAMmM,EAAOL,EAAaE,IAAgB,GAErCnD,EAAWtD,aAAa7G,KAAK5I,YAAaqW,IAC7CtD,EAAW7J,YACTN,KAAK5I,YACLqW,EACA9E,EAAOrH,EAAK,IAAIhK,OAAAmW,QAAQrJ,EAAWpE,KAAK5I,cAI5CmL,EAAQvE,GAAYuE,EAAOkL,EAC5B,CACF,CAED,OAAOlL,GAEVqK,CAAD,IC/Dac,GAAe1D,EAAMC,mBAAwC7F,GAE7DuJ,GAAgBD,GAAarD,kBAmC1BuD,KACd,IAAM1U,EAAQsR,EAAWkD,IAEzB,IAAKxU,EACH,MAAMyH,GAAY,IAGpB,OAAOzH,CACT,CAKwB,SAAA2U,GAAc9U,GACpC,IAAM+U,EAAa9D,EAAMQ,WAAWkD,IAC9BK,EAAejD,EACnB,WAAM,OAjDV,SAAoB5R,EAAsB4U,GACxC,IAAK5U,EACH,MAAMyH,GAAY,IAGpB,GAAI7C,GAAW5E,GAAQ,CACrB,IACM8U,EADU9U,EACY4U,GAE5B,GAC2B,eAAzB/X,QAAQC,IAAIa,WACK,OAAhBmX,GAAwBpP,MAAMC,QAAQmP,IAAuC,iBAAhBA,GAE9D,MAAMrN,GAAY,GAGpB,OAAOqN,CACR,CAED,GAAIpP,MAAMC,QAAQ3F,IAA2B,iBAAVA,EACjC,MAAMyH,GAAY,GAGpB,OAAOmN,EAAkB9H,EAAAA,EAAA,CAAA,EAAA8H,GAAe5U,GAAUA,CACpD,CAyBU+U,CAAWlV,EAAMG,MAAO4U,EAAW,EACzC,CAAC/U,EAAMG,MAAO4U,IAGhB,OAAK/U,EAAM4O,SAIJqC,EAACjG,cAAA2J,GAAatC,SAAS,CAAApM,MAAO+O,GAAehV,EAAM4O,UAHjD,IAIX,CCjEA,IAAMuG,GAAyC,CAAA,EAyE3CC,GAAmB,IAAIlX,IA0F3B,SAASmX,GAKPxT,EACAiL,EACAtF,GAEA,IAAM8N,EAAqBtQ,GAAkBnD,GACvC0T,EAAwB1T,EACxB2T,GAAwB1T,EAAMD,GAGlC4B,EAGEqJ,EAAO2I,MAHTA,aAAQ9V,EAAW8D,EACnBwL,EAEEnC,EAFsEzO,YAAxEA,OAAc,IAAA4Q,EA/KlB,SACE7Q,EACAsX,GAEA,IAAMxU,EAA8B,iBAAhB9C,EAA2B,KAAOmC,EAAOnC,GAE7D+W,GAAYjU,IAASiU,GAAYjU,IAAS,GAAK,EAE/C,IAAM7C,EAAc,GAAGE,OAAA2C,cAAQS,EAG7BtE,EAAa6D,EAAOiU,GAAYjU,KAGlC,OAAOwU,EAAoB,GAAGnX,OAAAmX,EAAqB,KAAAnX,OAAAF,GAAgBA,CACrE,CAgKkBsX,CAAW7I,EAAQ1O,YAAa0O,EAAQ4I,mBAAkBzG,EACxEC,EACEpC,EADuC1O,YAAzCA,OAAc,IAAA8Q,ECpNM,SAAoBrN,GAC1C,OAAOC,EAAMD,GAAU,UAAUtD,OAAAsD,GAAW,UAAUtD,OAAAqD,EAAiBC,OACzE,CDkNkB+T,CAAoB/T,KAG9B4R,EACJ3G,EAAQ1O,aAAe0O,EAAQzO,YAC3B,GAAAE,OAAGgC,EAAOuM,EAAQ1O,aAAgB,KAAAG,OAAAuO,EAAQzO,aAC1CyO,EAAQzO,aAAeA,EAGvBwX,EACJP,GAAsBC,EAAsBE,MACxCF,EAAsBE,MAAMlX,OAAOkX,GAAyCK,OAAOnY,SAClF8X,EAEDtE,EAAsBrE,EAAOqE,kBAEnC,GAAImE,GAAsBC,EAAsBpE,kBAAmB,CACjE,IAAM4E,EAAsBR,EAAsBpE,kBAElD,GAAIrE,EAAQqE,kBAAmB,CAC7B,IAAM6E,EAA4BlJ,EAAQqE,kBAG1CA,EAAoB,SAACxC,EAAMsH,GACzB,OAAAF,EAAoBpH,EAAMsH,IAC1BD,EAA0BrH,EAAMsH,EADhC,CAEH,MACC9E,EAAoB4E,CAEvB,CAED,IAAMG,EAAiB,IAAIrC,GACzBrM,EACAiM,EACA6B,EAAsBC,EAAsBW,oBAAoC7K,GAGlF,SAAS8K,EAAiBnW,EAAoCoW,GAC5D,OA9IJ,SACEC,EACArW,EACAsW,GAGE,IAAOC,EAMLF,EAAkBZ,MALpBS,EAKEG,EALYH,eACdhW,EAIEmW,EAAkBnW,aAHpBsW,EAGEH,EAHgBG,mBAClB/C,EAEE4C,EAAkB5C,kBADpB5R,EACEwU,SAEEI,EAAexF,EAAMQ,WAAWkD,IAChC+B,EAAMlF,KACNL,EAAoBkF,EAAmBlF,mBAAqBuF,EAAIvF,kBAEzC,eAAzBnU,QAAQC,IAAIa,UAA2B6Y,EAAclD,GAKzD,IAAMtT,EAAQJ,EAAeC,EAAOyW,EAAcvW,IAAiBJ,EAE7D8W,EA/DR,SACEnB,EACAzV,EACAG,GAYA,IAVA,IAQI0W,EARED,SAGD5W,GAAK,CAER8W,eAAWzL,EACXlL,MAAKA,IAIEqB,EAAI,EAAGA,EAAIiU,EAAMxW,OAAQuC,GAAK,EAAG,CAExC,IAAMuV,EAAkBhS,GADxB8R,EAAUpB,EAAMjU,IAC8BqV,EAAQD,GAAWC,EAEjE,IAAK,IAAMjS,KAAOmS,EAChBH,EAAQhS,GACE,cAARA,EACIK,GAAY2R,EAAQhS,GAA4BmS,EAAgBnS,IACxD,UAARA,SACOgS,EAAQhS,IAASmS,EAAgBnS,IACtCmS,EAAgBnS,EAE3B,CAMD,OAJI5E,EAAM8W,YACRF,EAAQE,UAAY7R,GAAY2R,EAAQE,UAAW9W,EAAM8W,YAGpDF,CACT,CA6BkBI,CAAsBT,EAAgBvW,EAAOG,GACvD8V,EAAgCW,EAAQK,IAAMpV,EAC9CqV,EAA6B,CAAA,EAEnC,IAAK,IAAMtS,KAAOgS,OACKvL,IAAjBuL,EAAQhS,IAGU,MAAXA,EAAI,IAAsB,OAARA,GAAyB,UAARA,GAAmBgS,EAAQzW,QAAUA,IAEhE,gBAARyE,EACTsS,EAAgBD,GAAKL,EAAQO,YACnBhG,IAAqBA,EAAkBvM,EAAKqR,KACtDiB,EAAgBtS,GAAOgS,EAAQhS,GAG5BuM,GACwB,gBAAzBnU,QAAQC,IAAIa,UACXsZ,EAAYxS,IACZwQ,GAAiB7V,IAAIqF,KAEtBxE,EAAYb,IAAI0W,KAEhBb,GAAiB3V,IAAImF,GACrBlG,QAAQc,KACN,4DAAqDoF,EAAG,4VAMhE,IAAMyS,EA/GR,SACEnB,EACAoB,GAEA,IAAMZ,EAAMlF,KAENsF,EAAYZ,EAAehC,wBAC/BoD,EACAZ,EAAItF,WACJsF,EAAI9G,QAKN,MAF6B,eAAzB5S,QAAQC,IAAIa,UAA2B6Y,EAAcG,GAElDA,CACT,CAgG6BS,CAAiBrB,EAAgBU,GAE/B,eAAzB5Z,QAAQC,IAAIa,UAA6BuY,EAAmBmB,oBAC9DnB,EAAmBmB,mBAAmBH,GAGxC,IAAII,EAAcxS,GAAYuR,EAAoB/C,GAuBlD,OAtBI4D,IACFI,GAAe,IAAMJ,GAEnBT,EAAQE,YACVW,GAAe,IAAMb,EAAQE,WAG/BI,EAEEpV,EAAMmU,KACL7V,EAAYb,IAAI0W,GACb,QACA,aACFwB,EAKAnB,IACFY,EAAgBd,IAAME,GAGjBtL,EAAciL,EAAoBiB,EAC3C,CAwDWQ,CAAmCC,EAAwB3X,EAAOoW,EAC1E,CAEDD,EAAiB/X,YAAcA,EAM/B,IAAIuZ,EAAyB1G,EAAM2G,WAAWzB,GA+D9C,OA1DAwB,EAAuBlC,MAAQI,EAC/B8B,EAAuBzB,eAAiBA,EACxCyB,EAAuBvZ,YAAcA,EACrCuZ,EAAuBxG,kBAAoBA,EAI3CwG,EAAuBnB,mBAAqBlB,EACxCrQ,GAAYsQ,EAAsBiB,mBAAoBjB,EAAsB9B,mBAC5E,GAEJkE,EAAuBlE,kBAAoBA,EAG3CkE,EAAuB9V,OAASyT,EAAqBC,EAAsB1T,OAASA,EAEpFjC,OAAOkE,eAAe6T,EAAwB,eAAgB,CAC5D3O,IAAG,WACD,OAAO/B,KAAK4Q,mBACb,EAEDhQ,aAAIoL,GACFhM,KAAK4Q,oBAAsBvC,ErBvQT,SAAUzT,OAAa,IAAiBiW,EAAA,GAAA/Y,EAAA,EAAjBA,EAAiBC,UAAAC,OAAjBF,IAAA+Y,EAAiB/Y,EAAA,GAAAC,UAAAD,GAC9D,IAAqB,IAAA0E,EAAA,EAAAsU,EAAOD,EAAPrU,WAAAA,IACnBiC,GAAiB7D,EADFkW,EAAAtU,IACkB,GAGnC,OAAO5B,CACT,CqBkQUmW,CAAM,CAAE,EAAEzC,EAAsBrV,aAAc+S,GAC9CA,CACL,IAG0B,eAAzBjW,QAAQC,IAAIa,WACdK,EAAqBC,EAAaqV,GAElCkE,EAAuBH,mBEvSZ,SAACpZ,EAAqBC,GACnC,IAAI4Z,EAA8B,CAAA,EAC9BC,GAAc,EAElB,OAAO,SAACpB,GACN,IAAKoB,IACHD,EAAiBnB,IAAa,EAC1BlX,OAAO6E,KAAKwT,GAAkBhZ,QATnB,KASoC,CAGjD,IAAMX,EAAiBD,EAAc,oBAAoBE,OAAAF,EAAc,KAAG,GAE1EK,QAAQc,KACN,QAAAjB,OAfW,IAe2C,0CAAAA,OAAAH,GAAcG,OAAAD,EAAmB,OAAvF,+PAUF4Z,GAAc,EACdD,EAAmB,CAAA,CACpB,CAEL,CACD,CF2Q+CE,CAC1C/Z,EACAqV,IAIJ1N,GAAY4R,EAAwB,WAAM,MAAA,IAAApZ,OAAIoZ,EAAuBlE,kBAA3B,GAEtC+B,GAGF4C,GACET,EAH+B9V,EAK/B,CAEE4T,OAAO,EACPS,gBAAgB,EAChB9X,aAAa,EACboY,oBAAoB,EACpBrF,mBAAmB,EACnBsC,mBAAmB,EACnB5R,QAAQ,IAKP8V,CACT,CGrUc,SAAUU,GACtBC,EACA3R,GAIA,IAFA,IAAMpB,EAAiC,CAAC+S,EAAQ,IAEvC9W,EAAI,EAAG8E,EAAMK,EAAe1H,OAAQuC,EAAI8E,EAAK9E,GAAK,EACzD+D,EAAOgB,KAAKI,EAAenF,GAAI8W,EAAQ9W,EAAI,IAG7C,OAAO+D,CACT,CCMA,IAAMgT,GAAS,SAAyBC,GACtC,OAAA5Y,OAAO6Y,OAAOD,EAAK,CAAEpF,OAAO,GAA5B,EAOF,SAAS7K,GACPmQ,OACA,IAAkD/R,EAAA,GAAA5H,EAAA,EAAlDA,EAAkDC,UAAAC,OAAlDF,IAAA4H,EAAkD5H,EAAA,GAAAC,UAAAD,GAElD,GAAIgG,GAAW2T,IAAWlT,GAAckT,GAGtC,OAAOH,GACLhF,GACE8E,GAAkB1Y,EAAWN,EAAA,CAJHqZ,GAMrB/R,GAAc,MAMzB,IAAMgS,EAAmBD,EAEzB,OAC4B,IAA1B/R,EAAe1H,QACa,IAA5B0Z,EAAiB1Z,QACc,iBAAxB0Z,EAAiB,GAEjBpF,GAAeoF,GAGjBJ,GACLhF,GAAe8E,GAAkBM,EAAkBhS,IAEvD,CC0BwB,SAAAiS,GAQtBC,EACA7R,EACA8F,GASA,QATA,IAAAA,IAAAA,EAAoDhN,IAS/CkH,EACH,MAAMY,GAAY,EAAGZ,GAIvB,IAAM8R,EAAmB,SACvBC,OACA,IAAiEpS,EAAA,GAAA5H,EAAA,EAAjEA,EAAiEC,UAAAC,OAAjEF,IAAA4H,EAAiE5H,EAAA,GAAAC,UAAAD,GAEjE,OAAA8Z,EACE7R,EACA8F,EACAvE,GAAmCnJ,WAAA,EAAAC,EAAA,CAAA0Z,GAAkBpS,GACtD,IAJD,EA6CF,OAjCAmS,EAAiBrD,MAAQ,SAMvBA,GAEA,OAAAmD,GAUEC,EAAsB7R,EACnBiG,EAAAA,EAAA,CAAA,EAAAH,GACH,CAAA2I,MAAO5P,MAAM5C,UAAU1E,OAAOuO,EAAQ2I,MAAOA,GAAOK,OAAOnY,WAZ7D,EAmBFmb,EAAiBE,WAAa,SAACC,GAC7B,OAAAL,GAA0DC,EAAsB7R,EAC3EiG,EAAAA,EAAA,CAAA,EAAAH,GACAmM,GAFL,EAKKH,CACT,CCvJA,IAAMI,GAAa,SACjBlS,GAEA,OAAA4R,GAIEvD,GAAuBrO,EAJzB,EAMImS,GAASD,GAKf9Y,EAAYoG,QAAQ,SAAA4S,GAElBD,GAAOC,GAAcF,GAA8BE,EACrD,GCjBA,IAAAC,GAAA,WAKE,SAAYA,EAAA7R,EAAuBnJ,GACjC4I,KAAKO,MAAQA,EACbP,KAAK5I,YAAcA,EACnB4I,KAAK+M,SAAWJ,GAAcpM,GAI9BqF,GAAWW,WAAWvG,KAAK5I,YAAc,EAC1C,CAkCH,OAhCEgb,EAAYpW,UAAAqW,aAAZ,SACEC,EACA/F,EACApC,EACAxB,GAEA,IAGMrH,EAAMqH,EAHIxK,GACdmO,GAAQtM,KAAKO,MAA0BgM,EAAkBpC,EAAYxB,IAE3C,IACtB7G,EAAK9B,KAAK5I,YAAckb,EAG9BnI,EAAW7J,YAAYwB,EAAIA,EAAIR,IAGjC8Q,EAAApW,UAAAuW,aAAA,SAAaD,EAAkBnI,GAC7BA,EAAWlD,WAAWjH,KAAK5I,YAAckb,IAG3CF,EAAYpW,UAAAwW,aAAZ,SACEF,EACA/F,EACApC,EACAxB,GAEI2J,EAAW,GAAG1M,GAAWW,WAAWvG,KAAK5I,YAAckb,GAG3DtS,KAAKuS,aAAaD,EAAUnI,GAC5BnK,KAAKqS,aAAaC,EAAU/F,EAAkBpC,EAAYxB,IAE7DyJ,CAAD,ICzCwB,SAAAK,GACtBpB,OACA,IAA8C3R,EAAA,GAAA5H,EAAA,EAA9CA,EAA8CC,UAAAC,OAA9CF,IAAA4H,EAA8C5H,EAAA,GAAAC,UAAAD,GAE9C,IAAMyI,EAAQe,GAAGnJ,WAAA,EAAAC,EAAA,CAAQiZ,GAAY3R,OAC/B8M,EAAoB,aAAalV,OAAAoD,EAAoBgY,KAAK1J,UAAUzI,KACpEoS,EAAc,IAAIP,GAAmB7R,EAAOiM,GAErB,eAAzBzW,QAAQC,IAAIa,UACdK,EAAqBsV,GAGvB,IAAMoG,EAAoE,SAAA7Z,GACxE,IAAM0W,EAAMlF,KACNrR,EAAQ8Q,EAAMQ,WAAWkD,IAGzB4E,EAFctI,EAAM3R,OAAOoX,EAAItF,WAAWxD,mBAAmB6F,IAEtCqG,QA8B7B,MA5B6B,eAAzB9c,QAAQC,IAAIa,UAA6BmT,EAAM8I,SAASC,MAAMha,EAAM4O,WACtElQ,QAAQc,KACN,qCAA8BiU,EAAiB,sEAKxB,eAAzBzW,QAAQC,IAAIa,UACZ0J,EAAMyS,KAAK,SAAAjO,GAAQ,MAAgB,iBAATA,IAAkD,IAA7BA,EAAKkO,QAAQ,UAAiB,IAE7Exb,QAAQc,KACN,gVAIAkX,EAAItF,WAAWjE,QAgBrB,SACEoM,EACAvZ,EACAoR,EACAjR,EACAyP,GAEA,GAAIgK,EAAY5F,SACd4F,EAAYH,aACVF,EACAxb,EACAqT,EACAxB,OAEG,CACL,IAAMgH,EAAU3J,EAAAA,EAAA,CAAA,EACXjN,GACH,CAAAG,MAAOJ,EAAeC,EAAOG,EAAO0Z,EAAqB3Z,gBAG3D0Z,EAAYH,aAAaF,EAAU3C,EAASxF,EAAYxB,EACzD,CACF,CArCG6J,CAAaF,EAAUvZ,EAAO0W,EAAItF,WAAYjR,EAAOuW,EAAI9G,QAYpD,IACT,EA0BA,OAAOqB,EAAMkJ,KAAKN,EACpB,CCjFwB,SAAAO,GACtB9B,OACA,IAA8C3R,EAAA,GAAA5H,EAAA,EAA9CA,EAA8CC,UAAAC,OAA9CF,IAAA4H,EAA8C5H,EAAA,GAAAC,UAAAD,GAInB,eAAzB/B,QAAQC,IAAIa,UACS,oBAAduc,WACe,gBAAtBA,UAAUC,SAEV5b,QAAQc,KACN,mHAIJ,IAAMgI,EAAQpC,GAAgBmD,GAAWnJ,WAAA,EAAAC,EAAA,CAAAiZ,GAAY3R,GAA2B,KAC1EzF,EAAOS,EAAoB6F,GACjC,OAAO,IAAI8K,GAAUpR,EAAMsG,EAC7B,CCjBwB,SAAA+S,GACtBC,GAMA,IAAMC,EAAYxJ,EAAM2G,WACtB,SAAC5X,EAAOoW,GACN,IACMsE,EAAY3a,EAAeC,EADnBiR,EAAMQ,WAAWkD,IACgB6F,EAAUta,cAUzD,MAR6B,eAAzBlD,QAAQC,IAAIa,eAA2CuN,IAAdqP,GAC3Chc,QAAQc,KACN,yHAAyHjB,OAAAqD,EACvH4Y,GACE,MAIDvJ,EAACjG,cAAAwP,EAAcvN,EAAA,CAAA,EAAAjN,EAAO,CAAAG,MAAOua,EAAWtE,IAAKA,IACtD,GAKF,OAFAqE,EAAUrc,YAAc,aAAAG,OAAaqD,EAAiB4Y,GAAU,KAEzDpC,GAAMqC,EAAWD,EAC1B,CCtBA,IAAMG,GAAgB,gBAEtBC,GAAA,WAIE,SAAAA,IAAA,IAGC5N,EAAA/F,KAEDA,KAAA4T,cAAgB,WACd,IAAMtS,EAAMyE,EAAKuM,SAAS1I,WAC1B,IAAKtI,EAAK,MAAO,GACjB,IAAMgD,EAAQZ,KAMRmQ,EAAW1V,GALH,CACZmG,GAAS,UAAUhN,OAAAgN,EAAQ,KAC3B,GAAAhN,OAAGxB,EAAgB,WACnB,GAAGwB,OAAAnB,EAAoB,MAAAmB,OAAAlB,EAAa,MAECyY,OAAOnY,SAAsB,KAEpE,MAAO,UAAUY,OAAAuc,EAAY,KAAAvc,OAAAgK,aAC/B,EAUAtB,KAAA8T,aAAe,WACb,GAAI/N,EAAKgO,OACP,MAAMpT,GAAY,GAGpB,OAAOoF,EAAK6N,eACd,EAEA5T,KAAAgU,gBAAkB,iBAChB,GAAIjO,EAAKgO,OACP,MAAMpT,GAAY,GAGpB,IAAMW,EAAMyE,EAAKuM,SAAS1I,WAC1B,IAAKtI,EAAK,MAAO,GAEjB,IAAMvI,IAAKyD,EAAA,CAAA,GACR1G,GAAU,GACX0G,EAACrG,GAAkBC,EACnBoG,EAAAyX,wBAAyB,CACvBC,OAAQ5S,MAINgD,EAAQZ,KAMd,OALIY,IACDvL,EAAcuL,MAAQA,GAIlB,CAAC0F,6BAAWjR,EAAK,CAAE4E,IAAI,YAChC,EAyDAqC,KAAAmU,KAAO,WACLpO,EAAKgO,QAAS,CAChB,EApHE/T,KAAKsS,SAAW,IAAI1M,GAAW,CAAEF,UAAU,IAC3C1F,KAAK+T,QAAS,CACf,CAmHH,OAnGEJ,EAAa3X,UAAAoY,cAAb,SAAczM,GACZ,GAAI3H,KAAK+T,OACP,MAAMpT,GAAY,GAGpB,OAAOqJ,EAAAjG,cAAC0G,GAAiB,CAACpI,MAAOrC,KAAKsS,UAAW3K,IAqCnDgM,EAAwB3X,UAAAqY,yBAAxB,SAAyBC,GACvB,GAAmBhe,EACjB,MAAMqK,GAAY,GACb,GAAIX,KAAK+T,OACd,MAAMpT,GAAY,GAIlBX,KAAKmU,OAEG,IAAAI,EAAcC,QAAQ,oBAExBC,EAA2BH,EACfI,EAAyB1U,cAAlB2U,EAAkB3U,mBAErC4U,EAAwC,IAAIL,EAAU,CAC1DM,UAAW,SACT/I,EAEAgJ,EACAC,GAIA,IAAMC,EAAelJ,EAAMlC,WACrBqL,EAAON,IAMb,GAJAD,EAAMxN,WAIFwM,GAAczb,KAAK+c,GAAe,CACpC,IAAME,EAAkBF,EAAa/B,QAAQ,KAAO,EAC9CkC,EAASH,EAAatM,MAAM,EAAGwM,GAC/BE,EAAQJ,EAAatM,MAAMwM,GAEjClV,KAAKV,KAAK6V,EAASF,EAAOG,EAC3B,MACCpV,KAAKV,KAAK2V,EAAOD,GAGnBD,GACD,IAQH,OALAN,EAAeY,GAAG,QAAS,SAAAC,GAEzBV,EAAYW,KAAK,QAASD,EAC5B,GAEOb,EAAee,KAAKZ,IAOhCjB,CAAD,ICrIa8B,GAAc,CACzB7P,WAAUA,GACViE,UAASA,ICkBgB,eAAzB9T,QAAQC,IAAIa,UACS,oBAAduc,WACe,gBAAtBA,UAAUC,SAEV5b,QAAQc,KACN,wNAIJ,IAAMmd,GAAkB,QAAQpe,OAAAxB,QAIL,eAAzBC,QAAQC,IAAIa,UACa,SAAzBd,QAAQC,IAAIa,UACM,oBAAXN,SAGPA,OAAOmf,MAAPnf,OAAOmf,IAAqB,GAGI,IAA5Bnf,OAAOmf,KACTje,QAAQc,KACN,4TAKJhC,OAAOmf,KAAoB"}