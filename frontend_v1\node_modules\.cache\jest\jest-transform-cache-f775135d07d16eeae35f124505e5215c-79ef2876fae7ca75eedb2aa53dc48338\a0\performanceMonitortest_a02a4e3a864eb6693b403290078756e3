0ab64684af81f33097d76f0ffeb1f626
var _performanceMonitor = require("../performanceMonitor");
describe('PerformanceMonitorService', function () {
  beforeEach(function () {
    _performanceMonitor.performanceMonitor.clearMetrics();
    _performanceMonitor.performanceMonitor.startMonitoring();
  });
  afterEach(function () {
    _performanceMonitor.performanceMonitor.stopMonitoring();
    jest.clearAllMocks();
  });
  describe('Initialization', function () {
    it('starts monitoring correctly', function () {
      expect(_performanceMonitor.performanceMonitor['isMonitoring']).toBe(true);
    });
    it('stops monitoring correctly', function () {
      _performanceMonitor.performanceMonitor.stopMonitoring();
      expect(_performanceMonitor.performanceMonitor['isMonitoring']).toBe(false);
    });
    it('prevents double initialization', function () {
      var consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      _performanceMonitor.performanceMonitor.startMonitoring();
      _performanceMonitor.performanceMonitor.startMonitoring();
      expect(consoleSpy).toHaveBeenCalledTimes(1);
      consoleSpy.mockRestore();
    });
  });
  describe('Render Performance Tracking', function () {
    it('tracks component render times', function () {
      var _metrics$0$metadata;
      var componentName = 'TestComponent';
      var renderTime = 50;
      _performanceMonitor.performanceMonitor.trackRender(componentName, renderTime);
      var metrics = _performanceMonitor.performanceMonitor.getMetricsByCategory('render');
      expect(metrics).toHaveLength(1);
      expect(metrics[0].name).toBe('component_render');
      expect(metrics[0].value).toBe(renderTime);
      expect((_metrics$0$metadata = metrics[0].metadata) == null ? void 0 : _metrics$0$metadata.componentName).toBe(componentName);
    });
    it('calculates average render times for components', function () {
      var componentName = 'TestComponent';
      _performanceMonitor.performanceMonitor.trackRender(componentName, 40);
      _performanceMonitor.performanceMonitor.trackRender(componentName, 60);
      var renderMetrics = _performanceMonitor.performanceMonitor['renderMetrics'];
      var componentMetrics = renderMetrics.get(componentName);
      expect(componentMetrics == null ? void 0 : componentMetrics.renderTime).toBe(50);
      expect(componentMetrics == null ? void 0 : componentMetrics.reRenders).toBe(2);
    });
    it('warns about slow renders', function () {
      var consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
      _performanceMonitor.performanceMonitor.trackRender('SlowComponent', 100);
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Slow render detected: SlowComponent took 100ms'));
      consoleSpy.mockRestore();
    });
    it('tracks render metadata correctly', function () {
      var metadata = {
        propsCount: 5,
        stateUpdates: 2
      };
      _performanceMonitor.performanceMonitor.trackRender('TestComponent', 30, metadata);
      var renderMetrics = _performanceMonitor.performanceMonitor['renderMetrics'];
      var componentMetrics = renderMetrics.get('TestComponent');
      expect(componentMetrics == null ? void 0 : componentMetrics.propsCount).toBe(5);
      expect(componentMetrics == null ? void 0 : componentMetrics.stateUpdates).toBe(2);
    });
  });
  describe('Network Performance Tracking', function () {
    it('tracks network request performance', function () {
      var _metrics$0$metadata2, _metrics$0$metadata3, _metrics$0$metadata4;
      var url = '/api/test';
      var method = 'GET';
      var responseTime = 200;
      var statusCode = 200;
      _performanceMonitor.performanceMonitor.trackNetworkRequest(url, method, responseTime, statusCode);
      var metrics = _performanceMonitor.performanceMonitor.getMetricsByCategory('network');
      expect(metrics).toHaveLength(1);
      expect(metrics[0].value).toBe(responseTime);
      expect((_metrics$0$metadata2 = metrics[0].metadata) == null ? void 0 : _metrics$0$metadata2.url).toBe(url);
      expect((_metrics$0$metadata3 = metrics[0].metadata) == null ? void 0 : _metrics$0$metadata3.method).toBe(method);
      expect((_metrics$0$metadata4 = metrics[0].metadata) == null ? void 0 : _metrics$0$metadata4.statusCode).toBe(statusCode);
    });
    it('warns about slow network requests', function () {
      var consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
      _performanceMonitor.performanceMonitor.trackNetworkRequest('/api/slow', 'GET', 3000, 200);
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Slow network request: GET /api/slow took 3000ms'));
      consoleSpy.mockRestore();
    });
    it('tracks cached vs non-cached requests', function () {
      _performanceMonitor.performanceMonitor.trackNetworkRequest('/api/cached', 'GET', 50, 200, 0, 0, true);
      _performanceMonitor.performanceMonitor.trackNetworkRequest('/api/fresh', 'GET', 200, 200, 0, 0, false);
      var networkMetrics = _performanceMonitor.performanceMonitor['networkMetrics'];
      expect(networkMetrics[0].cached).toBe(true);
      expect(networkMetrics[1].cached).toBe(false);
    });
    it('limits stored network metrics', function () {
      var maxMetrics = _performanceMonitor.performanceMonitor['MAX_METRICS'];
      for (var i = 0; i < maxMetrics + 100; i++) {
        _performanceMonitor.performanceMonitor.trackNetworkRequest(`/api/test${i}`, 'GET', 100, 200);
      }
      var networkMetrics = _performanceMonitor.performanceMonitor['networkMetrics'];
      expect(networkMetrics.length).toBeLessThanOrEqual(maxMetrics);
    });
  });
  describe('User Interaction Tracking', function () {
    it('tracks user interaction performance', function () {
      var _metrics$0$metadata5, _metrics$0$metadata6;
      var interactionType = 'button_click';
      var responseTime = 50;
      var metadata = {
        buttonId: 'submit'
      };
      _performanceMonitor.performanceMonitor.trackUserInteraction(interactionType, responseTime, metadata);
      var metrics = _performanceMonitor.performanceMonitor.getMetricsByCategory('user_interaction');
      expect(metrics).toHaveLength(1);
      expect(metrics[0].value).toBe(responseTime);
      expect((_metrics$0$metadata5 = metrics[0].metadata) == null ? void 0 : _metrics$0$metadata5.interactionType).toBe(interactionType);
      expect((_metrics$0$metadata6 = metrics[0].metadata) == null ? void 0 : _metrics$0$metadata6.buttonId).toBe('submit');
    });
    it('warns about slow interactions', function () {
      var consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
      _performanceMonitor.performanceMonitor.trackUserInteraction('slow_interaction', 150);
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Slow interaction: slow_interaction took 150ms'));
      consoleSpy.mockRestore();
    });
  });
  describe('Navigation Performance Tracking', function () {
    it('tracks navigation performance', function () {
      var _metrics$0$metadata7, _metrics$0$metadata8;
      var fromScreen = 'Home';
      var toScreen = 'Profile';
      var navigationTime = 300;
      _performanceMonitor.performanceMonitor.trackNavigation(fromScreen, toScreen, navigationTime);
      var metrics = _performanceMonitor.performanceMonitor.getMetricsByCategory('navigation');
      expect(metrics).toHaveLength(1);
      expect(metrics[0].value).toBe(navigationTime);
      expect((_metrics$0$metadata7 = metrics[0].metadata) == null ? void 0 : _metrics$0$metadata7.fromScreen).toBe(fromScreen);
      expect((_metrics$0$metadata8 = metrics[0].metadata) == null ? void 0 : _metrics$0$metadata8.toScreen).toBe(toScreen);
    });
  });
  describe('Performance Reports', function () {
    beforeEach(function () {
      _performanceMonitor.performanceMonitor.trackRender('FastComponent', 10);
      _performanceMonitor.performanceMonitor.trackRender('SlowComponent', 50);
      _performanceMonitor.performanceMonitor.trackNetworkRequest('/api/fast', 'GET', 100, 200, 0, 0, true);
      _performanceMonitor.performanceMonitor.trackNetworkRequest('/api/slow', 'GET', 3000, 200, 0, 0, false);
      _performanceMonitor.performanceMonitor.trackUserInteraction('click', 25);
    });
    it('generates comprehensive performance report', function () {
      var report = _performanceMonitor.performanceMonitor.getPerformanceReport();
      expect(report.summary).toBeDefined();
      expect(report.summary.averageRenderTime).toBeGreaterThan(0);
      expect(report.summary.averageNetworkTime).toBeGreaterThan(0);
      expect(report.summary.cacheHitRate).toBeDefined();
      expect(report.slowComponents).toBeDefined();
      expect(report.slowNetworkRequests).toBeDefined();
      expect(report.recommendations).toBeDefined();
    });
    it('calculates cache hit rate correctly', function () {
      var report = _performanceMonitor.performanceMonitor.getPerformanceReport();
      expect(report.summary.cacheHitRate).toBe(0.5);
    });
    it('identifies slow components', function () {
      var report = _performanceMonitor.performanceMonitor.getPerformanceReport();
      var slowComponent = report.slowComponents.find(function (c) {
        return c.componentName === 'SlowComponent';
      });
      expect(slowComponent).toBeDefined();
      expect(slowComponent == null ? void 0 : slowComponent.renderTime).toBe(50);
    });
    it('identifies slow network requests', function () {
      var report = _performanceMonitor.performanceMonitor.getPerformanceReport();
      var slowRequest = report.slowNetworkRequests.find(function (r) {
        return r.url === '/api/slow';
      });
      expect(slowRequest).toBeDefined();
      expect(slowRequest == null ? void 0 : slowRequest.responseTime).toBe(3000);
    });
    it('generates performance recommendations', function () {
      var report = _performanceMonitor.performanceMonitor.getPerformanceReport();
      expect(report.recommendations).toContain(expect.stringContaining('Optimize slow components'));
      expect(report.recommendations).toContain(expect.stringContaining('request caching'));
    });
  });
  describe('Memory Monitoring', function () {
    it('collects memory metrics when available', function () {
      var mockMemory = {
        usedJSHeapSize: 1000000,
        totalJSHeapSize: 2000000,
        jsHeapSizeLimit: 4000000
      };
      global.performance = {
        memory: mockMemory
      };
      _performanceMonitor.performanceMonitor['collectMemoryMetrics']();
      var memoryMetrics = _performanceMonitor.performanceMonitor['memoryMetrics'];
      expect(memoryMetrics).toHaveLength(1);
      expect(memoryMetrics[0].usedJSHeapSize).toBe(1000000);
    });
    it('detects potential memory leaks', function () {
      var memoryMetrics = _performanceMonitor.performanceMonitor['memoryMetrics'];
      for (var i = 0; i < 10; i++) {
        memoryMetrics.push({
          usedJSHeapSize: 1000000 + i * 10000000,
          totalJSHeapSize: 2000000,
          jsHeapSizeLimit: 4000000,
          timestamp: Date.now() + i
        });
      }
      var leaks = _performanceMonitor.performanceMonitor['detectMemoryLeaks']();
      expect(leaks.length).toBeGreaterThan(0);
      expect(leaks[0]).toContain('Memory usage increased');
    });
    it('detects excessive re-renders', function () {
      for (var i = 0; i < 150; i++) {
        _performanceMonitor.performanceMonitor.trackRender('ExcessiveComponent', 10);
      }
      var leaks = _performanceMonitor.performanceMonitor['detectMemoryLeaks']();
      var reRenderLeak = leaks.find(function (leak) {
        return leak.includes('ExcessiveComponent');
      });
      expect(reRenderLeak).toBeDefined();
      expect(reRenderLeak).toContain('150 re-renders');
    });
  });
  describe('Cleanup and Maintenance', function () {
    it('clears all metrics', function () {
      _performanceMonitor.performanceMonitor.trackRender('TestComponent', 50);
      _performanceMonitor.performanceMonitor.trackNetworkRequest('/api/test', 'GET', 100, 200);
      _performanceMonitor.performanceMonitor.clearMetrics();
      expect(_performanceMonitor.performanceMonitor.getMetricsByCategory('render')).toHaveLength(0);
      expect(_performanceMonitor.performanceMonitor.getMetricsByCategory('network')).toHaveLength(0);
    });
    it('cleans up old metrics automatically', function () {
      var oldTimestamp = Date.now() - 700000;
      _performanceMonitor.performanceMonitor['metrics'].push({
        name: 'old_metric',
        value: 100,
        timestamp: oldTimestamp,
        category: 'render'
      });
      _performanceMonitor.performanceMonitor['cleanupOldMetrics']();
      var oldMetric = _performanceMonitor.performanceMonitor['metrics'].find(function (m) {
        return m.timestamp === oldTimestamp;
      });
      expect(oldMetric).toBeUndefined();
    });
    it('destroys service correctly', function () {
      var clearIntervalSpy = jest.spyOn(global, 'clearInterval');
      _performanceMonitor.performanceMonitor.destroy();
      expect(clearIntervalSpy).toHaveBeenCalled();
      expect(_performanceMonitor.performanceMonitor['memoryCache'].size).toBe(0);
      clearIntervalSpy.mockRestore();
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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