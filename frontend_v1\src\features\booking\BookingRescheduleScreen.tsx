/**
 * Booking Reschedule Screen - Reschedule existing bookings
 *
 * Screen Contract:
 * - Allow users to reschedule existing bookings
 * - Display current booking details for reference
 * - Show available time slots for rescheduling
 * - Implement real-time availability checking
 * - Provide confirmation and feedback for reschedule actions
 * - Support accessibility and minimalist design principles
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  StyleSheet,
} from 'react-native';
import { useNavigation, useRoute, useFocusEffect } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';

import { useBookingStore, Booking } from '../../store/bookingSlice';
import { useTheme } from '../../contexts/ThemeContext';
import { Colors } from '../../constants/Colors';
import { LoadingSpinner } from '../../components/common/LoadingSpinner';
import { ErrorMessage } from '../../components/common/ErrorMessage';
import { useBookingFeedback } from '../../hooks/useActionFeedbackHooks';
import { useActionFeedback } from '../../components/ui/ActionFeedbackSystem';
import { StandardizedButton } from '../../components/atoms/StandardizedButton';
import { Calendar } from 'react-native-calendars';

interface RouteParams {
  bookingId: string;
}

interface TimeSlot {
  time: string;
  available: boolean;
  price?: number;
}

export const BookingRescheduleScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { bookingId } = route.params as RouteParams;
  const { colors } = useTheme();
  
  // Store hooks
  const {
    currentBooking,
    isLoading,
    error,
    loadBookingDetails,
    clearError,
  } = useBookingStore();

  // Action feedback hooks
  const { rescheduleBooking } = useBookingFeedback();
  const { showConfirmation } = useActionFeedback();

  // Local state
  const [refreshing, setRefreshing] = useState(false);
  const [selectedDate, setSelectedDate] = useState<string>('');
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<string>('');
  const [availableTimeSlots, setAvailableTimeSlots] = useState<TimeSlot[]>([]);
  const [loadingTimeSlots, setLoadingTimeSlots] = useState(false);

  // Load booking details on mount
  useFocusEffect(
    useCallback(() => {
      if (bookingId) {
        loadBookingDetails(bookingId);
      }
    }, [bookingId, loadBookingDetails])
  );

  // Load available time slots when date is selected
  useEffect(() => {
    if (selectedDate && currentBooking) {
      loadAvailableTimeSlots(selectedDate);
    }
  }, [selectedDate, currentBooking]);

  const loadAvailableTimeSlots = async (date: string) => {
    if (!currentBooking) return;

    setLoadingTimeSlots(true);
    try {
      // Mock available time slots - replace with actual API call
      const mockTimeSlots: TimeSlot[] = [
        { time: '09:00', available: true },
        { time: '10:00', available: false },
        { time: '11:00', available: true },
        { time: '12:00', available: true },
        { time: '13:00', available: false },
        { time: '14:00', available: true },
        { time: '15:00', available: true },
        { time: '16:00', available: false },
        { time: '17:00', available: true },
      ];

      setAvailableTimeSlots(mockTimeSlots);
    } catch (error) {
      console.error('Failed to load available time slots:', error);
    } finally {
      setLoadingTimeSlots(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await loadBookingDetails(bookingId);
      if (selectedDate) {
        await loadAvailableTimeSlots(selectedDate);
      }
    } catch (error) {
      console.error('Failed to refresh:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleDateSelect = (day: any) => {
    setSelectedDate(day.dateString);
    setSelectedTimeSlot(''); // Reset time slot selection
  };

  const handleTimeSlotSelect = (timeSlot: TimeSlot) => {
    if (timeSlot.available) {
      setSelectedTimeSlot(timeSlot.time);
    }
  };

  const handleReschedule = async () => {
    if (!currentBooking || !selectedDate || !selectedTimeSlot) return;

    try {
      // Show confirmation dialog
      const confirmed = await showConfirmation({
        title: 'Reschedule Booking',
        message: `Are you sure you want to reschedule your booking to ${formatDate(selectedDate)} at ${selectedTimeSlot}?`,
        confirmText: 'Reschedule',
        cancelText: 'Cancel',
      });

      if (confirmed) {
        // Reschedule booking with feedback
        await rescheduleBooking(currentBooking.id, {
          date: selectedDate,
          time: selectedTimeSlot,
        });

        // Navigate back to booking details
        navigation.goBack();
      }
    } catch (error) {
      console.error('Failed to reschedule booking:', error);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  };

  const getMinDate = () => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    return tomorrow.toISOString().split('T')[0];
  };

  const getMaxDate = () => {
    const maxDate = new Date();
    maxDate.setDate(maxDate.getDate() + 30); // 30 days from now
    return maxDate.toISOString().split('T')[0];
  };

  const canReschedule = () => {
    return selectedDate && selectedTimeSlot;
  };

  // Loading state
  if (isLoading && !currentBooking) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <LoadingSpinner size="large" />
          <Text style={styles.loadingText}>Loading booking details...</Text>
        </View>
      </View>
    );
  }

  // Error state
  if (error && !currentBooking) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <ErrorMessage
            error={error}
            onRetry={() => loadBookingDetails(bookingId)}
            title="Failed to load booking details"
          />
        </View>
      </View>
    );
  }

  // No booking found
  if (!currentBooking) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.emptyStateText}>Booking not found</Text>
          <StandardizedButton
            action="back"
            onPress={() => navigation.goBack()}
            style={{ marginTop: 16 }}
          />
        </View>
      </View>
    );
  }

  return (
    <Container>
      <ScrollView
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary || Colors.light.primary]}
          />
        }
      >
        {/* Current Booking Summary */}
        <Section>
          <SectionTitle>Current Booking</SectionTitle>
          <CurrentBookingCard>
            <BookingInfo>
              <ServiceName>{currentBooking.service.name}</ServiceName>
              <ProviderName>{currentBooking.provider.businessName}</ProviderName>
              <CurrentDateTime>
                {formatDate(currentBooking.scheduledDateTime)} at {formatTime(currentBooking.scheduledDateTime)}
              </CurrentDateTime>
            </BookingInfo>
          </CurrentBookingCard>
        </Section>

        {/* Date Selection */}
        <Section>
          <SectionTitle>Select New Date</SectionTitle>
          <CalendarContainer>
            <Calendar
              onDayPress={handleDateSelect}
              markedDates={{
                [selectedDate]: {
                  selected: true,
                  selectedColor: colors.primary || Colors.light.primary,
                },
              }}
              minDate={getMinDate()}
              maxDate={getMaxDate()}
              theme={{
                selectedDayBackgroundColor: colors.primary || Colors.light.primary,
                selectedDayTextColor: colors.surface || Colors.light.surface,
                todayTextColor: colors.primary || Colors.light.primary,
                dayTextColor: colors.textPrimary || Colors.light.textPrimary,
                textDisabledColor: colors.textSecondary || Colors.light.textSecondary,
                arrowColor: colors.primary || Colors.light.primary,
                monthTextColor: colors.textPrimary || Colors.light.textPrimary,
                indicatorColor: colors.primary || Colors.light.primary,
              }}
            />
          </CalendarContainer>
        </Section>

        {/* Time Slot Selection */}
        {selectedDate && (
          <Section>
            <SectionTitle>Available Times for {formatDate(selectedDate)}</SectionTitle>
            {loadingTimeSlots ? (
              <LoadingContainer>
                <LoadingSpinner size="small" />
                <LoadingText>Loading available times...</LoadingText>
              </LoadingContainer>
            ) : (
              <TimeSlotsContainer>
                {availableTimeSlots.map((slot) => (
                  <TimeSlotButton
                    key={slot.time}
                    available={slot.available}
                    selected={selectedTimeSlot === slot.time}
                    onPress={() => handleTimeSlotSelect(slot)}
                    disabled={!slot.available}
                  >
                    <TimeSlotText
                      available={slot.available}
                      selected={selectedTimeSlot === slot.time}
                    >
                      {slot.time}
                    </TimeSlotText>
                  </TimeSlotButton>
                ))}
              </TimeSlotsContainer>
            )}
          </Section>
        )}

        {/* Reschedule Button */}
        <Section>
          <StandardizedButton
            action="confirm"
            onPress={handleReschedule}
            disabled={!canReschedule()}
            fullWidth
            title="Reschedule Booking"
          />
        </Section>
      </ScrollView>
    </Container>
  );
};

// Styled Components
const Container = styled.View`
  flex: 1;
  background-color: ${Colors.light.background};
`;

const LoadingContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 20px;
`;

const LoadingText = styled.Text`
  margin-top: 16px;
  font-size: 16px;
  color: ${Colors.light.textSecondary};
  text-align: center;
`;

const ErrorContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 20px;
`;

const EmptyStateText = styled.Text`
  font-size: 18px;
  color: ${Colors.light.textSecondary};
  text-align: center;
`;

const Section = styled.View`
  padding: 20px;
  border-bottom-width: 1px;
  border-bottom-color: ${Colors.light.border};
`;

const SectionTitle = styled.Text`
  font-size: 18px;
  font-weight: 600;
  color: ${Colors.light.textPrimary};
  margin-bottom: 16px;
`;

const CurrentBookingCard = styled.View`
  background-color: ${Colors.light.surface};
  padding: 16px;
  border-radius: 12px;
`;

const BookingInfo = styled.View``;

const ServiceName = styled.Text`
  font-size: 16px;
  font-weight: 600;
  color: ${Colors.light.textPrimary};
  margin-bottom: 4px;
`;

const ProviderName = styled.Text`
  font-size: 14px;
  color: ${Colors.light.textSecondary};
  margin-bottom: 8px;
`;

const CurrentDateTime = styled.Text`
  font-size: 14px;
  font-weight: 500;
  color: ${Colors.light.primary};
`;

const CalendarContainer = styled.View`
  background-color: ${Colors.light.surface};
  border-radius: 12px;
  overflow: hidden;
`;

const TimeSlotsContainer = styled.View`
  flex-direction: row;
  flex-wrap: wrap;
  gap: 12px;
`;

const TimeSlotButton = styled.TouchableOpacity<{ available: boolean; selected: boolean }>`
  padding: 12px 16px;
  border-radius: 8px;
  border-width: 1px;
  border-color: ${props => {
    if (!props.available) return Colors.light.border;
    if (props.selected) return Colors.light.primary;
    return Colors.light.border;
  }};
  background-color: ${props => {
    if (!props.available) return Colors.light.border + '20';
    if (props.selected) return Colors.light.primary;
    return Colors.light.surface;
  }};
  opacity: ${props => props.available ? 1 : 0.5};
`;

const TimeSlotText = styled.Text<{ available: boolean; selected: boolean }>`
  font-size: 14px;
  font-weight: 500;
  color: ${props => {
    if (!props.available) return Colors.light.textSecondary;
    if (props.selected) return Colors.light.surface;
    return Colors.light.textPrimary;
  }};
`;

export default BookingRescheduleScreen;
