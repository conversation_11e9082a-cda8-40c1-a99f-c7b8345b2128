{"version": 3, "names": ["vanilla", "require", "react", "Object", "keys", "for<PERSON>ach", "k", "prototype", "hasOwnProperty", "call", "exports", "defineProperty", "enumerable", "get"], "sources": ["index.js"], "sourcesContent": ["'use strict';\n\nvar vanilla = require('zustand/vanilla');\nvar react = require('zustand/react');\n\n\n\nObject.keys(vanilla).forEach(function (k) {\n\tif (k !== 'default' && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {\n\t\tenumerable: true,\n\t\tget: function () { return vanilla[k]; }\n\t});\n});\nObject.keys(react).forEach(function (k) {\n\tif (k !== 'default' && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {\n\t\tenumerable: true,\n\t\tget: function () { return react[k]; }\n\t});\n});\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,OAAO,GAAGC,OAAO,CAAC,iBAAiB,CAAC;AACxC,IAAIC,KAAK,GAAGD,OAAO,CAAC,eAAe,CAAC;AAIpCE,MAAM,CAACC,IAAI,CAACJ,OAAO,CAAC,CAACK,OAAO,CAAC,UAAUC,CAAC,EAAE;EACzC,IAAIA,CAAC,KAAK,SAAS,IAAI,CAACH,MAAM,CAACI,SAAS,CAACC,cAAc,CAACC,IAAI,CAACC,OAAO,EAAEJ,CAAC,CAAC,EAAEH,MAAM,CAACQ,cAAc,CAACD,OAAO,EAAEJ,CAAC,EAAE;IAC3GM,UAAU,EAAE,IAAI;IAChBC,GAAG,EAAE,SAALA,GAAGA,CAAA,EAAc;MAAE,OAAOb,OAAO,CAACM,CAAC,CAAC;IAAE;EACvC,CAAC,CAAC;AACH,CAAC,CAAC;AACFH,MAAM,CAACC,IAAI,CAACF,KAAK,CAAC,CAACG,OAAO,CAAC,UAAUC,CAAC,EAAE;EACvC,IAAIA,CAAC,KAAK,SAAS,IAAI,CAACH,MAAM,CAACI,SAAS,CAACC,cAAc,CAACC,IAAI,CAACC,OAAO,EAAEJ,CAAC,CAAC,EAAEH,MAAM,CAACQ,cAAc,CAACD,OAAO,EAAEJ,CAAC,EAAE;IAC3GM,UAAU,EAAE,IAAI;IAChBC,GAAG,EAAE,SAALA,GAAGA,CAAA,EAAc;MAAE,OAAOX,KAAK,CAACI,CAAC,CAAC;IAAE;EACrC,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}