/**
 * Favorite Providers Screen - Saved Providers Management
 *
 * Component Contract:
 * - Displays user's favorite/saved service providers
 * - Allows removing providers from favorites
 * - Provides quick access to provider profiles and booking
 * - Integrates with backend favorites API
 * - Supports search and filtering of favorite providers
 * - Implements proper accessibility and responsive design
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Alert, TextInput } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { SafeAreaScreen } from '../components/templates/SafeAreaScreen';
import { Card } from '../components/molecules/Card';
import { StandardizedButton } from '../components/atoms/StandardizedButton';
import { AccessibleTouchable } from '../components/atoms/AccessibleTouchable';
import { ProviderCard } from '../components/molecules/ProviderCard';
import { useTheme } from '../contexts/ThemeContext';
import { getResponsiveSpacing, getResponsiveFontSize } from '../utils/responsiveUtils';
import { providerService } from '../services/providerService';
import { profileService } from '../services/profileService';

type FavoriteProvidersScreenNavigationProp = StackNavigationProp<any, 'FavoriteProviders'>;

interface FavoriteProvider {
  id: string;
  business_name: string;
  full_name: string;
  profile_image?: string;
  rating: number;
  review_count: number;
  services: string[];
  distance?: number;
  is_available: boolean;
  next_available_slot?: string;
}

export const FavoriteProvidersScreen: React.FC = () => {
  const { colors, isDark } = useTheme();
  const navigation = useNavigation<FavoriteProvidersScreenNavigationProp>();
  const styles = createStyles(colors);
  
  const [favoriteProviders, setFavoriteProviders] = useState<FavoriteProvider[]>([]);
  const [filteredProviders, setFilteredProviders] = useState<FavoriteProvider[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    loadFavoriteProviders();
  }, []);

  useEffect(() => {
    filterProviders();
  }, [searchQuery, favoriteProviders]);

  const loadFavoriteProviders = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const favorites = await profileService.getFavoriteProviders();
      setFavoriteProviders(favorites);
    } catch (err) {
      console.error('Failed to load favorite providers:', err);
      setError('Failed to load favorite providers');
    } finally {
      setLoading(false);
    }
  };

  const filterProviders = () => {
    if (!searchQuery.trim()) {
      setFilteredProviders(favoriteProviders);
      return;
    }

    const filtered = favoriteProviders.filter(provider =>
      provider.business_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      provider.full_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      provider.services.some(service => 
        service.toLowerCase().includes(searchQuery.toLowerCase())
      )
    );
    
    setFilteredProviders(filtered);
  };

  const handleRemoveFromFavorites = (provider: FavoriteProvider) => {
    Alert.alert(
      'Remove from Favorites',
      `Remove ${provider.business_name} from your favorites?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => removeFromFavorites(provider.id),
        },
      ]
    );
  };

  const removeFromFavorites = async (providerId: string) => {
    try {
      await profileService.removeFromFavorites(providerId);
      
      // Update local state
      setFavoriteProviders(prev => prev.filter(p => p.id !== providerId));
      
      Alert.alert('Success', 'Provider removed from favorites');
    } catch (error) {
      console.error('Failed to remove from favorites:', error);
      Alert.alert('Error', 'Failed to remove provider from favorites');
    }
  };

  const handleProviderPress = (provider: FavoriteProvider) => {
    navigation.navigate('ProviderDetails', { providerId: provider.id });
  };

  const handleBookProvider = (provider: FavoriteProvider) => {
    navigation.navigate('BookingFlow', { providerId: provider.id });
  };

  const renderSearchBar = () => (
    <View style={styles.searchContainer}>
      <View style={styles.searchInputContainer}>
        <Ionicons name="search" size={20} color={colors.text.tertiary} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search favorite providers..."
          placeholderTextColor={colors.text.tertiary}
          value={searchQuery}
          onChangeText={setSearchQuery}
          testID="search-input"
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity
            onPress={() => setSearchQuery('')}
            style={styles.clearButton}
            testID="clear-search">
            <Ionicons name="close-circle" size={20} color={colors.text.tertiary} />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  const renderProvider = (provider: FavoriteProvider) => (
    <Card key={provider.id} style={styles.providerCard}>
      <TouchableOpacity
        onPress={() => handleProviderPress(provider)}
        style={styles.providerContent}
        testID={`provider-${provider.id}`}>
        
        <ProviderCard
          provider={provider}
          onPress={() => handleProviderPress(provider)}
          showDistance={true}
          showAvailability={true}
        />
        
        <View style={styles.providerActions}>
          <StandardizedButton
            action="book"
            onPress={() => handleBookProvider(provider)}
            style={styles.bookButton}
            size="small"
            testID={`book-${provider.id}`}>
            Book Now
          </StandardizedButton>
          
          <TouchableOpacity
            onPress={() => handleRemoveFromFavorites(provider)}
            style={styles.favoriteButton}
            testID={`remove-favorite-${provider.id}`}>
            <Ionicons name="heart" size={20} color={colors.error} />
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    </Card>
  );

  const renderEmptyState = () => (
    <Card style={styles.emptyStateCard}>
      <View style={styles.emptyStateContent}>
        <Ionicons name="heart-outline" size={48} color={colors.text.tertiary} />
        <Text style={styles.emptyStateTitle}>No Favorite Providers</Text>
        <Text style={styles.emptyStateSubtitle}>
          {searchQuery 
            ? `No providers found matching "${searchQuery}"`
            : 'Start adding providers to your favorites to see them here'
          }
        </Text>
        {!searchQuery && (
          <StandardizedButton
            action="search"
            onPress={() => navigation.navigate('Search')}
            style={styles.exploreButton}
            testID="explore-providers">
            Explore Providers
          </StandardizedButton>
        )}
      </View>
    </Card>
  );

  return (
    <SafeAreaScreen
      backgroundColor={colors.background.primary}
      statusBarStyle={isDark ? 'light-content' : 'dark-content'}
      testID="favorite-providers-screen">
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={styles.backButton}
          testID="back-button">
          <Ionicons name="arrow-back" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Favorite Providers</Text>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}>
        
        {renderSearchBar()}
        
        {loading ? (
          <Card style={styles.loadingCard}>
            <Text style={styles.loadingText}>Loading favorite providers...</Text>
          </Card>
        ) : error ? (
          <Card style={styles.errorCard}>
            <Text style={styles.errorText}>{error}</Text>
            <StandardizedButton
              action="retry"
              onPress={loadFavoriteProviders}
              style={styles.retryButton}
              testID="retry-button">
              Try Again
            </StandardizedButton>
          </Card>
        ) : filteredProviders.length === 0 ? (
          renderEmptyState()
        ) : (
          <>
            <View style={styles.resultsHeader}>
              <Text style={styles.resultsCount}>
                {filteredProviders.length} favorite provider{filteredProviders.length !== 1 ? 's' : ''}
                {searchQuery && ` matching "${searchQuery}"`}
              </Text>
            </View>
            
            {filteredProviders.map(renderProvider)}
          </>
        )}
      </ScrollView>
    </SafeAreaScreen>
  );
};

const createStyles = (colors: any) => ({
  header: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    paddingHorizontal: getResponsiveSpacing(16),
    paddingVertical: getResponsiveSpacing(12),
    borderBottomWidth: 1,
    borderBottomColor: colors.border.primary,
  },
  backButton: {
    padding: getResponsiveSpacing(8),
    marginLeft: -getResponsiveSpacing(8),
  },
  headerTitle: {
    fontSize: getResponsiveFontSize(20),
    fontWeight: '600',
    color: colors.text.primary,
    flex: 1,
    textAlign: 'center' as const,
  },
  headerSpacer: {
    width: 40,
  },
  container: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: getResponsiveSpacing(16),
    paddingVertical: getResponsiveSpacing(16),
  },
  searchContainer: {
    marginBottom: getResponsiveSpacing(16),
  },
  searchInputContainer: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    backgroundColor: colors.background.secondary,
    borderRadius: 12,
    paddingHorizontal: getResponsiveSpacing(16),
    paddingVertical: getResponsiveSpacing(12),
    borderWidth: 1,
    borderColor: colors.border.primary,
  },
  searchInput: {
    flex: 1,
    fontSize: getResponsiveFontSize(16),
    color: colors.text.primary,
    marginLeft: getResponsiveSpacing(12),
  },
  clearButton: {
    padding: getResponsiveSpacing(4),
  },
  resultsHeader: {
    marginBottom: getResponsiveSpacing(16),
  },
  resultsCount: {
    fontSize: getResponsiveFontSize(14),
    color: colors.text.secondary,
    fontWeight: '500',
  },
  providerCard: {
    marginBottom: getResponsiveSpacing(16),
    padding: getResponsiveSpacing(16),
  },
  providerContent: {
    flex: 1,
  },
  providerActions: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'space-between' as const,
    marginTop: getResponsiveSpacing(12),
    paddingTop: getResponsiveSpacing(12),
    borderTopWidth: 1,
    borderTopColor: colors.border.secondary,
  },
  bookButton: {
    flex: 1,
    marginRight: getResponsiveSpacing(12),
  },
  favoriteButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: colors.background.secondary,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    borderWidth: 1,
    borderColor: colors.border.primary,
  },
  emptyStateCard: {
    padding: getResponsiveSpacing(32),
    alignItems: 'center' as const,
  },
  emptyStateContent: {
    alignItems: 'center' as const,
  },
  emptyStateTitle: {
    fontSize: getResponsiveFontSize(20),
    fontWeight: '600',
    color: colors.text.primary,
    marginTop: getResponsiveSpacing(16),
    marginBottom: getResponsiveSpacing(8),
  },
  emptyStateSubtitle: {
    fontSize: getResponsiveFontSize(16),
    color: colors.text.secondary,
    textAlign: 'center' as const,
    marginBottom: getResponsiveSpacing(24),
  },
  exploreButton: {
    minWidth: 200,
  },
  loadingCard: {
    padding: getResponsiveSpacing(24),
    alignItems: 'center' as const,
  },
  loadingText: {
    fontSize: getResponsiveFontSize(16),
    color: colors.text.secondary,
  },
  errorCard: {
    padding: getResponsiveSpacing(24),
    alignItems: 'center' as const,
  },
  errorText: {
    fontSize: getResponsiveFontSize(16),
    color: colors.error,
    textAlign: 'center' as const,
    marginBottom: getResponsiveSpacing(16),
  },
  retryButton: {
    minWidth: 120,
  },
});
