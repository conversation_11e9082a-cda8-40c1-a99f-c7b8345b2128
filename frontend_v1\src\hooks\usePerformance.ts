/**
 * Performance Hook - React integration for performance monitoring
 *
 * Hook Contract:
 * - Automatically tracks component render performance
 * - Provides performance measurement utilities
 * - Integrates with performance monitoring service
 * - Offers performance optimization helpers
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { useEffect, useRef, useCallback, useMemo } from 'react';
import { performanceMonitor } from '../services/performanceMonitor';

interface UsePerformanceOptions {
  componentName?: string;
  trackRenders?: boolean;
  trackInteractions?: boolean;
  enableProfiling?: boolean;
}

interface PerformanceHookResult {
  trackInteraction: (name: string, fn: () => void | Promise<void>) => Promise<void>;
  trackAsyncOperation: (name: string, operation: () => Promise<any>) => Promise<any>;
  measureRender: () => void;
  getComponentStats: () => {
    renderCount: number;
    averageRenderTime: number;
    lastRenderTime: number;
  };
}

/**
 * Main performance monitoring hook
 */
export const usePerformance = (
  options: UsePerformanceOptions = {}
): PerformanceHookResult => {
  const {
    componentName = 'UnknownComponent',
    trackRenders = true,
    trackInteractions = true,
    enableProfiling = __DEV__,
  } = options;

  const renderStartRef = useRef<number>(0);
  const renderCountRef = useRef<number>(0);
  const renderTimesRef = useRef<number[]>([]);
  const mountTimeRef = useRef<number>(Date.now());

  // Track component mount
  useEffect(() => {
    if (enableProfiling) {
      const mountTime = Date.now() - mountTimeRef.current;
      performanceMonitor.trackRender(componentName, mountTime, {
        type: 'mount',
        renderCount: 1,
      });
    }
  }, [componentName, enableProfiling]);

  // Track renders
  useEffect(() => {
    if (trackRenders && enableProfiling) {
      const renderTime = renderStartRef.current > 0 
        ? Date.now() - renderStartRef.current 
        : 0;
      
      if (renderTime > 0) {
        renderCountRef.current++;
        renderTimesRef.current.push(renderTime);
        
        // Keep only last 10 render times
        if (renderTimesRef.current.length > 10) {
          renderTimesRef.current = renderTimesRef.current.slice(-10);
        }

        performanceMonitor.trackRender(componentName, renderTime, {
          renderCount: renderCountRef.current,
          type: 'update',
        });
      }
    }
  });

  // Mark render start
  const measureRender = useCallback(() => {
    if (enableProfiling) {
      renderStartRef.current = Date.now();
    }
  }, [enableProfiling]);

  // Track user interactions
  const trackInteraction = useCallback(async (
    name: string, 
    fn: () => void | Promise<void>
  ): Promise<void> => {
    if (!trackInteractions || !enableProfiling) {
      await fn();
      return;
    }

    const startTime = Date.now();
    try {
      await fn();
    } finally {
      const duration = Date.now() - startTime;
      performanceMonitor.trackUserInteraction(name, duration, {
        componentName,
      });
    }
  }, [trackInteractions, enableProfiling, componentName]);

  // Track async operations
  const trackAsyncOperation = useCallback(async <T>(
    name: string,
    operation: () => Promise<T>
  ): Promise<T> => {
    if (!enableProfiling) {
      return await operation();
    }

    const startTime = Date.now();
    try {
      const result = await operation();
      const duration = Date.now() - startTime;
      
      performanceMonitor.trackUserInteraction(`async_${name}`, duration, {
        componentName,
        type: 'async_operation',
      });
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      performanceMonitor.trackUserInteraction(`async_${name}_error`, duration, {
        componentName,
        type: 'async_operation_error',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }, [enableProfiling, componentName]);

  // Get component performance stats
  const getComponentStats = useCallback(() => {
    const averageRenderTime = renderTimesRef.current.length > 0
      ? renderTimesRef.current.reduce((sum, time) => sum + time, 0) / renderTimesRef.current.length
      : 0;

    return {
      renderCount: renderCountRef.current,
      averageRenderTime,
      lastRenderTime: renderTimesRef.current[renderTimesRef.current.length - 1] || 0,
    };
  }, []);

  // Auto-measure renders
  useMemo(() => {
    measureRender();
  }, [measureRender]);

  return {
    trackInteraction,
    trackAsyncOperation,
    measureRender,
    getComponentStats,
  };
};

/**
 * Hook for tracking network request performance
 */
export const useNetworkPerformance = () => {
  const trackRequest = useCallback((
    url: string,
    method: string,
    startTime: number,
    endTime: number,
    statusCode: number,
    cached: boolean = false
  ) => {
    const responseTime = endTime - startTime;
    performanceMonitor.trackNetworkRequest(
      url,
      method,
      responseTime,
      statusCode,
      0, // requestSize - would need to be calculated
      0, // responseSize - would need to be calculated
      cached
    );
  }, []);

  return { trackRequest };
};

/**
 * Hook for performance-optimized state updates
 */
export const useOptimizedState = <T>(
  initialValue: T,
  componentName?: string
): [T, (value: T | ((prev: T) => T)) => void] => {
  const [state, setState] = React.useState<T>(initialValue);
  const updateCountRef = useRef<number>(0);
  const lastUpdateRef = useRef<number>(Date.now());

  const optimizedSetState = useCallback((value: T | ((prev: T) => T)) => {
    const now = Date.now();
    const timeSinceLastUpdate = now - lastUpdateRef.current;
    
    // Throttle rapid updates
    if (timeSinceLastUpdate < 16) { // 60fps threshold
      setTimeout(() => {
        setState(value);
        updateCountRef.current++;
        lastUpdateRef.current = Date.now();
      }, 16 - timeSinceLastUpdate);
    } else {
      setState(value);
      updateCountRef.current++;
      lastUpdateRef.current = now;
    }

    // Track excessive state updates
    if (updateCountRef.current > 100 && componentName) {
      console.warn(`⚠️ Excessive state updates in ${componentName}: ${updateCountRef.current}`);
    }
  }, [componentName]);

  return [state, optimizedSetState];
};

/**
 * Hook for memoizing expensive calculations with performance tracking
 */
export const usePerformanceMemo = <T>(
  factory: () => T,
  deps: React.DependencyList,
  name?: string
): T => {
  const startTimeRef = useRef<number>(0);
  
  return useMemo(() => {
    startTimeRef.current = Date.now();
    const result = factory();
    const calculationTime = Date.now() - startTimeRef.current;
    
    if (calculationTime > 10 && name) { // 10ms threshold
      console.warn(`🐌 Expensive calculation in ${name}: ${calculationTime}ms`);
      
      if (__DEV__) {
        performanceMonitor.trackUserInteraction(`memo_${name}`, calculationTime, {
          type: 'expensive_calculation',
        });
      }
    }
    
    return result;
  }, deps);
};

/**
 * Hook for performance-aware effects
 */
export const usePerformanceEffect = (
  effect: React.EffectCallback,
  deps: React.DependencyList,
  name?: string
): void => {
  useEffect(() => {
    const startTime = Date.now();
    const cleanup = effect();
    const effectTime = Date.now() - startTime;
    
    if (effectTime > 16 && name) { // 16ms threshold
      console.warn(`🐌 Slow effect in ${name}: ${effectTime}ms`);
    }
    
    return cleanup;
  }, deps);
};

/**
 * Hook for tracking component lifecycle performance
 */
export const useLifecyclePerformance = (componentName: string) => {
  const mountTimeRef = useRef<number>(Date.now());
  const updateCountRef = useRef<number>(0);

  // Track mount time
  useEffect(() => {
    const mountTime = Date.now() - mountTimeRef.current;
    performanceMonitor.trackRender(componentName, mountTime, {
      type: 'mount',
    });
  }, [componentName]);

  // Track updates
  useEffect(() => {
    updateCountRef.current++;
    
    if (updateCountRef.current > 1) { // Skip initial mount
      performanceMonitor.trackRender(componentName, 0, {
        type: 'update',
        updateCount: updateCountRef.current - 1,
      });
    }
  });

  // Track unmount
  useEffect(() => {
    return () => {
      const totalLifetime = Date.now() - mountTimeRef.current;
      performanceMonitor.trackRender(componentName, totalLifetime, {
        type: 'unmount',
        totalUpdates: updateCountRef.current - 1,
        lifetime: totalLifetime,
      });
    };
  }, [componentName]);
};

/**
 * Hook for performance debugging
 */
export const usePerformanceDebug = (componentName: string) => {
  const renderCountRef = useRef<number>(0);
  const propsRef = useRef<any>({});
  
  return useCallback((props: any) => {
    if (!__DEV__) return;
    
    renderCountRef.current++;
    
    // Check for unnecessary re-renders
    const propsChanged = Object.keys(props).some(
      key => props[key] !== propsRef.current[key]
    );
    
    if (!propsChanged && renderCountRef.current > 1) {
      console.warn(`🔄 Unnecessary re-render in ${componentName} (render #${renderCountRef.current})`);
    }
    
    propsRef.current = props;
  }, [componentName]);
};
