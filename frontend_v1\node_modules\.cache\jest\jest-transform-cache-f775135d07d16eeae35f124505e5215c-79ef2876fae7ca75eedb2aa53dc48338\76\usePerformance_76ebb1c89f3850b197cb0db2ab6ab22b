702bf2ca56a30d19c6b840c7fa2f9fcd
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.usePerformanceMemo = exports.usePerformanceEffect = exports.usePerformanceDebug = exports.usePerformance = exports.useOptimizedState = exports.useNetworkPerformance = exports.useLifecyclePerformance = void 0;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _react = require("react");
var _performanceMonitor = require("../services/performanceMonitor");
var usePerformance = exports.usePerformance = function usePerformance() {
  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  var _options$componentNam = options.componentName,
    componentName = _options$componentNam === void 0 ? 'UnknownComponent' : _options$componentNam,
    _options$trackRenders = options.trackRenders,
    trackRenders = _options$trackRenders === void 0 ? true : _options$trackRenders,
    _options$trackInterac = options.trackInteractions,
    trackInteractions = _options$trackInterac === void 0 ? true : _options$trackInterac,
    _options$enableProfil = options.enableProfiling,
    enableProfiling = _options$enableProfil === void 0 ? __DEV__ : _options$enableProfil;
  var renderStartRef = (0, _react.useRef)(0);
  var renderCountRef = (0, _react.useRef)(0);
  var renderTimesRef = (0, _react.useRef)([]);
  var mountTimeRef = (0, _react.useRef)(Date.now());
  (0, _react.useEffect)(function () {
    if (enableProfiling) {
      var mountTime = Date.now() - mountTimeRef.current;
      _performanceMonitor.performanceMonitor.trackRender(componentName, mountTime, {
        type: 'mount',
        renderCount: 1
      });
    }
  }, [componentName, enableProfiling]);
  (0, _react.useEffect)(function () {
    if (trackRenders && enableProfiling) {
      var renderTime = renderStartRef.current > 0 ? Date.now() - renderStartRef.current : 0;
      if (renderTime > 0) {
        renderCountRef.current++;
        renderTimesRef.current.push(renderTime);
        if (renderTimesRef.current.length > 10) {
          renderTimesRef.current = renderTimesRef.current.slice(-10);
        }
        _performanceMonitor.performanceMonitor.trackRender(componentName, renderTime, {
          renderCount: renderCountRef.current,
          type: 'update'
        });
      }
    }
  });
  var measureRender = (0, _react.useCallback)(function () {
    if (enableProfiling) {
      renderStartRef.current = Date.now();
    }
  }, [enableProfiling]);
  var trackInteraction = (0, _react.useCallback)(function () {
    var _ref = (0, _asyncToGenerator2.default)(function* (name, fn) {
      if (!trackInteractions || !enableProfiling) {
        yield fn();
        return;
      }
      var startTime = Date.now();
      try {
        yield fn();
      } finally {
        var duration = Date.now() - startTime;
        _performanceMonitor.performanceMonitor.trackUserInteraction(name, duration, {
          componentName: componentName
        });
      }
    });
    return function (_x, _x2) {
      return _ref.apply(this, arguments);
    };
  }(), [trackInteractions, enableProfiling, componentName]);
  var trackAsyncOperation = (0, _react.useCallback)(function () {
    var _ref2 = (0, _asyncToGenerator2.default)(function* (name, operation) {
      if (!enableProfiling) {
        return yield operation();
      }
      var startTime = Date.now();
      try {
        var result = yield operation();
        var duration = Date.now() - startTime;
        _performanceMonitor.performanceMonitor.trackUserInteraction(`async_${name}`, duration, {
          componentName: componentName,
          type: 'async_operation'
        });
        return result;
      } catch (error) {
        var _duration = Date.now() - startTime;
        _performanceMonitor.performanceMonitor.trackUserInteraction(`async_${name}_error`, _duration, {
          componentName: componentName,
          type: 'async_operation_error',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        throw error;
      }
    });
    return function (_x3, _x4) {
      return _ref2.apply(this, arguments);
    };
  }(), [enableProfiling, componentName]);
  var getComponentStats = (0, _react.useCallback)(function () {
    var averageRenderTime = renderTimesRef.current.length > 0 ? renderTimesRef.current.reduce(function (sum, time) {
      return sum + time;
    }, 0) / renderTimesRef.current.length : 0;
    return {
      renderCount: renderCountRef.current,
      averageRenderTime: averageRenderTime,
      lastRenderTime: renderTimesRef.current[renderTimesRef.current.length - 1] || 0
    };
  }, []);
  (0, _react.useMemo)(function () {
    measureRender();
  }, [measureRender]);
  return {
    trackInteraction: trackInteraction,
    trackAsyncOperation: trackAsyncOperation,
    measureRender: measureRender,
    getComponentStats: getComponentStats
  };
};
var useNetworkPerformance = exports.useNetworkPerformance = function useNetworkPerformance() {
  var trackRequest = (0, _react.useCallback)(function (url, method, startTime, endTime, statusCode) {
    var cached = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : false;
    var responseTime = endTime - startTime;
    _performanceMonitor.performanceMonitor.trackNetworkRequest(url, method, responseTime, statusCode, 0, 0, cached);
  }, []);
  return {
    trackRequest: trackRequest
  };
};
var useOptimizedState = exports.useOptimizedState = function useOptimizedState(initialValue, componentName) {
  var _React$useState = React.useState(initialValue),
    _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),
    state = _React$useState2[0],
    setState = _React$useState2[1];
  var updateCountRef = (0, _react.useRef)(0);
  var lastUpdateRef = (0, _react.useRef)(Date.now());
  var optimizedSetState = (0, _react.useCallback)(function (value) {
    var now = Date.now();
    var timeSinceLastUpdate = now - lastUpdateRef.current;
    if (timeSinceLastUpdate < 16) {
      setTimeout(function () {
        setState(value);
        updateCountRef.current++;
        lastUpdateRef.current = Date.now();
      }, 16 - timeSinceLastUpdate);
    } else {
      setState(value);
      updateCountRef.current++;
      lastUpdateRef.current = now;
    }
    if (updateCountRef.current > 100 && componentName) {
      console.warn(`⚠️ Excessive state updates in ${componentName}: ${updateCountRef.current}`);
    }
  }, [componentName]);
  return [state, optimizedSetState];
};
var usePerformanceMemo = exports.usePerformanceMemo = function usePerformanceMemo(factory, deps, name) {
  var startTimeRef = (0, _react.useRef)(0);
  return (0, _react.useMemo)(function () {
    startTimeRef.current = Date.now();
    var result = factory();
    var calculationTime = Date.now() - startTimeRef.current;
    if (calculationTime > 10 && name) {
      console.warn(`🐌 Expensive calculation in ${name}: ${calculationTime}ms`);
      if (__DEV__) {
        _performanceMonitor.performanceMonitor.trackUserInteraction(`memo_${name}`, calculationTime, {
          type: 'expensive_calculation'
        });
      }
    }
    return result;
  }, deps);
};
var usePerformanceEffect = exports.usePerformanceEffect = function usePerformanceEffect(effect, deps, name) {
  (0, _react.useEffect)(function () {
    var startTime = Date.now();
    var cleanup = effect();
    var effectTime = Date.now() - startTime;
    if (effectTime > 16 && name) {
      console.warn(`🐌 Slow effect in ${name}: ${effectTime}ms`);
    }
    return cleanup;
  }, deps);
};
var useLifecyclePerformance = exports.useLifecyclePerformance = function useLifecyclePerformance(componentName) {
  var mountTimeRef = (0, _react.useRef)(Date.now());
  var updateCountRef = (0, _react.useRef)(0);
  (0, _react.useEffect)(function () {
    var mountTime = Date.now() - mountTimeRef.current;
    _performanceMonitor.performanceMonitor.trackRender(componentName, mountTime, {
      type: 'mount'
    });
  }, [componentName]);
  (0, _react.useEffect)(function () {
    updateCountRef.current++;
    if (updateCountRef.current > 1) {
      _performanceMonitor.performanceMonitor.trackRender(componentName, 0, {
        type: 'update',
        updateCount: updateCountRef.current - 1
      });
    }
  });
  (0, _react.useEffect)(function () {
    return function () {
      var totalLifetime = Date.now() - mountTimeRef.current;
      _performanceMonitor.performanceMonitor.trackRender(componentName, totalLifetime, {
        type: 'unmount',
        totalUpdates: updateCountRef.current - 1,
        lifetime: totalLifetime
      });
    };
  }, [componentName]);
};
var usePerformanceDebug = exports.usePerformanceDebug = function usePerformanceDebug(componentName) {
  var renderCountRef = (0, _react.useRef)(0);
  var propsRef = (0, _react.useRef)({});
  return (0, _react.useCallback)(function (props) {
    if (!__DEV__) return;
    renderCountRef.current++;
    var propsChanged = Object.keys(props).some(function (key) {
      return props[key] !== propsRef.current[key];
    });
    if (!propsChanged && renderCountRef.current > 1) {
      console.warn(`🔄 Unnecessary re-render in ${componentName} (render #${renderCountRef.current})`);
    }
    propsRef.current = props;
  }, [componentName]);
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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