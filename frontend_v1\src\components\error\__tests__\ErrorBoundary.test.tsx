/**
 * ErrorBoundary Component Tests
 *
 * Test Coverage:
 * - Error catching and handling
 * - Fallback UI rendering
 * - Reset functionality
 * - Retry mechanism
 * - Integration with performance monitoring
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react-native';
import { Text, View, Button } from 'react-native';
import { ErrorBoundary } from '../ErrorBoundary';
import { performanceMonitor } from '../../../services/performanceMonitor';

// Mock dependencies
jest.mock('../../../services/performanceMonitor');

const mockPerformanceMonitor = performanceMonitor as jest.Mocked<typeof performanceMonitor>;

// Test components
const ErrorComponent: React.FC = () => {
  throw new Error('Test error');
  return null;
};

const ButtonThatThrows: React.FC<{ onError?: () => void }> = ({ onError }) => {
  const [shouldThrow, setShouldThrow] = React.useState(false);

  if (shouldThrow) {
    try {
      throw new Error('Button error');
    } catch (error) {
      if (onError) onError();
      throw error;
    }
  }

  return (
    <Button
      title="Throw Error"
      onPress={() => setShouldThrow(true)}
      testID="throw-button"
    />
  );
};

const WorkingComponent: React.FC = () => (
  <View testID="working-component">
    <Text>Working Component</Text>
  </View>
);

describe('ErrorBoundary', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Suppress React error boundary warnings in tests
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Error Handling', () => {
    it('catches errors in child components', () => {
      render(
        <ErrorBoundary>
          <ErrorComponent />
        </ErrorBoundary>
      );

      // Should render fallback UI instead of crashing
      expect(screen.getByText('Something went wrong')).toBeTruthy();
    });

    it('renders children normally when no errors occur', () => {
      render(
        <ErrorBoundary>
          <WorkingComponent />
        </ErrorBoundary>
      );

      expect(screen.getByTestID('working-component')).toBeTruthy();
      expect(screen.getByText('Working Component')).toBeTruthy();
    });

    it('calls onError callback when an error occurs', () => {
      const onError = jest.fn();

      render(
        <ErrorBoundary onError={onError}>
          <ErrorComponent />
        </ErrorBoundary>
      );

      expect(onError).toHaveBeenCalledWith(expect.objectContaining({
        message: 'Test error',
      }));
    });

    it('tracks errors with performance monitor', () => {
      render(
        <ErrorBoundary>
          <ErrorComponent />
        </ErrorBoundary>
      );

      expect(mockPerformanceMonitor.trackUserInteraction).toHaveBeenCalledWith(
        'error_boundary_catch',
        0,
        expect.objectContaining({
          error: 'Test error',
          componentStack: expect.any(String),
        })
      );
    });
  });

  describe('Fallback UI', () => {
    it('renders default fallback UI', () => {
      render(
        <ErrorBoundary>
          <ErrorComponent />
        </ErrorBoundary>
      );

      expect(screen.getByText('Something went wrong')).toBeTruthy();
      expect(screen.getByText(/We're sorry, but an error occurred/)).toBeTruthy();
      expect(screen.getByText('Try Again')).toBeTruthy();
    });

    it('renders custom fallback UI when provided', () => {
      const CustomFallback = ({ error, retry }: { error: Error, retry: () => void }) => (
        <View testID="custom-fallback">
          <Text>Custom Error: {error.message}</Text>
          <Button title="Custom Retry" onPress={retry} testID="custom-retry" />
        </View>
      );

      render(
        <ErrorBoundary fallback={(error, retry) => <CustomFallback error={error} retry={retry} />}>
          <ErrorComponent />
        </ErrorBoundary>
      );

      expect(screen.getByTestID('custom-fallback')).toBeTruthy();
      expect(screen.getByText('Custom Error: Test error')).toBeTruthy();
      expect(screen.getByText('Custom Retry')).toBeTruthy();
    });

    it('shows technical details in development mode', () => {
      // Mock __DEV__ to be true
      global.__DEV__ = true;

      render(
        <ErrorBoundary>
          <ErrorComponent />
        </ErrorBoundary>
      );

      expect(screen.getByText(/Technical Details/)).toBeTruthy();
      expect(screen.getByText('Test error')).toBeTruthy();

      // Reset __DEV__
      global.__DEV__ = false;
    });
  });

  describe('Reset Functionality', () => {
    it('resets error state when retry button is pressed', async () => {
      const TestComponent = () => {
        const [shouldError, setShouldError] = React.useState(true);

        React.useEffect(() => {
          // After first render with error, set up to not error on next render
          if (shouldError) {
            setShouldError(false);
          }
        }, [shouldError]);

        if (shouldError) {
          throw new Error('Initial error');
        }

        return <Text>Recovered Component</Text>;
      };

      render(
        <ErrorBoundary>
          <TestComponent />
        </ErrorBoundary>
      );

      // Initially shows error
      expect(screen.getByText('Something went wrong')).toBeTruthy();

      // Press retry button
      fireEvent.press(screen.getByText('Try Again'));

      // Should recover and render component
      await waitFor(() => {
        expect(screen.getByText('Recovered Component')).toBeTruthy();
      });
    });

    it('respects maxRetries limit', async () => {
      const maxRetries = 2;
      
      render(
        <ErrorBoundary maxRetries={maxRetries}>
          <ErrorComponent />
        </ErrorBoundary>
      );

      // Press retry button multiple times
      for (let i = 0; i < maxRetries; i++) {
        fireEvent.press(screen.getByText('Try Again'));
      }

      // After max retries, button should be disabled
      const retryButton = screen.getByText('Try Again');
      expect(retryButton.props.disabled).toBe(true);
    });

    it('resets on props change when resetOnPropsChange is true', async () => {
      const TestWrapper = ({ children }: { children: React.ReactNode }) => {
        const [key, setKey] = React.useState(1);

        return (
          <View>
            <ErrorBoundary resetOnPropsChange>
              {children}
            </ErrorBoundary>
            <Button 
              title="Change Props" 
              onPress={() => setKey(k => k + 1)} 
              testID="change-props"
            />
          </View>
        );
      };

      const { rerender } = render(
        <TestWrapper>
          <ErrorComponent />
        </TestWrapper>
      );

      // Initially shows error
      expect(screen.getByText('Something went wrong')).toBeTruthy();

      // Change props
      fireEvent.press(screen.getByTestID('change-props'));

      // Should attempt to re-render, but still error
      expect(screen.getByText('Something went wrong')).toBeTruthy();
    });
  });

  describe('Dynamic Error Handling', () => {
    it('catches runtime errors from user interactions', async () => {
      const onError = jest.fn();

      render(
        <ErrorBoundary onError={onError}>
          <ButtonThatThrows onError={onError} />
        </ErrorBoundary>
      );

      // Initially renders without error
      expect(screen.getByTestID('throw-button')).toBeTruthy();

      // Trigger error
      fireEvent.press(screen.getByTestID('throw-button'));

      // Should show error boundary fallback
      await waitFor(() => {
        expect(screen.getByText('Something went wrong')).toBeTruthy();
      });

      expect(onError).toHaveBeenCalledWith(expect.objectContaining({
        message: 'Button error',
      }));
    });

    it('handles nested error boundaries correctly', () => {
      const OuterFallback = () => <Text>Outer Error</Text>;
      const InnerFallback = () => <Text>Inner Error</Text>;

      render(
        <ErrorBoundary fallback={() => <OuterFallback />}>
          <View>
            <Text>Outer Content</Text>
            <ErrorBoundary fallback={() => <InnerFallback />}>
              <ErrorComponent />
            </ErrorBoundary>
          </View>
        </ErrorBoundary>
      );

      // Inner error boundary should catch the error
      expect(screen.getByText('Inner Error')).toBeTruthy();
      expect(screen.getByText('Outer Content')).toBeTruthy();
      expect(screen.queryByText('Outer Error')).toBeNull();
    });
  });

  describe('Accessibility', () => {
    it('announces errors to screen readers', () => {
      const mockAnnounce = jest.fn();
      
      // Mock AccessibilityInfo
      jest.mock('react-native', () => {
        const rn = jest.requireActual('react-native');
        return {
          ...rn,
          AccessibilityInfo: {
            ...rn.AccessibilityInfo,
            announceForAccessibility: mockAnnounce,
          },
        };
      });

      render(
        <ErrorBoundary>
          <ErrorComponent />
        </ErrorBoundary>
      );

      expect(mockAnnounce).toHaveBeenCalledWith(
        expect.stringContaining('error occurred')
      );
    });

    it('provides proper accessibility props on fallback UI', () => {
      render(
        <ErrorBoundary>
          <ErrorComponent />
        </ErrorBoundary>
      );

      const errorContainer = screen.getByTestID('error-boundary-container');
      expect(errorContainer.props.accessibilityRole).toBe('alert');
      
      const retryButton = screen.getByText('Try Again');
      expect(retryButton.props.accessibilityRole).toBe('button');
      expect(retryButton.props.accessibilityLabel).toBeTruthy();
    });
  });
});
