57ba319d14fd2ce581daed6ed6be663a
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.validateContrast = exports.rgbToHex = exports.lightenColor = exports.hexToRgb = exports.getRelativeLuminance = exports.getContrastRatio = exports.getButtonColorSuggestions = exports.fixContrast = exports.default = exports.darkenColor = exports.CONTRAST_STANDARDS = void 0;
var CONTRAST_STANDARDS = exports.CONTRAST_STANDARDS = {
  AA_NORMAL: 4.5,
  AA_LARGE: 3.0,
  AAA_NORMAL: 7.0,
  AAA_LARGE: 4.5,
  NON_TEXT: 3.0
};
var hexToRgb = exports.hexToRgb = function hexToRgb(hex) {
  var result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
};
var rgbToHex = exports.rgbToHex = function rgbToHex(r, g, b) {
  return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
};
var getRelativeLuminance = exports.getRelativeLuminance = function getRelativeLuminance(hex) {
  var rgb = hexToRgb(hex);
  if (!rgb) return 0;
  var r = rgb.r,
    g = rgb.g,
    b = rgb.b;
  var rsRGB = r / 255;
  var gsRGB = g / 255;
  var bsRGB = b / 255;
  var rLinear = rsRGB <= 0.03928 ? rsRGB / 12.92 : Math.pow((rsRGB + 0.055) / 1.055, 2.4);
  var gLinear = gsRGB <= 0.03928 ? gsRGB / 12.92 : Math.pow((gsRGB + 0.055) / 1.055, 2.4);
  var bLinear = bsRGB <= 0.03928 ? bsRGB / 12.92 : Math.pow((bsRGB + 0.055) / 1.055, 2.4);
  return 0.2126 * rLinear + 0.7152 * gLinear + 0.0722 * bLinear;
};
var getContrastRatio = exports.getContrastRatio = function getContrastRatio(foreground, background) {
  var l1 = getRelativeLuminance(foreground);
  var l2 = getRelativeLuminance(background);
  var lighter = Math.max(l1, l2);
  var darker = Math.min(l1, l2);
  return (lighter + 0.05) / (darker + 0.05);
};
var validateContrast = exports.validateContrast = function validateContrast(foreground, background) {
  var isLargeText = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
  var targetLevel = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'AA';
  var ratio = getContrastRatio(foreground, background);
  var requiredRatio = targetLevel === 'AAA' ? isLargeText ? CONTRAST_STANDARDS.AAA_LARGE : CONTRAST_STANDARDS.AAA_NORMAL : isLargeText ? CONTRAST_STANDARDS.AA_LARGE : CONTRAST_STANDARDS.AA_NORMAL;
  var isValid = ratio >= requiredRatio;
  var level = 'FAIL';
  if (ratio >= CONTRAST_STANDARDS.AAA_NORMAL || isLargeText && ratio >= CONTRAST_STANDARDS.AAA_LARGE) {
    level = 'AAA';
  } else if (ratio >= CONTRAST_STANDARDS.AA_NORMAL || isLargeText && ratio >= CONTRAST_STANDARDS.AA_LARGE) {
    level = 'AA';
  }
  var recommendation;
  if (!isValid) {
    var deficit = requiredRatio - ratio;
    recommendation = `Contrast ratio ${ratio.toFixed(2)}:1 is below ${targetLevel} standard (${requiredRatio}:1). ` + `Increase contrast by ${deficit.toFixed(2)} to meet accessibility requirements.`;
  }
  return {
    ratio: ratio,
    isValid: isValid,
    level: level,
    recommendation: recommendation
  };
};
var darkenColor = exports.darkenColor = function darkenColor(hex, percentage) {
  var rgb = hexToRgb(hex);
  if (!rgb) return hex;
  var factor = 1 - percentage / 100;
  return rgbToHex(Math.round(rgb.r * factor), Math.round(rgb.g * factor), Math.round(rgb.b * factor));
};
var lightenColor = exports.lightenColor = function lightenColor(hex, percentage) {
  var rgb = hexToRgb(hex);
  if (!rgb) return hex;
  var factor = percentage / 100;
  return rgbToHex(Math.round(rgb.r + (255 - rgb.r) * factor), Math.round(rgb.g + (255 - rgb.g) * factor), Math.round(rgb.b + (255 - rgb.b) * factor));
};
var fixContrast = exports.fixContrast = function fixContrast(foreground, background) {
  var targetRatio = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : CONTRAST_STANDARDS.AA_NORMAL;
  var adjustBackground = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;
  var currentForeground = foreground;
  var currentBackground = background;
  var currentRatio = getContrastRatio(currentForeground, currentBackground);
  if (currentRatio >= targetRatio) {
    return {
      foreground: currentForeground,
      background: currentBackground,
      ratio: currentRatio
    };
  }
  var foregroundLuminance = getRelativeLuminance(foreground);
  var backgroundLuminance = getRelativeLuminance(background);
  if (adjustBackground) {
    if (foregroundLuminance > backgroundLuminance) {
      for (var i = 5; i <= 80; i += 5) {
        var testBackground = darkenColor(background, i);
        var testRatio = getContrastRatio(foreground, testBackground);
        if (testRatio >= targetRatio) {
          return {
            foreground: foreground,
            background: testBackground,
            ratio: testRatio
          };
        }
      }
    } else {
      for (var _i = 5; _i <= 80; _i += 5) {
        var _testBackground = lightenColor(background, _i);
        var _testRatio = getContrastRatio(foreground, _testBackground);
        if (_testRatio >= targetRatio) {
          return {
            foreground: foreground,
            background: _testBackground,
            ratio: _testRatio
          };
        }
      }
    }
  } else {
    if (foregroundLuminance > backgroundLuminance) {
      for (var _i2 = 5; _i2 <= 80; _i2 += 5) {
        var testForeground = lightenColor(foreground, _i2);
        var _testRatio2 = getContrastRatio(testForeground, background);
        if (_testRatio2 >= targetRatio) {
          return {
            foreground: testForeground,
            background: background,
            ratio: _testRatio2
          };
        }
      }
    } else {
      for (var _i3 = 5; _i3 <= 80; _i3 += 5) {
        var _testForeground = darkenColor(foreground, _i3);
        var _testRatio3 = getContrastRatio(_testForeground, background);
        if (_testRatio3 >= targetRatio) {
          return {
            foreground: _testForeground,
            background: background,
            ratio: _testRatio3
          };
        }
      }
    }
  }
  if (foregroundLuminance > backgroundLuminance) {
    return {
      foreground: '#FFFFFF',
      background: darkenColor(background, 60),
      ratio: getContrastRatio('#FFFFFF', darkenColor(background, 60))
    };
  } else {
    return {
      foreground: darkenColor(foreground, 60),
      background: '#FFFFFF',
      ratio: getContrastRatio(darkenColor(foreground, 60), '#FFFFFF')
    };
  }
};
var getButtonColorSuggestions = exports.getButtonColorSuggestions = function getButtonColorSuggestions(brandColor) {
  var suggestions = {
    primary: {
      background: brandColor,
      text: '#FFFFFF'
    },
    secondary: {
      background: 'transparent',
      text: brandColor,
      border: brandColor
    },
    disabled: {
      background: '#F3F4F6',
      text: '#9CA3AF'
    }
  };
  var primaryValidation = validateContrast(suggestions.primary.text, suggestions.primary.background);
  if (!primaryValidation.isValid) {
    var fixed = fixContrast(suggestions.primary.text, suggestions.primary.background);
    suggestions.primary.background = fixed.background;
    suggestions.primary.text = fixed.foreground;
  }
  var secondaryValidation = validateContrast(suggestions.secondary.text, '#FFFFFF');
  if (!secondaryValidation.isValid) {
    var _fixed = fixContrast(suggestions.secondary.text, '#FFFFFF');
    suggestions.secondary.text = _fixed.foreground;
    suggestions.secondary.border = _fixed.foreground;
  }
  return suggestions;
};
var _default = exports.default = {
  CONTRAST_STANDARDS: CONTRAST_STANDARDS,
  hexToRgb: hexToRgb,
  rgbToHex: rgbToHex,
  getRelativeLuminance: getRelativeLuminance,
  getContrastRatio: getContrastRatio,
  validateContrast: validateContrast,
  darkenColor: darkenColor,
  lightenColor: lightenColor,
  fixContrast: fixContrast,
  getButtonColorSuggestions: getButtonColorSuggestions
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJDT05UUkFTVF9TVEFOREFSRFMiLCJleHBvcnRzIiwiQUFfTk9STUFMIiwiQUFfTEFSR0UiLCJBQUFfTk9STUFMIiwiQUFBX0xBUkdFIiwiTk9OX1RFWFQiLCJoZXhUb1JnYiIsImhleCIsInJlc3VsdCIsImV4ZWMiLCJyIiwicGFyc2VJbnQiLCJnIiwiYiIsInJnYlRvSGV4IiwidG9TdHJpbmciLCJzbGljZSIsImdldFJlbGF0aXZlTHVtaW5hbmNlIiwicmdiIiwicnNSR0IiLCJnc1JHQiIsImJzUkdCIiwickxpbmVhciIsIk1hdGgiLCJwb3ciLCJnTGluZWFyIiwiYkxpbmVhciIsImdldENvbnRyYXN0UmF0aW8iLCJmb3JlZ3JvdW5kIiwiYmFja2dyb3VuZCIsImwxIiwibDIiLCJsaWdodGVyIiwibWF4IiwiZGFya2VyIiwibWluIiwidmFsaWRhdGVDb250cmFzdCIsImlzTGFyZ2VUZXh0IiwiYXJndW1lbnRzIiwibGVuZ3RoIiwidW5kZWZpbmVkIiwidGFyZ2V0TGV2ZWwiLCJyYXRpbyIsInJlcXVpcmVkUmF0aW8iLCJpc1ZhbGlkIiwibGV2ZWwiLCJyZWNvbW1lbmRhdGlvbiIsImRlZmljaXQiLCJ0b0ZpeGVkIiwiZGFya2VuQ29sb3IiLCJwZXJjZW50YWdlIiwiZmFjdG9yIiwicm91bmQiLCJsaWdodGVuQ29sb3IiLCJmaXhDb250cmFzdCIsInRhcmdldFJhdGlvIiwiYWRqdXN0QmFja2dyb3VuZCIsImN1cnJlbnRGb3JlZ3JvdW5kIiwiY3VycmVudEJhY2tncm91bmQiLCJjdXJyZW50UmF0aW8iLCJmb3JlZ3JvdW5kTHVtaW5hbmNlIiwiYmFja2dyb3VuZEx1bWluYW5jZSIsImkiLCJ0ZXN0QmFja2dyb3VuZCIsInRlc3RSYXRpbyIsInRlc3RGb3JlZ3JvdW5kIiwiZ2V0QnV0dG9uQ29sb3JTdWdnZXN0aW9ucyIsImJyYW5kQ29sb3IiLCJzdWdnZXN0aW9ucyIsInByaW1hcnkiLCJ0ZXh0Iiwic2Vjb25kYXJ5IiwiYm9yZGVyIiwiZGlzYWJsZWQiLCJwcmltYXJ5VmFsaWRhdGlvbiIsImZpeGVkIiwic2Vjb25kYXJ5VmFsaWRhdGlvbiIsIl9kZWZhdWx0IiwiZGVmYXVsdCJdLCJzb3VyY2VzIjpbImNvbnRyYXN0VmFsaWRhdG9yLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogV0NBRyAyLjEgQUEgQ29udHJhc3QgVmFsaWRhdG9yIGFuZCBGaXhlclxuICogXG4gKiBJbXBsZW1lbnRzIFJFQy1BQ0MtMDAxOiBGaXggYnV0dG9uIGNvbnRyYXN0IHJhdGlvcyB0byBtZWV0IFdDQUcgMi4xIEFBIHN0YW5kYXJkcy5cbiAqIFByb3ZpZGVzIHV0aWxpdGllcyB0byB2YWxpZGF0ZSBhbmQgYXV0b21hdGljYWxseSBmaXggY29sb3IgY29udHJhc3QgaXNzdWVzLlxuICogXG4gKiBXQ0FHIDIuMSBBQSBSZXF1aXJlbWVudHM6XG4gKiAtIE5vcm1hbCB0ZXh0OiA0LjU6MSBjb250cmFzdCByYXRpbyBtaW5pbXVtXG4gKiAtIExhcmdlIHRleHQgKDE4cHQrIG9yIDE0cHQrIGJvbGQpOiAzOjEgY29udHJhc3QgcmF0aW8gbWluaW11bVxuICogLSBOb24tdGV4dCBlbGVtZW50czogMzoxIGNvbnRyYXN0IHJhdGlvIG1pbmltdW1cbiAqIFxuICogQHZlcnNpb24gMS4wLjBcbiAqIEBhdXRob3IgVmllcmxhIERldmVsb3BtZW50IFRlYW1cbiAqL1xuXG4vLyBXQ0FHIDIuMSBBQSBjb250cmFzdCBzdGFuZGFyZHNcbmV4cG9ydCBjb25zdCBDT05UUkFTVF9TVEFOREFSRFMgPSB7XG4gIEFBX05PUk1BTDogNC41LFxuICBBQV9MQVJHRTogMy4wLFxuICBBQUFfTk9STUFMOiA3LjAsXG4gIEFBQV9MQVJHRTogNC41LFxuICBOT05fVEVYVDogMy4wLFxufSBhcyBjb25zdDtcblxuLy8gQ29sb3IgY29udHJhc3QgdmFsaWRhdGlvbiByZXN1bHRcbmV4cG9ydCBpbnRlcmZhY2UgQ29udHJhc3RWYWxpZGF0aW9uUmVzdWx0IHtcbiAgcmF0aW86IG51bWJlcjtcbiAgaXNWYWxpZDogYm9vbGVhbjtcbiAgbGV2ZWw6ICdBQScgfCAnQUFBJyB8ICdGQUlMJztcbiAgcmVjb21tZW5kYXRpb24/OiBzdHJpbmc7XG4gIHN1Z2dlc3RlZENvbG9ycz86IHtcbiAgICBmb3JlZ3JvdW5kPzogc3RyaW5nO1xuICAgIGJhY2tncm91bmQ/OiBzdHJpbmc7XG4gIH07XG59XG5cbi8qKlxuICogQ29udmVydCBoZXggY29sb3IgdG8gUkdCIHZhbHVlc1xuICovXG5leHBvcnQgY29uc3QgaGV4VG9SZ2IgPSAoaGV4OiBzdHJpbmcpOiB7IHI6IG51bWJlcjsgZzogbnVtYmVyOyBiOiBudW1iZXIgfSB8IG51bGwgPT4ge1xuICBjb25zdCByZXN1bHQgPSAvXiM/KFthLWZcXGRdezJ9KShbYS1mXFxkXXsyfSkoW2EtZlxcZF17Mn0pJC9pLmV4ZWMoaGV4KTtcbiAgcmV0dXJuIHJlc3VsdCA/IHtcbiAgICByOiBwYXJzZUludChyZXN1bHRbMV0sIDE2KSxcbiAgICBnOiBwYXJzZUludChyZXN1bHRbMl0sIDE2KSxcbiAgICBiOiBwYXJzZUludChyZXN1bHRbM10sIDE2KSxcbiAgfSA6IG51bGw7XG59O1xuXG4vKipcbiAqIENvbnZlcnQgUkdCIHRvIGhleCBjb2xvclxuICovXG5leHBvcnQgY29uc3QgcmdiVG9IZXggPSAocjogbnVtYmVyLCBnOiBudW1iZXIsIGI6IG51bWJlcik6IHN0cmluZyA9PiB7XG4gIHJldHVybiBcIiNcIiArICgoMSA8PCAyNCkgKyAociA8PCAxNikgKyAoZyA8PCA4KSArIGIpLnRvU3RyaW5nKDE2KS5zbGljZSgxKTtcbn07XG5cbi8qKlxuICogQ2FsY3VsYXRlIHJlbGF0aXZlIGx1bWluYW5jZSBvZiBhIGNvbG9yXG4gKi9cbmV4cG9ydCBjb25zdCBnZXRSZWxhdGl2ZUx1bWluYW5jZSA9IChoZXg6IHN0cmluZyk6IG51bWJlciA9PiB7XG4gIGNvbnN0IHJnYiA9IGhleFRvUmdiKGhleCk7XG4gIGlmICghcmdiKSByZXR1cm4gMDtcblxuICBjb25zdCB7IHIsIGcsIGIgfSA9IHJnYjtcbiAgXG4gIC8vIENvbnZlcnQgdG8gc1JHQlxuICBjb25zdCByc1JHQiA9IHIgLyAyNTU7XG4gIGNvbnN0IGdzUkdCID0gZyAvIDI1NTtcbiAgY29uc3QgYnNSR0IgPSBiIC8gMjU1O1xuXG4gIC8vIEFwcGx5IGdhbW1hIGNvcnJlY3Rpb25cbiAgY29uc3QgckxpbmVhciA9IHJzUkdCIDw9IDAuMDM5MjggPyByc1JHQiAvIDEyLjkyIDogTWF0aC5wb3coKHJzUkdCICsgMC4wNTUpIC8gMS4wNTUsIDIuNCk7XG4gIGNvbnN0IGdMaW5lYXIgPSBnc1JHQiA8PSAwLjAzOTI4ID8gZ3NSR0IgLyAxMi45MiA6IE1hdGgucG93KChnc1JHQiArIDAuMDU1KSAvIDEuMDU1LCAyLjQpO1xuICBjb25zdCBiTGluZWFyID0gYnNSR0IgPD0gMC4wMzkyOCA/IGJzUkdCIC8gMTIuOTIgOiBNYXRoLnBvdygoYnNSR0IgKyAwLjA1NSkgLyAxLjA1NSwgMi40KTtcblxuICAvLyBDYWxjdWxhdGUgcmVsYXRpdmUgbHVtaW5hbmNlXG4gIHJldHVybiAwLjIxMjYgKiByTGluZWFyICsgMC43MTUyICogZ0xpbmVhciArIDAuMDcyMiAqIGJMaW5lYXI7XG59O1xuXG4vKipcbiAqIENhbGN1bGF0ZSBjb250cmFzdCByYXRpbyBiZXR3ZWVuIHR3byBjb2xvcnNcbiAqL1xuZXhwb3J0IGNvbnN0IGdldENvbnRyYXN0UmF0aW8gPSAoZm9yZWdyb3VuZDogc3RyaW5nLCBiYWNrZ3JvdW5kOiBzdHJpbmcpOiBudW1iZXIgPT4ge1xuICBjb25zdCBsMSA9IGdldFJlbGF0aXZlTHVtaW5hbmNlKGZvcmVncm91bmQpO1xuICBjb25zdCBsMiA9IGdldFJlbGF0aXZlTHVtaW5hbmNlKGJhY2tncm91bmQpO1xuICBcbiAgY29uc3QgbGlnaHRlciA9IE1hdGgubWF4KGwxLCBsMik7XG4gIGNvbnN0IGRhcmtlciA9IE1hdGgubWluKGwxLCBsMik7XG4gIFxuICByZXR1cm4gKGxpZ2h0ZXIgKyAwLjA1KSAvIChkYXJrZXIgKyAwLjA1KTtcbn07XG5cbi8qKlxuICogVmFsaWRhdGUgY29udHJhc3QgcmF0aW8gYWdhaW5zdCBXQ0FHIHN0YW5kYXJkc1xuICovXG5leHBvcnQgY29uc3QgdmFsaWRhdGVDb250cmFzdCA9IChcbiAgZm9yZWdyb3VuZDogc3RyaW5nLFxuICBiYWNrZ3JvdW5kOiBzdHJpbmcsXG4gIGlzTGFyZ2VUZXh0OiBib29sZWFuID0gZmFsc2UsXG4gIHRhcmdldExldmVsOiAnQUEnIHwgJ0FBQScgPSAnQUEnXG4pOiBDb250cmFzdFZhbGlkYXRpb25SZXN1bHQgPT4ge1xuICBjb25zdCByYXRpbyA9IGdldENvbnRyYXN0UmF0aW8oZm9yZWdyb3VuZCwgYmFja2dyb3VuZCk7XG4gIFxuICBjb25zdCByZXF1aXJlZFJhdGlvID0gdGFyZ2V0TGV2ZWwgPT09ICdBQUEnIFxuICAgID8gKGlzTGFyZ2VUZXh0ID8gQ09OVFJBU1RfU1RBTkRBUkRTLkFBQV9MQVJHRSA6IENPTlRSQVNUX1NUQU5EQVJEUy5BQUFfTk9STUFMKVxuICAgIDogKGlzTGFyZ2VUZXh0ID8gQ09OVFJBU1RfU1RBTkRBUkRTLkFBX0xBUkdFIDogQ09OVFJBU1RfU1RBTkRBUkRTLkFBX05PUk1BTCk7XG4gIFxuICBjb25zdCBpc1ZhbGlkID0gcmF0aW8gPj0gcmVxdWlyZWRSYXRpbztcbiAgXG4gIGxldCBsZXZlbDogJ0FBJyB8ICdBQUEnIHwgJ0ZBSUwnID0gJ0ZBSUwnO1xuICBpZiAocmF0aW8gPj0gQ09OVFJBU1RfU1RBTkRBUkRTLkFBQV9OT1JNQUwgfHwgKGlzTGFyZ2VUZXh0ICYmIHJhdGlvID49IENPTlRSQVNUX1NUQU5EQVJEUy5BQUFfTEFSR0UpKSB7XG4gICAgbGV2ZWwgPSAnQUFBJztcbiAgfSBlbHNlIGlmIChyYXRpbyA+PSBDT05UUkFTVF9TVEFOREFSRFMuQUFfTk9STUFMIHx8IChpc0xhcmdlVGV4dCAmJiByYXRpbyA+PSBDT05UUkFTVF9TVEFOREFSRFMuQUFfTEFSR0UpKSB7XG4gICAgbGV2ZWwgPSAnQUEnO1xuICB9XG4gIFxuICBsZXQgcmVjb21tZW5kYXRpb246IHN0cmluZyB8IHVuZGVmaW5lZDtcbiAgaWYgKCFpc1ZhbGlkKSB7XG4gICAgY29uc3QgZGVmaWNpdCA9IHJlcXVpcmVkUmF0aW8gLSByYXRpbztcbiAgICByZWNvbW1lbmRhdGlvbiA9IGBDb250cmFzdCByYXRpbyAke3JhdGlvLnRvRml4ZWQoMil9OjEgaXMgYmVsb3cgJHt0YXJnZXRMZXZlbH0gc3RhbmRhcmQgKCR7cmVxdWlyZWRSYXRpb306MSkuIGAgK1xuICAgICAgYEluY3JlYXNlIGNvbnRyYXN0IGJ5ICR7ZGVmaWNpdC50b0ZpeGVkKDIpfSB0byBtZWV0IGFjY2Vzc2liaWxpdHkgcmVxdWlyZW1lbnRzLmA7XG4gIH1cbiAgXG4gIHJldHVybiB7XG4gICAgcmF0aW8sXG4gICAgaXNWYWxpZCxcbiAgICBsZXZlbCxcbiAgICByZWNvbW1lbmRhdGlvbixcbiAgfTtcbn07XG5cbi8qKlxuICogRGFya2VuIGEgY29sb3IgYnkgYSBwZXJjZW50YWdlXG4gKi9cbmV4cG9ydCBjb25zdCBkYXJrZW5Db2xvciA9IChoZXg6IHN0cmluZywgcGVyY2VudGFnZTogbnVtYmVyKTogc3RyaW5nID0+IHtcbiAgY29uc3QgcmdiID0gaGV4VG9SZ2IoaGV4KTtcbiAgaWYgKCFyZ2IpIHJldHVybiBoZXg7XG4gIFxuICBjb25zdCBmYWN0b3IgPSAxIC0gKHBlcmNlbnRhZ2UgLyAxMDApO1xuICByZXR1cm4gcmdiVG9IZXgoXG4gICAgTWF0aC5yb3VuZChyZ2IuciAqIGZhY3RvciksXG4gICAgTWF0aC5yb3VuZChyZ2IuZyAqIGZhY3RvciksXG4gICAgTWF0aC5yb3VuZChyZ2IuYiAqIGZhY3RvcilcbiAgKTtcbn07XG5cbi8qKlxuICogTGlnaHRlbiBhIGNvbG9yIGJ5IGEgcGVyY2VudGFnZVxuICovXG5leHBvcnQgY29uc3QgbGlnaHRlbkNvbG9yID0gKGhleDogc3RyaW5nLCBwZXJjZW50YWdlOiBudW1iZXIpOiBzdHJpbmcgPT4ge1xuICBjb25zdCByZ2IgPSBoZXhUb1JnYihoZXgpO1xuICBpZiAoIXJnYikgcmV0dXJuIGhleDtcbiAgXG4gIGNvbnN0IGZhY3RvciA9IHBlcmNlbnRhZ2UgLyAxMDA7XG4gIHJldHVybiByZ2JUb0hleChcbiAgICBNYXRoLnJvdW5kKHJnYi5yICsgKDI1NSAtIHJnYi5yKSAqIGZhY3RvciksXG4gICAgTWF0aC5yb3VuZChyZ2IuZyArICgyNTUgLSByZ2IuZykgKiBmYWN0b3IpLFxuICAgIE1hdGgucm91bmQocmdiLmIgKyAoMjU1IC0gcmdiLmIpICogZmFjdG9yKVxuICApO1xufTtcblxuLyoqXG4gKiBBdXRvbWF0aWNhbGx5IGZpeCBjb250cmFzdCBieSBhZGp1c3RpbmcgY29sb3JzXG4gKi9cbmV4cG9ydCBjb25zdCBmaXhDb250cmFzdCA9IChcbiAgZm9yZWdyb3VuZDogc3RyaW5nLFxuICBiYWNrZ3JvdW5kOiBzdHJpbmcsXG4gIHRhcmdldFJhdGlvOiBudW1iZXIgPSBDT05UUkFTVF9TVEFOREFSRFMuQUFfTk9STUFMLFxuICBhZGp1c3RCYWNrZ3JvdW5kOiBib29sZWFuID0gZmFsc2Vcbik6IHsgZm9yZWdyb3VuZDogc3RyaW5nOyBiYWNrZ3JvdW5kOiBzdHJpbmc7IHJhdGlvOiBudW1iZXIgfSA9PiB7XG4gIGxldCBjdXJyZW50Rm9yZWdyb3VuZCA9IGZvcmVncm91bmQ7XG4gIGxldCBjdXJyZW50QmFja2dyb3VuZCA9IGJhY2tncm91bmQ7XG4gIGxldCBjdXJyZW50UmF0aW8gPSBnZXRDb250cmFzdFJhdGlvKGN1cnJlbnRGb3JlZ3JvdW5kLCBjdXJyZW50QmFja2dyb3VuZCk7XG4gIFxuICAvLyBJZiBhbHJlYWR5IG1lZXRzIHRhcmdldCwgcmV0dXJuIGFzLWlzXG4gIGlmIChjdXJyZW50UmF0aW8gPj0gdGFyZ2V0UmF0aW8pIHtcbiAgICByZXR1cm4geyBmb3JlZ3JvdW5kOiBjdXJyZW50Rm9yZWdyb3VuZCwgYmFja2dyb3VuZDogY3VycmVudEJhY2tncm91bmQsIHJhdGlvOiBjdXJyZW50UmF0aW8gfTtcbiAgfVxuICBcbiAgLy8gRGV0ZXJtaW5lIHdoaWNoIGNvbG9yIHRvIGFkanVzdFxuICBjb25zdCBmb3JlZ3JvdW5kTHVtaW5hbmNlID0gZ2V0UmVsYXRpdmVMdW1pbmFuY2UoZm9yZWdyb3VuZCk7XG4gIGNvbnN0IGJhY2tncm91bmRMdW1pbmFuY2UgPSBnZXRSZWxhdGl2ZUx1bWluYW5jZShiYWNrZ3JvdW5kKTtcbiAgXG4gIGlmIChhZGp1c3RCYWNrZ3JvdW5kKSB7XG4gICAgLy8gQWRqdXN0IGJhY2tncm91bmQgY29sb3JcbiAgICBpZiAoZm9yZWdyb3VuZEx1bWluYW5jZSA+IGJhY2tncm91bmRMdW1pbmFuY2UpIHtcbiAgICAgIC8vIERhcmtlbiBiYWNrZ3JvdW5kXG4gICAgICBmb3IgKGxldCBpID0gNTsgaSA8PSA4MDsgaSArPSA1KSB7XG4gICAgICAgIGNvbnN0IHRlc3RCYWNrZ3JvdW5kID0gZGFya2VuQ29sb3IoYmFja2dyb3VuZCwgaSk7XG4gICAgICAgIGNvbnN0IHRlc3RSYXRpbyA9IGdldENvbnRyYXN0UmF0aW8oZm9yZWdyb3VuZCwgdGVzdEJhY2tncm91bmQpO1xuICAgICAgICBpZiAodGVzdFJhdGlvID49IHRhcmdldFJhdGlvKSB7XG4gICAgICAgICAgcmV0dXJuIHsgZm9yZWdyb3VuZCwgYmFja2dyb3VuZDogdGVzdEJhY2tncm91bmQsIHJhdGlvOiB0ZXN0UmF0aW8gfTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0gZWxzZSB7XG4gICAgICAvLyBMaWdodGVuIGJhY2tncm91bmRcbiAgICAgIGZvciAobGV0IGkgPSA1OyBpIDw9IDgwOyBpICs9IDUpIHtcbiAgICAgICAgY29uc3QgdGVzdEJhY2tncm91bmQgPSBsaWdodGVuQ29sb3IoYmFja2dyb3VuZCwgaSk7XG4gICAgICAgIGNvbnN0IHRlc3RSYXRpbyA9IGdldENvbnRyYXN0UmF0aW8oZm9yZWdyb3VuZCwgdGVzdEJhY2tncm91bmQpO1xuICAgICAgICBpZiAodGVzdFJhdGlvID49IHRhcmdldFJhdGlvKSB7XG4gICAgICAgICAgcmV0dXJuIHsgZm9yZWdyb3VuZCwgYmFja2dyb3VuZDogdGVzdEJhY2tncm91bmQsIHJhdGlvOiB0ZXN0UmF0aW8gfTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfSBlbHNlIHtcbiAgICAvLyBBZGp1c3QgZm9yZWdyb3VuZCBjb2xvclxuICAgIGlmIChmb3JlZ3JvdW5kTHVtaW5hbmNlID4gYmFja2dyb3VuZEx1bWluYW5jZSkge1xuICAgICAgLy8gTGlnaHRlbiBmb3JlZ3JvdW5kXG4gICAgICBmb3IgKGxldCBpID0gNTsgaSA8PSA4MDsgaSArPSA1KSB7XG4gICAgICAgIGNvbnN0IHRlc3RGb3JlZ3JvdW5kID0gbGlnaHRlbkNvbG9yKGZvcmVncm91bmQsIGkpO1xuICAgICAgICBjb25zdCB0ZXN0UmF0aW8gPSBnZXRDb250cmFzdFJhdGlvKHRlc3RGb3JlZ3JvdW5kLCBiYWNrZ3JvdW5kKTtcbiAgICAgICAgaWYgKHRlc3RSYXRpbyA+PSB0YXJnZXRSYXRpbykge1xuICAgICAgICAgIHJldHVybiB7IGZvcmVncm91bmQ6IHRlc3RGb3JlZ3JvdW5kLCBiYWNrZ3JvdW5kLCByYXRpbzogdGVzdFJhdGlvIH07XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9IGVsc2Uge1xuICAgICAgLy8gRGFya2VuIGZvcmVncm91bmRcbiAgICAgIGZvciAobGV0IGkgPSA1OyBpIDw9IDgwOyBpICs9IDUpIHtcbiAgICAgICAgY29uc3QgdGVzdEZvcmVncm91bmQgPSBkYXJrZW5Db2xvcihmb3JlZ3JvdW5kLCBpKTtcbiAgICAgICAgY29uc3QgdGVzdFJhdGlvID0gZ2V0Q29udHJhc3RSYXRpbyh0ZXN0Rm9yZWdyb3VuZCwgYmFja2dyb3VuZCk7XG4gICAgICAgIGlmICh0ZXN0UmF0aW8gPj0gdGFyZ2V0UmF0aW8pIHtcbiAgICAgICAgICByZXR1cm4geyBmb3JlZ3JvdW5kOiB0ZXN0Rm9yZWdyb3VuZCwgYmFja2dyb3VuZCwgcmF0aW86IHRlc3RSYXRpbyB9O1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9XG4gIFxuICAvLyBJZiB3ZSBjYW4ndCBmaXggaXQgYnkgYWRqdXN0aW5nIG9uZSBjb2xvciwgdHJ5IGV4dHJlbWUgdmFsdWVzXG4gIGlmIChmb3JlZ3JvdW5kTHVtaW5hbmNlID4gYmFja2dyb3VuZEx1bWluYW5jZSkge1xuICAgIHJldHVybiB7IGZvcmVncm91bmQ6ICcjRkZGRkZGJywgYmFja2dyb3VuZDogZGFya2VuQ29sb3IoYmFja2dyb3VuZCwgNjApLCByYXRpbzogZ2V0Q29udHJhc3RSYXRpbygnI0ZGRkZGRicsIGRhcmtlbkNvbG9yKGJhY2tncm91bmQsIDYwKSkgfTtcbiAgfSBlbHNlIHtcbiAgICByZXR1cm4geyBmb3JlZ3JvdW5kOiBkYXJrZW5Db2xvcihmb3JlZ3JvdW5kLCA2MCksIGJhY2tncm91bmQ6ICcjRkZGRkZGJywgcmF0aW86IGdldENvbnRyYXN0UmF0aW8oZGFya2VuQ29sb3IoZm9yZWdyb3VuZCwgNjApLCAnI0ZGRkZGRicpIH07XG4gIH1cbn07XG5cbi8qKlxuICogR2V0IFdDQUctY29tcGxpYW50IGNvbG9yIHN1Z2dlc3Rpb25zIGZvciBjb21tb24gYnV0dG9uIHN0YXRlc1xuICovXG5leHBvcnQgY29uc3QgZ2V0QnV0dG9uQ29sb3JTdWdnZXN0aW9ucyA9IChicmFuZENvbG9yOiBzdHJpbmcpID0+IHtcbiAgY29uc3Qgc3VnZ2VzdGlvbnMgPSB7XG4gICAgcHJpbWFyeToge1xuICAgICAgYmFja2dyb3VuZDogYnJhbmRDb2xvcixcbiAgICAgIHRleHQ6ICcjRkZGRkZGJyxcbiAgICB9LFxuICAgIHNlY29uZGFyeToge1xuICAgICAgYmFja2dyb3VuZDogJ3RyYW5zcGFyZW50JyxcbiAgICAgIHRleHQ6IGJyYW5kQ29sb3IsXG4gICAgICBib3JkZXI6IGJyYW5kQ29sb3IsXG4gICAgfSxcbiAgICBkaXNhYmxlZDoge1xuICAgICAgYmFja2dyb3VuZDogJyNGM0Y0RjYnLFxuICAgICAgdGV4dDogJyM5Q0EzQUYnLFxuICAgIH0sXG4gIH07XG4gIFxuICAvLyBWYWxpZGF0ZSBhbmQgZml4IHByaW1hcnkgYnV0dG9uXG4gIGNvbnN0IHByaW1hcnlWYWxpZGF0aW9uID0gdmFsaWRhdGVDb250cmFzdChzdWdnZXN0aW9ucy5wcmltYXJ5LnRleHQsIHN1Z2dlc3Rpb25zLnByaW1hcnkuYmFja2dyb3VuZCk7XG4gIGlmICghcHJpbWFyeVZhbGlkYXRpb24uaXNWYWxpZCkge1xuICAgIGNvbnN0IGZpeGVkID0gZml4Q29udHJhc3Qoc3VnZ2VzdGlvbnMucHJpbWFyeS50ZXh0LCBzdWdnZXN0aW9ucy5wcmltYXJ5LmJhY2tncm91bmQpO1xuICAgIHN1Z2dlc3Rpb25zLnByaW1hcnkuYmFja2dyb3VuZCA9IGZpeGVkLmJhY2tncm91bmQ7XG4gICAgc3VnZ2VzdGlvbnMucHJpbWFyeS50ZXh0ID0gZml4ZWQuZm9yZWdyb3VuZDtcbiAgfVxuICBcbiAgLy8gVmFsaWRhdGUgYW5kIGZpeCBzZWNvbmRhcnkgYnV0dG9uXG4gIGNvbnN0IHNlY29uZGFyeVZhbGlkYXRpb24gPSB2YWxpZGF0ZUNvbnRyYXN0KHN1Z2dlc3Rpb25zLnNlY29uZGFyeS50ZXh0LCAnI0ZGRkZGRicpOyAvLyBBc3N1bWluZyB3aGl0ZSBiYWNrZ3JvdW5kXG4gIGlmICghc2Vjb25kYXJ5VmFsaWRhdGlvbi5pc1ZhbGlkKSB7XG4gICAgY29uc3QgZml4ZWQgPSBmaXhDb250cmFzdChzdWdnZXN0aW9ucy5zZWNvbmRhcnkudGV4dCwgJyNGRkZGRkYnKTtcbiAgICBzdWdnZXN0aW9ucy5zZWNvbmRhcnkudGV4dCA9IGZpeGVkLmZvcmVncm91bmQ7XG4gICAgc3VnZ2VzdGlvbnMuc2Vjb25kYXJ5LmJvcmRlciA9IGZpeGVkLmZvcmVncm91bmQ7XG4gIH1cbiAgXG4gIHJldHVybiBzdWdnZXN0aW9ucztcbn07XG5cbmV4cG9ydCBkZWZhdWx0IHtcbiAgQ09OVFJBU1RfU1RBTkRBUkRTLFxuICBoZXhUb1JnYixcbiAgcmdiVG9IZXgsXG4gIGdldFJlbGF0aXZlTHVtaW5hbmNlLFxuICBnZXRDb250cmFzdFJhdGlvLFxuICB2YWxpZGF0ZUNvbnRyYXN0LFxuICBkYXJrZW5Db2xvcixcbiAgbGlnaHRlbkNvbG9yLFxuICBmaXhDb250cmFzdCxcbiAgZ2V0QnV0dG9uQ29sb3JTdWdnZXN0aW9ucyxcbn07XG4iXSwibWFwcGluZ3MiOiI7Ozs7QUFnQk8sSUFBTUEsa0JBQWtCLEdBQUFDLE9BQUEsQ0FBQUQsa0JBQUEsR0FBRztFQUNoQ0UsU0FBUyxFQUFFLEdBQUc7RUFDZEMsUUFBUSxFQUFFLEdBQUc7RUFDYkMsVUFBVSxFQUFFLEdBQUc7RUFDZkMsU0FBUyxFQUFFLEdBQUc7RUFDZEMsUUFBUSxFQUFFO0FBQ1osQ0FBVTtBQWlCSCxJQUFNQyxRQUFRLEdBQUFOLE9BQUEsQ0FBQU0sUUFBQSxHQUFHLFNBQVhBLFFBQVFBLENBQUlDLEdBQVcsRUFBaUQ7RUFDbkYsSUFBTUMsTUFBTSxHQUFHLDJDQUEyQyxDQUFDQyxJQUFJLENBQUNGLEdBQUcsQ0FBQztFQUNwRSxPQUFPQyxNQUFNLEdBQUc7SUFDZEUsQ0FBQyxFQUFFQyxRQUFRLENBQUNILE1BQU0sQ0FBQyxDQUFDLENBQUMsRUFBRSxFQUFFLENBQUM7SUFDMUJJLENBQUMsRUFBRUQsUUFBUSxDQUFDSCxNQUFNLENBQUMsQ0FBQyxDQUFDLEVBQUUsRUFBRSxDQUFDO0lBQzFCSyxDQUFDLEVBQUVGLFFBQVEsQ0FBQ0gsTUFBTSxDQUFDLENBQUMsQ0FBQyxFQUFFLEVBQUU7RUFDM0IsQ0FBQyxHQUFHLElBQUk7QUFDVixDQUFDO0FBS00sSUFBTU0sUUFBUSxHQUFBZCxPQUFBLENBQUFjLFFBQUEsR0FBRyxTQUFYQSxRQUFRQSxDQUFJSixDQUFTLEVBQUVFLENBQVMsRUFBRUMsQ0FBUyxFQUFhO0VBQ25FLE9BQU8sR0FBRyxHQUFHLENBQUMsQ0FBQyxDQUFDLElBQUksRUFBRSxLQUFLSCxDQUFDLElBQUksRUFBRSxDQUFDLElBQUlFLENBQUMsSUFBSSxDQUFDLENBQUMsR0FBR0MsQ0FBQyxFQUFFRSxRQUFRLENBQUMsRUFBRSxDQUFDLENBQUNDLEtBQUssQ0FBQyxDQUFDLENBQUM7QUFDM0UsQ0FBQztBQUtNLElBQU1DLG9CQUFvQixHQUFBakIsT0FBQSxDQUFBaUIsb0JBQUEsR0FBRyxTQUF2QkEsb0JBQW9CQSxDQUFJVixHQUFXLEVBQWE7RUFDM0QsSUFBTVcsR0FBRyxHQUFHWixRQUFRLENBQUNDLEdBQUcsQ0FBQztFQUN6QixJQUFJLENBQUNXLEdBQUcsRUFBRSxPQUFPLENBQUM7RUFFbEIsSUFBUVIsQ0FBQyxHQUFXUSxHQUFHLENBQWZSLENBQUM7SUFBRUUsQ0FBQyxHQUFRTSxHQUFHLENBQVpOLENBQUM7SUFBRUMsQ0FBQyxHQUFLSyxHQUFHLENBQVRMLENBQUM7RUFHZixJQUFNTSxLQUFLLEdBQUdULENBQUMsR0FBRyxHQUFHO0VBQ3JCLElBQU1VLEtBQUssR0FBR1IsQ0FBQyxHQUFHLEdBQUc7RUFDckIsSUFBTVMsS0FBSyxHQUFHUixDQUFDLEdBQUcsR0FBRztFQUdyQixJQUFNUyxPQUFPLEdBQUdILEtBQUssSUFBSSxPQUFPLEdBQUdBLEtBQUssR0FBRyxLQUFLLEdBQUdJLElBQUksQ0FBQ0MsR0FBRyxDQUFDLENBQUNMLEtBQUssR0FBRyxLQUFLLElBQUksS0FBSyxFQUFFLEdBQUcsQ0FBQztFQUN6RixJQUFNTSxPQUFPLEdBQUdMLEtBQUssSUFBSSxPQUFPLEdBQUdBLEtBQUssR0FBRyxLQUFLLEdBQUdHLElBQUksQ0FBQ0MsR0FBRyxDQUFDLENBQUNKLEtBQUssR0FBRyxLQUFLLElBQUksS0FBSyxFQUFFLEdBQUcsQ0FBQztFQUN6RixJQUFNTSxPQUFPLEdBQUdMLEtBQUssSUFBSSxPQUFPLEdBQUdBLEtBQUssR0FBRyxLQUFLLEdBQUdFLElBQUksQ0FBQ0MsR0FBRyxDQUFDLENBQUNILEtBQUssR0FBRyxLQUFLLElBQUksS0FBSyxFQUFFLEdBQUcsQ0FBQztFQUd6RixPQUFPLE1BQU0sR0FBR0MsT0FBTyxHQUFHLE1BQU0sR0FBR0csT0FBTyxHQUFHLE1BQU0sR0FBR0MsT0FBTztBQUMvRCxDQUFDO0FBS00sSUFBTUMsZ0JBQWdCLEdBQUEzQixPQUFBLENBQUEyQixnQkFBQSxHQUFHLFNBQW5CQSxnQkFBZ0JBLENBQUlDLFVBQWtCLEVBQUVDLFVBQWtCLEVBQWE7RUFDbEYsSUFBTUMsRUFBRSxHQUFHYixvQkFBb0IsQ0FBQ1csVUFBVSxDQUFDO0VBQzNDLElBQU1HLEVBQUUsR0FBR2Qsb0JBQW9CLENBQUNZLFVBQVUsQ0FBQztFQUUzQyxJQUFNRyxPQUFPLEdBQUdULElBQUksQ0FBQ1UsR0FBRyxDQUFDSCxFQUFFLEVBQUVDLEVBQUUsQ0FBQztFQUNoQyxJQUFNRyxNQUFNLEdBQUdYLElBQUksQ0FBQ1ksR0FBRyxDQUFDTCxFQUFFLEVBQUVDLEVBQUUsQ0FBQztFQUUvQixPQUFPLENBQUNDLE9BQU8sR0FBRyxJQUFJLEtBQUtFLE1BQU0sR0FBRyxJQUFJLENBQUM7QUFDM0MsQ0FBQztBQUtNLElBQU1FLGdCQUFnQixHQUFBcEMsT0FBQSxDQUFBb0MsZ0JBQUEsR0FBRyxTQUFuQkEsZ0JBQWdCQSxDQUMzQlIsVUFBa0IsRUFDbEJDLFVBQWtCLEVBR1c7RUFBQSxJQUY3QlEsV0FBb0IsR0FBQUMsU0FBQSxDQUFBQyxNQUFBLFFBQUFELFNBQUEsUUFBQUUsU0FBQSxHQUFBRixTQUFBLE1BQUcsS0FBSztFQUFBLElBQzVCRyxXQUF5QixHQUFBSCxTQUFBLENBQUFDLE1BQUEsUUFBQUQsU0FBQSxRQUFBRSxTQUFBLEdBQUFGLFNBQUEsTUFBRyxJQUFJO0VBRWhDLElBQU1JLEtBQUssR0FBR2YsZ0JBQWdCLENBQUNDLFVBQVUsRUFBRUMsVUFBVSxDQUFDO0VBRXRELElBQU1jLGFBQWEsR0FBR0YsV0FBVyxLQUFLLEtBQUssR0FDdENKLFdBQVcsR0FBR3RDLGtCQUFrQixDQUFDSyxTQUFTLEdBQUdMLGtCQUFrQixDQUFDSSxVQUFVLEdBQzFFa0MsV0FBVyxHQUFHdEMsa0JBQWtCLENBQUNHLFFBQVEsR0FBR0gsa0JBQWtCLENBQUNFLFNBQVU7RUFFOUUsSUFBTTJDLE9BQU8sR0FBR0YsS0FBSyxJQUFJQyxhQUFhO0VBRXRDLElBQUlFLEtBQTRCLEdBQUcsTUFBTTtFQUN6QyxJQUFJSCxLQUFLLElBQUkzQyxrQkFBa0IsQ0FBQ0ksVUFBVSxJQUFLa0MsV0FBVyxJQUFJSyxLQUFLLElBQUkzQyxrQkFBa0IsQ0FBQ0ssU0FBVSxFQUFFO0lBQ3BHeUMsS0FBSyxHQUFHLEtBQUs7RUFDZixDQUFDLE1BQU0sSUFBSUgsS0FBSyxJQUFJM0Msa0JBQWtCLENBQUNFLFNBQVMsSUFBS29DLFdBQVcsSUFBSUssS0FBSyxJQUFJM0Msa0JBQWtCLENBQUNHLFFBQVMsRUFBRTtJQUN6RzJDLEtBQUssR0FBRyxJQUFJO0VBQ2Q7RUFFQSxJQUFJQyxjQUFrQztFQUN0QyxJQUFJLENBQUNGLE9BQU8sRUFBRTtJQUNaLElBQU1HLE9BQU8sR0FBR0osYUFBYSxHQUFHRCxLQUFLO0lBQ3JDSSxjQUFjLEdBQUcsa0JBQWtCSixLQUFLLENBQUNNLE9BQU8sQ0FBQyxDQUFDLENBQUMsZUFBZVAsV0FBVyxjQUFjRSxhQUFhLE9BQU8sR0FDN0csd0JBQXdCSSxPQUFPLENBQUNDLE9BQU8sQ0FBQyxDQUFDLENBQUMsc0NBQXNDO0VBQ3BGO0VBRUEsT0FBTztJQUNMTixLQUFLLEVBQUxBLEtBQUs7SUFDTEUsT0FBTyxFQUFQQSxPQUFPO0lBQ1BDLEtBQUssRUFBTEEsS0FBSztJQUNMQyxjQUFjLEVBQWRBO0VBQ0YsQ0FBQztBQUNILENBQUM7QUFLTSxJQUFNRyxXQUFXLEdBQUFqRCxPQUFBLENBQUFpRCxXQUFBLEdBQUcsU0FBZEEsV0FBV0EsQ0FBSTFDLEdBQVcsRUFBRTJDLFVBQWtCLEVBQWE7RUFDdEUsSUFBTWhDLEdBQUcsR0FBR1osUUFBUSxDQUFDQyxHQUFHLENBQUM7RUFDekIsSUFBSSxDQUFDVyxHQUFHLEVBQUUsT0FBT1gsR0FBRztFQUVwQixJQUFNNEMsTUFBTSxHQUFHLENBQUMsR0FBSUQsVUFBVSxHQUFHLEdBQUk7RUFDckMsT0FBT3BDLFFBQVEsQ0FDYlMsSUFBSSxDQUFDNkIsS0FBSyxDQUFDbEMsR0FBRyxDQUFDUixDQUFDLEdBQUd5QyxNQUFNLENBQUMsRUFDMUI1QixJQUFJLENBQUM2QixLQUFLLENBQUNsQyxHQUFHLENBQUNOLENBQUMsR0FBR3VDLE1BQU0sQ0FBQyxFQUMxQjVCLElBQUksQ0FBQzZCLEtBQUssQ0FBQ2xDLEdBQUcsQ0FBQ0wsQ0FBQyxHQUFHc0MsTUFBTSxDQUMzQixDQUFDO0FBQ0gsQ0FBQztBQUtNLElBQU1FLFlBQVksR0FBQXJELE9BQUEsQ0FBQXFELFlBQUEsR0FBRyxTQUFmQSxZQUFZQSxDQUFJOUMsR0FBVyxFQUFFMkMsVUFBa0IsRUFBYTtFQUN2RSxJQUFNaEMsR0FBRyxHQUFHWixRQUFRLENBQUNDLEdBQUcsQ0FBQztFQUN6QixJQUFJLENBQUNXLEdBQUcsRUFBRSxPQUFPWCxHQUFHO0VBRXBCLElBQU00QyxNQUFNLEdBQUdELFVBQVUsR0FBRyxHQUFHO0VBQy9CLE9BQU9wQyxRQUFRLENBQ2JTLElBQUksQ0FBQzZCLEtBQUssQ0FBQ2xDLEdBQUcsQ0FBQ1IsQ0FBQyxHQUFHLENBQUMsR0FBRyxHQUFHUSxHQUFHLENBQUNSLENBQUMsSUFBSXlDLE1BQU0sQ0FBQyxFQUMxQzVCLElBQUksQ0FBQzZCLEtBQUssQ0FBQ2xDLEdBQUcsQ0FBQ04sQ0FBQyxHQUFHLENBQUMsR0FBRyxHQUFHTSxHQUFHLENBQUNOLENBQUMsSUFBSXVDLE1BQU0sQ0FBQyxFQUMxQzVCLElBQUksQ0FBQzZCLEtBQUssQ0FBQ2xDLEdBQUcsQ0FBQ0wsQ0FBQyxHQUFHLENBQUMsR0FBRyxHQUFHSyxHQUFHLENBQUNMLENBQUMsSUFBSXNDLE1BQU0sQ0FDM0MsQ0FBQztBQUNILENBQUM7QUFLTSxJQUFNRyxXQUFXLEdBQUF0RCxPQUFBLENBQUFzRCxXQUFBLEdBQUcsU0FBZEEsV0FBV0EsQ0FDdEIxQixVQUFrQixFQUNsQkMsVUFBa0IsRUFHNEM7RUFBQSxJQUY5RDBCLFdBQW1CLEdBQUFqQixTQUFBLENBQUFDLE1BQUEsUUFBQUQsU0FBQSxRQUFBRSxTQUFBLEdBQUFGLFNBQUEsTUFBR3ZDLGtCQUFrQixDQUFDRSxTQUFTO0VBQUEsSUFDbER1RCxnQkFBeUIsR0FBQWxCLFNBQUEsQ0FBQUMsTUFBQSxRQUFBRCxTQUFBLFFBQUFFLFNBQUEsR0FBQUYsU0FBQSxNQUFHLEtBQUs7RUFFakMsSUFBSW1CLGlCQUFpQixHQUFHN0IsVUFBVTtFQUNsQyxJQUFJOEIsaUJBQWlCLEdBQUc3QixVQUFVO0VBQ2xDLElBQUk4QixZQUFZLEdBQUdoQyxnQkFBZ0IsQ0FBQzhCLGlCQUFpQixFQUFFQyxpQkFBaUIsQ0FBQztFQUd6RSxJQUFJQyxZQUFZLElBQUlKLFdBQVcsRUFBRTtJQUMvQixPQUFPO01BQUUzQixVQUFVLEVBQUU2QixpQkFBaUI7TUFBRTVCLFVBQVUsRUFBRTZCLGlCQUFpQjtNQUFFaEIsS0FBSyxFQUFFaUI7SUFBYSxDQUFDO0VBQzlGO0VBR0EsSUFBTUMsbUJBQW1CLEdBQUczQyxvQkFBb0IsQ0FBQ1csVUFBVSxDQUFDO0VBQzVELElBQU1pQyxtQkFBbUIsR0FBRzVDLG9CQUFvQixDQUFDWSxVQUFVLENBQUM7RUFFNUQsSUFBSTJCLGdCQUFnQixFQUFFO0lBRXBCLElBQUlJLG1CQUFtQixHQUFHQyxtQkFBbUIsRUFBRTtNQUU3QyxLQUFLLElBQUlDLENBQUMsR0FBRyxDQUFDLEVBQUVBLENBQUMsSUFBSSxFQUFFLEVBQUVBLENBQUMsSUFBSSxDQUFDLEVBQUU7UUFDL0IsSUFBTUMsY0FBYyxHQUFHZCxXQUFXLENBQUNwQixVQUFVLEVBQUVpQyxDQUFDLENBQUM7UUFDakQsSUFBTUUsU0FBUyxHQUFHckMsZ0JBQWdCLENBQUNDLFVBQVUsRUFBRW1DLGNBQWMsQ0FBQztRQUM5RCxJQUFJQyxTQUFTLElBQUlULFdBQVcsRUFBRTtVQUM1QixPQUFPO1lBQUUzQixVQUFVLEVBQVZBLFVBQVU7WUFBRUMsVUFBVSxFQUFFa0MsY0FBYztZQUFFckIsS0FBSyxFQUFFc0I7VUFBVSxDQUFDO1FBQ3JFO01BQ0Y7SUFDRixDQUFDLE1BQU07TUFFTCxLQUFLLElBQUlGLEVBQUMsR0FBRyxDQUFDLEVBQUVBLEVBQUMsSUFBSSxFQUFFLEVBQUVBLEVBQUMsSUFBSSxDQUFDLEVBQUU7UUFDL0IsSUFBTUMsZUFBYyxHQUFHVixZQUFZLENBQUN4QixVQUFVLEVBQUVpQyxFQUFDLENBQUM7UUFDbEQsSUFBTUUsVUFBUyxHQUFHckMsZ0JBQWdCLENBQUNDLFVBQVUsRUFBRW1DLGVBQWMsQ0FBQztRQUM5RCxJQUFJQyxVQUFTLElBQUlULFdBQVcsRUFBRTtVQUM1QixPQUFPO1lBQUUzQixVQUFVLEVBQVZBLFVBQVU7WUFBRUMsVUFBVSxFQUFFa0MsZUFBYztZQUFFckIsS0FBSyxFQUFFc0I7VUFBVSxDQUFDO1FBQ3JFO01BQ0Y7SUFDRjtFQUNGLENBQUMsTUFBTTtJQUVMLElBQUlKLG1CQUFtQixHQUFHQyxtQkFBbUIsRUFBRTtNQUU3QyxLQUFLLElBQUlDLEdBQUMsR0FBRyxDQUFDLEVBQUVBLEdBQUMsSUFBSSxFQUFFLEVBQUVBLEdBQUMsSUFBSSxDQUFDLEVBQUU7UUFDL0IsSUFBTUcsY0FBYyxHQUFHWixZQUFZLENBQUN6QixVQUFVLEVBQUVrQyxHQUFDLENBQUM7UUFDbEQsSUFBTUUsV0FBUyxHQUFHckMsZ0JBQWdCLENBQUNzQyxjQUFjLEVBQUVwQyxVQUFVLENBQUM7UUFDOUQsSUFBSW1DLFdBQVMsSUFBSVQsV0FBVyxFQUFFO1VBQzVCLE9BQU87WUFBRTNCLFVBQVUsRUFBRXFDLGNBQWM7WUFBRXBDLFVBQVUsRUFBVkEsVUFBVTtZQUFFYSxLQUFLLEVBQUVzQjtVQUFVLENBQUM7UUFDckU7TUFDRjtJQUNGLENBQUMsTUFBTTtNQUVMLEtBQUssSUFBSUYsR0FBQyxHQUFHLENBQUMsRUFBRUEsR0FBQyxJQUFJLEVBQUUsRUFBRUEsR0FBQyxJQUFJLENBQUMsRUFBRTtRQUMvQixJQUFNRyxlQUFjLEdBQUdoQixXQUFXLENBQUNyQixVQUFVLEVBQUVrQyxHQUFDLENBQUM7UUFDakQsSUFBTUUsV0FBUyxHQUFHckMsZ0JBQWdCLENBQUNzQyxlQUFjLEVBQUVwQyxVQUFVLENBQUM7UUFDOUQsSUFBSW1DLFdBQVMsSUFBSVQsV0FBVyxFQUFFO1VBQzVCLE9BQU87WUFBRTNCLFVBQVUsRUFBRXFDLGVBQWM7WUFBRXBDLFVBQVUsRUFBVkEsVUFBVTtZQUFFYSxLQUFLLEVBQUVzQjtVQUFVLENBQUM7UUFDckU7TUFDRjtJQUNGO0VBQ0Y7RUFHQSxJQUFJSixtQkFBbUIsR0FBR0MsbUJBQW1CLEVBQUU7SUFDN0MsT0FBTztNQUFFakMsVUFBVSxFQUFFLFNBQVM7TUFBRUMsVUFBVSxFQUFFb0IsV0FBVyxDQUFDcEIsVUFBVSxFQUFFLEVBQUUsQ0FBQztNQUFFYSxLQUFLLEVBQUVmLGdCQUFnQixDQUFDLFNBQVMsRUFBRXNCLFdBQVcsQ0FBQ3BCLFVBQVUsRUFBRSxFQUFFLENBQUM7SUFBRSxDQUFDO0VBQzVJLENBQUMsTUFBTTtJQUNMLE9BQU87TUFBRUQsVUFBVSxFQUFFcUIsV0FBVyxDQUFDckIsVUFBVSxFQUFFLEVBQUUsQ0FBQztNQUFFQyxVQUFVLEVBQUUsU0FBUztNQUFFYSxLQUFLLEVBQUVmLGdCQUFnQixDQUFDc0IsV0FBVyxDQUFDckIsVUFBVSxFQUFFLEVBQUUsQ0FBQyxFQUFFLFNBQVM7SUFBRSxDQUFDO0VBQzVJO0FBQ0YsQ0FBQztBQUtNLElBQU1zQyx5QkFBeUIsR0FBQWxFLE9BQUEsQ0FBQWtFLHlCQUFBLEdBQUcsU0FBNUJBLHlCQUF5QkEsQ0FBSUMsVUFBa0IsRUFBSztFQUMvRCxJQUFNQyxXQUFXLEdBQUc7SUFDbEJDLE9BQU8sRUFBRTtNQUNQeEMsVUFBVSxFQUFFc0MsVUFBVTtNQUN0QkcsSUFBSSxFQUFFO0lBQ1IsQ0FBQztJQUNEQyxTQUFTLEVBQUU7TUFDVDFDLFVBQVUsRUFBRSxhQUFhO01BQ3pCeUMsSUFBSSxFQUFFSCxVQUFVO01BQ2hCSyxNQUFNLEVBQUVMO0lBQ1YsQ0FBQztJQUNETSxRQUFRLEVBQUU7TUFDUjVDLFVBQVUsRUFBRSxTQUFTO01BQ3JCeUMsSUFBSSxFQUFFO0lBQ1I7RUFDRixDQUFDO0VBR0QsSUFBTUksaUJBQWlCLEdBQUd0QyxnQkFBZ0IsQ0FBQ2dDLFdBQVcsQ0FBQ0MsT0FBTyxDQUFDQyxJQUFJLEVBQUVGLFdBQVcsQ0FBQ0MsT0FBTyxDQUFDeEMsVUFBVSxDQUFDO0VBQ3BHLElBQUksQ0FBQzZDLGlCQUFpQixDQUFDOUIsT0FBTyxFQUFFO0lBQzlCLElBQU0rQixLQUFLLEdBQUdyQixXQUFXLENBQUNjLFdBQVcsQ0FBQ0MsT0FBTyxDQUFDQyxJQUFJLEVBQUVGLFdBQVcsQ0FBQ0MsT0FBTyxDQUFDeEMsVUFBVSxDQUFDO0lBQ25GdUMsV0FBVyxDQUFDQyxPQUFPLENBQUN4QyxVQUFVLEdBQUc4QyxLQUFLLENBQUM5QyxVQUFVO0lBQ2pEdUMsV0FBVyxDQUFDQyxPQUFPLENBQUNDLElBQUksR0FBR0ssS0FBSyxDQUFDL0MsVUFBVTtFQUM3QztFQUdBLElBQU1nRCxtQkFBbUIsR0FBR3hDLGdCQUFnQixDQUFDZ0MsV0FBVyxDQUFDRyxTQUFTLENBQUNELElBQUksRUFBRSxTQUFTLENBQUM7RUFDbkYsSUFBSSxDQUFDTSxtQkFBbUIsQ0FBQ2hDLE9BQU8sRUFBRTtJQUNoQyxJQUFNK0IsTUFBSyxHQUFHckIsV0FBVyxDQUFDYyxXQUFXLENBQUNHLFNBQVMsQ0FBQ0QsSUFBSSxFQUFFLFNBQVMsQ0FBQztJQUNoRUYsV0FBVyxDQUFDRyxTQUFTLENBQUNELElBQUksR0FBR0ssTUFBSyxDQUFDL0MsVUFBVTtJQUM3Q3dDLFdBQVcsQ0FBQ0csU0FBUyxDQUFDQyxNQUFNLEdBQUdHLE1BQUssQ0FBQy9DLFVBQVU7RUFDakQ7RUFFQSxPQUFPd0MsV0FBVztBQUNwQixDQUFDO0FBQUMsSUFBQVMsUUFBQSxHQUFBN0UsT0FBQSxDQUFBOEUsT0FBQSxHQUVhO0VBQ2IvRSxrQkFBa0IsRUFBbEJBLGtCQUFrQjtFQUNsQk8sUUFBUSxFQUFSQSxRQUFRO0VBQ1JRLFFBQVEsRUFBUkEsUUFBUTtFQUNSRyxvQkFBb0IsRUFBcEJBLG9CQUFvQjtFQUNwQlUsZ0JBQWdCLEVBQWhCQSxnQkFBZ0I7RUFDaEJTLGdCQUFnQixFQUFoQkEsZ0JBQWdCO0VBQ2hCYSxXQUFXLEVBQVhBLFdBQVc7RUFDWEksWUFBWSxFQUFaQSxZQUFZO0VBQ1pDLFdBQVcsRUFBWEEsV0FBVztFQUNYWSx5QkFBeUIsRUFBekJBO0FBQ0YsQ0FBQyIsImlnbm9yZUxpc3QiOltdfQ==