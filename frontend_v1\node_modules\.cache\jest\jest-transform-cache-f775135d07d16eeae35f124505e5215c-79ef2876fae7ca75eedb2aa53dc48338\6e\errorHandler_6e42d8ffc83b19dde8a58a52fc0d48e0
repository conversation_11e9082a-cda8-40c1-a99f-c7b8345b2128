0fea4d0dfb64a50cf868dfda66d567c9
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.handleValidationError = exports.handleNetworkError = exports.handleError = exports.handleCriticalError = exports.handleAuthError = exports.errorHandler = exports.ErrorType = exports.ErrorSeverity = void 0;
var _toConsumableArray2 = _interopRequireDefault(require("@babel/runtime/helpers/toConsumableArray"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _reactNative = require("react-native");
var Haptics = _interopRequireWildcard(require("expo-haptics"));
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var ErrorType = exports.ErrorType = function (ErrorType) {
  ErrorType["NETWORK"] = "NETWORK";
  ErrorType["VALIDATION"] = "VALIDATION";
  ErrorType["AUTHENTICATION"] = "AUTHENTICATION";
  ErrorType["AUTHORIZATION"] = "AUTHORIZATION";
  ErrorType["NOT_FOUND"] = "NOT_FOUND";
  ErrorType["SERVER"] = "SERVER";
  ErrorType["CLIENT"] = "CLIENT";
  ErrorType["UNKNOWN"] = "UNKNOWN";
  return ErrorType;
}({});
var ErrorSeverity = exports.ErrorSeverity = function (ErrorSeverity) {
  ErrorSeverity["LOW"] = "LOW";
  ErrorSeverity["MEDIUM"] = "MEDIUM";
  ErrorSeverity["HIGH"] = "HIGH";
  ErrorSeverity["CRITICAL"] = "CRITICAL";
  return ErrorSeverity;
}({});
var ErrorHandler = function () {
  function ErrorHandler() {
    var config = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    (0, _classCallCheck2.default)(this, ErrorHandler);
    this.errorQueue = [];
    this.maxQueueSize = 100;
    this.config = Object.assign({
      enableLogging: true,
      enableHaptics: true,
      enableUserNotification: true,
      logToConsole: __DEV__,
      logToRemote: !__DEV__
    }, config);
  }
  return (0, _createClass2.default)(ErrorHandler, [{
    key: "handleError",
    value: function handleError(error, context) {
      var appError = this.normalizeError(error, context);
      if (this.config.enableLogging) {
        this.logError(appError);
      }
      if (this.config.enableHaptics && appError.severity !== ErrorSeverity.LOW) {
        this.provideHapticFeedback(appError.severity);
      }
      if (this.config.enableUserNotification && appError.userMessage) {
        this.showUserNotification(appError);
      }
      this.addToQueue(appError);
      return appError;
    }
  }, {
    key: "handleNetworkError",
    value: function handleNetworkError(error, context) {
      var appError = {
        id: this.generateErrorId(),
        type: ErrorType.NETWORK,
        severity: ErrorSeverity.MEDIUM,
        message: error.message,
        userMessage: 'Network connection issue. Please check your internet connection and try again.',
        details: error,
        timestamp: new Date(),
        stack: error.stack,
        context: context
      };
      return this.handleError(appError, context);
    }
  }, {
    key: "handleValidationError",
    value: function handleValidationError(message, field, context) {
      var appError = {
        id: this.generateErrorId(),
        type: ErrorType.VALIDATION,
        severity: ErrorSeverity.LOW,
        message: message,
        userMessage: message,
        details: {
          field: field
        },
        timestamp: new Date(),
        context: context
      };
      return this.handleError(appError, context);
    }
  }, {
    key: "handleAuthError",
    value: function handleAuthError(error, context) {
      var appError = {
        id: this.generateErrorId(),
        type: ErrorType.AUTHENTICATION,
        severity: ErrorSeverity.HIGH,
        message: error.message,
        userMessage: 'Authentication failed. Please log in again.',
        details: error,
        timestamp: new Date(),
        stack: error.stack,
        context: context
      };
      return this.handleError(appError, context);
    }
  }, {
    key: "handleCriticalError",
    value: function handleCriticalError(error, context) {
      var appError = {
        id: this.generateErrorId(),
        type: ErrorType.UNKNOWN,
        severity: ErrorSeverity.CRITICAL,
        message: error.message,
        userMessage: 'A critical error occurred. The app will restart.',
        details: error,
        timestamp: new Date(),
        stack: error.stack,
        context: context
      };
      return this.handleError(appError, context);
    }
  }, {
    key: "normalizeError",
    value: function normalizeError(error, context) {
      if (this.isAppError(error)) {
        return Object.assign({}, error, {
          context: Object.assign({}, error.context, context)
        });
      }
      return {
        id: this.generateErrorId(),
        type: this.determineErrorType(error),
        severity: this.determineSeverity(error),
        message: error.message,
        userMessage: this.generateUserMessage(error),
        details: error,
        timestamp: new Date(),
        stack: error.stack,
        context: context
      };
    }
  }, {
    key: "logError",
    value: function logError(error) {
      if (this.config.logToConsole) {
        console.group(`🚨 Error [${error.severity}] - ${error.type}`);
        console.error('Message:', error.message);
        console.error('User Message:', error.userMessage);
        console.error('Details:', error.details);
        console.error('Context:', error.context);
        console.error('Stack:', error.stack);
        console.groupEnd();
      }
      if (this.config.logToRemote) {}
    }
  }, {
    key: "provideHapticFeedback",
    value: function provideHapticFeedback(severity) {
      try {
        switch (severity) {
          case ErrorSeverity.LOW:
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
            break;
          case ErrorSeverity.MEDIUM:
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
            break;
          case ErrorSeverity.HIGH:
          case ErrorSeverity.CRITICAL:
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
            setTimeout(function () {
              Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
            }, 100);
            break;
        }
      } catch (hapticError) {
        console.warn('Haptic feedback failed:', hapticError);
      }
    }
  }, {
    key: "showUserNotification",
    value: function showUserNotification(error) {
      var _this = this;
      var title = this.getErrorTitle(error.type);
      _reactNative.Alert.alert(title, error.userMessage || error.message, [{
        text: 'OK',
        style: 'default'
      }].concat((0, _toConsumableArray2.default)(error.severity === ErrorSeverity.CRITICAL ? [{
        text: 'Report Issue',
        style: 'default',
        onPress: function onPress() {
          return _this.reportIssue(error);
        }
      }] : [])));
    }
  }, {
    key: "addToQueue",
    value: function addToQueue(error) {
      this.errorQueue.push(error);
      if (this.errorQueue.length > this.maxQueueSize) {
        this.errorQueue.shift();
      }
    }
  }, {
    key: "getErrorTitle",
    value: function getErrorTitle(type) {
      switch (type) {
        case ErrorType.NETWORK:
          return 'Connection Issue';
        case ErrorType.VALIDATION:
          return 'Input Error';
        case ErrorType.AUTHENTICATION:
          return 'Authentication Required';
        case ErrorType.AUTHORIZATION:
          return 'Access Denied';
        case ErrorType.NOT_FOUND:
          return 'Not Found';
        case ErrorType.SERVER:
          return 'Server Error';
        default:
          return 'Error';
      }
    }
  }, {
    key: "determineErrorType",
    value: function determineErrorType(error) {
      var message = error.message.toLowerCase();
      if (message.includes('network') || message.includes('fetch')) {
        return ErrorType.NETWORK;
      }
      if (message.includes('unauthorized') || message.includes('401')) {
        return ErrorType.AUTHENTICATION;
      }
      if (message.includes('forbidden') || message.includes('403')) {
        return ErrorType.AUTHORIZATION;
      }
      if (message.includes('not found') || message.includes('404')) {
        return ErrorType.NOT_FOUND;
      }
      if (message.includes('server') || message.includes('500')) {
        return ErrorType.SERVER;
      }
      return ErrorType.UNKNOWN;
    }
  }, {
    key: "determineSeverity",
    value: function determineSeverity(error) {
      var message = error.message.toLowerCase();
      if (message.includes('critical') || message.includes('fatal')) {
        return ErrorSeverity.CRITICAL;
      }
      if (message.includes('unauthorized') || message.includes('forbidden')) {
        return ErrorSeverity.HIGH;
      }
      if (message.includes('network') || message.includes('server')) {
        return ErrorSeverity.MEDIUM;
      }
      return ErrorSeverity.LOW;
    }
  }, {
    key: "generateUserMessage",
    value: function generateUserMessage(error) {
      var type = this.determineErrorType(error);
      switch (type) {
        case ErrorType.NETWORK:
          return 'Please check your internet connection and try again.';
        case ErrorType.AUTHENTICATION:
          return 'Please log in to continue.';
        case ErrorType.AUTHORIZATION:
          return 'You don\'t have permission to perform this action.';
        case ErrorType.NOT_FOUND:
          return 'The requested item could not be found.';
        case ErrorType.SERVER:
          return 'Server is temporarily unavailable. Please try again later.';
        default:
          return 'An unexpected error occurred. Please try again.';
      }
    }
  }, {
    key: "generateErrorId",
    value: function generateErrorId() {
      return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
  }, {
    key: "isAppError",
    value: function isAppError(error) {
      return error && typeof error === 'object' && 'id' in error && 'type' in error;
    }
  }, {
    key: "reportIssue",
    value: function reportIssue(error) {
      console.log('Reporting issue:', error.id);
    }
  }, {
    key: "getErrorStats",
    value: function getErrorStats() {
      var stats = {
        total: this.errorQueue.length,
        byType: {},
        bySeverity: {}
      };
      this.errorQueue.forEach(function (error) {
        stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
        stats.bySeverity[error.severity] = (stats.bySeverity[error.severity] || 0) + 1;
      });
      return stats;
    }
  }, {
    key: "clearErrors",
    value: function clearErrors() {
      this.errorQueue = [];
    }
  }]);
}();
var errorHandler = exports.errorHandler = new ErrorHandler();
var handleError = exports.handleError = function handleError(error, context) {
  return errorHandler.handleError(error, context);
};
var handleNetworkError = exports.handleNetworkError = function handleNetworkError(error, context) {
  return errorHandler.handleNetworkError(error, context);
};
var handleValidationError = exports.handleValidationError = function handleValidationError(message, field, context) {
  return errorHandler.handleValidationError(message, field, context);
};
var handleAuthError = exports.handleAuthError = function handleAuthError(error, context) {
  return errorHandler.handleAuthError(error, context);
};
var handleCriticalError = exports.handleCriticalError = function handleCriticalError(error, context) {
  return errorHandler.handleCriticalError(error, context);
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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