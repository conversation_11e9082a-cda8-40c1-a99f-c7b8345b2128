a4682f4d4c9fd851e25e0a79d4e50409
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.validateColorContrast = exports.getThemeColor = exports.getThemeAwareColors = exports.getSageVariant = exports.getColorWithOpacity = exports.getAccessibleTextColor = exports.generateColorReport = exports.default = exports.SemanticColors = exports.SageColors = exports.GrayColors = exports.DarkModeColors = exports.DarkModeColors = exports.Colors = void 0;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
console.log('[Colors] Initializing Vierla color system...');
var Colors = exports.Colors = {
  sage50: '#F4F7F5',
  sage100: '#E1EDE4',
  sage200: '#C3DAC8',
  sage300: '#A5C7AC',
  sage400: '#5A7A63',
  sage500: '#4A6B52',
  sage600: '#3A5B42',
  sage700: '#2A4B32',
  sage800: '#1A3A22',
  sage900: '#0A2A12',
  primary: {
    sage: '#5A7A63',
    default: '#5A7A63',
    light: '#A5C7AC',
    dark: '#6B8A74',
    contrast: '#FFFFFF'
  },
  primaryLight: '#A5C7AC',
  primaryDark: '#6B8A74',
  primaryContrast: '#FFFFFF',
  roseGold: '#E8B4A0',
  warmCream: '#F7F3F0',
  softPink: '#F4E6E7',
  pearlWhite: '#FEFCFB',
  secondary: '#E8F5E8',
  secondaryLight: '#F4F9F4',
  secondaryDark: '#D1E7D1',
  secondaryContrast: '#374151',
  accent: '#E8B4A0',
  accentLight: '#F4E6E7',
  accentDark: '#D4A088',
  accentContrast: '#1F2937',
  white: '#FFFFFF',
  black: '#000000',
  gray: {
    50: '#F9FAFB',
    100: '#F3F4F6',
    200: '#E5E7EB',
    300: '#D1D5DB',
    400: '#9CA3AF',
    500: '#6B7280',
    600: '#4B5563',
    700: '#374151',
    800: '#1F2937',
    900: '#111827'
  },
  success: '#10B981',
  successLight: '#D1FAE5',
  successDark: '#047857',
  warning: '#F59E0B',
  warningLight: '#FEF3C7',
  warningDark: '#D97706',
  error: '#DC2626',
  errorLight: '#FEE2E2',
  errorDark: '#B91C1C',
  info: '#3B82F6',
  infoLight: '#DBEAFE',
  infoDark: '#1D4ED8',
  status: {
    success: '#10B981',
    warning: '#F59E0B',
    error: '#DC2626',
    info: '#3B82F6'
  },
  background: {
    primary: '#FFFFFF',
    secondary: '#F8F9FA',
    tertiary: '#F3F4F6',
    elevated: '#FFFFFF',
    overlay: 'rgba(0, 0, 0, 0.5)',
    sage: '#F4F7F5',
    disabled: '#F3F4F6'
  },
  surface: {
    primary: '#FFFFFF',
    secondary: '#F9FAFB',
    tertiary: '#F3F4F6',
    inverse: '#1F2937',
    disabled: '#F3F4F6',
    sage: '#E1EDE4'
  },
  text: {
    primary: '#1F2937',
    secondary: '#6B7280',
    tertiary: '#9CA3AF',
    inverse: '#FFFFFF',
    disabled: '#D1D5DB',
    onPrimary: '#FFFFFF',
    onSage: '#2A4B32',
    onLight: '#2A4B32',
    placeholder: '#9CA3AF'
  },
  border: {
    primary: '#E5E7EB',
    secondary: '#D1D5DB',
    focus: '#3B82F6',
    error: '#EF4444',
    sage: '#C3DAC8',
    light: '#E5E7EB',
    disabled: '#D1D5DB'
  },
  interactive: {
    primary: {
      default: '#4A6B52',
      hover: '#3A5B42',
      pressed: '#2A4B32',
      disabled: '#C3DAC8',
      text: '#FFFFFF',
      textDisabled: '#9CA3AF'
    },
    secondary: {
      default: '#FFFFFF',
      hover: '#F9FAFB',
      pressed: '#F3F4F6',
      disabled: '#F3F4F6',
      text: '#4A6B52',
      textDisabled: '#9CA3AF',
      border: '#4A6B52',
      borderDisabled: '#D1D5DB'
    },
    destructive: {
      default: '#DC2626',
      hover: '#B91C1C',
      pressed: '#991B1B',
      disabled: '#FCA5A5',
      text: '#FFFFFF',
      textDisabled: '#9CA3AF'
    },
    ghost: {
      default: 'transparent',
      hover: 'rgba(74, 107, 82, 0.1)',
      pressed: 'rgba(74, 107, 82, 0.2)',
      disabled: 'transparent',
      text: '#4A6B52',
      textDisabled: '#9CA3AF'
    }
  },
  focus: {
    ring: '#3B82F6',
    ringOpacity: 0.5,
    outline: '#1D4ED8',
    sage: '#7C9A85'
  },
  shadow: {
    light: 'rgba(0, 0, 0, 0.1)',
    medium: 'rgba(0, 0, 0, 0.15)',
    dark: 'rgba(0, 0, 0, 0.25)',
    sage: 'rgba(124, 154, 133, 0.15)'
  },
  overlay: {
    light: 'rgba(255, 255, 255, 0.9)',
    medium: 'rgba(0, 0, 0, 0.5)',
    dark: 'rgba(0, 0, 0, 0.8)',
    sage: 'rgba(124, 154, 133, 0.1)',
    warm: 'rgba(232, 180, 160, 0.1)'
  },
  gradient: {
    sage: 'linear-gradient(135deg, #7C9A85 0%, #A5C7AC 100%)',
    sageLight: 'linear-gradient(135deg, #E1EDE4 0%, #F4F7F5 100%)',
    sageDark: 'linear-gradient(135deg, #4A6B52 0%, #2A4B32 100%)',
    warm: 'linear-gradient(135deg, #E8B4A0 0%, #F4E6E7 100%)'
  }
};
var SageColors = exports.SageColors = {
  50: '#F4F7F5',
  100: '#E1EDE4',
  200: '#C3DAC8',
  300: '#A5C7AC',
  400: '#7C9A85',
  500: '#6B8A74',
  600: '#5A7A63',
  700: '#4A6B52',
  800: '#3A5B42',
  900: '#2A4B32'
};
var getColorWithOpacity = exports.getColorWithOpacity = function getColorWithOpacity(color, opacity) {
  var hex = color.replace('#', '');
  var r = parseInt(hex.substring(0, 2), 16);
  var g = parseInt(hex.substring(2, 4), 16);
  var b = parseInt(hex.substring(4, 6), 16);
  return `rgba(${r}, ${g}, ${b}, ${opacity})`;
};
var getSageVariant = exports.getSageVariant = function getSageVariant(variant) {
  return SageColors[variant];
};
var GrayColors = exports.GrayColors = {
  50: '#F9FAFB',
  100: '#F3F4F6',
  200: '#E5E7EB',
  300: '#D1D5DB',
  400: '#9CA3AF',
  500: '#6B7280',
  600: '#4B5563',
  700: '#374151',
  800: '#1F2937',
  900: '#111827'
};
var getThemeColor = exports.getThemeColor = function getThemeColor(lightColor, darkColor) {
  var isDark = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
  return isDark ? darkColor : lightColor;
};
var validateColorContrast = exports.validateColorContrast = function validateColorContrast(foreground, background) {
  var hexToRgb = function hexToRgb(hex) {
    var result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : {
      r: 0,
      g: 0,
      b: 0
    };
  };
  var getLuminance = function getLuminance(r, g, b) {
    var _map = [r, g, b].map(function (c) {
        c = c / 255;
        return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
      }),
      _map2 = (0, _slicedToArray2.default)(_map, 3),
      rs = _map2[0],
      gs = _map2[1],
      bs = _map2[2];
    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
  };
  var fg = hexToRgb(foreground);
  var bg = hexToRgb(background);
  var fgLuminance = getLuminance(fg.r, fg.g, fg.b);
  var bgLuminance = getLuminance(bg.r, bg.g, bg.b);
  var ratio = (Math.max(fgLuminance, bgLuminance) + 0.05) / (Math.min(fgLuminance, bgLuminance) + 0.05);
  var isAACompliant = ratio >= 4.5;
  var isAAACompliant = ratio >= 7;
  var recommendation = '';
  if (!isAACompliant) {
    recommendation = 'Increase contrast - does not meet WCAG AA standards';
  } else if (!isAAACompliant) {
    recommendation = 'Meets WCAG AA - consider enhancing for AAA compliance';
  } else {
    recommendation = 'Excellent contrast - meets WCAG AAA standards';
  }
  return {
    ratio: Math.round(ratio * 100) / 100,
    isAACompliant: isAACompliant,
    isAAACompliant: isAAACompliant,
    recommendation: recommendation
  };
};
var DarkModeColors = exports.DarkModeColors = exports.DarkModeColors = {
  sage50: '#0A140D',
  sage100: '#152A1A',
  sage200: '#1F3A26',
  sage300: '#2A4B32',
  sage400: '#4A6B52',
  sage500: '#5A7A63',
  sage600: '#6B8A74',
  sage700: '#7C9A85',
  sage800: '#A5C7AC',
  sage900: '#C3DAC8',
  gray: {
    50: '#F9FAFB',
    100: '#F3F4F6',
    200: '#E5E7EB',
    300: '#D1D5DB',
    400: '#9CA3AF',
    500: '#6B7280',
    600: '#4B5563',
    700: '#374151',
    800: '#1F2937',
    900: '#111827'
  },
  background: {
    primary: '#111827',
    secondary: '#1F2937',
    tertiary: '#374151',
    elevated: '#4B5563',
    overlay: 'rgba(0, 0, 0, 0.8)',
    sage: '#0A140D'
  },
  text: {
    primary: '#F9FAFB',
    secondary: '#D1D5DB',
    tertiary: '#9CA3AF',
    inverse: '#1F2937',
    disabled: '#6B7280',
    onPrimary: '#FFFFFF',
    onSage: '#F4F7F5'
  }
};
var getThemeAwareColors = exports.getThemeAwareColors = function getThemeAwareColors() {
  var isDarkMode = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;
  return isDarkMode ? DarkModeColors : Colors;
};
var getAccessibleTextColor = exports.getAccessibleTextColor = function getAccessibleTextColor(backgroundColor) {
  var whiteContrast = validateColorContrast('#FFFFFF', backgroundColor);
  var blackContrast = validateColorContrast('#000000', backgroundColor);
  return whiteContrast.ratio > blackContrast.ratio ? '#FFFFFF' : '#000000';
};
var SemanticColors = exports.SemanticColors = {
  primary: '#5A7A63',
  primaryHover: '#4A6B52',
  primaryPressed: '#3A5B42',
  primaryDisabled: '#C3DAC8',
  secondary: '#F3F4F6',
  secondaryHover: '#E5E7EB',
  secondaryPressed: '#D1D5DB',
  success: '#10B981',
  warning: '#F59E0B',
  error: '#EF4444',
  info: '#3B82F6',
  textPrimary: '#1F2937',
  textSecondary: '#6B7280',
  textTertiary: '#9CA3AF',
  textDisabled: '#D1D5DB',
  backgroundPrimary: '#FFFFFF',
  backgroundSecondary: '#F8F9FA',
  backgroundSage: '#F4F7F5',
  borderPrimary: '#E5E7EB',
  borderSecondary: '#D1D5DB',
  borderFocus: '#3B82F6'
};
var generateColorReport = exports.generateColorReport = function generateColorReport() {
  var testPairs = [{
    fg: '#1F2937',
    bg: '#FFFFFF',
    name: 'Primary text on white'
  }, {
    fg: '#6B7280',
    bg: '#FFFFFF',
    name: 'Secondary text on white'
  }, {
    fg: '#FFFFFF',
    bg: '#2A4B32',
    name: 'White text on dark sage primary (WCAG AA compliant)'
  }, {
    fg: '#2A4B32',
    bg: '#2A4B32',
    name: 'Text on dark sage'
  }];
  var results = testPairs.map(function (pair) {
    return Object.assign({}, pair, {
      validation: validateColorContrast(pair.fg, pair.bg)
    });
  });
  var compliantPairs = results.filter(function (r) {
    return r.validation.isAACompliant;
  }).length;
  var recommendations = results.filter(function (r) {
    return !r.validation.isAACompliant;
  }).map(function (r) {
    return `${r.name}: ${r.validation.recommendation}`;
  });
  return {
    totalColors: testPairs.length,
    wcagCompliantPairs: compliantPairs,
    recommendations: recommendations
  };
};
try {
  if (!Colors || typeof Colors !== 'object') {
    throw new Error('Colors object is not properly defined');
  }
  if (!Colors.primary || typeof Colors.primary !== 'object') {
    throw new Error('Colors.primary is not properly defined');
  }
  if (!Colors.text || typeof Colors.text !== 'object') {
    throw new Error('Colors.text is not properly defined');
  }
  if (!Colors.background || typeof Colors.background !== 'object') {
    throw new Error('Colors.background is not properly defined');
  }
  console.log('[Colors] ✅ Color system validation passed');
} catch (error) {
  console.error('[Colors] ❌ Color system validation failed:', error);
  throw error;
}
var _default = exports.default = Colors;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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