2c12aef58b2b12cff14128b5f3938dc1
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.dispatchCommand = dispatchCommand;
exports.findHostInstance_DEPRECATED = findHostInstance_DEPRECATED;
exports.findNodeHandle = findNodeHandle;
exports.getNodeFromInternalInstanceHandle = getNodeFromInternalInstanceHandle;
exports.getPublicInstanceFromInternalInstanceHandle = getPublicInstanceFromInternalInstanceHandle;
exports.getPublicInstanceFromRootTag = getPublicInstanceFromRootTag;
exports.isChildPublicInstance = isChildPublicInstance;
exports.isProfilingRenderer = isProfilingRenderer;
exports.renderElement = renderElement;
exports.sendAccessibilityEvent = sendAccessibilityEvent;
exports.unmountComponentAtNodeAndRemoveContainer = unmountComponentAtNodeAndRemoveContainer;
exports.unstable_batchedUpdates = unstable_batchedUpdates;
var _ErrorHandlers = require("../../src/private/renderer/errorhandling/ErrorHandlers");
function renderElement(_ref) {
  var element = _ref.element,
    rootTag = _ref.rootTag,
    useFabric = _ref.useFabric,
    useConcurrentRoot = _ref.useConcurrentRoot;
  if (useFabric) {
    require("../Renderer/shims/ReactFabric").default.render(element, rootTag, null, useConcurrentRoot, {
      onCaughtError: _ErrorHandlers.onCaughtError,
      onUncaughtError: _ErrorHandlers.onUncaughtError,
      onRecoverableError: _ErrorHandlers.onRecoverableError
    });
  } else {
    require("../Renderer/shims/ReactNative").default.render(element, rootTag, undefined, {
      onCaughtError: _ErrorHandlers.onCaughtError,
      onUncaughtError: _ErrorHandlers.onUncaughtError,
      onRecoverableError: _ErrorHandlers.onRecoverableError
    });
  }
}
function findHostInstance_DEPRECATED(componentOrHandle) {
  return require("../Renderer/shims/ReactNative").default.findHostInstance_DEPRECATED(componentOrHandle);
}
function findNodeHandle(componentOrHandle) {
  return require("../Renderer/shims/ReactNative").default.findNodeHandle(componentOrHandle);
}
function dispatchCommand(handle, command, args) {
  if (global.RN$Bridgeless === true) {
    return require("../Renderer/shims/ReactFabric").default.dispatchCommand(handle, command, args);
  } else {
    return require("../Renderer/shims/ReactNative").default.dispatchCommand(handle, command, args);
  }
}
function sendAccessibilityEvent(handle, eventType) {
  return require("../Renderer/shims/ReactNative").default.sendAccessibilityEvent(handle, eventType);
}
function unmountComponentAtNodeAndRemoveContainer(rootTag) {
  var rootTagAsNumber = rootTag;
  require("../Renderer/shims/ReactNative").default.unmountComponentAtNodeAndRemoveContainer(rootTagAsNumber);
}
function unstable_batchedUpdates(fn, bookkeeping) {
  return require("../Renderer/shims/ReactNative").default.unstable_batchedUpdates(fn, bookkeeping);
}
function isProfilingRenderer() {
  return Boolean(__DEV__);
}
function isChildPublicInstance(parentInstance, childInstance) {
  return require("../Renderer/shims/ReactNative").default.isChildPublicInstance(parentInstance, childInstance);
}
function getNodeFromInternalInstanceHandle(internalInstanceHandle) {
  return require("../Renderer/shims/ReactFabric").default.getNodeFromInternalInstanceHandle(internalInstanceHandle);
}
function getPublicInstanceFromInternalInstanceHandle(internalInstanceHandle) {
  return require("../Renderer/shims/ReactFabric").default.getPublicInstanceFromInternalInstanceHandle(internalInstanceHandle);
}
function getPublicInstanceFromRootTag(rootTag) {
  return require("../Renderer/shims/ReactFabric").default.getPublicInstanceFromRootTag(rootTag);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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