/**
 * User Feedback System - Comprehensive User Feedback Management
 *
 * Component Contract:
 * - Provides toast notifications, alerts, and feedback messages
 * - Manages loading states and progress indicators
 * - Implements success, error, and warning feedback
 * - Supports custom feedback actions and recovery options
 * - Provides accessibility support for feedback messages
 * - Follows responsive design and theme guidelines
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useEffect, useRef } from 'react';
import { View, Text, TouchableOpacity, Animated, Dimensions } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { useTheme } from '../../contexts/ThemeContext';
import { getResponsiveSpacing, getResponsiveFontSize } from '../../utils/responsiveUtils';

export interface FeedbackMessage {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title?: string;
  message: string;
  duration?: number;
  persistent?: boolean;
  actions?: FeedbackAction[];
  onDismiss?: () => void;
}

export interface FeedbackAction {
  label: string;
  onPress: () => void;
  style?: 'primary' | 'secondary' | 'destructive';
}

interface UserFeedbackSystemProps {
  messages: FeedbackMessage[];
  onDismiss: (id: string) => void;
  position?: 'top' | 'bottom' | 'center';
}

export const UserFeedbackSystem: React.FC<UserFeedbackSystemProps> = ({
  messages,
  onDismiss,
  position = 'top',
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  const [visibleMessages, setVisibleMessages] = useState<FeedbackMessage[]>([]);
  const animatedValues = useRef<Map<string, Animated.Value>>(new Map()).current;

  useEffect(() => {
    // Add new messages
    messages.forEach(message => {
      if (!visibleMessages.find(m => m.id === message.id)) {
        addMessage(message);
      }
    });

    // Remove dismissed messages
    visibleMessages.forEach(message => {
      if (!messages.find(m => m.id === message.id)) {
        removeMessage(message.id);
      }
    });
  }, [messages]);

  const addMessage = (message: FeedbackMessage) => {
    setVisibleMessages(prev => [...prev, message]);
    
    // Create animation value
    const animValue = new Animated.Value(0);
    animatedValues.set(message.id, animValue);

    // Animate in
    Animated.spring(animValue, {
      toValue: 1,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start();

    // Auto dismiss if not persistent
    if (!message.persistent && message.duration !== 0) {
      const duration = message.duration || getDurationByType(message.type);
      setTimeout(() => {
        dismissMessage(message.id);
      }, duration);
    }
  };

  const removeMessage = (messageId: string) => {
    setVisibleMessages(prev => prev.filter(m => m.id !== messageId));
    animatedValues.delete(messageId);
  };

  const dismissMessage = (messageId: string) => {
    const animValue = animatedValues.get(messageId);
    if (animValue) {
      Animated.timing(animValue, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start(() => {
        onDismiss(messageId);
        const message = visibleMessages.find(m => m.id === messageId);
        if (message?.onDismiss) {
          message.onDismiss();
        }
      });
    } else {
      onDismiss(messageId);
    }
  };

  const getDurationByType = (type: FeedbackMessage['type']): number => {
    switch (type) {
      case 'success':
        return 3000;
      case 'info':
        return 4000;
      case 'warning':
        return 5000;
      case 'error':
        return 6000;
      default:
        return 4000;
    }
  };

  const getIconName = (type: FeedbackMessage['type']): string => {
    switch (type) {
      case 'success':
        return 'checkmark-circle';
      case 'error':
        return 'close-circle';
      case 'warning':
        return 'warning';
      case 'info':
        return 'information-circle';
      default:
        return 'information-circle';
    }
  };

  const getTypeStyles = (type: FeedbackMessage['type']) => {
    switch (type) {
      case 'success':
        return {
          backgroundColor: colors.success + '15',
          borderColor: colors.success,
          iconColor: colors.success,
        };
      case 'error':
        return {
          backgroundColor: colors.error + '15',
          borderColor: colors.error,
          iconColor: colors.error,
        };
      case 'warning':
        return {
          backgroundColor: colors.warning + '15',
          borderColor: colors.warning,
          iconColor: colors.warning,
        };
      case 'info':
        return {
          backgroundColor: colors.sage100,
          borderColor: colors.sage400,
          iconColor: colors.sage500,
        };
      default:
        return {
          backgroundColor: colors.background.secondary,
          borderColor: colors.border.primary,
          iconColor: colors.text.secondary,
        };
    }
  };

  const renderMessage = (message: FeedbackMessage) => {
    const animValue = animatedValues.get(message.id);
    if (!animValue) return null;

    const typeStyles = getTypeStyles(message.type);
    const iconName = getIconName(message.type);

    const translateY = animValue.interpolate({
      inputRange: [0, 1],
      outputRange: position === 'top' ? [-100, 0] : [100, 0],
    });

    return (
      <Animated.View
        key={message.id}
        style={[
          styles.messageContainer,
          {
            backgroundColor: typeStyles.backgroundColor,
            borderColor: typeStyles.borderColor,
            transform: [
              { translateY },
              { scale: animValue },
            ],
            opacity: animValue,
          },
        ]}>
        
        <View style={styles.messageContent}>
          <View style={styles.messageHeader}>
            <Ionicons
              name={iconName as any}
              size={24}
              color={typeStyles.iconColor}
              style={styles.messageIcon}
            />
            
            <View style={styles.messageText}>
              {message.title && (
                <Text style={[styles.messageTitle, { color: typeStyles.iconColor }]}>
                  {message.title}
                </Text>
              )}
              <Text style={styles.messageBody}>{message.message}</Text>
            </View>

            {!message.persistent && (
              <TouchableOpacity
                onPress={() => dismissMessage(message.id)}
                style={styles.dismissButton}
                accessibilityLabel="Dismiss message"
                accessibilityRole="button">
                <Ionicons
                  name="close"
                  size={20}
                  color={colors.text.tertiary}
                />
              </TouchableOpacity>
            )}
          </View>

          {message.actions && message.actions.length > 0 && (
            <View style={styles.actionsContainer}>
              {message.actions.map((action, index) => (
                <TouchableOpacity
                  key={index}
                  onPress={() => {
                    action.onPress();
                    if (!message.persistent) {
                      dismissMessage(message.id);
                    }
                  }}
                  style={[
                    styles.actionButton,
                    action.style === 'primary' && styles.primaryActionButton,
                    action.style === 'destructive' && styles.destructiveActionButton,
                  ]}
                  accessibilityLabel={action.label}
                  accessibilityRole="button">
                  <Text
                    style={[
                      styles.actionButtonText,
                      action.style === 'primary' && styles.primaryActionButtonText,
                      action.style === 'destructive' && styles.destructiveActionButtonText,
                    ]}>
                    {action.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>
      </Animated.View>
    );
  };

  if (visibleMessages.length === 0) {
    return null;
  }

  return (
    <View style={[styles.container, styles[`${position}Container`]]}>
      {visibleMessages.map(renderMessage)}
    </View>
  );
};

// Feedback Manager Hook
export const useFeedback = () => {
  const [messages, setMessages] = useState<FeedbackMessage[]>([]);

  const showMessage = (message: Omit<FeedbackMessage, 'id'>) => {
    const id = `feedback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const fullMessage: FeedbackMessage = { ...message, id };
    
    setMessages(prev => [...prev, fullMessage]);
    return id;
  };

  const dismissMessage = (id: string) => {
    setMessages(prev => prev.filter(m => m.id !== id));
  };

  const clearAll = () => {
    setMessages([]);
  };

  // Convenience methods
  const showSuccess = (message: string, options?: Partial<FeedbackMessage>) =>
    showMessage({ ...options, type: 'success', message });

  const showError = (message: string, options?: Partial<FeedbackMessage>) =>
    showMessage({ ...options, type: 'error', message });

  const showWarning = (message: string, options?: Partial<FeedbackMessage>) =>
    showMessage({ ...options, type: 'warning', message });

  const showInfo = (message: string, options?: Partial<FeedbackMessage>) =>
    showMessage({ ...options, type: 'info', message });

  return {
    messages,
    showMessage,
    dismissMessage,
    clearAll,
    showSuccess,
    showError,
    showWarning,
    showInfo,
  };
};

const createStyles = (colors: any) => ({
  container: {
    position: 'absolute' as const,
    left: getResponsiveSpacing(16),
    right: getResponsiveSpacing(16),
    zIndex: 9999,
  },
  topContainer: {
    top: getResponsiveSpacing(60),
  },
  bottomContainer: {
    bottom: getResponsiveSpacing(100),
  },
  centerContainer: {
    top: '50%',
    transform: [{ translateY: -50 }],
  },
  messageContainer: {
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: getResponsiveSpacing(8),
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  messageContent: {
    padding: getResponsiveSpacing(16),
  },
  messageHeader: {
    flexDirection: 'row' as const,
    alignItems: 'flex-start' as const,
  },
  messageIcon: {
    marginRight: getResponsiveSpacing(12),
    marginTop: getResponsiveSpacing(2),
  },
  messageText: {
    flex: 1,
  },
  messageTitle: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '600',
    marginBottom: getResponsiveSpacing(4),
  },
  messageBody: {
    fontSize: getResponsiveFontSize(14),
    color: colors.text.primary,
    lineHeight: 20,
  },
  dismissButton: {
    padding: getResponsiveSpacing(4),
    marginLeft: getResponsiveSpacing(8),
  },
  actionsContainer: {
    flexDirection: 'row' as const,
    marginTop: getResponsiveSpacing(12),
    paddingTop: getResponsiveSpacing(12),
    borderTopWidth: 1,
    borderTopColor: colors.border.secondary,
  },
  actionButton: {
    paddingHorizontal: getResponsiveSpacing(16),
    paddingVertical: getResponsiveSpacing(8),
    borderRadius: 6,
    marginRight: getResponsiveSpacing(8),
    backgroundColor: colors.background.secondary,
  },
  primaryActionButton: {
    backgroundColor: colors.sage500,
  },
  destructiveActionButton: {
    backgroundColor: colors.error,
  },
  actionButtonText: {
    fontSize: getResponsiveFontSize(14),
    fontWeight: '500',
    color: colors.text.primary,
  },
  primaryActionButtonText: {
    color: colors.background.primary,
  },
  destructiveActionButtonText: {
    color: colors.background.primary,
  },
});
