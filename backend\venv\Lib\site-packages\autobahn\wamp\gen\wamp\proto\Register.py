# automatically generated by the FlatBuffers compiler, do not modify

# namespace: proto

import flatbuffers
from flatbuffers.compat import import_numpy
np = import_numpy()

class Register(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = Register()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsRegister(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    # Register
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # Register
    def Session(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint64Flags, o + self._tab.Pos)
        return 0

    # Register
    def Request(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint64Flags, o + self._tab.Pos)
        return 0

    # Register
    def Procedure(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(8))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # Register
    def Match(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(10))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint8Flags, o + self._tab.Pos)
        return 0

    # Register
    def Invoke(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(12))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint8Flags, o + self._tab.Pos)
        return 0

    # Register
    def Concurrency(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(14))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint16Flags, o + self._tab.Pos)
        return 0

    # Register
    def ForceReregister(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(16))
        if o != 0:
            return bool(self._tab.Get(flatbuffers.number_types.BoolFlags, o + self._tab.Pos))
        return False

def RegisterStart(builder): builder.StartObject(7)
def Start(builder):
    return RegisterStart(builder)
def RegisterAddSession(builder, session): builder.PrependUint64Slot(0, session, 0)
def AddSession(builder, session):
    return RegisterAddSession(builder, session)
def RegisterAddRequest(builder, request): builder.PrependUint64Slot(1, request, 0)
def AddRequest(builder, request):
    return RegisterAddRequest(builder, request)
def RegisterAddProcedure(builder, procedure): builder.PrependUOffsetTRelativeSlot(2, flatbuffers.number_types.UOffsetTFlags.py_type(procedure), 0)
def AddProcedure(builder, procedure):
    return RegisterAddProcedure(builder, procedure)
def RegisterAddMatch(builder, match): builder.PrependUint8Slot(3, match, 0)
def AddMatch(builder, match):
    return RegisterAddMatch(builder, match)
def RegisterAddInvoke(builder, invoke): builder.PrependUint8Slot(4, invoke, 0)
def AddInvoke(builder, invoke):
    return RegisterAddInvoke(builder, invoke)
def RegisterAddConcurrency(builder, concurrency): builder.PrependUint16Slot(5, concurrency, 0)
def AddConcurrency(builder, concurrency):
    return RegisterAddConcurrency(builder, concurrency)
def RegisterAddForceReregister(builder, forceReregister): builder.PrependBoolSlot(6, forceReregister, 0)
def AddForceReregister(builder, forceReregister):
    return RegisterAddForceReregister(builder, forceReregister)
def RegisterEnd(builder): return builder.EndObject()
def End(builder):
    return RegisterEnd(builder)