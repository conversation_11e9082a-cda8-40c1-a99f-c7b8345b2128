363a7f571daa7e389492534fb602d50d
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "QUICK_LOGIN_ACCOUNTS", {
  enumerable: true,
  get: function get() {
    return _testAccounts.QUICK_LOGIN_ACCOUNTS;
  }
});
Object.defineProperty(exports, "TEST_ACCOUNTS_SUMMARY", {
  enumerable: true,
  get: function get() {
    return _testAccounts.TEST_ACCOUNTS_SUMMARY;
  }
});
Object.defineProperty(exports, "TestAccount", {
  enumerable: true,
  get: function get() {
    return _testAccounts.TestAccount;
  }
});
exports.testAccountsService = void 0;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _asyncStorage = _interopRequireDefault(require("@react-native-async-storage/async-storage"));
var _testAccounts = require("../config/testAccounts");
var _authService = require("./authService");
var STORAGE_KEYS = {
  LAST_TEST_ACCOUNT: '@vierla/last_test_account',
  TEST_MODE_ENABLED: '@vierla/test_mode_enabled',
  PREFERRED_TEST_ACCOUNTS: '@vierla/preferred_test_accounts'
};
var TestAccountsService = function () {
  function TestAccountsService() {
    (0, _classCallCheck2.default)(this, TestAccountsService);
    this.isTestModeEnabled = __DEV__;
  }
  return (0, _createClass2.default)(TestAccountsService, [{
    key: "isTestModeActive",
    value: function () {
      var _isTestModeActive = (0, _asyncToGenerator2.default)(function* () {
        if (!__DEV__) return false;
        try {
          var stored = yield _asyncStorage.default.getItem(STORAGE_KEYS.TEST_MODE_ENABLED);
          return stored === 'true';
        } catch (_unused) {
          return this.isTestModeEnabled;
        }
      });
      function isTestModeActive() {
        return _isTestModeActive.apply(this, arguments);
      }
      return isTestModeActive;
    }()
  }, {
    key: "setTestMode",
    value: (function () {
      var _setTestMode = (0, _asyncToGenerator2.default)(function* (enabled) {
        if (!__DEV__) return;
        this.isTestModeEnabled = enabled;
        yield _asyncStorage.default.setItem(STORAGE_KEYS.TEST_MODE_ENABLED, enabled.toString());
      });
      function setTestMode(_x) {
        return _setTestMode.apply(this, arguments);
      }
      return setTestMode;
    }())
  }, {
    key: "getAllTestAccounts",
    value: function getAllTestAccounts() {
      return _testAccounts.ALL_TEST_ACCOUNTS;
    }
  }, {
    key: "getAccountsByRole",
    value: function getAccountsByRole(role) {
      return (0, _testAccounts.getTestAccountsByRole)(role);
    }
  }, {
    key: "getProvidersByCategory",
    value: function getProvidersByCategory(category) {
      return (0, _testAccounts.getTestAccountsByCategory)(category);
    }
  }, {
    key: "getRandomAccount",
    value: function getRandomAccount(role) {
      return (0, _testAccounts.getRandomTestAccount)(role);
    }
  }, {
    key: "findAccountByEmail",
    value: function findAccountByEmail(email) {
      return (0, _testAccounts.findTestAccountByEmail)(email);
    }
  }, {
    key: "getQuickLoginAccounts",
    value: function getQuickLoginAccounts() {
      return _testAccounts.QUICK_LOGIN_ACCOUNTS;
    }
  }, {
    key: "getAccountsStats",
    value: function getAccountsStats() {
      return {
        totalAccounts: _testAccounts.TEST_ACCOUNTS_SUMMARY.total,
        customerAccounts: _testAccounts.TEST_ACCOUNTS_SUMMARY.customers,
        providerAccounts: _testAccounts.TEST_ACCOUNTS_SUMMARY.providers,
        categoriesBreakdown: _testAccounts.TEST_ACCOUNTS_SUMMARY.categories,
        citiesBreakdown: _testAccounts.TEST_ACCOUNTS_SUMMARY.cities
      };
    }
  }, {
    key: "loginWithTestAccount",
    value: (function () {
      var _loginWithTestAccount = (0, _asyncToGenerator2.default)(function* (account) {
        if (!__DEV__ || !this.isTestModeEnabled) {
          return {
            success: false,
            error: 'Test mode is not enabled'
          };
        }
        try {
          var loginRequest = {
            email: account.email,
            password: account.password
          };
          var authResponse = yield _authService.authService.login(loginRequest);
          yield this.storeLastTestAccount(account);
          return {
            success: true,
            account: account,
            authResponse: authResponse
          };
        } catch (error) {
          return {
            success: false,
            account: account,
            error: error instanceof Error ? error.message : 'Login failed'
          };
        }
      });
      function loginWithTestAccount(_x2) {
        return _loginWithTestAccount.apply(this, arguments);
      }
      return loginWithTestAccount;
    }())
  }, {
    key: "quickLogin",
    value: (function () {
      var _quickLogin = (0, _asyncToGenerator2.default)(function* (accountType) {
        var account = _testAccounts.QUICK_LOGIN_ACCOUNTS[accountType];
        return this.loginWithTestAccount(account);
      });
      function quickLogin(_x3) {
        return _quickLogin.apply(this, arguments);
      }
      return quickLogin;
    }())
  }, {
    key: "loginWithRandomAccount",
    value: (function () {
      var _loginWithRandomAccount = (0, _asyncToGenerator2.default)(function* (role) {
        var account = this.getRandomAccount(role);
        return this.loginWithTestAccount(account);
      });
      function loginWithRandomAccount(_x4) {
        return _loginWithRandomAccount.apply(this, arguments);
      }
      return loginWithRandomAccount;
    }())
  }, {
    key: "storeLastTestAccount",
    value: (function () {
      var _storeLastTestAccount = (0, _asyncToGenerator2.default)(function* (account) {
        try {
          yield _asyncStorage.default.setItem(STORAGE_KEYS.LAST_TEST_ACCOUNT, JSON.stringify(account));
        } catch (error) {
          console.warn('Failed to store last test account:', error);
        }
      });
      function storeLastTestAccount(_x5) {
        return _storeLastTestAccount.apply(this, arguments);
      }
      return storeLastTestAccount;
    }())
  }, {
    key: "getLastTestAccount",
    value: (function () {
      var _getLastTestAccount = (0, _asyncToGenerator2.default)(function* () {
        try {
          var stored = yield _asyncStorage.default.getItem(STORAGE_KEYS.LAST_TEST_ACCOUNT);
          return stored ? JSON.parse(stored) : null;
        } catch (_unused2) {
          return null;
        }
      });
      function getLastTestAccount() {
        return _getLastTestAccount.apply(this, arguments);
      }
      return getLastTestAccount;
    }())
  }, {
    key: "clearTestAccountData",
    value: (function () {
      var _clearTestAccountData = (0, _asyncToGenerator2.default)(function* () {
        try {
          yield _asyncStorage.default.multiRemove([STORAGE_KEYS.LAST_TEST_ACCOUNT, STORAGE_KEYS.PREFERRED_TEST_ACCOUNTS]);
        } catch (error) {
          console.warn('Failed to clear test account data:', error);
        }
      });
      function clearTestAccountData() {
        return _clearTestAccountData.apply(this, arguments);
      }
      return clearTestAccountData;
    }())
  }, {
    key: "validateTestAccount",
    value: function validateTestAccount(email, password) {
      var account = this.findAccountByEmail(email);
      if (account && account.password === password) {
        return account;
      }
      return null;
    }
  }, {
    key: "getAccountsForScenario",
    value: function getAccountsForScenario(scenario) {
      var customer = _testAccounts.CUSTOMER_TEST_ACCOUNTS[0];
      var provider;
      switch (scenario) {
        case 'booking':
          provider = _testAccounts.QUICK_LOGIN_ACCOUNTS.BARBER_PROVIDER;
          break;
        case 'messaging':
          provider = _testAccounts.QUICK_LOGIN_ACCOUNTS.NAIL_PROVIDER;
          break;
        case 'payments':
          provider = _testAccounts.QUICK_LOGIN_ACCOUNTS.LASH_PROVIDER;
          break;
        case 'reviews':
          provider = _testAccounts.QUICK_LOGIN_ACCOUNTS.MASSAGE_PROVIDER;
          break;
        default:
          provider = _testAccounts.QUICK_LOGIN_ACCOUNTS.BARBER_PROVIDER;
      }
      return {
        customer: customer,
        provider: provider
      };
    }
  }, {
    key: "getTestAccountCredentials",
    value: function getTestAccountCredentials() {
      return [{
        label: 'Test Customer',
        email: _testAccounts.QUICK_LOGIN_ACCOUNTS.CUSTOMER.email,
        password: _testAccounts.QUICK_LOGIN_ACCOUNTS.CUSTOMER.password,
        role: 'Customer'
      }, {
        label: 'Barber Provider',
        email: _testAccounts.QUICK_LOGIN_ACCOUNTS.BARBER_PROVIDER.email,
        password: _testAccounts.QUICK_LOGIN_ACCOUNTS.BARBER_PROVIDER.password,
        role: 'Service Provider',
        category: 'Barber'
      }, {
        label: 'Nail Services Provider',
        email: _testAccounts.QUICK_LOGIN_ACCOUNTS.NAIL_PROVIDER.email,
        password: _testAccounts.QUICK_LOGIN_ACCOUNTS.NAIL_PROVIDER.password,
        role: 'Service Provider',
        category: 'Nail Services'
      }, {
        label: 'Lash Services Provider',
        email: _testAccounts.QUICK_LOGIN_ACCOUNTS.LASH_PROVIDER.email,
        password: _testAccounts.QUICK_LOGIN_ACCOUNTS.LASH_PROVIDER.password,
        role: 'Service Provider',
        category: 'Lash Services'
      }];
    }
  }, {
    key: "logTestAccountsSummary",
    value: function logTestAccountsSummary() {
      if (!__DEV__) return;
      console.log('🧪 Vierla Test Accounts Summary');
      console.log('================================');
      console.log(`Total Accounts: ${_testAccounts.TEST_ACCOUNTS_SUMMARY.total}`);
      console.log(`Customers: ${_testAccounts.TEST_ACCOUNTS_SUMMARY.customers}`);
      console.log(`Providers: ${_testAccounts.TEST_ACCOUNTS_SUMMARY.providers}`);
      console.log('\nCategories:');
      Object.entries(_testAccounts.TEST_ACCOUNTS_SUMMARY.categories).forEach(function (_ref) {
        var _ref2 = (0, _slicedToArray2.default)(_ref, 2),
          category = _ref2[0],
          count = _ref2[1];
        console.log(`  ${category}: ${count} providers`);
      });
      console.log('\nCities:');
      Object.entries(_testAccounts.TEST_ACCOUNTS_SUMMARY.cities).forEach(function (_ref3) {
        var _ref4 = (0, _slicedToArray2.default)(_ref3, 2),
          city = _ref4[0],
          count = _ref4[1];
        console.log(`  ${city}: ${count} accounts`);
      });
      console.log('\nQuick Login Accounts:');
      console.log(`  Customer: ${_testAccounts.QUICK_LOGIN_ACCOUNTS.CUSTOMER.email}`);
      console.log(`  Barber Provider: ${_testAccounts.QUICK_LOGIN_ACCOUNTS.BARBER_PROVIDER.email}`);
      console.log(`  Nail Provider: ${_testAccounts.QUICK_LOGIN_ACCOUNTS.NAIL_PROVIDER.email}`);
      console.log('================================');
    }
  }]);
}();
var testAccountsService = exports.testAccountsService = new TestAccountsService();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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