3cdfa17305984049dc28026b300793ff
_getJestObj().mock("../../hooks/useCustomerHomeData");
_getJestObj().mock("../../store/authSlice");
_getJestObj().mock("../../hooks/useNavigationGuard");
_getJestObj().mock("../../services/performanceMonitor");
_getJestObj().mock("../../hooks/usePerformance");
_getJestObj().mock("../../hooks/useErrorHandling");
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _react = _interopRequireDefault(require("react"));
var _reactNative = require("@testing-library/react-native");
var _native = require("@react-navigation/native");
var _CustomerHomeScreen = require("../CustomerHomeScreen");
var _ThemeContext = require("../../contexts/ThemeContext");
var _useCustomerHomeData = require("../../hooks/useCustomerHomeData");
var _authSlice = require("../../store/authSlice");
var _useNavigationGuard = require("../../hooks/useNavigationGuard");
var _performanceMonitor = require("../../services/performanceMonitor");
var _jsxRuntime = require("react/jsx-runtime");
function _getJestObj() {
  var _require = require("@jest/globals"),
    jest = _require.jest;
  _getJestObj = function _getJestObj() {
    return jest;
  };
  return jest;
}
var mockUseCustomerHomeData = _useCustomerHomeData.useCustomerHomeData;
var mockUseAuthStore = _authSlice.useAuthStore;
var mockUseNavigationGuard = _useNavigationGuard.useNavigationGuard;
var TestWrapper = function TestWrapper(_ref) {
  var children = _ref.children;
  return (0, _jsxRuntime.jsx)(_native.NavigationContainer, {
    children: (0, _jsxRuntime.jsx)(_ThemeContext.ThemeProvider, {
      children: children
    })
  });
};
var mockCategories = [{
  id: 1,
  name: 'Hair',
  slug: 'hair',
  icon: 'cut',
  color: '#FF6B6B'
}, {
  id: 2,
  name: 'Nails',
  slug: 'nails',
  icon: 'hand',
  color: '#4ECDC4'
}, {
  id: 3,
  name: 'Skincare',
  slug: 'skincare',
  icon: 'face',
  color: '#45B7D1'
}];
var mockFeaturedProviders = [{
  id: 1,
  name: 'Beauty Studio',
  rating: 4.8,
  reviewCount: 150,
  imageUrl: 'https://example.com/provider1.jpg',
  services: ['Hair', 'Makeup'],
  distance: 2.5
}, {
  id: 2,
  name: 'Spa Wellness',
  rating: 4.6,
  reviewCount: 89,
  imageUrl: 'https://example.com/provider2.jpg',
  services: ['Skincare', 'Massage'],
  distance: 1.8
}];
var mockUser = {
  id: 1,
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User',
  role: 'customer'
};
describe('CustomerHomeScreen', function () {
  var mockNavigate = jest.fn();
  var mockRefresh = jest.fn();
  beforeEach(function () {
    jest.clearAllMocks();
    mockUseAuthStore.mockReturnValue({
      isAuthenticated: true,
      user: mockUser,
      login: jest.fn(),
      logout: jest.fn(),
      register: jest.fn()
    });
    mockUseNavigationGuard.mockReturnValue({
      navigate: mockNavigate,
      canNavigate: jest.fn(function () {
        return true;
      }),
      guardedNavigate: mockNavigate
    });
    mockUseCustomerHomeData.mockReturnValue({
      data: {
        categories: mockCategories,
        featuredProviders: mockFeaturedProviders,
        nearbyProviders: [],
        favoriteProviders: [],
        recentBookings: [],
        dashboard: null
      },
      loading: {
        categories: false,
        featuredProviders: false,
        nearbyProviders: false,
        favoriteProviders: false,
        recentBookings: false,
        dashboard: false
      },
      error: {
        categories: null,
        featuredProviders: null,
        nearbyProviders: null,
        favoriteProviders: null,
        recentBookings: null,
        dashboard: null
      },
      refreshing: false,
      refresh: mockRefresh
    });
  });
  describe('Rendering', function () {
    it('renders the home screen correctly', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      expect(_reactNative.screen.getByTestId('customer-home-screen')).toBeTruthy();
      expect(_reactNative.screen.getByText('Browse Services')).toBeTruthy();
      expect(_reactNative.screen.getByText('Featured Providers')).toBeTruthy();
    }));
    it('displays user greeting when authenticated', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText(/Hello, Test/)).toBeTruthy();
      });
    }));
    it('renders categories correctly', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('Hair')).toBeTruthy();
        expect(_reactNative.screen.getByText('Nails')).toBeTruthy();
        expect(_reactNative.screen.getByText('Skincare')).toBeTruthy();
      });
    }));
    it('renders featured providers correctly', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('Beauty Studio')).toBeTruthy();
        expect(_reactNative.screen.getByText('Spa Wellness')).toBeTruthy();
        expect(_reactNative.screen.getByText('4.8')).toBeTruthy();
        expect(_reactNative.screen.getByText('4.6')).toBeTruthy();
      });
    }));
  });
  describe('Loading States', function () {
    it('shows loading indicators when data is loading', (0, _asyncToGenerator2.default)(function* () {
      mockUseCustomerHomeData.mockReturnValue({
        data: {
          categories: [],
          featuredProviders: [],
          nearbyProviders: [],
          favoriteProviders: [],
          recentBookings: [],
          dashboard: null
        },
        loading: {
          categories: true,
          featuredProviders: true,
          nearbyProviders: false,
          favoriteProviders: false,
          recentBookings: false,
          dashboard: false
        },
        error: {
          categories: null,
          featuredProviders: null,
          nearbyProviders: null,
          favoriteProviders: null,
          recentBookings: null,
          dashboard: null
        },
        refreshing: false,
        refresh: mockRefresh
      });
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      expect(_reactNative.screen.getByTestId('categories-loading')).toBeTruthy();
      expect(_reactNative.screen.getByTestId('featured-providers-loading')).toBeTruthy();
    }));
    it('shows refreshing state correctly', (0, _asyncToGenerator2.default)(function* () {
      mockUseCustomerHomeData.mockReturnValue({
        data: {
          categories: mockCategories,
          featuredProviders: mockFeaturedProviders,
          nearbyProviders: [],
          favoriteProviders: [],
          recentBookings: [],
          dashboard: null
        },
        loading: {
          categories: false,
          featuredProviders: false,
          nearbyProviders: false,
          favoriteProviders: false,
          recentBookings: false,
          dashboard: false
        },
        error: {
          categories: null,
          featuredProviders: null,
          nearbyProviders: null,
          favoriteProviders: null,
          recentBookings: null,
          dashboard: null
        },
        refreshing: true,
        refresh: mockRefresh
      });
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      var scrollView = _reactNative.screen.getByTestId('home-scroll-view');
      expect(scrollView.props.refreshControl.props.refreshing).toBe(true);
    }));
  });
  describe('Error States', function () {
    it('handles category loading errors gracefully', (0, _asyncToGenerator2.default)(function* () {
      mockUseCustomerHomeData.mockReturnValue({
        data: {
          categories: [],
          featuredProviders: mockFeaturedProviders,
          nearbyProviders: [],
          favoriteProviders: [],
          recentBookings: [],
          dashboard: null
        },
        loading: {
          categories: false,
          featuredProviders: false,
          nearbyProviders: false,
          favoriteProviders: false,
          recentBookings: false,
          dashboard: false
        },
        error: {
          categories: new Error('Failed to load categories'),
          featuredProviders: null,
          nearbyProviders: null,
          favoriteProviders: null,
          recentBookings: null,
          dashboard: null
        },
        refreshing: false,
        refresh: mockRefresh
      });
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByTestId('categories-error')).toBeTruthy();
      });
    }));
    it('handles provider loading errors gracefully', (0, _asyncToGenerator2.default)(function* () {
      mockUseCustomerHomeData.mockReturnValue({
        data: {
          categories: mockCategories,
          featuredProviders: [],
          nearbyProviders: [],
          favoriteProviders: [],
          recentBookings: [],
          dashboard: null
        },
        loading: {
          categories: false,
          featuredProviders: false,
          nearbyProviders: false,
          favoriteProviders: false,
          recentBookings: false,
          dashboard: false
        },
        error: {
          categories: null,
          featuredProviders: new Error('Failed to load providers'),
          nearbyProviders: null,
          favoriteProviders: null,
          recentBookings: null,
          dashboard: null
        },
        refreshing: false,
        refresh: mockRefresh
      });
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByTestId('featured-providers-error')).toBeTruthy();
      });
    }));
  });
  describe('User Interactions', function () {
    it('handles category press correctly', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        var hairCategory = _reactNative.screen.getByText('Hair');
        _reactNative.fireEvent.press(hairCategory);
      });
      expect(mockNavigate).toHaveBeenCalledWith('Search', {
        category: 'hair'
      });
    }));
    it('handles provider press correctly', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        var provider = _reactNative.screen.getByText('Beauty Studio');
        _reactNative.fireEvent.press(provider);
      });
      expect(mockNavigate).toHaveBeenCalledWith('ProviderDetails', {
        providerId: 1
      });
    }));
    it('handles pull to refresh correctly', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      var scrollView = _reactNative.screen.getByTestId('home-scroll-view');
      yield (0, _reactNative.act)((0, _asyncToGenerator2.default)(function* () {
        (0, _reactNative.fireEvent)(scrollView, 'refresh');
      }));
      expect(mockRefresh).toHaveBeenCalled();
    }));
    it('handles see all buttons correctly', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        var seeAllButton = _reactNative.screen.getByTestId('featured-providers-see-all');
        _reactNative.fireEvent.press(seeAllButton);
      });
      expect(mockNavigate).toHaveBeenCalledWith('Search', {
        filter: 'featured'
      });
    }));
  });
  describe('Performance', function () {
    it('tracks component render performance', (0, _asyncToGenerator2.default)(function* () {
      var performanceSpy = jest.spyOn(_performanceMonitor.performanceMonitor, 'trackRender');
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        expect(performanceSpy).toHaveBeenCalledWith('CustomerHomeScreen', expect.any(Number), expect.any(Object));
      });
    }));
    it('tracks user interaction performance', (0, _asyncToGenerator2.default)(function* () {
      var interactionSpy = jest.spyOn(_performanceMonitor.performanceMonitor, 'trackUserInteraction');
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        var hairCategory = _reactNative.screen.getByText('Hair');
        _reactNative.fireEvent.press(hairCategory);
      });
      expect(interactionSpy).toHaveBeenCalledWith('category_press', expect.any(Number), expect.any(Object));
    }));
    it('renders within performance threshold', (0, _asyncToGenerator2.default)(function* () {
      var renderTime = yield measureAsyncPerformance('CustomerHomeScreen render', (0, _asyncToGenerator2.default)(function* () {
        (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
        }));
        yield (0, _reactNative.waitFor)(function () {
          expect(_reactNative.screen.getByTestId('customer-home-screen')).toBeTruthy();
        });
      }));
      expect(renderTime).toBeLessThan(1000);
    }));
  });
  describe('Accessibility', function () {
    it('has proper accessibility labels', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      expect(_reactNative.screen.getByLabelText('Customer Home Screen')).toBeTruthy();
      expect(_reactNative.screen.getByLabelText('Main content area')).toBeTruthy();
      expect(_reactNative.screen.getByLabelText('Pull to refresh')).toBeTruthy();
    }));
    it('has proper accessibility roles', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      var scrollView = _reactNative.screen.getByTestId('home-scroll-view');
      expect(scrollView.props.accessibilityRole).toBe('scrollbar');
    }));
    it('supports screen reader navigation', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      yield (0, _reactNative.waitFor)(function () {
        var categories = _reactNative.screen.getAllByRole('button');
        expect(categories.length).toBeGreaterThan(0);
        categories.forEach(function (category) {
          expect(category.props.accessibilityLabel).toBeTruthy();
        });
      });
    }));
  });
  describe('Integration', function () {
    it('integrates with auth store correctly', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      expect(mockUseAuthStore).toHaveBeenCalled();
    }));
    it('integrates with navigation guard correctly', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      expect(mockUseNavigationGuard).toHaveBeenCalled();
    }));
    it('integrates with customer home data hook correctly', (0, _asyncToGenerator2.default)(function* () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
        children: (0, _jsxRuntime.jsx)(_CustomerHomeScreen.CustomerHomeScreen, {})
      }));
      expect(mockUseCustomerHomeData).toHaveBeenCalled();
    }));
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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