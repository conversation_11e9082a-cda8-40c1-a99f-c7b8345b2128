{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "<PERSON><PERSON><PERSON><PERSON>", "warnOnce", "key", "message", "console", "warn", "_default"], "sources": ["warnOnce.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict\n */\n\n'use strict';\n\nconst warnedKeys: {[string]: boolean, ...} = {};\n\n/**\n * A simple function that prints a warning message once per session.\n *\n * @param {string} key - The key used to ensure the message is printed once.\n *                       This should be unique to the callsite.\n * @param {string} message - The message to print\n */\nfunction warnOnce(key: string, message: string) {\n  if (warnedKeys[key]) {\n    return;\n  }\n\n  console.warn(message);\n\n  warnedKeys[key] = true;\n}\n\nexport default warnOnce;\n"], "mappings": "AAUA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAEb,IAAMC,UAAoC,GAAG,CAAC,CAAC;AAS/C,SAASC,QAAQA,CAACC,GAAW,EAAEC,OAAe,EAAE;EAC9C,IAAIH,UAAU,CAACE,GAAG,CAAC,EAAE;IACnB;EACF;EAEAE,OAAO,CAACC,IAAI,CAACF,OAAO,CAAC;EAErBH,UAAU,CAACE,GAAG,CAAC,GAAG,IAAI;AACxB;AAAC,IAAAI,QAAA,GAAAT,OAAA,CAAAE,OAAA,GAEcE,QAAQ", "ignoreList": []}