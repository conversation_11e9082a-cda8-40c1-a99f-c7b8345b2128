{"version": 3, "names": ["_useRefEffect", "_interopRequireDefault", "require", "_react", "_interopRequireWildcard", "React", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "useMergeRefs", "_len", "arguments", "length", "refs", "Array", "_key", "refEffect", "useCallback", "current", "cleanups", "map", "ref", "undefined", "cleanup", "concat", "useRefEffect"], "sources": ["useMergeRefs.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict\n * @format\n */\n\nimport useRefEffect from './useRefEffect';\nimport * as React from 'react';\nimport {useCallback} from 'react';\n\n/**\n * Constructs a new ref that forwards new values to each of the given refs. The\n * given refs will always be invoked in the order that they are supplied.\n *\n * WARNING: A known problem of merging refs using this approach is that if any\n * of the given refs change, the returned callback ref will also be changed. If\n * the returned callback ref is supplied as a `ref` to a React element, this may\n * lead to problems with the given refs being invoked more times than desired.\n */\nexport default function useMergeRefs<Instance>(\n  ...refs: $ReadOnlyArray<?React.RefSetter<Instance>>\n): React.RefSetter<Instance> {\n  const refEffect = useCallback(\n    (current: Instance) => {\n      const cleanups: $ReadOnlyArray<void | (() => void)> = refs.map(ref => {\n        if (ref == null) {\n          return undefined;\n        } else {\n          if (typeof ref === 'function') {\n            // $FlowIssue[incompatible-type] - Flow does not understand ref cleanup.\n            const cleanup: void | (() => void) = ref(current);\n            return typeof cleanup === 'function'\n              ? cleanup\n              : () => {\n                  ref(null);\n                };\n          } else {\n            ref.current = current;\n            return () => {\n              ref.current = null;\n            };\n          }\n        }\n      });\n\n      return () => {\n        for (const cleanup of cleanups) {\n          cleanup?.();\n        }\n      };\n    },\n    [...refs], // eslint-disable-line react-hooks/exhaustive-deps\n  );\n  return useRefEffect(refEffect);\n}\n"], "mappings": ";;;;;AAUA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAC,uBAAA,CAAAF,OAAA;AAA+B,IAAAG,KAAA,GAAAF,MAAA;AAAA,SAAAC,wBAAAE,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAJ,uBAAA,YAAAA,wBAAAE,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAYhB,SAASmB,YAAYA,CAAA,EAEP;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EADxBC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAAJF,IAAI,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;EAAA;EAEP,IAAMC,SAAS,GAAG,IAAAC,kBAAW,EAC3B,UAACC,OAAiB,EAAK;IACrB,IAAMC,QAA6C,GAAGN,IAAI,CAACO,GAAG,CAAC,UAAAC,GAAG,EAAI;MACpE,IAAIA,GAAG,IAAI,IAAI,EAAE;QACf,OAAOC,SAAS;MAClB,CAAC,MAAM;QACL,IAAI,OAAOD,GAAG,KAAK,UAAU,EAAE;UAE7B,IAAME,OAA4B,GAAGF,GAAG,CAACH,OAAO,CAAC;UACjD,OAAO,OAAOK,OAAO,KAAK,UAAU,GAChCA,OAAO,GACP,YAAM;YACJF,GAAG,CAAC,IAAI,CAAC;UACX,CAAC;QACP,CAAC,MAAM;UACLA,GAAG,CAACH,OAAO,GAAGA,OAAO;UACrB,OAAO,YAAM;YACXG,GAAG,CAACH,OAAO,GAAG,IAAI;UACpB,CAAC;QACH;MACF;IACF,CAAC,CAAC;IAEF,OAAO,YAAM;MACX,KAAK,IAAMK,OAAO,IAAIJ,QAAQ,EAAE;QAC9BI,OAAO,YAAPA,OAAO,CAAG,CAAC;MACb;IACF,CAAC;EACH,CAAC,KAAAC,MAAA,CACGX,IAAI,CACV,CAAC;EACD,OAAO,IAAAY,qBAAY,EAACT,SAAS,CAAC;AAChC", "ignoreList": []}