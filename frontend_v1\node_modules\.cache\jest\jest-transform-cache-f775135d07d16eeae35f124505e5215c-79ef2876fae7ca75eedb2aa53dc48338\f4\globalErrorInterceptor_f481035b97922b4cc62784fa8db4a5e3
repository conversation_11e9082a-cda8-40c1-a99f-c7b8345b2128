a8590b4d5c75b0885f2cbd9b8c3afcef
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.resetErrorTracking = exports.installGlobalErrorInterceptor = exports.hasRecentThemeErrors = exports.getFallbackTheme = exports.getErrorStats = void 0;
var errorCount = 0;
var lastErrorTime = 0;
var MAX_ERRORS_PER_MINUTE = 10;
var FALLBACK_THEME = {
  colors: {
    primary: {
      default: '#4A6B52',
      light: '#6B8A74',
      dark: '#2A4B32',
      contrast: '#FFFFFF'
    },
    text: {
      primary: '#1A1A1A',
      secondary: '#6B7280',
      tertiary: '#9CA3AF',
      inverse: '#FFFFFF',
      disabled: '#9CA3AF',
      onPrimary: '#FFFFFF',
      onSecondary: '#FFFFFF',
      link: '#4A6B52',
      linkHover: '#2A4B32'
    },
    background: {
      primary: '#FFFFFF',
      secondary: '#F9FAFB',
      tertiary: '#F3F4F6',
      elevated: '#FFFFFF',
      overlay: 'rgba(0, 0, 0, 0.5)',
      sage: '#F4F7F5'
    },
    surface: {
      primary: '#FFFFFF',
      secondary: '#F9FAFB',
      tertiary: '#F3F4F6',
      inverse: '#1A1A1A',
      disabled: '#F3F4F6'
    },
    border: {
      light: '#E5E7EB',
      medium: '#D1D5DB',
      dark: '#9CA3AF',
      focus: '#4A6B52',
      error: '#EF4444',
      success: '#10B981'
    }
  },
  isDark: false
};
var THEME_ERROR_PATTERNS = [/Cannot read property 'primary' of undefined/, /Cannot read property 'text' of undefined/, /Cannot read property 'background' of undefined/, /Cannot read property 'surface' of undefined/, /Cannot read property 'colors' of undefined/, /Cannot read properties of undefined \(reading 'primary'\)/, /Cannot read properties of undefined \(reading 'text'\)/, /Cannot read properties of undefined \(reading 'background'\)/, /Cannot read properties of undefined \(reading 'surface'\)/, /Cannot read properties of undefined \(reading 'colors'\)/];
var isThemeError = function isThemeError(error) {
  if (!error || !error.message) return false;
  return THEME_ERROR_PATTERNS.some(function (pattern) {
    return pattern.test(error.message);
  });
};
var shouldHandleError = function shouldHandleError() {
  var now = Date.now();
  if (now - lastErrorTime > 60000) {
    errorCount = 0;
  }
  lastErrorTime = now;
  errorCount++;
  return errorCount <= MAX_ERRORS_PER_MINUTE;
};
var globalErrorHandler = function globalErrorHandler(error) {
  var isFatal = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
  if (!isThemeError(error)) {
    return;
  }
  if (!shouldHandleError()) {
    console.warn('[GlobalErrorInterceptor] Too many theme errors, throttling...');
    return;
  }
  console.error('[GlobalErrorInterceptor] 🛡️ Theme error intercepted:', error.message);
  console.error('[GlobalErrorInterceptor] Stack trace:', error.stack);
  try {
    if (typeof global !== 'undefined') {
      global.__VIERLA_FALLBACK_THEME__ = FALLBACK_THEME;
      console.log('[GlobalErrorInterceptor] ✅ Fallback theme stored in global');
    }
    console.log('[GlobalErrorInterceptor] 🔄 Attempting graceful recovery...');
    if (!isFatal) {
      console.log('[GlobalErrorInterceptor] ✅ Non-fatal theme error handled gracefully');
      return;
    }
    console.warn('[GlobalErrorInterceptor] ⚠️ Fatal theme error detected, attempting recovery...');
  } catch (recoveryError) {
    console.error('[GlobalErrorInterceptor] ❌ Recovery attempt failed:', recoveryError);
  }
};
var installGlobalErrorInterceptor = exports.installGlobalErrorInterceptor = function installGlobalErrorInterceptor() {
  console.log('[GlobalErrorInterceptor] Installing global error interceptor...');
  try {
    var _global$ErrorUtils;
    if (global && global.ErrorUtils) {
      var originalReportError = global.ErrorUtils.reportError;
      var originalReportFatalError = global.ErrorUtils.reportFatalError;
      global.ErrorUtils.reportError = function (error) {
        globalErrorHandler(error, false);
        if (originalReportError && !isThemeError(error)) {
          originalReportError.call(this, error);
        }
      };
      global.ErrorUtils.reportFatalError = function (error) {
        globalErrorHandler(error, true);
        if (originalReportFatalError && !isThemeError(error)) {
          originalReportFatalError.call(this, error);
        }
      };
      console.log('[GlobalErrorInterceptor] ✅ Installed on ErrorUtils');
    }
    if (global && typeof ((_global$ErrorUtils = global.ErrorUtils) == null ? void 0 : _global$ErrorUtils.setGlobalHandler) === 'function') {
      var originalHandler = (global.ErrorUtils.getGlobalHandler == null ? void 0 : global.ErrorUtils.getGlobalHandler()) || null;
      global.ErrorUtils.setGlobalHandler(function (error, isFatal) {
        globalErrorHandler(error, isFatal || false);
        if (originalHandler && !isThemeError(error)) {
          originalHandler(error, isFatal);
        }
      });
      console.log('[GlobalErrorInterceptor] ✅ Installed global handler');
    }
    if (typeof window !== 'undefined') {
      var originalOnError = window.onerror;
      window.onerror = function (message, source, lineno, colno, error) {
        if (error && isThemeError(error)) {
          globalErrorHandler(error, false);
          return true;
        }
        if (originalOnError) {
          return originalOnError.call(this, message, source, lineno, colno, error);
        }
        return false;
      };
      console.log('[GlobalErrorInterceptor] ✅ Installed on window.onerror');
    }
    console.log('[GlobalErrorInterceptor] ✅ Global error interceptor installed successfully');
  } catch (error) {
    console.error('[GlobalErrorInterceptor] ❌ Failed to install global error interceptor:', error);
  }
};
var getFallbackTheme = exports.getFallbackTheme = function getFallbackTheme() {
  return FALLBACK_THEME;
};
var hasRecentThemeErrors = exports.hasRecentThemeErrors = function hasRecentThemeErrors() {
  return errorCount > 0 && Date.now() - lastErrorTime < 60000;
};
var resetErrorTracking = exports.resetErrorTracking = function resetErrorTracking() {
  errorCount = 0;
  lastErrorTime = 0;
  console.log('[GlobalErrorInterceptor] Error tracking reset');
};
var getErrorStats = exports.getErrorStats = function getErrorStats() {
  return {
    errorCount: errorCount,
    lastErrorTime: lastErrorTime,
    hasRecentErrors: hasRecentThemeErrors()
  };
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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