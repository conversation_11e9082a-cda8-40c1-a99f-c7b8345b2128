<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="465" failures="75" errors="0" time="28.364">
  <testsuite name="Test Setup" errors="0" failures="15" skipped="0" timestamp="2025-07-21T07:45:47" time="4.902" tests="16">
    <testcase classname="Test Setup" name="should configure test environment" time="0.005">
    </testcase>
    <testcase classname="Customer Home Flow Integration › Complete User Journey" name="loads home screen and displays all data correctly" time="0.045">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;mockResolvedValue&apos;)
    at Object.mockResolvedValue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\__tests__\integration\CustomerHomeFlow.integration.test.tsx:79:43)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:281:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Customer Home Flow Integration › Complete User Journey" name="handles category selection flow" time="0.002">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;mockResolvedValue&apos;)
    at Object.mockResolvedValue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\__tests__\integration\CustomerHomeFlow.integration.test.tsx:79:43)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:281:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Customer Home Flow Integration › Complete User Journey" name="handles provider selection flow" time="0.002">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;mockResolvedValue&apos;)
    at Object.mockResolvedValue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\__tests__\integration\CustomerHomeFlow.integration.test.tsx:79:43)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:281:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Customer Home Flow Integration › Error Handling Integration" name="handles service failures gracefully" time="0.001">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;mockResolvedValue&apos;)
    at Object.mockResolvedValue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\__tests__\integration\CustomerHomeFlow.integration.test.tsx:79:43)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:281:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Customer Home Flow Integration › Error Handling Integration" name="handles partial service failures" time="0.001">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;mockResolvedValue&apos;)
    at Object.mockResolvedValue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\__tests__\integration\CustomerHomeFlow.integration.test.tsx:79:43)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:281:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Customer Home Flow Integration › Error Handling Integration" name="handles retry functionality" time="0.001">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;mockResolvedValue&apos;)
    at Object.mockResolvedValue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\__tests__\integration\CustomerHomeFlow.integration.test.tsx:79:43)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:281:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Customer Home Flow Integration › Performance Integration" name="tracks performance metrics during normal flow" time="0.001">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;mockResolvedValue&apos;)
    at Object.mockResolvedValue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\__tests__\integration\CustomerHomeFlow.integration.test.tsx:79:43)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:281:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Customer Home Flow Integration › Performance Integration" name="handles performance under load" time="0.001">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;mockResolvedValue&apos;)
    at Object.mockResolvedValue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\__tests__\integration\CustomerHomeFlow.integration.test.tsx:79:43)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:281:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Customer Home Flow Integration › Cache Integration" name="uses cached data when available" time="0.003">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;mockResolvedValue&apos;)
    at Object.mockResolvedValue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\__tests__\integration\CustomerHomeFlow.integration.test.tsx:79:43)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:281:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Customer Home Flow Integration › Cache Integration" name="falls back to API when cache is expired" time="0.001">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;mockResolvedValue&apos;)
    at Object.mockResolvedValue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\__tests__\integration\CustomerHomeFlow.integration.test.tsx:79:43)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:281:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Customer Home Flow Integration › Cache Integration" name="updates cache after successful API calls" time="0.001">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;mockResolvedValue&apos;)
    at Object.mockResolvedValue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\__tests__\integration\CustomerHomeFlow.integration.test.tsx:79:43)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:281:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Customer Home Flow Integration › Refresh Integration" name="handles pull-to-refresh correctly" time="0.001">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;mockResolvedValue&apos;)
    at Object.mockResolvedValue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\__tests__\integration\CustomerHomeFlow.integration.test.tsx:79:43)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:281:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Customer Home Flow Integration › Refresh Integration" name="shows refreshing state during refresh" time="0.001">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;mockResolvedValue&apos;)
    at Object.mockResolvedValue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\__tests__\integration\CustomerHomeFlow.integration.test.tsx:79:43)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:281:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Customer Home Flow Integration › Memory and Resource Management" name="cleans up resources on unmount" time="0.001">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;mockResolvedValue&apos;)
    at Object.mockResolvedValue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\__tests__\integration\CustomerHomeFlow.integration.test.tsx:79:43)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:281:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Customer Home Flow Integration › Memory and Resource Management" name="handles multiple rapid re-renders efficiently" time="0.001">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;mockResolvedValue&apos;)
    at Object.mockResolvedValue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\__tests__\integration\CustomerHomeFlow.integration.test.tsx:79:43)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:281:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="21" skipped="0" timestamp="2025-07-21T07:45:52" time="0.673" tests="22">
    <testcase classname="Test Setup" name="should configure test environment" time="0.006">
    </testcase>
    <testcase classname="CustomerHomeScreen › Rendering" name="renders the home screen correctly" time="0.033">
      <failure>Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11889:28)
    at createFiberFromElement (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11903:14)
    at reconcileChildFibersImpl (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3203:31)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3380:33
    at reconcileChildren (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:5361:13)
    at beginWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:7252:13)
    at runWithFiberInDEV (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:540:16)
    at performUnitOfWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10925:22)
    at workLoopSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10763:41)
    at renderRootSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10744:11)
    at performWorkOnRoot (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10299:39)
    at performWorkOnRootViaSchedulerTask (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:1879:7)
    at flushActQueue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:862:34)
    at Object.&lt;anonymous&gt;.process.env.NODE_ENV.exports.act (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:1151:10)
    at actImplementation (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\act.ts:31:25)
    at renderWithAct (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-act.ts:13:11)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:69:33)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:44:10)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\screens\__tests__\CustomerHomeScreen.test.tsx:136:13)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="CustomerHomeScreen › Rendering" name="displays user greeting when authenticated" time="0.008">
      <failure>Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11889:28)
    at createFiberFromElement (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11903:14)
    at reconcileChildFibersImpl (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3203:31)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3380:33
    at reconcileChildren (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:5361:13)
    at beginWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:7252:13)
    at runWithFiberInDEV (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:540:16)
    at performUnitOfWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10925:22)
    at workLoopSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10763:41)
    at renderRootSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10744:11)
    at performWorkOnRoot (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10299:39)
    at performWorkOnRootViaSchedulerTask (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:1879:7)
    at flushActQueue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:862:34)
    at Object.&lt;anonymous&gt;.process.env.NODE_ENV.exports.act (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:1151:10)
    at actImplementation (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\act.ts:31:25)
    at renderWithAct (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-act.ts:13:11)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:69:33)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:44:10)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\screens\__tests__\CustomerHomeScreen.test.tsx:149:13)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="CustomerHomeScreen › Rendering" name="renders categories correctly" time="0.008">
      <failure>Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11889:28)
    at createFiberFromElement (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11903:14)
    at reconcileChildFibersImpl (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3203:31)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3380:33
    at reconcileChildren (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:5361:13)
    at beginWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:7252:13)
    at runWithFiberInDEV (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:540:16)
    at performUnitOfWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10925:22)
    at workLoopSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10763:41)
    at renderRootSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10744:11)
    at performWorkOnRoot (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10299:39)
    at performWorkOnRootViaSchedulerTask (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:1879:7)
    at flushActQueue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:862:34)
    at Object.&lt;anonymous&gt;.process.env.NODE_ENV.exports.act (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:1151:10)
    at actImplementation (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\act.ts:31:25)
    at renderWithAct (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-act.ts:13:11)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:69:33)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:44:10)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\screens\__tests__\CustomerHomeScreen.test.tsx:161:13)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="CustomerHomeScreen › Rendering" name="renders featured providers correctly" time="0.006">
      <failure>Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11889:28)
    at createFiberFromElement (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11903:14)
    at reconcileChildFibersImpl (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3203:31)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3380:33
    at reconcileChildren (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:5361:13)
    at beginWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:7252:13)
    at runWithFiberInDEV (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:540:16)
    at performUnitOfWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10925:22)
    at workLoopSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10763:41)
    at renderRootSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10744:11)
    at performWorkOnRoot (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10299:39)
    at performWorkOnRootViaSchedulerTask (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:1879:7)
    at flushActQueue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:862:34)
    at Object.&lt;anonymous&gt;.process.env.NODE_ENV.exports.act (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:1151:10)
    at actImplementation (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\act.ts:31:25)
    at renderWithAct (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-act.ts:13:11)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:69:33)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:44:10)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\screens\__tests__\CustomerHomeScreen.test.tsx:175:13)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="CustomerHomeScreen › Loading States" name="shows loading indicators when data is loading" time="0.005">
      <failure>Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11889:28)
    at createFiberFromElement (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11903:14)
    at reconcileChildFibersImpl (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3203:31)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3380:33
    at reconcileChildren (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:5361:13)
    at beginWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:7252:13)
    at runWithFiberInDEV (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:540:16)
    at performUnitOfWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10925:22)
    at workLoopSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10763:41)
    at renderRootSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10744:11)
    at performWorkOnRoot (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10299:39)
    at performWorkOnRootViaSchedulerTask (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:1879:7)
    at flushActQueue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:862:34)
    at Object.&lt;anonymous&gt;.process.env.NODE_ENV.exports.act (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:1151:10)
    at actImplementation (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\act.ts:31:25)
    at renderWithAct (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-act.ts:13:11)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:69:33)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:44:10)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\screens\__tests__\CustomerHomeScreen.test.tsx:221:13)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="CustomerHomeScreen › Loading States" name="shows refreshing state correctly" time="0.005">
      <failure>Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11889:28)
    at createFiberFromElement (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11903:14)
    at reconcileChildFibersImpl (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3203:31)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3380:33
    at reconcileChildren (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:5361:13)
    at beginWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:7252:13)
    at runWithFiberInDEV (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:540:16)
    at performUnitOfWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10925:22)
    at workLoopSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10763:41)
    at renderRootSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10744:11)
    at performWorkOnRoot (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10299:39)
    at performWorkOnRootViaSchedulerTask (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:1879:7)
    at flushActQueue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:862:34)
    at Object.&lt;anonymous&gt;.process.env.NODE_ENV.exports.act (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:1151:10)
    at actImplementation (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\act.ts:31:25)
    at renderWithAct (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-act.ts:13:11)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:69:33)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:44:10)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\screens\__tests__\CustomerHomeScreen.test.tsx:261:13)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="CustomerHomeScreen › Error States" name="handles category loading errors gracefully" time="0.007">
      <failure>Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11889:28)
    at createFiberFromElement (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11903:14)
    at reconcileChildFibersImpl (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3203:31)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3380:33
    at reconcileChildren (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:5361:13)
    at beginWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:7252:13)
    at runWithFiberInDEV (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:540:16)
    at performUnitOfWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10925:22)
    at workLoopSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10763:41)
    at renderRootSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10744:11)
    at performWorkOnRoot (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10299:39)
    at performWorkOnRootViaSchedulerTask (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:1879:7)
    at flushActQueue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:862:34)
    at Object.&lt;anonymous&gt;.process.env.NODE_ENV.exports.act (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:1151:10)
    at actImplementation (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\act.ts:31:25)
    at renderWithAct (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-act.ts:13:11)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:69:33)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:44:10)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\screens\__tests__\CustomerHomeScreen.test.tsx:304:13)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="CustomerHomeScreen › Error States" name="handles provider loading errors gracefully" time="0.005">
      <failure>Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11889:28)
    at createFiberFromElement (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11903:14)
    at reconcileChildFibersImpl (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3203:31)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3380:33
    at reconcileChildren (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:5361:13)
    at beginWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:7252:13)
    at runWithFiberInDEV (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:540:16)
    at performUnitOfWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10925:22)
    at workLoopSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10763:41)
    at renderRootSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10744:11)
    at performWorkOnRoot (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10299:39)
    at performWorkOnRootViaSchedulerTask (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:1879:7)
    at flushActQueue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:862:34)
    at Object.&lt;anonymous&gt;.process.env.NODE_ENV.exports.act (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:1151:10)
    at actImplementation (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\act.ts:31:25)
    at renderWithAct (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-act.ts:13:11)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:69:33)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:44:10)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\screens\__tests__\CustomerHomeScreen.test.tsx:345:13)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="CustomerHomeScreen › User Interactions" name="handles category press correctly" time="0.005">
      <failure>Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11889:28)
    at createFiberFromElement (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11903:14)
    at reconcileChildFibersImpl (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3203:31)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3380:33
    at reconcileChildren (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:5361:13)
    at beginWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:7252:13)
    at runWithFiberInDEV (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:540:16)
    at performUnitOfWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10925:22)
    at workLoopSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10763:41)
    at renderRootSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10744:11)
    at performWorkOnRoot (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10299:39)
    at performWorkOnRootViaSchedulerTask (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:1879:7)
    at flushActQueue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:862:34)
    at Object.&lt;anonymous&gt;.process.env.NODE_ENV.exports.act (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:1151:10)
    at actImplementation (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\act.ts:31:25)
    at renderWithAct (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-act.ts:13:11)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:69:33)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:44:10)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\screens\__tests__\CustomerHomeScreen.test.tsx:359:13)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="CustomerHomeScreen › User Interactions" name="handles provider press correctly" time="0.004">
      <failure>Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11889:28)
    at createFiberFromElement (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11903:14)
    at reconcileChildFibersImpl (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3203:31)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3380:33
    at reconcileChildren (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:5361:13)
    at beginWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:7252:13)
    at runWithFiberInDEV (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:540:16)
    at performUnitOfWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10925:22)
    at workLoopSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10763:41)
    at renderRootSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10744:11)
    at performWorkOnRoot (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10299:39)
    at performWorkOnRootViaSchedulerTask (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:1879:7)
    at flushActQueue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:862:34)
    at Object.&lt;anonymous&gt;.process.env.NODE_ENV.exports.act (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:1151:10)
    at actImplementation (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\act.ts:31:25)
    at renderWithAct (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-act.ts:13:11)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:69:33)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:44:10)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\screens\__tests__\CustomerHomeScreen.test.tsx:374:13)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="CustomerHomeScreen › User Interactions" name="handles pull to refresh correctly" time="0.006">
      <failure>Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11889:28)
    at createFiberFromElement (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11903:14)
    at reconcileChildFibersImpl (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3203:31)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3380:33
    at reconcileChildren (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:5361:13)
    at beginWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:7252:13)
    at runWithFiberInDEV (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:540:16)
    at performUnitOfWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10925:22)
    at workLoopSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10763:41)
    at renderRootSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10744:11)
    at performWorkOnRoot (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10299:39)
    at performWorkOnRootViaSchedulerTask (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:1879:7)
    at flushActQueue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:862:34)
    at Object.&lt;anonymous&gt;.process.env.NODE_ENV.exports.act (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:1151:10)
    at actImplementation (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\act.ts:31:25)
    at renderWithAct (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-act.ts:13:11)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:69:33)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:44:10)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\screens\__tests__\CustomerHomeScreen.test.tsx:389:13)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="CustomerHomeScreen › User Interactions" name="handles see all buttons correctly" time="0.004">
      <failure>Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11889:28)
    at createFiberFromElement (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11903:14)
    at reconcileChildFibersImpl (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3203:31)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3380:33
    at reconcileChildren (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:5361:13)
    at beginWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:7252:13)
    at runWithFiberInDEV (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:540:16)
    at performUnitOfWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10925:22)
    at workLoopSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10763:41)
    at renderRootSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10744:11)
    at performWorkOnRoot (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10299:39)
    at performWorkOnRootViaSchedulerTask (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:1879:7)
    at flushActQueue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:862:34)
    at Object.&lt;anonymous&gt;.process.env.NODE_ENV.exports.act (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:1151:10)
    at actImplementation (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\act.ts:31:25)
    at renderWithAct (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-act.ts:13:11)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:69:33)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:44:10)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\screens\__tests__\CustomerHomeScreen.test.tsx:405:13)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="CustomerHomeScreen › Performance" name="tracks component render performance" time="0.004">
      <failure>Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11889:28)
    at createFiberFromElement (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11903:14)
    at reconcileChildFibersImpl (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3203:31)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3380:33
    at reconcileChildren (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:5361:13)
    at beginWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:7252:13)
    at runWithFiberInDEV (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:540:16)
    at performUnitOfWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10925:22)
    at workLoopSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10763:41)
    at renderRootSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10744:11)
    at performWorkOnRoot (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10299:39)
    at performWorkOnRootViaSchedulerTask (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:1879:7)
    at flushActQueue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:862:34)
    at Object.&lt;anonymous&gt;.process.env.NODE_ENV.exports.act (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:1151:10)
    at actImplementation (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\act.ts:31:25)
    at renderWithAct (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-act.ts:13:11)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:69:33)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:44:10)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\screens\__tests__\CustomerHomeScreen.test.tsx:424:13)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="CustomerHomeScreen › Performance" name="tracks user interaction performance" time="0.005">
      <failure>Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11889:28)
    at createFiberFromElement (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11903:14)
    at reconcileChildFibersImpl (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3203:31)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3380:33
    at reconcileChildren (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:5361:13)
    at beginWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:7252:13)
    at runWithFiberInDEV (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:540:16)
    at performUnitOfWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10925:22)
    at workLoopSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10763:41)
    at renderRootSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10744:11)
    at performWorkOnRoot (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10299:39)
    at performWorkOnRootViaSchedulerTask (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:1879:7)
    at flushActQueue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:862:34)
    at Object.&lt;anonymous&gt;.process.env.NODE_ENV.exports.act (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:1151:10)
    at actImplementation (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\act.ts:31:25)
    at renderWithAct (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-act.ts:13:11)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:69:33)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:44:10)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\screens\__tests__\CustomerHomeScreen.test.tsx:442:13)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="CustomerHomeScreen › Performance" name="renders within performance threshold" time="0.001">
      <failure>ReferenceError: measureAsyncPerformance is not defined
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\screens\__tests__\CustomerHomeScreen.test.tsx:461:23)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="CustomerHomeScreen › Accessibility" name="has proper accessibility labels" time="0.005">
      <failure>Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11889:28)
    at createFiberFromElement (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11903:14)
    at reconcileChildFibersImpl (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3203:31)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3380:33
    at reconcileChildren (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:5361:13)
    at beginWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:7252:13)
    at runWithFiberInDEV (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:540:16)
    at performUnitOfWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10925:22)
    at workLoopSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10763:41)
    at renderRootSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10744:11)
    at performWorkOnRoot (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10299:39)
    at performWorkOnRootViaSchedulerTask (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:1879:7)
    at flushActQueue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:862:34)
    at Object.&lt;anonymous&gt;.process.env.NODE_ENV.exports.act (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:1151:10)
    at actImplementation (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\act.ts:31:25)
    at renderWithAct (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-act.ts:13:11)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:69:33)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:44:10)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\screens\__tests__\CustomerHomeScreen.test.tsx:480:13)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="CustomerHomeScreen › Accessibility" name="has proper accessibility roles" time="0.004">
      <failure>Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11889:28)
    at createFiberFromElement (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11903:14)
    at reconcileChildFibersImpl (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3203:31)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3380:33
    at reconcileChildren (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:5361:13)
    at beginWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:7252:13)
    at runWithFiberInDEV (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:540:16)
    at performUnitOfWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10925:22)
    at workLoopSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10763:41)
    at renderRootSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10744:11)
    at performWorkOnRoot (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10299:39)
    at performWorkOnRootViaSchedulerTask (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:1879:7)
    at flushActQueue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:862:34)
    at Object.&lt;anonymous&gt;.process.env.NODE_ENV.exports.act (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:1151:10)
    at actImplementation (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\act.ts:31:25)
    at renderWithAct (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-act.ts:13:11)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:69:33)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:44:10)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\screens\__tests__\CustomerHomeScreen.test.tsx:492:13)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="CustomerHomeScreen › Accessibility" name="supports screen reader navigation" time="0.004">
      <failure>Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11889:28)
    at createFiberFromElement (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11903:14)
    at reconcileChildFibersImpl (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3203:31)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3380:33
    at reconcileChildren (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:5361:13)
    at beginWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:7252:13)
    at runWithFiberInDEV (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:540:16)
    at performUnitOfWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10925:22)
    at workLoopSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10763:41)
    at renderRootSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10744:11)
    at performWorkOnRoot (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10299:39)
    at performWorkOnRootViaSchedulerTask (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:1879:7)
    at flushActQueue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:862:34)
    at Object.&lt;anonymous&gt;.process.env.NODE_ENV.exports.act (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:1151:10)
    at actImplementation (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\act.ts:31:25)
    at renderWithAct (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-act.ts:13:11)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:69:33)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:44:10)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\screens\__tests__\CustomerHomeScreen.test.tsx:503:13)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="CustomerHomeScreen › Integration" name="integrates with auth store correctly" time="0.004">
      <failure>Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11889:28)
    at createFiberFromElement (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11903:14)
    at reconcileChildFibersImpl (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3203:31)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3380:33
    at reconcileChildren (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:5361:13)
    at beginWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:7252:13)
    at runWithFiberInDEV (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:540:16)
    at performUnitOfWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10925:22)
    at workLoopSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10763:41)
    at renderRootSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10744:11)
    at performWorkOnRoot (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10299:39)
    at performWorkOnRootViaSchedulerTask (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:1879:7)
    at flushActQueue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:862:34)
    at Object.&lt;anonymous&gt;.process.env.NODE_ENV.exports.act (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:1151:10)
    at actImplementation (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\act.ts:31:25)
    at renderWithAct (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-act.ts:13:11)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:69:33)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:44:10)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\screens\__tests__\CustomerHomeScreen.test.tsx:522:13)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="CustomerHomeScreen › Integration" name="integrates with navigation guard correctly" time="0.004">
      <failure>Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11889:28)
    at createFiberFromElement (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11903:14)
    at reconcileChildFibersImpl (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3203:31)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3380:33
    at reconcileChildren (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:5361:13)
    at beginWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:7252:13)
    at runWithFiberInDEV (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:540:16)
    at performUnitOfWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10925:22)
    at workLoopSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10763:41)
    at renderRootSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10744:11)
    at performWorkOnRoot (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10299:39)
    at performWorkOnRootViaSchedulerTask (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:1879:7)
    at flushActQueue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:862:34)
    at Object.&lt;anonymous&gt;.process.env.NODE_ENV.exports.act (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:1151:10)
    at actImplementation (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\act.ts:31:25)
    at renderWithAct (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-act.ts:13:11)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:69:33)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:44:10)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\screens\__tests__\CustomerHomeScreen.test.tsx:532:13)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="CustomerHomeScreen › Integration" name="integrates with customer home data hook correctly" time="0.006">
      <failure>Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it&apos;s defined in, or you might have mixed up default and named imports.
    at createFiberFromTypeAndProps (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11889:28)
    at createFiberFromElement (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:11903:14)
    at reconcileChildFibersImpl (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3203:31)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:3380:33
    at reconcileChildren (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:5361:13)
    at beginWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:7252:13)
    at runWithFiberInDEV (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:540:16)
    at performUnitOfWork (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10925:22)
    at workLoopSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10763:41)
    at renderRootSync (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10744:11)
    at performWorkOnRoot (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:10299:39)
    at performWorkOnRootViaSchedulerTask (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:1879:7)
    at flushActQueue (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:862:34)
    at Object.&lt;anonymous&gt;.process.env.NODE_ENV.exports.act (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react\cjs\react.development.js:1151:10)
    at actImplementation (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\act.ts:31:25)
    at renderWithAct (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-act.ts:13:11)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:69:33)
    at renderInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:44:10)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\screens\__tests__\CustomerHomeScreen.test.tsx:542:13)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="1" skipped="0" timestamp="2025-07-21T07:45:53" time="0.545" tests="30">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="CacheService › Basic Cache Operations" name="sets and gets data from memory cache" time="0.012">
    </testcase>
    <testcase classname="CacheService › Basic Cache Operations" name="returns null for non-existent keys" time="0.002">
    </testcase>
    <testcase classname="CacheService › Basic Cache Operations" name="removes data from cache" time="0.001">
    </testcase>
    <testcase classname="CacheService › Basic Cache Operations" name="clears all cache data" time="0.001">
    </testcase>
    <testcase classname="CacheService › TTL and Expiration" name="respects TTL for cache entries" time="0.161">
    </testcase>
    <testcase classname="CacheService › TTL and Expiration" name="uses default TTL when not specified" time="0.001">
    </testcase>
    <testcase classname="CacheService › TTL and Expiration" name="updates TTL on cache hit" time="0.014">
    </testcase>
    <testcase classname="CacheService › Storage Cache Integration" name="falls back to storage cache when memory cache misses" time="0.001">
    </testcase>
    <testcase classname="CacheService › Storage Cache Integration" name="stores data in both memory and storage by default" time="0.001">
    </testcase>
    <testcase classname="CacheService › Storage Cache Integration" name="supports memory-only storage option" time="0.001">
    </testcase>
    <testcase classname="CacheService › Storage Cache Integration" name="supports storage-only option" time="0.001">
    </testcase>
    <testcase classname="CacheService › Memory Management" name="enforces memory limits" time="0.001">
    </testcase>
    <testcase classname="CacheService › Memory Management" name="uses LFU + LRU eviction strategy" time="0.024">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: &quot;xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx&quot;
Received: null
    at Object.toBe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\__tests__\cacheService.test.ts:247:30)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)</failure>
    </testcase>
    <testcase classname="CacheService › Cache Statistics" name="tracks cache hits and misses" time="0.001">
    </testcase>
    <testcase classname="CacheService › Cache Statistics" name="calculates hit rate correctly" time="0.001">
    </testcase>
    <testcase classname="CacheService › Cache Statistics" name="tracks entry count and total size" time="0.002">
    </testcase>
    <testcase classname="CacheService › Cache Preloading" name="preloads multiple entries" time="0.001">
    </testcase>
    <testcase classname="CacheService › Cache Preloading" name="handles preload failures gracefully" time="0">
    </testcase>
    <testcase classname="CacheService › Pattern-based Invalidation" name="invalidates entries matching pattern" time="0.001">
    </testcase>
    <testcase classname="CacheService › Pattern-based Invalidation" name="invalidates storage entries matching pattern" time="0.001">
    </testcase>
    <testcase classname="CacheService › Entry Information" name="provides entry metadata" time="0.001">
    </testcase>
    <testcase classname="CacheService › Entry Information" name="returns null for non-existent entries" time="0.001">
    </testcase>
    <testcase classname="CacheService › Entry Information" name="updates access count on cache hits" time="0.001">
    </testcase>
    <testcase classname="CacheService › Error Handling" name="handles AsyncStorage errors gracefully" time="0.004">
    </testcase>
    <testcase classname="CacheService › Error Handling" name="handles set errors gracefully" time="0.002">
    </testcase>
    <testcase classname="CacheService › Error Handling" name="handles remove errors gracefully" time="0.003">
    </testcase>
    <testcase classname="CacheService › Error Handling" name="handles clear errors gracefully" time="0.003">
    </testcase>
    <testcase classname="CacheService › Service Lifecycle" name="destroys service correctly" time="0.002">
    </testcase>
    <testcase classname="CacheService › Service Lifecycle" name="cleans up expired entries periodically" time="0.109">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="23" skipped="0" timestamp="2025-07-21T07:45:53" time="10.433" tests="26">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="useErrorHandling › Basic Error Handling" name="initializes with no error state" time="0.013">
    </testcase>
    <testcase classname="useErrorHandling › Basic Error Handling" name="handles string errors" time="0.004">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: &quot;Test error message&quot;
Received: undefined
    at Object.toBe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:68:45)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Basic Error Handling" name="handles Error objects" time="0.003">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: &quot;Test error object&quot;
Received: undefined
    at Object.toBe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:81:45)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Basic Error Handling" name="clears error state" time="0.003">
    </testcase>
    <testcase classname="useErrorHandling › Retry Logic" name="increments retry count on retry" time="10.017">
      <failure>Error: thrown: &quot;Exceeded timeout of 10000 ms for a test.
Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout.&quot;
    at it (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:105:5)
    at _dispatchDescribe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\index.js:91:26)
    at describe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\index.js:55:5)
    at describe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:104:3)
    at _dispatchDescribe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\index.js:91:26)
    at describe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\index.js:55:5)
    at Object.describe (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:40:1)
    at Runtime._execModule (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runtime\build\index.js:1439:24)
    at Runtime._loadModule (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runtime\build\index.js:1022:12)
    at Runtime.requireModule (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runtime\build\index.js:882:12)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:77:13)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Retry Logic" name="respects max retries limit" time="0.003">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:123:36)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Retry Logic" name="uses progressive retry delays" time="0.003">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:154:36)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Retry Logic" name="uses fixed retry delay when progressive is disabled" time="0.003">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:181:36)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Error Classification" name="identifies network errors" time="0.002">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:208:36)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Error Classification" name="identifies server errors" time="0.002">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:220:36)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Error Classification" name="identifies authentication errors" time="0.003">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:232:36)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Error Classification" name="provides appropriate error messages" time="0.002">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:244:36)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Callbacks and Options" name="calls onError callback" time="0.004">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:274:36)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Callbacks and Options" name="calls onRetry callback" time="0.003">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:287:36)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Callbacks and Options" name="calls onMaxRetriesExceeded callback" time="0.003">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:303:36)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Callbacks and Options" name="respects error reporting setting" time="0.009">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:323:36)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Performance Tracking Integration" name="tracks error handling performance" time="0.012">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:339:36)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Performance Tracking Integration" name="tracks retry performance" time="0.002">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:356:36)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › App State Integration" name="retries on app focus when enabled" time="0.002">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:380:36)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › App State Integration" name="does not retry on app focus when disabled" time="0.001">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:413:36)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Cleanup" name="cleans up timeouts on unmount" time="0.005">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:433:45)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Cleanup" name="removes app state listeners on unmount" time="0.003">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:456:37)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Edge Cases" name="handles retry when no error exists" time="0.003">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:468:36)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Edge Cases" name="handles retry when max retries already exceeded" time="0.003">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:479:36)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="useErrorHandling › Edge Cases" name="handles component unmount during retry delay" time="0.003">
      <failure>Error: Can&apos;t access .root on unmounted test renderer
    at Object.get [as root] (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\react-test-renderer\cjs\react-test-renderer.development.js:14491:19)
    at root (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:102:29)
    at buildRenderResult (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render.tsx:70:10)
    at renderHook (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@testing-library\react-native\src\render-hook.tsx:49:66)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\hooks\__tests__\useErrorHandling.test.ts:505:45)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="4" skipped="0" timestamp="2025-07-21T07:46:04" time="0.366" tests="26">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="PerformanceMonitorService › Initialization" name="starts monitoring correctly" time="0.013">
    </testcase>
    <testcase classname="PerformanceMonitorService › Initialization" name="stops monitoring correctly" time="0.004">
    </testcase>
    <testcase classname="PerformanceMonitorService › Initialization" name="prevents double initialization" time="0.005">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledTimes(expected)

Expected number of calls: 1
Received number of calls: 0
    at Object.toHaveBeenCalledTimes (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\__tests__\performanceMonitor.test.ts:45:26)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="PerformanceMonitorService › Render Performance Tracking" name="tracks component render times" time="0.008">
    </testcase>
    <testcase classname="PerformanceMonitorService › Render Performance Tracking" name="calculates average render times for components" time="0.007">
    </testcase>
    <testcase classname="PerformanceMonitorService › Render Performance Tracking" name="warns about slow renders" time="0.007">
    </testcase>
    <testcase classname="PerformanceMonitorService › Render Performance Tracking" name="tracks render metadata correctly" time="0.004">
    </testcase>
    <testcase classname="PerformanceMonitorService › Network Performance Tracking" name="tracks network request performance" time="0.005">
    </testcase>
    <testcase classname="PerformanceMonitorService › Network Performance Tracking" name="warns about slow network requests" time="0.004">
    </testcase>
    <testcase classname="PerformanceMonitorService › Network Performance Tracking" name="tracks cached vs non-cached requests" time="0.004">
    </testcase>
    <testcase classname="PerformanceMonitorService › Network Performance Tracking" name="limits stored network metrics" time="0.005">
    </testcase>
    <testcase classname="PerformanceMonitorService › User Interaction Tracking" name="tracks user interaction performance" time="0.004">
    </testcase>
    <testcase classname="PerformanceMonitorService › User Interaction Tracking" name="warns about slow interactions" time="0.004">
    </testcase>
    <testcase classname="PerformanceMonitorService › Navigation Performance Tracking" name="tracks navigation performance" time="0.004">
    </testcase>
    <testcase classname="PerformanceMonitorService › Performance Reports" name="generates comprehensive performance report" time="0.006">
    </testcase>
    <testcase classname="PerformanceMonitorService › Performance Reports" name="calculates cache hit rate correctly" time="0.005">
    </testcase>
    <testcase classname="PerformanceMonitorService › Performance Reports" name="identifies slow components" time="0.005">
    </testcase>
    <testcase classname="PerformanceMonitorService › Performance Reports" name="identifies slow network requests" time="0.005">
    </testcase>
    <testcase classname="PerformanceMonitorService › Performance Reports" name="generates performance recommendations" time="0.008">
      <failure>Error: expect(received).toContain(expected) // indexOf

Expected value: StringContaining &quot;Optimize slow components&quot;
Received array: [&quot;Consider optimizing component renders with React.memo or useMemo&quot;, &quot;Optimize slow components: SlowComponent&quot;, &quot;Consider implementing request caching or optimizing API endpoints&quot;]

Looks like you wanted to test for object/array equality with the stricter `toContain` matcher. You probably need to use `toContainEqual` instead.
    at Object.toContain (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\__tests__\performanceMonitor.test.ts:247:38)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="PerformanceMonitorService › Memory Monitoring" name="collects memory metrics when available" time="0.005">
    </testcase>
    <testcase classname="PerformanceMonitorService › Memory Monitoring" name="detects potential memory leaks" time="0.003">
    </testcase>
    <testcase classname="PerformanceMonitorService › Memory Monitoring" name="detects excessive re-renders" time="0.003">
      <failure>Error: expect(received).toBeDefined()

Received: undefined
    at Object.toBeDefined (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\__tests__\performanceMonitor.test.ts:302:28)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="PerformanceMonitorService › Cleanup and Maintenance" name="clears all metrics" time="0.004">
    </testcase>
    <testcase classname="PerformanceMonitorService › Cleanup and Maintenance" name="cleans up old metrics automatically" time="0.003">
    </testcase>
    <testcase classname="PerformanceMonitorService › Cleanup and Maintenance" name="destroys service correctly" time="0.003">
      <failure>TypeError: _performanceMonitor.performanceMonitor.destroy is not a function
    at Object.destroy (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\services\__tests__\performanceMonitor.test.ts:338:26)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="11" skipped="0" timestamp="2025-07-21T07:46:04" time="3.485" tests="15">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="ErrorBoundary › Error Handling" name="catches errors in child components" time="2.206">
      <failure>Error: Unable to find an element with text: Something went wrong

&lt;View&gt;
  &lt;View&gt;
    &lt;View&gt;
      &lt;Text
        accessibilityRole=&quot;text&quot;
      &gt;
        ⚠️
      &lt;/Text&gt;
    &lt;/View&gt;
    &lt;Text
      accessibilityRole=&quot;header&quot;
    &gt;
      Minor Issue
    &lt;/Text&gt;
    &lt;Text
      accessibilityRole=&quot;text&quot;
    &gt;
      An unexpected error occurred. Please try again.
    &lt;/Text&gt;
    &lt;View&gt;
      &lt;Text
        accessibilityRole=&quot;text&quot;
      &gt;
        Try these solutions:
      &lt;/Text&gt;
      &lt;Text
        accessibilityRole=&quot;text&quot;
      &gt;
        • 
        Try refreshing the page
      &lt;/Text&gt;
      &lt;Text
        accessibilityRole=&quot;text&quot;
      &gt;
        • 
        Restart the app
      &lt;/Text&gt;
      &lt;Text
        accessibilityRole=&quot;text&quot;
      &gt;
        • 
        Contact support if the problem continues
      &lt;/Text&gt;
    &lt;/View&gt;
    &lt;View&gt;
      &lt;View
        accessibilityLabel=&quot;Go Back&quot;
        accessibilityRole=&quot;button&quot;
        accessibilityState={
          {
            &quot;busy&quot;: false,
            &quot;disabled&quot;: false,
          }
        }
        accessible={true}
      &gt;
        &lt;View&gt;
          &lt;Text
            accessibilityRole=&quot;text&quot;
          &gt;
            Go Back
          &lt;/Text&gt;
        &lt;/View&gt;
      &lt;/View&gt;
    &lt;/View&gt;
    &lt;Text
      accessibilityRole=&quot;text&quot;
    &gt;
      Error ID: 
      err_1753083966939_dylpe984m
    &lt;/Text&gt;
  &lt;/View&gt;
&lt;/View&gt;
    at Object.getByText (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\error\__tests__\ErrorBoundary.test.tsx:80:21)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="ErrorBoundary › Error Handling" name="renders children normally when no errors occur" time="0.002">
      <failure>TypeError: _reactNative.screen.getByTestID is not a function
    at Object.getByTestID (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\error\__tests__\ErrorBoundary.test.tsx:90:21)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="ErrorBoundary › Error Handling" name="calls onError callback when an error occurs" time="0.085">
    </testcase>
    <testcase classname="ErrorBoundary › Error Handling" name="tracks errors with performance monitor" time="0.08">
    </testcase>
    <testcase classname="ErrorBoundary › Fallback UI" name="renders default fallback UI" time="0.075">
      <failure>Error: Unable to find an element with text: Something went wrong

&lt;View&gt;
  &lt;View&gt;
    &lt;View&gt;
      &lt;Text
        accessibilityRole=&quot;text&quot;
      &gt;
        ⚠️
      &lt;/Text&gt;
    &lt;/View&gt;
    &lt;Text
      accessibilityRole=&quot;header&quot;
    &gt;
      Minor Issue
    &lt;/Text&gt;
    &lt;Text
      accessibilityRole=&quot;text&quot;
    &gt;
      An unexpected error occurred. Please try again.
    &lt;/Text&gt;
    &lt;View&gt;
      &lt;Text
        accessibilityRole=&quot;text&quot;
      &gt;
        Try these solutions:
      &lt;/Text&gt;
      &lt;Text
        accessibilityRole=&quot;text&quot;
      &gt;
        • 
        Try refreshing the page
      &lt;/Text&gt;
      &lt;Text
        accessibilityRole=&quot;text&quot;
      &gt;
        • 
        Restart the app
      &lt;/Text&gt;
      &lt;Text
        accessibilityRole=&quot;text&quot;
      &gt;
        • 
        Contact support if the problem continues
      &lt;/Text&gt;
    &lt;/View&gt;
    &lt;View&gt;
      &lt;View
        accessibilityLabel=&quot;Go Back&quot;
        accessibilityRole=&quot;button&quot;
        accessibilityState={
          {
            &quot;busy&quot;: false,
            &quot;disabled&quot;: false,
          }
        }
        accessible={true}
      &gt;
        &lt;View&gt;
          &lt;Text
            accessibilityRole=&quot;text&quot;
          &gt;
            Go Back
          &lt;/Text&gt;
        &lt;/View&gt;
      &lt;/View&gt;
    &lt;/View&gt;
    &lt;Text
      accessibilityRole=&quot;text&quot;
    &gt;
      Error ID: 
      err_1753083967213_rm4ql4m0q
    &lt;/Text&gt;
  &lt;/View&gt;
&lt;/View&gt;
    at Object.getByText (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\error\__tests__\ErrorBoundary.test.tsx:134:21)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="ErrorBoundary › Fallback UI" name="renders custom fallback UI when provided" time="0.073">
      <failure>TypeError: _reactNative.screen.getByTestID is not a function
    at Object.getByTestID (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\error\__tests__\ErrorBoundary.test.tsx:153:21)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="ErrorBoundary › Fallback UI" name="shows technical details in development mode" time="0.076">
      <failure>Error: Unable to find an element with text: /Technical Details/

&lt;View&gt;
  &lt;View&gt;
    &lt;View&gt;
      &lt;Text
        accessibilityRole=&quot;text&quot;
      &gt;
        ⚠️
      &lt;/Text&gt;
    &lt;/View&gt;
    &lt;Text
      accessibilityRole=&quot;header&quot;
    &gt;
      Minor Issue
    &lt;/Text&gt;
    &lt;Text
      accessibilityRole=&quot;text&quot;
    &gt;
      An unexpected error occurred. Please try again.
    &lt;/Text&gt;
    &lt;View&gt;
      &lt;Text
        accessibilityRole=&quot;text&quot;
      &gt;
        Try these solutions:
      &lt;/Text&gt;
      &lt;Text
        accessibilityRole=&quot;text&quot;
      &gt;
        • 
        Try refreshing the page
      &lt;/Text&gt;
      &lt;Text
        accessibilityRole=&quot;text&quot;
      &gt;
        • 
        Restart the app
      &lt;/Text&gt;
      &lt;Text
        accessibilityRole=&quot;text&quot;
      &gt;
        • 
        Contact support if the problem continues
      &lt;/Text&gt;
    &lt;/View&gt;
    &lt;View&gt;
      &lt;View
        accessibilityLabel=&quot;Go Back&quot;
        accessibilityRole=&quot;button&quot;
        accessibilityState={
          {
            &quot;busy&quot;: false,
            &quot;disabled&quot;: false,
          }
        }
        accessible={true}
      &gt;
        &lt;View&gt;
          &lt;Text
            accessibilityRole=&quot;text&quot;
          &gt;
            Go Back
          &lt;/Text&gt;
        &lt;/View&gt;
      &lt;/View&gt;
    &lt;/View&gt;
    &lt;Text
      accessibilityRole=&quot;text&quot;
    &gt;
      Error ID: 
      err_1753083967363_powh43x5v
    &lt;/Text&gt;
  &lt;/View&gt;
&lt;/View&gt;
    at Object.getByText (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\error\__tests__\ErrorBoundary.test.tsx:168:21)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="ErrorBoundary › Reset Functionality" name="resets error state when retry button is pressed" time="0.104">
      <failure>Error: Unable to find an element with text: Something went wrong

&lt;View&gt;
  &lt;View&gt;
    &lt;View&gt;
      &lt;Text
        accessibilityRole=&quot;text&quot;
      &gt;
        ⚠️
      &lt;/Text&gt;
    &lt;/View&gt;
    &lt;Text
      accessibilityRole=&quot;header&quot;
    &gt;
      Minor Issue
    &lt;/Text&gt;
    &lt;Text
      accessibilityRole=&quot;text&quot;
    &gt;
      An unexpected error occurred. Please try again.
    &lt;/Text&gt;
    &lt;View&gt;
      &lt;Text
        accessibilityRole=&quot;text&quot;
      &gt;
        Try these solutions:
      &lt;/Text&gt;
      &lt;Text
        accessibilityRole=&quot;text&quot;
      &gt;
        • 
        Try refreshing the page
      &lt;/Text&gt;
      &lt;Text
        accessibilityRole=&quot;text&quot;
      &gt;
        • 
        Restart the app
      &lt;/Text&gt;
      &lt;Text
        accessibilityRole=&quot;text&quot;
      &gt;
        • 
        Contact support if the problem continues
      &lt;/Text&gt;
    &lt;/View&gt;
    &lt;View&gt;
      &lt;View
        accessibilityLabel=&quot;Go Back&quot;
        accessibilityRole=&quot;button&quot;
        accessibilityState={
          {
            &quot;busy&quot;: false,
            &quot;disabled&quot;: false,
          }
        }
        accessible={true}
      &gt;
        &lt;View&gt;
          &lt;Text
            accessibilityRole=&quot;text&quot;
          &gt;
            Go Back
          &lt;/Text&gt;
        &lt;/View&gt;
      &lt;/View&gt;
    &lt;/View&gt;
    &lt;Text
      accessibilityRole=&quot;text&quot;
    &gt;
      Error ID: 
      err_1753083967443_fl59ruiam
    &lt;/Text&gt;
  &lt;/View&gt;
&lt;/View&gt;
    at Object.getByText (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\error\__tests__\ErrorBoundary.test.tsx:202:21)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="ErrorBoundary › Reset Functionality" name="respects maxRetries limit" time="0.118">
      <failure>Error: Unable to find an element with text: Try Again

&lt;View&gt;
  &lt;View&gt;
    &lt;View&gt;
      &lt;Text
        accessibilityRole=&quot;text&quot;
      &gt;
        ⚠️
      &lt;/Text&gt;
    &lt;/View&gt;
    &lt;Text
      accessibilityRole=&quot;header&quot;
    &gt;
      Minor Issue
    &lt;/Text&gt;
    &lt;Text
      accessibilityRole=&quot;text&quot;
    &gt;
      An unexpected error occurred. Please try again.
    &lt;/Text&gt;
    &lt;View&gt;
      &lt;Text
        accessibilityRole=&quot;text&quot;
      &gt;
        Try these solutions:
      &lt;/Text&gt;
      &lt;Text
        accessibilityRole=&quot;text&quot;
      &gt;
        • 
        Try refreshing the page
      &lt;/Text&gt;
      &lt;Text
        accessibilityRole=&quot;text&quot;
      &gt;
        • 
        Restart the app
      &lt;/Text&gt;
      &lt;Text
        accessibilityRole=&quot;text&quot;
      &gt;
        • 
        Contact support if the problem continues
      &lt;/Text&gt;
    &lt;/View&gt;
    &lt;View&gt;
      &lt;View
        accessibilityLabel=&quot;Go Back&quot;
        accessibilityRole=&quot;button&quot;
        accessibilityState={
          {
            &quot;busy&quot;: false,
            &quot;disabled&quot;: false,
          }
        }
        accessible={true}
      &gt;
        &lt;View&gt;
          &lt;Text
            accessibilityRole=&quot;text&quot;
          &gt;
            Go Back
          &lt;/Text&gt;
        &lt;/View&gt;
      &lt;/View&gt;
    &lt;/View&gt;
    &lt;Text
      accessibilityRole=&quot;text&quot;
    &gt;
      Error ID: 
      err_1753083967568_qmqa8yksu
    &lt;/Text&gt;
  &lt;/View&gt;
&lt;/View&gt;
    at Object.getByText (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\error\__tests__\ErrorBoundary.test.tsx:224:32)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="ErrorBoundary › Reset Functionality" name="resets on props change when resetOnPropsChange is true" time="0.108">
      <failure>Error: Unable to find an element with text: Something went wrong

&lt;View&gt;
  &lt;View&gt;
    &lt;View&gt;
      &lt;View&gt;
        &lt;Text
          accessibilityRole=&quot;text&quot;
        &gt;
          ⚠️
        &lt;/Text&gt;
      &lt;/View&gt;
      &lt;Text
        accessibilityRole=&quot;header&quot;
      &gt;
        Minor Issue
      &lt;/Text&gt;
      &lt;Text
        accessibilityRole=&quot;text&quot;
      &gt;
        An unexpected error occurred. Please try again.
      &lt;/Text&gt;
      &lt;View&gt;
        &lt;Text
          accessibilityRole=&quot;text&quot;
        &gt;
          Try these solutions:
        &lt;/Text&gt;
        &lt;Text
          accessibilityRole=&quot;text&quot;
        &gt;
          • 
          Try refreshing the page
        &lt;/Text&gt;
        &lt;Text
          accessibilityRole=&quot;text&quot;
        &gt;
          • 
          Restart the app
        &lt;/Text&gt;
        &lt;Text
          accessibilityRole=&quot;text&quot;
        &gt;
          • 
          Contact support if the problem continues
        &lt;/Text&gt;
      &lt;/View&gt;
      &lt;View&gt;
        &lt;View
          accessibilityLabel=&quot;Go Back&quot;
          accessibilityRole=&quot;button&quot;
          accessibilityState={
            {
              &quot;busy&quot;: false,
              &quot;disabled&quot;: false,
            }
          }
          accessible={true}
        &gt;
          &lt;View&gt;
            &lt;Text
              accessibilityRole=&quot;text&quot;
            &gt;
              Go Back
            &lt;/Text&gt;
          &lt;/View&gt;
        &lt;/View&gt;
      &lt;/View&gt;
      &lt;Text
        accessibilityRole=&quot;text&quot;
      &gt;
        Error ID: 
        err_1753083967685_me89eknpy
      &lt;/Text&gt;
    &lt;/View&gt;
  &lt;/View&gt;
  &lt;View
    accessibilityRole=&quot;button&quot;
    accessible={true}
    testID=&quot;change-props&quot;
  &gt;
    &lt;View&gt;
      &lt;Text&gt;
        Change Props
      &lt;/Text&gt;
    &lt;/View&gt;
  &lt;/View&gt;
&lt;/View&gt;
    at Object.getByText (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\error\__tests__\ErrorBoundary.test.tsx:257:21)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="ErrorBoundary › Dynamic Error Handling" name="catches runtime errors from user interactions" time="0.003">
      <failure>TypeError: _reactNative.screen.getByTestID is not a function
    at Object.getByTestID (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\error\__tests__\ErrorBoundary.test.tsx:278:21)
    at Generator.next (&lt;anonymous&gt;)
    at asyncGeneratorStep (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3:17)
    at _next (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17:9)
    at C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14:12)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="ErrorBoundary › Dynamic Error Handling" name="handles nested error boundaries correctly" time="0.005">
    </testcase>
    <testcase classname="ErrorBoundary › Accessibility" name="announces errors to screen readers" time="0.082">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: StringContaining &quot;error occurred&quot;

Number of calls: 0
    at Object.toHaveBeenCalledWith (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\error\__tests__\ErrorBoundary.test.tsx:337:28)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="ErrorBoundary › Accessibility" name="provides proper accessibility props on fallback UI" time="0.085">
      <failure>TypeError: _reactNative.screen.getByTestID is not a function
    at Object.getByTestID (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\src\components\error\__tests__\ErrorBoundary.test.tsx:349:37)
    at Promise.then.completed (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\Files\Services Startup\Workspace\code\vierla-codebase\frontend_v1\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-07-21T07:46:07" time="1.693" tests="7">
    <testcase classname="Test Setup" name="should configure test environment" time="0.003">
    </testcase>
    <testcase classname="useProvidersStore › initial state" name="should have correct initial state" time="0.017">
    </testcase>
    <testcase classname="useProvidersStore › fetchProviders" name="should fetch providers successfully" time="0.816">
    </testcase>
    <testcase classname="useProvidersStore › fetchProviders" name="should handle loading error" time="0.003">
    </testcase>
    <testcase classname="useProvidersStore › updateFilters" name="should update filters correctly" time="0.002">
    </testcase>
    <testcase classname="useProvidersStore › clearFilters" name="should clear all filters" time="0.001">
    </testcase>
    <testcase classname="useProvidersStore › searchProviders" name="should search providers with filters" time="0.615">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-07-21T07:46:09" time="1.148" tests="25">
    <testcase classname="Test Setup" name="should configure test environment" time="0.001">
    </testcase>
    <testcase classname="ErrorHandler › handleError" name="should handle basic errors correctly" time="0.016">
    </testcase>
    <testcase classname="ErrorHandler › handleError" name="should handle errors with context" time="0.007">
    </testcase>
    <testcase classname="ErrorHandler › handleError" name="should preserve AppError properties" time="0.006">
    </testcase>
    <testcase classname="ErrorHandler › handleNetworkError" name="should handle network errors correctly" time="0.007">
    </testcase>
    <testcase classname="ErrorHandler › error type detection" name="should detect network errors" time="0.009">
    </testcase>
    <testcase classname="ErrorHandler › error type detection" name="should detect authentication errors" time="0.007">
    </testcase>
    <testcase classname="ErrorHandler › error type detection" name="should detect authorization errors" time="0.008">
    </testcase>
    <testcase classname="ErrorHandler › error type detection" name="should detect not found errors" time="0.007">
    </testcase>
    <testcase classname="ErrorHandler › error type detection" name="should detect server errors" time="0.009">
    </testcase>
    <testcase classname="ErrorHandler › severity detection" name="should detect critical errors" time="0.008">
    </testcase>
    <testcase classname="ErrorHandler › severity detection" name="should detect high severity errors" time="0.007">
    </testcase>
    <testcase classname="ErrorHandler › severity detection" name="should detect medium severity errors" time="0.006">
    </testcase>
    <testcase classname="ErrorHandler › severity detection" name="should default to low severity" time="0.007">
    </testcase>
    <testcase classname="ErrorHandler › user message generation" name="should generate appropriate network error messages" time="0.009">
    </testcase>
    <testcase classname="ErrorHandler › user message generation" name="should generate appropriate auth error messages" time="0.007">
    </testcase>
    <testcase classname="ErrorHandler › user message generation" name="should generate appropriate authorization error messages" time="0.007">
    </testcase>
    <testcase classname="ErrorHandler › user message generation" name="should generate appropriate not found error messages" time="0.007">
    </testcase>
    <testcase classname="ErrorHandler › user message generation" name="should generate appropriate server error messages" time="0.008">
    </testcase>
    <testcase classname="ErrorHandler › user message generation" name="should generate default error messages" time="0.006">
    </testcase>
    <testcase classname="ErrorHandler › error statistics" name="should track error statistics correctly" time="0.018">
    </testcase>
    <testcase classname="ErrorHandler › error statistics" name="should clear errors correctly" time="0.008">
    </testcase>
    <testcase classname="ErrorHandler › error queue management" name="should maintain queue size limit" time="0.77">
    </testcase>
    <testcase classname="ErrorHandler › error ID generation" name="should generate unique error IDs" time="0.015">
    </testcase>
    <testcase classname="ErrorHandler › error ID generation" name="should generate IDs with correct format" time="0.008">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-07-21T07:46:10" time="0.455" tests="27">
    <testcase classname="Test Setup" name="should configure test environment" time="0.005">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › REC-TEST-001 through REC-TEST-008: Complete Testing Infrastructure" name="should initialize with default configuration" time="0.003">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › REC-TEST-001 through REC-TEST-008: Complete Testing Infrastructure" name="should support custom configuration" time="0.005">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › REC-TEST-001 through REC-TEST-008: Complete Testing Infrastructure" name="should validate default configuration values" time="0.002">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Performance Testing" name="should validate performance metrics" time="0.004">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Performance Testing" name="should calculate performance scores correctly" time="0.007">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Accessibility Testing" name="should validate accessibility for elements with labels" time="0.003">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Accessibility Testing" name="should detect accessibility issues" time="0.003">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Accessibility Testing" name="should calculate accessibility scores" time="0.002">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Code Quality Testing" name="should detect code quality issues" time="0.006">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Code Quality Testing" name="should pass clean code validation" time="0.002">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Code Quality Testing" name="should calculate code quality scores" time="0.001">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Test Data Factories" name="should create user test data" time="0.003">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Test Data Factories" name="should create user test data with overrides" time="0.002">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Test Data Factories" name="should create service test data" time="0.002">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Test Data Factories" name="should create booking test data" time="0.002">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Test Data Factories" name="should create provider test data" time="0.002">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Enhanced Assertions" name="should provide enhanced assertion helpers" time="0.002">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Enhanced Assertions" name="should validate test coverage expectations" time="0.027">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Test Suite Utilities" name="should provide test suite creation utilities" time="0.002">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Test Suite Utilities" name="should create comprehensive test suites" time="0.041">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Quality Reporting" name="should generate quality reports" time="0.003">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Quality Reporting" name="should provide current metrics" time="0.002">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Singleton Instance" name="should provide singleton instance" time="0.002">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Singleton Instance" name="should export utility functions" time="0.005">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Error Handling" name="should handle missing performance object gracefully" time="0.002">
    </testcase>
    <testcase classname="Enhanced Testing and QA System › Error Handling" name="should handle invalid container in accessibility validation" time="0.002">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-07-21T07:46:11" time="0.352" tests="6">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="AnimatedButton › Rendering" name="renders correctly with default props" time="0.084">
    </testcase>
    <testcase classname="AnimatedButton › Rendering" name="renders with different variants" time="0.003">
    </testcase>
    <testcase classname="AnimatedButton › Functionality" name="calls onPress when pressed" time="0.004">
    </testcase>
    <testcase classname="AnimatedButton › Functionality" name="does not call onPress when disabled" time="0.005">
    </testcase>
    <testcase classname="AnimatedButton › Accessibility" name="has proper accessibility properties" time="0.003">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-07-21T07:46:11" time="0.18" tests="17">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus Indicator Thickness" name="should meet WCAG 2.1 AA minimum thickness requirement (2px)" time="0.003">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus Indicator Thickness" name="should use recommended thickness for better visibility" time="0.002">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus Indicator Thickness" name="should enforce minimum thickness even when smaller width is requested" time="0.002">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus Indicator Visibility" name="should have high z-index to prevent obscuring by sticky elements" time="0.002">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus Indicator Visibility" name="should have visible overflow to show focus ring" time="0.002">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus Indicator Visibility" name="should have shadow for enhanced visibility" time="0.002">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Platform-Specific Focus Indicators" name="should provide web-specific outline properties" time="0.002">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Platform-Specific Focus Indicators" name="should provide Android-specific elevation" time="0.002">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus Indicator Colors" name="should support different focus indicator colors" time="0.006">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus Indicator Colors" name="should provide subtle background highlight" time="0.002">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus Indicator Obscuring Prevention" name="should ensure focus is not obscured by sticky elements" time="0.001">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus Indicator Obscuring Prevention" name="should handle empty sticky elements array" time="0.004">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus Indicator Obscuring Prevention" name="should preserve existing high z-index if already sufficient" time="0.001">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus State Management" name="should return empty style when not focused" time="0.002">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › Focus State Management" name="should merge with base styles when focused" time="0.001">
    </testcase>
    <testcase classname="Focus Indicator Validation - WCAG 2.1 AA Compliance › WCAG Standards Compliance" name="should meet all WCAG 2.1 AA focus indicator requirements" time="0.006">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-07-21T07:46:12" time="0.278" tests="24">
    <testcase classname="Test Setup" name="should configure test environment" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › Magic Link Authentication › sendMagicLink" name="should send magic link successfully" time="0.004">
    </testcase>
    <testcase classname="PasswordlessAuthService › Magic Link Authentication › sendMagicLink" name="should reject invalid email format" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › Magic Link Authentication › sendMagicLink" name="should enforce rate limiting" time="0.003">
    </testcase>
    <testcase classname="PasswordlessAuthService › Magic Link Authentication › verifyMagicLink" name="should verify valid magic link" time="0.002">
    </testcase>
    <testcase classname="PasswordlessAuthService › Magic Link Authentication › verifyMagicLink" name="should reject expired magic link" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › Magic Link Authentication › verifyMagicLink" name="should reject already used magic link" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › Magic Link Authentication › verifyMagicLink" name="should reject invalid token" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › SMS OTP Authentication › sendOTP" name="should send OTP successfully" time="0.002">
    </testcase>
    <testcase classname="PasswordlessAuthService › SMS OTP Authentication › sendOTP" name="should reject invalid phone format" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › SMS OTP Authentication › sendOTP" name="should enforce rate limiting for phone numbers" time="0.005">
    </testcase>
    <testcase classname="PasswordlessAuthService › SMS OTP Authentication › verifyOTP" name="should verify correct OTP" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › SMS OTP Authentication › verifyOTP" name="should reject incorrect OTP with attempt tracking" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › SMS OTP Authentication › verifyOTP" name="should reject expired OTP" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › SMS OTP Authentication › verifyOTP" name="should block after max attempts" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › Biometric Authentication › authenticateWithBiometrics" name="should authenticate successfully with biometrics" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › Biometric Authentication › authenticateWithBiometrics" name="should fail when no biometric hardware available" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › Biometric Authentication › authenticateWithBiometrics" name="should fail when no biometric credentials enrolled" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › Biometric Authentication › authenticateWithBiometrics" name="should fail when biometric authentication fails" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › Biometric Authentication › authenticateWithBiometrics" name="should fail when no biometric user data found" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › Biometric Authentication › setupBiometricAuth" name="should setup biometric authentication successfully" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › Biometric Authentication › isBiometricSetup" name="should return true when biometric is setup" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › Biometric Authentication › isBiometricSetup" name="should return false when biometric is not setup" time="0.001">
    </testcase>
    <testcase classname="PasswordlessAuthService › Utility Methods › clearAuthData" name="should clear all authentication data" time="0.002">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-07-21T07:46:12" time="0.196" tests="14">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › Form Field Accessibility Props Generation" name="should generate comprehensive accessibility props for text input" time="0.005">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › Form Field Accessibility Props Generation" name="should generate error-specific accessibility props" time="0.004">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › Form Field Accessibility Props Generation" name="should handle checkbox accessibility props" time="0.002">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › Accessibility Label Generation" name="should generate proper labels for different field types" time="0.003">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › Accessibility Hint Generation" name="should generate appropriate hints for different field types" time="0.003">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › Error Message Accessibility" name="should generate proper error message props" time="0.001">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › Helper Text Accessibility" name="should generate proper helper text props" time="0.001">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › Label Accessibility" name="should generate proper label props" time="0.001">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › Label Accessibility" name="should handle non-required labels" time="0.001">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › Form Accessibility Validation" name="should validate complete form accessibility" time="0.001">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › Form Accessibility Validation" name="should identify accessibility issues" time="0.002">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › Field Type Specific Accessibility" name="should handle different field types correctly" time="0.01">
    </testcase>
    <testcase classname="Form Accessibility Validation - WCAG 2.1 AA Compliance › WCAG 2.1 AA Compliance Summary" name="should meet all form accessibility requirements" time="0.011">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-07-21T07:46:12" time="0.279" tests="48">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="Accessibility Utilities › WCAG_CONSTANTS" name="defines correct contrast ratios" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › WCAG_CONSTANTS" name="defines correct touch target sizes" time="0">
    </testcase>
    <testcase classname="Accessibility Utilities › WCAG_CONSTANTS" name="defines correct animation timing" time="0">
    </testcase>
    <testcase classname="Accessibility Utilities › ScreenReaderUtils › generateFormFieldLabel" name="generates basic label" time="0">
    </testcase>
    <testcase classname="Accessibility Utilities › ScreenReaderUtils › generateFormFieldLabel" name="adds required indicator" time="0">
    </testcase>
    <testcase classname="Accessibility Utilities › ScreenReaderUtils › generateFormFieldLabel" name="adds error message" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › ScreenReaderUtils › generateFormFieldLabel" name="combines required and error" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › ScreenReaderUtils › generateInteractionHint" name="generates basic hint" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › ScreenReaderUtils › generateInteractionHint" name="adds additional info" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › ColorContrastUtils › getRelativeLuminance" name="calculates luminance for white" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › ColorContrastUtils › getRelativeLuminance" name="calculates luminance for black" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › ColorContrastUtils › getRelativeLuminance" name="calculates luminance for gray" time="0">
    </testcase>
    <testcase classname="Accessibility Utilities › ColorContrastUtils › getContrastRatio" name="calculates maximum contrast ratio" time="0">
    </testcase>
    <testcase classname="Accessibility Utilities › ColorContrastUtils › getContrastRatio" name="calculates minimum contrast ratio" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › ColorContrastUtils › getContrastRatio" name="calculates intermediate contrast ratio" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › ColorContrastUtils › meetsWCAGAA" name="passes for high contrast combinations" time="0.003">
    </testcase>
    <testcase classname="Accessibility Utilities › ColorContrastUtils › meetsWCAGAA" name="fails for low contrast combinations" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › ColorContrastUtils › meetsWCAGAA" name="uses different thresholds for large text" time="0">
    </testcase>
    <testcase classname="Accessibility Utilities › ColorContrastUtils › suggestAccessibleColor" name="returns null for already accessible combinations" time="0">
    </testcase>
    <testcase classname="Accessibility Utilities › ColorContrastUtils › suggestAccessibleColor" name="suggests darker color for low contrast" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › SemanticMarkupUtils › generateHeadingProps" name="generates heading props" time="0.002">
    </testcase>
    <testcase classname="Accessibility Utilities › SemanticMarkupUtils › generateHeadingProps" name="handles different heading levels" time="0">
    </testcase>
    <testcase classname="Accessibility Utilities › SemanticMarkupUtils › generateListProps" name="generates list props" time="0">
    </testcase>
    <testcase classname="Accessibility Utilities › SemanticMarkupUtils › generateListItemProps" name="generates list item props" time="0">
    </testcase>
    <testcase classname="Accessibility Utilities › SemanticMarkupUtils › generateButtonProps" name="generates basic button props" time="0">
    </testcase>
    <testcase classname="Accessibility Utilities › SemanticMarkupUtils › generateButtonProps" name="includes action hint" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › SemanticMarkupUtils › generateButtonProps" name="includes state" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › SemanticMarkupUtils › generateInputProps" name="generates basic input props" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › SemanticMarkupUtils › generateInputProps" name="includes value" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › SemanticMarkupUtils › generateInputProps" name="includes required and error info" time="0">
    </testcase>
    <testcase classname="Accessibility Utilities › AccessibilityTestUtils › validateAccessibilityProps" name="passes for well-formed interactive element" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AccessibilityTestUtils › validateAccessibilityProps" name="flags missing accessibility role" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AccessibilityTestUtils › validateAccessibilityProps" name="flags missing accessibility label" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AccessibilityTestUtils › validateAccessibilityProps" name="flags small touch targets" time="0.002">
    </testcase>
    <testcase classname="Accessibility Utilities › AccessibilityTestUtils › validateAccessibilityProps" name="passes for adequate touch targets" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AccessibilityTestUtils › generateAccessibilityReport" name="generates report for component tree" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AccessibilityTestUtils › generateAccessibilityReport" name="handles empty component tree" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AdvancedAccessibilityUtils › announceLiveRegion" name="should announce with polite priority" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AdvancedAccessibilityUtils › announceLiveRegion" name="should announce with assertive priority" time="0">
    </testcase>
    <testcase classname="Accessibility Utilities › AdvancedAccessibilityUtils › announceContentChange" name="should announce content changes" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AdvancedAccessibilityUtils › announceFormValidation" name="should announce valid form field" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AdvancedAccessibilityUtils › announceFormValidation" name="should announce invalid form field" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AdvancedAccessibilityUtils › announceProgress" name="should announce progress updates" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AdvancedAccessibilityUtils › announceLoadingState" name="should announce loading start" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AdvancedAccessibilityUtils › announceLoadingState" name="should announce loading complete" time="0.001">
    </testcase>
    <testcase classname="Accessibility Utilities › AccessibilityMonitoringUtils › trackAccessibilityUsage" name="should track accessibility usage statistics" time="0.012">
    </testcase>
    <testcase classname="Accessibility Utilities › AccessibilityMonitoringUtils › validateCompliance" name="should validate accessibility compliance" time="0.005">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-07-21T07:46:12" time="0.273" tests="23">
    <testcase classname="Test Setup" name="should configure test environment" time="0.001">
    </testcase>
    <testcase classname="Performance Utilities › PerformanceMonitor" name="creates singleton instance" time="0.001">
    </testcase>
    <testcase classname="Performance Utilities › PerformanceMonitor" name="records metrics correctly" time="0.005">
    </testcase>
    <testcase classname="Performance Utilities › PerformanceMonitor" name="gets all metrics when no name specified" time="0.004">
    </testcase>
    <testcase classname="Performance Utilities › PerformanceMonitor" name="generates performance summary" time="0.003">
    </testcase>
    <testcase classname="Performance Utilities › PerformanceMonitor" name="limits metrics to prevent memory leaks" time="0.001">
    </testcase>
    <testcase classname="Performance Utilities › PerformanceMonitor" name="clears metrics" time="0.006">
    </testcase>
    <testcase classname="Performance Utilities › PerformanceTimer" name="measures timing correctly" time="0.002">
    </testcase>
    <testcase classname="Performance Utilities › PerformanceTimer" name="includes tags in metrics" time="0.002">
    </testcase>
    <testcase classname="Performance Utilities › CriticalCSSOptimizer" name="sets and gets critical CSS" time="0.001">
    </testcase>
    <testcase classname="Performance Utilities › CriticalCSSOptimizer" name="loads non-critical CSS asynchronously" time="0.001">
    </testcase>
    <testcase classname="Performance Utilities › CriticalCSSOptimizer" name="preloads critical resources" time="0.001">
    </testcase>
    <testcase classname="Performance Utilities › CodeSplittingUtils" name="imports module with performance tracking" time="0.002">
    </testcase>
    <testcase classname="Performance Utilities › CodeSplittingUtils" name="handles import errors" time="0.011">
    </testcase>
    <testcase classname="Performance Utilities › CodeSplittingUtils" name="preloads chunks" time="0.001">
    </testcase>
    <testcase classname="Performance Utilities › CodeSplittingUtils" name="tracks loaded chunks" time="0.002">
    </testcase>
    <testcase classname="Performance Utilities › MemoryMonitor" name="gets memory usage information" time="0.001">
    </testcase>
    <testcase classname="Performance Utilities › MemoryMonitor" name="returns null when memory API not available" time="0.003">
    </testcase>
    <testcase classname="Performance Utilities › MemoryMonitor" name="starts memory monitoring" time="0.001">
    </testcase>
    <testcase classname="Performance Utilities › Utility Functions" name="measurePerformance creates PerformanceTimer" time="0">
    </testcase>
    <testcase classname="Performance Utilities › Utility Functions" name="withPerformanceTracking wraps synchronous functions" time="0.001">
    </testcase>
    <testcase classname="Performance Utilities › Utility Functions" name="withPerformanceTracking wraps asynchronous functions" time="0.001">
    </testcase>
    <testcase classname="Performance Utilities › Utility Functions" name="withPerformanceTracking handles errors" time="0.003">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-07-21T07:46:13" time="0.235" tests="33">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="TestAccountsService › Test Mode Management" name="should return true for test mode in development" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Test Mode Management" name="should return false for test mode in production" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Test Mode Management" name="should set test mode enabled state" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Test Mode Management" name="should not set test mode in production" time="0">
    </testcase>
    <testcase classname="TestAccountsService › Account Retrieval" name="should return all test accounts" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Account Retrieval" name="should return customer accounts only" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Account Retrieval" name="should return service provider accounts only" time="0.002">
    </testcase>
    <testcase classname="TestAccountsService › Account Retrieval" name="should return providers by category" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Account Retrieval" name="should return random account" time="0.002">
    </testcase>
    <testcase classname="TestAccountsService › Account Retrieval" name="should return random account by role" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Account Retrieval" name="should find account by email" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Account Retrieval" name="should return undefined for non-existent email" time="0">
    </testcase>
    <testcase classname="TestAccountsService › Quick Access Functions" name="should return quick login accounts" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Quick Access Functions" name="should return account statistics" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Login Functionality" name="should successfully login with test account" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Login Functionality" name="should handle login failure" time="0">
    </testcase>
    <testcase classname="TestAccountsService › Login Functionality" name="should not login when test mode is disabled" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Login Functionality" name="should perform quick login" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Login Functionality" name="should login with random account" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Storage Management" name="should store last test account" time="0">
    </testcase>
    <testcase classname="TestAccountsService › Storage Management" name="should retrieve last test account" time="0.002">
    </testcase>
    <testcase classname="TestAccountsService › Storage Management" name="should return null when no last account stored" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Storage Management" name="should clear test account data" time="0">
    </testcase>
    <testcase classname="TestAccountsService › Account Validation" name="should validate correct test account credentials" time="0">
    </testcase>
    <testcase classname="TestAccountsService › Account Validation" name="should return null for invalid credentials" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Account Validation" name="should return null for correct email but wrong password" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Scenario-based Account Selection" name="should return appropriate accounts for booking scenario" time="0.002">
    </testcase>
    <testcase classname="TestAccountsService › Scenario-based Account Selection" name="should return appropriate accounts for messaging scenario" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Scenario-based Account Selection" name="should return appropriate accounts for payments scenario" time="0">
    </testcase>
    <testcase classname="TestAccountsService › Development Helpers" name="should generate test account credentials for UI" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Development Helpers" name="should log test accounts summary in development" time="0.001">
    </testcase>
    <testcase classname="TestAccountsService › Development Helpers" name="should not log in production" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-07-21T07:46:13" time="0.222" tests="18">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Alt-Text Generation" name="should generate appropriate alt-text for different image types" time="0.006">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Alt-Text Generation" name="should use custom description when provided" time="0.002">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Alt-Text Generation" name="should handle missing entity names gracefully" time="0.004">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Accessibility Props Generation" name="should generate comprehensive accessibility props for informative images" time="0.002">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Accessibility Props Generation" name="should generate proper props for functional images" time="0.002">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Accessibility Props Generation" name="should handle decorative images correctly" time="0.002">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Accessibility Props Generation" name="should use custom alt-text when provided" time="0.001">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Accessibility Validation" name="should validate informative images correctly" time="0.004">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Accessibility Validation" name="should identify missing accessibility labels" time="0.001">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Accessibility Validation" name="should warn about overly long accessibility labels" time="0.001">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Accessibility Validation" name="should validate decorative images correctly" time="0.002">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Predefined Image Contexts" name="should provide correct context for app logo" time="0.001">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Predefined Image Contexts" name="should provide correct context for user avatar" time="0.002">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Predefined Image Contexts" name="should provide correct context for functional icons" time="0.002">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Predefined Image Contexts" name="should provide correct context for decorative images" time="0.001">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › Batch Validation" name="should validate multiple images and provide comprehensive results" time="0.003">
    </testcase>
    <testcase classname="Image Accessibility Validation - WCAG 2.1 AA Compliance › WCAG 2.1 AA Compliance Summary" name="should meet all image accessibility requirements" time="0.008">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-07-21T07:46:13" time="0.16" tests="14">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="AuthSlice › Initial State" name="should have correct initial state" time="0.001">
    </testcase>
    <testcase classname="AuthSlice › Login Actions" name="should handle loginStart" time="0.001">
    </testcase>
    <testcase classname="AuthSlice › Login Actions" name="should handle loginSuccess" time="0.001">
    </testcase>
    <testcase classname="AuthSlice › Login Actions" name="should handle loginFailure" time="0.002">
    </testcase>
    <testcase classname="AuthSlice › Registration Actions" name="should handle registerStart" time="0.001">
    </testcase>
    <testcase classname="AuthSlice › Registration Actions" name="should handle registerSuccess" time="0.001">
    </testcase>
    <testcase classname="AuthSlice › Registration Actions" name="should handle registerFailure" time="0.001">
    </testcase>
    <testcase classname="AuthSlice › Profile Management" name="should handle updateProfile" time="0.001">
    </testcase>
    <testcase classname="AuthSlice › Profile Management" name="should handle updateTokens" time="0.001">
    </testcase>
    <testcase classname="AuthSlice › Logout" name="should handle logout" time="0.002">
    </testcase>
    <testcase classname="AuthSlice › Authentication Status Check" name="should load stored authentication data" time="0.001">
    </testcase>
    <testcase classname="AuthSlice › Authentication Status Check" name="should handle missing stored data" time="0.002">
    </testcase>
    <testcase classname="AuthSlice › Authentication Status Check" name="should handle corrupted stored user data" time="0.01">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-07-21T07:46:13" time="0.313" tests="10">
    <testcase classname="Test Setup" name="should configure test environment" time="0.004">
    </testcase>
    <testcase classname="Button Contrast Validation - WCAG 2.1 AA Compliance › Primary Button Contrast" name="should meet WCAG AA standards for primary button text" time="0.01">
    </testcase>
    <testcase classname="Button Contrast Validation - WCAG 2.1 AA Compliance › Primary Button Contrast" name="should have appropriate disabled state contrast" time="0.003">
    </testcase>
    <testcase classname="Button Contrast Validation - WCAG 2.1 AA Compliance › Secondary Button Contrast" name="should meet WCAG AA standards for secondary button text on white background" time="0.003">
    </testcase>
    <testcase classname="Button Contrast Validation - WCAG 2.1 AA Compliance › Secondary Button Contrast" name="should meet WCAG AA standards for secondary button border" time="0.002">
    </testcase>
    <testcase classname="Button Contrast Validation - WCAG 2.1 AA Compliance › Destructive Button Contrast" name="should meet WCAG AA standards for destructive button text" time="0.005">
    </testcase>
    <testcase classname="Button Contrast Validation - WCAG 2.1 AA Compliance › Ghost Button Contrast" name="should meet WCAG AA standards for ghost button text on white background" time="0.003">
    </testcase>
    <testcase classname="Button Contrast Validation - WCAG 2.1 AA Compliance › Focus States Contrast" name="should meet WCAG AA standards for focus indicators" time="0.005">
    </testcase>
    <testcase classname="Button Contrast Validation - WCAG 2.1 AA Compliance › Comprehensive Color Combinations" name="should validate all interactive color combinations" time="0.012">
    </testcase>
    <testcase classname="Button Contrast Validation - WCAG 2.1 AA Compliance › Large Text Contrast" name="should meet WCAG AA standards for large text (18pt+)" time="0.004">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-07-21T07:46:14" time="0.222" tests="10">
    <testcase classname="Test Setup" name="should configure test environment" time="0.003">
    </testcase>
    <testcase classname="AuthService › login" name="should successfully login with valid credentials" time="0.002">
    </testcase>
    <testcase classname="AuthService › login" name="should handle login failure with error message" time="0.01">
    </testcase>
    <testcase classname="AuthService › login" name="should handle network errors" time="0.005">
    </testcase>
    <testcase classname="AuthService › register" name="should successfully register a new customer" time="0.001">
    </testcase>
    <testcase classname="AuthService › register" name="should successfully register a new service provider" time="0.001">
    </testcase>
    <testcase classname="AuthService › register" name="should handle registration validation errors" time="0.001">
    </testcase>
    <testcase classname="AuthService › refreshToken" name="should successfully refresh access token" time="0.001">
    </testcase>
    <testcase classname="AuthService › logout" name="should successfully logout" time="0.001">
    </testcase>
    <testcase classname="AuthService › logout" name="should handle logout errors gracefully" time="0.005">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-07-21T07:46:14" time="0.234" tests="20">
    <testcase classname="Test Setup" name="should configure test environment" time="0.003">
    </testcase>
    <testcase classname="Contrast Enhancer › enhancePrimaryCTAContrast" name="should validate compliant colors" time="0.001">
    </testcase>
    <testcase classname="Contrast Enhancer › enhancePrimaryCTAContrast" name="should enhance non-compliant colors" time="0.002">
    </testcase>
    <testcase classname="Contrast Enhancer › enhancePrimaryCTAContrast" name="should identify optimal colors (AAA)" time="0.002">
    </testcase>
    <testcase classname="Contrast Enhancer › validateInteractiveColors" name="should validate all interactive colors" time="0.001">
    </testcase>
    <testcase classname="Contrast Enhancer › generateContrastReport" name="should generate comprehensive contrast report" time="0.002">
    </testcase>
    <testcase classname="Contrast Enhancer › generateContrastReport" name="should have high compliance rate" time="0.001">
    </testcase>
    <testcase classname="Contrast Enhancer › getBestTextColor" name="should return white for dark backgrounds" time="0">
    </testcase>
    <testcase classname="Contrast Enhancer › getBestTextColor" name="should return black for light backgrounds" time="0">
    </testcase>
    <testcase classname="Contrast Enhancer › getBestTextColor" name="should return appropriate color for sage green" time="0.001">
    </testcase>
    <testcase classname="Contrast Enhancer › ensureMinimumContrast" name="should return original color if contrast is sufficient" time="0.001">
    </testcase>
    <testcase classname="Contrast Enhancer › ensureMinimumContrast" name="should return high contrast alternative if insufficient" time="0.001">
    </testcase>
    <testcase classname="Contrast Enhancer › EnhancedCTAColors" name="should have all required color constants" time="0.001">
    </testcase>
    <testcase classname="Contrast Enhancer › EnhancedCTAColors" name="should have WCAG compliant primary colors" time="0">
    </testcase>
    <testcase classname="Contrast Enhancer › EnhancedCTAColors" name="should have WCAG compliant secondary colors" time="0">
    </testcase>
    <testcase classname="Contrast Enhancer › Color contrast ratios" name="should meet WCAG AA standards for Primary CTA" time="0.001">
    </testcase>
    <testcase classname="Contrast Enhancer › Color contrast ratios" name="should meet WCAG AA standards for Primary CTA Hover" time="0.003">
    </testcase>
    <testcase classname="Contrast Enhancer › Color contrast ratios" name="should meet WCAG AA standards for Primary CTA Pressed" time="0.001">
    </testcase>
    <testcase classname="Contrast Enhancer › Color contrast ratios" name="should meet WCAG AA standards for Secondary Button" time="0.001">
    </testcase>
    <testcase classname="Contrast Enhancer › Color contrast ratios" name="should meet WCAG AA standards for Error Button" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-07-21T07:46:14" time="0.311" tests="16">
    <testcase classname="Test Setup" name="should configure test environment" time="0.003">
    </testcase>
    <testcase classname="Enhanced Form Validation System › FormValidator Class" name="should validate individual fields correctly" time="0.002">
    </testcase>
    <testcase classname="Enhanced Form Validation System › FormValidator Class" name="should detect invalid fields" time="0.001">
    </testcase>
    <testcase classname="Enhanced Form Validation System › FormValidator Class" name="should validate entire form" time="0.002">
    </testcase>
    <testcase classname="Enhanced Form Validation System › FormValidator Class" name="should detect form validation errors" time="0.002">
    </testcase>
    <testcase classname="Enhanced Form Validation System › Service Form Validation" name="should validate service name correctly" time="0.002">
    </testcase>
    <testcase classname="Enhanced Form Validation System › Service Form Validation" name="should validate service description" time="0.002">
    </testcase>
    <testcase classname="Enhanced Form Validation System › Service Form Validation" name="should validate price correctly" time="0.002">
    </testcase>
    <testcase classname="Enhanced Form Validation System › Service Form Validation" name="should validate duration correctly" time="0.002">
    </testcase>
    <testcase classname="Enhanced Form Validation System › Profile Form Validation" name="should validate profile data correctly" time="0.002">
    </testcase>
    <testcase classname="Enhanced Form Validation System › Profile Form Validation" name="should handle optional fields correctly" time="0.001">
    </testcase>
    <testcase classname="Enhanced Form Validation System › Profile Form Validation" name="should validate date of birth format" time="0.002">
    </testcase>
    <testcase classname="Enhanced Form Validation System › Real-time Validation Scenarios" name="should provide immediate feedback for email validation" time="0.002">
    </testcase>
    <testcase classname="Enhanced Form Validation System › Real-time Validation Scenarios" name="should handle password confirmation validation" time="0.004">
    </testcase>
    <testcase classname="Enhanced Form Validation System › Error Message Quality" name="should provide clear, actionable error messages" time="0.002">
    </testcase>
    <testcase classname="Enhanced Form Validation System › Error Message Quality" name="should provide specific validation feedback" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-07-21T07:46:15" time="0.143" tests="8">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="WCAG_STANDARDS Fix" name="should have correct TOUCH_TARGETS structure" time="0.001">
    </testcase>
    <testcase classname="WCAG_STANDARDS Fix" name="should have backward compatibility TARGET_SIZE alias" time="0.001">
    </testcase>
    <testcase classname="WCAG_STANDARDS Fix" name="should have correct FOCUS_INDICATORS structure" time="0.001">
    </testcase>
    <testcase classname="WCAG_STANDARDS Fix" name="should have correct CONTRAST_RATIOS structure" time="0.003">
    </testcase>
    <testcase classname="WCAG_STANDARDS Fix" name="should be frozen to prevent modification" time="0.001">
    </testcase>
    <testcase classname="WCAG_STANDARDS Fix" name="should not throw runtime errors when accessing properties" time="0.001">
    </testcase>
    <testcase classname="WCAG_STANDARDS Fix" name="should maintain consistency between TOUCH_TARGETS.MINIMUM_SIZE and TARGET_SIZE.MINIMUM" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="0" timestamp="2025-07-21T07:46:15" time="0.113" tests="6">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="Navigation Types › Type Definitions" name="should define RootStackParamList correctly" time="0">
    </testcase>
    <testcase classname="Navigation Types › Type Definitions" name="should define AuthStackParamList correctly" time="0.001">
    </testcase>
    <testcase classname="Navigation Types › Type Definitions" name="should define CustomerTabParamList correctly" time="0.001">
    </testcase>
    <testcase classname="Navigation Types › Type Definitions" name="should define ProviderTabParamList correctly" time="0.003">
    </testcase>
    <testcase classname="Navigation Types › Type Safety" name="should enforce correct parameter types" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Test Setup" errors="0" failures="0" skipped="3" timestamp="2025-07-21T07:46:15" time="0.12" tests="4">
    <testcase classname="Test Setup" name="should configure test environment" time="0.002">
    </testcase>
    <testcase classname="AuthService Integration Tests › login integration" name="should successfully login with test credentials" time="0">
      <skipped/>
    </testcase>
    <testcase classname="AuthService Integration Tests › login integration" name="should fail with invalid credentials" time="0">
      <skipped/>
    </testcase>
    <testcase classname="AuthService Integration Tests › register integration" name="should handle registration attempt" time="0">
      <skipped/>
    </testcase>
  </testsuite>
</testsuites>