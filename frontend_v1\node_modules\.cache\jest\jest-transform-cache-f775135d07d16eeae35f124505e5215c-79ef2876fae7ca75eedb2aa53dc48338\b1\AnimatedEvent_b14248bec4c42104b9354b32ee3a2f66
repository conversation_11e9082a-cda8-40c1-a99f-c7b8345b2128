ba52addb62bffe82ad2e47c66ea9d687
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.AnimatedEvent = void 0;
exports.attachNativeEvent = attachNativeEvent;
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _NativeAnimatedHelper = _interopRequireDefault(require("../../src/private/animated/NativeAnimatedHelper"));
var _RendererProxy = require("../ReactNative/RendererProxy");
var _AnimatedValue = _interopRequireDefault(require("./nodes/AnimatedValue"));
var _AnimatedValueXY = _interopRequireDefault(require("./nodes/AnimatedValueXY"));
var _invariant = _interopRequireDefault(require("invariant"));
function attachNativeEvent(viewRef, eventName, argMapping, platformConfig) {
  var eventMappings = [];
  var _traverse = function traverse(value, path) {
    if (value instanceof _AnimatedValue.default) {
      value.__makeNative(platformConfig);
      eventMappings.push({
        nativeEventPath: path,
        animatedValueTag: value.__getNativeTag()
      });
    } else if (value instanceof _AnimatedValueXY.default) {
      _traverse(value.x, path.concat('x'));
      _traverse(value.y, path.concat('y'));
    } else if (typeof value === 'object') {
      for (var _key in value) {
        _traverse(value[_key], path.concat(_key));
      }
    }
  };
  (0, _invariant.default)(argMapping[0] && argMapping[0].nativeEvent, 'Native driven events only support animated values contained inside `nativeEvent`.');
  _traverse(argMapping[0].nativeEvent, []);
  var viewTag = (0, _RendererProxy.findNodeHandle)(viewRef);
  if (viewTag != null) {
    eventMappings.forEach(function (mapping) {
      _NativeAnimatedHelper.default.API.addAnimatedEventToView(viewTag, eventName, mapping);
    });
  }
  return {
    detach: function detach() {
      if (viewTag != null) {
        eventMappings.forEach(function (mapping) {
          _NativeAnimatedHelper.default.API.removeAnimatedEventFromView(viewTag, eventName, mapping.animatedValueTag);
        });
      }
    }
  };
}
function validateMapping(argMapping, args) {
  var _validate = function validate(recMapping, recEvt, key) {
    if (recMapping instanceof _AnimatedValue.default) {
      (0, _invariant.default)(typeof recEvt === 'number', 'Bad mapping of event key ' + key + ', should be number but got ' + typeof recEvt);
      return;
    }
    if (recMapping instanceof _AnimatedValueXY.default) {
      (0, _invariant.default)(typeof recEvt.x === 'number' && typeof recEvt.y === 'number', 'Bad mapping of event key ' + key + ', should be XY but got ' + recEvt);
      return;
    }
    if (typeof recEvt === 'number') {
      (0, _invariant.default)(recMapping instanceof _AnimatedValue.default, 'Bad mapping of type ' + typeof recMapping + ' for key ' + key + ', event value must map to AnimatedValue');
      return;
    }
    (0, _invariant.default)(typeof recMapping === 'object', 'Bad mapping of type ' + typeof recMapping + ' for key ' + key);
    (0, _invariant.default)(typeof recEvt === 'object', 'Bad event of type ' + typeof recEvt + ' for key ' + key);
    for (var mappingKey in recMapping) {
      _validate(recMapping[mappingKey], recEvt[mappingKey], mappingKey);
    }
  };
  (0, _invariant.default)(args.length >= argMapping.length, 'Event has less arguments than mapping');
  argMapping.forEach(function (mapping, idx) {
    _validate(mapping, args[idx], 'arg' + idx);
  });
}
var AnimatedEvent = exports.AnimatedEvent = function () {
  function AnimatedEvent(argMapping, config) {
    var _this = this;
    (0, _classCallCheck2.default)(this, AnimatedEvent);
    this._listeners = [];
    this._callListeners = function () {
      for (var _len = arguments.length, args = new Array(_len), _key2 = 0; _key2 < _len; _key2++) {
        args[_key2] = arguments[_key2];
      }
      _this._listeners.forEach(function (listener) {
        return listener.apply(void 0, args);
      });
    };
    this._argMapping = argMapping;
    if (config == null) {
      console.warn('Animated.event now requires a second argument for options');
      config = {
        useNativeDriver: false
      };
    }
    if (config.listener) {
      this.__addListener(config.listener);
    }
    this._attachedEvent = null;
    this.__isNative = _NativeAnimatedHelper.default.shouldUseNativeDriver(config);
    this.__platformConfig = config.platformConfig;
  }
  return (0, _createClass2.default)(AnimatedEvent, [{
    key: "__addListener",
    value: function __addListener(callback) {
      this._listeners.push(callback);
    }
  }, {
    key: "__removeListener",
    value: function __removeListener(callback) {
      this._listeners = this._listeners.filter(function (listener) {
        return listener !== callback;
      });
    }
  }, {
    key: "__attach",
    value: function __attach(viewRef, eventName) {
      (0, _invariant.default)(this.__isNative, 'Only native driven events need to be attached.');
      this._attachedEvent = attachNativeEvent(viewRef, eventName, this._argMapping, this.__platformConfig);
    }
  }, {
    key: "__detach",
    value: function __detach(viewTag, eventName) {
      (0, _invariant.default)(this.__isNative, 'Only native driven events need to be detached.');
      this._attachedEvent && this._attachedEvent.detach();
    }
  }, {
    key: "__getHandler",
    value: function __getHandler() {
      var _this2 = this;
      if (this.__isNative) {
        if (__DEV__) {
          var _validatedMapping = false;
          return function () {
            for (var _len2 = arguments.length, args = new Array(_len2), _key3 = 0; _key3 < _len2; _key3++) {
              args[_key3] = arguments[_key3];
            }
            if (!_validatedMapping) {
              validateMapping(_this2._argMapping, args);
              _validatedMapping = true;
            }
            _this2._callListeners.apply(_this2, args);
          };
        } else {
          return this._callListeners;
        }
      }
      var validatedMapping = false;
      return function () {
        for (var _len3 = arguments.length, args = new Array(_len3), _key4 = 0; _key4 < _len3; _key4++) {
          args[_key4] = arguments[_key4];
        }
        if (__DEV__ && !validatedMapping) {
          validateMapping(_this2._argMapping, args);
          validatedMapping = true;
        }
        var _traverse2 = function traverse(recMapping, recEvt) {
          if (recMapping instanceof _AnimatedValue.default) {
            if (typeof recEvt === 'number') {
              recMapping.setValue(recEvt);
            }
          } else if (recMapping instanceof _AnimatedValueXY.default) {
            if (typeof recEvt === 'object') {
              _traverse2(recMapping.x, recEvt.x);
              _traverse2(recMapping.y, recEvt.y);
            }
          } else if (typeof recMapping === 'object') {
            for (var mappingKey in recMapping) {
              _traverse2(recMapping[mappingKey], recEvt[mappingKey]);
            }
          }
        };
        _this2._argMapping.forEach(function (mapping, idx) {
          _traverse2(mapping, args[idx]);
        });
        _this2._callListeners.apply(_this2, args);
      };
    }
  }]);
}();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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