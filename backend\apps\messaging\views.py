from django.shortcuts import render
from django.db.models import Q, Count, Max
from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.contrib.auth import get_user_model

from .models import Conversation, Message, MessageAttachment
from .serializers import (
    ConversationSerializer, ConversationCreateSerializer,
    MessageSerializer, MessageCreateSerializer,
    MessageAttachmentSerializer
)

User = get_user_model()


class ConversationViewSet(viewsets.ModelViewSet):
    """ViewSet for managing conversations"""
    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        if self.action == 'create':
            return ConversationCreateSerializer
        return ConversationSerializer

    def get_queryset(self):
        """Get conversations for the authenticated user"""
        return Conversation.objects.filter(
            participants=self.request.user,
            is_active=True
        ).annotate(
            unread_count=Count(
                'messages',
                filter=Q(messages__is_read=False) & ~Q(
                    messages__sender=self.request.user)
            ),
            last_message_time=Max('messages__created_at')
        ).order_by('-updated_at').distinct()

    def create(self, request):
        """Create a new conversation"""
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            conversation = serializer.save()
            response_serializer = ConversationSerializer(
                conversation, context={'request': request})
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['get'])
    def messages(self, request, pk=None):
        """Get messages for a conversation"""
        conversation = self.get_object()

        # Check if user is participant
        if not conversation.participants.filter(id=request.user.id).exists():
            return Response(
                {'error': 'You are not a participant in this conversation'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Get pagination parameters
        page = int(request.query_params.get('page', 1))
        limit = int(request.query_params.get('limit', 50))
        offset = (page - 1) * limit

        messages = conversation.messages.select_related(
            'sender', 'reply_to'
        ).prefetch_related('attachments').order_by('-created_at')[offset:offset + limit]

        # Reverse to show oldest first
        messages = list(reversed(messages))

        serializer = MessageSerializer(messages, many=True)
        return Response({
            'messages': serializer.data,
            'page': page,
            'limit': limit,
            'total_count': conversation.messages.count()
        })

    @action(detail=True, methods=['post'])
    def send_message(self, request, pk=None):
        """Send a message in a conversation"""
        conversation = self.get_object()

        # Check if user is participant
        if not conversation.participants.filter(id=request.user.id).exists():
            return Response(
                {'error': 'You are not a participant in this conversation'},
                status=status.HTTP_403_FORBIDDEN
            )

        serializer = MessageCreateSerializer(data=request.data)
        if serializer.is_valid():
            # Create message
            message = Message.objects.create(
                conversation=conversation,
                sender=request.user,
                content=serializer.validated_data['content'],
                message_type=serializer.validated_data.get('message_type', 'text'),
                reply_to=serializer.validated_data.get('reply_to'),
                metadata=serializer.validated_data.get('metadata', {}),
                delivery_status='delivered'
            )

            # Handle attachments if any
            attachments = request.FILES.getlist('attachments')
            for attachment in attachments:
                MessageAttachment.objects.create(
                    message=message,
                    file=attachment,
                    file_name=attachment.name,
                    file_size=attachment.size,
                    file_type=attachment.name.split('.')[-1] if '.' in attachment.name else 'unknown',
                    mime_type=getattr(attachment, 'content_type', 'application/octet-stream')
                )

            # TODO: Send real-time notification via WebSocket
            # self.send_realtime_message(conversation, message)

            response_serializer = MessageSerializer(message)
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def mark_as_read(self, request, pk=None):
        """Mark messages as read in a conversation"""
        conversation = self.get_object()

        # Check if user is participant
        if not conversation.participants.filter(id=request.user.id).exists():
            return Response(
                {'error': 'You are not a participant in this conversation'},
                status=status.HTTP_403_FORBIDDEN
            )

        message_ids = request.data.get('message_ids', [])
        if message_ids:
            # Mark specific messages as read
            messages = conversation.messages.filter(
                id__in=message_ids,
                is_read=False
            ).exclude(sender=request.user)
        else:
            # Mark all unread messages as read
            messages = conversation.messages.filter(
                is_read=False
            ).exclude(sender=request.user)

        updated_count = 0
        for message in messages:
            message.mark_as_read()
            updated_count += 1

        return Response({
            'message': f'Marked {updated_count} messages as read',
            'updated_count': updated_count
        })


class MessageViewSet(viewsets.ModelViewSet):
    """ViewSet for managing messages"""
    serializer_class = MessageSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Get messages for conversations the user participates in"""
        return Message.objects.filter(
            conversation__participants=self.request.user
        ).select_related('sender', 'conversation', 'reply_to').prefetch_related('attachments')

    def perform_create(self, serializer):
        """Set sender when creating a message"""
        serializer.save(sender=self.request.user)
