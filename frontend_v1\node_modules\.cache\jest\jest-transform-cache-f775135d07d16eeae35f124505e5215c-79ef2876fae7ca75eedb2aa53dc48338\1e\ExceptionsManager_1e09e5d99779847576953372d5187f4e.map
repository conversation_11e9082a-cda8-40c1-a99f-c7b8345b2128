{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "default", "SyntheticError", "_createClass2", "_classCallCheck2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_inherits2", "_wrapNativeSuper2", "_callSuper", "t", "o", "e", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_Error", "_this", "_len", "arguments", "length", "args", "Array", "_key", "concat", "name", "Error", "userExceptionDecorator", "inUserExceptionDecorator", "decoratedExtraDataKey", "unstable_setExceptionDecorator", "exceptionDecorator", "preprocessException", "data", "_unused", "exceptionID", "reportException", "isFatal", "reportToConsole", "parseError<PERSON>tack", "stack", "currentExceptionID", "originalMessage", "message", "componentStack", "namePrefix", "startsWith", "jsEngine", "extraData", "assign", "rawStack", "cause", "stackSymbols", "stackReturnAddresses", "stackElements", "id", "console", "error", "__DEV__", "LogBox", "addException", "isComponentError", "type", "NativeExceptionsManager", "global", "RN$hasHandledFatalException", "RN$notifyOfFatalException", "inExceptionHandler", "handleException", "RN$handleException", "reactConsoleErrorHandler", "_console", "_len2", "_key2", "_errorOriginal", "reportErrorsAsExceptions", "RN$inExceptionHandler", "firstArg", "stringifySafe", "map", "arg", "join", "installConsoleErrorReporter", "bind", "undefined", "ExceptionsManager", "_default"], "sources": ["ExceptionsManager.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict\n */\n\n'use strict';\n\nimport type {ExtendedError} from './ExtendedError';\nimport type {ExceptionData} from './NativeExceptionsManager';\n\nexport class SyntheticError extends Error {\n  name: string = '';\n}\n\ntype ExceptionDecorator = ExceptionData => ExceptionData;\n\nlet userExceptionDecorator: ?ExceptionDecorator;\nlet inUserExceptionDecorator = false;\n\n// This string is used to decorate an ExtendedError with extra data in select usecases.\n// Note that data passed using this method should be strictly contained,\n// as data that's not serializable/too large may cause issues with passing the error to the native code.\n// TODO(T204185517): We should use a Symbol for this, but jsi through jsc doesn't support it yet.\nconst decoratedExtraDataKey = 'RN$ErrorExtraDataKey';\n\n/**\n * Allows the app to add information to the exception report before it is sent\n * to native. This API is not final.\n */\n\nfunction unstable_setExceptionDecorator(\n  exceptionDecorator: ?ExceptionDecorator,\n) {\n  userExceptionDecorator = exceptionDecorator;\n}\n\nfunction preprocessException(data: ExceptionData): ExceptionData {\n  if (userExceptionDecorator && !inUserExceptionDecorator) {\n    inUserExceptionDecorator = true;\n    try {\n      return userExceptionDecorator(data);\n    } catch {\n      // Fall through\n    } finally {\n      inUserExceptionDecorator = false;\n    }\n  }\n  return data;\n}\n\n/**\n * Handles the developer-visible aspect of errors and exceptions\n */\nlet exceptionID = 0;\nfunction reportException(\n  e: ExtendedError,\n  isFatal: boolean,\n  reportToConsole: boolean, // only true when coming from handleException; the error has not yet been logged\n) {\n  const parseErrorStack = require('./Devtools/parseErrorStack').default;\n  const stack = parseErrorStack(e?.stack);\n  const currentExceptionID = ++exceptionID;\n  const originalMessage = e.message || '';\n  let message = originalMessage;\n  if (e.componentStack != null) {\n    message += `\\n\\nThis error is located at:${e.componentStack}`;\n  }\n  const namePrefix = e.name == null || e.name === '' ? '' : `${e.name}: `;\n\n  if (!message.startsWith(namePrefix)) {\n    message = namePrefix + message;\n  }\n\n  message =\n    e.jsEngine == null ? message : `${message}, js engine: ${e.jsEngine}`;\n\n  // $FlowFixMe[unclear-type]\n  const extraData: Object = {\n    // $FlowFixMe[incompatible-use] we can't define a type with a Symbol-keyed field in flow\n    ...e[decoratedExtraDataKey],\n    jsEngine: e.jsEngine,\n    rawStack: e.stack,\n  };\n  if (e.cause != null && typeof e.cause === 'object') {\n    extraData.stackSymbols = e.cause.stackSymbols;\n    extraData.stackReturnAddresses = e.cause.stackReturnAddresses;\n    extraData.stackElements = e.cause.stackElements;\n  }\n\n  const data = preprocessException({\n    message,\n    originalMessage: message === originalMessage ? null : originalMessage,\n    name: e.name == null || e.name === '' ? null : e.name,\n    componentStack:\n      typeof e.componentStack === 'string' ? e.componentStack : null,\n    stack,\n    id: currentExceptionID,\n    isFatal,\n    extraData,\n  });\n\n  if (reportToConsole) {\n    // we feed back into console.error, to make sure any methods that are\n    // monkey patched on top of console.error are called when coming from\n    // handleException\n    console.error(data.message);\n  }\n\n  if (__DEV__) {\n    const LogBox = require('../LogBox/LogBox').default;\n    LogBox.addException({\n      ...data,\n      isComponentError: !!e.isComponentError,\n    });\n  } else if (isFatal || e.type !== 'warn') {\n    const NativeExceptionsManager =\n      require('./NativeExceptionsManager').default;\n    if (NativeExceptionsManager) {\n      if (isFatal) {\n        if (global.RN$hasHandledFatalException?.()) {\n          return;\n        }\n        global.RN$notifyOfFatalException?.();\n      }\n      NativeExceptionsManager.reportException(data);\n    }\n  }\n}\n\ndeclare var console: {\n  error: (...data: $ReadOnlyArray<mixed>) => void,\n  _errorOriginal: (...data: $ReadOnlyArray<mixed>) => void,\n  reportErrorsAsExceptions: boolean,\n  ...\n};\n\n// If we trigger console.error _from_ handleException,\n// we do want to make sure that console.error doesn't trigger error reporting again\nlet inExceptionHandler = false;\n\n/**\n * Logs exceptions to the (native) console and displays them\n */\nfunction handleException(e: mixed, isFatal: boolean) {\n  // TODO(T196834299): We should really use a c++ turbomodule for this\n  const reportToConsole = true;\n  if (\n    !global.RN$handleException ||\n    !global.RN$handleException(e, isFatal, reportToConsole)\n  ) {\n    let error: Error;\n    if (e instanceof Error) {\n      error = e;\n    } else {\n      // Workaround for reporting errors caused by `throw 'some string'`\n      // Unfortunately there is no way to figure out the stacktrace in this\n      // case, so if you ended up here trying to trace an error, look for\n      // `throw '<error message>'` somewhere in your codebase.\n      error = new SyntheticError(e);\n    }\n    try {\n      inExceptionHandler = true;\n      /* $FlowFixMe[class-object-subtyping] added when improving typing for this\n       * parameters */\n      // $FlowFixMe[incompatible-call]\n      reportException(error, isFatal, reportToConsole);\n    } finally {\n      inExceptionHandler = false;\n    }\n  }\n}\n\n/* $FlowFixMe[missing-local-annot] The type annotation(s) required by Flow's\n * LTI update could not be added via codemod */\nfunction reactConsoleErrorHandler(...args) {\n  // bubble up to any original handlers\n  console._errorOriginal(...args);\n  if (!console.reportErrorsAsExceptions) {\n    return;\n  }\n  if (inExceptionHandler || global.RN$inExceptionHandler?.()) {\n    // The fundamental trick here is that are multiple entry point to logging errors:\n    // (see ********* for more background)\n    //\n    // 1. An uncaught exception being caught by the global handler\n    // 2. An error being logged throw console.error\n    //\n    // However, console.error is monkey patched multiple times: by this module, and by the\n    // DevTools setup that sends messages to Metro.\n    // The patching order cannot be relied upon.\n    //\n    // So, some scenarios that are handled by this flag:\n    //\n    // Logging an error:\n    // 1. console.error called from user code\n    // 2. (possibly) arrives _first_ at DevTool handler, send to Metro\n    // 3. Bubbles to here\n    // 4. goes into report Exception.\n    // 5. should not trigger console.error again, to avoid looping / logging twice\n    // 6. should still bubble up to original console\n    //    (which might either be console.log, or the DevTools handler in case it patched _earlier_ and (2) didn't happen)\n    //\n    // Throwing an uncaught exception:\n    // 1. exception thrown\n    // 2. picked up by handleException\n    // 3. should be sent to console.error (not console._errorOriginal, as DevTools might have patched _later_ and it needs to send it to Metro)\n    // 4. that _might_ bubble again to the `reactConsoleErrorHandle` defined here\n    //    -> should not handle exception _again_, to avoid looping / showing twice (this code branch)\n    // 5. should still bubble up to original console (which might either be console.log, or the DevTools handler in case that one patched _earlier_)\n    return;\n  }\n\n  let error;\n\n  const firstArg = args[0];\n  if (firstArg?.stack) {\n    // reportException will console.error this with high enough fidelity.\n    error = firstArg;\n  } else {\n    const stringifySafe = require('../Utilities/stringifySafe').default;\n    if (typeof firstArg === 'string' && firstArg.startsWith('Warning: ')) {\n      // React warnings use console.error so that a stack trace is shown, but\n      // we don't (currently) want these to show a redbox\n      // (Note: Logic duplicated in polyfills/console.js.)\n      return;\n    }\n    const message = args\n      .map(arg => (typeof arg === 'string' ? arg : stringifySafe(arg)))\n      .join(' ');\n\n    error = new SyntheticError(message);\n    error.name = 'console.error';\n  }\n\n  const isFatal = false;\n  const reportToConsole = false;\n  if (\n    !global.RN$handleException ||\n    !global.RN$handleException(error, isFatal, reportToConsole)\n  ) {\n    reportException(\n      /* $FlowFixMe[class-object-subtyping] added when improving typing for this\n       * parameters */\n      // $FlowFixMe[incompatible-call]\n      error,\n      isFatal,\n      reportToConsole,\n    );\n  }\n}\n\n/**\n * Shows a redbox with stacktrace for all console.error messages.  Disable by\n * setting `console.reportErrorsAsExceptions = false;` in your app.\n */\nfunction installConsoleErrorReporter() {\n  // Enable reportErrorsAsExceptions\n  if (console._errorOriginal) {\n    return; // already installed\n  }\n  // Flow doesn't like it when you set arbitrary values on a global object\n  console._errorOriginal = console.error.bind(console);\n  console.error = reactConsoleErrorHandler;\n  if (console.reportErrorsAsExceptions === undefined) {\n    // Individual apps can disable this\n    // Flow doesn't like it when you set arbitrary values on a global object\n    console.reportErrorsAsExceptions = true;\n  }\n}\n\nconst ExceptionsManager = {\n  decoratedExtraDataKey,\n  handleException,\n  installConsoleErrorReporter,\n  SyntheticError, // <- for backwards compatibility\n  unstable_setExceptionDecorator,\n};\n\nexport default ExceptionsManager;\n"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA,GAAAF,OAAA,CAAAG,cAAA;AAAA,IAAAC,aAAA,GAAAR,sBAAA,CAAAC,OAAA;AAAA,IAAAQ,gBAAA,GAAAT,sBAAA,CAAAC,OAAA;AAAA,IAAAS,2BAAA,GAAAV,sBAAA,CAAAC,OAAA;AAAA,IAAAU,gBAAA,GAAAX,sBAAA,CAAAC,OAAA;AAAA,IAAAW,UAAA,GAAAZ,sBAAA,CAAAC,OAAA;AAAA,IAAAY,iBAAA,GAAAb,sBAAA,CAAAC,OAAA;AAAA,SAAAa,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAL,gBAAA,CAAAL,OAAA,EAAAU,CAAA,OAAAN,2BAAA,CAAAJ,OAAA,EAAAS,CAAA,EAAAG,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAJ,CAAA,EAAAC,CAAA,YAAAN,gBAAA,CAAAL,OAAA,EAAAS,CAAA,EAAAM,WAAA,IAAAL,CAAA,CAAAM,KAAA,CAAAP,CAAA,EAAAE,CAAA;AAAA,SAAAC,0BAAA,cAAAH,CAAA,IAAAQ,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAR,CAAA,aAAAG,yBAAA,YAAAA,0BAAA,aAAAH,CAAA;AAAA,IAKAR,cAAc,GAAAH,OAAA,CAAAG,cAAA,aAAAoB,MAAA;EAAA,SAAApB,eAAA;IAAA,IAAAqB,KAAA;IAAA,IAAAnB,gBAAA,CAAAH,OAAA,QAAAC,cAAA;IAAA,SAAAsB,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAAF,IAAA,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IAAAN,KAAA,GAAAd,UAAA,OAAAP,cAAA,KAAA4B,MAAA,CAAAH,IAAA;IAAAJ,KAAA,CACzBQ,IAAI,GAAW,EAAE;IAAA,OAAAR,KAAA;EAAA;EAAA,IAAAhB,UAAA,CAAAN,OAAA,EAAAC,cAAA,EAAAoB,MAAA;EAAA,WAAAnB,aAAA,CAAAF,OAAA,EAAAC,cAAA;AAAA,MAAAM,iBAAA,CAAAP,OAAA,EADiB+B,KAAK;AAMzC,IAAIC,sBAA2C;AAC/C,IAAIC,wBAAwB,GAAG,KAAK;AAMpC,IAAMC,qBAAqB,GAAG,sBAAsB;AAOpD,SAASC,8BAA8BA,CACrCC,kBAAuC,EACvC;EACAJ,sBAAsB,GAAGI,kBAAkB;AAC7C;AAEA,SAASC,mBAAmBA,CAACC,IAAmB,EAAiB;EAC/D,IAAIN,sBAAsB,IAAI,CAACC,wBAAwB,EAAE;IACvDA,wBAAwB,GAAG,IAAI;IAC/B,IAAI;MACF,OAAOD,sBAAsB,CAACM,IAAI,CAAC;IACrC,CAAC,CAAC,OAAAC,OAAA,EAAM,CAER,CAAC,SAAS;MACRN,wBAAwB,GAAG,KAAK;IAClC;EACF;EACA,OAAOK,IAAI;AACb;AAKA,IAAIE,WAAW,GAAG,CAAC;AACnB,SAASC,eAAeA,CACtB9B,CAAgB,EAChB+B,OAAgB,EAChBC,eAAwB,EACxB;EACA,IAAMC,eAAe,GAAGjD,OAAO,6BAA6B,CAAC,CAACK,OAAO;EACrE,IAAM6C,KAAK,GAAGD,eAAe,CAACjC,CAAC,oBAADA,CAAC,CAAEkC,KAAK,CAAC;EACvC,IAAMC,kBAAkB,GAAG,EAAEN,WAAW;EACxC,IAAMO,eAAe,GAAGpC,CAAC,CAACqC,OAAO,IAAI,EAAE;EACvC,IAAIA,OAAO,GAAGD,eAAe;EAC7B,IAAIpC,CAAC,CAACsC,cAAc,IAAI,IAAI,EAAE;IAC5BD,OAAO,IAAI,gCAAgCrC,CAAC,CAACsC,cAAc,EAAE;EAC/D;EACA,IAAMC,UAAU,GAAGvC,CAAC,CAACmB,IAAI,IAAI,IAAI,IAAInB,CAAC,CAACmB,IAAI,KAAK,EAAE,GAAG,EAAE,GAAG,GAAGnB,CAAC,CAACmB,IAAI,IAAI;EAEvE,IAAI,CAACkB,OAAO,CAACG,UAAU,CAACD,UAAU,CAAC,EAAE;IACnCF,OAAO,GAAGE,UAAU,GAAGF,OAAO;EAChC;EAEAA,OAAO,GACLrC,CAAC,CAACyC,QAAQ,IAAI,IAAI,GAAGJ,OAAO,GAAG,GAAGA,OAAO,gBAAgBrC,CAAC,CAACyC,QAAQ,EAAE;EAGvE,IAAMC,SAAiB,GAAAzD,MAAA,CAAA0D,MAAA,KAElB3C,CAAC,CAACuB,qBAAqB,CAAC;IAC3BkB,QAAQ,EAAEzC,CAAC,CAACyC,QAAQ;IACpBG,QAAQ,EAAE5C,CAAC,CAACkC;EAAK,EAClB;EACD,IAAIlC,CAAC,CAAC6C,KAAK,IAAI,IAAI,IAAI,OAAO7C,CAAC,CAAC6C,KAAK,KAAK,QAAQ,EAAE;IAClDH,SAAS,CAACI,YAAY,GAAG9C,CAAC,CAAC6C,KAAK,CAACC,YAAY;IAC7CJ,SAAS,CAACK,oBAAoB,GAAG/C,CAAC,CAAC6C,KAAK,CAACE,oBAAoB;IAC7DL,SAAS,CAACM,aAAa,GAAGhD,CAAC,CAAC6C,KAAK,CAACG,aAAa;EACjD;EAEA,IAAMrB,IAAI,GAAGD,mBAAmB,CAAC;IAC/BW,OAAO,EAAPA,OAAO;IACPD,eAAe,EAAEC,OAAO,KAAKD,eAAe,GAAG,IAAI,GAAGA,eAAe;IACrEjB,IAAI,EAAEnB,CAAC,CAACmB,IAAI,IAAI,IAAI,IAAInB,CAAC,CAACmB,IAAI,KAAK,EAAE,GAAG,IAAI,GAAGnB,CAAC,CAACmB,IAAI;IACrDmB,cAAc,EACZ,OAAOtC,CAAC,CAACsC,cAAc,KAAK,QAAQ,GAAGtC,CAAC,CAACsC,cAAc,GAAG,IAAI;IAChEJ,KAAK,EAALA,KAAK;IACLe,EAAE,EAAEd,kBAAkB;IACtBJ,OAAO,EAAPA,OAAO;IACPW,SAAS,EAATA;EACF,CAAC,CAAC;EAEF,IAAIV,eAAe,EAAE;IAInBkB,OAAO,CAACC,KAAK,CAACxB,IAAI,CAACU,OAAO,CAAC;EAC7B;EAEA,IAAIe,OAAO,EAAE;IACX,IAAMC,MAAM,GAAGrE,OAAO,mBAAmB,CAAC,CAACK,OAAO;IAClDgE,MAAM,CAACC,YAAY,CAAArE,MAAA,CAAA0D,MAAA,KACdhB,IAAI;MACP4B,gBAAgB,EAAE,CAAC,CAACvD,CAAC,CAACuD;IAAgB,EACvC,CAAC;EACJ,CAAC,MAAM,IAAIxB,OAAO,IAAI/B,CAAC,CAACwD,IAAI,KAAK,MAAM,EAAE;IACvC,IAAMC,uBAAuB,GAC3BzE,OAAO,4BAA4B,CAAC,CAACK,OAAO;IAC9C,IAAIoE,uBAAuB,EAAE;MAC3B,IAAI1B,OAAO,EAAE;QACX,IAAI2B,MAAM,CAACC,2BAA2B,YAAlCD,MAAM,CAACC,2BAA2B,CAAG,CAAC,EAAE;UAC1C;QACF;QACAD,MAAM,CAACE,yBAAyB,YAAhCF,MAAM,CAACE,yBAAyB,CAAG,CAAC;MACtC;MACAH,uBAAuB,CAAC3B,eAAe,CAACH,IAAI,CAAC;IAC/C;EACF;AACF;AAWA,IAAIkC,kBAAkB,GAAG,KAAK;AAK9B,SAASC,eAAeA,CAAC9D,CAAQ,EAAE+B,OAAgB,EAAE;EAEnD,IAAMC,eAAe,GAAG,IAAI;EAC5B,IACE,CAAC0B,MAAM,CAACK,kBAAkB,IAC1B,CAACL,MAAM,CAACK,kBAAkB,CAAC/D,CAAC,EAAE+B,OAAO,EAAEC,eAAe,CAAC,EACvD;IACA,IAAImB,KAAY;IAChB,IAAInD,CAAC,YAAYoB,KAAK,EAAE;MACtB+B,KAAK,GAAGnD,CAAC;IACX,CAAC,MAAM;MAKLmD,KAAK,GAAG,IAAI7D,cAAc,CAACU,CAAC,CAAC;IAC/B;IACA,IAAI;MACF6D,kBAAkB,GAAG,IAAI;MAIzB/B,eAAe,CAACqB,KAAK,EAAEpB,OAAO,EAAEC,eAAe,CAAC;IAClD,CAAC,SAAS;MACR6B,kBAAkB,GAAG,KAAK;IAC5B;EACF;AACF;AAIA,SAASG,wBAAwBA,CAAA,EAAU;EAAA,IAAAC,QAAA;EAAA,SAAAC,KAAA,GAAArD,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAAkD,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;IAAJpD,IAAI,CAAAoD,KAAA,IAAAtD,SAAA,CAAAsD,KAAA;EAAA;EAEvC,CAAAF,QAAA,GAAAf,OAAO,EAACkB,cAAc,CAAA/D,KAAA,CAAA4D,QAAA,EAAIlD,IAAI,CAAC;EAC/B,IAAI,CAACmC,OAAO,CAACmB,wBAAwB,EAAE;IACrC;EACF;EACA,IAAIR,kBAAkB,IAAIH,MAAM,CAACY,qBAAqB,YAA5BZ,MAAM,CAACY,qBAAqB,CAAG,CAAC,EAAE;IA6B1D;EACF;EAEA,IAAInB,KAAK;EAET,IAAMoB,QAAQ,GAAGxD,IAAI,CAAC,CAAC,CAAC;EACxB,IAAIwD,QAAQ,YAARA,QAAQ,CAAErC,KAAK,EAAE;IAEnBiB,KAAK,GAAGoB,QAAQ;EAClB,CAAC,MAAM;IACL,IAAMC,aAAa,GAAGxF,OAAO,6BAA6B,CAAC,CAACK,OAAO;IACnE,IAAI,OAAOkF,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,CAAC/B,UAAU,CAAC,WAAW,CAAC,EAAE;MAIpE;IACF;IACA,IAAMH,OAAO,GAAGtB,IAAI,CACjB0D,GAAG,CAAC,UAAAC,GAAG;MAAA,OAAK,OAAOA,GAAG,KAAK,QAAQ,GAAGA,GAAG,GAAGF,aAAa,CAACE,GAAG,CAAC;IAAA,CAAC,CAAC,CAChEC,IAAI,CAAC,GAAG,CAAC;IAEZxB,KAAK,GAAG,IAAI7D,cAAc,CAAC+C,OAAO,CAAC;IACnCc,KAAK,CAAChC,IAAI,GAAG,eAAe;EAC9B;EAEA,IAAMY,OAAO,GAAG,KAAK;EACrB,IAAMC,eAAe,GAAG,KAAK;EAC7B,IACE,CAAC0B,MAAM,CAACK,kBAAkB,IAC1B,CAACL,MAAM,CAACK,kBAAkB,CAACZ,KAAK,EAAEpB,OAAO,EAAEC,eAAe,CAAC,EAC3D;IACAF,eAAe,CAIbqB,KAAK,EACLpB,OAAO,EACPC,eACF,CAAC;EACH;AACF;AAMA,SAAS4C,2BAA2BA,CAAA,EAAG;EAErC,IAAI1B,OAAO,CAACkB,cAAc,EAAE;IAC1B;EACF;EAEAlB,OAAO,CAACkB,cAAc,GAAGlB,OAAO,CAACC,KAAK,CAAC0B,IAAI,CAAC3B,OAAO,CAAC;EACpDA,OAAO,CAACC,KAAK,GAAGa,wBAAwB;EACxC,IAAId,OAAO,CAACmB,wBAAwB,KAAKS,SAAS,EAAE;IAGlD5B,OAAO,CAACmB,wBAAwB,GAAG,IAAI;EACzC;AACF;AAEA,IAAMU,iBAAiB,GAAG;EACxBxD,qBAAqB,EAArBA,qBAAqB;EACrBuC,eAAe,EAAfA,eAAe;EACfc,2BAA2B,EAA3BA,2BAA2B;EAC3BtF,cAAc,EAAdA,cAAc;EACdkC,8BAA8B,EAA9BA;AACF,CAAC;AAAC,IAAAwD,QAAA,GAAA7F,OAAA,CAAAE,OAAA,GAEa0F,iBAAiB", "ignoreList": []}