{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "default", "isPlainObject", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_get2", "_inherits2", "_classPrivateFieldLooseBase2", "_classPrivateFieldLooseKey2", "_AnimatedNode", "_AnimatedWithChildren2", "React", "_interopRequireWildcard", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "has", "get", "set", "_t", "hasOwnProperty", "call", "getOwnPropertyDescriptor", "_callSuper", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "_superPropGet", "p", "MAX_DEPTH", "getPrototypeOf", "isPrototypeOf", "isValidElement", "flatAnimatedNodes", "nodes", "arguments", "length", "undefined", "depth", "AnimatedNode", "push", "Array", "isArray", "ii", "element", "keys", "key", "mapAnimatedNodes", "fn", "map", "result", "_nodes", "AnimatedObject", "_Animated<PERSON>ith<PERSON><PERSON><PERSON><PERSON>", "config", "_this", "writable", "_value", "__getValue", "node", "__getValueWithStaticObject", "staticObject", "index", "__getAnimatedValue", "__attach", "__add<PERSON><PERSON>d", "__detach", "__remove<PERSON><PERSON>d", "__makeNative", "platformConfig", "__getNativeConfig", "type", "nodeTag", "__getNativeTag", "debugID", "__getDebugID", "from", "AnimatedWithChildren"], "sources": ["AnimatedObject.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n * @format\n * @oncall react_native\n */\n\n'use strict';\n\nimport type {PlatformConfig} from '../AnimatedPlatformConfig';\nimport type {AnimatedNodeConfig} from './AnimatedNode';\n\nimport AnimatedNode from './AnimatedNode';\nimport AnimatedWithChildren from './AnimatedWithChildren';\nimport * as React from 'react';\n\nconst MAX_DEPTH = 5;\n\nexport function isPlainObject(\n  value: mixed,\n  /* $FlowIssue[incompatible-type-guard] - Flow does not know that the prototype\n     and ReactElement checks preserve the type refinement of `value`. */\n): value is $ReadOnly<{[string]: mixed}> {\n  return (\n    // $FlowFixMe[incompatible-type-guard]\n    value !== null &&\n    typeof value === 'object' &&\n    Object.getPrototypeOf(value).isPrototypeOf(Object) &&\n    !React.isValidElement(value)\n  );\n}\n\nfunction flatAnimatedNodes(\n  value: mixed,\n  nodes: Array<AnimatedNode> = [],\n  depth: number = 0,\n): Array<AnimatedNode> {\n  if (depth >= MAX_DEPTH) {\n    return nodes;\n  }\n  if (value instanceof AnimatedNode) {\n    nodes.push(value);\n  } else if (Array.isArray(value)) {\n    for (let ii = 0, length = value.length; ii < length; ii++) {\n      const element = value[ii];\n      flatAnimatedNodes(element, nodes, depth + 1);\n    }\n  } else if (isPlainObject(value)) {\n    const keys = Object.keys(value);\n    for (let ii = 0, length = keys.length; ii < length; ii++) {\n      const key = keys[ii];\n      flatAnimatedNodes(value[key], nodes, depth + 1);\n    }\n  }\n  return nodes;\n}\n\n// Returns a copy of value with a transformation fn applied to any AnimatedNodes\nfunction mapAnimatedNodes(value: any, fn: any => any, depth: number = 0): any {\n  if (depth >= MAX_DEPTH) {\n    return value;\n  }\n\n  if (value instanceof AnimatedNode) {\n    return fn(value);\n  } else if (Array.isArray(value)) {\n    return value.map(element => mapAnimatedNodes(element, fn, depth + 1));\n  } else if (isPlainObject(value)) {\n    const result: {[string]: any} = {};\n    const keys = Object.keys(value);\n    for (let ii = 0, length = keys.length; ii < length; ii++) {\n      const key = keys[ii];\n      result[key] = mapAnimatedNodes(value[key], fn, depth + 1);\n    }\n    return result;\n  } else {\n    return value;\n  }\n}\n\nexport default class AnimatedObject extends AnimatedWithChildren {\n  #nodes: $ReadOnlyArray<AnimatedNode>;\n  _value: mixed;\n\n  /**\n   * Creates an `AnimatedObject` if `value` contains `AnimatedNode` instances.\n   * Otherwise, returns `null`.\n   */\n  static from(value: mixed): ?AnimatedObject {\n    const nodes = flatAnimatedNodes(value);\n    if (nodes.length === 0) {\n      return null;\n    }\n    return new AnimatedObject(nodes, value);\n  }\n\n  /**\n   * Should only be called by `AnimatedObject.from`.\n   */\n  constructor(\n    nodes: $ReadOnlyArray<AnimatedNode>,\n    value: mixed,\n    config?: ?AnimatedNodeConfig,\n  ) {\n    super(config);\n    this.#nodes = nodes;\n    this._value = value;\n  }\n\n  __getValue(): any {\n    return mapAnimatedNodes(this._value, node => {\n      return node.__getValue();\n    });\n  }\n\n  __getValueWithStaticObject(staticObject: mixed): any {\n    const nodes = this.#nodes;\n    let index = 0;\n    // NOTE: We can depend on `this._value` and `staticObject` sharing a\n    // structure because of `useAnimatedPropsMemo`.\n    return mapAnimatedNodes(staticObject, () => nodes[index++].__getValue());\n  }\n\n  __getAnimatedValue(): any {\n    return mapAnimatedNodes(this._value, node => {\n      return node.__getAnimatedValue();\n    });\n  }\n\n  __attach(): void {\n    const nodes = this.#nodes;\n    for (let ii = 0, length = nodes.length; ii < length; ii++) {\n      const node = nodes[ii];\n      node.__addChild(this);\n    }\n    super.__attach();\n  }\n\n  __detach(): void {\n    const nodes = this.#nodes;\n    for (let ii = 0, length = nodes.length; ii < length; ii++) {\n      const node = nodes[ii];\n      node.__removeChild(this);\n    }\n    super.__detach();\n  }\n\n  __makeNative(platformConfig: ?PlatformConfig): void {\n    const nodes = this.#nodes;\n    for (let ii = 0, length = nodes.length; ii < length; ii++) {\n      const node = nodes[ii];\n      node.__makeNative(platformConfig);\n    }\n    super.__makeNative(platformConfig);\n  }\n\n  __getNativeConfig(): any {\n    return {\n      type: 'object',\n      value: mapAnimatedNodes(this._value, node => {\n        return {nodeTag: node.__getNativeTag()};\n      }),\n      debugID: this.__getDebugID(),\n    };\n  }\n}\n"], "mappings": "AAWA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAAAF,OAAA,CAAAG,aAAA,GAAAA,aAAA;AAAA,IAAAC,gBAAA,GAAAR,sBAAA,CAAAC,OAAA;AAAA,IAAAQ,aAAA,GAAAT,sBAAA,CAAAC,OAAA;AAAA,IAAAS,2BAAA,GAAAV,sBAAA,CAAAC,OAAA;AAAA,IAAAU,gBAAA,GAAAX,sBAAA,CAAAC,OAAA;AAAA,IAAAW,KAAA,GAAAZ,sBAAA,CAAAC,OAAA;AAAA,IAAAY,UAAA,GAAAb,sBAAA,CAAAC,OAAA;AAAA,IAAAa,4BAAA,GAAAd,sBAAA,CAAAC,OAAA;AAAA,IAAAc,2BAAA,GAAAf,sBAAA,CAAAC,OAAA;AAKb,IAAAe,aAAA,GAAAhB,sBAAA,CAAAC,OAAA;AACA,IAAAgB,sBAAA,GAAAjB,sBAAA,CAAAC,OAAA;AACA,IAAAiB,KAAA,GAAAC,uBAAA,CAAAlB,OAAA;AAA+B,SAAAkB,wBAAAC,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAH,uBAAA,YAAAA,wBAAAC,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAvB,OAAA,EAAAc,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAI,GAAA,CAAAV,CAAA,UAAAM,CAAA,CAAAK,GAAA,CAAAX,CAAA,GAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,EAAAQ,CAAA,cAAAK,EAAA,IAAAb,CAAA,gBAAAa,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAa,EAAA,OAAAN,CAAA,IAAAD,CAAA,GAAAxB,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAkC,wBAAA,CAAAhB,CAAA,EAAAa,EAAA,OAAAN,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAK,EAAA,EAAAN,CAAA,IAAAC,CAAA,CAAAK,EAAA,IAAAb,CAAA,CAAAa,EAAA,WAAAL,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAAA,SAAAgB,WAAAhB,CAAA,EAAAK,CAAA,EAAAN,CAAA,WAAAM,CAAA,OAAAf,gBAAA,CAAAL,OAAA,EAAAoB,CAAA,OAAAhB,2BAAA,CAAAJ,OAAA,EAAAe,CAAA,EAAAiB,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAd,CAAA,EAAAN,CAAA,YAAAT,gBAAA,CAAAL,OAAA,EAAAe,CAAA,EAAAoB,WAAA,IAAAf,CAAA,CAAAgB,KAAA,CAAArB,CAAA,EAAAD,CAAA;AAAA,SAAAkB,0BAAA,cAAAjB,CAAA,IAAAsB,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAV,IAAA,CAAAI,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAtB,CAAA,aAAAiB,yBAAA,YAAAA,0BAAA,aAAAjB,CAAA;AAAA,SAAAyB,cAAAzB,CAAA,EAAAK,CAAA,EAAAN,CAAA,EAAAG,CAAA,QAAAwB,CAAA,OAAAnC,KAAA,CAAAN,OAAA,MAAAK,gBAAA,CAAAL,OAAA,MAAAiB,CAAA,GAAAF,CAAA,CAAAuB,SAAA,GAAAvB,CAAA,GAAAK,CAAA,EAAAN,CAAA,cAAAG,CAAA,yBAAAwB,CAAA,aAAA1B,CAAA,WAAA0B,CAAA,CAAAL,KAAA,CAAAtB,CAAA,EAAAC,CAAA,OAAA0B,CAAA;AAE/B,IAAMC,SAAS,GAAG,CAAC;AAEZ,SAASzC,aAAaA,CAC3BF,KAAY,EAG2B;EACvC,OAEEA,KAAK,KAAK,IAAI,IACd,OAAOA,KAAK,KAAK,QAAQ,IACzBH,MAAM,CAAC+C,cAAc,CAAC5C,KAAK,CAAC,CAAC6C,aAAa,CAAChD,MAAM,CAAC,IAClD,CAACgB,KAAK,CAACiC,cAAc,CAAC9C,KAAK,CAAC;AAEhC;AAEA,SAAS+C,iBAAiBA,CACxB/C,KAAY,EAGS;EAAA,IAFrBgD,KAA0B,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EAAA,IAC/BG,KAAa,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAEjB,IAAIG,KAAK,IAAIT,SAAS,EAAE;IACtB,OAAOK,KAAK;EACd;EACA,IAAIhD,KAAK,YAAYqD,qBAAY,EAAE;IACjCL,KAAK,CAACM,IAAI,CAACtD,KAAK,CAAC;EACnB,CAAC,MAAM,IAAIuD,KAAK,CAACC,OAAO,CAACxD,KAAK,CAAC,EAAE;IAC/B,KAAK,IAAIyD,EAAE,GAAG,CAAC,EAAEP,MAAM,GAAGlD,KAAK,CAACkD,MAAM,EAAEO,EAAE,GAAGP,MAAM,EAAEO,EAAE,EAAE,EAAE;MACzD,IAAMC,OAAO,GAAG1D,KAAK,CAACyD,EAAE,CAAC;MACzBV,iBAAiB,CAACW,OAAO,EAAEV,KAAK,EAAEI,KAAK,GAAG,CAAC,CAAC;IAC9C;EACF,CAAC,MAAM,IAAIlD,aAAa,CAACF,KAAK,CAAC,EAAE;IAC/B,IAAM2D,IAAI,GAAG9D,MAAM,CAAC8D,IAAI,CAAC3D,KAAK,CAAC;IAC/B,KAAK,IAAIyD,GAAE,GAAG,CAAC,EAAEP,OAAM,GAAGS,IAAI,CAACT,MAAM,EAAEO,GAAE,GAAGP,OAAM,EAAEO,GAAE,EAAE,EAAE;MACxD,IAAMG,GAAG,GAAGD,IAAI,CAACF,GAAE,CAAC;MACpBV,iBAAiB,CAAC/C,KAAK,CAAC4D,GAAG,CAAC,EAAEZ,KAAK,EAAEI,KAAK,GAAG,CAAC,CAAC;IACjD;EACF;EACA,OAAOJ,KAAK;AACd;AAGA,SAASa,gBAAgBA,CAAC7D,KAAU,EAAE8D,EAAc,EAA0B;EAAA,IAAxBV,KAAa,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EACrE,IAAIG,KAAK,IAAIT,SAAS,EAAE;IACtB,OAAO3C,KAAK;EACd;EAEA,IAAIA,KAAK,YAAYqD,qBAAY,EAAE;IACjC,OAAOS,EAAE,CAAC9D,KAAK,CAAC;EAClB,CAAC,MAAM,IAAIuD,KAAK,CAACC,OAAO,CAACxD,KAAK,CAAC,EAAE;IAC/B,OAAOA,KAAK,CAAC+D,GAAG,CAAC,UAAAL,OAAO;MAAA,OAAIG,gBAAgB,CAACH,OAAO,EAAEI,EAAE,EAAEV,KAAK,GAAG,CAAC,CAAC;IAAA,EAAC;EACvE,CAAC,MAAM,IAAIlD,aAAa,CAACF,KAAK,CAAC,EAAE;IAC/B,IAAMgE,MAAuB,GAAG,CAAC,CAAC;IAClC,IAAML,IAAI,GAAG9D,MAAM,CAAC8D,IAAI,CAAC3D,KAAK,CAAC;IAC/B,KAAK,IAAIyD,EAAE,GAAG,CAAC,EAAEP,MAAM,GAAGS,IAAI,CAACT,MAAM,EAAEO,EAAE,GAAGP,MAAM,EAAEO,EAAE,EAAE,EAAE;MACxD,IAAMG,GAAG,GAAGD,IAAI,CAACF,EAAE,CAAC;MACpBO,MAAM,CAACJ,GAAG,CAAC,GAAGC,gBAAgB,CAAC7D,KAAK,CAAC4D,GAAG,CAAC,EAAEE,EAAE,EAAEV,KAAK,GAAG,CAAC,CAAC;IAC3D;IACA,OAAOY,MAAM;EACf,CAAC,MAAM;IACL,OAAOhE,KAAK;EACd;AACF;AAAC,IAAAiE,MAAA,OAAAvD,2BAAA,CAAAT,OAAA;AAAA,IAEoBiE,cAAc,GAAAnE,OAAA,CAAAE,OAAA,aAAAkE,qBAAA;EAmBjC,SAAAD,eACElB,KAAmC,EACnChD,KAAY,EACZoE,MAA4B,EAC5B;IAAA,IAAAC,KAAA;IAAA,IAAAlE,gBAAA,CAAAF,OAAA,QAAAiE,cAAA;IACAG,KAAA,GAAArC,UAAA,OAAAkC,cAAA,GAAME,MAAM;IAAEvE,MAAA,CAAAC,cAAA,CAAAuE,KAAA,EAAAJ,MAAA;MAAAK,QAAA;MAAAtE,KAAA;IAAA;IACd,IAAAS,4BAAA,CAAAR,OAAA,EAAAoE,KAAA,EAAAJ,MAAA,EAAAA,MAAA,IAAcjB,KAAK;IACnBqB,KAAA,CAAKE,MAAM,GAAGvE,KAAK;IAAC,OAAAqE,KAAA;EACtB;EAAC,IAAA7D,UAAA,CAAAP,OAAA,EAAAiE,cAAA,EAAAC,qBAAA;EAAA,WAAA/D,aAAA,CAAAH,OAAA,EAAAiE,cAAA;IAAAN,GAAA;IAAA5D,KAAA,EAED,SAAAwE,UAAUA,CAAA,EAAQ;MAChB,OAAOX,gBAAgB,CAAC,IAAI,CAACU,MAAM,EAAE,UAAAE,IAAI,EAAI;QAC3C,OAAOA,IAAI,CAACD,UAAU,CAAC,CAAC;MAC1B,CAAC,CAAC;IACJ;EAAC;IAAAZ,GAAA;IAAA5D,KAAA,EAED,SAAA0E,0BAA0BA,CAACC,YAAmB,EAAO;MACnD,IAAM3B,KAAK,OAAAvC,4BAAA,CAAAR,OAAA,EAAG,IAAI,EAAAgE,MAAA,EAAAA,MAAA,CAAO;MACzB,IAAIW,KAAK,GAAG,CAAC;MAGb,OAAOf,gBAAgB,CAACc,YAAY,EAAE;QAAA,OAAM3B,KAAK,CAAC4B,KAAK,EAAE,CAAC,CAACJ,UAAU,CAAC,CAAC;MAAA,EAAC;IAC1E;EAAC;IAAAZ,GAAA;IAAA5D,KAAA,EAED,SAAA6E,kBAAkBA,CAAA,EAAQ;MACxB,OAAOhB,gBAAgB,CAAC,IAAI,CAACU,MAAM,EAAE,UAAAE,IAAI,EAAI;QAC3C,OAAOA,IAAI,CAACI,kBAAkB,CAAC,CAAC;MAClC,CAAC,CAAC;IACJ;EAAC;IAAAjB,GAAA;IAAA5D,KAAA,EAED,SAAA8E,QAAQA,CAAA,EAAS;MACf,IAAM9B,KAAK,OAAAvC,4BAAA,CAAAR,OAAA,EAAG,IAAI,EAAAgE,MAAA,EAAAA,MAAA,CAAO;MACzB,KAAK,IAAIR,EAAE,GAAG,CAAC,EAAEP,MAAM,GAAGF,KAAK,CAACE,MAAM,EAAEO,EAAE,GAAGP,MAAM,EAAEO,EAAE,EAAE,EAAE;QACzD,IAAMgB,IAAI,GAAGzB,KAAK,CAACS,EAAE,CAAC;QACtBgB,IAAI,CAACM,UAAU,CAAC,IAAI,CAAC;MACvB;MACAtC,aAAA,CAAAyB,cAAA;IACF;EAAC;IAAAN,GAAA;IAAA5D,KAAA,EAED,SAAAgF,QAAQA,CAAA,EAAS;MACf,IAAMhC,KAAK,OAAAvC,4BAAA,CAAAR,OAAA,EAAG,IAAI,EAAAgE,MAAA,EAAAA,MAAA,CAAO;MACzB,KAAK,IAAIR,EAAE,GAAG,CAAC,EAAEP,MAAM,GAAGF,KAAK,CAACE,MAAM,EAAEO,EAAE,GAAGP,MAAM,EAAEO,EAAE,EAAE,EAAE;QACzD,IAAMgB,IAAI,GAAGzB,KAAK,CAACS,EAAE,CAAC;QACtBgB,IAAI,CAACQ,aAAa,CAAC,IAAI,CAAC;MAC1B;MACAxC,aAAA,CAAAyB,cAAA;IACF;EAAC;IAAAN,GAAA;IAAA5D,KAAA,EAED,SAAAkF,YAAYA,CAACC,cAA+B,EAAQ;MAClD,IAAMnC,KAAK,OAAAvC,4BAAA,CAAAR,OAAA,EAAG,IAAI,EAAAgE,MAAA,EAAAA,MAAA,CAAO;MACzB,KAAK,IAAIR,EAAE,GAAG,CAAC,EAAEP,MAAM,GAAGF,KAAK,CAACE,MAAM,EAAEO,EAAE,GAAGP,MAAM,EAAEO,EAAE,EAAE,EAAE;QACzD,IAAMgB,IAAI,GAAGzB,KAAK,CAACS,EAAE,CAAC;QACtBgB,IAAI,CAACS,YAAY,CAACC,cAAc,CAAC;MACnC;MACA1C,aAAA,CAAAyB,cAAA,4BAAmBiB,cAAc;IACnC;EAAC;IAAAvB,GAAA;IAAA5D,KAAA,EAED,SAAAoF,iBAAiBA,CAAA,EAAQ;MACvB,OAAO;QACLC,IAAI,EAAE,QAAQ;QACdrF,KAAK,EAAE6D,gBAAgB,CAAC,IAAI,CAACU,MAAM,EAAE,UAAAE,IAAI,EAAI;UAC3C,OAAO;YAACa,OAAO,EAAEb,IAAI,CAACc,cAAc,CAAC;UAAC,CAAC;QACzC,CAAC,CAAC;QACFC,OAAO,EAAE,IAAI,CAACC,YAAY,CAAC;MAC7B,CAAC;IACH;EAAC;IAAA7B,GAAA;IAAA5D,KAAA,EA5ED,SAAO0F,IAAIA,CAAC1F,KAAY,EAAmB;MACzC,IAAMgD,KAAK,GAAGD,iBAAiB,CAAC/C,KAAK,CAAC;MACtC,IAAIgD,KAAK,CAACE,MAAM,KAAK,CAAC,EAAE;QACtB,OAAO,IAAI;MACb;MACA,OAAO,IAAIgB,cAAc,CAAClB,KAAK,EAAEhD,KAAK,CAAC;IACzC;EAAC;AAAA,EAdyC2F,8BAAoB", "ignoreList": []}