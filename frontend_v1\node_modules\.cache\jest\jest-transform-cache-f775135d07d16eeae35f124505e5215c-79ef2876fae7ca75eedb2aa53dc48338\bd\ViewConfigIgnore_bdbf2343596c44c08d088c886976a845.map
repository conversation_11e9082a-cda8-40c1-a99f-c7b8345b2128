{"version": 3, "names": ["_Platform", "_interopRequireDefault", "require", "ignoredViewConfigProps", "WeakSet", "DynamicallyInjectedByGestureHandler", "object", "add", "ConditionallyIgnoredEventHandlers", "value", "Platform", "OS", "undefined", "isIgnored", "has"], "sources": ["ViewConfigIgnore.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict\n * @format\n */\n\nimport Platform from '../Utilities/Platform';\n\nconst ignoredViewConfigProps = new WeakSet<{...}>();\n\n/**\n * Decorates ViewConfig values that are dynamically injected by the library,\n * react-native-gesture-handler. (*********)\n */\nexport function DynamicallyInjectedByGestureHandler<T: {...}>(object: T): T {\n  ignoredViewConfigProps.add(object);\n  return object;\n}\n\n/**\n * On iOS, ViewManager event declarations generate {eventName}: true entries\n * in ViewConfig valueAttributes. These entries aren't generated for Android.\n * This annotation allows Static ViewConfigs to insert these entries into\n * iOS but not Android.\n *\n * In the future, we want to remove this platform-inconsistency. We want\n * to set RN$ViewConfigEventValidAttributesDisabled = true server-side,\n * so that iOS does not generate validAttributes from event props in iOS RCTViewManager,\n * since Android does not generate validAttributes from events props in Android ViewManager.\n *\n * TODO(T110872225): Remove this logic, after achieving platform-consistency\n */\nexport function ConditionallyIgnoredEventHandlers<T: {[name: string]: true}>(\n  value: T,\n): T | void {\n  if (Platform.OS === 'ios') {\n    return value;\n  }\n  return undefined;\n}\n\nexport function isIgnored(value: mixed): boolean {\n  if (typeof value === 'object' && value != null) {\n    return ignoredViewConfigProps.has(value);\n  }\n  return false;\n}\n"], "mappings": ";;;;;;;AAUA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAMC,sBAAsB,GAAG,IAAIC,OAAO,CAAQ,CAAC;AAM5C,SAASC,mCAAmCA,CAAWC,MAAS,EAAK;EAC1EH,sBAAsB,CAACI,GAAG,CAACD,MAAM,CAAC;EAClC,OAAOA,MAAM;AACf;AAeO,SAASE,iCAAiCA,CAC/CC,KAAQ,EACE;EACV,IAAIC,iBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;IACzB,OAAOF,KAAK;EACd;EACA,OAAOG,SAAS;AAClB;AAEO,SAASC,SAASA,CAACJ,KAAY,EAAW;EAC/C,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,IAAI,IAAI,EAAE;IAC9C,OAAON,sBAAsB,CAACW,GAAG,CAACL,KAAK,CAAC;EAC1C;EACA,OAAO,KAAK;AACd", "ignoreList": []}