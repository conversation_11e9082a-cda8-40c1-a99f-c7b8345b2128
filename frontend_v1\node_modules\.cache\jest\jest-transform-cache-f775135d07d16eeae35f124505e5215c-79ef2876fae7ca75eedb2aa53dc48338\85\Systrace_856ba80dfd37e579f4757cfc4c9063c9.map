{"version": 3, "names": ["TRACE_TAG_REACT_APPS", "_async<PERSON><PERSON>ie", "isEnabled", "global", "nativeTraceIsTracing", "Boolean", "__RCTProfileIsProfiling", "setEnabled", "_doEnable", "beginEvent", "eventName", "args", "eventNameString", "nativeTraceBeginSection", "endEvent", "nativeTraceEndSection", "beginAsyncEvent", "cookie", "nativeTraceBeginAsyncSection", "endAsyncEvent", "nativeTraceEndAsyncSection", "counterEvent", "value", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "__DEV__", "Systrace", "__METRO_GLOBAL_PREFIX__"], "sources": ["Systrace.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict\n * @format\n */\n\nimport typeof * as SystraceModule from './Systrace';\n\nconst TRACE_TAG_REACT_APPS = 1 << 17; // eslint-disable-line no-bitwise\n\nlet _asyncCookie = 0;\n\ntype EventName = string | (() => string);\ntype EventArgs = ?{[string]: string};\n\n/**\n * Indicates if the application is currently being traced.\n *\n * Calling methods on this module when the application isn't being traced is\n * cheap, but this method can be used to avoid computing expensive values for\n * those functions.\n *\n * @example\n * if (Systrace.isEnabled()) {\n *   const expensiveArgs = computeExpensiveArgs();\n *   Systrace.beginEvent('myEvent', expensiveArgs);\n * }\n */\nexport function isEnabled(): boolean {\n  return global.nativeTraceIsTracing\n    ? global.nativeTraceIsTracing(TRACE_TAG_REACT_APPS)\n    : Boolean(global.__RCTProfileIsProfiling);\n}\n\n/**\n * @deprecated This function is now a no-op but it's left for backwards\n * compatibility. `isEnabled` will now synchronously check if we're actively\n * profiling or not. This is necessary because we don't have callbacks to know\n * when profiling has started/stopped on Android APIs.\n */\nexport function setEnabled(_doEnable: boolean): void {}\n\n/**\n * Marks the start of a synchronous event that should end in the same stack\n * frame. The end of this event should be marked using the `endEvent` function.\n */\nexport function beginEvent(eventName: EventName, args?: EventArgs): void {\n  if (isEnabled()) {\n    const eventNameString =\n      typeof eventName === 'function' ? eventName() : eventName;\n    global.nativeTraceBeginSection(TRACE_TAG_REACT_APPS, eventNameString, args);\n  }\n}\n\n/**\n * Marks the end of a synchronous event started in the same stack frame.\n */\nexport function endEvent(args?: EventArgs): void {\n  if (isEnabled()) {\n    global.nativeTraceEndSection(TRACE_TAG_REACT_APPS, args);\n  }\n}\n\n/**\n * Marks the start of a potentially asynchronous event. The end of this event\n * should be marked calling the `endAsyncEvent` function with the cookie\n * returned by this function.\n */\nexport function beginAsyncEvent(\n  eventName: EventName,\n  args?: EventArgs,\n): number {\n  const cookie = _asyncCookie;\n  if (isEnabled()) {\n    _asyncCookie++;\n    const eventNameString =\n      typeof eventName === 'function' ? eventName() : eventName;\n    global.nativeTraceBeginAsyncSection(\n      TRACE_TAG_REACT_APPS,\n      eventNameString,\n      cookie,\n      args,\n    );\n  }\n  return cookie;\n}\n\n/**\n * Marks the end of a potentially asynchronous event, which was started with\n * the given cookie.\n */\nexport function endAsyncEvent(\n  eventName: EventName,\n  cookie: number,\n  args?: EventArgs,\n): void {\n  if (isEnabled()) {\n    const eventNameString =\n      typeof eventName === 'function' ? eventName() : eventName;\n    global.nativeTraceEndAsyncSection(\n      TRACE_TAG_REACT_APPS,\n      eventNameString,\n      cookie,\n      args,\n    );\n  }\n}\n\n/**\n * Registers a new value for a counter event.\n */\nexport function counterEvent(eventName: EventName, value: number): void {\n  if (isEnabled()) {\n    const eventNameString =\n      typeof eventName === 'function' ? eventName() : eventName;\n    global.nativeTraceCounter &&\n      global.nativeTraceCounter(TRACE_TAG_REACT_APPS, eventNameString, value);\n  }\n}\n\nif (__DEV__) {\n  const Systrace: SystraceModule = {\n    isEnabled,\n    setEnabled,\n    beginEvent,\n    endEvent,\n    beginAsyncEvent,\n    endAsyncEvent,\n    counterEvent,\n  };\n\n  // The metro require polyfill can not have dependencies (true for all polyfills).\n  // Ensure that `Systrace` is available in polyfill by exposing it globally.\n  global[(global.__METRO_GLOBAL_PREFIX__ || '') + '__SYSTRACE'] = Systrace;\n}\n"], "mappings": ";;;;;;;;;;AAYA,IAAMA,oBAAoB,GAAG,CAAC,IAAI,EAAE;AAEpC,IAAIC,YAAY,GAAG,CAAC;AAkBb,SAASC,SAASA,CAAA,EAAY;EACnC,OAAOC,MAAM,CAACC,oBAAoB,GAC9BD,MAAM,CAACC,oBAAoB,CAACJ,oBAAoB,CAAC,GACjDK,OAAO,CAACF,MAAM,CAACG,uBAAuB,CAAC;AAC7C;AAQO,SAASC,UAAUA,CAACC,SAAkB,EAAQ,CAAC;AAM/C,SAASC,UAAUA,CAACC,SAAoB,EAAEC,IAAgB,EAAQ;EACvE,IAAIT,SAAS,CAAC,CAAC,EAAE;IACf,IAAMU,eAAe,GACnB,OAAOF,SAAS,KAAK,UAAU,GAAGA,SAAS,CAAC,CAAC,GAAGA,SAAS;IAC3DP,MAAM,CAACU,uBAAuB,CAACb,oBAAoB,EAAEY,eAAe,EAAED,IAAI,CAAC;EAC7E;AACF;AAKO,SAASG,QAAQA,CAACH,IAAgB,EAAQ;EAC/C,IAAIT,SAAS,CAAC,CAAC,EAAE;IACfC,MAAM,CAACY,qBAAqB,CAACf,oBAAoB,EAAEW,IAAI,CAAC;EAC1D;AACF;AAOO,SAASK,eAAeA,CAC7BN,SAAoB,EACpBC,IAAgB,EACR;EACR,IAAMM,MAAM,GAAGhB,YAAY;EAC3B,IAAIC,SAAS,CAAC,CAAC,EAAE;IACfD,YAAY,EAAE;IACd,IAAMW,eAAe,GACnB,OAAOF,SAAS,KAAK,UAAU,GAAGA,SAAS,CAAC,CAAC,GAAGA,SAAS;IAC3DP,MAAM,CAACe,4BAA4B,CACjClB,oBAAoB,EACpBY,eAAe,EACfK,MAAM,EACNN,IACF,CAAC;EACH;EACA,OAAOM,MAAM;AACf;AAMO,SAASE,aAAaA,CAC3BT,SAAoB,EACpBO,MAAc,EACdN,IAAgB,EACV;EACN,IAAIT,SAAS,CAAC,CAAC,EAAE;IACf,IAAMU,eAAe,GACnB,OAAOF,SAAS,KAAK,UAAU,GAAGA,SAAS,CAAC,CAAC,GAAGA,SAAS;IAC3DP,MAAM,CAACiB,0BAA0B,CAC/BpB,oBAAoB,EACpBY,eAAe,EACfK,MAAM,EACNN,IACF,CAAC;EACH;AACF;AAKO,SAASU,YAAYA,CAACX,SAAoB,EAAEY,KAAa,EAAQ;EACtE,IAAIpB,SAAS,CAAC,CAAC,EAAE;IACf,IAAMU,eAAe,GACnB,OAAOF,SAAS,KAAK,UAAU,GAAGA,SAAS,CAAC,CAAC,GAAGA,SAAS;IAC3DP,MAAM,CAACoB,kBAAkB,IACvBpB,MAAM,CAACoB,kBAAkB,CAACvB,oBAAoB,EAAEY,eAAe,EAAEU,KAAK,CAAC;EAC3E;AACF;AAEA,IAAIE,OAAO,EAAE;EACX,IAAMC,QAAwB,GAAG;IAC/BvB,SAAS,EAATA,SAAS;IACTK,UAAU,EAAVA,UAAU;IACVE,UAAU,EAAVA,UAAU;IACVK,QAAQ,EAARA,QAAQ;IACRE,eAAe,EAAfA,eAAe;IACfG,aAAa,EAAbA,aAAa;IACbE,YAAY,EAAZA;EACF,CAAC;EAIDlB,MAAM,CAAC,CAACA,MAAM,CAACuB,uBAAuB,IAAI,EAAE,IAAI,YAAY,CAAC,GAAGD,QAAQ;AAC1E", "ignoreList": []}