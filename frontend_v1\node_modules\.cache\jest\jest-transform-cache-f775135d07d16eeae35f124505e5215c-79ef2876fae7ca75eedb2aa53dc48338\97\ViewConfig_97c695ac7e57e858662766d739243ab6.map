{"version": 3, "names": ["_PlatformBaseViewConfig", "_interopRequireDefault", "require", "createViewConfig", "partialViewConfig", "uiViewClassName", "Commands", "bubblingEventTypes", "composeIndexers", "PlatformBaseViewConfig", "directEventTypes", "validAttributes", "maybeA", "maybeB", "_ref", "Object", "assign"], "sources": ["ViewConfig.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n */\n\nimport type {\n  PartialViewConfig,\n  ViewConfig,\n} from '../Renderer/shims/ReactNativeTypes';\n\nimport PlatformBaseViewConfig from './PlatformBaseViewConfig';\n\n/**\n * Creates a complete `ViewConfig` from a `PartialViewConfig`.\n */\nexport function createViewConfig(\n  partialViewConfig: PartialViewConfig,\n): ViewConfig {\n  return {\n    uiViewClassName: partialViewConfig.uiViewClassName,\n    Commands: {},\n    bubblingEventTypes: composeIndexers(\n      PlatformBaseViewConfig.bubblingEventTypes,\n      partialViewConfig.bubblingEventTypes,\n    ),\n    directEventTypes: composeIndexers(\n      PlatformBaseViewConfig.directEventTypes,\n      partialViewConfig.directEventTypes,\n    ),\n    // $FlowFixMe[incompatible-return]\n    validAttributes: composeIndexers(\n      // $FlowFixMe[incompatible-call] `style` property confuses Flow.\n      PlatformBaseViewConfig.validAttributes,\n      // $FlowFixMe[incompatible-call] `style` property confuses Flow.\n      partialViewConfig.validAttributes,\n    ),\n  };\n}\n\nfunction composeIndexers<T>(\n  maybeA: ?{+[string]: T},\n  maybeB: ?{+[string]: T},\n): {+[string]: T} {\n  return maybeA == null || maybeB == null\n    ? maybeA ?? maybeB ?? {}\n    : {...maybeA, ...maybeB};\n}\n"], "mappings": ";;;;;AAeA,IAAAA,uBAAA,GAAAC,sBAAA,CAAAC,OAAA;AAKO,SAASC,gBAAgBA,CAC9BC,iBAAoC,EACxB;EACZ,OAAO;IACLC,eAAe,EAAED,iBAAiB,CAACC,eAAe;IAClDC,QAAQ,EAAE,CAAC,CAAC;IACZC,kBAAkB,EAAEC,eAAe,CACjCC,+BAAsB,CAACF,kBAAkB,EACzCH,iBAAiB,CAACG,kBACpB,CAAC;IACDG,gBAAgB,EAAEF,eAAe,CAC/BC,+BAAsB,CAACC,gBAAgB,EACvCN,iBAAiB,CAACM,gBACpB,CAAC;IAEDC,eAAe,EAAEH,eAAe,CAE9BC,+BAAsB,CAACE,eAAe,EAEtCP,iBAAiB,CAACO,eACpB;EACF,CAAC;AACH;AAEA,SAASH,eAAeA,CACtBI,MAAuB,EACvBC,MAAuB,EACP;EAAA,IAAAC,IAAA;EAChB,OAAOF,MAAM,IAAI,IAAI,IAAIC,MAAM,IAAI,IAAI,IAAAC,IAAA,GACnCF,MAAM,WAANA,MAAM,GAAIC,MAAM,YAAAC,IAAA,GAAI,CAAC,CAAC,GAAAC,MAAA,CAAAC,MAAA,KAClBJ,MAAM,EAAKC,MAAM,CAAC;AAC5B", "ignoreList": []}