/**
 * Integration Testing Utilities - End-to-End Testing Support
 *
 * Utilities Contract:
 * - Provides comprehensive integration testing utilities
 * - Supports real-time feature testing with WebSocket mocks
 * - Implements booking flow testing utilities
 * - Provides performance testing helpers
 * - Supports error handling testing scenarios
 * - Includes accessibility testing utilities
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { ReactElement } from 'react';
import { render, fireEvent, waitFor, act } from '@testing-library/react-native';
import { NavigationContainer } from '@react-navigation/native';
import { Provider } from 'react-redux';

import { ThemeProvider } from '../contexts/ThemeContext';
import { useAuthStore } from '../store/authSlice';
import { realTimeNotificationService } from '../services/realTimeNotificationService';
import { errorHandlingService } from '../services/errorHandlingService';
import { performanceOptimizationService } from '../services/performanceOptimizationService';

// Mock WebSocket for real-time testing
export class MockWebSocket {
  private listeners: Map<string, Set<Function>> = new Map();
  private isConnected = false;

  constructor(public url: string) {}

  connect() {
    this.isConnected = true;
    this.emit('connect', {});
  }

  disconnect() {
    this.isConnected = false;
    this.emit('disconnect', {});
  }

  send(data: any) {
    // Simulate sending data
    console.log('MockWebSocket send:', data);
  }

  on(event: string, callback: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event)!.add(callback);
  }

  off(event: string, callback: Function) {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.delete(callback);
    }
  }

  emit(event: string, data: any) {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach(callback => callback(data));
    }
  }

  // Test utilities
  simulateBookingUpdate(bookingId: string, status: string) {
    this.emit('booking_update', {
      bookingId,
      status,
      message: `Booking ${status}`,
      timestamp: new Date().toISOString(),
    });
  }

  simulateNewMessage(conversationId: string, message: string) {
    this.emit('new_message', {
      conversationId,
      senderId: 'test_sender',
      senderName: 'Test Sender',
      message,
      messageType: 'text',
    });
  }

  simulateProviderLocation(bookingId: string, location: { latitude: number; longitude: number }) {
    this.emit('provider_location_update', {
      bookingId,
      location,
    });
  }
}

// Mock API responses
export const mockApiResponses = {
  // Booking API responses
  createBooking: {
    id: 'test_booking_123',
    status: 'confirmed',
    providerId: 'test_provider_123',
    serviceId: 'test_service_123',
    scheduledDate: '2024-01-15',
    scheduledTime: '10:00',
    totalAmount: 50.00,
  },

  getBookingDetails: {
    id: 'test_booking_123',
    status: 'confirmed',
    provider: {
      id: 'test_provider_123',
      name: 'Test Provider',
      phone: '+**********',
      profileImage: 'https://example.com/avatar.jpg',
    },
    service: {
      name: 'Test Service',
      duration: 60,
      price: 50.00,
    },
    customerLocation: {
      latitude: 45.4215,
      longitude: -75.6972,
    },
    estimatedArrival: '2024-01-15T10:30:00Z',
  },

  // Provider API responses
  getProviders: [
    {
      id: 'test_provider_1',
      name: 'Test Provider 1',
      rating: 4.8,
      reviewCount: 125,
      services: ['haircut', 'styling'],
      location: { latitude: 45.4215, longitude: -75.6972 },
    },
    {
      id: 'test_provider_2',
      name: 'Test Provider 2',
      rating: 4.6,
      reviewCount: 89,
      services: ['massage', 'therapy'],
      location: { latitude: 45.4225, longitude: -75.6982 },
    },
  ],

  // Service API responses
  getServices: [
    {
      id: 'test_service_1',
      name: 'Haircut',
      description: 'Professional haircut service',
      price: 30.00,
      duration: 45,
      category: 'Hair',
    },
    {
      id: 'test_service_2',
      name: 'Massage',
      description: 'Relaxing massage therapy',
      price: 80.00,
      duration: 90,
      category: 'Wellness',
    },
  ],

  // Search API responses
  searchResults: {
    providers: [],
    services: [],
    total: 0,
    page: 1,
    limit: 20,
  },
};

// Test data factories
export const createTestUser = (overrides = {}) => ({
  id: 'test_user_123',
  firstName: 'Test',
  lastName: 'User',
  email: '<EMAIL>',
  phone: '+**********',
  profileImage: null,
  ...overrides,
});

export const createTestBooking = (overrides = {}) => ({
  id: 'test_booking_123',
  status: 'confirmed',
  providerId: 'test_provider_123',
  serviceId: 'test_service_123',
  scheduledDate: '2024-01-15',
  scheduledTime: '10:00',
  totalAmount: 50.00,
  ...overrides,
});

export const createTestProvider = (overrides = {}) => ({
  id: 'test_provider_123',
  name: 'Test Provider',
  businessName: 'Test Business',
  description: 'Test provider description',
  rating: 4.8,
  reviewCount: 125,
  isVerified: true,
  isOnline: true,
  avatar: 'https://example.com/avatar.jpg',
  coverImage: 'https://example.com/cover.jpg',
  contact: {
    phone: '+**********',
    email: '<EMAIL>',
  },
  location: {
    latitude: 45.4215,
    longitude: -75.6972,
    address: '123 Test Street, Test City',
  },
  ...overrides,
});

// Integration test helpers
export const setupIntegrationTest = () => {
  const mockWebSocket = new MockWebSocket('ws://test');
  
  // Mock services
  jest.spyOn(realTimeNotificationService, 'initialize').mockResolvedValue(undefined);
  jest.spyOn(realTimeNotificationService, 'isServiceConnected').mockReturnValue(true);
  
  return {
    mockWebSocket,
    cleanup: () => {
      jest.restoreAllMocks();
    },
  };
};

// Booking flow test utilities
export const testBookingFlow = {
  async selectService(getByTestId: any, serviceId: string) {
    const serviceCard = getByTestId(`service-card-${serviceId}`);
    fireEvent.press(serviceCard);
    
    const continueButton = getByTestId('continue-button');
    fireEvent.press(continueButton);
    
    await waitFor(() => {
      expect(getByTestId('time-slot-selection')).toBeTruthy();
    });
  },

  async selectTimeSlot(getByTestId: any, timeSlot: string) {
    const timeSlotButton = getByTestId(`time-slot-${timeSlot}`);
    fireEvent.press(timeSlotButton);
    
    const continueButton = getByTestId('continue-button');
    fireEvent.press(continueButton);
    
    await waitFor(() => {
      expect(getByTestId('customer-info-form')).toBeTruthy();
    });
  },

  async fillCustomerInfo(getByTestId: any, info: any) {
    const firstNameInput = getByTestId('first-name-input');
    const lastNameInput = getByTestId('last-name-input');
    const emailInput = getByTestId('email-input');
    const phoneInput = getByTestId('phone-input');
    
    fireEvent.changeText(firstNameInput, info.firstName);
    fireEvent.changeText(lastNameInput, info.lastName);
    fireEvent.changeText(emailInput, info.email);
    fireEvent.changeText(phoneInput, info.phone);
    
    const continueButton = getByTestId('continue-button');
    fireEvent.press(continueButton);
    
    await waitFor(() => {
      expect(getByTestId('payment-selection')).toBeTruthy();
    });
  },

  async selectPaymentMethod(getByTestId: any, paymentMethodId: string) {
    const paymentMethod = getByTestId(`payment-method-${paymentMethodId}`);
    fireEvent.press(paymentMethod);
    
    const continueButton = getByTestId('continue-button');
    fireEvent.press(continueButton);
    
    await waitFor(() => {
      expect(getByTestId('booking-summary')).toBeTruthy();
    });
  },

  async confirmBooking(getByTestId: any) {
    const confirmButton = getByTestId('confirm-booking-button');
    fireEvent.press(confirmButton);
    
    await waitFor(() => {
      expect(getByTestId('booking-confirmation')).toBeTruthy();
    });
  },
};

// Performance testing utilities
export const performanceTestUtils = {
  async measureRenderTime(component: ReactElement): Promise<number> {
    const startTime = performance.now();
    
    await act(async () => {
      render(component);
    });
    
    const endTime = performance.now();
    return endTime - startTime;
  },

  async measureInteractionTime(getByTestId: any, testId: string): Promise<number> {
    const startTime = performance.now();
    
    await act(async () => {
      const element = getByTestId(testId);
      fireEvent.press(element);
    });
    
    const endTime = performance.now();
    return endTime - startTime;
  },

  expectPerformanceWithinBounds(actualTime: number, maxTime: number) {
    expect(actualTime).toBeLessThan(maxTime);
  },
};

// Error handling test utilities
export const errorTestUtils = {
  simulateNetworkError() {
    return new Error('Network request failed');
  },

  simulateValidationError(field: string) {
    const error = new Error(`Validation failed for ${field}`);
    error.name = 'ValidationError';
    return error;
  },

  simulateAuthenticationError() {
    const error = new Error('Authentication failed');
    error.name = 'AuthenticationError';
    return error;
  },

  async testErrorRecovery(getByTestId: any, errorType: string) {
    // Simulate error
    const error = this.simulateNetworkError();
    
    // Trigger error
    await act(async () => {
      throw error;
    });
    
    // Check error message is displayed
    await waitFor(() => {
      expect(getByTestId('error-message')).toBeTruthy();
    });
    
    // Test retry functionality
    const retryButton = getByTestId('retry-button');
    fireEvent.press(retryButton);
    
    await waitFor(() => {
      expect(getByTestId('loading-indicator')).toBeTruthy();
    });
  },
};

// Accessibility testing utilities
export const accessibilityTestUtils = {
  checkAccessibilityLabels(getByTestId: any, testIds: string[]) {
    testIds.forEach(testId => {
      const element = getByTestId(testId);
      expect(element.props.accessibilityLabel).toBeTruthy();
    });
  },

  checkAccessibilityRoles(getByTestId: any, testIds: string[]) {
    testIds.forEach(testId => {
      const element = getByTestId(testId);
      expect(element.props.accessibilityRole).toBeTruthy();
    });
  },

  checkMinimumTouchTargets(getByTestId: any, testIds: string[]) {
    testIds.forEach(testId => {
      const element = getByTestId(testId);
      const style = element.props.style;
      
      // Check minimum touch target size (44x44 points)
      if (style && (style.width || style.height)) {
        expect(style.width).toBeGreaterThanOrEqual(44);
        expect(style.height).toBeGreaterThanOrEqual(44);
      }
    });
  },
};

// Custom render with integration test setup
export const renderWithIntegrationSetup = (
  ui: ReactElement,
  options: any = {}
) => {
  const { mockWebSocket, cleanup } = setupIntegrationTest();
  
  const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
    return (
      <Provider store={options.store || createMockStore()}>
        <ThemeProvider>
          <NavigationContainer>
            {children}
          </NavigationContainer>
        </ThemeProvider>
      </Provider>
    );
  };

  const result = render(ui, { wrapper: AllTheProviders, ...options });
  
  return {
    ...result,
    mockWebSocket,
    cleanup,
  };
};

// Mock store factory for integration tests
const createMockStore = (initialState = {}) => {
  return {
    getState: () => ({
      auth: {
        user: createTestUser(),
        authToken: 'test_token',
        isAuthenticated: true,
      },
      bookings: {
        bookings: [createTestBooking()],
        loading: false,
      },
      ...initialState,
    }),
    dispatch: jest.fn(),
    subscribe: jest.fn(),
  };
};

export default {
  setupIntegrationTest,
  testBookingFlow,
  performanceTestUtils,
  errorTestUtils,
  accessibilityTestUtils,
  renderWithIntegrationSetup,
  mockApiResponses,
  createTestUser,
  createTestBooking,
  createTestProvider,
};
