{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "default", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_get2", "_inherits2", "_classPrivateFieldLooseBase2", "_classPrivateFieldLooseKey2", "_NativeAnimatedHelper", "_NativeAnimatedValidation", "_AnimatedNode", "_AnimatedWithChildren2", "_callSuper", "t", "o", "e", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_superPropGet", "r", "p", "flatAnimatedNodes", "transforms", "nodes", "ii", "length", "transform", "key", "AnimatedNode", "push", "_nodes", "AnimatedTransform", "_Animated<PERSON>ith<PERSON><PERSON><PERSON><PERSON>", "config", "_this", "writable", "_transforms", "__makeNative", "platformConfig", "node", "__getValue", "mapTransforms", "animatedNode", "__getValueWithStaticTransforms", "staticTransforms", "values", "shift", "__getAnimatedValue", "__attach", "__add<PERSON><PERSON>d", "__detach", "__remove<PERSON><PERSON>d", "__getNativeConfig", "transformsConfig", "type", "property", "nodeTag", "__getNativeTag", "NativeAnimatedHelper", "transformDataType", "__DEV__", "validateTransform", "debugID", "__getDebugID", "from", "Array", "isArray", "AnimatedWithChildren", "mapFunction", "map", "result", "element", "object", "propertyName", "propertyValue"], "sources": ["AnimatedTransform.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n * @format\n */\n\n'use strict';\n\nimport type {PlatformConfig} from '../AnimatedPlatformConfig';\nimport type {AnimatedNodeConfig} from './AnimatedNode';\n\nimport NativeAnimatedHelper from '../../../src/private/animated/NativeAnimatedHelper';\nimport {validateTransform} from '../../../src/private/animated/NativeAnimatedValidation';\nimport AnimatedNode from './AnimatedNode';\nimport AnimatedWithChildren from './AnimatedWithChildren';\n\ntype Transform<T = AnimatedNode> = {\n  [string]:\n    | number\n    | string\n    | T\n    | $ReadOnlyArray<number | string | T>\n    | {[string]: number | string | T},\n};\n\nfunction flatAnimatedNodes(\n  transforms: $ReadOnlyArray<Transform<>>,\n): Array<AnimatedNode> {\n  const nodes = [];\n  for (let ii = 0, length = transforms.length; ii < length; ii++) {\n    const transform = transforms[ii];\n    // There should be exactly one property in `transform`.\n    for (const key in transform) {\n      const value = transform[key];\n      if (value instanceof AnimatedNode) {\n        nodes.push(value);\n      }\n    }\n  }\n  return nodes;\n}\n\nexport default class AnimatedTransform extends AnimatedWithChildren {\n  // NOTE: For potentially historical reasons, some operations only operate on\n  // the first level of AnimatedNode instances. This optimizes that bevavior.\n  #nodes: $ReadOnlyArray<AnimatedNode>;\n\n  _transforms: $ReadOnlyArray<Transform<>>;\n\n  /**\n   * Creates an `AnimatedTransform` if `transforms` contains `AnimatedNode`\n   * instances. Otherwise, returns `null`.\n   */\n  static from(transforms: $ReadOnlyArray<Transform<>>): ?AnimatedTransform {\n    const nodes = flatAnimatedNodes(\n      // NOTE: This check should not be necessary, but the types are not\n      // enforced as of this writing. This check should be hoisted to\n      // instantiation sites.\n      Array.isArray(transforms) ? transforms : [],\n    );\n    if (nodes.length === 0) {\n      return null;\n    }\n    return new AnimatedTransform(nodes, transforms);\n  }\n\n  constructor(\n    nodes: $ReadOnlyArray<AnimatedNode>,\n    transforms: $ReadOnlyArray<Transform<>>,\n    config?: ?AnimatedNodeConfig,\n  ) {\n    super(config);\n    this.#nodes = nodes;\n    this._transforms = transforms;\n  }\n\n  __makeNative(platformConfig: ?PlatformConfig) {\n    const nodes = this.#nodes;\n    for (let ii = 0, length = nodes.length; ii < length; ii++) {\n      const node = nodes[ii];\n      node.__makeNative(platformConfig);\n    }\n    super.__makeNative(platformConfig);\n  }\n\n  __getValue(): $ReadOnlyArray<Transform<any>> {\n    return mapTransforms(this._transforms, animatedNode =>\n      animatedNode.__getValue(),\n    );\n  }\n\n  __getValueWithStaticTransforms(\n    staticTransforms: $ReadOnlyArray<Object>,\n  ): $ReadOnlyArray<Object> {\n    const values = [];\n    mapTransforms(this._transforms, node => {\n      values.push(node.__getValue());\n    });\n    // NOTE: We can depend on `this._transforms` and `staticTransforms` sharing\n    // a structure because of `useAnimatedPropsMemo`.\n    return mapTransforms(staticTransforms, () => values.shift());\n  }\n\n  __getAnimatedValue(): $ReadOnlyArray<Transform<any>> {\n    return mapTransforms(this._transforms, animatedNode =>\n      animatedNode.__getAnimatedValue(),\n    );\n  }\n\n  __attach(): void {\n    const nodes = this.#nodes;\n    for (let ii = 0, length = nodes.length; ii < length; ii++) {\n      const node = nodes[ii];\n      node.__addChild(this);\n    }\n    super.__attach();\n  }\n\n  __detach(): void {\n    const nodes = this.#nodes;\n    for (let ii = 0, length = nodes.length; ii < length; ii++) {\n      const node = nodes[ii];\n      node.__removeChild(this);\n    }\n    super.__detach();\n  }\n\n  __getNativeConfig(): any {\n    const transformsConfig: Array<any> = [];\n\n    const transforms = this._transforms;\n    for (let ii = 0, length = transforms.length; ii < length; ii++) {\n      const transform = transforms[ii];\n      // There should be exactly one property in `transform`.\n      for (const key in transform) {\n        const value = transform[key];\n        if (value instanceof AnimatedNode) {\n          transformsConfig.push({\n            type: 'animated',\n            property: key,\n            nodeTag: value.__getNativeTag(),\n          });\n        } else {\n          transformsConfig.push({\n            type: 'static',\n            property: key,\n            /* $FlowFixMe[incompatible-call] - `value` can be an array or an\n               object. This is not currently handled by `transformDataType`.\n               Migrating to `TransformObject` might solve this. */\n            value: NativeAnimatedHelper.transformDataType(value),\n          });\n        }\n      }\n    }\n\n    if (__DEV__) {\n      validateTransform(transformsConfig);\n    }\n    return {\n      type: 'transform',\n      transforms: transformsConfig,\n      debugID: this.__getDebugID(),\n    };\n  }\n}\n\nfunction mapTransforms<T>(\n  transforms: $ReadOnlyArray<Transform<>>,\n  mapFunction: AnimatedNode => T,\n): $ReadOnlyArray<Transform<T>> {\n  return transforms.map(transform => {\n    const result: Transform<T> = {};\n    // There should be exactly one property in `transform`.\n    for (const key in transform) {\n      const value = transform[key];\n      if (value instanceof AnimatedNode) {\n        result[key] = mapFunction(value);\n      } else if (Array.isArray(value)) {\n        result[key] = value.map(element =>\n          element instanceof AnimatedNode ? mapFunction(element) : element,\n        );\n      } else if (typeof value === 'object') {\n        const object: {[string]: number | string | T} = {};\n        for (const propertyName in value) {\n          const propertyValue = value[propertyName];\n          object[propertyName] =\n            propertyValue instanceof AnimatedNode\n              ? mapFunction(propertyValue)\n              : propertyValue;\n        }\n        result[key] = object;\n      } else {\n        result[key] = value;\n      }\n    }\n    return result;\n  });\n}\n"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAAA,IAAAC,gBAAA,GAAAP,sBAAA,CAAAC,OAAA;AAAA,IAAAO,aAAA,GAAAR,sBAAA,CAAAC,OAAA;AAAA,IAAAQ,2BAAA,GAAAT,sBAAA,CAAAC,OAAA;AAAA,IAAAS,gBAAA,GAAAV,sBAAA,CAAAC,OAAA;AAAA,IAAAU,KAAA,GAAAX,sBAAA,CAAAC,OAAA;AAAA,IAAAW,UAAA,GAAAZ,sBAAA,CAAAC,OAAA;AAAA,IAAAY,4BAAA,GAAAb,sBAAA,CAAAC,OAAA;AAAA,IAAAa,2BAAA,GAAAd,sBAAA,CAAAC,OAAA;AAKb,IAAAc,qBAAA,GAAAf,sBAAA,CAAAC,OAAA;AACA,IAAAe,yBAAA,GAAAf,OAAA;AACA,IAAAgB,aAAA,GAAAjB,sBAAA,CAAAC,OAAA;AACA,IAAAiB,sBAAA,GAAAlB,sBAAA,CAAAC,OAAA;AAA0D,SAAAkB,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAX,gBAAA,CAAAJ,OAAA,EAAAe,CAAA,OAAAZ,2BAAA,CAAAH,OAAA,EAAAc,CAAA,EAAAG,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAJ,CAAA,EAAAC,CAAA,YAAAZ,gBAAA,CAAAJ,OAAA,EAAAc,CAAA,EAAAM,WAAA,IAAAL,CAAA,CAAAM,KAAA,CAAAP,CAAA,EAAAE,CAAA;AAAA,SAAAC,0BAAA,cAAAH,CAAA,IAAAQ,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAR,CAAA,aAAAG,yBAAA,YAAAA,0BAAA,aAAAH,CAAA;AAAA,SAAAY,cAAAZ,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAW,CAAA,QAAAC,CAAA,OAAAvB,KAAA,CAAAL,OAAA,MAAAI,gBAAA,CAAAJ,OAAA,MAAA2B,CAAA,GAAAb,CAAA,CAAAS,SAAA,GAAAT,CAAA,GAAAC,CAAA,EAAAC,CAAA,cAAAW,CAAA,yBAAAC,CAAA,aAAAd,CAAA,WAAAc,CAAA,CAAAP,KAAA,CAAAL,CAAA,EAAAF,CAAA,OAAAc,CAAA;AAW1D,SAASC,iBAAiBA,CACxBC,UAAuC,EAClB;EACrB,IAAMC,KAAK,GAAG,EAAE;EAChB,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEC,MAAM,GAAGH,UAAU,CAACG,MAAM,EAAED,EAAE,GAAGC,MAAM,EAAED,EAAE,EAAE,EAAE;IAC9D,IAAME,SAAS,GAAGJ,UAAU,CAACE,EAAE,CAAC;IAEhC,KAAK,IAAMG,GAAG,IAAID,SAAS,EAAE;MAC3B,IAAMnC,KAAK,GAAGmC,SAAS,CAACC,GAAG,CAAC;MAC5B,IAAIpC,KAAK,YAAYqC,qBAAY,EAAE;QACjCL,KAAK,CAACM,IAAI,CAACtC,KAAK,CAAC;MACnB;IACF;EACF;EACA,OAAOgC,KAAK;AACd;AAAC,IAAAO,MAAA,OAAA9B,2BAAA,CAAAR,OAAA;AAAA,IAEoBuC,iBAAiB,GAAAzC,OAAA,CAAAE,OAAA,aAAAwC,qBAAA;EAwBpC,SAAAD,kBACER,KAAmC,EACnCD,UAAuC,EACvCW,MAA4B,EAC5B;IAAA,IAAAC,KAAA;IAAA,IAAAzC,gBAAA,CAAAD,OAAA,QAAAuC,iBAAA;IACAG,KAAA,GAAA7B,UAAA,OAAA0B,iBAAA,GAAME,MAAM;IAAE7C,MAAA,CAAAC,cAAA,CAAA6C,KAAA,EAAAJ,MAAA;MAAAK,QAAA;MAAA5C,KAAA;IAAA;IACd,IAAAQ,4BAAA,CAAAP,OAAA,EAAA0C,KAAA,EAAAJ,MAAA,EAAAA,MAAA,IAAcP,KAAK;IACnBW,KAAA,CAAKE,WAAW,GAAGd,UAAU;IAAC,OAAAY,KAAA;EAChC;EAAC,IAAApC,UAAA,CAAAN,OAAA,EAAAuC,iBAAA,EAAAC,qBAAA;EAAA,WAAAtC,aAAA,CAAAF,OAAA,EAAAuC,iBAAA;IAAAJ,GAAA;IAAApC,KAAA,EAED,SAAA8C,YAAYA,CAACC,cAA+B,EAAE;MAC5C,IAAMf,KAAK,OAAAxB,4BAAA,CAAAP,OAAA,EAAG,IAAI,EAAAsC,MAAA,EAAAA,MAAA,CAAO;MACzB,KAAK,IAAIN,EAAE,GAAG,CAAC,EAAEC,MAAM,GAAGF,KAAK,CAACE,MAAM,EAAED,EAAE,GAAGC,MAAM,EAAED,EAAE,EAAE,EAAE;QACzD,IAAMe,IAAI,GAAGhB,KAAK,CAACC,EAAE,CAAC;QACtBe,IAAI,CAACF,YAAY,CAACC,cAAc,CAAC;MACnC;MACApB,aAAA,CAAAa,iBAAA,4BAAmBO,cAAc;IACnC;EAAC;IAAAX,GAAA;IAAApC,KAAA,EAED,SAAAiD,UAAUA,CAAA,EAAmC;MAC3C,OAAOC,aAAa,CAAC,IAAI,CAACL,WAAW,EAAE,UAAAM,YAAY;QAAA,OACjDA,YAAY,CAACF,UAAU,CAAC,CAAC;MAAA,CAC3B,CAAC;IACH;EAAC;IAAAb,GAAA;IAAApC,KAAA,EAED,SAAAoD,8BAA8BA,CAC5BC,gBAAwC,EAChB;MACxB,IAAMC,MAAM,GAAG,EAAE;MACjBJ,aAAa,CAAC,IAAI,CAACL,WAAW,EAAE,UAAAG,IAAI,EAAI;QACtCM,MAAM,CAAChB,IAAI,CAACU,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC;MAGF,OAAOC,aAAa,CAACG,gBAAgB,EAAE;QAAA,OAAMC,MAAM,CAACC,KAAK,CAAC,CAAC;MAAA,EAAC;IAC9D;EAAC;IAAAnB,GAAA;IAAApC,KAAA,EAED,SAAAwD,kBAAkBA,CAAA,EAAmC;MACnD,OAAON,aAAa,CAAC,IAAI,CAACL,WAAW,EAAE,UAAAM,YAAY;QAAA,OACjDA,YAAY,CAACK,kBAAkB,CAAC,CAAC;MAAA,CACnC,CAAC;IACH;EAAC;IAAApB,GAAA;IAAApC,KAAA,EAED,SAAAyD,QAAQA,CAAA,EAAS;MACf,IAAMzB,KAAK,OAAAxB,4BAAA,CAAAP,OAAA,EAAG,IAAI,EAAAsC,MAAA,EAAAA,MAAA,CAAO;MACzB,KAAK,IAAIN,EAAE,GAAG,CAAC,EAAEC,MAAM,GAAGF,KAAK,CAACE,MAAM,EAAED,EAAE,GAAGC,MAAM,EAAED,EAAE,EAAE,EAAE;QACzD,IAAMe,IAAI,GAAGhB,KAAK,CAACC,EAAE,CAAC;QACtBe,IAAI,CAACU,UAAU,CAAC,IAAI,CAAC;MACvB;MACA/B,aAAA,CAAAa,iBAAA;IACF;EAAC;IAAAJ,GAAA;IAAApC,KAAA,EAED,SAAA2D,QAAQA,CAAA,EAAS;MACf,IAAM3B,KAAK,OAAAxB,4BAAA,CAAAP,OAAA,EAAG,IAAI,EAAAsC,MAAA,EAAAA,MAAA,CAAO;MACzB,KAAK,IAAIN,EAAE,GAAG,CAAC,EAAEC,MAAM,GAAGF,KAAK,CAACE,MAAM,EAAED,EAAE,GAAGC,MAAM,EAAED,EAAE,EAAE,EAAE;QACzD,IAAMe,IAAI,GAAGhB,KAAK,CAACC,EAAE,CAAC;QACtBe,IAAI,CAACY,aAAa,CAAC,IAAI,CAAC;MAC1B;MACAjC,aAAA,CAAAa,iBAAA;IACF;EAAC;IAAAJ,GAAA;IAAApC,KAAA,EAED,SAAA6D,iBAAiBA,CAAA,EAAQ;MACvB,IAAMC,gBAA4B,GAAG,EAAE;MAEvC,IAAM/B,UAAU,GAAG,IAAI,CAACc,WAAW;MACnC,KAAK,IAAIZ,EAAE,GAAG,CAAC,EAAEC,MAAM,GAAGH,UAAU,CAACG,MAAM,EAAED,EAAE,GAAGC,MAAM,EAAED,EAAE,EAAE,EAAE;QAC9D,IAAME,SAAS,GAAGJ,UAAU,CAACE,EAAE,CAAC;QAEhC,KAAK,IAAMG,GAAG,IAAID,SAAS,EAAE;UAC3B,IAAMnC,KAAK,GAAGmC,SAAS,CAACC,GAAG,CAAC;UAC5B,IAAIpC,KAAK,YAAYqC,qBAAY,EAAE;YACjCyB,gBAAgB,CAACxB,IAAI,CAAC;cACpByB,IAAI,EAAE,UAAU;cAChBC,QAAQ,EAAE5B,GAAG;cACb6B,OAAO,EAAEjE,KAAK,CAACkE,cAAc,CAAC;YAChC,CAAC,CAAC;UACJ,CAAC,MAAM;YACLJ,gBAAgB,CAACxB,IAAI,CAAC;cACpByB,IAAI,EAAE,QAAQ;cACdC,QAAQ,EAAE5B,GAAG;cAIbpC,KAAK,EAAEmE,6BAAoB,CAACC,iBAAiB,CAACpE,KAAK;YACrD,CAAC,CAAC;UACJ;QACF;MACF;MAEA,IAAIqE,OAAO,EAAE;QACX,IAAAC,2CAAiB,EAACR,gBAAgB,CAAC;MACrC;MACA,OAAO;QACLC,IAAI,EAAE,WAAW;QACjBhC,UAAU,EAAE+B,gBAAgB;QAC5BS,OAAO,EAAE,IAAI,CAACC,YAAY,CAAC;MAC7B,CAAC;IACH;EAAC;IAAApC,GAAA;IAAApC,KAAA,EA9GD,SAAOyE,IAAIA,CAAC1C,UAAuC,EAAsB;MACvE,IAAMC,KAAK,GAAGF,iBAAiB,CAI7B4C,KAAK,CAACC,OAAO,CAAC5C,UAAU,CAAC,GAAGA,UAAU,GAAG,EAC3C,CAAC;MACD,IAAIC,KAAK,CAACE,MAAM,KAAK,CAAC,EAAE;QACtB,OAAO,IAAI;MACb;MACA,OAAO,IAAIM,iBAAiB,CAACR,KAAK,EAAED,UAAU,CAAC;IACjD;EAAC;AAAA,EAtB4C6C,8BAAoB;AA4HnE,SAAS1B,aAAaA,CACpBnB,UAAuC,EACvC8C,WAA8B,EACA;EAC9B,OAAO9C,UAAU,CAAC+C,GAAG,CAAC,UAAA3C,SAAS,EAAI;IACjC,IAAM4C,MAAoB,GAAG,CAAC,CAAC;IAE/B,KAAK,IAAM3C,GAAG,IAAID,SAAS,EAAE;MAC3B,IAAMnC,KAAK,GAAGmC,SAAS,CAACC,GAAG,CAAC;MAC5B,IAAIpC,KAAK,YAAYqC,qBAAY,EAAE;QACjC0C,MAAM,CAAC3C,GAAG,CAAC,GAAGyC,WAAW,CAAC7E,KAAK,CAAC;MAClC,CAAC,MAAM,IAAI0E,KAAK,CAACC,OAAO,CAAC3E,KAAK,CAAC,EAAE;QAC/B+E,MAAM,CAAC3C,GAAG,CAAC,GAAGpC,KAAK,CAAC8E,GAAG,CAAC,UAAAE,OAAO;UAAA,OAC7BA,OAAO,YAAY3C,qBAAY,GAAGwC,WAAW,CAACG,OAAO,CAAC,GAAGA,OAAO;QAAA,CAClE,CAAC;MACH,CAAC,MAAM,IAAI,OAAOhF,KAAK,KAAK,QAAQ,EAAE;QACpC,IAAMiF,MAAuC,GAAG,CAAC,CAAC;QAClD,KAAK,IAAMC,YAAY,IAAIlF,KAAK,EAAE;UAChC,IAAMmF,aAAa,GAAGnF,KAAK,CAACkF,YAAY,CAAC;UACzCD,MAAM,CAACC,YAAY,CAAC,GAClBC,aAAa,YAAY9C,qBAAY,GACjCwC,WAAW,CAACM,aAAa,CAAC,GAC1BA,aAAa;QACrB;QACAJ,MAAM,CAAC3C,GAAG,CAAC,GAAG6C,MAAM;MACtB,CAAC,MAAM;QACLF,MAAM,CAAC3C,GAAG,CAAC,GAAGpC,KAAK;MACrB;IACF;IACA,OAAO+E,MAAM;EACf,CAAC,CAAC;AACJ", "ignoreList": []}