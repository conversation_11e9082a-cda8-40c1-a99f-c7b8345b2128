/**
 * Checkout Types
 *
 * TypeScript interfaces and types for checkout functionality
 * Aligned with Stripe Payment Intents and backend API structure
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

// Base types
export interface BaseEntity {
  id: string;
  created_at: string;
  updated_at: string;
}

// Service booking information
export interface ServiceBooking {
  serviceId: string;
  providerId: string;
  serviceName: string;
  providerName: string;
  price: number;
  duration: number;
  selectedDate: string;
  selectedTime: string;
  description?: string;
}

// Customer information
export interface CustomerInfo {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
}

// Payment information
export interface PaymentInfo {
  amount: number;
  currency: string;
  paymentMethodId?: string;
  clientSecret?: string;
  paymentIntentId?: string;
}

// Checkout session
export interface CheckoutSession {
  id: string;
  booking: ServiceBooking;
  customer: CustomerInfo;
  payment: PaymentInfo;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  createdAt: string;
  expiresAt: string;
}

// Stripe Payment Intent response
export interface PaymentIntentResponse {
  clientSecret: string;
  paymentIntentId: string;
  amount: number;
  currency: string;
  status: string;
}

// Payment result
export interface PaymentResult {
  success: boolean;
  paymentIntentId?: string;
  error?: string;
  errorCode?: string;
}

// Checkout state for components
export interface CheckoutState {
  session: CheckoutSession | null;
  isLoading: boolean;
  isProcessing: boolean;
  error: string | null;
  step: 'review' | 'payment' | 'success' | 'error';
}

// Navigation types
export interface CheckoutNavigationParams {
  Checkout: {
    serviceId: string;
    providerId: string;
    selectedDate: string;
    selectedTime: string;
  };
  Payment: {
    sessionId: string;
  };
  PaymentSuccess: {
    paymentIntentId: string;
    bookingId: string;
  };
}

// Component props types
export interface CheckoutScreenProps {
  serviceId: string;
  providerId: string;
  selectedDate: string;
  selectedTime: string;
}

export interface PaymentScreenProps {
  sessionId: string;
}

export interface PaymentSuccessScreenProps {
  paymentIntentId: string;
  bookingId: string;
}

// API request/response types
export interface CreateCheckoutSessionRequest {
  serviceId: string;
  providerId: string;
  bookingDate: string;
  bookingTime: string;
  customerNotes?: string;
  paymentMethodId?: string;
}

export interface CreateCheckoutSessionResponse {
  sessionId: string;
  clientSecret: string;
  amount: number;
  currency: string;
  booking?: any;
  paymentIntentId?: string;
}

export interface ConfirmPaymentRequest {
  sessionId: string;
  paymentMethodId: string;
}

export interface ConfirmPaymentResponse {
  success: boolean;
  bookingId?: string;
  error?: string;
}

// Error types
export interface CheckoutError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

// Stripe configuration
export interface StripeConfig {
  publishableKey: string;
  merchantIdentifier?: string;
  urlScheme?: string;
}
