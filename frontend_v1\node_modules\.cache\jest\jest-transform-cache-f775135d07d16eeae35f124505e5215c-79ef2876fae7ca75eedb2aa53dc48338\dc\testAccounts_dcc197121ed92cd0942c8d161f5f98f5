29f7fde2a31e0c93ee81b8f02d07ed25
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getTestAccountsByRole = exports.getTestAccountsByCity = exports.getTestAccountsByCategory = exports.getRandomTestAccount = exports.findTestAccountByEmail = exports.TEST_ACCOUNTS_SUMMARY = exports.SKINCARE_PROVIDERS = exports.SALON_PROVIDERS = exports.QUICK_LOGIN_ACCOUNTS = exports.NAIL_SERVICES_PROVIDERS = exports.MASSAGE_PROVIDERS = exports.LASH_SERVICES_PROVIDERS = exports.CUSTOMER_TEST_ACCOUNTS = exports.BRAIDING_PROVIDERS = exports.BARBER_PROVIDERS = exports.ALL_TEST_ACCOUNTS = exports.ALL_SERVICE_PROVIDERS = void 0;
var _toConsumableArray2 = _interopRequireDefault(require("@babel/runtime/helpers/toConsumableArray"));
var CUSTOMER_TEST_ACCOUNTS = exports.CUSTOMER_TEST_ACCOUNTS = [{
  email: '<EMAIL>',
  password: 'testpass123',
  role: 'customer',
  firstName: 'Test',
  lastName: 'User',
  description: 'Basic test customer account'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'customer',
  firstName: 'Sarah',
  lastName: 'Johnson',
  description: 'Premium customer account'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'customer',
  firstName: 'Michael',
  lastName: 'Chen',
  description: 'Regular customer account'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'customer',
  firstName: 'Emily',
  lastName: 'Davis',
  description: 'Frequent customer account'
}];
var BARBER_PROVIDERS = exports.BARBER_PROVIDERS = [{
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Marcus',
  lastName: 'Johnson',
  businessName: 'Elite Cuts Barbershop',
  category: 'Barber',
  city: 'Ottawa',
  description: 'Traditional barbering, classic cuts, and beard grooming'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'David',
  lastName: 'Thompson',
  businessName: 'Classic Barber Co',
  category: 'Barber',
  city: 'Toronto',
  description: 'Traditional and contemporary men\'s grooming services'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'James',
  lastName: 'Wilson',
  businessName: 'Modern Cuts Barbershop',
  category: 'Barber',
  city: 'Ottawa',
  description: 'Modern barbering techniques with classic service'
}];
var SALON_PROVIDERS = exports.SALON_PROVIDERS = [{
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Emma',
  lastName: 'Rodriguez',
  businessName: 'Trendy Cuts Salon',
  category: 'Salon',
  city: 'Ottawa',
  description: 'Creative hair designs and color specialists'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Lisa',
  lastName: 'Wang',
  businessName: 'Luxe Hair Boutique',
  category: 'Salon',
  city: 'Ottawa',
  description: 'Premium hair care and styling services'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Sarah',
  lastName: 'Mitchell',
  businessName: 'Bella Hair Studio',
  category: 'Salon',
  city: 'Toronto',
  description: 'Full-service salon specializing in color and styling'
}];
var NAIL_SERVICES_PROVIDERS = exports.NAIL_SERVICES_PROVIDERS = [{
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider1',
  lastName: 'Nail',
  businessName: 'Nail Services Studio 1',
  category: 'Nail Services',
  city: 'Ottawa',
  description: 'Manicures, pedicures, nail art, and nail care'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider2',
  lastName: 'Nail',
  businessName: 'Nail Services Studio 2',
  category: 'Nail Services',
  city: 'Ottawa',
  description: 'Manicures, pedicures, nail art, and nail care'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider3',
  lastName: 'Nail',
  businessName: 'Nail Services Studio 3',
  category: 'Nail Services',
  city: 'Toronto',
  description: 'Manicures, pedicures, nail art, and nail care'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider4',
  lastName: 'Nail',
  businessName: 'Nail Services Studio 4',
  category: 'Nail Services',
  city: 'Toronto',
  description: 'Manicures, pedicures, nail art, and nail care'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider5',
  lastName: 'Nail',
  businessName: 'Nail Services Studio 5',
  category: 'Nail Services',
  city: 'Ottawa',
  description: 'Manicures, pedicures, nail art, and nail care'
}];
var LASH_SERVICES_PROVIDERS = exports.LASH_SERVICES_PROVIDERS = [{
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider1',
  lastName: 'Lash',
  businessName: 'Lash Services Studio 1',
  category: 'Lash Services',
  city: 'Ottawa',
  description: 'Eyelash extensions, lifts, tinting, and brow services'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider2',
  lastName: 'Lash',
  businessName: 'Lash Services Studio 2',
  category: 'Lash Services',
  city: 'Ottawa',
  description: 'Eyelash extensions, lifts, tinting, and brow services'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider3',
  lastName: 'Lash',
  businessName: 'Lash Services Studio 3',
  category: 'Lash Services',
  city: 'Toronto',
  description: 'Eyelash extensions, lifts, tinting, and brow services'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider4',
  lastName: 'Lash',
  businessName: 'Lash Services Studio 4',
  category: 'Lash Services',
  city: 'Toronto',
  description: 'Eyelash extensions, lifts, tinting, and brow services'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider5',
  lastName: 'Lash',
  businessName: 'Lash Services Studio 5',
  category: 'Lash Services',
  city: 'Ottawa',
  description: 'Eyelash extensions, lifts, tinting, and brow services'
}];
var BRAIDING_PROVIDERS = exports.BRAIDING_PROVIDERS = [{
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider1',
  lastName: 'Braiding',
  businessName: 'Braiding Studio 1',
  category: 'Braiding',
  city: 'Ottawa',
  description: 'Professional braiding and protective styling'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider2',
  lastName: 'Braiding',
  businessName: 'Braiding Studio 2',
  category: 'Braiding',
  city: 'Ottawa',
  description: 'Professional braiding and protective styling'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider3',
  lastName: 'Braiding',
  businessName: 'Braiding Studio 3',
  category: 'Braiding',
  city: 'Toronto',
  description: 'Professional braiding and protective styling'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider4',
  lastName: 'Braiding',
  businessName: 'Braiding Studio 4',
  category: 'Braiding',
  city: 'Toronto',
  description: 'Professional braiding and protective styling'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider5',
  lastName: 'Braiding',
  businessName: 'Braiding Studio 5',
  category: 'Braiding',
  city: 'Ottawa',
  description: 'Professional braiding and protective styling'
}];
var MASSAGE_PROVIDERS = exports.MASSAGE_PROVIDERS = [{
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider1',
  lastName: 'Massage',
  businessName: 'Massage Studio 1',
  category: 'Massage',
  city: 'Ottawa',
  description: 'Therapeutic and relaxation massage services'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider2',
  lastName: 'Massage',
  businessName: 'Massage Studio 2',
  category: 'Massage',
  city: 'Ottawa',
  description: 'Therapeutic and relaxation massage services'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider3',
  lastName: 'Massage',
  businessName: 'Massage Studio 3',
  category: 'Massage',
  city: 'Toronto',
  description: 'Therapeutic and relaxation massage services'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider4',
  lastName: 'Massage',
  businessName: 'Massage Studio 4',
  category: 'Massage',
  city: 'Toronto',
  description: 'Therapeutic and relaxation massage services'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider5',
  lastName: 'Massage',
  businessName: 'Massage Studio 5',
  category: 'Massage',
  city: 'Ottawa',
  description: 'Therapeutic and relaxation massage services'
}];
var SKINCARE_PROVIDERS = exports.SKINCARE_PROVIDERS = [{
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider1',
  lastName: 'Skincare',
  businessName: 'Skincare Studio 1',
  category: 'Skincare',
  city: 'Ottawa',
  description: 'Facial treatments, skincare consultations, and beauty treatments'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider2',
  lastName: 'Skincare',
  businessName: 'Skincare Studio 2',
  category: 'Skincare',
  city: 'Ottawa',
  description: 'Facial treatments, skincare consultations, and beauty treatments'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider3',
  lastName: 'Skincare',
  businessName: 'Skincare Studio 3',
  category: 'Skincare',
  city: 'Toronto',
  description: 'Facial treatments, skincare consultations, and beauty treatments'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider4',
  lastName: 'Skincare',
  businessName: 'Skincare Studio 4',
  category: 'Skincare',
  city: 'Toronto',
  description: 'Facial treatments, skincare consultations, and beauty treatments'
}, {
  email: '<EMAIL>',
  password: 'TestPass123!',
  role: 'service_provider',
  firstName: 'Provider5',
  lastName: 'Skincare',
  businessName: 'Skincare Studio 5',
  category: 'Skincare',
  city: 'Ottawa',
  description: 'Facial treatments, skincare consultations, and beauty treatments'
}];
var ALL_SERVICE_PROVIDERS = exports.ALL_SERVICE_PROVIDERS = [].concat(BARBER_PROVIDERS, SALON_PROVIDERS, NAIL_SERVICES_PROVIDERS, LASH_SERVICES_PROVIDERS, BRAIDING_PROVIDERS, MASSAGE_PROVIDERS, SKINCARE_PROVIDERS);
var ALL_TEST_ACCOUNTS = exports.ALL_TEST_ACCOUNTS = [].concat(CUSTOMER_TEST_ACCOUNTS, (0, _toConsumableArray2.default)(ALL_SERVICE_PROVIDERS));
var getTestAccountsByRole = exports.getTestAccountsByRole = function getTestAccountsByRole(role) {
  return ALL_TEST_ACCOUNTS.filter(function (account) {
    return account.role === role;
  });
};
var getTestAccountsByCategory = exports.getTestAccountsByCategory = function getTestAccountsByCategory(category) {
  return ALL_SERVICE_PROVIDERS.filter(function (account) {
    return account.category === category;
  });
};
var getTestAccountsByCity = exports.getTestAccountsByCity = function getTestAccountsByCity(city) {
  return ALL_TEST_ACCOUNTS.filter(function (account) {
    return account.city === city;
  });
};
var getRandomTestAccount = exports.getRandomTestAccount = function getRandomTestAccount(role) {
  var accounts = role ? getTestAccountsByRole(role) : ALL_TEST_ACCOUNTS;
  var randomIndex = Math.floor(Math.random() * accounts.length);
  return accounts[randomIndex];
};
var findTestAccountByEmail = exports.findTestAccountByEmail = function findTestAccountByEmail(email) {
  return ALL_TEST_ACCOUNTS.find(function (account) {
    return account.email === email;
  });
};
var QUICK_LOGIN_ACCOUNTS = exports.QUICK_LOGIN_ACCOUNTS = {
  CUSTOMER: CUSTOMER_TEST_ACCOUNTS[0],
  BARBER_PROVIDER: BARBER_PROVIDERS[0],
  SALON_PROVIDER: SALON_PROVIDERS[0],
  NAIL_PROVIDER: NAIL_SERVICES_PROVIDERS[0],
  LASH_PROVIDER: LASH_SERVICES_PROVIDERS[0],
  BRAIDING_PROVIDER: BRAIDING_PROVIDERS[0],
  MASSAGE_PROVIDER: MASSAGE_PROVIDERS[0],
  SKINCARE_PROVIDER: SKINCARE_PROVIDERS[0]
};
var TEST_ACCOUNTS_SUMMARY = exports.TEST_ACCOUNTS_SUMMARY = {
  total: ALL_TEST_ACCOUNTS.length,
  customers: CUSTOMER_TEST_ACCOUNTS.length,
  providers: ALL_SERVICE_PROVIDERS.length,
  categories: {
    'Barber': BARBER_PROVIDERS.length,
    'Salon': SALON_PROVIDERS.length,
    'Nail Services': NAIL_SERVICES_PROVIDERS.length,
    'Lash Services': LASH_SERVICES_PROVIDERS.length,
    Braiding: BRAIDING_PROVIDERS.length,
    Massage: MASSAGE_PROVIDERS.length,
    Skincare: SKINCARE_PROVIDERS.length
  },
  cities: {
    Ottawa: getTestAccountsByCity('Ottawa').length,
    Toronto: getTestAccountsByCity('Toronto').length
  }
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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