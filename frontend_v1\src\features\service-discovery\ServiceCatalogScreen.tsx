/**
 * Service Catalog Screen - Comprehensive Service Discovery
 *
 * Component Contract:
 * - Displays categorized service catalog with visual hierarchy
 * - Implements advanced search and filtering capabilities
 * - Provides service recommendations based on user preferences
 * - Supports location-based service discovery
 * - Integrates with booking system for seamless user experience
 * - Follows TDD methodology with comprehensive test coverage
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useEffect, useMemo } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Image } from 'react-native';
import { FlatList, ActivityIndicator, RefreshControl,  } from 'react-native';
import { SafeAreaWrapper } from '../../components/ui/SafeAreaWrapper';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../../contexts/ThemeContext';
import { getResponsiveSpacing, getResponsiveFontSize } from '../../utils/responsiveUtils';
import { createStyles } from './ServiceCatalogScreen.styles';
import { Input } from '../../components/atoms/Input';
import { Button } from '../../components/atoms/Button';

interface ServiceCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  serviceCount: number;
  popularServices: string[];
  averagePrice: string;
  image?: string;
}

interface ServiceProvider {
  id: string;
  businessName: string;
  category: string;
  subcategories: string[];
  rating: number;
  reviewCount: number;
  distance: string;
  priceRange: string;
  isVerified: boolean;
  isOpenNow: boolean;
  nextAvailable: string;
  specialOffers?: string[];
  images: string[];
}

interface SearchFilters {
  query: string;
  category?: string;
  location?: string;
  priceRange?: string;
  rating?: number;
  availability?: 'now' | 'today' | 'week';
  verified?: boolean;
  offers?: boolean;
}

export const ServiceCatalogScreen: React.FC = () => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  const navigation = useNavigation();

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [filters, setFilters] = useState<SearchFilters>({
    query: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Mock data - in real app, this would come from API
  const serviceCategories: ServiceCategory[] = [
    {
      id: '1',
      name: 'Barber',
      description: 'Professional mens grooming and haircuts',
      icon: 'cut-outline',
      serviceCount: 78,
      popularServices: ['Classic Haircut', 'Beard Trim', 'Hot Towel Shave'],
      averagePrice: '$25-45',
    },
    {
      id: '2',
      name: 'Salon',
      description: 'Professional hair styling, cutting, and treatments',
      icon: 'cut-outline',
      serviceCount: 78,
      popularServices: ['Haircut & Style', 'Hair Color', 'Highlights'],
      averagePrice: '$65-150',
    },
    {
      id: '3',
      name: 'Nail Services',
      description: 'Manicures, pedicures, and nail art',
      icon: 'hand-left-outline',
      serviceCount: 89,
      popularServices: ['Manicure', 'Pedicure', 'Gel Nails'],
      averagePrice: '$25-65',
    },
    {
      id: '4',
      name: 'Skincare',
      description: 'Facials, treatments, and skin consultations',
      icon: 'sparkles-outline',
      serviceCount: 67,
      popularServices: ['Facial', 'Chemical Peel', 'Microdermabrasion'],
      averagePrice: '$60-120',
    },
    {
      id: '4',
      name: 'Massage Therapy',
      description: 'Therapeutic and relaxation massage services',
      icon: 'body-outline',
      serviceCount: 45,
      popularServices: ['Swedish Massage', 'Deep Tissue', 'Hot Stone'],
      averagePrice: '$70-130',
    },
    {
      id: '5',
      name: 'Lash & Brow',
      description: 'Eyelash extensions, brow shaping, and tinting',
      icon: 'eye-outline',
      serviceCount: 78,
      popularServices: ['Lash Extensions', 'Brow Wax', 'Lash Lift'],
      averagePrice: '$35-95',
    },
    {
      id: '6',
      name: 'Makeup Services',
      description: 'Professional makeup for events and occasions',
      icon: 'color-palette-outline',
      serviceCount: 34,
      popularServices: ['Bridal Makeup', 'Event Makeup', 'Makeup Lesson'],
      averagePrice: '$80-200',
    },
  ];

  const featuredProviders: ServiceProvider[] = [
    {
      id: '1',
      businessName: 'Luxe Hair Studio',
      category: 'Hair Services',
      subcategories: ['Haircut', 'Color', 'Styling'],
      rating: 4.8,
      reviewCount: 124,
      distance: '0.8 km',
      priceRange: '$$',
      isVerified: true,
      isOpenNow: true,
      nextAvailable: 'Today 2:30 PM',
      specialOffers: ['20% off first visit'],
      images: [],
    },
    {
      id: '2',
      businessName: 'Serenity Spa',
      category: 'Skincare',
      subcategories: ['Facial', 'Chemical Peel', 'Microneedling'],
      rating: 4.9,
      reviewCount: 89,
      distance: '1.2 km',
      priceRange: '$$$',
      isVerified: true,
      isOpenNow: false,
      nextAvailable: 'Tomorrow 10:00 AM',
      images: [],
    },
  ];

  const filteredCategories = useMemo(() => {
    if (!searchQuery) return serviceCategories;
    return serviceCategories.filter(category =>
      category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      category.popularServices.some(service =>
        service.toLowerCase().includes(searchQuery.toLowerCase())
      )
    );
  }, [searchQuery, serviceCategories]);

  const handleCategoryPress = (category: ServiceCategory) => {
    setSelectedCategory(category.id);
    navigation.navigate('SearchScreen', {
      category: category.name,
      filters: { category: category.name },
    });
  };

  const handleProviderPress = (provider: ServiceProvider) => {
    navigation.navigate('ProviderDetails', { providerId: provider.id });
  };

  const handleSearch = () => {
    navigation.navigate('SearchScreen', {
      query: searchQuery,
      filters: { query: searchQuery },
    });
  };

  const onRefresh = async () => {
    setRefreshing(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  };

  const renderCategoryCard = ({ item }: { item: ServiceCategory }) => (
    <TouchableOpacity
      style={styles.categoryCard}
      onPress={() => handleCategoryPress(item)}
      testID={`category-${item.id}`}
    >
      <View style={styles.categoryIconContainer}>
        <Ionicons
          name={item.icon as any}
          size={getResponsiveSpacing(32)}
          color={colors.primary}
        />
      </View>
      <View style={styles.categoryInfo}>
        <Text style={styles.categoryName}>{item.name}</Text>
        <Text style={styles.categoryDescription}>{item.description}</Text>
        <View style={styles.categoryStats}>
          <Text style={styles.categoryCount}>{item.serviceCount} providers</Text>
          <Text style={styles.categoryPrice}>{item.averagePrice}</Text>
        </View>
        <View style={styles.popularServices}>
          {item.popularServices.slice(0, 3).map((service, index) => (
            <View key={index} style={styles.serviceTag}>
              <Text style={styles.serviceTagText}>{service}</Text>
            </View>
          ))}
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderProviderCard = ({ item }: { item: ServiceProvider }) => (
    <TouchableOpacity
      style={styles.providerCard}
      onPress={() => handleProviderPress(item)}
      testID={`provider-${item.id}`}
    >
      <View style={styles.providerImageContainer}>
        <View style={styles.providerImagePlaceholder}>
          <Ionicons
            name="storefront-outline"
            size={getResponsiveSpacing(24)}
            color={colors.textSecondary}
          />
        </View>
        {item.isVerified && (
          <View style={styles.verifiedBadge}>
            <Ionicons name="checkmark-circle" size={16} color={colors.success} />
          </View>
        )}
      </View>
      <View style={styles.providerInfo}>
        <Text style={styles.providerName}>{item.businessName}</Text>
        <Text style={styles.providerCategory}>{item.category}</Text>
        <View style={styles.providerStats}>
          <View style={styles.ratingContainer}>
            <Ionicons name="star" size={14} color={colors.warning} />
            <Text style={styles.rating}>{item.rating}</Text>
            <Text style={styles.reviewCount}>({item.reviewCount})</Text>
          </View>
          <Text style={styles.distance}>{item.distance}</Text>
        </View>
        <View style={styles.availabilityContainer}>
          <View style={[
            styles.statusIndicator,
            { backgroundColor: item.isOpenNow ? colors.success : colors.error }
          ]} />
          <Text style={styles.availability}>
            {item.isOpenNow ? 'Open now' : item.nextAvailable}
          </Text>
        </View>
        {item.specialOffers && item.specialOffers.length > 0 && (
          <View style={styles.offerContainer}>
            <Text style={styles.offerText}>{item.specialOffers[0]}</Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaWrapper style={styles.container} testID="service-catalog-screen">
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Discover Services</Text>
          <Text style={styles.subtitle}>Find the perfect service provider near you</Text>
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <Input
            placeholder="Search services, providers, or treatments..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            style={styles.searchInput}
            leftIcon="search-outline"
            testID="search-input"
          />
          <Button
            onPress={handleSearch}
            style={styles.searchButton}
            testID="search-button"
          >
            Search
          </Button>
        </View>

        {/* Quick Filters */}
        <View style={styles.quickFilters}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <TouchableOpacity style={styles.filterChip}>
              <Ionicons name="location-outline" size={16} color={colors.primary} />
              <Text style={styles.filterChipText}>Near me</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.filterChip}>
              <Ionicons name="time-outline" size={16} color={colors.primary} />
              <Text style={styles.filterChipText}>Available now</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.filterChip}>
              <Ionicons name="star-outline" size={16} color={colors.primary} />
              <Text style={styles.filterChipText}>Top rated</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.filterChip}>
              <Ionicons name="pricetag-outline" size={16} color={colors.primary} />
              <Text style={styles.filterChipText}>Special offers</Text>
            </TouchableOpacity>
          </ScrollView>
        </View>

        {/* Service Categories */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Browse Categories</Text>
          <FlatList
            data={filteredCategories}
            renderItem={renderCategoryCard}
            keyExtractor={item => item.id}
            numColumns={2}
            columnWrapperStyle={styles.categoryRow}
            scrollEnabled={false}
            testID="categories-list"
          />
        </View>

        {/* Featured Providers */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Featured Providers</Text>
            <TouchableOpacity onPress={() => navigation.navigate('SearchScreen')}>
              <Text style={styles.seeAllText}>See all</Text>
            </TouchableOpacity>
          </View>
          <FlatList
            data={featuredProviders}
            renderItem={renderProviderCard}
            keyExtractor={item => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.providersContainer}
            testID="featured-providers-list"
          />
        </View>
      </ScrollView>
    </SafeAreaWrapper>
  );
};
