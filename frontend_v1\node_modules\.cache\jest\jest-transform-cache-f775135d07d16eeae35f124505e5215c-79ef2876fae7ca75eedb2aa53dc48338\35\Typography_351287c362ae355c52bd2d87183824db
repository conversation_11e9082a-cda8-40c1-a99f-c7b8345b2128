1755355485ef500812120d46983b4f8b
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.isTextSizeAccessible = exports.getPlatformAdjustedSize = exports.getOptimalLineHeight = exports.getAccessibleFontSize = exports.default = exports.TYPOGRAPHY_VARIANTS = exports.READING_WIDTH = exports.LINE_HEIGHTS = exports.LETTER_SPACING = exports.FONT_WEIGHTS = exports.FONT_SIZES = exports.FONT_FAMILIES = exports.ACCESSIBILITY_FONT_SIZES = void 0;
var _reactNative = require("react-native");
var BASE_FONT_SIZE = 16;
var SCALE_RATIO = 1.25;
var LINE_HEIGHT_RATIO = 1.5;
var FONT_FAMILIES = exports.FONT_FAMILIES = {
  primary: _reactNative.Platform != null && _reactNative.Platform.select ? _reactNative.Platform.select({
    ios: 'SF Pro Display',
    android: 'Roboto',
    web: '-apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif',
    default: 'System'
  }) : 'System',
  secondary: _reactNative.Platform != null && _reactNative.Platform.select ? _reactNative.Platform.select({
    ios: 'SF Pro Text',
    android: 'Roboto',
    web: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    default: 'System'
  }) : 'System',
  monospace: _reactNative.Platform != null && _reactNative.Platform.select ? _reactNative.Platform.select({
    ios: 'SF Mono',
    android: 'Roboto Mono',
    web: 'SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace',
    default: 'monospace'
  }) : 'monospace'
};
var FONT_WEIGHTS = exports.FONT_WEIGHTS = {
  light: '300',
  regular: '400',
  medium: '500',
  semibold: '600',
  bold: '700',
  extrabold: '800'
};
var getResponsiveFontSize = function getResponsiveFontSize(size) {
  var scale = _reactNative.PixelRatio != null && _reactNative.PixelRatio.getFontScale ? _reactNative.PixelRatio.getFontScale() : 1;
  return Math.round(size * scale);
};
var generateScale = function generateScale(base, ratio, steps) {
  var scale = [];
  for (var i = -2; i <= steps; i++) {
    scale.push(Math.round(base * Math.pow(ratio, i)));
  }
  return scale;
};
var SCALE = generateScale(BASE_FONT_SIZE, SCALE_RATIO, 6);
var FONT_SIZES = exports.FONT_SIZES = {
  xs: getResponsiveFontSize(SCALE[0]),
  sm: getResponsiveFontSize(SCALE[1]),
  base: getResponsiveFontSize(SCALE[2]),
  lg: getResponsiveFontSize(SCALE[3]),
  xl: getResponsiveFontSize(SCALE[4]),
  '2xl': getResponsiveFontSize(SCALE[5]),
  '3xl': getResponsiveFontSize(SCALE[6]),
  '4xl': getResponsiveFontSize(SCALE[7]),
  '5xl': getResponsiveFontSize(SCALE[8])
};
var LINE_HEIGHTS = exports.LINE_HEIGHTS = {
  tight: 1.25,
  normal: 1.5,
  relaxed: 1.75,
  loose: 2
};
var LETTER_SPACING = exports.LETTER_SPACING = {
  tighter: -0.05,
  tight: -0.025,
  normal: 0,
  wide: 0.025,
  wider: 0.05,
  widest: 0.1
};
var TYPOGRAPHY_VARIANTS = exports.TYPOGRAPHY_VARIANTS = {
  h1: {
    fontSize: FONT_SIZES['4xl'],
    fontFamily: FONT_FAMILIES.primary,
    fontWeight: FONT_WEIGHTS.bold,
    lineHeight: FONT_SIZES['4xl'] * LINE_HEIGHTS.tight,
    letterSpacing: LETTER_SPACING.tight
  },
  h2: {
    fontSize: FONT_SIZES['3xl'],
    fontFamily: FONT_FAMILIES.primary,
    fontWeight: FONT_WEIGHTS.bold,
    lineHeight: FONT_SIZES['3xl'] * LINE_HEIGHTS.tight,
    letterSpacing: LETTER_SPACING.tight
  },
  h3: {
    fontSize: FONT_SIZES['2xl'],
    fontFamily: FONT_FAMILIES.primary,
    fontWeight: FONT_WEIGHTS.semibold,
    lineHeight: FONT_SIZES['2xl'] * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.normal
  },
  h4: {
    fontSize: FONT_SIZES.xl,
    fontFamily: FONT_FAMILIES.primary,
    fontWeight: FONT_WEIGHTS.semibold,
    lineHeight: FONT_SIZES.xl * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.normal
  },
  h5: {
    fontSize: FONT_SIZES.lg,
    fontFamily: FONT_FAMILIES.primary,
    fontWeight: FONT_WEIGHTS.medium,
    lineHeight: FONT_SIZES.lg * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.normal
  },
  h6: {
    fontSize: FONT_SIZES.base,
    fontFamily: FONT_FAMILIES.primary,
    fontWeight: FONT_WEIGHTS.medium,
    lineHeight: FONT_SIZES.base * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.normal
  },
  body1: {
    fontSize: FONT_SIZES.base,
    fontFamily: FONT_FAMILIES.secondary,
    fontWeight: FONT_WEIGHTS.regular,
    lineHeight: FONT_SIZES.base * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.normal
  },
  body2: {
    fontSize: FONT_SIZES.sm,
    fontFamily: FONT_FAMILIES.secondary,
    fontWeight: FONT_WEIGHTS.regular,
    lineHeight: FONT_SIZES.sm * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.normal
  },
  subtitle1: {
    fontSize: FONT_SIZES.lg,
    fontFamily: FONT_FAMILIES.secondary,
    fontWeight: FONT_WEIGHTS.medium,
    lineHeight: FONT_SIZES.lg * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.wide
  },
  subtitle2: {
    fontSize: FONT_SIZES.base,
    fontFamily: FONT_FAMILIES.secondary,
    fontWeight: FONT_WEIGHTS.medium,
    lineHeight: FONT_SIZES.base * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.wide
  },
  caption: {
    fontSize: FONT_SIZES.xs,
    fontFamily: FONT_FAMILIES.secondary,
    fontWeight: FONT_WEIGHTS.regular,
    lineHeight: FONT_SIZES.xs * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.wide
  },
  overline: {
    fontSize: FONT_SIZES.xs,
    fontFamily: FONT_FAMILIES.secondary,
    fontWeight: FONT_WEIGHTS.medium,
    lineHeight: FONT_SIZES.xs * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.widest,
    textTransform: 'uppercase'
  },
  button: {
    fontSize: FONT_SIZES.base,
    fontFamily: FONT_FAMILIES.primary,
    fontWeight: FONT_WEIGHTS.semibold,
    lineHeight: FONT_SIZES.base * LINE_HEIGHTS.tight,
    letterSpacing: LETTER_SPACING.wide
  },
  buttonSmall: {
    fontSize: FONT_SIZES.sm,
    fontFamily: FONT_FAMILIES.primary,
    fontWeight: FONT_WEIGHTS.semibold,
    lineHeight: FONT_SIZES.sm * LINE_HEIGHTS.tight,
    letterSpacing: LETTER_SPACING.wide
  },
  buttonLarge: {
    fontSize: FONT_SIZES.lg,
    fontFamily: FONT_FAMILIES.primary,
    fontWeight: FONT_WEIGHTS.semibold,
    lineHeight: FONT_SIZES.lg * LINE_HEIGHTS.tight,
    letterSpacing: LETTER_SPACING.wide
  },
  input: {
    fontSize: FONT_SIZES.base,
    fontFamily: FONT_FAMILIES.secondary,
    fontWeight: FONT_WEIGHTS.regular,
    lineHeight: FONT_SIZES.base * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.normal
  },
  label: {
    fontSize: FONT_SIZES.sm,
    fontFamily: FONT_FAMILIES.secondary,
    fontWeight: FONT_WEIGHTS.medium,
    lineHeight: FONT_SIZES.sm * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.wide
  },
  helper: {
    fontSize: FONT_SIZES.xs,
    fontFamily: FONT_FAMILIES.secondary,
    fontWeight: FONT_WEIGHTS.regular,
    lineHeight: FONT_SIZES.xs * LINE_HEIGHTS.normal,
    letterSpacing: LETTER_SPACING.normal
  },
  navItem: {
    fontSize: FONT_SIZES.base,
    fontFamily: FONT_FAMILIES.primary,
    fontWeight: FONT_WEIGHTS.medium,
    lineHeight: FONT_SIZES.base * LINE_HEIGHTS.tight,
    letterSpacing: LETTER_SPACING.normal
  },
  tabItem: {
    fontSize: FONT_SIZES.sm,
    fontFamily: FONT_FAMILIES.primary,
    fontWeight: FONT_WEIGHTS.medium,
    lineHeight: FONT_SIZES.sm * LINE_HEIGHTS.tight,
    letterSpacing: LETTER_SPACING.wide
  },
  code: {
    fontSize: FONT_SIZES.sm,
    fontFamily: FONT_FAMILIES.monospace,
    fontWeight: FONT_WEIGHTS.regular,
    lineHeight: FONT_SIZES.sm * LINE_HEIGHTS.relaxed,
    letterSpacing: LETTER_SPACING.normal
  }
};
var ACCESSIBILITY_FONT_SIZES = exports.ACCESSIBILITY_FONT_SIZES = {
  minimum: 12,
  comfortable: 16,
  large: 20,
  extraLarge: 24
};
var READING_WIDTH = exports.READING_WIDTH = {
  optimal: 65,
  maximum: 75,
  minimum: 45
};
var getOptimalLineHeight = exports.getOptimalLineHeight = function getOptimalLineHeight(fontSize) {
  if (fontSize >= FONT_SIZES.xl) return fontSize * 1.2;
  if (fontSize >= FONT_SIZES.lg) return fontSize * 1.3;
  return fontSize * 1.5;
};
var getAccessibleFontSize = exports.getAccessibleFontSize = function getAccessibleFontSize(baseFontSize) {
  var userPreference = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'normal';
  var multipliers = {
    small: 0.875,
    normal: 1,
    large: 1.125,
    extraLarge: 1.25
  };
  return Math.max(ACCESSIBILITY_FONT_SIZES.minimum, Math.round(baseFontSize * multipliers[userPreference]));
};
var isTextSizeAccessible = exports.isTextSizeAccessible = function isTextSizeAccessible(fontSize) {
  return fontSize >= ACCESSIBILITY_FONT_SIZES.minimum;
};
var getPlatformAdjustedSize = exports.getPlatformAdjustedSize = function getPlatformAdjustedSize(size) {
  if (_reactNative.Platform.OS === 'android') {
    return Math.round(size * 1.05);
  }
  return size;
};
var _default = exports.default = {
  FONT_FAMILIES: FONT_FAMILIES,
  FONT_WEIGHTS: FONT_WEIGHTS,
  FONT_SIZES: FONT_SIZES,
  LINE_HEIGHTS: LINE_HEIGHTS,
  LETTER_SPACING: LETTER_SPACING,
  TYPOGRAPHY_VARIANTS: TYPOGRAPHY_VARIANTS,
  ACCESSIBILITY_FONT_SIZES: ACCESSIBILITY_FONT_SIZES,
  READING_WIDTH: READING_WIDTH,
  getOptimalLineHeight: getOptimalLineHeight,
  getAccessibleFontSize: getAccessibleFontSize,
  isTextSizeAccessible: isTextSizeAccessible,
  getPlatformAdjustedSize: getPlatformAdjustedSize
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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