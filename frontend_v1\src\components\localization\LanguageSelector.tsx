/**
 * Language Selector Component
 *
 * Component for selecting and switching between supported languages
 * with proper Quebec French cultural considerations.
 *
 * Features:
 * - Language switching
 * - Cultural adaptations
 * - Accessibility compliance
 * - Persistent preferences
 * - Visual indicators
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useCallback } from 'react';
import { View, TouchableOpacity } from 'react-native';
import { Platform } from 'react-native';
import { StyleSheet, Modal, FlatList, AccessibilityInfo,  } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { Typography, Body } from '../typography/Typography';
// Temporarily commented out to fix provider issues
// import { useHighContrastColors } from '../../contexts/HighContrastContext';
// import { useTouchTargetStyles, useHapticFeedback } from '../../contexts/MotorAccessibilityContext';
import {
  SUPPORTED_LANGUAGES,
  SupportedLanguage,
  changeLanguage,
  getCurrentLanguage,
  isQuebec<PERSON>rench,
} from '../../localization/i18n';

// Component props
export interface LanguageSelectorProps {
  // Styling
  variant?: 'button' | 'dropdown' | 'inline';
  showFlag?: boolean;
  showNativeName?: boolean;
  compact?: boolean;
  style?: any;
  
  // Behavior
  onLanguageChange?: (language: SupportedLanguage) => void;
  disabled?: boolean;
  
  // Accessibility
  accessibilityLabel?: string;
  
  // Testing
  testID?: string;
}

export const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  variant = 'button',
  showFlag = true,
  showNativeName = true,
  compact = false,
  style,
  onLanguageChange,
  disabled = false,
  accessibilityLabel,
  testID,
}) => {
  // Hooks
  const { t } = useTranslation();
  // const { colors } = useHighContrastColors();
  // const touchTargetStyles = useTouchTargetStyles();
  // const triggerHapticFeedback = useHapticFeedback();

  // Temporary fallback values
  const colors = { background: '#FFFFFF', text: { primary: '#000000', secondary: '#666666' } };
  const touchTargetStyles = {};
  const triggerHapticFeedback = () => {};

  // State
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState<SupportedLanguage>(getCurrentLanguage());

  // Handle language selection
  const handleLanguageSelect = useCallback(async (language: SupportedLanguage) => {
    if (language === currentLanguage || disabled) return;

    try {
      triggerHapticFeedback('medium');
      
      // Change language
      await changeLanguage(language);
      setCurrentLanguage(language);
      
      // Close modal
      setIsModalVisible(false);
      
      // Callback
      if (onLanguageChange) {
        onLanguageChange(language);
      }
      
      // Announce language change to screen readers
      const languageName = SUPPORTED_LANGUAGES[language].nativeName;
      if (Platform.OS === 'ios' || Platform.OS === 'android') {
        AccessibilityInfo.announceForAccessibility(
          t('accessibility.language_changed', { language: languageName })
        );
      }
    } catch (error) {
      console.error('Failed to change language:', error);
    }
  }, [currentLanguage, disabled, onLanguageChange, triggerHapticFeedback, t]);

  // Handle modal toggle
  const toggleModal = useCallback(() => {
    if (disabled) return;
    
    triggerHapticFeedback('light');
    setIsModalVisible(!isModalVisible);
  }, [disabled, isModalVisible, triggerHapticFeedback]);

  // Get current language info
  const getCurrentLanguageInfo = () => {
    return SUPPORTED_LANGUAGES[currentLanguage];
  };

  // Render language option
  const renderLanguageOption = ({ item }: { item: SupportedLanguage }) => {
    const languageInfo = SUPPORTED_LANGUAGES[item];
    const isSelected = item === currentLanguage;
    const isQuebecOption = item === 'fr-CA';

    return (
      <TouchableOpacity
        style={[
          styles.languageOption,
          {
            backgroundColor: isSelected 
              ? colors?.primary?.light || '#E8F5E8'
              : colors?.background?.primary || '#FFFFFF',
            borderColor: colors?.border?.primary || '#E0E0E0',
          },
          touchTargetStyles,
        ]}
        onPress={() => handleLanguageSelect(item)}
        accessibilityRole="button"
        accessibilityLabel={`${languageInfo.nativeName}${isSelected ? ', selected' : ''}`}
        accessibilityHint={`Switch to ${languageInfo.name}`}
        accessibilityState={{ selected: isSelected }}
      >
        <View style={styles.languageContent}>
          {showFlag && (
            <Typography
              variant="body1"
              style={styles.flag}
            >
              {languageInfo.flag}
            </Typography>
          )}
          
          <View style={styles.languageText}>
            <Typography
              variant="body1"
              color={isSelected ? colors?.primary?.default : colors?.text?.primary}
              style={styles.languageName}
            >
              {showNativeName ? languageInfo.nativeName : languageInfo.name}
            </Typography>
            
            {!compact && showNativeName && (
              <Body
                color={colors?.text?.secondary}
                style={styles.languageSubtext}
              >
                {languageInfo.name}
              </Body>
            )}
            
            {isQuebecOption && (
              <Body
                color={colors?.text?.tertiary}
                style={styles.quebecNote}
              >
                Français québécois
              </Body>
            )}
          </View>
          
          {isSelected && (
            <Ionicons
              name="checkmark"
              size={20}
              color={colors?.primary?.default || '#5A7A63'}
              style={styles.checkmark}
            />
          )}
        </View>
      </TouchableOpacity>
    );
  };

  // Render button variant
  const renderButton = () => {
    const languageInfo = getCurrentLanguageInfo();
    
    return (
      <TouchableOpacity
        style={[
          styles.button,
          {
            backgroundColor: colors?.background?.secondary || '#F8F9FA',
            borderColor: colors?.border?.primary || '#E0E0E0',
          },
          touchTargetStyles,
          style,
        ]}
        onPress={toggleModal}
        disabled={disabled}
        accessibilityRole="button"
        accessibilityLabel={accessibilityLabel || `Current language: ${languageInfo.nativeName}`}
        accessibilityHint="Tap to change language"
        accessibilityState={{ disabled }}
        testID={testID}
      >
        <View style={styles.buttonContent}>
          {showFlag && (
            <Typography variant="body1" style={styles.buttonFlag}>
              {languageInfo.flag}
            </Typography>
          )}
          
          <Typography
            variant="body1"
            color={colors?.text?.primary}
            style={styles.buttonText}
          >
            {compact ? languageInfo.code.toUpperCase() : 
             showNativeName ? languageInfo.nativeName : languageInfo.name}
          </Typography>
          
          <Ionicons
            name="chevron-down"
            size={16}
            color={colors?.text?.secondary || '#666'}
            style={[
              styles.chevron,
              { opacity: disabled ? 0.5 : 1 },
            ]}
          />
        </View>
      </TouchableOpacity>
    );
  };

  // Render inline variant
  const renderInline = () => {
    const languages = Object.keys(SUPPORTED_LANGUAGES) as SupportedLanguage[];
    
    return (
      <View style={[styles.inlineContainer, style]} testID={testID}>
        {languages.map((language) => {
          const languageInfo = SUPPORTED_LANGUAGES[language];
          const isSelected = language === currentLanguage;
          
          return (
            <TouchableOpacity
              key={language}
              style={[
                styles.inlineOption,
                {
                  backgroundColor: isSelected 
                    ? colors?.primary?.default || '#5A7A63'
                    : 'transparent',
                  borderColor: colors?.primary?.default || '#5A7A63',
                },
                touchTargetStyles,
              ]}
              onPress={() => handleLanguageSelect(language)}
              disabled={disabled}
              accessibilityRole="button"
              accessibilityLabel={languageInfo.nativeName}
              accessibilityState={{ selected: isSelected, disabled }}
            >
              {showFlag && (
                <Typography variant="caption" style={styles.inlineFlag}>
                  {languageInfo.flag}
                </Typography>
              )}
              
              <Typography
                variant="caption"
                color={isSelected ? colors?.text?.inverse : colors?.primary?.default}
                style={styles.inlineText}
              >
                {compact ? language.split('-')[0].toUpperCase() : languageInfo.code.toUpperCase()}
              </Typography>
            </TouchableOpacity>
          );
        })}
      </View>
    );
  };

  // Render modal
  const renderModal = () => {
    const languages = Object.keys(SUPPORTED_LANGUAGES) as SupportedLanguage[];
    
    return (
      <Modal
        visible={isModalVisible}
        transparent
        animationType="fade"
        onRequestClose={() => setIsModalVisible(false)}
        accessibilityViewIsModal
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setIsModalVisible(false)}
        >
          <View
            style={[
              styles.modalContent,
              { backgroundColor: colors?.background?.primary || '#FFFFFF' },
            ]}
          >
            <View style={styles.modalHeader}>
              <Typography
                variant="h3"
                color={colors?.text?.primary}
                style={styles.modalTitle}
              >
                {t('forms.preferred_language')}
              </Typography>
              
              <TouchableOpacity
                style={[styles.closeButton, touchTargetStyles]}
                onPress={() => setIsModalVisible(false)}
                accessibilityRole="button"
                accessibilityLabel={t('common.close')}
              >
                <Ionicons
                  name="close"
                  size={24}
                  color={colors?.text?.secondary || '#666'}
                />
              </TouchableOpacity>
            </View>
            
            <FlatList
              data={languages}
              renderItem={renderLanguageOption}
              keyExtractor={(item) => item}
              style={styles.languageList}
              showsVerticalScrollIndicator={false}
            />
          </View>
        </TouchableOpacity>
      </Modal>
    );
  };

  // Render based on variant
  if (variant === 'inline') {
    return renderInline();
  }

  return (
    <>
      {renderButton()}
      {renderModal()}
    </>
  );
};

const styles = StyleSheet.create({
  // Button variant
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  buttonFlag: {
    marginRight: 8,
  },
  buttonText: {
    flex: 1,
  },
  chevron: {
    marginLeft: 8,
  },

  // Inline variant
  inlineContainer: {
    flexDirection: 'row',
    borderRadius: 8,
    overflow: 'hidden',
  },
  inlineOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderWidth: 1,
    marginRight: 1,
  },
  inlineFlag: {
    marginRight: 4,
  },
  inlineText: {
    fontWeight: '600',
  },

  // Modal
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxWidth: 400,
    maxHeight: '80%',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    flex: 1,
  },
  closeButton: {
    padding: 4,
  },
  languageList: {
    maxHeight: 300,
  },

  // Language options
  languageOption: {
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 8,
    padding: 16,
  },
  languageContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  flag: {
    marginRight: 12,
    fontSize: 20,
  },
  languageText: {
    flex: 1,
  },
  languageName: {
    fontWeight: '600',
  },
  languageSubtext: {
    marginTop: 2,
    fontSize: 12,
  },
  quebecNote: {
    marginTop: 2,
    fontSize: 10,
    fontStyle: 'italic',
  },
  checkmark: {
    marginLeft: 12,
  },
});

export default LanguageSelector;
