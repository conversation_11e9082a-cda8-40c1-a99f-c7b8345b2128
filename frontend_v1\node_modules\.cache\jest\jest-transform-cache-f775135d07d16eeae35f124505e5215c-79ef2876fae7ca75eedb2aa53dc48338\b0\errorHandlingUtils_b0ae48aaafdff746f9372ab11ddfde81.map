{"version": 3, "names": ["generateErrorId", "exports", "Date", "now", "Math", "random", "toString", "substr", "classifyError", "error", "_error$response", "_error$message", "name", "code", "status", "response", "type", "message", "includes", "getErrorSeverity", "getUserFriendlyMessage", "originalMessage", "messages", "network", "validation", "authentication", "authorization", "not_found", "server", "client", "timeout", "offline", "unknown", "getErrorSuggestions", "suggestions", "createAppError", "context", "arguments", "length", "undefined", "severity", "userMessage", "id", "Object", "assign", "timestamp", "originalError", "recoverable", "retryable", "isRetryableError", "retryCount", "getRetryDelay", "attemptCount", "min", "pow", "logError", "_error$originalError", "logData", "stack", "__DEV__", "console", "group", "toUpperCase", "groupEnd", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "_asyncToGenerator2", "default", "operation", "data", "appError", "_x", "apply", "retryOperation", "_ref2", "maxRetries", "lastError", "_loop", "v", "attempt", "delay", "Promise", "resolve", "setTimeout", "_ret", "_x2", "createRecoveryActions", "actions", "push", "label", "action", "window", "location", "reload", "primary", "history", "back", "href", "open", "encodeURIComponent", "formatErrorForDisplay", "titles", "low", "medium", "high", "critical", "title", "createErrorBoundaryState", "<PERSON><PERSON><PERSON><PERSON>", "errorId", "_default"], "sources": ["errorHandlingUtils.ts"], "sourcesContent": ["/**\n * Error Handling Utilities\n *\n * Comprehensive error handling utilities for graceful error management,\n * user-friendly error messages, and robust error recovery mechanisms.\n *\n * Features:\n * - Error classification\n * - User-friendly messaging\n * - Error recovery strategies\n * - Logging and reporting\n * - Accessibility compliance\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\n// Error types\nexport type ErrorType = \n  | 'network'\n  | 'validation'\n  | 'authentication'\n  | 'authorization'\n  | 'not_found'\n  | 'server'\n  | 'client'\n  | 'timeout'\n  | 'offline'\n  | 'unknown';\n\n// Error severity levels\nexport type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical';\n\n// Error context\nexport interface ErrorContext {\n  component?: string;\n  action?: string;\n  userId?: string;\n  timestamp: Date;\n  userAgent?: string;\n  url?: string;\n  additionalData?: Record<string, any>;\n}\n\n// Application error interface\nexport interface AppError {\n  id: string;\n  type: ErrorType;\n  severity: ErrorSeverity;\n  message: string;\n  userMessage: string;\n  code?: string | number;\n  context: ErrorContext;\n  originalError?: Error;\n  recoverable: boolean;\n  retryable: boolean;\n  suggestions: string[];\n}\n\n// Error recovery action\nexport interface ErrorRecoveryAction {\n  label: string;\n  action: () => void | Promise<void>;\n  primary?: boolean;\n}\n\n// Error boundary state\nexport interface ErrorBoundaryState {\n  hasError: boolean;\n  error: AppError | null;\n  errorId: string | null;\n  retryCount: number;\n}\n\n/**\n * Generate unique error ID\n */\nexport const generateErrorId = (): string => {\n  return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n};\n\n/**\n * Classify error type based on error object\n */\nexport const classifyError = (error: any): ErrorType => {\n  if (!error) return 'unknown';\n  \n  // Network errors\n  if (error.name === 'NetworkError' || error.code === 'NETWORK_ERROR') {\n    return 'network';\n  }\n  \n  // Timeout errors\n  if (error.name === 'TimeoutError' || error.code === 'TIMEOUT') {\n    return 'timeout';\n  }\n  \n  // HTTP status code based classification\n  if (error.status || error.response?.status) {\n    const status = error.status || error.response.status;\n    \n    if (status === 401) return 'authentication';\n    if (status === 403) return 'authorization';\n    if (status === 404) return 'not_found';\n    if (status >= 400 && status < 500) return 'client';\n    if (status >= 500) return 'server';\n  }\n  \n  // Validation errors\n  if (error.name === 'ValidationError' || error.type === 'validation') {\n    return 'validation';\n  }\n  \n  // Offline errors\n  if (error.message?.includes('offline') || error.code === 'OFFLINE') {\n    return 'offline';\n  }\n  \n  return 'unknown';\n};\n\n/**\n * Determine error severity\n */\nexport const getErrorSeverity = (error: any, type: ErrorType): ErrorSeverity => {\n  // Critical errors that break core functionality\n  if (type === 'authentication' || type === 'server') {\n    return 'critical';\n  }\n  \n  // High severity errors that significantly impact user experience\n  if (type === 'network' || type === 'timeout' || type === 'authorization') {\n    return 'high';\n  }\n  \n  // Medium severity errors that cause inconvenience\n  if (type === 'validation' || type === 'not_found') {\n    return 'medium';\n  }\n  \n  // Low severity errors that are minor issues\n  return 'low';\n};\n\n/**\n * Generate user-friendly error message\n */\nexport const getUserFriendlyMessage = (type: ErrorType, originalMessage?: string): string => {\n  const messages: Record<ErrorType, string> = {\n    network: 'Unable to connect to the internet. Please check your connection and try again.',\n    validation: 'Please check your input and try again.',\n    authentication: 'Your session has expired. Please sign in again.',\n    authorization: 'You don\\'t have permission to perform this action.',\n    not_found: 'The requested information could not be found.',\n    server: 'We\\'re experiencing technical difficulties. Please try again later.',\n    client: 'Something went wrong with your request. Please try again.',\n    timeout: 'The request took too long to complete. Please try again.',\n    offline: 'You appear to be offline. Please check your internet connection.',\n    unknown: 'An unexpected error occurred. Please try again.',\n  };\n  \n  return messages[type] || messages.unknown;\n};\n\n/**\n * Generate error suggestions\n */\nexport const getErrorSuggestions = (type: ErrorType): string[] => {\n  const suggestions: Record<ErrorType, string[]> = {\n    network: [\n      'Check your internet connection',\n      'Try switching between WiFi and mobile data',\n      'Restart your router if using WiFi',\n    ],\n    validation: [\n      'Double-check all required fields',\n      'Ensure email addresses are valid',\n      'Check password requirements',\n    ],\n    authentication: [\n      'Sign in again with your credentials',\n      'Reset your password if needed',\n      'Contact support if the problem persists',\n    ],\n    authorization: [\n      'Contact your administrator for access',\n      'Ensure you\\'re signed in to the correct account',\n      'Try refreshing the page',\n    ],\n    not_found: [\n      'Check the URL for typos',\n      'Go back and try again',\n      'Use the search function to find what you need',\n    ],\n    server: [\n      'Wait a few minutes and try again',\n      'Check our status page for updates',\n      'Contact support if the issue persists',\n    ],\n    client: [\n      'Refresh the page and try again',\n      'Clear your browser cache',\n      'Try using a different browser',\n    ],\n    timeout: [\n      'Check your internet connection speed',\n      'Try again with a better connection',\n      'Break large requests into smaller ones',\n    ],\n    offline: [\n      'Check your internet connection',\n      'Try again when you\\'re back online',\n      'Some features may work offline',\n    ],\n    unknown: [\n      'Try refreshing the page',\n      'Restart the app',\n      'Contact support if the problem continues',\n    ],\n  };\n  \n  return suggestions[type] || suggestions.unknown;\n};\n\n/**\n * Create application error object\n */\nexport const createAppError = (\n  error: any,\n  context: Partial<ErrorContext> = {}\n): AppError => {\n  const type = classifyError(error);\n  const severity = getErrorSeverity(error, type);\n  const userMessage = getUserFriendlyMessage(type, error.message);\n  const suggestions = getErrorSuggestions(type);\n  \n  return {\n    id: generateErrorId(),\n    type,\n    severity,\n    message: error.message || 'Unknown error',\n    userMessage,\n    code: error.code || error.status,\n    context: {\n      timestamp: new Date(),\n      ...context,\n    },\n    originalError: error,\n    recoverable: type !== 'critical',\n    retryable: ['network', 'timeout', 'server'].includes(type),\n    suggestions,\n  };\n};\n\n/**\n * Check if error is retryable\n */\nexport const isRetryableError = (error: AppError): boolean => {\n  return error.retryable && error.retryCount < 3;\n};\n\n/**\n * Get retry delay based on attempt count\n */\nexport const getRetryDelay = (attemptCount: number): number => {\n  // Exponential backoff: 1s, 2s, 4s, 8s...\n  return Math.min(1000 * Math.pow(2, attemptCount), 10000);\n};\n\n/**\n * Log error for debugging and monitoring\n */\nexport const logError = (error: AppError): void => {\n  const logData = {\n    id: error.id,\n    type: error.type,\n    severity: error.severity,\n    message: error.message,\n    code: error.code,\n    context: error.context,\n    stack: error.originalError?.stack,\n  };\n  \n  // In development, log to console\n  if (__DEV__) {\n    console.group(`🚨 Error [${error.severity.toUpperCase()}]`);\n    console.error('Error Details:', logData);\n    console.error('Original Error:', error.originalError);\n    console.groupEnd();\n  }\n  \n  // In production, send to error reporting service\n  // Example: Sentry, Bugsnag, etc.\n  // errorReportingService.captureError(logData);\n};\n\n/**\n * Handle async operation with error handling\n */\nexport const withErrorHandling = async <T>(\n  operation: () => Promise<T>,\n  context: Partial<ErrorContext> = {}\n): Promise<{ data: T | null; error: AppError | null }> => {\n  try {\n    const data = await operation();\n    return { data, error: null };\n  } catch (error) {\n    const appError = createAppError(error, context);\n    logError(appError);\n    return { data: null, error: appError };\n  }\n};\n\n/**\n * Retry operation with exponential backoff\n */\nexport const retryOperation = async <T>(\n  operation: () => Promise<T>,\n  maxRetries: number = 3,\n  context: Partial<ErrorContext> = {}\n): Promise<T> => {\n  let lastError: any;\n  \n  for (let attempt = 0; attempt <= maxRetries; attempt++) {\n    try {\n      return await operation();\n    } catch (error) {\n      lastError = error;\n      \n      if (attempt === maxRetries) {\n        throw createAppError(error, { ...context, retryCount: attempt });\n      }\n      \n      const delay = getRetryDelay(attempt);\n      await new Promise(resolve => setTimeout(resolve, delay));\n    }\n  }\n  \n  throw createAppError(lastError, context);\n};\n\n/**\n * Create error recovery actions\n */\nexport const createRecoveryActions = (error: AppError): ErrorRecoveryAction[] => {\n  const actions: ErrorRecoveryAction[] = [];\n  \n  // Retry action for retryable errors\n  if (error.retryable) {\n    actions.push({\n      label: 'Try Again',\n      action: () => {\n        // This would be implemented by the component using the error\n        window.location.reload();\n      },\n      primary: true,\n    });\n  }\n  \n  // Go back action\n  actions.push({\n    label: 'Go Back',\n    action: () => {\n      if (window.history.length > 1) {\n        window.history.back();\n      } else {\n        // Navigate to home or safe route\n        window.location.href = '/';\n      }\n    },\n  });\n  \n  // Contact support for critical errors\n  if (error.severity === 'critical') {\n    actions.push({\n      label: 'Contact Support',\n      action: () => {\n        // Open support contact method\n        window.open('mailto:<EMAIL>?subject=Error Report&body=' + \n          encodeURIComponent(`Error ID: ${error.id}\\nMessage: ${error.message}`));\n      },\n    });\n  }\n  \n  return actions;\n};\n\n/**\n * Format error for display\n */\nexport const formatErrorForDisplay = (error: AppError): {\n  title: string;\n  message: string;\n  suggestions: string[];\n  actions: ErrorRecoveryAction[];\n} => {\n  const titles: Record<ErrorSeverity, string> = {\n    low: 'Minor Issue',\n    medium: 'Something Went Wrong',\n    high: 'Connection Problem',\n    critical: 'Service Unavailable',\n  };\n  \n  return {\n    title: titles[error.severity],\n    message: error.userMessage,\n    suggestions: error.suggestions,\n    actions: createRecoveryActions(error),\n  };\n};\n\n/**\n * Error boundary helper\n */\nexport const createErrorBoundaryState = (): ErrorBoundaryState => ({\n  hasError: false,\n  error: null,\n  errorId: null,\n  retryCount: 0,\n});\n\nexport default {\n  generateErrorId,\n  classifyError,\n  getErrorSeverity,\n  getUserFriendlyMessage,\n  getErrorSuggestions,\n  createAppError,\n  isRetryableError,\n  getRetryDelay,\n  logError,\n  withErrorHandling,\n  retryOperation,\n  createRecoveryActions,\n  formatErrorForDisplay,\n  createErrorBoundaryState,\n};\n"], "mappings": ";;;;;;AA6EO,IAAMA,eAAe,GAAAC,OAAA,CAAAD,eAAA,GAAG,SAAlBA,eAAeA,CAAA,EAAiB;EAC3C,OAAO,OAAOE,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;AACvE,CAAC;AAKM,IAAMC,aAAa,GAAAP,OAAA,CAAAO,aAAA,GAAG,SAAhBA,aAAaA,CAAIC,KAAU,EAAgB;EAAA,IAAAC,eAAA,EAAAC,cAAA;EACtD,IAAI,CAACF,KAAK,EAAE,OAAO,SAAS;EAG5B,IAAIA,KAAK,CAACG,IAAI,KAAK,cAAc,IAAIH,KAAK,CAACI,IAAI,KAAK,eAAe,EAAE;IACnE,OAAO,SAAS;EAClB;EAGA,IAAIJ,KAAK,CAACG,IAAI,KAAK,cAAc,IAAIH,KAAK,CAACI,IAAI,KAAK,SAAS,EAAE;IAC7D,OAAO,SAAS;EAClB;EAGA,IAAIJ,KAAK,CAACK,MAAM,KAAAJ,eAAA,GAAID,KAAK,CAACM,QAAQ,aAAdL,eAAA,CAAgBI,MAAM,EAAE;IAC1C,IAAMA,MAAM,GAAGL,KAAK,CAACK,MAAM,IAAIL,KAAK,CAACM,QAAQ,CAACD,MAAM;IAEpD,IAAIA,MAAM,KAAK,GAAG,EAAE,OAAO,gBAAgB;IAC3C,IAAIA,MAAM,KAAK,GAAG,EAAE,OAAO,eAAe;IAC1C,IAAIA,MAAM,KAAK,GAAG,EAAE,OAAO,WAAW;IACtC,IAAIA,MAAM,IAAI,GAAG,IAAIA,MAAM,GAAG,GAAG,EAAE,OAAO,QAAQ;IAClD,IAAIA,MAAM,IAAI,GAAG,EAAE,OAAO,QAAQ;EACpC;EAGA,IAAIL,KAAK,CAACG,IAAI,KAAK,iBAAiB,IAAIH,KAAK,CAACO,IAAI,KAAK,YAAY,EAAE;IACnE,OAAO,YAAY;EACrB;EAGA,IAAI,CAAAL,cAAA,GAAAF,KAAK,CAACQ,OAAO,aAAbN,cAAA,CAAeO,QAAQ,CAAC,SAAS,CAAC,IAAIT,KAAK,CAACI,IAAI,KAAK,SAAS,EAAE;IAClE,OAAO,SAAS;EAClB;EAEA,OAAO,SAAS;AAClB,CAAC;AAKM,IAAMM,gBAAgB,GAAAlB,OAAA,CAAAkB,gBAAA,GAAG,SAAnBA,gBAAgBA,CAAIV,KAAU,EAAEO,IAAe,EAAoB;EAE9E,IAAIA,IAAI,KAAK,gBAAgB,IAAIA,IAAI,KAAK,QAAQ,EAAE;IAClD,OAAO,UAAU;EACnB;EAGA,IAAIA,IAAI,KAAK,SAAS,IAAIA,IAAI,KAAK,SAAS,IAAIA,IAAI,KAAK,eAAe,EAAE;IACxE,OAAO,MAAM;EACf;EAGA,IAAIA,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,WAAW,EAAE;IACjD,OAAO,QAAQ;EACjB;EAGA,OAAO,KAAK;AACd,CAAC;AAKM,IAAMI,sBAAsB,GAAAnB,OAAA,CAAAmB,sBAAA,GAAG,SAAzBA,sBAAsBA,CAAIJ,IAAe,EAAEK,eAAwB,EAAa;EAC3F,IAAMC,QAAmC,GAAG;IAC1CC,OAAO,EAAE,gFAAgF;IACzFC,UAAU,EAAE,wCAAwC;IACpDC,cAAc,EAAE,iDAAiD;IACjEC,aAAa,EAAE,oDAAoD;IACnEC,SAAS,EAAE,+CAA+C;IAC1DC,MAAM,EAAE,qEAAqE;IAC7EC,MAAM,EAAE,2DAA2D;IACnEC,OAAO,EAAE,0DAA0D;IACnEC,OAAO,EAAE,kEAAkE;IAC3EC,OAAO,EAAE;EACX,CAAC;EAED,OAAOV,QAAQ,CAACN,IAAI,CAAC,IAAIM,QAAQ,CAACU,OAAO;AAC3C,CAAC;AAKM,IAAMC,mBAAmB,GAAAhC,OAAA,CAAAgC,mBAAA,GAAG,SAAtBA,mBAAmBA,CAAIjB,IAAe,EAAe;EAChE,IAAMkB,WAAwC,GAAG;IAC/CX,OAAO,EAAE,CACP,gCAAgC,EAChC,4CAA4C,EAC5C,mCAAmC,CACpC;IACDC,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,6BAA6B,CAC9B;IACDC,cAAc,EAAE,CACd,qCAAqC,EACrC,+BAA+B,EAC/B,yCAAyC,CAC1C;IACDC,aAAa,EAAE,CACb,uCAAuC,EACvC,iDAAiD,EACjD,yBAAyB,CAC1B;IACDC,SAAS,EAAE,CACT,yBAAyB,EACzB,uBAAuB,EACvB,+CAA+C,CAChD;IACDC,MAAM,EAAE,CACN,kCAAkC,EAClC,mCAAmC,EACnC,uCAAuC,CACxC;IACDC,MAAM,EAAE,CACN,gCAAgC,EAChC,0BAA0B,EAC1B,+BAA+B,CAChC;IACDC,OAAO,EAAE,CACP,sCAAsC,EACtC,oCAAoC,EACpC,wCAAwC,CACzC;IACDC,OAAO,EAAE,CACP,gCAAgC,EAChC,oCAAoC,EACpC,gCAAgC,CACjC;IACDC,OAAO,EAAE,CACP,yBAAyB,EACzB,iBAAiB,EACjB,0CAA0C;EAE9C,CAAC;EAED,OAAOE,WAAW,CAAClB,IAAI,CAAC,IAAIkB,WAAW,CAACF,OAAO;AACjD,CAAC;AAKM,IAAMG,cAAc,GAAAlC,OAAA,CAAAkC,cAAA,GAAG,SAAjBA,cAAcA,CACzB1B,KAAU,EAEG;EAAA,IADb2B,OAA8B,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAEnC,IAAMrB,IAAI,GAAGR,aAAa,CAACC,KAAK,CAAC;EACjC,IAAM+B,QAAQ,GAAGrB,gBAAgB,CAACV,KAAK,EAAEO,IAAI,CAAC;EAC9C,IAAMyB,WAAW,GAAGrB,sBAAsB,CAACJ,IAAI,EAAEP,KAAK,CAACQ,OAAO,CAAC;EAC/D,IAAMiB,WAAW,GAAGD,mBAAmB,CAACjB,IAAI,CAAC;EAE7C,OAAO;IACL0B,EAAE,EAAE1C,eAAe,CAAC,CAAC;IACrBgB,IAAI,EAAJA,IAAI;IACJwB,QAAQ,EAARA,QAAQ;IACRvB,OAAO,EAAER,KAAK,CAACQ,OAAO,IAAI,eAAe;IACzCwB,WAAW,EAAXA,WAAW;IACX5B,IAAI,EAAEJ,KAAK,CAACI,IAAI,IAAIJ,KAAK,CAACK,MAAM;IAChCsB,OAAO,EAAAO,MAAA,CAAAC,MAAA;MACLC,SAAS,EAAE,IAAI3C,IAAI,CAAC;IAAC,GAClBkC,OAAO,CACX;IACDU,aAAa,EAAErC,KAAK;IACpBsC,WAAW,EAAE/B,IAAI,KAAK,UAAU;IAChCgC,SAAS,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC9B,QAAQ,CAACF,IAAI,CAAC;IAC1DkB,WAAW,EAAXA;EACF,CAAC;AACH,CAAC;AAKM,IAAMe,gBAAgB,GAAAhD,OAAA,CAAAgD,gBAAA,GAAG,SAAnBA,gBAAgBA,CAAIxC,KAAe,EAAc;EAC5D,OAAOA,KAAK,CAACuC,SAAS,IAAIvC,KAAK,CAACyC,UAAU,GAAG,CAAC;AAChD,CAAC;AAKM,IAAMC,aAAa,GAAAlD,OAAA,CAAAkD,aAAA,GAAG,SAAhBA,aAAaA,CAAIC,YAAoB,EAAa;EAE7D,OAAOhD,IAAI,CAACiD,GAAG,CAAC,IAAI,GAAGjD,IAAI,CAACkD,GAAG,CAAC,CAAC,EAAEF,YAAY,CAAC,EAAE,KAAK,CAAC;AAC1D,CAAC;AAKM,IAAMG,QAAQ,GAAAtD,OAAA,CAAAsD,QAAA,GAAG,SAAXA,QAAQA,CAAI9C,KAAe,EAAW;EAAA,IAAA+C,oBAAA;EACjD,IAAMC,OAAO,GAAG;IACdf,EAAE,EAAEjC,KAAK,CAACiC,EAAE;IACZ1B,IAAI,EAAEP,KAAK,CAACO,IAAI;IAChBwB,QAAQ,EAAE/B,KAAK,CAAC+B,QAAQ;IACxBvB,OAAO,EAAER,KAAK,CAACQ,OAAO;IACtBJ,IAAI,EAAEJ,KAAK,CAACI,IAAI;IAChBuB,OAAO,EAAE3B,KAAK,CAAC2B,OAAO;IACtBsB,KAAK,GAAAF,oBAAA,GAAE/C,KAAK,CAACqC,aAAa,qBAAnBU,oBAAA,CAAqBE;EAC9B,CAAC;EAGD,IAAIC,OAAO,EAAE;IACXC,OAAO,CAACC,KAAK,CAAC,aAAapD,KAAK,CAAC+B,QAAQ,CAACsB,WAAW,CAAC,CAAC,GAAG,CAAC;IAC3DF,OAAO,CAACnD,KAAK,CAAC,gBAAgB,EAAEgD,OAAO,CAAC;IACxCG,OAAO,CAACnD,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAACqC,aAAa,CAAC;IACrDc,OAAO,CAACG,QAAQ,CAAC,CAAC;EACpB;AAKF,CAAC;AAKM,IAAMC,iBAAiB,GAAA/D,OAAA,CAAA+D,iBAAA;EAAA,IAAAC,IAAA,OAAAC,kBAAA,CAAAC,OAAA,EAAG,WAC/BC,SAA2B,EAE6B;IAAA,IADxDhC,OAA8B,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAEnC,IAAI;MACF,IAAMgC,IAAI,SAASD,SAAS,CAAC,CAAC;MAC9B,OAAO;QAAEC,IAAI,EAAJA,IAAI;QAAE5D,KAAK,EAAE;MAAK,CAAC;IAC9B,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,IAAM6D,QAAQ,GAAGnC,cAAc,CAAC1B,KAAK,EAAE2B,OAAO,CAAC;MAC/CmB,QAAQ,CAACe,QAAQ,CAAC;MAClB,OAAO;QAAED,IAAI,EAAE,IAAI;QAAE5D,KAAK,EAAE6D;MAAS,CAAC;IACxC;EACF,CAAC;EAAA,gBAZYN,iBAAiBA,CAAAO,EAAA;IAAA,OAAAN,IAAA,CAAAO,KAAA,OAAAnC,SAAA;EAAA;AAAA,GAY7B;AAKM,IAAMoC,cAAc,GAAAxE,OAAA,CAAAwE,cAAA;EAAA,IAAAC,KAAA,OAAAR,kBAAA,CAAAC,OAAA,EAAG,WAC5BC,SAA2B,EAGZ;IAAA,IAFfO,UAAkB,GAAAtC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;IAAA,IACtBD,OAA8B,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAEnC,IAAIuC,SAAc;IAAC,IAAAC,KAAA,aAAAA,MAAA,EAEqC;QACtD,IAAI;UAAA;YAAAC,CAAA,QACWV,SAAS,CAAC;UAAC;QAC1B,CAAC,CAAC,OAAO3D,KAAK,EAAE;UACdmE,SAAS,GAAGnE,KAAK;UAEjB,IAAIsE,OAAO,KAAKJ,UAAU,EAAE;YAC1B,MAAMxC,cAAc,CAAC1B,KAAK,EAAAkC,MAAA,CAAAC,MAAA,KAAOR,OAAO;cAAEc,UAAU,EAAE6B;YAAO,EAAE,CAAC;UAClE;UAEA,IAAMC,KAAK,GAAG7B,aAAa,CAAC4B,OAAO,CAAC;UACpC,MAAM,IAAIE,OAAO,CAAC,UAAAC,OAAO;YAAA,OAAIC,UAAU,CAACD,OAAO,EAAEF,KAAK,CAAC;UAAA,EAAC;QAC1D;MACF,CAAC;MAAAI,IAAA;IAbD,KAAK,IAAIL,OAAO,GAAG,CAAC,EAAEA,OAAO,IAAIJ,UAAU,EAAEI,OAAO,EAAE;MAAAK,IAAA,UAAAP,KAAA;MAAA,IAAAO,IAAA,SAAAA,IAAA,CAAAN,CAAA;IAAA;IAetD,MAAM3C,cAAc,CAACyC,SAAS,EAAExC,OAAO,CAAC;EAC1C,CAAC;EAAA,gBAvBYqC,cAAcA,CAAAY,GAAA;IAAA,OAAAX,KAAA,CAAAF,KAAA,OAAAnC,SAAA;EAAA;AAAA,GAuB1B;AAKM,IAAMiD,qBAAqB,GAAArF,OAAA,CAAAqF,qBAAA,GAAG,SAAxBA,qBAAqBA,CAAI7E,KAAe,EAA4B;EAC/E,IAAM8E,OAA8B,GAAG,EAAE;EAGzC,IAAI9E,KAAK,CAACuC,SAAS,EAAE;IACnBuC,OAAO,CAACC,IAAI,CAAC;MACXC,KAAK,EAAE,WAAW;MAClBC,MAAM,EAAE,SAARA,MAAMA,CAAA,EAAQ;QAEZC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;MAC1B,CAAC;MACDC,OAAO,EAAE;IACX,CAAC,CAAC;EACJ;EAGAP,OAAO,CAACC,IAAI,CAAC;IACXC,KAAK,EAAE,SAAS;IAChBC,MAAM,EAAE,SAARA,MAAMA,CAAA,EAAQ;MACZ,IAAIC,MAAM,CAACI,OAAO,CAACzD,MAAM,GAAG,CAAC,EAAE;QAC7BqD,MAAM,CAACI,OAAO,CAACC,IAAI,CAAC,CAAC;MACvB,CAAC,MAAM;QAELL,MAAM,CAACC,QAAQ,CAACK,IAAI,GAAG,GAAG;MAC5B;IACF;EACF,CAAC,CAAC;EAGF,IAAIxF,KAAK,CAAC+B,QAAQ,KAAK,UAAU,EAAE;IACjC+C,OAAO,CAACC,IAAI,CAAC;MACXC,KAAK,EAAE,iBAAiB;MACxBC,MAAM,EAAE,SAARA,MAAMA,CAAA,EAAQ;QAEZC,MAAM,CAACO,IAAI,CAAC,sDAAsD,GAChEC,kBAAkB,CAAC,aAAa1F,KAAK,CAACiC,EAAE,cAAcjC,KAAK,CAACQ,OAAO,EAAE,CAAC,CAAC;MAC3E;IACF,CAAC,CAAC;EACJ;EAEA,OAAOsE,OAAO;AAChB,CAAC;AAKM,IAAMa,qBAAqB,GAAAnG,OAAA,CAAAmG,qBAAA,GAAG,SAAxBA,qBAAqBA,CAAI3F,KAAe,EAKhD;EACH,IAAM4F,MAAqC,GAAG;IAC5CC,GAAG,EAAE,aAAa;IAClBC,MAAM,EAAE,sBAAsB;IAC9BC,IAAI,EAAE,oBAAoB;IAC1BC,QAAQ,EAAE;EACZ,CAAC;EAED,OAAO;IACLC,KAAK,EAAEL,MAAM,CAAC5F,KAAK,CAAC+B,QAAQ,CAAC;IAC7BvB,OAAO,EAAER,KAAK,CAACgC,WAAW;IAC1BP,WAAW,EAAEzB,KAAK,CAACyB,WAAW;IAC9BqD,OAAO,EAAED,qBAAqB,CAAC7E,KAAK;EACtC,CAAC;AACH,CAAC;AAKM,IAAMkG,wBAAwB,GAAA1G,OAAA,CAAA0G,wBAAA,GAAG,SAA3BA,wBAAwBA,CAAA;EAAA,OAA8B;IACjEC,QAAQ,EAAE,KAAK;IACfnG,KAAK,EAAE,IAAI;IACXoG,OAAO,EAAE,IAAI;IACb3D,UAAU,EAAE;EACd,CAAC;AAAA,CAAC;AAAC,IAAA4D,QAAA,GAAA7G,OAAA,CAAAkE,OAAA,GAEY;EACbnE,eAAe,EAAfA,eAAe;EACfQ,aAAa,EAAbA,aAAa;EACbW,gBAAgB,EAAhBA,gBAAgB;EAChBC,sBAAsB,EAAtBA,sBAAsB;EACtBa,mBAAmB,EAAnBA,mBAAmB;EACnBE,cAAc,EAAdA,cAAc;EACdc,gBAAgB,EAAhBA,gBAAgB;EAChBE,aAAa,EAAbA,aAAa;EACbI,QAAQ,EAARA,QAAQ;EACRS,iBAAiB,EAAjBA,iBAAiB;EACjBS,cAAc,EAAdA,cAAc;EACda,qBAAqB,EAArBA,qBAAqB;EACrBc,qBAAqB,EAArBA,qBAAqB;EACrBO,wBAAwB,EAAxBA;AACF,CAAC", "ignoreList": []}