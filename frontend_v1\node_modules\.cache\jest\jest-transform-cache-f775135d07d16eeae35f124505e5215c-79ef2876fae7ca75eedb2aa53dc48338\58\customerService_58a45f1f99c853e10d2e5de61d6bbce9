b7fa1bd803fc96f6387e1dcb0f197657
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _apiClient = require("./apiClient");
var CustomerService = function () {
  function CustomerService() {
    (0, _classCallCheck2.default)(this, CustomerService);
  }
  return (0, _createClass2.default)(CustomerService, [{
    key: "getServiceCategories",
    value: (function () {
      var _getServiceCategories = (0, _asyncToGenerator2.default)(function* () {
        try {
          var response = yield _apiClient.apiClient.get('/api/v1/catalog/categories/');
          return response.data;
        } catch (error) {
          console.error('Failed to fetch service categories:', error);
          return this.getFallbackCategories();
        }
      });
      function getServiceCategories() {
        return _getServiceCategories.apply(this, arguments);
      }
      return getServiceCategories;
    }())
  }, {
    key: "getFeaturedProviders",
    value: (function () {
      var _getFeaturedProviders = (0, _asyncToGenerator2.default)(function* () {
        var limit = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 10;
        try {
          var response = yield _apiClient.apiClient.get('/api/v1/catalog/providers/featured/', {
            limit: limit
          });
          return response.data.results;
        } catch (error) {
          console.error('Failed to fetch featured providers:', error);
          return [];
        }
      });
      function getFeaturedProviders() {
        return _getFeaturedProviders.apply(this, arguments);
      }
      return getFeaturedProviders;
    }())
  }, {
    key: "getNearbyProviders",
    value: (function () {
      var _getNearbyProviders = (0, _asyncToGenerator2.default)(function* (latitude, longitude) {
        var radius = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 10;
        var limit = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 10;
        try {
          var response = yield _apiClient.apiClient.get('/api/v1/customer/nearby/providers/', {
            lat: latitude,
            lng: longitude,
            radius: radius,
            limit: limit
          });
          return response.data.results;
        } catch (error) {
          console.error('Failed to fetch nearby providers:', error);
          return [];
        }
      });
      function getNearbyProviders(_x, _x2) {
        return _getNearbyProviders.apply(this, arguments);
      }
      return getNearbyProviders;
    }())
  }, {
    key: "getCustomerDashboard",
    value: (function () {
      var _getCustomerDashboard = (0, _asyncToGenerator2.default)(function* () {
        try {
          var response = yield _apiClient.apiClient.get('/api/v1/customer/dashboard/');
          return response.data;
        } catch (error) {
          console.error('Failed to fetch customer dashboard:', error);
          return this.getFallbackDashboard();
        }
      });
      function getCustomerDashboard() {
        return _getCustomerDashboard.apply(this, arguments);
      }
      return getCustomerDashboard;
    }())
  }, {
    key: "getCustomerProfile",
    value: (function () {
      var _getCustomerProfile = (0, _asyncToGenerator2.default)(function* () {
        try {
          var response = yield _apiClient.apiClient.get('/api/v1/customer/profile/');
          return response.data;
        } catch (error) {
          console.error('Failed to fetch customer profile:', error);
          throw error;
        }
      });
      function getCustomerProfile() {
        return _getCustomerProfile.apply(this, arguments);
      }
      return getCustomerProfile;
    }())
  }, {
    key: "getPersonalizedRecommendations",
    value: (function () {
      var _getPersonalizedRecommendations = (0, _asyncToGenerator2.default)(function* () {
        try {
          var response = yield _apiClient.apiClient.get('/api/v1/customer/recommendations/personalized/');
          return response.data.results;
        } catch (error) {
          console.error('Failed to fetch personalized recommendations:', error);
          return [];
        }
      });
      function getPersonalizedRecommendations() {
        return _getPersonalizedRecommendations.apply(this, arguments);
      }
      return getPersonalizedRecommendations;
    }())
  }, {
    key: "getFavoriteProviders",
    value: (function () {
      var _getFavoriteProviders = (0, _asyncToGenerator2.default)(function* () {
        try {
          var response = yield _apiClient.apiClient.get('/api/v1/customer/favorites/');
          return response.data.results;
        } catch (error) {
          console.error('Failed to fetch favorite providers:', error);
          return [];
        }
      });
      function getFavoriteProviders() {
        return _getFavoriteProviders.apply(this, arguments);
      }
      return getFavoriteProviders;
    }())
  }, {
    key: "createQuickBooking",
    value: (function () {
      var _createQuickBooking = (0, _asyncToGenerator2.default)(function* (bookingData) {
        try {
          var response = yield _apiClient.apiClient.post('/api/v1/customer/bookings/quick-book/', bookingData);
          return response.data;
        } catch (error) {
          console.error('Failed to create quick booking:', error);
          throw error;
        }
      });
      function createQuickBooking(_x3) {
        return _createQuickBooking.apply(this, arguments);
      }
      return createQuickBooking;
    }())
  }, {
    key: "getFallbackCategories",
    value: function getFallbackCategories() {
      return [{
        id: '1',
        name: 'Barber',
        slug: 'barber',
        description: 'Professional barber services',
        icon: 'cut-outline',
        color: '#5A7A63',
        serviceCount: 12,
        isActive: true,
        displayOrder: 1
      }, {
        id: '2',
        name: 'Salon',
        slug: 'salon',
        description: 'Hair salon services',
        icon: 'brush-outline',
        color: '#6B8A74',
        serviceCount: 8,
        isActive: true,
        displayOrder: 2
      }, {
        id: '3',
        name: 'Nail Services',
        slug: 'nail-services',
        description: 'Professional nail care',
        icon: 'hand-left-outline',
        color: '#5A7A63',
        serviceCount: 15,
        isActive: true,
        displayOrder: 3
      }, {
        id: '4',
        name: 'Lash Services',
        slug: 'lash-services',
        description: 'Eyelash extensions and care',
        icon: 'eye-outline',
        color: '#4A6B52',
        serviceCount: 6,
        isActive: true,
        displayOrder: 4
      }, {
        id: '5',
        name: 'Braiding',
        slug: 'braiding',
        description: 'Hair braiding services',
        icon: 'flower-outline',
        color: '#3A5B42',
        serviceCount: 10,
        isActive: true,
        displayOrder: 5
      }, {
        id: '6',
        name: 'Skincare',
        slug: 'skincare',
        description: 'Facial and skincare treatments',
        icon: 'heart-outline',
        color: '#6B8A74',
        serviceCount: 7,
        isActive: true,
        displayOrder: 6
      }, {
        id: '7',
        name: 'Massage',
        slug: 'massage',
        description: 'Therapeutic massage services',
        icon: 'hand-right-outline',
        color: '#5A7A63',
        serviceCount: 8,
        isActive: true,
        displayOrder: 7
      }];
    }
  }, {
    key: "getFallbackDashboard",
    value: function getFallbackDashboard() {
      return {
        greeting: 'Good morning',
        upcomingBookings: 0,
        favoriteProviders: 0,
        recentActivity: [],
        recommendations: []
      };
    }
  }]);
}();
var customerService = new CustomerService();
var _default = exports.default = customerService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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