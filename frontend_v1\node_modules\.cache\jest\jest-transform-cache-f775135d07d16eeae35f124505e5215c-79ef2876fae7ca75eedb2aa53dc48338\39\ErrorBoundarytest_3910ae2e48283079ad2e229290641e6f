b5cdca250cb4fba787f9313ec3b01081
_getJestObj().mock("../../../services/performanceMonitor");
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _react = _interopRequireDefault(require("react"));
var _reactNative = require("@testing-library/react-native");
var _reactNative2 = require("react-native");
var _ErrorBoundary = require("../ErrorBoundary");
var _performanceMonitor = require("../../../services/performanceMonitor");
var _jsxRuntime = require("react/jsx-runtime");
function _getJestObj() {
  var _require = require("@jest/globals"),
    jest = _require.jest;
  _getJestObj = function _getJestObj() {
    return jest;
  };
  return jest;
}
var mockPerformanceMonitor = _performanceMonitor.performanceMonitor;
var ErrorComponent = function ErrorComponent() {
  throw new Error('Test error');
  return null;
};
var ButtonThatThrows = function ButtonThatThrows(_ref) {
  var onError = _ref.onError;
  var _React$useState = _react.default.useState(false),
    _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),
    shouldThrow = _React$useState2[0],
    setShouldThrow = _React$useState2[1];
  if (shouldThrow) {
    try {
      throw new Error('Button error');
    } catch (error) {
      if (onError) onError();
      throw error;
    }
  }
  return (0, _jsxRuntime.jsx)(_reactNative2.Button, {
    title: "Throw Error",
    onPress: function onPress() {
      return setShouldThrow(true);
    },
    testID: "throw-button"
  });
};
var WorkingComponent = function WorkingComponent() {
  return (0, _jsxRuntime.jsx)(_reactNative2.View, {
    testID: "working-component",
    children: (0, _jsxRuntime.jsx)(_reactNative2.Text, {
      children: "Working Component"
    })
  });
};
describe('ErrorBoundary', function () {
  beforeEach(function () {
    jest.clearAllMocks();
    jest.spyOn(console, 'error').mockImplementation(function () {});
  });
  afterEach(function () {
    jest.restoreAllMocks();
  });
  describe('Error Handling', function () {
    it('catches errors in child components', function () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(_ErrorBoundary.ErrorBoundary, {
        children: (0, _jsxRuntime.jsx)(ErrorComponent, {})
      }));
      expect(_reactNative.screen.getByText('Something went wrong')).toBeTruthy();
    });
    it('renders children normally when no errors occur', function () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(_ErrorBoundary.ErrorBoundary, {
        children: (0, _jsxRuntime.jsx)(WorkingComponent, {})
      }));
      expect(_reactNative.screen.getByTestID('working-component')).toBeTruthy();
      expect(_reactNative.screen.getByText('Working Component')).toBeTruthy();
    });
    it('calls onError callback when an error occurs', function () {
      var onError = jest.fn();
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(_ErrorBoundary.ErrorBoundary, {
        onError: onError,
        children: (0, _jsxRuntime.jsx)(ErrorComponent, {})
      }));
      expect(onError).toHaveBeenCalledWith(expect.objectContaining({
        message: 'Test error'
      }));
    });
    it('tracks errors with performance monitor', function () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(_ErrorBoundary.ErrorBoundary, {
        children: (0, _jsxRuntime.jsx)(ErrorComponent, {})
      }));
      expect(mockPerformanceMonitor.trackUserInteraction).toHaveBeenCalledWith('error_boundary_catch', 0, expect.objectContaining({
        error: 'Test error',
        componentStack: expect.any(String)
      }));
    });
  });
  describe('Fallback UI', function () {
    it('renders default fallback UI', function () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(_ErrorBoundary.ErrorBoundary, {
        children: (0, _jsxRuntime.jsx)(ErrorComponent, {})
      }));
      expect(_reactNative.screen.getByText('Something went wrong')).toBeTruthy();
      expect(_reactNative.screen.getByText(/We're sorry, but an error occurred/)).toBeTruthy();
      expect(_reactNative.screen.getByText('Try Again')).toBeTruthy();
    });
    it('renders custom fallback UI when provided', function () {
      var CustomFallback = function CustomFallback(_ref2) {
        var error = _ref2.error,
          retry = _ref2.retry;
        return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          testID: "custom-fallback",
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.Text, {
            children: ["Custom Error: ", error.message]
          }), (0, _jsxRuntime.jsx)(_reactNative2.Button, {
            title: "Custom Retry",
            onPress: retry,
            testID: "custom-retry"
          })]
        });
      };
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(_ErrorBoundary.ErrorBoundary, {
        fallback: function fallback(error, retry) {
          return (0, _jsxRuntime.jsx)(CustomFallback, {
            error: error,
            retry: retry
          });
        },
        children: (0, _jsxRuntime.jsx)(ErrorComponent, {})
      }));
      expect(_reactNative.screen.getByTestID('custom-fallback')).toBeTruthy();
      expect(_reactNative.screen.getByText('Custom Error: Test error')).toBeTruthy();
      expect(_reactNative.screen.getByText('Custom Retry')).toBeTruthy();
    });
    it('shows technical details in development mode', function () {
      global.__DEV__ = true;
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(_ErrorBoundary.ErrorBoundary, {
        children: (0, _jsxRuntime.jsx)(ErrorComponent, {})
      }));
      expect(_reactNative.screen.getByText(/Technical Details/)).toBeTruthy();
      expect(_reactNative.screen.getByText('Test error')).toBeTruthy();
      global.__DEV__ = false;
    });
  });
  describe('Reset Functionality', function () {
    it('resets error state when retry button is pressed', (0, _asyncToGenerator2.default)(function* () {
      var TestComponent = function TestComponent() {
        var _React$useState3 = _react.default.useState(true),
          _React$useState4 = (0, _slicedToArray2.default)(_React$useState3, 2),
          shouldError = _React$useState4[0],
          setShouldError = _React$useState4[1];
        _react.default.useEffect(function () {
          if (shouldError) {
            setShouldError(false);
          }
        }, [shouldError]);
        if (shouldError) {
          throw new Error('Initial error');
        }
        return (0, _jsxRuntime.jsx)(_reactNative2.Text, {
          children: "Recovered Component"
        });
      };
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(_ErrorBoundary.ErrorBoundary, {
        children: (0, _jsxRuntime.jsx)(TestComponent, {})
      }));
      expect(_reactNative.screen.getByText('Something went wrong')).toBeTruthy();
      _reactNative.fireEvent.press(_reactNative.screen.getByText('Try Again'));
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('Recovered Component')).toBeTruthy();
      });
    }));
    it('respects maxRetries limit', (0, _asyncToGenerator2.default)(function* () {
      var maxRetries = 2;
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(_ErrorBoundary.ErrorBoundary, {
        maxRetries: maxRetries,
        children: (0, _jsxRuntime.jsx)(ErrorComponent, {})
      }));
      for (var i = 0; i < maxRetries; i++) {
        _reactNative.fireEvent.press(_reactNative.screen.getByText('Try Again'));
      }
      var retryButton = _reactNative.screen.getByText('Try Again');
      expect(retryButton.props.disabled).toBe(true);
    }));
    it('resets on props change when resetOnPropsChange is true', (0, _asyncToGenerator2.default)(function* () {
      var TestWrapper = function TestWrapper(_ref6) {
        var children = _ref6.children;
        var _React$useState5 = _react.default.useState(1),
          _React$useState6 = (0, _slicedToArray2.default)(_React$useState5, 2),
          key = _React$useState6[0],
          setKey = _React$useState6[1];
        return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          children: [(0, _jsxRuntime.jsx)(_ErrorBoundary.ErrorBoundary, {
            resetOnPropsChange: true,
            children: children
          }), (0, _jsxRuntime.jsx)(_reactNative2.Button, {
            title: "Change Props",
            onPress: function onPress() {
              return setKey(function (k) {
                return k + 1;
              });
            },
            testID: "change-props"
          })]
        });
      };
      var _render = (0, _reactNative.render)((0, _jsxRuntime.jsx)(TestWrapper, {
          children: (0, _jsxRuntime.jsx)(ErrorComponent, {})
        })),
        rerender = _render.rerender;
      expect(_reactNative.screen.getByText('Something went wrong')).toBeTruthy();
      _reactNative.fireEvent.press(_reactNative.screen.getByTestID('change-props'));
      expect(_reactNative.screen.getByText('Something went wrong')).toBeTruthy();
    }));
  });
  describe('Dynamic Error Handling', function () {
    it('catches runtime errors from user interactions', (0, _asyncToGenerator2.default)(function* () {
      var onError = jest.fn();
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(_ErrorBoundary.ErrorBoundary, {
        onError: onError,
        children: (0, _jsxRuntime.jsx)(ButtonThatThrows, {
          onError: onError
        })
      }));
      expect(_reactNative.screen.getByTestID('throw-button')).toBeTruthy();
      _reactNative.fireEvent.press(_reactNative.screen.getByTestID('throw-button'));
      yield (0, _reactNative.waitFor)(function () {
        expect(_reactNative.screen.getByText('Something went wrong')).toBeTruthy();
      });
      expect(onError).toHaveBeenCalledWith(expect.objectContaining({
        message: 'Button error'
      }));
    }));
    it('handles nested error boundaries correctly', function () {
      var OuterFallback = function OuterFallback() {
        return (0, _jsxRuntime.jsx)(_reactNative2.Text, {
          children: "Outer Error"
        });
      };
      var InnerFallback = function InnerFallback() {
        return (0, _jsxRuntime.jsx)(_reactNative2.Text, {
          children: "Inner Error"
        });
      };
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(_ErrorBoundary.ErrorBoundary, {
        fallback: function fallback() {
          return (0, _jsxRuntime.jsx)(OuterFallback, {});
        },
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          children: [(0, _jsxRuntime.jsx)(_reactNative2.Text, {
            children: "Outer Content"
          }), (0, _jsxRuntime.jsx)(_ErrorBoundary.ErrorBoundary, {
            fallback: function fallback() {
              return (0, _jsxRuntime.jsx)(InnerFallback, {});
            },
            children: (0, _jsxRuntime.jsx)(ErrorComponent, {})
          })]
        })
      }));
      expect(_reactNative.screen.getByText('Inner Error')).toBeTruthy();
      expect(_reactNative.screen.getByText('Outer Content')).toBeTruthy();
      expect(_reactNative.screen.queryByText('Outer Error')).toBeNull();
    });
  });
  describe('Accessibility', function () {
    it('announces errors to screen readers', function () {
      _getJestObj().mock('react-native', function () {
        var rn = jest.requireActual('react-native');
        return Object.assign({}, rn, {
          AccessibilityInfo: Object.assign({}, rn.AccessibilityInfo, {
            announceForAccessibility: mockAnnounce
          })
        });
      });
      var mockAnnounce = jest.fn();
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(_ErrorBoundary.ErrorBoundary, {
        children: (0, _jsxRuntime.jsx)(ErrorComponent, {})
      }));
      expect(mockAnnounce).toHaveBeenCalledWith(expect.stringContaining('error occurred'));
    });
    it('provides proper accessibility props on fallback UI', function () {
      (0, _reactNative.render)((0, _jsxRuntime.jsx)(_ErrorBoundary.ErrorBoundary, {
        children: (0, _jsxRuntime.jsx)(ErrorComponent, {})
      }));
      var errorContainer = _reactNative.screen.getByTestID('error-boundary-container');
      expect(errorContainer.props.accessibilityRole).toBe('alert');
      var retryButton = _reactNative.screen.getByText('Try Again');
      expect(retryButton.props.accessibilityRole).toBe('button');
      expect(retryButton.props.accessibilityLabel).toBeTruthy();
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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