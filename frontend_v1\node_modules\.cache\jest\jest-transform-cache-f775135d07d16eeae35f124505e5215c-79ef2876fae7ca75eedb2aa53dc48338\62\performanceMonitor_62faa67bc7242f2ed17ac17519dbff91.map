{"version": 3, "names": ["PerformanceMonitorService", "_classCallCheck2", "default", "metrics", "renderMetrics", "Map", "networkMetrics", "memoryMetrics", "frameDropCount", "isMonitoring", "MAX_METRICS", "SLOW_RENDER_THRESHOLD", "SLOW_NETWORK_THRESHOLD", "MEMORY_LEAK_THRESHOLD", "_createClass2", "key", "value", "startMonitoring", "_this", "console", "log", "monitoringInterval", "setInterval", "collectMemoryMetrics", "detectMemoryLeaks", "cleanupOldMetrics", "startFrameMonitoring", "stopMonitoring", "clearInterval", "trackRender", "componentName", "renderTime", "metadata", "existing", "get", "reRenders", "lastRenderTime", "Date", "now", "propsCount", "stateUpdates", "set", "addMetric", "name", "timestamp", "category", "Object", "assign", "warn", "trackNetworkRequest", "url", "method", "responseTime", "statusCode", "requestSize", "arguments", "length", "undefined", "responseSize", "cached", "metric", "push", "slice", "trackUserInteraction", "interactionType", "trackNavigation", "fromScreen", "toScreen", "navigationTime", "getPerformanceReport", "_this2", "recentMetrics", "filter", "m", "averageRenderTime", "reduce", "sum", "averageNetworkTime", "slowComponents", "Array", "from", "values", "c", "sort", "a", "b", "slowNetworkRequests", "n", "cachedRequests", "totalRequests", "cacheHitRate", "latestMemory", "memoryUsage", "usedJSHeapSize", "recommendations", "generateRecommendations", "summary", "frameDrops", "memoryLeaks", "getMetricsByCategory", "clearMetrics", "clear", "performance", "memory", "totalJSHeapSize", "jsHeapSizeLimit", "leaks", "recent", "growth", "Math", "round", "_ref", "entries", "_ref2", "_slicedToArray2", "__DEV__", "cutoff", "data", "map", "join", "performanceMonitor", "exports", "_default"], "sources": ["performanceMonitor.ts"], "sourcesContent": ["/**\n * Performance Monitor Service - Track and optimize app performance\n *\n * Service Contract:\n * - Monitors render performance and frame drops\n * - Tracks memory usage and garbage collection\n * - Measures API response times and network performance\n * - Provides performance analytics and alerts\n * - Implements performance optimization suggestions\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\ninterface PerformanceMetric {\n  name: string;\n  value: number;\n  timestamp: number;\n  category: 'render' | 'network' | 'memory' | 'navigation' | 'user_interaction';\n  metadata?: Record<string, any>;\n}\n\ninterface RenderMetrics {\n  componentName: string;\n  renderTime: number;\n  propsCount: number;\n  stateUpdates: number;\n  reRenders: number;\n  lastRenderTime: number;\n}\n\ninterface NetworkMetrics {\n  url: string;\n  method: string;\n  responseTime: number;\n  statusCode: number;\n  requestSize: number;\n  responseSize: number;\n  cached: boolean;\n  timestamp: number;\n}\n\ninterface MemoryMetrics {\n  usedJSHeapSize: number;\n  totalJSHeapSize: number;\n  jsHeapSizeLimit: number;\n  timestamp: number;\n}\n\ninterface PerformanceReport {\n  summary: {\n    averageRenderTime: number;\n    averageNetworkTime: number;\n    memoryUsage: number;\n    frameDrops: number;\n    cacheHitRate: number;\n  };\n  slowComponents: RenderMetrics[];\n  slowNetworkRequests: NetworkMetrics[];\n  memoryLeaks: string[];\n  recommendations: string[];\n}\n\nclass PerformanceMonitorService {\n  private metrics: PerformanceMetric[] = [];\n  private renderMetrics = new Map<string, RenderMetrics>();\n  private networkMetrics: NetworkMetrics[] = [];\n  private memoryMetrics: MemoryMetrics[] = [];\n  private frameDropCount = 0;\n  private isMonitoring = false;\n  private monitoringInterval?: NodeJS.Timeout;\n\n  private readonly MAX_METRICS = 1000;\n  private readonly SLOW_RENDER_THRESHOLD = 16; // 16ms for 60fps\n  private readonly SLOW_NETWORK_THRESHOLD = 2000; // 2 seconds\n  private readonly MEMORY_LEAK_THRESHOLD = 50 * 1024 * 1024; // 50MB\n\n  /**\n   * Start performance monitoring\n   */\n  startMonitoring(): void {\n    if (this.isMonitoring) return;\n\n    this.isMonitoring = true;\n    console.log('📊 Performance Monitor: Started');\n\n    // Monitor memory usage periodically\n    this.monitoringInterval = setInterval(() => {\n      this.collectMemoryMetrics();\n      this.detectMemoryLeaks();\n      this.cleanupOldMetrics();\n    }, 5000); // Every 5 seconds\n\n    // Monitor frame drops (if available)\n    this.startFrameMonitoring();\n  }\n\n  /**\n   * Stop performance monitoring\n   */\n  stopMonitoring(): void {\n    if (!this.isMonitoring) return;\n\n    this.isMonitoring = false;\n    console.log('📊 Performance Monitor: Stopped');\n\n    if (this.monitoringInterval) {\n      clearInterval(this.monitoringInterval);\n    }\n  }\n\n  /**\n   * Track component render performance\n   */\n  trackRender(componentName: string, renderTime: number, metadata?: Record<string, any>): void {\n    const existing = this.renderMetrics.get(componentName);\n    \n    if (existing) {\n      existing.renderTime = (existing.renderTime + renderTime) / 2; // Moving average\n      existing.reRenders++;\n      existing.lastRenderTime = Date.now();\n      if (metadata?.propsCount) existing.propsCount = metadata.propsCount;\n      if (metadata?.stateUpdates) existing.stateUpdates += metadata.stateUpdates;\n    } else {\n      this.renderMetrics.set(componentName, {\n        componentName,\n        renderTime,\n        propsCount: metadata?.propsCount || 0,\n        stateUpdates: metadata?.stateUpdates || 0,\n        reRenders: 1,\n        lastRenderTime: Date.now(),\n      });\n    }\n\n    // Track as general metric\n    this.addMetric({\n      name: 'component_render',\n      value: renderTime,\n      timestamp: Date.now(),\n      category: 'render',\n      metadata: { componentName, ...metadata },\n    });\n\n    // Alert on slow renders\n    if (renderTime > this.SLOW_RENDER_THRESHOLD) {\n      console.warn(`🐌 Slow render detected: ${componentName} took ${renderTime}ms`);\n    }\n  }\n\n  /**\n   * Track network request performance\n   */\n  trackNetworkRequest(\n    url: string,\n    method: string,\n    responseTime: number,\n    statusCode: number,\n    requestSize: number = 0,\n    responseSize: number = 0,\n    cached: boolean = false\n  ): void {\n    const metric: NetworkMetrics = {\n      url,\n      method,\n      responseTime,\n      statusCode,\n      requestSize,\n      responseSize,\n      cached,\n      timestamp: Date.now(),\n    };\n\n    this.networkMetrics.push(metric);\n\n    // Track as general metric\n    this.addMetric({\n      name: 'network_request',\n      value: responseTime,\n      timestamp: Date.now(),\n      category: 'network',\n      metadata: { url, method, statusCode, cached },\n    });\n\n    // Alert on slow requests\n    if (responseTime > this.SLOW_NETWORK_THRESHOLD) {\n      console.warn(`🐌 Slow network request: ${method} ${url} took ${responseTime}ms`);\n    }\n\n    // Keep only recent network metrics\n    if (this.networkMetrics.length > this.MAX_METRICS) {\n      this.networkMetrics = this.networkMetrics.slice(-this.MAX_METRICS / 2);\n    }\n  }\n\n  /**\n   * Track user interaction performance\n   */\n  trackUserInteraction(\n    interactionType: string,\n    responseTime: number,\n    metadata?: Record<string, any>\n  ): void {\n    this.addMetric({\n      name: 'user_interaction',\n      value: responseTime,\n      timestamp: Date.now(),\n      category: 'user_interaction',\n      metadata: { interactionType, ...metadata },\n    });\n\n    // Alert on slow interactions\n    if (responseTime > 100) { // 100ms threshold for interactions\n      console.warn(`🐌 Slow interaction: ${interactionType} took ${responseTime}ms`);\n    }\n  }\n\n  /**\n   * Track navigation performance\n   */\n  trackNavigation(\n    fromScreen: string,\n    toScreen: string,\n    navigationTime: number,\n    metadata?: Record<string, any>\n  ): void {\n    this.addMetric({\n      name: 'navigation',\n      value: navigationTime,\n      timestamp: Date.now(),\n      category: 'navigation',\n      metadata: { fromScreen, toScreen, ...metadata },\n    });\n  }\n\n  /**\n   * Get performance report\n   */\n  getPerformanceReport(): PerformanceReport {\n    const now = Date.now();\n    const recentMetrics = this.metrics.filter(m => now - m.timestamp < 300000); // Last 5 minutes\n\n    // Calculate averages\n    const renderMetrics = recentMetrics.filter(m => m.category === 'render');\n    const networkMetrics = recentMetrics.filter(m => m.category === 'network');\n    \n    const averageRenderTime = renderMetrics.length > 0 \n      ? renderMetrics.reduce((sum, m) => sum + m.value, 0) / renderMetrics.length \n      : 0;\n\n    const averageNetworkTime = networkMetrics.length > 0\n      ? networkMetrics.reduce((sum, m) => sum + m.value, 0) / networkMetrics.length\n      : 0;\n\n    // Get slow components\n    const slowComponents = Array.from(this.renderMetrics.values())\n      .filter(c => c.renderTime > this.SLOW_RENDER_THRESHOLD)\n      .sort((a, b) => b.renderTime - a.renderTime)\n      .slice(0, 10);\n\n    // Get slow network requests\n    const slowNetworkRequests = this.networkMetrics\n      .filter(n => n.responseTime > this.SLOW_NETWORK_THRESHOLD)\n      .sort((a, b) => b.responseTime - a.responseTime)\n      .slice(0, 10);\n\n    // Calculate cache hit rate\n    const cachedRequests = this.networkMetrics.filter(n => n.cached).length;\n    const totalRequests = this.networkMetrics.length;\n    const cacheHitRate = totalRequests > 0 ? cachedRequests / totalRequests : 0;\n\n    // Get current memory usage\n    const latestMemory = this.memoryMetrics[this.memoryMetrics.length - 1];\n    const memoryUsage = latestMemory ? latestMemory.usedJSHeapSize : 0;\n\n    // Generate recommendations\n    const recommendations = this.generateRecommendations({\n      averageRenderTime,\n      averageNetworkTime,\n      slowComponents,\n      slowNetworkRequests,\n      cacheHitRate,\n      memoryUsage,\n    });\n\n    return {\n      summary: {\n        averageRenderTime,\n        averageNetworkTime,\n        memoryUsage,\n        frameDrops: this.frameDropCount,\n        cacheHitRate,\n      },\n      slowComponents,\n      slowNetworkRequests,\n      memoryLeaks: this.detectMemoryLeaks(),\n      recommendations,\n    };\n  }\n\n  /**\n   * Get metrics by category\n   */\n  getMetricsByCategory(category: PerformanceMetric['category']): PerformanceMetric[] {\n    return this.metrics.filter(m => m.category === category);\n  }\n\n  /**\n   * Clear all metrics\n   */\n  clearMetrics(): void {\n    this.metrics = [];\n    this.renderMetrics.clear();\n    this.networkMetrics = [];\n    this.memoryMetrics = [];\n    this.frameDropCount = 0;\n  }\n\n  /**\n   * Add a performance metric\n   */\n  private addMetric(metric: PerformanceMetric): void {\n    this.metrics.push(metric);\n\n    // Keep only recent metrics\n    if (this.metrics.length > this.MAX_METRICS) {\n      this.metrics = this.metrics.slice(-this.MAX_METRICS / 2);\n    }\n  }\n\n  /**\n   * Collect memory metrics\n   */\n  private collectMemoryMetrics(): void {\n    // In React Native, memory metrics are limited\n    // This is a placeholder for when performance.memory is available\n    if (typeof performance !== 'undefined' && (performance as any).memory) {\n      const memory = (performance as any).memory;\n      this.memoryMetrics.push({\n        usedJSHeapSize: memory.usedJSHeapSize,\n        totalJSHeapSize: memory.totalJSHeapSize,\n        jsHeapSizeLimit: memory.jsHeapSizeLimit,\n        timestamp: Date.now(),\n      });\n\n      // Keep only recent memory metrics\n      if (this.memoryMetrics.length > 100) {\n        this.memoryMetrics = this.memoryMetrics.slice(-50);\n      }\n    }\n  }\n\n  /**\n   * Detect potential memory leaks\n   */\n  private detectMemoryLeaks(): string[] {\n    const leaks: string[] = [];\n\n    if (this.memoryMetrics.length < 10) return leaks;\n\n    // Check for consistent memory growth\n    const recent = this.memoryMetrics.slice(-10);\n    const growth = recent[recent.length - 1].usedJSHeapSize - recent[0].usedJSHeapSize;\n    \n    if (growth > this.MEMORY_LEAK_THRESHOLD) {\n      leaks.push(`Memory usage increased by ${Math.round(growth / 1024 / 1024)}MB in recent measurements`);\n    }\n\n    // Check for components with excessive re-renders\n    for (const [name, metrics] of this.renderMetrics.entries()) {\n      if (metrics.reRenders > 100) {\n        leaks.push(`Component ${name} has ${metrics.reRenders} re-renders`);\n      }\n    }\n\n    return leaks;\n  }\n\n  /**\n   * Start frame monitoring (if available)\n   */\n  private startFrameMonitoring(): void {\n    // This would use platform-specific APIs to monitor frame drops\n    // For now, it's a placeholder\n    if (__DEV__) {\n      console.log('📊 Frame monitoring started (placeholder)');\n    }\n  }\n\n  /**\n   * Clean up old metrics\n   */\n  private cleanupOldMetrics(): void {\n    const cutoff = Date.now() - 600000; // 10 minutes ago\n    this.metrics = this.metrics.filter(m => m.timestamp > cutoff);\n  }\n\n  /**\n   * Generate performance recommendations\n   */\n  private generateRecommendations(data: {\n    averageRenderTime: number;\n    averageNetworkTime: number;\n    slowComponents: RenderMetrics[];\n    slowNetworkRequests: NetworkMetrics[];\n    cacheHitRate: number;\n    memoryUsage: number;\n  }): string[] {\n    const recommendations: string[] = [];\n\n    if (data.averageRenderTime > this.SLOW_RENDER_THRESHOLD) {\n      recommendations.push('Consider optimizing component renders with React.memo or useMemo');\n    }\n\n    if (data.slowComponents.length > 0) {\n      recommendations.push(`Optimize slow components: ${data.slowComponents.slice(0, 3).map(c => c.componentName).join(', ')}`);\n    }\n\n    if (data.averageNetworkTime > 1000) {\n      recommendations.push('Consider implementing request caching or optimizing API endpoints');\n    }\n\n    if (data.cacheHitRate < 0.5) {\n      recommendations.push('Improve cache hit rate by implementing better caching strategies');\n    }\n\n    if (data.memoryUsage > 100 * 1024 * 1024) { // 100MB\n      recommendations.push('High memory usage detected - check for memory leaks');\n    }\n\n    if (data.slowNetworkRequests.length > 5) {\n      recommendations.push('Multiple slow network requests detected - consider request batching');\n    }\n\n    return recommendations;\n  }\n}\n\n// Export singleton instance\nexport const performanceMonitor = new PerformanceMonitorService();\nexport default performanceMonitor;\n"], "mappings": ";;;;;;;;IA+DMA,yBAAyB;EAAA,SAAAA,0BAAA;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAF,yBAAA;IAAA,KACrBG,OAAO,GAAwB,EAAE;IAAA,KACjCC,aAAa,GAAG,IAAIC,GAAG,CAAwB,CAAC;IAAA,KAChDC,cAAc,GAAqB,EAAE;IAAA,KACrCC,aAAa,GAAoB,EAAE;IAAA,KACnCC,cAAc,GAAG,CAAC;IAAA,KAClBC,YAAY,GAAG,KAAK;IAAA,KAGXC,WAAW,GAAG,IAAI;IAAA,KAClBC,qBAAqB,GAAG,EAAE;IAAA,KAC1BC,sBAAsB,GAAG,IAAI;IAAA,KAC7BC,qBAAqB,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI;EAAA;EAAA,WAAAC,aAAA,CAAAZ,OAAA,EAAAF,yBAAA;IAAAe,GAAA;IAAAC,KAAA,EAKzD,SAAAC,eAAeA,CAAA,EAAS;MAAA,IAAAC,KAAA;MACtB,IAAI,IAAI,CAACT,YAAY,EAAE;MAEvB,IAAI,CAACA,YAAY,GAAG,IAAI;MACxBU,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;MAG9C,IAAI,CAACC,kBAAkB,GAAGC,WAAW,CAAC,YAAM;QAC1CJ,KAAI,CAACK,oBAAoB,CAAC,CAAC;QAC3BL,KAAI,CAACM,iBAAiB,CAAC,CAAC;QACxBN,KAAI,CAACO,iBAAiB,CAAC,CAAC;MAC1B,CAAC,EAAE,IAAI,CAAC;MAGR,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC7B;EAAC;IAAAX,GAAA;IAAAC,KAAA,EAKD,SAAAW,cAAcA,CAAA,EAAS;MACrB,IAAI,CAAC,IAAI,CAAClB,YAAY,EAAE;MAExB,IAAI,CAACA,YAAY,GAAG,KAAK;MACzBU,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;MAE9C,IAAI,IAAI,CAACC,kBAAkB,EAAE;QAC3BO,aAAa,CAAC,IAAI,CAACP,kBAAkB,CAAC;MACxC;IACF;EAAC;IAAAN,GAAA;IAAAC,KAAA,EAKD,SAAAa,WAAWA,CAACC,aAAqB,EAAEC,UAAkB,EAAEC,QAA8B,EAAQ;MAC3F,IAAMC,QAAQ,GAAG,IAAI,CAAC7B,aAAa,CAAC8B,GAAG,CAACJ,aAAa,CAAC;MAEtD,IAAIG,QAAQ,EAAE;QACZA,QAAQ,CAACF,UAAU,GAAG,CAACE,QAAQ,CAACF,UAAU,GAAGA,UAAU,IAAI,CAAC;QAC5DE,QAAQ,CAACE,SAAS,EAAE;QACpBF,QAAQ,CAACG,cAAc,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;QACpC,IAAIN,QAAQ,YAARA,QAAQ,CAAEO,UAAU,EAAEN,QAAQ,CAACM,UAAU,GAAGP,QAAQ,CAACO,UAAU;QACnE,IAAIP,QAAQ,YAARA,QAAQ,CAAEQ,YAAY,EAAEP,QAAQ,CAACO,YAAY,IAAIR,QAAQ,CAACQ,YAAY;MAC5E,CAAC,MAAM;QACL,IAAI,CAACpC,aAAa,CAACqC,GAAG,CAACX,aAAa,EAAE;UACpCA,aAAa,EAAbA,aAAa;UACbC,UAAU,EAAVA,UAAU;UACVQ,UAAU,EAAE,CAAAP,QAAQ,oBAARA,QAAQ,CAAEO,UAAU,KAAI,CAAC;UACrCC,YAAY,EAAE,CAAAR,QAAQ,oBAARA,QAAQ,CAAEQ,YAAY,KAAI,CAAC;UACzCL,SAAS,EAAE,CAAC;UACZC,cAAc,EAAEC,IAAI,CAACC,GAAG,CAAC;QAC3B,CAAC,CAAC;MACJ;MAGA,IAAI,CAACI,SAAS,CAAC;QACbC,IAAI,EAAE,kBAAkB;QACxB3B,KAAK,EAAEe,UAAU;QACjBa,SAAS,EAAEP,IAAI,CAACC,GAAG,CAAC,CAAC;QACrBO,QAAQ,EAAE,QAAQ;QAClBb,QAAQ,EAAAc,MAAA,CAAAC,MAAA;UAAIjB,aAAa,EAAbA;QAAa,GAAKE,QAAQ;MACxC,CAAC,CAAC;MAGF,IAAID,UAAU,GAAG,IAAI,CAACpB,qBAAqB,EAAE;QAC3CQ,OAAO,CAAC6B,IAAI,CAAC,4BAA4BlB,aAAa,SAASC,UAAU,IAAI,CAAC;MAChF;IACF;EAAC;IAAAhB,GAAA;IAAAC,KAAA,EAKD,SAAAiC,mBAAmBA,CACjBC,GAAW,EACXC,MAAc,EACdC,YAAoB,EACpBC,UAAkB,EAIZ;MAAA,IAHNC,WAAmB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;MAAA,IACvBG,YAAoB,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;MAAA,IACxBI,MAAe,GAAAJ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;MAEvB,IAAMK,MAAsB,GAAG;QAC7BV,GAAG,EAAHA,GAAG;QACHC,MAAM,EAANA,MAAM;QACNC,YAAY,EAAZA,YAAY;QACZC,UAAU,EAAVA,UAAU;QACVC,WAAW,EAAXA,WAAW;QACXI,YAAY,EAAZA,YAAY;QACZC,MAAM,EAANA,MAAM;QACNf,SAAS,EAAEP,IAAI,CAACC,GAAG,CAAC;MACtB,CAAC;MAED,IAAI,CAAChC,cAAc,CAACuD,IAAI,CAACD,MAAM,CAAC;MAGhC,IAAI,CAAClB,SAAS,CAAC;QACbC,IAAI,EAAE,iBAAiB;QACvB3B,KAAK,EAAEoC,YAAY;QACnBR,SAAS,EAAEP,IAAI,CAACC,GAAG,CAAC,CAAC;QACrBO,QAAQ,EAAE,SAAS;QACnBb,QAAQ,EAAE;UAAEkB,GAAG,EAAHA,GAAG;UAAEC,MAAM,EAANA,MAAM;UAAEE,UAAU,EAAVA,UAAU;UAAEM,MAAM,EAANA;QAAO;MAC9C,CAAC,CAAC;MAGF,IAAIP,YAAY,GAAG,IAAI,CAACxC,sBAAsB,EAAE;QAC9CO,OAAO,CAAC6B,IAAI,CAAC,4BAA4BG,MAAM,IAAID,GAAG,SAASE,YAAY,IAAI,CAAC;MAClF;MAGA,IAAI,IAAI,CAAC9C,cAAc,CAACkD,MAAM,GAAG,IAAI,CAAC9C,WAAW,EAAE;QACjD,IAAI,CAACJ,cAAc,GAAG,IAAI,CAACA,cAAc,CAACwD,KAAK,CAAC,CAAC,IAAI,CAACpD,WAAW,GAAG,CAAC,CAAC;MACxE;IACF;EAAC;IAAAK,GAAA;IAAAC,KAAA,EAKD,SAAA+C,oBAAoBA,CAClBC,eAAuB,EACvBZ,YAAoB,EACpBpB,QAA8B,EACxB;MACN,IAAI,CAACU,SAAS,CAAC;QACbC,IAAI,EAAE,kBAAkB;QACxB3B,KAAK,EAAEoC,YAAY;QACnBR,SAAS,EAAEP,IAAI,CAACC,GAAG,CAAC,CAAC;QACrBO,QAAQ,EAAE,kBAAkB;QAC5Bb,QAAQ,EAAAc,MAAA,CAAAC,MAAA;UAAIiB,eAAe,EAAfA;QAAe,GAAKhC,QAAQ;MAC1C,CAAC,CAAC;MAGF,IAAIoB,YAAY,GAAG,GAAG,EAAE;QACtBjC,OAAO,CAAC6B,IAAI,CAAC,wBAAwBgB,eAAe,SAASZ,YAAY,IAAI,CAAC;MAChF;IACF;EAAC;IAAArC,GAAA;IAAAC,KAAA,EAKD,SAAAiD,eAAeA,CACbC,UAAkB,EAClBC,QAAgB,EAChBC,cAAsB,EACtBpC,QAA8B,EACxB;MACN,IAAI,CAACU,SAAS,CAAC;QACbC,IAAI,EAAE,YAAY;QAClB3B,KAAK,EAAEoD,cAAc;QACrBxB,SAAS,EAAEP,IAAI,CAACC,GAAG,CAAC,CAAC;QACrBO,QAAQ,EAAE,YAAY;QACtBb,QAAQ,EAAAc,MAAA,CAAAC,MAAA;UAAImB,UAAU,EAAVA,UAAU;UAAEC,QAAQ,EAARA;QAAQ,GAAKnC,QAAQ;MAC/C,CAAC,CAAC;IACJ;EAAC;IAAAjB,GAAA;IAAAC,KAAA,EAKD,SAAAqD,oBAAoBA,CAAA,EAAsB;MAAA,IAAAC,MAAA;MACxC,IAAMhC,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;MACtB,IAAMiC,aAAa,GAAG,IAAI,CAACpE,OAAO,CAACqE,MAAM,CAAC,UAAAC,CAAC;QAAA,OAAInC,GAAG,GAAGmC,CAAC,CAAC7B,SAAS,GAAG,MAAM;MAAA,EAAC;MAG1E,IAAMxC,aAAa,GAAGmE,aAAa,CAACC,MAAM,CAAC,UAAAC,CAAC;QAAA,OAAIA,CAAC,CAAC5B,QAAQ,KAAK,QAAQ;MAAA,EAAC;MACxE,IAAMvC,cAAc,GAAGiE,aAAa,CAACC,MAAM,CAAC,UAAAC,CAAC;QAAA,OAAIA,CAAC,CAAC5B,QAAQ,KAAK,SAAS;MAAA,EAAC;MAE1E,IAAM6B,iBAAiB,GAAGtE,aAAa,CAACoD,MAAM,GAAG,CAAC,GAC9CpD,aAAa,CAACuE,MAAM,CAAC,UAACC,GAAG,EAAEH,CAAC;QAAA,OAAKG,GAAG,GAAGH,CAAC,CAACzD,KAAK;MAAA,GAAE,CAAC,CAAC,GAAGZ,aAAa,CAACoD,MAAM,GACzE,CAAC;MAEL,IAAMqB,kBAAkB,GAAGvE,cAAc,CAACkD,MAAM,GAAG,CAAC,GAChDlD,cAAc,CAACqE,MAAM,CAAC,UAACC,GAAG,EAAEH,CAAC;QAAA,OAAKG,GAAG,GAAGH,CAAC,CAACzD,KAAK;MAAA,GAAE,CAAC,CAAC,GAAGV,cAAc,CAACkD,MAAM,GAC3E,CAAC;MAGL,IAAMsB,cAAc,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC5E,aAAa,CAAC6E,MAAM,CAAC,CAAC,CAAC,CAC3DT,MAAM,CAAC,UAAAU,CAAC;QAAA,OAAIA,CAAC,CAACnD,UAAU,GAAGuC,MAAI,CAAC3D,qBAAqB;MAAA,EAAC,CACtDwE,IAAI,CAAC,UAACC,CAAC,EAAEC,CAAC;QAAA,OAAKA,CAAC,CAACtD,UAAU,GAAGqD,CAAC,CAACrD,UAAU;MAAA,EAAC,CAC3C+B,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;MAGf,IAAMwB,mBAAmB,GAAG,IAAI,CAAChF,cAAc,CAC5CkE,MAAM,CAAC,UAAAe,CAAC;QAAA,OAAIA,CAAC,CAACnC,YAAY,GAAGkB,MAAI,CAAC1D,sBAAsB;MAAA,EAAC,CACzDuE,IAAI,CAAC,UAACC,CAAC,EAAEC,CAAC;QAAA,OAAKA,CAAC,CAACjC,YAAY,GAAGgC,CAAC,CAAChC,YAAY;MAAA,EAAC,CAC/CU,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;MAGf,IAAM0B,cAAc,GAAG,IAAI,CAAClF,cAAc,CAACkE,MAAM,CAAC,UAAAe,CAAC;QAAA,OAAIA,CAAC,CAAC5B,MAAM;MAAA,EAAC,CAACH,MAAM;MACvE,IAAMiC,aAAa,GAAG,IAAI,CAACnF,cAAc,CAACkD,MAAM;MAChD,IAAMkC,YAAY,GAAGD,aAAa,GAAG,CAAC,GAAGD,cAAc,GAAGC,aAAa,GAAG,CAAC;MAG3E,IAAME,YAAY,GAAG,IAAI,CAACpF,aAAa,CAAC,IAAI,CAACA,aAAa,CAACiD,MAAM,GAAG,CAAC,CAAC;MACtE,IAAMoC,WAAW,GAAGD,YAAY,GAAGA,YAAY,CAACE,cAAc,GAAG,CAAC;MAGlE,IAAMC,eAAe,GAAG,IAAI,CAACC,uBAAuB,CAAC;QACnDrB,iBAAiB,EAAjBA,iBAAiB;QACjBG,kBAAkB,EAAlBA,kBAAkB;QAClBC,cAAc,EAAdA,cAAc;QACdQ,mBAAmB,EAAnBA,mBAAmB;QACnBI,YAAY,EAAZA,YAAY;QACZE,WAAW,EAAXA;MACF,CAAC,CAAC;MAEF,OAAO;QACLI,OAAO,EAAE;UACPtB,iBAAiB,EAAjBA,iBAAiB;UACjBG,kBAAkB,EAAlBA,kBAAkB;UAClBe,WAAW,EAAXA,WAAW;UACXK,UAAU,EAAE,IAAI,CAACzF,cAAc;UAC/BkF,YAAY,EAAZA;QACF,CAAC;QACDZ,cAAc,EAAdA,cAAc;QACdQ,mBAAmB,EAAnBA,mBAAmB;QACnBY,WAAW,EAAE,IAAI,CAAC1E,iBAAiB,CAAC,CAAC;QACrCsE,eAAe,EAAfA;MACF,CAAC;IACH;EAAC;IAAA/E,GAAA;IAAAC,KAAA,EAKD,SAAAmF,oBAAoBA,CAACtD,QAAuC,EAAuB;MACjF,OAAO,IAAI,CAAC1C,OAAO,CAACqE,MAAM,CAAC,UAAAC,CAAC;QAAA,OAAIA,CAAC,CAAC5B,QAAQ,KAAKA,QAAQ;MAAA,EAAC;IAC1D;EAAC;IAAA9B,GAAA;IAAAC,KAAA,EAKD,SAAAoF,YAAYA,CAAA,EAAS;MACnB,IAAI,CAACjG,OAAO,GAAG,EAAE;MACjB,IAAI,CAACC,aAAa,CAACiG,KAAK,CAAC,CAAC;MAC1B,IAAI,CAAC/F,cAAc,GAAG,EAAE;MACxB,IAAI,CAACC,aAAa,GAAG,EAAE;MACvB,IAAI,CAACC,cAAc,GAAG,CAAC;IACzB;EAAC;IAAAO,GAAA;IAAAC,KAAA,EAKD,SAAQ0B,SAASA,CAACkB,MAAyB,EAAQ;MACjD,IAAI,CAACzD,OAAO,CAAC0D,IAAI,CAACD,MAAM,CAAC;MAGzB,IAAI,IAAI,CAACzD,OAAO,CAACqD,MAAM,GAAG,IAAI,CAAC9C,WAAW,EAAE;QAC1C,IAAI,CAACP,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC2D,KAAK,CAAC,CAAC,IAAI,CAACpD,WAAW,GAAG,CAAC,CAAC;MAC1D;IACF;EAAC;IAAAK,GAAA;IAAAC,KAAA,EAKD,SAAQO,oBAAoBA,CAAA,EAAS;MAGnC,IAAI,OAAO+E,WAAW,KAAK,WAAW,IAAKA,WAAW,CAASC,MAAM,EAAE;QACrE,IAAMA,MAAM,GAAID,WAAW,CAASC,MAAM;QAC1C,IAAI,CAAChG,aAAa,CAACsD,IAAI,CAAC;UACtBgC,cAAc,EAAEU,MAAM,CAACV,cAAc;UACrCW,eAAe,EAAED,MAAM,CAACC,eAAe;UACvCC,eAAe,EAAEF,MAAM,CAACE,eAAe;UACvC7D,SAAS,EAAEP,IAAI,CAACC,GAAG,CAAC;QACtB,CAAC,CAAC;QAGF,IAAI,IAAI,CAAC/B,aAAa,CAACiD,MAAM,GAAG,GAAG,EAAE;UACnC,IAAI,CAACjD,aAAa,GAAG,IAAI,CAACA,aAAa,CAACuD,KAAK,CAAC,CAAC,EAAE,CAAC;QACpD;MACF;IACF;EAAC;IAAA/C,GAAA;IAAAC,KAAA,EAKD,SAAQQ,iBAAiBA,CAAA,EAAa;MACpC,IAAMkF,KAAe,GAAG,EAAE;MAE1B,IAAI,IAAI,CAACnG,aAAa,CAACiD,MAAM,GAAG,EAAE,EAAE,OAAOkD,KAAK;MAGhD,IAAMC,MAAM,GAAG,IAAI,CAACpG,aAAa,CAACuD,KAAK,CAAC,CAAC,EAAE,CAAC;MAC5C,IAAM8C,MAAM,GAAGD,MAAM,CAACA,MAAM,CAACnD,MAAM,GAAG,CAAC,CAAC,CAACqC,cAAc,GAAGc,MAAM,CAAC,CAAC,CAAC,CAACd,cAAc;MAElF,IAAIe,MAAM,GAAG,IAAI,CAAC/F,qBAAqB,EAAE;QACvC6F,KAAK,CAAC7C,IAAI,CAAC,6BAA6BgD,IAAI,CAACC,KAAK,CAACF,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC,2BAA2B,CAAC;MACtG;MAGA,SAAAG,IAAA,IAA8B,IAAI,CAAC3G,aAAa,CAAC4G,OAAO,CAAC,CAAC,EAAE;QAAA,IAAAC,KAAA,OAAAC,eAAA,CAAAhH,OAAA,EAAA6G,IAAA;QAAA,IAAhDpE,IAAI,GAAAsE,KAAA;QAAA,IAAE9G,OAAO,GAAA8G,KAAA;QACvB,IAAI9G,OAAO,CAACgC,SAAS,GAAG,GAAG,EAAE;UAC3BuE,KAAK,CAAC7C,IAAI,CAAC,aAAalB,IAAI,QAAQxC,OAAO,CAACgC,SAAS,aAAa,CAAC;QACrE;MACF;MAEA,OAAOuE,KAAK;IACd;EAAC;IAAA3F,GAAA;IAAAC,KAAA,EAKD,SAAQU,oBAAoBA,CAAA,EAAS;MAGnC,IAAIyF,OAAO,EAAE;QACXhG,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MAC1D;IACF;EAAC;IAAAL,GAAA;IAAAC,KAAA,EAKD,SAAQS,iBAAiBA,CAAA,EAAS;MAChC,IAAM2F,MAAM,GAAG/E,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,MAAM;MAClC,IAAI,CAACnC,OAAO,GAAG,IAAI,CAACA,OAAO,CAACqE,MAAM,CAAC,UAAAC,CAAC;QAAA,OAAIA,CAAC,CAAC7B,SAAS,GAAGwE,MAAM;MAAA,EAAC;IAC/D;EAAC;IAAArG,GAAA;IAAAC,KAAA,EAKD,SAAQ+E,uBAAuBA,CAACsB,IAO/B,EAAY;MACX,IAAMvB,eAAyB,GAAG,EAAE;MAEpC,IAAIuB,IAAI,CAAC3C,iBAAiB,GAAG,IAAI,CAAC/D,qBAAqB,EAAE;QACvDmF,eAAe,CAACjC,IAAI,CAAC,kEAAkE,CAAC;MAC1F;MAEA,IAAIwD,IAAI,CAACvC,cAAc,CAACtB,MAAM,GAAG,CAAC,EAAE;QAClCsC,eAAe,CAACjC,IAAI,CAAC,6BAA6BwD,IAAI,CAACvC,cAAc,CAAChB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACwD,GAAG,CAAC,UAAApC,CAAC;UAAA,OAAIA,CAAC,CAACpD,aAAa;QAAA,EAAC,CAACyF,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;MAC3H;MAEA,IAAIF,IAAI,CAACxC,kBAAkB,GAAG,IAAI,EAAE;QAClCiB,eAAe,CAACjC,IAAI,CAAC,mEAAmE,CAAC;MAC3F;MAEA,IAAIwD,IAAI,CAAC3B,YAAY,GAAG,GAAG,EAAE;QAC3BI,eAAe,CAACjC,IAAI,CAAC,kEAAkE,CAAC;MAC1F;MAEA,IAAIwD,IAAI,CAACzB,WAAW,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,EAAE;QACxCE,eAAe,CAACjC,IAAI,CAAC,qDAAqD,CAAC;MAC7E;MAEA,IAAIwD,IAAI,CAAC/B,mBAAmB,CAAC9B,MAAM,GAAG,CAAC,EAAE;QACvCsC,eAAe,CAACjC,IAAI,CAAC,qEAAqE,CAAC;MAC7F;MAEA,OAAOiC,eAAe;IACxB;EAAC;AAAA;AAII,IAAM0B,kBAAkB,GAAAC,OAAA,CAAAD,kBAAA,GAAG,IAAIxH,yBAAyB,CAAC,CAAC;AAAC,IAAA0H,QAAA,GAAAD,OAAA,CAAAvH,OAAA,GACnDsH,kBAAkB", "ignoreList": []}