1a505c284249e88d12d9d9d9f25c5969
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var ReactNativeFeatureFlags = _interopRequireWildcard(require("../../src/private/featureflags/ReactNativeFeatureFlags"));
var _EventEmitter = _interopRequireDefault(require("../vendor/emitter/EventEmitter"));
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var BatchedBridge = require("../BatchedBridge/BatchedBridge").default;
var infoLog = require("../Utilities/infoLog").default;
var TaskQueue = require("./TaskQueue").default;
var invariant = require('invariant');
var _emitter = new _EventEmitter.default();
var DEBUG_DELAY = 0;
var DEBUG = false;
var InteractionManagerImpl = {
  Events: {
    interactionStart: 'interactionStart',
    interactionComplete: 'interactionComplete'
  },
  runAfterInteractions: function runAfterInteractions(task) {
    var tasks = [];
    var promise = new Promise(function (resolve) {
      _scheduleUpdate();
      if (task) {
        tasks.push(task);
      }
      tasks.push({
        run: resolve,
        name: 'resolve ' + (task && task.name || '?')
      });
      _taskQueue.enqueueTasks(tasks);
    });
    return {
      then: promise.then.bind(promise),
      cancel: function cancel() {
        _taskQueue.cancelTasks(tasks);
      }
    };
  },
  createInteractionHandle: function createInteractionHandle() {
    DEBUG && infoLog('InteractionManager: create interaction handle');
    _scheduleUpdate();
    var handle = ++_inc;
    _addInteractionSet.add(handle);
    return handle;
  },
  clearInteractionHandle: function clearInteractionHandle(handle) {
    DEBUG && infoLog('InteractionManager: clear interaction handle');
    invariant(!!handle, 'InteractionManager: Must provide a handle to clear.');
    _scheduleUpdate();
    _addInteractionSet.delete(handle);
    _deleteInteractionSet.add(handle);
  },
  addListener: _emitter.addListener.bind(_emitter),
  setDeadline: function setDeadline(deadline) {
    _deadline = deadline;
  }
};
var _interactionSet = new Set();
var _addInteractionSet = new Set();
var _deleteInteractionSet = new Set();
var _taskQueue = new TaskQueue({
  onMoreTasks: _scheduleUpdate
});
var _nextUpdateHandle = 0;
var _inc = 0;
var _deadline = -1;
function _scheduleUpdate() {
  if (!_nextUpdateHandle) {
    if (_deadline > 0) {
      _nextUpdateHandle = setTimeout(_processUpdate, 0 + DEBUG_DELAY);
    } else {
      _nextUpdateHandle = setImmediate(_processUpdate);
    }
  }
}
function _processUpdate() {
  _nextUpdateHandle = 0;
  var interactionCount = _interactionSet.size;
  _addInteractionSet.forEach(function (handle) {
    return _interactionSet.add(handle);
  });
  _deleteInteractionSet.forEach(function (handle) {
    return _interactionSet.delete(handle);
  });
  var nextInteractionCount = _interactionSet.size;
  if (interactionCount !== 0 && nextInteractionCount === 0) {
    _emitter.emit(InteractionManager.Events.interactionComplete);
  } else if (interactionCount === 0 && nextInteractionCount !== 0) {
    _emitter.emit(InteractionManager.Events.interactionStart);
  }
  if (nextInteractionCount === 0) {
    while (_taskQueue.hasTasksToProcess()) {
      _taskQueue.processNext();
      if (_deadline > 0 && BatchedBridge.getEventLoopRunningTime() >= _deadline) {
        _scheduleUpdate();
        break;
      }
    }
  }
  _addInteractionSet.clear();
  _deleteInteractionSet.clear();
}
var InteractionManager = ReactNativeFeatureFlags.disableInteractionManager() ? require("./InteractionManagerStub").default : InteractionManagerImpl;
var _default = exports.default = InteractionManager;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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