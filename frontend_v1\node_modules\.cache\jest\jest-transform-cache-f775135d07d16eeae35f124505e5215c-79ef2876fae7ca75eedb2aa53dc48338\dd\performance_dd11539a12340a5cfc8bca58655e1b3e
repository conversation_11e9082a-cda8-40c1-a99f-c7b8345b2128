c0e4b52866ecff24df60ff31d4d119ca
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.withPerformanceTracking = exports.performanceMonitor = exports.measurePerformance = exports.PerformanceTimer = exports.PerformanceMonitor = exports.MemoryMonitor = exports.CriticalCSSOptimizer = exports.CodeSplittingUtils = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _toConsumableArray2 = _interopRequireDefault(require("@babel/runtime/helpers/toConsumableArray"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var PerformanceMonitor = exports.PerformanceMonitor = function () {
  function PerformanceMonitor() {
    (0, _classCallCheck2.default)(this, PerformanceMonitor);
    this.metrics = [];
    this.observers = new Map();
    this.initializeObservers();
  }
  return (0, _createClass2.default)(PerformanceMonitor, [{
    key: "initializeObservers",
    value: function initializeObservers() {
      var _this = this;
      if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
        return;
      }
      try {
        var navObserver = new PerformanceObserver(function (list) {
          list.getEntries().forEach(function (entry) {
            if (entry.entryType === 'navigation') {
              _this.recordNavigationTiming(entry);
            }
          });
        });
        navObserver.observe({
          entryTypes: ['navigation']
        });
        this.observers.set('navigation', navObserver);
      } catch (error) {
        console.warn('Navigation timing observer not supported:', error);
      }
      try {
        var paintObserver = new PerformanceObserver(function (list) {
          list.getEntries().forEach(function (entry) {
            _this.recordMetric({
              name: entry.name,
              value: entry.startTime,
              timestamp: Date.now(),
              type: 'timing',
              tags: {
                type: 'paint'
              }
            });
          });
        });
        paintObserver.observe({
          entryTypes: ['paint']
        });
        this.observers.set('paint', paintObserver);
      } catch (error) {
        console.warn('Paint timing observer not supported:', error);
      }
      try {
        var lcpObserver = new PerformanceObserver(function (list) {
          var entries = list.getEntries();
          var lastEntry = entries[entries.length - 1];
          _this.recordMetric({
            name: 'largest-contentful-paint',
            value: lastEntry.startTime,
            timestamp: Date.now(),
            type: 'timing',
            tags: {
              type: 'lcp'
            }
          });
        });
        lcpObserver.observe({
          entryTypes: ['largest-contentful-paint']
        });
        this.observers.set('lcp', lcpObserver);
      } catch (error) {
        console.warn('LCP observer not supported:', error);
      }
      try {
        var fidObserver = new PerformanceObserver(function (list) {
          list.getEntries().forEach(function (entry) {
            _this.recordMetric({
              name: 'first-input-delay',
              value: entry.processingStart - entry.startTime,
              timestamp: Date.now(),
              type: 'timing',
              tags: {
                type: 'fid'
              }
            });
          });
        });
        fidObserver.observe({
          entryTypes: ['first-input']
        });
        this.observers.set('fid', fidObserver);
      } catch (error) {
        console.warn('FID observer not supported:', error);
      }
    }
  }, {
    key: "recordNavigationTiming",
    value: function recordNavigationTiming(entry) {
      var timing = {
        navigationStart: entry.navigationStart,
        domContentLoadedEventEnd: entry.domContentLoadedEventEnd,
        loadEventEnd: entry.loadEventEnd
      };
      var domContentLoaded = timing.domContentLoadedEventEnd - timing.navigationStart;
      var pageLoad = timing.loadEventEnd - timing.navigationStart;
      this.recordMetric({
        name: 'dom-content-loaded',
        value: domContentLoaded,
        timestamp: Date.now(),
        type: 'timing',
        tags: {
          type: 'navigation'
        }
      });
      this.recordMetric({
        name: 'page-load',
        value: pageLoad,
        timestamp: Date.now(),
        type: 'timing',
        tags: {
          type: 'navigation'
        }
      });
    }
  }, {
    key: "recordMetric",
    value: function recordMetric(metric) {
      this.metrics.push(metric);
      if (this.metrics.length > 1000) {
        this.metrics = this.metrics.slice(-1000);
      }
      if (__DEV__) {
        console.log(`[Performance] ${metric.name}: ${metric.value}ms`, metric.tags);
      }
    }
  }, {
    key: "getMetrics",
    value: function getMetrics(name) {
      if (name) {
        return this.metrics.filter(function (metric) {
          return metric.name === name;
        });
      }
      return (0, _toConsumableArray2.default)(this.metrics);
    }
  }, {
    key: "getSummary",
    value: function getSummary() {
      var summary = {};
      this.metrics.forEach(function (metric) {
        if (!summary[metric.name]) {
          summary[metric.name] = {
            avg: 0,
            min: Infinity,
            max: -Infinity,
            count: 0
          };
        }
        var stat = summary[metric.name];
        stat.count++;
        stat.min = Math.min(stat.min, metric.value);
        stat.max = Math.max(stat.max, metric.value);
        stat.avg = (stat.avg * (stat.count - 1) + metric.value) / stat.count;
      });
      return summary;
    }
  }, {
    key: "clearMetrics",
    value: function clearMetrics() {
      this.metrics = [];
    }
  }, {
    key: "dispose",
    value: function dispose() {
      this.observers.forEach(function (observer) {
        return observer.disconnect();
      });
      this.observers.clear();
      this.metrics = [];
    }
  }], [{
    key: "getInstance",
    value: function getInstance() {
      if (!PerformanceMonitor.instance) {
        PerformanceMonitor.instance = new PerformanceMonitor();
      }
      return PerformanceMonitor.instance;
    }
  }]);
}();
var PerformanceTimer = exports.PerformanceTimer = function () {
  function PerformanceTimer(name) {
    (0, _classCallCheck2.default)(this, PerformanceTimer);
    this.name = name;
    this.startTime = performance.now();
  }
  return (0, _createClass2.default)(PerformanceTimer, [{
    key: "end",
    value: function end(tags) {
      var duration = performance.now() - this.startTime;
      PerformanceMonitor.getInstance().recordMetric({
        name: this.name,
        value: duration,
        timestamp: Date.now(),
        type: 'timing',
        tags: tags
      });
      return duration;
    }
  }]);
}();
var CriticalCSSOptimizer = exports.CriticalCSSOptimizer = function () {
  function CriticalCSSOptimizer() {
    (0, _classCallCheck2.default)(this, CriticalCSSOptimizer);
  }
  return (0, _createClass2.default)(CriticalCSSOptimizer, null, [{
    key: "setCriticalCSS",
    value: function setCriticalCSS(css) {
      this.criticalCSS = css;
    }
  }, {
    key: "addNonCriticalCSS",
    value: function addNonCriticalCSS(cssUrl) {
      this.nonCriticalCSS.push(cssUrl);
      this.loadCSSAsync(cssUrl);
    }
  }, {
    key: "loadCSSAsync",
    value: function loadCSSAsync(cssUrl) {
      if (typeof document === 'undefined') return;
      var link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = cssUrl;
      link.media = 'print';
      link.onload = function () {
        link.media = 'all';
      };
      document.head.appendChild(link);
    }
  }, {
    key: "getCriticalCSS",
    value: function getCriticalCSS() {
      return this.criticalCSS;
    }
  }, {
    key: "preloadCriticalResources",
    value: function preloadCriticalResources(resources) {
      if (typeof document === 'undefined') return;
      resources.forEach(function (resource) {
        var link = document.createElement('link');
        link.rel = 'preload';
        link.href = resource.href;
        link.as = resource.as;
        if (resource.type) {
          link.type = resource.type;
        }
        document.head.appendChild(link);
      });
    }
  }]);
}();
CriticalCSSOptimizer.criticalCSS = '';
CriticalCSSOptimizer.nonCriticalCSS = [];
var CodeSplittingUtils = exports.CodeSplittingUtils = function () {
  function CodeSplittingUtils() {
    (0, _classCallCheck2.default)(this, CodeSplittingUtils);
  }
  return (0, _createClass2.default)(CodeSplittingUtils, null, [{
    key: "importModule",
    value: (function () {
      var _importModule = (0, _asyncToGenerator2.default)(function* (importFn, chunkName) {
        var timer = new PerformanceTimer(`chunk-load-${chunkName}`);
        try {
          var module = yield importFn();
          timer.end({
            chunk: chunkName,
            status: 'success'
          });
          this.loadedChunks.add(chunkName);
          return module;
        } catch (error) {
          timer.end({
            chunk: chunkName,
            status: 'error'
          });
          throw error;
        }
      });
      function importModule(_x, _x2) {
        return _importModule.apply(this, arguments);
      }
      return importModule;
    }())
  }, {
    key: "preloadChunk",
    value: function preloadChunk(importFn, chunkName) {
      var _this2 = this;
      if (this.loadedChunks.has(chunkName)) return;
      if ('requestIdleCallback' in window) {
        requestIdleCallback(function () {
          _this2.importModule(importFn, chunkName).catch(function () {});
        });
      } else {
        setTimeout(function () {
          _this2.importModule(importFn, chunkName).catch(function () {});
        }, 100);
      }
    }
  }, {
    key: "getLoadedChunks",
    value: function getLoadedChunks() {
      return Array.from(this.loadedChunks);
    }
  }]);
}();
CodeSplittingUtils.loadedChunks = new Set();
var MemoryMonitor = exports.MemoryMonitor = function () {
  function MemoryMonitor() {
    (0, _classCallCheck2.default)(this, MemoryMonitor);
  }
  return (0, _createClass2.default)(MemoryMonitor, null, [{
    key: "getMemoryUsage",
    value: function getMemoryUsage() {
      if (typeof window === 'undefined' || !('performance' in window) || !performance.memory) {
        return null;
      }
      var memory = performance.memory;
      return {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        percentage: memory.usedJSHeapSize / memory.totalJSHeapSize * 100
      };
    }
  }, {
    key: "startMemoryMonitoring",
    value: function startMemoryMonitoring() {
      var _this3 = this;
      var threshold = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 80;
      if (typeof window === 'undefined') return;
      var checkMemory = function checkMemory() {
        var usage = _this3.getMemoryUsage();
        if (usage && usage.percentage > threshold) {
          console.warn(`High memory usage: ${usage.percentage.toFixed(1)}%`);
          PerformanceMonitor.getInstance().recordMetric({
            name: 'memory-usage-high',
            value: usage.percentage,
            timestamp: Date.now(),
            type: 'gauge',
            tags: {
              threshold: threshold.toString()
            }
          });
        }
      };
      setInterval(checkMemory, 30000);
    }
  }]);
}();
var performanceMonitor = exports.performanceMonitor = PerformanceMonitor.getInstance();
var measurePerformance = exports.measurePerformance = function measurePerformance(name) {
  return new PerformanceTimer(name);
};
var withPerformanceTracking = exports.withPerformanceTracking = function withPerformanceTracking(fn, name) {
  return function () {
    var timer = new PerformanceTimer(name);
    try {
      var result = fn.apply(void 0, arguments);
      if (result && typeof result.then === 'function') {
        return result.finally(function () {
          return timer.end();
        });
      }
      timer.end();
      return result;
    } catch (error) {
      timer.end({
        status: 'error'
      });
      throw error;
    }
  };
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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