/**
 * Search Suggestions Component
 *
 * Component Contract:
 * - Displays intelligent search suggestions based on user input
 * - Shows recent searches, popular searches, and trending categories
 * - Provides quick access to frequently used searches
 * - Implements accessibility features for screen readers
 * - Supports keyboard navigation
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, TouchableOpacity, FlatList, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { useTheme } from '../../contexts/ThemeContext';
import { usePerformance } from '../../hooks/usePerformance';
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
} from '../../utils/responsiveUtils';

interface SearchSuggestion {
  id: string;
  text: string;
  type: 'recent' | 'popular' | 'trending' | 'category' | 'provider';
  count?: number;
  icon?: string;
}

interface SearchSuggestionsProps {
  query: string;
  onSuggestionPress: (suggestion: SearchSuggestion) => void;
  onClearHistory?: () => void;
  visible: boolean;
  recentSearches?: string[];
  popularSearches?: string[];
  trendingCategories?: string[];
}

export const SearchSuggestions: React.FC<SearchSuggestionsProps> = ({
  query,
  onSuggestionPress,
  onClearHistory,
  visible,
  recentSearches = [],
  popularSearches = [],
  trendingCategories = [],
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [loading, setLoading] = useState(false);

  const { trackInteraction } = usePerformance({
    componentName: 'SearchSuggestions',
    trackInteractions: true,
  });

  // Generate suggestions based on query and available data
  const generateSuggestions = useCallback(() => {
    const allSuggestions: SearchSuggestion[] = [];

    if (query.trim() === '') {
      // Show recent searches when no query
      recentSearches.slice(0, 5).forEach((search, index) => {
        allSuggestions.push({
          id: `recent_${index}`,
          text: search,
          type: 'recent',
          icon: 'time-outline',
        });
      });

      // Show popular searches
      popularSearches.slice(0, 3).forEach((search, index) => {
        allSuggestions.push({
          id: `popular_${index}`,
          text: search,
          type: 'popular',
          icon: 'trending-up-outline',
        });
      });

      // Show trending categories
      trendingCategories.slice(0, 4).forEach((category, index) => {
        allSuggestions.push({
          id: `trending_${index}`,
          text: category,
          type: 'trending',
          icon: 'flash-outline',
        });
      });
    } else {
      // Filter suggestions based on query
      const queryLower = query.toLowerCase();

      // Recent searches that match
      recentSearches
        .filter(search => search.toLowerCase().includes(queryLower))
        .slice(0, 3)
        .forEach((search, index) => {
          allSuggestions.push({
            id: `recent_match_${index}`,
            text: search,
            type: 'recent',
            icon: 'time-outline',
          });
        });

      // Popular searches that match
      popularSearches
        .filter(search => search.toLowerCase().includes(queryLower))
        .slice(0, 3)
        .forEach((search, index) => {
          allSuggestions.push({
            id: `popular_match_${index}`,
            text: search,
            type: 'popular',
            icon: 'trending-up-outline',
          });
        });

      // Categories that match
      trendingCategories
        .filter(category => category.toLowerCase().includes(queryLower))
        .slice(0, 3)
        .forEach((category, index) => {
          allSuggestions.push({
            id: `category_match_${index}`,
            text: category,
            type: 'category',
            icon: 'grid-outline',
          });
        });
    }

    setSuggestions(allSuggestions);
  }, [query, recentSearches, popularSearches, trendingCategories]);

  useEffect(() => {
    generateSuggestions();
  }, [generateSuggestions]);

  const handleSuggestionPress = useCallback((suggestion: SearchSuggestion) => {
    trackInteraction('suggestionPress', { type: suggestion.type, text: suggestion.text });
    onSuggestionPress(suggestion);
  }, [onSuggestionPress, trackInteraction]);

  const handleClearHistory = useCallback(() => {
    trackInteraction('clearHistory');
    onClearHistory?.();
  }, [onClearHistory, trackInteraction]);

  const renderSuggestion = ({ item }: { item: SearchSuggestion }) => (
    <TouchableOpacity
      style={styles.suggestionItem}
      onPress={() => handleSuggestionPress(item)}
      accessibilityRole="button"
      accessibilityLabel={`Search for ${item.text}`}
      accessibilityHint={`Tap to search for ${item.text}`}
    >
      <View style={styles.suggestionContent}>
        <Ionicons
          name={item.icon as any}
          size={20}
          color={colors.text.secondary}
          style={styles.suggestionIcon}
        />
        <Text style={styles.suggestionText}>{item.text}</Text>
        {item.type === 'popular' && (
          <View style={styles.popularBadge}>
            <Text style={styles.popularBadgeText}>Popular</Text>
          </View>
        )}
        {item.type === 'trending' && (
          <View style={styles.trendingBadge}>
            <Text style={styles.trendingBadgeText}>Trending</Text>
          </View>
        )}
      </View>
      <Ionicons
        name="arrow-up-outline"
        size={16}
        color={colors.text.tertiary}
        style={styles.insertIcon}
      />
    </TouchableOpacity>
  );

  if (!visible || suggestions.length === 0) {
    return null;
  }

  return (
    <View style={styles.container}>
      {recentSearches.length > 0 && query.trim() === '' && (
        <View style={styles.header}>
          <Text style={styles.headerText}>Recent Searches</Text>
          <TouchableOpacity
            onPress={handleClearHistory}
            style={styles.clearButton}
            accessibilityRole="button"
            accessibilityLabel="Clear search history"
          >
            <Text style={styles.clearButtonText}>Clear</Text>
          </TouchableOpacity>
        </View>
      )}

      <FlatList
        data={suggestions}
        renderItem={renderSuggestion}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        style={styles.suggestionsList}
      />
    </View>
  );
};

// Styles
const createStyles = (colors: any) => ({
  container: {
    backgroundColor: colors.background.primary,
    borderRadius: 12,
    marginTop: getResponsiveSpacing(8),
    shadowColor: colors.shadow.default,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    maxHeight: 300,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: getResponsiveSpacing(16),
    paddingVertical: getResponsiveSpacing(12),
    borderBottomWidth: 1,
    borderBottomColor: colors.border.light,
  },
  headerText: {
    fontSize: getResponsiveFontSize(14),
    fontWeight: '600',
    color: colors.text.primary,
  },
  clearButton: {
    paddingHorizontal: getResponsiveSpacing(8),
    paddingVertical: getResponsiveSpacing(4),
  },
  clearButtonText: {
    fontSize: getResponsiveFontSize(14),
    color: colors.sage600,
    fontWeight: '500',
  },
  suggestionsList: {
    maxHeight: 240,
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: getResponsiveSpacing(16),
    paddingVertical: getResponsiveSpacing(12),
    borderBottomWidth: 1,
    borderBottomColor: colors.border.light,
  },
  suggestionContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  suggestionIcon: {
    marginRight: getResponsiveSpacing(12),
  },
  suggestionText: {
    flex: 1,
    fontSize: getResponsiveFontSize(16),
    color: colors.text.primary,
  },
  popularBadge: {
    backgroundColor: colors.sage100,
    paddingHorizontal: getResponsiveSpacing(8),
    paddingVertical: getResponsiveSpacing(2),
    borderRadius: 12,
    marginLeft: getResponsiveSpacing(8),
  },
  popularBadgeText: {
    fontSize: getResponsiveFontSize(12),
    color: colors.sage700,
    fontWeight: '500',
  },
  trendingBadge: {
    backgroundColor: colors.accent100,
    paddingHorizontal: getResponsiveSpacing(8),
    paddingVertical: getResponsiveSpacing(2),
    borderRadius: 12,
    marginLeft: getResponsiveSpacing(8),
  },
  trendingBadgeText: {
    fontSize: getResponsiveFontSize(12),
    color: colors.accent700,
    fontWeight: '500',
  },
  insertIcon: {
    marginLeft: getResponsiveSpacing(8),
    transform: [{ rotate: '45deg' }],
  },
});

export default SearchSuggestions;
