41b99f7c0f3532cafc55060dc3ddaac5
var _accessibilityUtils = require("../accessibilityUtils");
describe('WCAG_STANDARDS Fix', function () {
  it('should have correct TOUCH_TARGETS structure', function () {
    expect(_accessibilityUtils.WCAG_STANDARDS.TOUCH_TARGETS).toBeDefined();
    expect(_accessibilityUtils.WCAG_STANDARDS.TOUCH_TARGETS.MINIMUM_SIZE).toBe(44);
    expect(_accessibilityUtils.WCAG_STANDARDS.TOUCH_TARGETS.RECOMMENDED_SIZE).toBe(48);
    expect(_accessibilityUtils.WCAG_STANDARDS.TOUCH_TARGETS.SPACING).toBe(8);
  });
  it('should have backward compatibility TARGET_SIZE alias', function () {
    expect(_accessibilityUtils.WCAG_STANDARDS.TARGET_SIZE).toBeDefined();
    expect(_accessibilityUtils.WCAG_STANDARDS.TARGET_SIZE.MINIMUM).toBe(44);
  });
  it('should have correct FOCUS_INDICATORS structure', function () {
    expect(_accessibilityUtils.WCAG_STANDARDS.FOCUS_INDICATORS).toBeDefined();
    expect(_accessibilityUtils.WCAG_STANDARDS.FOCUS_INDICATORS.MIN_WIDTH).toBe(2);
    expect(_accessibilityUtils.WCAG_STANDARDS.FOCUS_INDICATORS.RECOMMENDED_WIDTH).toBe(3);
    expect(_accessibilityUtils.WCAG_STANDARDS.FOCUS_INDICATORS.OFFSET).toBe(2);
  });
  it('should have correct CONTRAST_RATIOS structure', function () {
    expect(_accessibilityUtils.WCAG_STANDARDS.CONTRAST_RATIOS).toBeDefined();
    expect(_accessibilityUtils.WCAG_STANDARDS.CONTRAST_RATIOS.AA_NORMAL).toBe(4.5);
    expect(_accessibilityUtils.WCAG_STANDARDS.CONTRAST_RATIOS.AA_LARGE).toBe(3.0);
    expect(_accessibilityUtils.WCAG_STANDARDS.CONTRAST_RATIOS.AAA_NORMAL).toBe(7.0);
    expect(_accessibilityUtils.WCAG_STANDARDS.CONTRAST_RATIOS.AAA_LARGE).toBe(4.5);
  });
  it('should be frozen to prevent modification', function () {
    expect(Object.isFrozen(_accessibilityUtils.WCAG_STANDARDS)).toBe(true);
    expect(Object.isFrozen(_accessibilityUtils.WCAG_STANDARDS.TOUCH_TARGETS)).toBe(true);
    expect(Object.isFrozen(_accessibilityUtils.WCAG_STANDARDS.TARGET_SIZE)).toBe(true);
    expect(Object.isFrozen(_accessibilityUtils.WCAG_STANDARDS.FOCUS_INDICATORS)).toBe(true);
    expect(Object.isFrozen(_accessibilityUtils.WCAG_STANDARDS.CONTRAST_RATIOS)).toBe(true);
  });
  it('should not throw runtime errors when accessing properties', function () {
    expect(function () {
      var minSize = _accessibilityUtils.WCAG_STANDARDS.TOUCH_TARGETS.MINIMUM_SIZE;
      var aliasSize = _accessibilityUtils.WCAG_STANDARDS.TARGET_SIZE.MINIMUM;
      var focusWidth = _accessibilityUtils.WCAG_STANDARDS.FOCUS_INDICATORS.RECOMMENDED_WIDTH;
      var contrastRatio = _accessibilityUtils.WCAG_STANDARDS.CONTRAST_RATIOS.AA_NORMAL;
      expect(typeof minSize).toBe('number');
      expect(typeof aliasSize).toBe('number');
      expect(typeof focusWidth).toBe('number');
      expect(typeof contrastRatio).toBe('number');
    }).not.toThrow();
  });
  it('should maintain consistency between TOUCH_TARGETS.MINIMUM_SIZE and TARGET_SIZE.MINIMUM', function () {
    expect(_accessibilityUtils.WCAG_STANDARDS.TOUCH_TARGETS.MINIMUM_SIZE).toBe(_accessibilityUtils.WCAG_STANDARDS.TARGET_SIZE.MINIMUM);
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfYWNjZXNzaWJpbGl0eVV0aWxzIiwicmVxdWlyZSIsImRlc2NyaWJlIiwiaXQiLCJleHBlY3QiLCJXQ0FHX1NUQU5EQVJEUyIsIlRPVUNIX1RBUkdFVFMiLCJ0b0JlRGVmaW5lZCIsIk1JTklNVU1fU0laRSIsInRvQmUiLCJSRUNPTU1FTkRFRF9TSVpFIiwiU1BBQ0lORyIsIlRBUkdFVF9TSVpFIiwiTUlOSU1VTSIsIkZPQ1VTX0lORElDQVRPUlMiLCJNSU5fV0lEVEgiLCJSRUNPTU1FTkRFRF9XSURUSCIsIk9GRlNFVCIsIkNPTlRSQVNUX1JBVElPUyIsIkFBX05PUk1BTCIsIkFBX0xBUkdFIiwiQUFBX05PUk1BTCIsIkFBQV9MQVJHRSIsIk9iamVjdCIsImlzRnJvemVuIiwibWluU2l6ZSIsImFsaWFzU2l6ZSIsImZvY3VzV2lkdGgiLCJjb250cmFzdFJhdGlvIiwibm90IiwidG9UaHJvdyJdLCJzb3VyY2VzIjpbIndjYWctc3RhbmRhcmRzLWZpeC50ZXN0LnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogVGVzdCBmb3IgV0NBRyBTdGFuZGFyZHMgRml4XG4gKiBcbiAqIFZlcmlmaWVzIHRoYXQgdGhlIFdDQUdfU1RBTkRBUkRTIGNvbnN0YW50IHN0cnVjdHVyZSBpcyBjb3JyZWN0XG4gKiBhbmQgdGhhdCB0aGUgcHJvcGVydHkgYWNjZXNzIGlzc3VlcyBhcmUgcmVzb2x2ZWQuXG4gKi9cblxuaW1wb3J0IHsgV0NBR19TVEFOREFSRFMgfSBmcm9tICcuLi9hY2Nlc3NpYmlsaXR5VXRpbHMnO1xuXG5kZXNjcmliZSgnV0NBR19TVEFOREFSRFMgRml4JywgKCkgPT4ge1xuICBpdCgnc2hvdWxkIGhhdmUgY29ycmVjdCBUT1VDSF9UQVJHRVRTIHN0cnVjdHVyZScsICgpID0+IHtcbiAgICBleHBlY3QoV0NBR19TVEFOREFSRFMuVE9VQ0hfVEFSR0VUUykudG9CZURlZmluZWQoKTtcbiAgICBleHBlY3QoV0NBR19TVEFOREFSRFMuVE9VQ0hfVEFSR0VUUy5NSU5JTVVNX1NJWkUpLnRvQmUoNDQpO1xuICAgIGV4cGVjdChXQ0FHX1NUQU5EQVJEUy5UT1VDSF9UQVJHRVRTLlJFQ09NTUVOREVEX1NJWkUpLnRvQmUoNDgpO1xuICAgIGV4cGVjdChXQ0FHX1NUQU5EQVJEUy5UT1VDSF9UQVJHRVRTLlNQQUNJTkcpLnRvQmUoOCk7XG4gIH0pO1xuXG4gIGl0KCdzaG91bGQgaGF2ZSBiYWNrd2FyZCBjb21wYXRpYmlsaXR5IFRBUkdFVF9TSVpFIGFsaWFzJywgKCkgPT4ge1xuICAgIGV4cGVjdChXQ0FHX1NUQU5EQVJEUy5UQVJHRVRfU0laRSkudG9CZURlZmluZWQoKTtcbiAgICBleHBlY3QoV0NBR19TVEFOREFSRFMuVEFSR0VUX1NJWkUuTUlOSU1VTSkudG9CZSg0NCk7XG4gIH0pO1xuXG4gIGl0KCdzaG91bGQgaGF2ZSBjb3JyZWN0IEZPQ1VTX0lORElDQVRPUlMgc3RydWN0dXJlJywgKCkgPT4ge1xuICAgIGV4cGVjdChXQ0FHX1NUQU5EQVJEUy5GT0NVU19JTkRJQ0FUT1JTKS50b0JlRGVmaW5lZCgpO1xuICAgIGV4cGVjdChXQ0FHX1NUQU5EQVJEUy5GT0NVU19JTkRJQ0FUT1JTLk1JTl9XSURUSCkudG9CZSgyKTtcbiAgICBleHBlY3QoV0NBR19TVEFOREFSRFMuRk9DVVNfSU5ESUNBVE9SUy5SRUNPTU1FTkRFRF9XSURUSCkudG9CZSgzKTtcbiAgICBleHBlY3QoV0NBR19TVEFOREFSRFMuRk9DVVNfSU5ESUNBVE9SUy5PRkZTRVQpLnRvQmUoMik7XG4gIH0pO1xuXG4gIGl0KCdzaG91bGQgaGF2ZSBjb3JyZWN0IENPTlRSQVNUX1JBVElPUyBzdHJ1Y3R1cmUnLCAoKSA9PiB7XG4gICAgZXhwZWN0KFdDQUdfU1RBTkRBUkRTLkNPTlRSQVNUX1JBVElPUykudG9CZURlZmluZWQoKTtcbiAgICBleHBlY3QoV0NBR19TVEFOREFSRFMuQ09OVFJBU1RfUkFUSU9TLkFBX05PUk1BTCkudG9CZSg0LjUpO1xuICAgIGV4cGVjdChXQ0FHX1NUQU5EQVJEUy5DT05UUkFTVF9SQVRJT1MuQUFfTEFSR0UpLnRvQmUoMy4wKTtcbiAgICBleHBlY3QoV0NBR19TVEFOREFSRFMuQ09OVFJBU1RfUkFUSU9TLkFBQV9OT1JNQUwpLnRvQmUoNy4wKTtcbiAgICBleHBlY3QoV0NBR19TVEFOREFSRFMuQ09OVFJBU1RfUkFUSU9TLkFBQV9MQVJHRSkudG9CZSg0LjUpO1xuICB9KTtcblxuICBpdCgnc2hvdWxkIGJlIGZyb3plbiB0byBwcmV2ZW50IG1vZGlmaWNhdGlvbicsICgpID0+IHtcbiAgICBleHBlY3QoT2JqZWN0LmlzRnJvemVuKFdDQUdfU1RBTkRBUkRTKSkudG9CZSh0cnVlKTtcbiAgICBleHBlY3QoT2JqZWN0LmlzRnJvemVuKFdDQUdfU1RBTkRBUkRTLlRPVUNIX1RBUkdFVFMpKS50b0JlKHRydWUpO1xuICAgIGV4cGVjdChPYmplY3QuaXNGcm96ZW4oV0NBR19TVEFOREFSRFMuVEFSR0VUX1NJWkUpKS50b0JlKHRydWUpO1xuICAgIGV4cGVjdChPYmplY3QuaXNGcm96ZW4oV0NBR19TVEFOREFSRFMuRk9DVVNfSU5ESUNBVE9SUykpLnRvQmUodHJ1ZSk7XG4gICAgZXhwZWN0KE9iamVjdC5pc0Zyb3plbihXQ0FHX1NUQU5EQVJEUy5DT05UUkFTVF9SQVRJT1MpKS50b0JlKHRydWUpO1xuICB9KTtcblxuICBpdCgnc2hvdWxkIG5vdCB0aHJvdyBydW50aW1lIGVycm9ycyB3aGVuIGFjY2Vzc2luZyBwcm9wZXJ0aWVzJywgKCkgPT4ge1xuICAgIGV4cGVjdCgoKSA9PiB7XG4gICAgICBjb25zdCBtaW5TaXplID0gV0NBR19TVEFOREFSRFMuVE9VQ0hfVEFSR0VUUy5NSU5JTVVNX1NJWkU7XG4gICAgICBjb25zdCBhbGlhc1NpemUgPSBXQ0FHX1NUQU5EQVJEUy5UQVJHRVRfU0laRS5NSU5JTVVNO1xuICAgICAgY29uc3QgZm9jdXNXaWR0aCA9IFdDQUdfU1RBTkRBUkRTLkZPQ1VTX0lORElDQVRPUlMuUkVDT01NRU5ERURfV0lEVEg7XG4gICAgICBjb25zdCBjb250cmFzdFJhdGlvID0gV0NBR19TVEFOREFSRFMuQ09OVFJBU1RfUkFUSU9TLkFBX05PUk1BTDtcbiAgICAgIFxuICAgICAgLy8gVGhlc2Ugc2hvdWxkIGFsbCBiZSBudW1iZXJzXG4gICAgICBleHBlY3QodHlwZW9mIG1pblNpemUpLnRvQmUoJ251bWJlcicpO1xuICAgICAgZXhwZWN0KHR5cGVvZiBhbGlhc1NpemUpLnRvQmUoJ251bWJlcicpO1xuICAgICAgZXhwZWN0KHR5cGVvZiBmb2N1c1dpZHRoKS50b0JlKCdudW1iZXInKTtcbiAgICAgIGV4cGVjdCh0eXBlb2YgY29udHJhc3RSYXRpbykudG9CZSgnbnVtYmVyJyk7XG4gICAgfSkubm90LnRvVGhyb3coKTtcbiAgfSk7XG5cbiAgaXQoJ3Nob3VsZCBtYWludGFpbiBjb25zaXN0ZW5jeSBiZXR3ZWVuIFRPVUNIX1RBUkdFVFMuTUlOSU1VTV9TSVpFIGFuZCBUQVJHRVRfU0laRS5NSU5JTVVNJywgKCkgPT4ge1xuICAgIGV4cGVjdChXQ0FHX1NUQU5EQVJEUy5UT1VDSF9UQVJHRVRTLk1JTklNVU1fU0laRSkudG9CZShXQ0FHX1NUQU5EQVJEUy5UQVJHRVRfU0laRS5NSU5JTVVNKTtcbiAgfSk7XG59KTtcbiJdLCJtYXBwaW5ncyI6IkFBT0EsSUFBQUEsbUJBQUEsR0FBQUMsT0FBQTtBQUVBQyxRQUFRLENBQUMsb0JBQW9CLEVBQUUsWUFBTTtFQUNuQ0MsRUFBRSxDQUFDLDZDQUE2QyxFQUFFLFlBQU07SUFDdERDLE1BQU0sQ0FBQ0Msa0NBQWMsQ0FBQ0MsYUFBYSxDQUFDLENBQUNDLFdBQVcsQ0FBQyxDQUFDO0lBQ2xESCxNQUFNLENBQUNDLGtDQUFjLENBQUNDLGFBQWEsQ0FBQ0UsWUFBWSxDQUFDLENBQUNDLElBQUksQ0FBQyxFQUFFLENBQUM7SUFDMURMLE1BQU0sQ0FBQ0Msa0NBQWMsQ0FBQ0MsYUFBYSxDQUFDSSxnQkFBZ0IsQ0FBQyxDQUFDRCxJQUFJLENBQUMsRUFBRSxDQUFDO0lBQzlETCxNQUFNLENBQUNDLGtDQUFjLENBQUNDLGFBQWEsQ0FBQ0ssT0FBTyxDQUFDLENBQUNGLElBQUksQ0FBQyxDQUFDLENBQUM7RUFDdEQsQ0FBQyxDQUFDO0VBRUZOLEVBQUUsQ0FBQyxzREFBc0QsRUFBRSxZQUFNO0lBQy9EQyxNQUFNLENBQUNDLGtDQUFjLENBQUNPLFdBQVcsQ0FBQyxDQUFDTCxXQUFXLENBQUMsQ0FBQztJQUNoREgsTUFBTSxDQUFDQyxrQ0FBYyxDQUFDTyxXQUFXLENBQUNDLE9BQU8sQ0FBQyxDQUFDSixJQUFJLENBQUMsRUFBRSxDQUFDO0VBQ3JELENBQUMsQ0FBQztFQUVGTixFQUFFLENBQUMsZ0RBQWdELEVBQUUsWUFBTTtJQUN6REMsTUFBTSxDQUFDQyxrQ0FBYyxDQUFDUyxnQkFBZ0IsQ0FBQyxDQUFDUCxXQUFXLENBQUMsQ0FBQztJQUNyREgsTUFBTSxDQUFDQyxrQ0FBYyxDQUFDUyxnQkFBZ0IsQ0FBQ0MsU0FBUyxDQUFDLENBQUNOLElBQUksQ0FBQyxDQUFDLENBQUM7SUFDekRMLE1BQU0sQ0FBQ0Msa0NBQWMsQ0FBQ1MsZ0JBQWdCLENBQUNFLGlCQUFpQixDQUFDLENBQUNQLElBQUksQ0FBQyxDQUFDLENBQUM7SUFDakVMLE1BQU0sQ0FBQ0Msa0NBQWMsQ0FBQ1MsZ0JBQWdCLENBQUNHLE1BQU0sQ0FBQyxDQUFDUixJQUFJLENBQUMsQ0FBQyxDQUFDO0VBQ3hELENBQUMsQ0FBQztFQUVGTixFQUFFLENBQUMsK0NBQStDLEVBQUUsWUFBTTtJQUN4REMsTUFBTSxDQUFDQyxrQ0FBYyxDQUFDYSxlQUFlLENBQUMsQ0FBQ1gsV0FBVyxDQUFDLENBQUM7SUFDcERILE1BQU0sQ0FBQ0Msa0NBQWMsQ0FBQ2EsZUFBZSxDQUFDQyxTQUFTLENBQUMsQ0FBQ1YsSUFBSSxDQUFDLEdBQUcsQ0FBQztJQUMxREwsTUFBTSxDQUFDQyxrQ0FBYyxDQUFDYSxlQUFlLENBQUNFLFFBQVEsQ0FBQyxDQUFDWCxJQUFJLENBQUMsR0FBRyxDQUFDO0lBQ3pETCxNQUFNLENBQUNDLGtDQUFjLENBQUNhLGVBQWUsQ0FBQ0csVUFBVSxDQUFDLENBQUNaLElBQUksQ0FBQyxHQUFHLENBQUM7SUFDM0RMLE1BQU0sQ0FBQ0Msa0NBQWMsQ0FBQ2EsZUFBZSxDQUFDSSxTQUFTLENBQUMsQ0FBQ2IsSUFBSSxDQUFDLEdBQUcsQ0FBQztFQUM1RCxDQUFDLENBQUM7RUFFRk4sRUFBRSxDQUFDLDBDQUEwQyxFQUFFLFlBQU07SUFDbkRDLE1BQU0sQ0FBQ21CLE1BQU0sQ0FBQ0MsUUFBUSxDQUFDbkIsa0NBQWMsQ0FBQyxDQUFDLENBQUNJLElBQUksQ0FBQyxJQUFJLENBQUM7SUFDbERMLE1BQU0sQ0FBQ21CLE1BQU0sQ0FBQ0MsUUFBUSxDQUFDbkIsa0NBQWMsQ0FBQ0MsYUFBYSxDQUFDLENBQUMsQ0FBQ0csSUFBSSxDQUFDLElBQUksQ0FBQztJQUNoRUwsTUFBTSxDQUFDbUIsTUFBTSxDQUFDQyxRQUFRLENBQUNuQixrQ0FBYyxDQUFDTyxXQUFXLENBQUMsQ0FBQyxDQUFDSCxJQUFJLENBQUMsSUFBSSxDQUFDO0lBQzlETCxNQUFNLENBQUNtQixNQUFNLENBQUNDLFFBQVEsQ0FBQ25CLGtDQUFjLENBQUNTLGdCQUFnQixDQUFDLENBQUMsQ0FBQ0wsSUFBSSxDQUFDLElBQUksQ0FBQztJQUNuRUwsTUFBTSxDQUFDbUIsTUFBTSxDQUFDQyxRQUFRLENBQUNuQixrQ0FBYyxDQUFDYSxlQUFlLENBQUMsQ0FBQyxDQUFDVCxJQUFJLENBQUMsSUFBSSxDQUFDO0VBQ3BFLENBQUMsQ0FBQztFQUVGTixFQUFFLENBQUMsMkRBQTJELEVBQUUsWUFBTTtJQUNwRUMsTUFBTSxDQUFDLFlBQU07TUFDWCxJQUFNcUIsT0FBTyxHQUFHcEIsa0NBQWMsQ0FBQ0MsYUFBYSxDQUFDRSxZQUFZO01BQ3pELElBQU1rQixTQUFTLEdBQUdyQixrQ0FBYyxDQUFDTyxXQUFXLENBQUNDLE9BQU87TUFDcEQsSUFBTWMsVUFBVSxHQUFHdEIsa0NBQWMsQ0FBQ1MsZ0JBQWdCLENBQUNFLGlCQUFpQjtNQUNwRSxJQUFNWSxhQUFhLEdBQUd2QixrQ0FBYyxDQUFDYSxlQUFlLENBQUNDLFNBQVM7TUFHOURmLE1BQU0sQ0FBQyxPQUFPcUIsT0FBTyxDQUFDLENBQUNoQixJQUFJLENBQUMsUUFBUSxDQUFDO01BQ3JDTCxNQUFNLENBQUMsT0FBT3NCLFNBQVMsQ0FBQyxDQUFDakIsSUFBSSxDQUFDLFFBQVEsQ0FBQztNQUN2Q0wsTUFBTSxDQUFDLE9BQU91QixVQUFVLENBQUMsQ0FBQ2xCLElBQUksQ0FBQyxRQUFRLENBQUM7TUFDeENMLE1BQU0sQ0FBQyxPQUFPd0IsYUFBYSxDQUFDLENBQUNuQixJQUFJLENBQUMsUUFBUSxDQUFDO0lBQzdDLENBQUMsQ0FBQyxDQUFDb0IsR0FBRyxDQUFDQyxPQUFPLENBQUMsQ0FBQztFQUNsQixDQUFDLENBQUM7RUFFRjNCLEVBQUUsQ0FBQyx3RkFBd0YsRUFBRSxZQUFNO0lBQ2pHQyxNQUFNLENBQUNDLGtDQUFjLENBQUNDLGFBQWEsQ0FBQ0UsWUFBWSxDQUFDLENBQUNDLElBQUksQ0FBQ0osa0NBQWMsQ0FBQ08sV0FBVyxDQUFDQyxPQUFPLENBQUM7RUFDNUYsQ0FBQyxDQUFDO0FBQ0osQ0FBQyxDQUFDIiwiaWdub3JlTGlzdCI6W119