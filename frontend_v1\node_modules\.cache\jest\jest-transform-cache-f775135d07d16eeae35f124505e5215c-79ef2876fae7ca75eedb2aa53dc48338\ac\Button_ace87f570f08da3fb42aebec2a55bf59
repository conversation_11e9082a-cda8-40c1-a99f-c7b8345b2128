b8159740355c3d538c38eedad4906cc2
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _StyleSheet = _interopRequireDefault(require("../StyleSheet/StyleSheet"));
var _Text = _interopRequireDefault(require("../Text/Text"));
var _Platform = _interopRequireDefault(require("../Utilities/Platform"));
var _TouchableNativeFeedback = _interopRequireDefault(require("./Touchable/TouchableNativeFeedback"));
var _TouchableOpacity = _interopRequireDefault(require("./Touchable/TouchableOpacity"));
var _View = _interopRequireDefault(require("./View/View"));
var _invariant = _interopRequireDefault(require("invariant"));
var React = _interopRequireWildcard(require("react"));
var _jsxRuntime = require("react/jsx-runtime");
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var Touchable = _Platform.default.OS === 'android' ? _TouchableNativeFeedback.default : _TouchableOpacity.default;
var Button = React.forwardRef(function (props, ref) {
  var _accessibilityState2, _accessibilityState3;
  var accessibilityLabel = props.accessibilityLabel,
    accessibilityState = props.accessibilityState,
    ariaBusy = props['aria-busy'],
    ariaChecked = props['aria-checked'],
    ariaDisabled = props['aria-disabled'],
    ariaExpanded = props['aria-expanded'],
    ariaLabel = props['aria-label'],
    ariaSelected = props['aria-selected'],
    importantForAccessibility = props.importantForAccessibility,
    color = props.color,
    onPress = props.onPress,
    touchSoundDisabled = props.touchSoundDisabled,
    title = props.title,
    hasTVPreferredFocus = props.hasTVPreferredFocus,
    nextFocusDown = props.nextFocusDown,
    nextFocusForward = props.nextFocusForward,
    nextFocusLeft = props.nextFocusLeft,
    nextFocusRight = props.nextFocusRight,
    nextFocusUp = props.nextFocusUp,
    testID = props.testID,
    accessible = props.accessible,
    accessibilityActions = props.accessibilityActions,
    accessibilityHint = props.accessibilityHint,
    accessibilityLanguage = props.accessibilityLanguage,
    onAccessibilityAction = props.onAccessibilityAction;
  var buttonStyles = [styles.button];
  var textStyles = [styles.text];
  if (color) {
    if (_Platform.default.OS === 'ios') {
      textStyles.push({
        color: color
      });
    } else {
      buttonStyles.push({
        backgroundColor: color
      });
    }
  }
  var _accessibilityState = {
    busy: ariaBusy != null ? ariaBusy : accessibilityState == null ? void 0 : accessibilityState.busy,
    checked: ariaChecked != null ? ariaChecked : accessibilityState == null ? void 0 : accessibilityState.checked,
    disabled: ariaDisabled != null ? ariaDisabled : accessibilityState == null ? void 0 : accessibilityState.disabled,
    expanded: ariaExpanded != null ? ariaExpanded : accessibilityState == null ? void 0 : accessibilityState.expanded,
    selected: ariaSelected != null ? ariaSelected : accessibilityState == null ? void 0 : accessibilityState.selected
  };
  var disabled = props.disabled != null ? props.disabled : (_accessibilityState2 = _accessibilityState) == null ? void 0 : _accessibilityState2.disabled;
  _accessibilityState = disabled !== ((_accessibilityState3 = _accessibilityState) == null ? void 0 : _accessibilityState3.disabled) ? Object.assign({}, _accessibilityState, {
    disabled: disabled
  }) : _accessibilityState;
  if (disabled) {
    buttonStyles.push(styles.buttonDisabled);
    textStyles.push(styles.textDisabled);
  }
  (0, _invariant.default)(typeof title === 'string', 'The title prop of a Button must be a string');
  var formattedTitle = _Platform.default.OS === 'android' ? title.toUpperCase() : title;
  var _importantForAccessibility = importantForAccessibility === 'no' ? 'no-hide-descendants' : importantForAccessibility;
  return (0, _jsxRuntime.jsx)(Touchable, {
    accessible: accessible,
    accessibilityActions: accessibilityActions,
    onAccessibilityAction: onAccessibilityAction,
    accessibilityLabel: ariaLabel || accessibilityLabel,
    accessibilityHint: accessibilityHint,
    accessibilityLanguage: accessibilityLanguage,
    accessibilityRole: "button",
    accessibilityState: _accessibilityState,
    importantForAccessibility: _importantForAccessibility,
    hasTVPreferredFocus: hasTVPreferredFocus,
    nextFocusDown: nextFocusDown,
    nextFocusForward: nextFocusForward,
    nextFocusLeft: nextFocusLeft,
    nextFocusRight: nextFocusRight,
    nextFocusUp: nextFocusUp,
    testID: testID,
    disabled: disabled,
    onPress: onPress,
    touchSoundDisabled: touchSoundDisabled,
    ref: ref,
    children: (0, _jsxRuntime.jsx)(_View.default, {
      style: buttonStyles,
      children: (0, _jsxRuntime.jsx)(_Text.default, {
        style: textStyles,
        disabled: disabled,
        children: formattedTitle
      })
    })
  });
});
Button.displayName = 'Button';
var styles = _StyleSheet.default.create({
  button: _Platform.default.select({
    ios: {},
    android: {
      elevation: 4,
      backgroundColor: '#2196F3',
      borderRadius: 2
    }
  }),
  text: Object.assign({
    textAlign: 'center',
    margin: 8
  }, _Platform.default.select({
    ios: {
      color: '#007AFF',
      fontSize: 18
    },
    android: {
      color: 'white',
      fontWeight: '500'
    }
  })),
  buttonDisabled: _Platform.default.select({
    ios: {},
    android: {
      elevation: 0,
      backgroundColor: '#dfdfdf'
    }
  }),
  textDisabled: _Platform.default.select({
    ios: {
      color: '#cdcdcd'
    },
    android: {
      color: '#a1a1a1'
    }
  })
});
var _default = exports.default = Button;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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