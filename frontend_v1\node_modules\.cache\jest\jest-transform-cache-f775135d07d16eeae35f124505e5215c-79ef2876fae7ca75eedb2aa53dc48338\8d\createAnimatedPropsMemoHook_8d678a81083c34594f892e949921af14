d2c3719bdb167f4b5ac2b51680fffa7c
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.areCompositeKeysEqual = areCompositeKeysEqual;
exports.createAnimatedPropsMemoHook = createAnimatedPropsMemoHook;
exports.createCompositeKeyForProps = createCompositeKeyForProps;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _AnimatedEvent = require("../../../Libraries/Animated/AnimatedEvent");
var _AnimatedNode = _interopRequireDefault(require("../../../Libraries/Animated/nodes/AnimatedNode"));
var _AnimatedObject = require("../../../Libraries/Animated/nodes/AnimatedObject");
var _flattenStyle = _interopRequireDefault(require("../../../Libraries/StyleSheet/flattenStyle"));
var ReactNativeFeatureFlags = _interopRequireWildcard(require("../featureflags/ReactNativeFeatureFlags"));
var _nullthrows = _interopRequireDefault(require("nullthrows"));
var _react = require("react");
var _Object$hasOwn;
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
function createAnimatedPropsMemoHook(allowlist) {
  return function useAnimatedPropsMemo(create, props) {
    var useAnimatedPropsImpl = ReactNativeFeatureFlags.avoidStateUpdateInAnimatedPropsMemo() ? useAnimatedPropsMemo_ref : useAnimatedPropsMemo_state;
    return useAnimatedPropsImpl(create, props);
  };
  function useAnimatedPropsMemo_ref(create, props) {
    var compositeKey = (0, _react.useMemo)(function () {
      return createCompositeKeyForProps(props, allowlist);
    }, [props]);
    var prevRef = (0, _react.useRef)();
    var prev = prevRef.current;
    var next = prev != null && areCompositeKeysEqual(prev.compositeKey, compositeKey, allowlist) ? prev : {
      compositeKey: compositeKey,
      node: create()
    };
    (0, _react.useInsertionEffect)(function () {
      prevRef.current = next;
    }, [next]);
    return next.node;
  }
  function useAnimatedPropsMemo_state(create, props) {
    var compositeKey = (0, _react.useMemo)(function () {
      return createCompositeKeyForProps(props, allowlist);
    }, [props]);
    var _useState = (0, _react.useState)(function () {
        return {
          allowlist: allowlist,
          compositeKey: compositeKey,
          value: create()
        };
      }),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      state = _useState2[0],
      setState = _useState2[1];
    if (state.allowlist !== allowlist || !areCompositeKeysEqual(state.compositeKey, compositeKey)) {
      setState({
        allowlist: allowlist,
        compositeKey: compositeKey,
        value: create()
      });
    }
    return state.value;
  }
}
function createCompositeKeyForProps(props, allowlist) {
  var compositeKey = null;
  var keys = Object.keys(props);
  for (var ii = 0, length = keys.length; ii < length; ii++) {
    var key = keys[ii];
    var value = props[key];
    if (allowlist == null || hasOwn(allowlist, key)) {
      var compositeKeyComponent = void 0;
      if (key === 'style') {
        var flatStyle = (0, _flattenStyle.default)(value);
        if (flatStyle != null) {
          compositeKeyComponent = createCompositeKeyForObject(flatStyle, allowlist == null ? void 0 : allowlist.style);
        }
      } else if (value instanceof _AnimatedNode.default || value instanceof _AnimatedEvent.AnimatedEvent) {
        compositeKeyComponent = value;
      } else if (Array.isArray(value)) {
        compositeKeyComponent = allowlist == null ? value : createCompositeKeyForArray(value);
      } else if ((0, _AnimatedObject.isPlainObject)(value)) {
        compositeKeyComponent = allowlist == null ? value : createCompositeKeyForObject(value);
      }
      if (compositeKeyComponent != null) {
        if (compositeKey == null) {
          compositeKey = {};
        }
        compositeKey[key] = compositeKeyComponent;
      }
    }
  }
  return compositeKey;
}
function createCompositeKeyForArray(array) {
  var compositeKey = null;
  for (var ii = 0, length = array.length; ii < length; ii++) {
    var value = array[ii];
    var compositeKeyComponent = void 0;
    if (value instanceof _AnimatedNode.default) {
      compositeKeyComponent = value;
    } else if (Array.isArray(value)) {
      compositeKeyComponent = createCompositeKeyForArray(value);
    } else if ((0, _AnimatedObject.isPlainObject)(value)) {
      compositeKeyComponent = createCompositeKeyForObject(value);
    }
    if (compositeKeyComponent != null) {
      if (compositeKey == null) {
        compositeKey = new Array(array.length).fill(null);
      }
      compositeKey[ii] = compositeKeyComponent;
    }
  }
  return compositeKey;
}
function createCompositeKeyForObject(object, allowlist) {
  var compositeKey = null;
  var keys = Object.keys(object);
  for (var ii = 0, length = keys.length; ii < length; ii++) {
    var key = keys[ii];
    if (allowlist == null || hasOwn(allowlist, key)) {
      var value = object[key];
      var compositeKeyComponent = void 0;
      if (value instanceof _AnimatedNode.default) {
        compositeKeyComponent = value;
      } else if (Array.isArray(value)) {
        compositeKeyComponent = createCompositeKeyForArray(value);
      } else if ((0, _AnimatedObject.isPlainObject)(value)) {
        compositeKeyComponent = createCompositeKeyForObject(value);
      }
      if (compositeKeyComponent != null) {
        if (compositeKey == null) {
          compositeKey = {};
        }
        compositeKey[key] = compositeKeyComponent;
      }
    }
  }
  return compositeKey;
}
function areCompositeKeysEqual(maybePrev, maybeNext, allowlist) {
  if (maybePrev === maybeNext) {
    return true;
  }
  if (maybePrev === null || maybeNext === null) {
    return false;
  }
  var prev = maybePrev;
  var next = maybeNext;
  var keys = Object.keys(prev);
  var length = keys.length;
  if (length !== Object.keys(next).length) {
    return false;
  }
  for (var ii = 0; ii < length; ii++) {
    var key = keys[ii];
    if (!hasOwn(next, key)) {
      return false;
    }
    var prevComponent = prev[key];
    var nextComponent = next[key];
    if (key === 'style') {
      if (!areCompositeKeyComponentsEqual(prevComponent, nextComponent)) {
        return false;
      }
    } else if (prevComponent instanceof _AnimatedNode.default || prevComponent instanceof _AnimatedEvent.AnimatedEvent) {
      if (prevComponent !== nextComponent) {
        return false;
      }
    } else {
      if (allowlist == null) {
        if (prevComponent !== nextComponent) {
          return false;
        }
      } else {
        if (!areCompositeKeyComponentsEqual(prevComponent, nextComponent)) {
          return false;
        }
      }
    }
  }
  return true;
}
function areCompositeKeyComponentsEqual(prev, next) {
  if (prev === next) {
    return true;
  }
  if (prev instanceof _AnimatedNode.default) {
    return prev === next;
  }
  if (Array.isArray(prev)) {
    if (!Array.isArray(next)) {
      return false;
    }
    var length = prev.length;
    if (length !== next.length) {
      return false;
    }
    for (var ii = 0; ii < length; ii++) {
      if (!areCompositeKeyComponentsEqual(prev[ii], next[ii])) {
        return false;
      }
    }
    return true;
  }
  if ((0, _AnimatedObject.isPlainObject)(prev)) {
    if (!(0, _AnimatedObject.isPlainObject)(next)) {
      return false;
    }
    var keys = Object.keys(prev);
    var _length = keys.length;
    if (_length !== Object.keys(next).length) {
      return false;
    }
    for (var _ii = 0; _ii < _length; _ii++) {
      var key = keys[_ii];
      if (!hasOwn((0, _nullthrows.default)(next), key) || !areCompositeKeyComponentsEqual(prev[key], next[key])) {
        return false;
      }
    }
    return true;
  }
  return false;
}
var _hasOwnProp = Object.prototype.hasOwnProperty;
var hasOwn = (_Object$hasOwn = Object.hasOwn) != null ? _Object$hasOwn : function (obj, prop) {
  return _hasOwnProp.call(obj, prop);
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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