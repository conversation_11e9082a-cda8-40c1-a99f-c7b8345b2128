# Comprehensive Testing Guide

## Overview

This guide covers all aspects of testing in the Vierla Frontend V2 application, including unit tests, integration tests, accessibility tests, and performance tests.

## Table of Contents

1. [Testing Philosophy](#testing-philosophy)
2. [Test Setup](#test-setup)
3. [Unit Testing](#unit-testing)
4. [Integration Testing](#integration-testing)
5. [Accessibility Testing](#accessibility-testing)
6. [Performance Testing](#performance-testing)
7. [Error Handling Testing](#error-handling-testing)
8. [Test Coverage](#test-coverage)
9. [Best Practices](#best-practices)
10. [Troubleshooting](#troubleshooting)

## Testing Philosophy

Our testing strategy follows the testing pyramid:

1. **Unit Tests (70%)**: Fast, isolated tests for individual functions and components
2. **Integration Tests (20%)**: Tests for component interactions and workflows
3. **End-to-End Tests (10%)**: Full user journey testing

### Key Principles

- **Test Behavior, Not Implementation**: Focus on what the component does, not how
- **Accessibility First**: Every test should consider accessibility
- **<PERSON><PERSON><PERSON>**: Test both success and failure paths
- **Performance Aware**: Include performance considerations in tests

## Test Setup

### Configuration

Tests are configured in `jest.config.js` with the following setup:

```javascript
module.exports = {
  preset: 'react-native',
  setupFilesAfterEnv: ['<rootDir>/src/__tests__/jestSetup.js'],
  testMatch: ['**/__tests__/**/*.test.(ts|tsx|js|jsx)'],
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/__tests__/**',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
};
```

### Test Environment

The test environment includes:

- **Jest**: Test runner and assertion library
- **React Native Testing Library**: Component testing utilities
- **Mock implementations**: For React Native modules
- **Accessibility testing**: Built-in accessibility validation

## Unit Testing

### Component Testing

#### Basic Component Test

```typescript
import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { Button } from '../Button';

describe('Button Component', () => {
  it('should render with correct label', () => {
    const { getByText } = render(
      <Button label="Click me" onPress={() => {}} />
    );
    
    expect(getByText('Click me')).toBeTruthy();
  });

  it('should call onPress when pressed', () => {
    const mockOnPress = jest.fn();
    const { getByText } = render(
      <Button label="Click me" onPress={mockOnPress} />
    );
    
    fireEvent.press(getByText('Click me'));
    expect(mockOnPress).toHaveBeenCalledTimes(1);
  });

  it('should be disabled when disabled prop is true', () => {
    const { getByText } = render(
      <Button label="Click me" onPress={() => {}} disabled />
    );
    
    const button = getByText('Click me').parent;
    expect(button).toHaveStyle({ opacity: 0.5 });
  });
});
```

#### Accessibility Testing in Components

```typescript
import { render } from '@testing-library/react-native';
import { AccessibilityTestUtils } from '../utils/accessibility';

describe('Button Accessibility', () => {
  it('should have proper accessibility props', () => {
    const { getByRole } = render(
      <Button label="Submit" onPress={() => {}} />
    );
    
    const button = getByRole('button');
    expect(button).toHaveAccessibilityRole('button');
    expect(button).toHaveAccessibilityLabel('Submit');
    expect(button).toHaveAccessibilityHint('Double tap to submit');
  });

  it('should announce state changes', () => {
    const { rerender, getByRole } = render(
      <Button label="Submit" onPress={() => {}} loading={false} />
    );
    
    rerender(<Button label="Submit" onPress={() => {}} loading={true} />);
    
    const button = getByRole('button');
    expect(button).toHaveAccessibilityState({ busy: true });
  });
});
```

### Utility Function Testing

#### Error Handler Testing

```typescript
import { errorHandler, ErrorType, ErrorSeverity } from '../utils/errorHandler';

describe('Error Handler', () => {
  beforeEach(() => {
    errorHandler.clearErrors();
  });

  it('should handle basic errors', () => {
    const error = new Error('Test error');
    const result = errorHandler.handleError(error);

    expect(result.type).toBe(ErrorType.UNKNOWN);
    expect(result.severity).toBe(ErrorSeverity.LOW);
    expect(result.message).toBe('Test error');
  });

  it('should detect network errors', () => {
    const error = new Error('fetch failed');
    const result = errorHandler.handleError(error);

    expect(result.type).toBe(ErrorType.NETWORK);
    expect(result.userMessage).toContain('internet connection');
  });
});
```

#### Form Validation Testing

```typescript
import { validateField, CommonValidations } from '../utils/formValidation';

describe('Form Validation', () => {
  describe('email validation', () => {
    it('should validate correct email', () => {
      const result = validateField('<EMAIL>', CommonValidations.email, {});
      expect(result.isValid).toBe(true);
      expect(result.error).toBeNull();
    });

    it('should reject invalid email', () => {
      const result = validateField('invalid-email', CommonValidations.email, {});
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Please enter a valid email address');
    });
  });
});
```

## Integration Testing

### Screen Testing

```typescript
import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { LoginScreen } from '../screens/LoginScreen';
import { AuthProvider } from '../contexts/AuthContext';

const renderWithProviders = (component) => {
  return render(
    <AuthProvider>
      {component}
    </AuthProvider>
  );
};

describe('Login Screen Integration', () => {
  it('should complete login flow', async () => {
    const { getByPlaceholderText, getByText } = renderWithProviders(
      <LoginScreen />
    );

    // Fill in form
    fireEvent.changeText(getByPlaceholderText('Email'), '<EMAIL>');
    fireEvent.changeText(getByPlaceholderText('Password'), 'password123');

    // Submit form
    fireEvent.press(getByText('Login'));

    // Wait for navigation or success state
    await waitFor(() => {
      expect(getByText('Welcome back!')).toBeTruthy();
    });
  });

  it('should handle login errors', async () => {
    const { getByPlaceholderText, getByText } = renderWithProviders(
      <LoginScreen />
    );

    // Fill in invalid credentials
    fireEvent.changeText(getByPlaceholderText('Email'), '<EMAIL>');
    fireEvent.changeText(getByPlaceholderText('Password'), 'wrongpassword');

    // Submit form
    fireEvent.press(getByText('Login'));

    // Wait for error message
    await waitFor(() => {
      expect(getByText('Invalid credentials')).toBeTruthy();
    });
  });
});
```

### Navigation Testing

```typescript
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { render, fireEvent } from '@testing-library/react-native';

const Stack = createStackNavigator();

const TestNavigator = () => (
  <NavigationContainer>
    <Stack.Navigator>
      <Stack.Screen name="Home" component={HomeScreen} />
      <Stack.Screen name="Profile" component={ProfileScreen} />
    </Stack.Navigator>
  </NavigationContainer>
);

describe('Navigation Integration', () => {
  it('should navigate between screens', () => {
    const { getByText } = render(<TestNavigator />);

    // Navigate to profile
    fireEvent.press(getByText('Go to Profile'));
    expect(getByText('Profile Screen')).toBeTruthy();

    // Navigate back
    fireEvent.press(getByText('Back'));
    expect(getByText('Home Screen')).toBeTruthy();
  });
});
```

## Accessibility Testing

### Automated Accessibility Testing

```typescript
import { render } from '@testing-library/react-native';
import { AccessibilityTestUtils } from '../utils/accessibility';

describe('Accessibility Compliance', () => {
  it('should pass accessibility audit', () => {
    const { container } = render(<MyComponent />);
    
    const issues = AccessibilityTestUtils.validateAccessibilityProps(container.props);
    expect(issues).toHaveLength(0);
  });

  it('should have proper color contrast', () => {
    const foreground = '#000000';
    const background = '#FFFFFF';
    
    const meetsStandards = ColorContrastUtils.meetsWCAGStandards(
      foreground, 
      background, 
      'AA', 
      'normal'
    );
    
    expect(meetsStandards).toBe(true);
  });
});
```

### Screen Reader Testing

```typescript
import { render } from '@testing-library/react-native';
import { AdvancedAccessibilityUtils } from '../utils/accessibility';

describe('Screen Reader Integration', () => {
  it('should announce form validation errors', () => {
    const mockAnnounce = jest.spyOn(AdvancedAccessibilityUtils, 'announceFormValidation');
    
    const { getByText } = render(<LoginForm />);
    
    // Trigger validation error
    fireEvent.press(getByText('Submit'));
    
    expect(mockAnnounce).toHaveBeenCalledWith(
      'Email',
      false,
      'Please enter a valid email address'
    );
  });

  it('should announce loading states', () => {
    const mockAnnounce = jest.spyOn(AdvancedAccessibilityUtils, 'announceLoadingState');
    
    const { rerender } = render(<DataComponent loading={false} />);
    rerender(<DataComponent loading={true} />);
    
    expect(mockAnnounce).toHaveBeenCalledWith(true, 'Data');
  });
});
```

## Performance Testing

### Bundle Size Testing

```typescript
import { BundleAnalyzer } from '../scripts/bundle-analyzer';

describe('Bundle Performance', () => {
  it('should maintain bundle size under threshold', async () => {
    const analyzer = new BundleAnalyzer();
    await analyzer.analyze();
    
    const stats = analyzer.getStats();
    expect(stats.totalSize).toBeLessThan(400 * 1024 * 1024); // 400MB
  });

  it('should not have duplicate dependencies', async () => {
    const analyzer = new BundleAnalyzer();
    await analyzer.analyze();
    
    const duplicates = analyzer.getDuplicates();
    expect(duplicates.length).toBe(0);
  });
});
```

### Render Performance Testing

```typescript
import { render } from '@testing-library/react-native';
import { measurePerformance } from '../utils/performance';

describe('Render Performance', () => {
  it('should render large lists efficiently', () => {
    const items = Array.from({ length: 1000 }, (_, i) => ({ id: i, name: `Item ${i}` }));
    
    const renderTime = measurePerformance(() => {
      render(<LargeList items={items} />);
    }, 'LargeList render');
    
    expect(renderTime).toBeLessThan(100); // 100ms threshold
  });
});
```

## Error Handling Testing

### Error Boundary Testing

```typescript
import React from 'react';
import { render } from '@testing-library/react-native';
import { ErrorBoundary } from '../components/error/ErrorBoundary';

const ThrowError = ({ shouldThrow }) => {
  if (shouldThrow) {
    throw new Error('Test error');
  }
  return <Text>No error</Text>;
};

describe('Error Boundary', () => {
  it('should catch and display errors', () => {
    const { getByText } = render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );

    expect(getByText('Oops! Something went wrong')).toBeTruthy();
  });

  it('should render children when no error', () => {
    const { getByText } = render(
      <ErrorBoundary>
        <ThrowError shouldThrow={false} />
      </ErrorBoundary>
    );

    expect(getByText('No error')).toBeTruthy();
  });
});
```

### Hook Error Testing

```typescript
import { renderHook } from '@testing-library/react-hooks';
import { useErrorHandler } from '../hooks/useErrorHandler';

describe('useErrorHandler Hook', () => {
  it('should handle async errors gracefully', async () => {
    const { result } = renderHook(() => useErrorHandler());
    
    const failingFunction = async () => {
      throw new Error('Async error');
    };
    
    const response = await result.current.handleAsyncError(failingFunction);
    expect(response).toBeNull();
  });
});
```

## Test Coverage

### Coverage Requirements

- **Overall Coverage**: Minimum 80%
- **Critical Components**: Minimum 95%
- **Utility Functions**: Minimum 90%
- **Error Handlers**: 100%
- **Accessibility Utils**: 100%

### Running Coverage

```bash
# Generate coverage report
npm run test:coverage

# View coverage in browser
npm run test:coverage:open

# Check coverage thresholds
npm run test:coverage:check
```

### Coverage Reports

Coverage reports are generated in multiple formats:
- **HTML**: `coverage/lcov-report/index.html`
- **JSON**: `coverage/coverage-final.json`
- **LCOV**: `coverage/lcov.info`

## Best Practices

### Test Organization

1. **Group Related Tests**: Use `describe` blocks to group related tests
2. **Clear Test Names**: Use descriptive test names that explain the scenario
3. **Setup and Teardown**: Use `beforeEach` and `afterEach` for test setup
4. **Mock External Dependencies**: Mock API calls and external services

### Test Writing Guidelines

1. **Arrange, Act, Assert**: Structure tests with clear phases
2. **Test One Thing**: Each test should verify one specific behavior
3. **Use Real User Interactions**: Test how users actually interact with the app
4. **Include Edge Cases**: Test boundary conditions and error scenarios

### Accessibility Testing Guidelines

1. **Test with Screen Readers**: Verify screen reader announcements
2. **Check Color Contrast**: Validate color combinations meet WCAG standards
3. **Verify Focus Management**: Ensure proper focus handling
4. **Test Keyboard Navigation**: Verify keyboard accessibility

### Performance Testing Guidelines

1. **Set Performance Budgets**: Define acceptable performance thresholds
2. **Test on Different Devices**: Consider various device capabilities
3. **Monitor Bundle Size**: Track bundle size changes over time
4. **Profile Render Performance**: Use React DevTools for profiling

## Troubleshooting

### Common Issues

#### Tests Timing Out

```typescript
// Increase timeout for slow operations
it('should handle slow operation', async () => {
  await waitFor(() => {
    expect(getByText('Loaded')).toBeTruthy();
  }, { timeout: 5000 });
}, 10000); // 10 second test timeout
```

#### Mock Issues

```typescript
// Reset mocks between tests
beforeEach(() => {
  jest.clearAllMocks();
});

// Mock specific modules
jest.mock('react-native', () => ({
  AccessibilityInfo: {
    isScreenReaderEnabled: jest.fn(() => Promise.resolve(false)),
  },
}));
```

#### Async Testing Issues

```typescript
// Use waitFor for async operations
await waitFor(() => {
  expect(mockFunction).toHaveBeenCalled();
});

// Use act for state updates
await act(async () => {
  fireEvent.press(button);
});
```

### Debugging Tests

1. **Use `screen.debug()`**: Print component tree for debugging
2. **Add Console Logs**: Temporary logging for test debugging
3. **Run Single Tests**: Use `it.only()` to run specific tests
4. **Check Test Output**: Review test output for error details

### Performance Debugging

1. **Profile Test Execution**: Use `--verbose` flag for detailed output
2. **Check Memory Usage**: Monitor memory usage during tests
3. **Optimize Test Setup**: Reduce unnecessary test setup
4. **Parallel Execution**: Use `--maxWorkers` for parallel test execution

## Continuous Integration

### GitHub Actions

Tests are automatically run on:
- **Pull Requests**: All tests must pass before merging
- **Main Branch**: Full test suite including coverage checks
- **Release Branches**: Extended test suite with performance tests

### Test Reports

- **Coverage Reports**: Uploaded to code coverage services
- **Test Results**: Available in CI/CD pipeline
- **Performance Metrics**: Tracked over time for regression detection

For more detailed examples, see the test files in `src/**/__tests__/` directories.
