# Installation
> `npm install --save @types/styled-components-react-native`

# Summary
This package contains type definitions for styled-components-react-native (https://github.com/styled-components/styled-components).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/styled-components-react-native.

### Additional Details
 * Last updated: Tue, 07 Nov 2023 15:11:36 GMT
 * Dependencies: [@types/react](https://npmjs.com/package/@types/react), [@types/react-native](https://npmjs.com/package/@types/react-native), [@types/styled-components](https://npmjs.com/package/@types/styled-components)

# Credits
These definitions were written by [<PERSON>](https://github.com/Methuselah96).
