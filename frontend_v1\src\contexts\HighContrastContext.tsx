/**
 * High Contrast Mode Context Provider
 *
 * Provides high contrast mode functionality for enhanced visual accessibility
 * following WCAG 2.2 AA guidelines and user preferences.
 *
 * Features:
 * - High contrast color schemes
 * - User preference persistence
 * - System preference detection
 * - Dynamic color adjustments
 * - Accessibility announcements
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { Platform } from 'react-native';
import { AccessibilityInfo, Appearance } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Colors } from '../constants/Colors';
import { getHighContrastColor, adjustColorForContrast } from '../utils/colorContrastUtils';

// High contrast color schemes
export const HIGH_CONTRAST_COLORS = {
  light: {
    background: {
      primary: '#FFFFFF',
      secondary: '#F0F0F0',
      elevated: '#FFFFFF',
      overlay: 'rgba(0, 0, 0, 0.8)',
    },
    text: {
      primary: '#000000',
      secondary: '#000000',
      tertiary: '#000000',
      inverse: '#FFFFFF',
      disabled: '#666666',
    },
    primary: {
      default: '#000000',
      light: '#333333',
      dark: '#000000',
      text: '#FFFFFF',
    },
    interactive: {
      primary: {
        default: '#000000',
        hover: '#333333',
        pressed: '#666666',
        disabled: '#CCCCCC',
        text: '#FFFFFF',
        textDisabled: '#999999',
      },
      secondary: {
        default: '#FFFFFF',
        hover: '#F0F0F0',
        pressed: '#E0E0E0',
        disabled: '#F8F8F8',
        border: '#000000',
        borderDisabled: '#CCCCCC',
        text: '#000000',
        textDisabled: '#999999',
      },
    },
    status: {
      success: '#000000',
      warning: '#000000',
      error: '#000000',
      info: '#000000',
    },
    border: {
      primary: '#000000',
      secondary: '#666666',
      tertiary: '#CCCCCC',
    },
  },
  dark: {
    background: {
      primary: '#000000',
      secondary: '#1A1A1A',
      elevated: '#000000',
      overlay: 'rgba(255, 255, 255, 0.8)',
    },
    text: {
      primary: '#FFFFFF',
      secondary: '#FFFFFF',
      tertiary: '#FFFFFF',
      inverse: '#000000',
      disabled: '#999999',
    },
    primary: {
      default: '#FFFFFF',
      light: '#CCCCCC',
      dark: '#FFFFFF',
      text: '#000000',
    },
    interactive: {
      primary: {
        default: '#FFFFFF',
        hover: '#CCCCCC',
        pressed: '#999999',
        disabled: '#333333',
        text: '#000000',
        textDisabled: '#666666',
      },
      secondary: {
        default: '#000000',
        hover: '#1A1A1A',
        pressed: '#333333',
        disabled: '#0A0A0A',
        border: '#FFFFFF',
        borderDisabled: '#333333',
        text: '#FFFFFF',
        textDisabled: '#666666',
      },
    },
    status: {
      success: '#FFFFFF',
      warning: '#FFFFFF',
      error: '#FFFFFF',
      info: '#FFFFFF',
    },
    border: {
      primary: '#FFFFFF',
      secondary: '#999999',
      tertiary: '#333333',
    },
  },
} as const;

// Context interface
interface HighContrastContextType {
  // State
  isHighContrastEnabled: boolean;
  highContrastScheme: 'light' | 'dark';
  colors: typeof Colors;
  
  // Actions
  toggleHighContrast: () => void;
  setHighContrastScheme: (scheme: 'light' | 'dark') => void;
  enableHighContrast: () => void;
  disableHighContrast: () => void;
  
  // Utilities
  getHighContrastColor: (color: string, background?: string) => string;
  adjustColorForAccessibility: (color: string, background: string) => string;
}

// Create context
const HighContrastContext = createContext<HighContrastContextType | undefined>(undefined);

// Storage key
const HIGH_CONTRAST_STORAGE_KEY = '@vierla_high_contrast_mode';
const HIGH_CONTRAST_SCHEME_STORAGE_KEY = '@vierla_high_contrast_scheme';

// Provider props
interface HighContrastProviderProps {
  children: React.ReactNode;
}

export const HighContrastProvider: React.FC<HighContrastProviderProps> = ({ children }) => {
  // State
  const [isHighContrastEnabled, setIsHighContrastEnabled] = useState(false);
  const [highContrastScheme, setHighContrastSchemeState] = useState<'light' | 'dark'>('light');
  const [systemColorScheme, setSystemColorScheme] = useState<'light' | 'dark'>('light');

  // Load saved preferences
  useEffect(() => {
    const loadPreferences = async () => {
      try {
        // Load high contrast preference
        const savedHighContrast = await AsyncStorage.getItem(HIGH_CONTRAST_STORAGE_KEY);
        if (savedHighContrast !== null) {
          setIsHighContrastEnabled(JSON.parse(savedHighContrast));
        } else {
          // Check system accessibility preferences
          if (Platform.OS === 'ios' || Platform.OS === 'android') {
            const isReduceTransparencyEnabled = await AccessibilityInfo.isReduceTransparencyEnabled?.();
            if (isReduceTransparencyEnabled) {
              setIsHighContrastEnabled(true);
            }
          }
        }

        // Load color scheme preference
        const savedScheme = await AsyncStorage.getItem(HIGH_CONTRAST_SCHEME_STORAGE_KEY);
        if (savedScheme) {
          setHighContrastSchemeState(savedScheme as 'light' | 'dark');
        } else {
          // Use system color scheme
          const systemScheme = Appearance.getColorScheme() || 'light';
          setSystemColorScheme(systemScheme);
          setHighContrastSchemeState(systemScheme);
        }
      } catch (error) {
        console.warn('Failed to load high contrast preferences:', error);
      }
    };

    loadPreferences();
  }, []);

  // Listen for system color scheme changes
  useEffect(() => {
    const subscription = Appearance.addChangeListener(({ colorScheme }) => {
      const scheme = colorScheme || 'light';
      setSystemColorScheme(scheme);
      
      // Update high contrast scheme if user hasn't manually set it
      AsyncStorage.getItem(HIGH_CONTRAST_SCHEME_STORAGE_KEY).then(savedScheme => {
        if (!savedScheme) {
          setHighContrastSchemeState(scheme);
        }
      });
    });

    return () => subscription?.remove();
  }, []);

  // Save high contrast preference
  const saveHighContrastPreference = useCallback(async (enabled: boolean) => {
    try {
      await AsyncStorage.setItem(HIGH_CONTRAST_STORAGE_KEY, JSON.stringify(enabled));
    } catch (error) {
      console.warn('Failed to save high contrast preference:', error);
    }
  }, []);

  // Save color scheme preference
  const saveSchemePreference = useCallback(async (scheme: 'light' | 'dark') => {
    try {
      await AsyncStorage.setItem(HIGH_CONTRAST_SCHEME_STORAGE_KEY, scheme);
    } catch (error) {
      console.warn('Failed to save color scheme preference:', error);
    }
  }, []);

  // Toggle high contrast mode
  const toggleHighContrast = useCallback(() => {
    const newValue = !isHighContrastEnabled;
    setIsHighContrastEnabled(newValue);
    saveHighContrastPreference(newValue);

    // Announce change to screen readers
    if (Platform.OS === 'ios' || Platform.OS === 'android') {
      AccessibilityInfo.announceForAccessibility(
        newValue ? 'High contrast mode enabled' : 'High contrast mode disabled'
      );
    }
  }, [isHighContrastEnabled, saveHighContrastPreference]);

  // Set high contrast scheme
  const setHighContrastScheme = useCallback((scheme: 'light' | 'dark') => {
    setHighContrastSchemeState(scheme);
    saveSchemePreference(scheme);

    // Announce change to screen readers
    if (Platform.OS === 'ios' || Platform.OS === 'android') {
      AccessibilityInfo.announceForAccessibility(`High contrast ${scheme} mode enabled`);
    }
  }, [saveSchemePreference]);

  // Enable high contrast
  const enableHighContrast = useCallback(() => {
    setIsHighContrastEnabled(true);
    saveHighContrastPreference(true);
  }, [saveHighContrastPreference]);

  // Disable high contrast
  const disableHighContrast = useCallback(() => {
    setIsHighContrastEnabled(false);
    saveHighContrastPreference(false);
  }, [saveHighContrastPreference]);

  // Get effective colors
  const colors = React.useMemo(() => {
    if (isHighContrastEnabled) {
      return HIGH_CONTRAST_COLORS[highContrastScheme];
    }
    return Colors;
  }, [isHighContrastEnabled, highContrastScheme]);

  // Utility functions
  const getHighContrastColorUtil = useCallback((color: string, background: string = '#FFFFFF') => {
    if (isHighContrastEnabled) {
      return getHighContrastColor(color, background);
    }
    return color;
  }, [isHighContrastEnabled]);

  const adjustColorForAccessibility = useCallback((color: string, background: string) => {
    if (isHighContrastEnabled) {
      return adjustColorForContrast(color, background, 7.0); // AAA level
    }
    return adjustColorForContrast(color, background, 4.5); // AA level
  }, [isHighContrastEnabled]);

  // Context value
  const contextValue: HighContrastContextType = {
    // State
    isHighContrastEnabled,
    highContrastScheme,
    colors,
    
    // Actions
    toggleHighContrast,
    setHighContrastScheme,
    enableHighContrast,
    disableHighContrast,
    
    // Utilities
    getHighContrastColor: getHighContrastColorUtil,
    adjustColorForAccessibility,
  };

  return (
    <HighContrastContext.Provider value={contextValue}>
      {children}
    </HighContrastContext.Provider>
  );
};

// Hook to use high contrast context
export const useHighContrast = (): HighContrastContextType => {
  const context = useContext(HighContrastContext);

  if (context === undefined) {
    console.warn('useHighContrast used outside HighContrastProvider, using fallback values');
    // Return fallback values instead of throwing
    return {
      isHighContrastEnabled: false,
      highContrastScheme: 'light',
      colors: {
        background: '#FFFFFF',
        surface: '#F5F5F5',
        primary: '#000000',
        secondary: '#666666',
        accent: '#007AFF',
        text: {
          primary: '#000000',
          secondary: '#666666',
          tertiary: '#999999',
          inverse: '#FFFFFF'
        },
        border: '#E0E0E0',
        error: '#FF3B30',
        warning: '#FF9500',
        success: '#34C759'
      },
      toggleHighContrast: () => {},
      setHighContrastScheme: () => {},
      enableHighContrast: () => {},
      disableHighContrast: () => {},
      getHighContrastColor: (color: string) => color,
      adjustColorForAccessibility: (color: string) => color
    };
  }

  return context;
};

// Convenience hooks
export const useHighContrastColors = () => {
  const { colors, isHighContrastEnabled } = useHighContrast();
  return { colors, isHighContrastEnabled };
};

export const useAccessibleColor = (color: string, background?: string) => {
  const { getHighContrastColor, adjustColorForAccessibility } = useHighContrast();
  
  return React.useMemo(() => {
    if (background) {
      return adjustColorForAccessibility(color, background);
    }
    return getHighContrastColor(color);
  }, [color, background, getHighContrastColor, adjustColorForAccessibility]);
};

export default HighContrastProvider;
