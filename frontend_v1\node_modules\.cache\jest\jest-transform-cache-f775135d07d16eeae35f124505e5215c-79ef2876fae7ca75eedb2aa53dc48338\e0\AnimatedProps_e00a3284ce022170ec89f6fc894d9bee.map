{"version": 3, "names": ["_NativeAnimatedHelper", "_interopRequireDefault", "require", "_RendererProxy", "_AnimatedEvent", "_AnimatedNode2", "_AnimatedObject", "_AnimatedStyle", "_invariant", "_Object$hasOwn", "_callSuper", "t", "o", "e", "_getPrototypeOf2", "default", "_possibleConstructorReturn2", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_superPropGet", "r", "p", "_get2", "createAnimatedProps", "inputProps", "allowlist", "nodeKeys", "nodes", "props", "keys", "Object", "ii", "length", "key", "value", "hasOwn", "node", "AnimatedStyle", "from", "style", "AnimatedNode", "AnimatedObject", "push", "__DEV__", "console", "error", "_animated<PERSON>iew", "_classPrivateFieldLooseKey2", "_callback", "_nodeKeys", "_nodes", "_props", "AnimatedProps", "exports", "_AnimatedNode", "callback", "config", "_this", "_classCallCheck2", "defineProperty", "writable", "_createAnimatedProps", "_createAnimatedProps2", "_slicedToArray2", "_classPrivateFieldLooseBase2", "_inherits2", "_createClass2", "__getValue", "AnimatedEvent", "__<PERSON><PERSON><PERSON><PERSON>", "__getValueWithStaticProps", "staticProps", "assign", "maybeNode", "__getValueWithStaticStyle", "__getAnimatedValue", "__attach", "__add<PERSON><PERSON>d", "__detach", "__isNative", "__disconnectAnimatedView", "__remove<PERSON><PERSON>d", "update", "__makeNative", "platformConfig", "__connectAnimatedView", "setNativeView", "animatedView", "invariant", "nativeViewTag", "findNodeHandle", "process", "env", "NODE_ENV", "Error", "NativeAnimatedHelper", "API", "connectAnimatedNodeToView", "__getNativeTag", "disconnectAnimatedNodeFromView", "__restore<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "restoreDefaultValues", "__getNativeConfig", "__getPlatformConfig", "propsConfig", "type", "debugID", "__getDebugID", "_hasOwnProp", "hasOwnProperty", "obj", "prop"], "sources": ["AnimatedProps.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n * @format\n */\n\nimport type {PlatformConfig} from '../AnimatedPlatformConfig';\nimport type {AnimatedNodeConfig} from './AnimatedNode';\nimport type {AnimatedStyleAllowlist} from './AnimatedStyle';\n\nimport NativeAnimatedHelper from '../../../src/private/animated/NativeAnimatedHelper';\nimport {findNodeHandle} from '../../ReactNative/RendererProxy';\nimport {AnimatedEvent} from '../AnimatedEvent';\nimport AnimatedNode from './AnimatedNode';\nimport AnimatedObject from './AnimatedObject';\nimport AnimatedStyle from './AnimatedStyle';\nimport invariant from 'invariant';\n\nexport type AnimatedPropsAllowlist = $ReadOnly<{\n  style?: ?AnimatedStyleAllowlist,\n  [string]: true,\n}>;\n\nfunction createAnimatedProps(\n  inputProps: {[string]: mixed},\n  allowlist: ?AnimatedPropsAllowlist,\n): [$ReadOnlyArray<string>, $ReadOnlyArray<AnimatedNode>, {[string]: mixed}] {\n  const nodeKeys: Array<string> = [];\n  const nodes: Array<AnimatedNode> = [];\n  const props: {[string]: mixed} = {};\n\n  const keys = Object.keys(inputProps);\n  for (let ii = 0, length = keys.length; ii < length; ii++) {\n    const key = keys[ii];\n    const value = inputProps[key];\n\n    if (allowlist == null || hasOwn(allowlist, key)) {\n      let node;\n      if (key === 'style') {\n        node = AnimatedStyle.from(value, allowlist?.style);\n      } else if (value instanceof AnimatedNode) {\n        node = value;\n      } else {\n        node = AnimatedObject.from(value);\n      }\n      if (node == null) {\n        props[key] = value;\n      } else {\n        nodeKeys.push(key);\n        nodes.push(node);\n        props[key] = node;\n      }\n    } else {\n      if (__DEV__) {\n        // WARNING: This is a potentially expensive check that we should only\n        // do in development. Without this check in development, it might be\n        // difficult to identify which props need to be allowlisted.\n        if (AnimatedObject.from(inputProps[key]) != null) {\n          console.error(\n            `AnimatedProps: ${key} is not allowlisted for animation, but it ` +\n              'contains AnimatedNode values; props allowing animation: ',\n            allowlist,\n          );\n        }\n      }\n      props[key] = value;\n    }\n  }\n\n  return [nodeKeys, nodes, props];\n}\n\nexport default class AnimatedProps extends AnimatedNode {\n  #animatedView: any = null;\n  #callback: () => void;\n  #nodeKeys: $ReadOnlyArray<string>;\n  #nodes: $ReadOnlyArray<AnimatedNode>;\n  #props: {[string]: mixed};\n\n  constructor(\n    inputProps: {[string]: mixed},\n    callback: () => void,\n    allowlist?: ?AnimatedPropsAllowlist,\n    config?: ?AnimatedNodeConfig,\n  ) {\n    super(config);\n    const [nodeKeys, nodes, props] = createAnimatedProps(inputProps, allowlist);\n    this.#nodeKeys = nodeKeys;\n    this.#nodes = nodes;\n    this.#props = props;\n    this.#callback = callback;\n  }\n\n  __getValue(): Object {\n    const props: {[string]: mixed} = {};\n\n    const keys = Object.keys(this.#props);\n    for (let ii = 0, length = keys.length; ii < length; ii++) {\n      const key = keys[ii];\n      const value = this.#props[key];\n\n      if (value instanceof AnimatedNode) {\n        props[key] = value.__getValue();\n      } else if (value instanceof AnimatedEvent) {\n        props[key] = value.__getHandler();\n      } else {\n        props[key] = value;\n      }\n    }\n\n    return props;\n  }\n\n  /**\n   * Creates a new `props` object that contains the same props as the supplied\n   * `staticProps` object, except with animated nodes for any props that were\n   * created by this `AnimatedProps` instance.\n   */\n  __getValueWithStaticProps(staticProps: Object): Object {\n    const props: {[string]: mixed} = {...staticProps};\n\n    const keys = Object.keys(staticProps);\n    for (let ii = 0, length = keys.length; ii < length; ii++) {\n      const key = keys[ii];\n      const maybeNode = this.#props[key];\n\n      if (key === 'style' && maybeNode instanceof AnimatedStyle) {\n        props[key] = maybeNode.__getValueWithStaticStyle(staticProps.style);\n      } else if (maybeNode instanceof AnimatedNode) {\n        props[key] = maybeNode.__getValue();\n      } else if (maybeNode instanceof AnimatedEvent) {\n        props[key] = maybeNode.__getHandler();\n      }\n    }\n\n    return props;\n  }\n\n  __getAnimatedValue(): Object {\n    const props: {[string]: mixed} = {};\n\n    const nodeKeys = this.#nodeKeys;\n    const nodes = this.#nodes;\n    for (let ii = 0, length = nodes.length; ii < length; ii++) {\n      const key = nodeKeys[ii];\n      const node = nodes[ii];\n      props[key] = node.__getAnimatedValue();\n    }\n\n    return props;\n  }\n\n  __attach(): void {\n    const nodes = this.#nodes;\n    for (let ii = 0, length = nodes.length; ii < length; ii++) {\n      const node = nodes[ii];\n      node.__addChild(this);\n    }\n    super.__attach();\n  }\n\n  __detach(): void {\n    if (this.__isNative && this.#animatedView) {\n      this.__disconnectAnimatedView();\n    }\n    this.#animatedView = null;\n\n    const nodes = this.#nodes;\n    for (let ii = 0, length = nodes.length; ii < length; ii++) {\n      const node = nodes[ii];\n      node.__removeChild(this);\n    }\n\n    super.__detach();\n  }\n\n  update(): void {\n    this.#callback();\n  }\n\n  __makeNative(platformConfig: ?PlatformConfig): void {\n    const nodes = this.#nodes;\n    for (let ii = 0, length = nodes.length; ii < length; ii++) {\n      const node = nodes[ii];\n      node.__makeNative(platformConfig);\n    }\n\n    if (!this.__isNative) {\n      this.__isNative = true;\n\n      // Since this does not call the super.__makeNative, we need to store the\n      // supplied platformConfig here, before calling __connectAnimatedView\n      // where it will be needed to traverse the graph of attached values.\n      super.__setPlatformConfig(platformConfig);\n\n      if (this.#animatedView) {\n        this.__connectAnimatedView();\n      }\n    }\n  }\n\n  setNativeView(animatedView: any): void {\n    if (this.#animatedView === animatedView) {\n      return;\n    }\n    this.#animatedView = animatedView;\n    if (this.__isNative) {\n      this.__connectAnimatedView();\n    }\n  }\n\n  __connectAnimatedView(): void {\n    invariant(this.__isNative, 'Expected node to be marked as \"native\"');\n    let nativeViewTag: ?number = findNodeHandle(this.#animatedView);\n    if (nativeViewTag == null) {\n      if (process.env.NODE_ENV === 'test') {\n        nativeViewTag = -1;\n      } else {\n        throw new Error('Unable to locate attached view in the native tree');\n      }\n    }\n    NativeAnimatedHelper.API.connectAnimatedNodeToView(\n      this.__getNativeTag(),\n      nativeViewTag,\n    );\n  }\n\n  __disconnectAnimatedView(): void {\n    invariant(this.__isNative, 'Expected node to be marked as \"native\"');\n    let nativeViewTag: ?number = findNodeHandle(this.#animatedView);\n    if (nativeViewTag == null) {\n      if (process.env.NODE_ENV === 'test') {\n        nativeViewTag = -1;\n      } else {\n        throw new Error('Unable to locate attached view in the native tree');\n      }\n    }\n    NativeAnimatedHelper.API.disconnectAnimatedNodeFromView(\n      this.__getNativeTag(),\n      nativeViewTag,\n    );\n  }\n\n  __restoreDefaultValues(): void {\n    // When using the native driver, view properties need to be restored to\n    // their default values manually since react no longer tracks them. This\n    // is needed to handle cases where a prop driven by native animated is removed\n    // after having been changed natively by an animation.\n    if (this.__isNative) {\n      NativeAnimatedHelper.API.restoreDefaultValues(this.__getNativeTag());\n    }\n  }\n\n  __getNativeConfig(): Object {\n    const platformConfig = this.__getPlatformConfig();\n    const propsConfig: {[string]: number} = {};\n\n    const nodeKeys = this.#nodeKeys;\n    const nodes = this.#nodes;\n    for (let ii = 0, length = nodes.length; ii < length; ii++) {\n      const key = nodeKeys[ii];\n      const node = nodes[ii];\n      node.__makeNative(platformConfig);\n      propsConfig[key] = node.__getNativeTag();\n    }\n\n    return {\n      type: 'props',\n      props: propsConfig,\n      debugID: this.__getDebugID(),\n    };\n  }\n}\n\n// Supported versions of JSC do not implement the newer Object.hasOwn. Remove\n// this shim when they do.\n// $FlowIgnore[method-unbinding]\nconst _hasOwnProp = Object.prototype.hasOwnProperty;\nconst hasOwn: (obj: $ReadOnly<{...}>, prop: string) => boolean =\n  // $FlowIgnore[method-unbinding]\n  Object.hasOwn ?? ((obj, prop) => _hasOwnProp.call(obj, prop));\n"], "mappings": ";;;;;;;;;;;;;;AAcA,IAAAA,qBAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,cAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAF,OAAA;AACA,IAAAG,cAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,eAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,cAAA,GAAAN,sBAAA,CAAAC,OAAA;AACA,IAAAM,UAAA,GAAAP,sBAAA,CAAAC,OAAA;AAAkC,IAAAO,cAAA;AAAA,SAAAC,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAE,gBAAA,CAAAC,OAAA,EAAAH,CAAA,OAAAI,2BAAA,CAAAD,OAAA,EAAAJ,CAAA,EAAAM,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAP,CAAA,EAAAC,CAAA,YAAAC,gBAAA,CAAAC,OAAA,EAAAJ,CAAA,EAAAS,WAAA,IAAAR,CAAA,CAAAS,KAAA,CAAAV,CAAA,EAAAE,CAAA;AAAA,SAAAI,0BAAA,cAAAN,CAAA,IAAAW,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAX,CAAA,aAAAM,yBAAA,YAAAA,0BAAA,aAAAN,CAAA;AAAA,SAAAe,cAAAf,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAc,CAAA,QAAAC,CAAA,OAAAC,KAAA,CAAAd,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAY,CAAA,GAAAhB,CAAA,CAAAY,SAAA,GAAAZ,CAAA,GAAAC,CAAA,EAAAC,CAAA,cAAAc,CAAA,yBAAAC,CAAA,aAAAjB,CAAA,WAAAiB,CAAA,CAAAP,KAAA,CAAAR,CAAA,EAAAF,CAAA,OAAAiB,CAAA;AAOlC,SAASE,mBAAmBA,CAC1BC,UAA6B,EAC7BC,SAAkC,EACyC;EAC3E,IAAMC,QAAuB,GAAG,EAAE;EAClC,IAAMC,KAA0B,GAAG,EAAE;EACrC,IAAMC,KAAwB,GAAG,CAAC,CAAC;EAEnC,IAAMC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACL,UAAU,CAAC;EACpC,KAAK,IAAIO,EAAE,GAAG,CAAC,EAAEC,MAAM,GAAGH,IAAI,CAACG,MAAM,EAAED,EAAE,GAAGC,MAAM,EAAED,EAAE,EAAE,EAAE;IACxD,IAAME,GAAG,GAAGJ,IAAI,CAACE,EAAE,CAAC;IACpB,IAAMG,KAAK,GAAGV,UAAU,CAACS,GAAG,CAAC;IAE7B,IAAIR,SAAS,IAAI,IAAI,IAAIU,MAAM,CAACV,SAAS,EAAEQ,GAAG,CAAC,EAAE;MAC/C,IAAIG,IAAI;MACR,IAAIH,GAAG,KAAK,OAAO,EAAE;QACnBG,IAAI,GAAGC,sBAAa,CAACC,IAAI,CAACJ,KAAK,EAAET,SAAS,oBAATA,SAAS,CAAEc,KAAK,CAAC;MACpD,CAAC,MAAM,IAAIL,KAAK,YAAYM,sBAAY,EAAE;QACxCJ,IAAI,GAAGF,KAAK;MACd,CAAC,MAAM;QACLE,IAAI,GAAGK,uBAAc,CAACH,IAAI,CAACJ,KAAK,CAAC;MACnC;MACA,IAAIE,IAAI,IAAI,IAAI,EAAE;QAChBR,KAAK,CAACK,GAAG,CAAC,GAAGC,KAAK;MACpB,CAAC,MAAM;QACLR,QAAQ,CAACgB,IAAI,CAACT,GAAG,CAAC;QAClBN,KAAK,CAACe,IAAI,CAACN,IAAI,CAAC;QAChBR,KAAK,CAACK,GAAG,CAAC,GAAGG,IAAI;MACnB;IACF,CAAC,MAAM;MACL,IAAIO,OAAO,EAAE;QAIX,IAAIF,uBAAc,CAACH,IAAI,CAACd,UAAU,CAACS,GAAG,CAAC,CAAC,IAAI,IAAI,EAAE;UAChDW,OAAO,CAACC,KAAK,CACX,kBAAkBZ,GAAG,4CAA4C,GAC/D,0DAA0D,EAC5DR,SACF,CAAC;QACH;MACF;MACAG,KAAK,CAACK,GAAG,CAAC,GAAGC,KAAK;IACpB;EACF;EAEA,OAAO,CAACR,QAAQ,EAAEC,KAAK,EAAEC,KAAK,CAAC;AACjC;AAAC,IAAAkB,aAAA,OAAAC,2BAAA,CAAAvC,OAAA;AAAA,IAAAwC,SAAA,OAAAD,2BAAA,CAAAvC,OAAA;AAAA,IAAAyC,SAAA,OAAAF,2BAAA,CAAAvC,OAAA;AAAA,IAAA0C,MAAA,OAAAH,2BAAA,CAAAvC,OAAA;AAAA,IAAA2C,MAAA,OAAAJ,2BAAA,CAAAvC,OAAA;AAAA,IAEoB4C,aAAa,GAAAC,OAAA,CAAA7C,OAAA,aAAA8C,aAAA;EAOhC,SAAAF,cACE5B,UAA6B,EAC7B+B,QAAoB,EACpB9B,SAAmC,EACnC+B,MAA4B,EAC5B;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAAlD,OAAA,QAAA4C,aAAA;IACAK,KAAA,GAAAtD,UAAA,OAAAiD,aAAA,GAAMI,MAAM;IAAE1B,MAAA,CAAA6B,cAAA,CAAAF,KAAA,EAAAX,aAAA;MAAAc,QAAA;MAAA1B,KAAA,EAZK;IAAI;IAAAJ,MAAA,CAAA6B,cAAA,CAAAF,KAAA,EAAAT,SAAA;MAAAY,QAAA;MAAA1B,KAAA;IAAA;IAAAJ,MAAA,CAAA6B,cAAA,CAAAF,KAAA,EAAAR,SAAA;MAAAW,QAAA;MAAA1B,KAAA;IAAA;IAAAJ,MAAA,CAAA6B,cAAA,CAAAF,KAAA,EAAAP,MAAA;MAAAU,QAAA;MAAA1B,KAAA;IAAA;IAAAJ,MAAA,CAAA6B,cAAA,CAAAF,KAAA,EAAAN,MAAA;MAAAS,QAAA;MAAA1B,KAAA;IAAA;IAavB,IAAA2B,oBAAA,GAAiCtC,mBAAmB,CAACC,UAAU,EAAEC,SAAS,CAAC;MAAAqC,qBAAA,OAAAC,eAAA,CAAAvD,OAAA,EAAAqD,oBAAA;MAApEnC,QAAQ,GAAAoC,qBAAA;MAAEnC,KAAK,GAAAmC,qBAAA;MAAElC,KAAK,GAAAkC,qBAAA;IAC7B,IAAAE,4BAAA,CAAAxD,OAAA,EAAAiD,KAAA,EAAAR,SAAA,EAAAA,SAAA,IAAiBvB,QAAQ;IACzB,IAAAsC,4BAAA,CAAAxD,OAAA,EAAAiD,KAAA,EAAAP,MAAA,EAAAA,MAAA,IAAcvB,KAAK;IACnB,IAAAqC,4BAAA,CAAAxD,OAAA,EAAAiD,KAAA,EAAAN,MAAA,EAAAA,MAAA,IAAcvB,KAAK;IACnB,IAAAoC,4BAAA,CAAAxD,OAAA,EAAAiD,KAAA,EAAAT,SAAA,EAAAA,SAAA,IAAiBO,QAAQ;IAAC,OAAAE,KAAA;EAC5B;EAAC,IAAAQ,UAAA,CAAAzD,OAAA,EAAA4C,aAAA,EAAAE,aAAA;EAAA,WAAAY,aAAA,CAAA1D,OAAA,EAAA4C,aAAA;IAAAnB,GAAA;IAAAC,KAAA,EAED,SAAAiC,UAAUA,CAAA,EAAW;MACnB,IAAMvC,KAAwB,GAAG,CAAC,CAAC;MAEnC,IAAMC,IAAI,GAAGC,MAAM,CAACD,IAAI,KAAAmC,4BAAA,CAAAxD,OAAA,EAAC,IAAI,EAAA2C,MAAA,EAAAA,MAAA,CAAO,CAAC;MACrC,KAAK,IAAIpB,EAAE,GAAG,CAAC,EAAEC,MAAM,GAAGH,IAAI,CAACG,MAAM,EAAED,EAAE,GAAGC,MAAM,EAAED,EAAE,EAAE,EAAE;QACxD,IAAME,GAAG,GAAGJ,IAAI,CAACE,EAAE,CAAC;QACpB,IAAMG,KAAK,GAAG,IAAA8B,4BAAA,CAAAxD,OAAA,MAAI,EAAA2C,MAAA,EAAAA,MAAA,EAAQlB,GAAG,CAAC;QAE9B,IAAIC,KAAK,YAAYM,sBAAY,EAAE;UACjCZ,KAAK,CAACK,GAAG,CAAC,GAAGC,KAAK,CAACiC,UAAU,CAAC,CAAC;QACjC,CAAC,MAAM,IAAIjC,KAAK,YAAYkC,4BAAa,EAAE;UACzCxC,KAAK,CAACK,GAAG,CAAC,GAAGC,KAAK,CAACmC,YAAY,CAAC,CAAC;QACnC,CAAC,MAAM;UACLzC,KAAK,CAACK,GAAG,CAAC,GAAGC,KAAK;QACpB;MACF;MAEA,OAAON,KAAK;IACd;EAAC;IAAAK,GAAA;IAAAC,KAAA,EAOD,SAAAoC,yBAAyBA,CAACC,WAAmB,EAAU;MACrD,IAAM3C,KAAwB,GAAAE,MAAA,CAAA0C,MAAA,KAAOD,WAAW,CAAC;MAEjD,IAAM1C,IAAI,GAAGC,MAAM,CAACD,IAAI,CAAC0C,WAAW,CAAC;MACrC,KAAK,IAAIxC,EAAE,GAAG,CAAC,EAAEC,MAAM,GAAGH,IAAI,CAACG,MAAM,EAAED,EAAE,GAAGC,MAAM,EAAED,EAAE,EAAE,EAAE;QACxD,IAAME,GAAG,GAAGJ,IAAI,CAACE,EAAE,CAAC;QACpB,IAAM0C,SAAS,GAAG,IAAAT,4BAAA,CAAAxD,OAAA,MAAI,EAAA2C,MAAA,EAAAA,MAAA,EAAQlB,GAAG,CAAC;QAElC,IAAIA,GAAG,KAAK,OAAO,IAAIwC,SAAS,YAAYpC,sBAAa,EAAE;UACzDT,KAAK,CAACK,GAAG,CAAC,GAAGwC,SAAS,CAACC,yBAAyB,CAACH,WAAW,CAAChC,KAAK,CAAC;QACrE,CAAC,MAAM,IAAIkC,SAAS,YAAYjC,sBAAY,EAAE;UAC5CZ,KAAK,CAACK,GAAG,CAAC,GAAGwC,SAAS,CAACN,UAAU,CAAC,CAAC;QACrC,CAAC,MAAM,IAAIM,SAAS,YAAYL,4BAAa,EAAE;UAC7CxC,KAAK,CAACK,GAAG,CAAC,GAAGwC,SAAS,CAACJ,YAAY,CAAC,CAAC;QACvC;MACF;MAEA,OAAOzC,KAAK;IACd;EAAC;IAAAK,GAAA;IAAAC,KAAA,EAED,SAAAyC,kBAAkBA,CAAA,EAAW;MAC3B,IAAM/C,KAAwB,GAAG,CAAC,CAAC;MAEnC,IAAMF,QAAQ,OAAAsC,4BAAA,CAAAxD,OAAA,EAAG,IAAI,EAAAyC,SAAA,EAAAA,SAAA,CAAU;MAC/B,IAAMtB,KAAK,OAAAqC,4BAAA,CAAAxD,OAAA,EAAG,IAAI,EAAA0C,MAAA,EAAAA,MAAA,CAAO;MACzB,KAAK,IAAInB,EAAE,GAAG,CAAC,EAAEC,MAAM,GAAGL,KAAK,CAACK,MAAM,EAAED,EAAE,GAAGC,MAAM,EAAED,EAAE,EAAE,EAAE;QACzD,IAAME,GAAG,GAAGP,QAAQ,CAACK,EAAE,CAAC;QACxB,IAAMK,IAAI,GAAGT,KAAK,CAACI,EAAE,CAAC;QACtBH,KAAK,CAACK,GAAG,CAAC,GAAGG,IAAI,CAACuC,kBAAkB,CAAC,CAAC;MACxC;MAEA,OAAO/C,KAAK;IACd;EAAC;IAAAK,GAAA;IAAAC,KAAA,EAED,SAAA0C,QAAQA,CAAA,EAAS;MACf,IAAMjD,KAAK,OAAAqC,4BAAA,CAAAxD,OAAA,EAAG,IAAI,EAAA0C,MAAA,EAAAA,MAAA,CAAO;MACzB,KAAK,IAAInB,EAAE,GAAG,CAAC,EAAEC,MAAM,GAAGL,KAAK,CAACK,MAAM,EAAED,EAAE,GAAGC,MAAM,EAAED,EAAE,EAAE,EAAE;QACzD,IAAMK,IAAI,GAAGT,KAAK,CAACI,EAAE,CAAC;QACtBK,IAAI,CAACyC,UAAU,CAAC,IAAI,CAAC;MACvB;MACA1D,aAAA,CAAAiC,aAAA;IACF;EAAC;IAAAnB,GAAA;IAAAC,KAAA,EAED,SAAA4C,QAAQA,CAAA,EAAS;MACf,IAAI,IAAI,CAACC,UAAU,QAAAf,4BAAA,CAAAxD,OAAA,EAAI,IAAI,EAAAsC,aAAA,EAAAA,aAAA,CAAc,EAAE;QACzC,IAAI,CAACkC,wBAAwB,CAAC,CAAC;MACjC;MACA,IAAAhB,4BAAA,CAAAxD,OAAA,MAAI,EAAAsC,aAAA,EAAAA,aAAA,IAAiB,IAAI;MAEzB,IAAMnB,KAAK,OAAAqC,4BAAA,CAAAxD,OAAA,EAAG,IAAI,EAAA0C,MAAA,EAAAA,MAAA,CAAO;MACzB,KAAK,IAAInB,EAAE,GAAG,CAAC,EAAEC,MAAM,GAAGL,KAAK,CAACK,MAAM,EAAED,EAAE,GAAGC,MAAM,EAAED,EAAE,EAAE,EAAE;QACzD,IAAMK,IAAI,GAAGT,KAAK,CAACI,EAAE,CAAC;QACtBK,IAAI,CAAC6C,aAAa,CAAC,IAAI,CAAC;MAC1B;MAEA9D,aAAA,CAAAiC,aAAA;IACF;EAAC;IAAAnB,GAAA;IAAAC,KAAA,EAED,SAAAgD,MAAMA,CAAA,EAAS;MACb,IAAAlB,4BAAA,CAAAxD,OAAA,MAAI,EAAAwC,SAAA,EAAAA,SAAA;IACN;EAAC;IAAAf,GAAA;IAAAC,KAAA,EAED,SAAAiD,YAAYA,CAACC,cAA+B,EAAQ;MAClD,IAAMzD,KAAK,OAAAqC,4BAAA,CAAAxD,OAAA,EAAG,IAAI,EAAA0C,MAAA,EAAAA,MAAA,CAAO;MACzB,KAAK,IAAInB,EAAE,GAAG,CAAC,EAAEC,MAAM,GAAGL,KAAK,CAACK,MAAM,EAAED,EAAE,GAAGC,MAAM,EAAED,EAAE,EAAE,EAAE;QACzD,IAAMK,IAAI,GAAGT,KAAK,CAACI,EAAE,CAAC;QACtBK,IAAI,CAAC+C,YAAY,CAACC,cAAc,CAAC;MACnC;MAEA,IAAI,CAAC,IAAI,CAACL,UAAU,EAAE;QACpB,IAAI,CAACA,UAAU,GAAG,IAAI;QAKtB5D,aAAA,CAAAiC,aAAA,mCAA0BgC,cAAc;QAExC,QAAApB,4BAAA,CAAAxD,OAAA,EAAI,IAAI,EAAAsC,aAAA,EAAAA,aAAA,GAAgB;UACtB,IAAI,CAACuC,qBAAqB,CAAC,CAAC;QAC9B;MACF;IACF;EAAC;IAAApD,GAAA;IAAAC,KAAA,EAED,SAAAoD,aAAaA,CAACC,YAAiB,EAAQ;MACrC,IAAI,IAAAvB,4BAAA,CAAAxD,OAAA,MAAI,EAAAsC,aAAA,EAAAA,aAAA,MAAmByC,YAAY,EAAE;QACvC;MACF;MACA,IAAAvB,4BAAA,CAAAxD,OAAA,MAAI,EAAAsC,aAAA,EAAAA,aAAA,IAAiByC,YAAY;MACjC,IAAI,IAAI,CAACR,UAAU,EAAE;QACnB,IAAI,CAACM,qBAAqB,CAAC,CAAC;MAC9B;IACF;EAAC;IAAApD,GAAA;IAAAC,KAAA,EAED,SAAAmD,qBAAqBA,CAAA,EAAS;MAC5B,IAAAG,kBAAS,EAAC,IAAI,CAACT,UAAU,EAAE,wCAAwC,CAAC;MACpE,IAAIU,aAAsB,GAAG,IAAAC,6BAAc,MAAA1B,4BAAA,CAAAxD,OAAA,EAAC,IAAI,EAAAsC,aAAA,EAAAA,aAAA,CAAc,CAAC;MAC/D,IAAI2C,aAAa,IAAI,IAAI,EAAE;QACzB,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,EAAE;UACnCJ,aAAa,GAAG,CAAC,CAAC;QACpB,CAAC,MAAM;UACL,MAAM,IAAIK,KAAK,CAAC,mDAAmD,CAAC;QACtE;MACF;MACAC,6BAAoB,CAACC,GAAG,CAACC,yBAAyB,CAChD,IAAI,CAACC,cAAc,CAAC,CAAC,EACrBT,aACF,CAAC;IACH;EAAC;IAAAxD,GAAA;IAAAC,KAAA,EAED,SAAA8C,wBAAwBA,CAAA,EAAS;MAC/B,IAAAQ,kBAAS,EAAC,IAAI,CAACT,UAAU,EAAE,wCAAwC,CAAC;MACpE,IAAIU,aAAsB,GAAG,IAAAC,6BAAc,MAAA1B,4BAAA,CAAAxD,OAAA,EAAC,IAAI,EAAAsC,aAAA,EAAAA,aAAA,CAAc,CAAC;MAC/D,IAAI2C,aAAa,IAAI,IAAI,EAAE;QACzB,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,EAAE;UACnCJ,aAAa,GAAG,CAAC,CAAC;QACpB,CAAC,MAAM;UACL,MAAM,IAAIK,KAAK,CAAC,mDAAmD,CAAC;QACtE;MACF;MACAC,6BAAoB,CAACC,GAAG,CAACG,8BAA8B,CACrD,IAAI,CAACD,cAAc,CAAC,CAAC,EACrBT,aACF,CAAC;IACH;EAAC;IAAAxD,GAAA;IAAAC,KAAA,EAED,SAAAkE,sBAAsBA,CAAA,EAAS;MAK7B,IAAI,IAAI,CAACrB,UAAU,EAAE;QACnBgB,6BAAoB,CAACC,GAAG,CAACK,oBAAoB,CAAC,IAAI,CAACH,cAAc,CAAC,CAAC,CAAC;MACtE;IACF;EAAC;IAAAjE,GAAA;IAAAC,KAAA,EAED,SAAAoE,iBAAiBA,CAAA,EAAW;MAC1B,IAAMlB,cAAc,GAAG,IAAI,CAACmB,mBAAmB,CAAC,CAAC;MACjD,IAAMC,WAA+B,GAAG,CAAC,CAAC;MAE1C,IAAM9E,QAAQ,OAAAsC,4BAAA,CAAAxD,OAAA,EAAG,IAAI,EAAAyC,SAAA,EAAAA,SAAA,CAAU;MAC/B,IAAMtB,KAAK,OAAAqC,4BAAA,CAAAxD,OAAA,EAAG,IAAI,EAAA0C,MAAA,EAAAA,MAAA,CAAO;MACzB,KAAK,IAAInB,EAAE,GAAG,CAAC,EAAEC,MAAM,GAAGL,KAAK,CAACK,MAAM,EAAED,EAAE,GAAGC,MAAM,EAAED,EAAE,EAAE,EAAE;QACzD,IAAME,GAAG,GAAGP,QAAQ,CAACK,EAAE,CAAC;QACxB,IAAMK,IAAI,GAAGT,KAAK,CAACI,EAAE,CAAC;QACtBK,IAAI,CAAC+C,YAAY,CAACC,cAAc,CAAC;QACjCoB,WAAW,CAACvE,GAAG,CAAC,GAAGG,IAAI,CAAC8D,cAAc,CAAC,CAAC;MAC1C;MAEA,OAAO;QACLO,IAAI,EAAE,OAAO;QACb7E,KAAK,EAAE4E,WAAW;QAClBE,OAAO,EAAE,IAAI,CAACC,YAAY,CAAC;MAC7B,CAAC;IACH;EAAC;AAAA,EAvMwCnE,sBAAY;AA6MvD,IAAMoE,WAAW,GAAG9E,MAAM,CAACd,SAAS,CAAC6F,cAAc;AACnD,IAAM1E,MAAwD,IAAAjC,cAAA,GAE5D4B,MAAM,CAACK,MAAM,YAAAjC,cAAA,GAAK,UAAC4G,GAAG,EAAEC,IAAI;EAAA,OAAKH,WAAW,CAAC1F,IAAI,CAAC4F,GAAG,EAAEC,IAAI,CAAC;AAAA,CAAC", "ignoreList": []}