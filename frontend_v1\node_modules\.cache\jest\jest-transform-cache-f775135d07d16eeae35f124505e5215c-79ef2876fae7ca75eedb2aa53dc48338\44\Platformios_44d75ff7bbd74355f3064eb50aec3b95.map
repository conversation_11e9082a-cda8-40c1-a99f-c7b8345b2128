{"version": 3, "names": ["_NativePlatformConstantsIOS", "_interopRequireDefault", "require", "Platform", "__constants", "OS", "Version", "constants", "osVersion", "NativePlatformConstantsIOS", "getConstants", "isPad", "interfaceIdiom", "isTV", "isVision", "isTesting", "__DEV__", "isDisableAnimations", "_this$constants$isDis", "isMacCatalyst", "_this$constants$isMac", "select", "spec", "ios", "native", "default", "_default", "exports"], "sources": ["Platform.ios.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict\n */\n\nimport type {\n  Platform as PlatformType,\n  PlatformSelectSpec,\n} from './PlatformTypes';\n\nimport NativePlatformConstantsIOS from './NativePlatformConstantsIOS';\n\nconst Platform: PlatformType = {\n  __constants: null,\n  OS: 'ios',\n  // $FlowFixMe[unsafe-getters-setters]\n  get Version(): string {\n    // $FlowFixMe[object-this-reference]\n    return this.constants.osVersion;\n  },\n  // $FlowFixMe[unsafe-getters-setters]\n  get constants(): {\n    forceTouchAvailable: boolean,\n    interfaceIdiom: string,\n    isTesting: boolean,\n    isDisableAnimations?: boolean,\n    osVersion: string,\n    reactNativeVersion: {\n      major: number,\n      minor: number,\n      patch: number,\n      prerelease: ?string,\n    },\n    systemName: string,\n    isMacCatalyst?: boolean,\n  } {\n    // $FlowFixMe[object-this-reference]\n    if (this.__constants == null) {\n      // $FlowFixMe[object-this-reference]\n      this.__constants = NativePlatformConstantsIOS.getConstants();\n    }\n    // $FlowFixMe[object-this-reference]\n    return this.__constants;\n  },\n  // $FlowFixMe[unsafe-getters-setters]\n  get isPad(): boolean {\n    // $FlowFixMe[object-this-reference]\n    return this.constants.interfaceIdiom === 'pad';\n  },\n  // $FlowFixMe[unsafe-getters-setters]\n  get isTV(): boolean {\n    // $FlowFixMe[object-this-reference]\n    return this.constants.interfaceIdiom === 'tv';\n  },\n  // $FlowFixMe[unsafe-getters-setters]\n  get isVision(): boolean {\n    // $FlowFixMe[object-this-reference]\n    return this.constants.interfaceIdiom === 'vision';\n  },\n  // $FlowFixMe[unsafe-getters-setters]\n  get isTesting(): boolean {\n    if (__DEV__) {\n      // $FlowFixMe[object-this-reference]\n      return this.constants.isTesting;\n    }\n    return false;\n  },\n  // $FlowFixMe[unsafe-getters-setters]\n  get isDisableAnimations(): boolean {\n    // $FlowFixMe[object-this-reference]\n    return this.constants.isDisableAnimations ?? this.isTesting;\n  },\n  // $FlowFixMe[unsafe-getters-setters]\n  get isMacCatalyst(): boolean {\n    // $FlowFixMe[object-this-reference]\n    return this.constants.isMacCatalyst ?? false;\n  },\n  select: <T>(spec: PlatformSelectSpec<T>): T =>\n    // $FlowFixMe[incompatible-return]\n    'ios' in spec ? spec.ios : 'native' in spec ? spec.native : spec.default,\n};\n\nexport default Platform;\n"], "mappings": ";;;;;AAeA,IAAAA,2BAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAMC,QAAsB,GAAG;EAC7BC,WAAW,EAAE,IAAI;EACjBC,EAAE,EAAE,KAAK;EAET,IAAIC,OAAOA,CAAA,EAAW;IAEpB,OAAO,IAAI,CAACC,SAAS,CAACC,SAAS;EACjC,CAAC;EAED,IAAID,SAASA,CAAA,EAcX;IAEA,IAAI,IAAI,CAACH,WAAW,IAAI,IAAI,EAAE;MAE5B,IAAI,CAACA,WAAW,GAAGK,mCAA0B,CAACC,YAAY,CAAC,CAAC;IAC9D;IAEA,OAAO,IAAI,CAACN,WAAW;EACzB,CAAC;EAED,IAAIO,KAAKA,CAAA,EAAY;IAEnB,OAAO,IAAI,CAACJ,SAAS,CAACK,cAAc,KAAK,KAAK;EAChD,CAAC;EAED,IAAIC,IAAIA,CAAA,EAAY;IAElB,OAAO,IAAI,CAACN,SAAS,CAACK,cAAc,KAAK,IAAI;EAC/C,CAAC;EAED,IAAIE,QAAQA,CAAA,EAAY;IAEtB,OAAO,IAAI,CAACP,SAAS,CAACK,cAAc,KAAK,QAAQ;EACnD,CAAC;EAED,IAAIG,SAASA,CAAA,EAAY;IACvB,IAAIC,OAAO,EAAE;MAEX,OAAO,IAAI,CAACT,SAAS,CAACQ,SAAS;IACjC;IACA,OAAO,KAAK;EACd,CAAC;EAED,IAAIE,mBAAmBA,CAAA,EAAY;IAAA,IAAAC,qBAAA;IAEjC,QAAAA,qBAAA,GAAO,IAAI,CAACX,SAAS,CAACU,mBAAmB,YAAAC,qBAAA,GAAI,IAAI,CAACH,SAAS;EAC7D,CAAC;EAED,IAAII,aAAaA,CAAA,EAAY;IAAA,IAAAC,qBAAA;IAE3B,QAAAA,qBAAA,GAAO,IAAI,CAACb,SAAS,CAACY,aAAa,YAAAC,qBAAA,GAAI,KAAK;EAC9C,CAAC;EACDC,MAAM,EAAE,SAARA,MAAMA,CAAMC,IAA2B;IAAA,OAErC,KAAK,IAAIA,IAAI,GAAGA,IAAI,CAACC,GAAG,GAAG,QAAQ,IAAID,IAAI,GAAGA,IAAI,CAACE,MAAM,GAAGF,IAAI,CAACG,OAAO;EAAA;AAC5E,CAAC;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAF,OAAA,GAEatB,QAAQ", "ignoreList": []}