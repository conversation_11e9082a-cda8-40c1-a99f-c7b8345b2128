ec120b7ea010d083bb80f6ee1bafd7f9
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _readOnlyError2 = _interopRequireDefault(require("@babel/runtime/helpers/readOnlyError"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _get2 = _interopRequireDefault(require("@babel/runtime/helpers/get"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _AnimatedColor = _interopRequireDefault(require("../nodes/AnimatedColor"));
var SpringConfig = _interopRequireWildcard(require("../SpringConfig"));
var _Animation2 = _interopRequireDefault(require("./Animation"));
var _invariant = _interopRequireDefault(require("invariant"));
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && "function" == typeof p ? function (t) { return p.apply(e, t); } : p; }
var SpringAnimation = exports.default = function (_Animation) {
  function SpringAnimation(config) {
    var _config$overshootClam, _config$restDisplacem, _config$restSpeedThre, _config$velocity, _config$velocity2, _config$delay;
    var _this;
    (0, _classCallCheck2.default)(this, SpringAnimation);
    _this = _callSuper(this, SpringAnimation, [config]);
    _this._overshootClamping = (_config$overshootClam = config.overshootClamping) != null ? _config$overshootClam : false;
    _this._restDisplacementThreshold = (_config$restDisplacem = config.restDisplacementThreshold) != null ? _config$restDisplacem : 0.001;
    _this._restSpeedThreshold = (_config$restSpeedThre = config.restSpeedThreshold) != null ? _config$restSpeedThre : 0.001;
    _this._initialVelocity = (_config$velocity = config.velocity) != null ? _config$velocity : 0;
    _this._lastVelocity = (_config$velocity2 = config.velocity) != null ? _config$velocity2 : 0;
    _this._toValue = config.toValue;
    _this._delay = (_config$delay = config.delay) != null ? _config$delay : 0;
    _this._platformConfig = config.platformConfig;
    if (config.stiffness !== undefined || config.damping !== undefined || config.mass !== undefined) {
      var _config$stiffness, _config$damping, _config$mass;
      (0, _invariant.default)(config.bounciness === undefined && config.speed === undefined && config.tension === undefined && config.friction === undefined, 'You can define one of bounciness/speed, tension/friction, or stiffness/damping/mass, but not more than one');
      _this._stiffness = (_config$stiffness = config.stiffness) != null ? _config$stiffness : 100;
      _this._damping = (_config$damping = config.damping) != null ? _config$damping : 10;
      _this._mass = (_config$mass = config.mass) != null ? _config$mass : 1;
    } else if (config.bounciness !== undefined || config.speed !== undefined) {
      var _config$bounciness, _config$speed;
      (0, _invariant.default)(config.tension === undefined && config.friction === undefined && config.stiffness === undefined && config.damping === undefined && config.mass === undefined, 'You can define one of bounciness/speed, tension/friction, or stiffness/damping/mass, but not more than one');
      var springConfig = SpringConfig.fromBouncinessAndSpeed((_config$bounciness = config.bounciness) != null ? _config$bounciness : 8, (_config$speed = config.speed) != null ? _config$speed : 12);
      _this._stiffness = springConfig.stiffness;
      _this._damping = springConfig.damping;
      _this._mass = 1;
    } else {
      var _config$tension, _config$friction;
      var _springConfig = SpringConfig.fromOrigamiTensionAndFriction((_config$tension = config.tension) != null ? _config$tension : 40, (_config$friction = config.friction) != null ? _config$friction : 7);
      _this._stiffness = _springConfig.stiffness;
      _this._damping = _springConfig.damping;
      _this._mass = 1;
    }
    (0, _invariant.default)(_this._stiffness > 0, 'Stiffness value must be greater than 0');
    (0, _invariant.default)(_this._damping > 0, 'Damping value must be greater than 0');
    (0, _invariant.default)(_this._mass > 0, 'Mass value must be greater than 0');
    return _this;
  }
  (0, _inherits2.default)(SpringAnimation, _Animation);
  return (0, _createClass2.default)(SpringAnimation, [{
    key: "__getNativeAnimationConfig",
    value: function __getNativeAnimationConfig() {
      var _this$_initialVelocit;
      return {
        type: 'spring',
        overshootClamping: this._overshootClamping,
        restDisplacementThreshold: this._restDisplacementThreshold,
        restSpeedThreshold: this._restSpeedThreshold,
        stiffness: this._stiffness,
        damping: this._damping,
        mass: this._mass,
        initialVelocity: (_this$_initialVelocit = this._initialVelocity) != null ? _this$_initialVelocit : this._lastVelocity,
        toValue: this._toValue,
        iterations: this.__iterations,
        platformConfig: this._platformConfig,
        debugID: this.__getDebugID()
      };
    }
  }, {
    key: "start",
    value: function start(fromValue, onUpdate, onEnd, previousAnimation, animatedValue) {
      var _this2 = this;
      _superPropGet(SpringAnimation, "start", this, 3)([fromValue, onUpdate, onEnd, previousAnimation, animatedValue]);
      this._startPosition = fromValue;
      this._lastPosition = this._startPosition;
      this._onUpdate = onUpdate;
      this._lastTime = Date.now();
      this._frameTime = 0.0;
      if (previousAnimation instanceof SpringAnimation) {
        var internalState = previousAnimation.getInternalState();
        this._lastPosition = internalState.lastPosition;
        this._lastVelocity = internalState.lastVelocity;
        this._initialVelocity = this._lastVelocity;
        this._lastTime = internalState.lastTime;
      }
      var start = function start() {
        var useNativeDriver = _this2.__startAnimationIfNative(animatedValue);
        if (!useNativeDriver) {
          _this2.onUpdate();
        }
      };
      if (this._delay) {
        this._timeout = setTimeout(start, this._delay);
      } else {
        start();
      }
    }
  }, {
    key: "getInternalState",
    value: function getInternalState() {
      return {
        lastPosition: this._lastPosition,
        lastVelocity: this._lastVelocity,
        lastTime: this._lastTime
      };
    }
  }, {
    key: "onUpdate",
    value: function onUpdate() {
      var MAX_STEPS = 64;
      var now = Date.now();
      if (now > this._lastTime + MAX_STEPS) {
        now = this._lastTime + MAX_STEPS;
      }
      var deltaTime = (now - this._lastTime) / 1000;
      this._frameTime += deltaTime;
      var c = this._damping;
      var m = this._mass;
      var k = this._stiffness;
      var v0 = -this._initialVelocity;
      var zeta = c / (2 * Math.sqrt(k * m));
      var omega0 = Math.sqrt(k / m);
      var omega1 = omega0 * Math.sqrt(1.0 - zeta * zeta);
      var x0 = this._toValue - this._startPosition;
      var position = 0.0;
      var velocity = 0.0;
      var t = this._frameTime;
      if (zeta < 1) {
        var envelope = Math.exp(-zeta * omega0 * t);
        position = this._toValue - envelope * ((v0 + zeta * omega0 * x0) / omega1 * Math.sin(omega1 * t) + x0 * Math.cos(omega1 * t));
        velocity = zeta * omega0 * envelope * (Math.sin(omega1 * t) * (v0 + zeta * omega0 * x0) / omega1 + x0 * Math.cos(omega1 * t)) - envelope * (Math.cos(omega1 * t) * (v0 + zeta * omega0 * x0) - omega1 * x0 * Math.sin(omega1 * t));
      } else {
        var _envelope = Math.exp(-omega0 * t);
        position = this._toValue - _envelope * (x0 + (v0 + omega0 * x0) * t);
        velocity = _envelope * (v0 * (t * omega0 - 1) + t * x0 * (omega0 * omega0));
      }
      this._lastTime = now;
      this._lastPosition = position;
      this._lastVelocity = velocity;
      this._onUpdate(position);
      if (!this.__active) {
        return;
      }
      var isOvershooting = false;
      if (this._overshootClamping && this._stiffness !== 0) {
        if (this._startPosition < this._toValue) {
          isOvershooting = position > this._toValue;
        } else {
          isOvershooting = position < this._toValue;
        }
      }
      var isVelocity = Math.abs(velocity) <= this._restSpeedThreshold;
      var isDisplacement = true;
      if (this._stiffness !== 0) {
        isDisplacement = Math.abs(this._toValue - position) <= this._restDisplacementThreshold;
      }
      if (isOvershooting || isVelocity && isDisplacement) {
        if (this._stiffness !== 0) {
          this._lastPosition = this._toValue;
          this._lastVelocity = 0;
          this._onUpdate(this._toValue);
        }
        this.__notifyAnimationEnd({
          finished: true
        });
        return;
      }
      this._animationFrame = requestAnimationFrame(this.onUpdate.bind(this));
    }
  }, {
    key: "stop",
    value: function stop() {
      _superPropGet(SpringAnimation, "stop", this, 3)([]);
      clearTimeout(this._timeout);
      if (this._animationFrame != null) {
        global.cancelAnimationFrame(this._animationFrame);
      }
      this.__notifyAnimationEnd({
        finished: false
      });
    }
  }]);
}(_Animation2.default);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfQW5pbWF0ZWRDb2xvciIsIl9pbnRlcm9wUmVxdWlyZURlZmF1bHQiLCJyZXF1aXJlIiwiU3ByaW5nQ29uZmlnIiwiX2ludGVyb3BSZXF1aXJlV2lsZGNhcmQiLCJfQW5pbWF0aW9uMiIsIl9pbnZhcmlhbnQiLCJlIiwidCIsIldlYWtNYXAiLCJyIiwibiIsIl9fZXNNb2R1bGUiLCJvIiwiaSIsImYiLCJfX3Byb3RvX18iLCJkZWZhdWx0IiwiaGFzIiwiZ2V0Iiwic2V0IiwiX3QiLCJoYXNPd25Qcm9wZXJ0eSIsImNhbGwiLCJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImdldE93blByb3BlcnR5RGVzY3JpcHRvciIsIl9jYWxsU3VwZXIiLCJfZ2V0UHJvdG90eXBlT2YyIiwiX3Bvc3NpYmxlQ29uc3RydWN0b3JSZXR1cm4yIiwiX2lzTmF0aXZlUmVmbGVjdENvbnN0cnVjdCIsIlJlZmxlY3QiLCJjb25zdHJ1Y3QiLCJjb25zdHJ1Y3RvciIsImFwcGx5IiwiQm9vbGVhbiIsInByb3RvdHlwZSIsInZhbHVlT2YiLCJfc3VwZXJQcm9wR2V0IiwicCIsIl9nZXQyIiwiU3ByaW5nQW5pbWF0aW9uIiwiZXhwb3J0cyIsIl9BbmltYXRpb24iLCJjb25maWciLCJfY29uZmlnJG92ZXJzaG9vdENsYW0iLCJfY29uZmlnJHJlc3REaXNwbGFjZW0iLCJfY29uZmlnJHJlc3RTcGVlZFRocmUiLCJfY29uZmlnJHZlbG9jaXR5IiwiX2NvbmZpZyR2ZWxvY2l0eTIiLCJfY29uZmlnJGRlbGF5IiwiX3RoaXMiLCJfY2xhc3NDYWxsQ2hlY2syIiwiX292ZXJzaG9vdENsYW1waW5nIiwib3ZlcnNob290Q2xhbXBpbmciLCJfcmVzdERpc3BsYWNlbWVudFRocmVzaG9sZCIsInJlc3REaXNwbGFjZW1lbnRUaHJlc2hvbGQiLCJfcmVzdFNwZWVkVGhyZXNob2xkIiwicmVzdFNwZWVkVGhyZXNob2xkIiwiX2luaXRpYWxWZWxvY2l0eSIsInZlbG9jaXR5IiwiX2xhc3RWZWxvY2l0eSIsIl90b1ZhbHVlIiwidG9WYWx1ZSIsIl9kZWxheSIsImRlbGF5IiwiX3BsYXRmb3JtQ29uZmlnIiwicGxhdGZvcm1Db25maWciLCJzdGlmZm5lc3MiLCJ1bmRlZmluZWQiLCJkYW1waW5nIiwibWFzcyIsIl9jb25maWckc3RpZmZuZXNzIiwiX2NvbmZpZyRkYW1waW5nIiwiX2NvbmZpZyRtYXNzIiwiaW52YXJpYW50IiwiYm91bmNpbmVzcyIsInNwZWVkIiwidGVuc2lvbiIsImZyaWN0aW9uIiwiX3N0aWZmbmVzcyIsIl9kYW1waW5nIiwiX21hc3MiLCJfY29uZmlnJGJvdW5jaW5lc3MiLCJfY29uZmlnJHNwZWVkIiwic3ByaW5nQ29uZmlnIiwiZnJvbUJvdW5jaW5lc3NBbmRTcGVlZCIsIl9jb25maWckdGVuc2lvbiIsIl9jb25maWckZnJpY3Rpb24iLCJmcm9tT3JpZ2FtaVRlbnNpb25BbmRGcmljdGlvbiIsIl9pbmhlcml0czIiLCJfY3JlYXRlQ2xhc3MyIiwia2V5IiwidmFsdWUiLCJfX2dldE5hdGl2ZUFuaW1hdGlvbkNvbmZpZyIsIl90aGlzJF9pbml0aWFsVmVsb2NpdCIsInR5cGUiLCJpbml0aWFsVmVsb2NpdHkiLCJpdGVyYXRpb25zIiwiX19pdGVyYXRpb25zIiwiZGVidWdJRCIsIl9fZ2V0RGVidWdJRCIsInN0YXJ0IiwiZnJvbVZhbHVlIiwib25VcGRhdGUiLCJvbkVuZCIsInByZXZpb3VzQW5pbWF0aW9uIiwiYW5pbWF0ZWRWYWx1ZSIsIl90aGlzMiIsIl9zdGFydFBvc2l0aW9uIiwiX2xhc3RQb3NpdGlvbiIsIl9vblVwZGF0ZSIsIl9sYXN0VGltZSIsIkRhdGUiLCJub3ciLCJfZnJhbWVUaW1lIiwiaW50ZXJuYWxTdGF0ZSIsImdldEludGVybmFsU3RhdGUiLCJsYXN0UG9zaXRpb24iLCJsYXN0VmVsb2NpdHkiLCJsYXN0VGltZSIsInVzZU5hdGl2ZURyaXZlciIsIl9fc3RhcnRBbmltYXRpb25JZk5hdGl2ZSIsIl90aW1lb3V0Iiwic2V0VGltZW91dCIsIk1BWF9TVEVQUyIsImRlbHRhVGltZSIsImMiLCJtIiwiayIsInYwIiwiemV0YSIsIk1hdGgiLCJzcXJ0Iiwib21lZ2EwIiwib21lZ2ExIiwieDAiLCJwb3NpdGlvbiIsImVudmVsb3BlIiwiZXhwIiwic2luIiwiY29zIiwiX19hY3RpdmUiLCJpc092ZXJzaG9vdGluZyIsImlzVmVsb2NpdHkiLCJhYnMiLCJpc0Rpc3BsYWNlbWVudCIsIl9fbm90aWZ5QW5pbWF0aW9uRW5kIiwiZmluaXNoZWQiLCJfYW5pbWF0aW9uRnJhbWUiLCJyZXF1ZXN0QW5pbWF0aW9uRnJhbWUiLCJiaW5kIiwic3RvcCIsImNsZWFyVGltZW91dCIsImdsb2JhbCIsImNhbmNlbEFuaW1hdGlvbkZyYW1lIiwiQW5pbWF0aW9uIl0sInNvdXJjZXMiOlsiU3ByaW5nQW5pbWF0aW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQ29weXJpZ2h0IChjKSBNZXRhIFBsYXRmb3JtcywgSW5jLiBhbmQgYWZmaWxpYXRlcy5cbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZSBmb3VuZCBpbiB0aGVcbiAqIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqXG4gKiBAZmxvdyBzdHJpY3QtbG9jYWxcbiAqIEBmb3JtYXRcbiAqL1xuXG5pbXBvcnQgdHlwZSB7UGxhdGZvcm1Db25maWd9IGZyb20gJy4uL0FuaW1hdGVkUGxhdGZvcm1Db25maWcnO1xuaW1wb3J0IHR5cGUgQW5pbWF0ZWRJbnRlcnBvbGF0aW9uIGZyb20gJy4uL25vZGVzL0FuaW1hdGVkSW50ZXJwb2xhdGlvbic7XG5pbXBvcnQgdHlwZSBBbmltYXRlZFZhbHVlIGZyb20gJy4uL25vZGVzL0FuaW1hdGVkVmFsdWUnO1xuaW1wb3J0IHR5cGUgQW5pbWF0ZWRWYWx1ZVhZIGZyb20gJy4uL25vZGVzL0FuaW1hdGVkVmFsdWVYWSc7XG5pbXBvcnQgdHlwZSB7QW5pbWF0aW9uQ29uZmlnLCBFbmRDYWxsYmFja30gZnJvbSAnLi9BbmltYXRpb24nO1xuXG5pbXBvcnQgQW5pbWF0ZWRDb2xvciBmcm9tICcuLi9ub2Rlcy9BbmltYXRlZENvbG9yJztcbmltcG9ydCAqIGFzIFNwcmluZ0NvbmZpZyBmcm9tICcuLi9TcHJpbmdDb25maWcnO1xuaW1wb3J0IEFuaW1hdGlvbiBmcm9tICcuL0FuaW1hdGlvbic7XG5pbXBvcnQgaW52YXJpYW50IGZyb20gJ2ludmFyaWFudCc7XG5cbmV4cG9ydCB0eXBlIFNwcmluZ0FuaW1hdGlvbkNvbmZpZyA9ICRSZWFkT25seTx7XG4gIC4uLkFuaW1hdGlvbkNvbmZpZyxcbiAgdG9WYWx1ZTpcbiAgICB8IG51bWJlclxuICAgIHwgQW5pbWF0ZWRWYWx1ZVxuICAgIHwge1xuICAgICAgICB4OiBudW1iZXIsXG4gICAgICAgIHk6IG51bWJlcixcbiAgICAgICAgLi4uXG4gICAgICB9XG4gICAgfCBBbmltYXRlZFZhbHVlWFlcbiAgICB8IHtcbiAgICAgICAgcjogbnVtYmVyLFxuICAgICAgICBnOiBudW1iZXIsXG4gICAgICAgIGI6IG51bWJlcixcbiAgICAgICAgYTogbnVtYmVyLFxuICAgICAgICAuLi5cbiAgICAgIH1cbiAgICB8IEFuaW1hdGVkQ29sb3JcbiAgICB8IEFuaW1hdGVkSW50ZXJwb2xhdGlvbjxudW1iZXI+LFxuICBvdmVyc2hvb3RDbGFtcGluZz86IGJvb2xlYW4sXG4gIHJlc3REaXNwbGFjZW1lbnRUaHJlc2hvbGQ/OiBudW1iZXIsXG4gIHJlc3RTcGVlZFRocmVzaG9sZD86IG51bWJlcixcbiAgdmVsb2NpdHk/OlxuICAgIHwgbnVtYmVyXG4gICAgfCAkUmVhZE9ubHk8e1xuICAgICAgICB4OiBudW1iZXIsXG4gICAgICAgIHk6IG51bWJlcixcbiAgICAgICAgLi4uXG4gICAgICB9PixcbiAgYm91bmNpbmVzcz86IG51bWJlcixcbiAgc3BlZWQ/OiBudW1iZXIsXG4gIHRlbnNpb24/OiBudW1iZXIsXG4gIGZyaWN0aW9uPzogbnVtYmVyLFxuICBzdGlmZm5lc3M/OiBudW1iZXIsXG4gIGRhbXBpbmc/OiBudW1iZXIsXG4gIG1hc3M/OiBudW1iZXIsXG4gIGRlbGF5PzogbnVtYmVyLFxuICAuLi5cbn0+O1xuXG5leHBvcnQgdHlwZSBTcHJpbmdBbmltYXRpb25Db25maWdTaW5nbGUgPSAkUmVhZE9ubHk8e1xuICAuLi5BbmltYXRpb25Db25maWcsXG4gIHRvVmFsdWU6IG51bWJlcixcbiAgb3ZlcnNob290Q2xhbXBpbmc/OiBib29sZWFuLFxuICByZXN0RGlzcGxhY2VtZW50VGhyZXNob2xkPzogbnVtYmVyLFxuICByZXN0U3BlZWRUaHJlc2hvbGQ/OiBudW1iZXIsXG4gIHZlbG9jaXR5PzogbnVtYmVyLFxuICBib3VuY2luZXNzPzogbnVtYmVyLFxuICBzcGVlZD86IG51bWJlcixcbiAgdGVuc2lvbj86IG51bWJlcixcbiAgZnJpY3Rpb24/OiBudW1iZXIsXG4gIHN0aWZmbmVzcz86IG51bWJlcixcbiAgZGFtcGluZz86IG51bWJlcixcbiAgbWFzcz86IG51bWJlcixcbiAgZGVsYXk/OiBudW1iZXIsXG4gIC4uLlxufT47XG5cbm9wYXF1ZSB0eXBlIFNwcmluZ0FuaW1hdGlvbkludGVybmFsU3RhdGUgPSAkUmVhZE9ubHk8e1xuICBsYXN0UG9zaXRpb246IG51bWJlcixcbiAgbGFzdFZlbG9jaXR5OiBudW1iZXIsXG4gIGxhc3RUaW1lOiBudW1iZXIsXG59PjtcblxuZXhwb3J0IGRlZmF1bHQgY2xhc3MgU3ByaW5nQW5pbWF0aW9uIGV4dGVuZHMgQW5pbWF0aW9uIHtcbiAgX292ZXJzaG9vdENsYW1waW5nOiBib29sZWFuO1xuICBfcmVzdERpc3BsYWNlbWVudFRocmVzaG9sZDogbnVtYmVyO1xuICBfcmVzdFNwZWVkVGhyZXNob2xkOiBudW1iZXI7XG4gIF9sYXN0VmVsb2NpdHk6IG51bWJlcjtcbiAgX3N0YXJ0UG9zaXRpb246IG51bWJlcjtcbiAgX2xhc3RQb3NpdGlvbjogbnVtYmVyO1xuICBfZnJvbVZhbHVlOiBudW1iZXI7XG4gIF90b1ZhbHVlOiBudW1iZXI7XG4gIF9zdGlmZm5lc3M6IG51bWJlcjtcbiAgX2RhbXBpbmc6IG51bWJlcjtcbiAgX21hc3M6IG51bWJlcjtcbiAgX2luaXRpYWxWZWxvY2l0eTogbnVtYmVyO1xuICBfZGVsYXk6IG51bWJlcjtcbiAgX3RpbWVvdXQ6ID9UaW1lb3V0SUQ7XG4gIF9zdGFydFRpbWU6IG51bWJlcjtcbiAgX2xhc3RUaW1lOiBudW1iZXI7XG4gIF9mcmFtZVRpbWU6IG51bWJlcjtcbiAgX29uVXBkYXRlOiAodmFsdWU6IG51bWJlcikgPT4gdm9pZDtcbiAgX2FuaW1hdGlvbkZyYW1lOiA/QW5pbWF0aW9uRnJhbWVJRDtcbiAgX3BsYXRmb3JtQ29uZmlnOiA/UGxhdGZvcm1Db25maWc7XG5cbiAgY29uc3RydWN0b3IoY29uZmlnOiBTcHJpbmdBbmltYXRpb25Db25maWdTaW5nbGUpIHtcbiAgICBzdXBlcihjb25maWcpO1xuXG4gICAgdGhpcy5fb3ZlcnNob290Q2xhbXBpbmcgPSBjb25maWcub3ZlcnNob290Q2xhbXBpbmcgPz8gZmFsc2U7XG4gICAgdGhpcy5fcmVzdERpc3BsYWNlbWVudFRocmVzaG9sZCA9IGNvbmZpZy5yZXN0RGlzcGxhY2VtZW50VGhyZXNob2xkID8/IDAuMDAxO1xuICAgIHRoaXMuX3Jlc3RTcGVlZFRocmVzaG9sZCA9IGNvbmZpZy5yZXN0U3BlZWRUaHJlc2hvbGQgPz8gMC4wMDE7XG4gICAgdGhpcy5faW5pdGlhbFZlbG9jaXR5ID0gY29uZmlnLnZlbG9jaXR5ID8/IDA7XG4gICAgdGhpcy5fbGFzdFZlbG9jaXR5ID0gY29uZmlnLnZlbG9jaXR5ID8/IDA7XG4gICAgdGhpcy5fdG9WYWx1ZSA9IGNvbmZpZy50b1ZhbHVlO1xuICAgIHRoaXMuX2RlbGF5ID0gY29uZmlnLmRlbGF5ID8/IDA7XG4gICAgdGhpcy5fcGxhdGZvcm1Db25maWcgPSBjb25maWcucGxhdGZvcm1Db25maWc7XG5cbiAgICBpZiAoXG4gICAgICBjb25maWcuc3RpZmZuZXNzICE9PSB1bmRlZmluZWQgfHxcbiAgICAgIGNvbmZpZy5kYW1waW5nICE9PSB1bmRlZmluZWQgfHxcbiAgICAgIGNvbmZpZy5tYXNzICE9PSB1bmRlZmluZWRcbiAgICApIHtcbiAgICAgIGludmFyaWFudChcbiAgICAgICAgY29uZmlnLmJvdW5jaW5lc3MgPT09IHVuZGVmaW5lZCAmJlxuICAgICAgICAgIGNvbmZpZy5zcGVlZCA9PT0gdW5kZWZpbmVkICYmXG4gICAgICAgICAgY29uZmlnLnRlbnNpb24gPT09IHVuZGVmaW5lZCAmJlxuICAgICAgICAgIGNvbmZpZy5mcmljdGlvbiA9PT0gdW5kZWZpbmVkLFxuICAgICAgICAnWW91IGNhbiBkZWZpbmUgb25lIG9mIGJvdW5jaW5lc3Mvc3BlZWQsIHRlbnNpb24vZnJpY3Rpb24sIG9yIHN0aWZmbmVzcy9kYW1waW5nL21hc3MsIGJ1dCBub3QgbW9yZSB0aGFuIG9uZScsXG4gICAgICApO1xuICAgICAgdGhpcy5fc3RpZmZuZXNzID0gY29uZmlnLnN0aWZmbmVzcyA/PyAxMDA7XG4gICAgICB0aGlzLl9kYW1waW5nID0gY29uZmlnLmRhbXBpbmcgPz8gMTA7XG4gICAgICB0aGlzLl9tYXNzID0gY29uZmlnLm1hc3MgPz8gMTtcbiAgICB9IGVsc2UgaWYgKGNvbmZpZy5ib3VuY2luZXNzICE9PSB1bmRlZmluZWQgfHwgY29uZmlnLnNwZWVkICE9PSB1bmRlZmluZWQpIHtcbiAgICAgIC8vIENvbnZlcnQgdGhlIG9yaWdhbWkgYm91bmNpbmVzcy9zcGVlZCB2YWx1ZXMgdG8gc3RpZmZuZXNzL2RhbXBpbmdcbiAgICAgIC8vIFdlIGFzc3VtZSBtYXNzIGlzIDEuXG4gICAgICBpbnZhcmlhbnQoXG4gICAgICAgIGNvbmZpZy50ZW5zaW9uID09PSB1bmRlZmluZWQgJiZcbiAgICAgICAgICBjb25maWcuZnJpY3Rpb24gPT09IHVuZGVmaW5lZCAmJlxuICAgICAgICAgIGNvbmZpZy5zdGlmZm5lc3MgPT09IHVuZGVmaW5lZCAmJlxuICAgICAgICAgIGNvbmZpZy5kYW1waW5nID09PSB1bmRlZmluZWQgJiZcbiAgICAgICAgICBjb25maWcubWFzcyA9PT0gdW5kZWZpbmVkLFxuICAgICAgICAnWW91IGNhbiBkZWZpbmUgb25lIG9mIGJvdW5jaW5lc3Mvc3BlZWQsIHRlbnNpb24vZnJpY3Rpb24sIG9yIHN0aWZmbmVzcy9kYW1waW5nL21hc3MsIGJ1dCBub3QgbW9yZSB0aGFuIG9uZScsXG4gICAgICApO1xuICAgICAgY29uc3Qgc3ByaW5nQ29uZmlnID0gU3ByaW5nQ29uZmlnLmZyb21Cb3VuY2luZXNzQW5kU3BlZWQoXG4gICAgICAgIGNvbmZpZy5ib3VuY2luZXNzID8/IDgsXG4gICAgICAgIGNvbmZpZy5zcGVlZCA/PyAxMixcbiAgICAgICk7XG4gICAgICB0aGlzLl9zdGlmZm5lc3MgPSBzcHJpbmdDb25maWcuc3RpZmZuZXNzO1xuICAgICAgdGhpcy5fZGFtcGluZyA9IHNwcmluZ0NvbmZpZy5kYW1waW5nO1xuICAgICAgdGhpcy5fbWFzcyA9IDE7XG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIENvbnZlcnQgdGhlIG9yaWdhbWkgdGVuc2lvbi9mcmljdGlvbiB2YWx1ZXMgdG8gc3RpZmZuZXNzL2RhbXBpbmdcbiAgICAgIC8vIFdlIGFzc3VtZSBtYXNzIGlzIDEuXG4gICAgICBjb25zdCBzcHJpbmdDb25maWcgPSBTcHJpbmdDb25maWcuZnJvbU9yaWdhbWlUZW5zaW9uQW5kRnJpY3Rpb24oXG4gICAgICAgIGNvbmZpZy50ZW5zaW9uID8/IDQwLFxuICAgICAgICBjb25maWcuZnJpY3Rpb24gPz8gNyxcbiAgICAgICk7XG4gICAgICB0aGlzLl9zdGlmZm5lc3MgPSBzcHJpbmdDb25maWcuc3RpZmZuZXNzO1xuICAgICAgdGhpcy5fZGFtcGluZyA9IHNwcmluZ0NvbmZpZy5kYW1waW5nO1xuICAgICAgdGhpcy5fbWFzcyA9IDE7XG4gICAgfVxuXG4gICAgaW52YXJpYW50KHRoaXMuX3N0aWZmbmVzcyA+IDAsICdTdGlmZm5lc3MgdmFsdWUgbXVzdCBiZSBncmVhdGVyIHRoYW4gMCcpO1xuICAgIGludmFyaWFudCh0aGlzLl9kYW1waW5nID4gMCwgJ0RhbXBpbmcgdmFsdWUgbXVzdCBiZSBncmVhdGVyIHRoYW4gMCcpO1xuICAgIGludmFyaWFudCh0aGlzLl9tYXNzID4gMCwgJ01hc3MgdmFsdWUgbXVzdCBiZSBncmVhdGVyIHRoYW4gMCcpO1xuICB9XG5cbiAgX19nZXROYXRpdmVBbmltYXRpb25Db25maWcoKTogJFJlYWRPbmx5PHtcbiAgICBkYW1waW5nOiBudW1iZXIsXG4gICAgaW5pdGlhbFZlbG9jaXR5OiBudW1iZXIsXG4gICAgaXRlcmF0aW9uczogbnVtYmVyLFxuICAgIG1hc3M6IG51bWJlcixcbiAgICBwbGF0Zm9ybUNvbmZpZzogP1BsYXRmb3JtQ29uZmlnLFxuICAgIG92ZXJzaG9vdENsYW1waW5nOiBib29sZWFuLFxuICAgIHJlc3REaXNwbGFjZW1lbnRUaHJlc2hvbGQ6IG51bWJlcixcbiAgICByZXN0U3BlZWRUaHJlc2hvbGQ6IG51bWJlcixcbiAgICBzdGlmZm5lc3M6IG51bWJlcixcbiAgICB0b1ZhbHVlOiBudW1iZXIsXG4gICAgdHlwZTogJ3NwcmluZycsXG4gICAgLi4uXG4gIH0+IHtcbiAgICByZXR1cm4ge1xuICAgICAgdHlwZTogJ3NwcmluZycsXG4gICAgICBvdmVyc2hvb3RDbGFtcGluZzogdGhpcy5fb3ZlcnNob290Q2xhbXBpbmcsXG4gICAgICByZXN0RGlzcGxhY2VtZW50VGhyZXNob2xkOiB0aGlzLl9yZXN0RGlzcGxhY2VtZW50VGhyZXNob2xkLFxuICAgICAgcmVzdFNwZWVkVGhyZXNob2xkOiB0aGlzLl9yZXN0U3BlZWRUaHJlc2hvbGQsXG4gICAgICBzdGlmZm5lc3M6IHRoaXMuX3N0aWZmbmVzcyxcbiAgICAgIGRhbXBpbmc6IHRoaXMuX2RhbXBpbmcsXG4gICAgICBtYXNzOiB0aGlzLl9tYXNzLFxuICAgICAgaW5pdGlhbFZlbG9jaXR5OiB0aGlzLl9pbml0aWFsVmVsb2NpdHkgPz8gdGhpcy5fbGFzdFZlbG9jaXR5LFxuICAgICAgdG9WYWx1ZTogdGhpcy5fdG9WYWx1ZSxcbiAgICAgIGl0ZXJhdGlvbnM6IHRoaXMuX19pdGVyYXRpb25zLFxuICAgICAgcGxhdGZvcm1Db25maWc6IHRoaXMuX3BsYXRmb3JtQ29uZmlnLFxuICAgICAgZGVidWdJRDogdGhpcy5fX2dldERlYnVnSUQoKSxcbiAgICB9O1xuICB9XG5cbiAgc3RhcnQoXG4gICAgZnJvbVZhbHVlOiBudW1iZXIsXG4gICAgb25VcGRhdGU6ICh2YWx1ZTogbnVtYmVyKSA9PiB2b2lkLFxuICAgIG9uRW5kOiA/RW5kQ2FsbGJhY2ssXG4gICAgcHJldmlvdXNBbmltYXRpb246ID9BbmltYXRpb24sXG4gICAgYW5pbWF0ZWRWYWx1ZTogQW5pbWF0ZWRWYWx1ZSxcbiAgKTogdm9pZCB7XG4gICAgc3VwZXIuc3RhcnQoZnJvbVZhbHVlLCBvblVwZGF0ZSwgb25FbmQsIHByZXZpb3VzQW5pbWF0aW9uLCBhbmltYXRlZFZhbHVlKTtcblxuICAgIHRoaXMuX3N0YXJ0UG9zaXRpb24gPSBmcm9tVmFsdWU7XG4gICAgdGhpcy5fbGFzdFBvc2l0aW9uID0gdGhpcy5fc3RhcnRQb3NpdGlvbjtcblxuICAgIHRoaXMuX29uVXBkYXRlID0gb25VcGRhdGU7XG4gICAgdGhpcy5fbGFzdFRpbWUgPSBEYXRlLm5vdygpO1xuICAgIHRoaXMuX2ZyYW1lVGltZSA9IDAuMDtcblxuICAgIGlmIChwcmV2aW91c0FuaW1hdGlvbiBpbnN0YW5jZW9mIFNwcmluZ0FuaW1hdGlvbikge1xuICAgICAgY29uc3QgaW50ZXJuYWxTdGF0ZSA9IHByZXZpb3VzQW5pbWF0aW9uLmdldEludGVybmFsU3RhdGUoKTtcbiAgICAgIHRoaXMuX2xhc3RQb3NpdGlvbiA9IGludGVybmFsU3RhdGUubGFzdFBvc2l0aW9uO1xuICAgICAgdGhpcy5fbGFzdFZlbG9jaXR5ID0gaW50ZXJuYWxTdGF0ZS5sYXN0VmVsb2NpdHk7XG4gICAgICAvLyBTZXQgdGhlIGluaXRpYWwgdmVsb2NpdHkgdG8gdGhlIGxhc3QgdmVsb2NpdHlcbiAgICAgIHRoaXMuX2luaXRpYWxWZWxvY2l0eSA9IHRoaXMuX2xhc3RWZWxvY2l0eTtcbiAgICAgIHRoaXMuX2xhc3RUaW1lID0gaW50ZXJuYWxTdGF0ZS5sYXN0VGltZTtcbiAgICB9XG5cbiAgICBjb25zdCBzdGFydCA9ICgpID0+IHtcbiAgICAgIGNvbnN0IHVzZU5hdGl2ZURyaXZlciA9IHRoaXMuX19zdGFydEFuaW1hdGlvbklmTmF0aXZlKGFuaW1hdGVkVmFsdWUpO1xuICAgICAgaWYgKCF1c2VOYXRpdmVEcml2ZXIpIHtcbiAgICAgICAgdGhpcy5vblVwZGF0ZSgpO1xuICAgICAgfVxuICAgIH07XG5cbiAgICAvLyAgSWYgdGhpcy5fZGVsYXkgaXMgbW9yZSB0aGFuIDAsIHdlIHN0YXJ0IGFmdGVyIHRoZSB0aW1lb3V0LlxuICAgIGlmICh0aGlzLl9kZWxheSkge1xuICAgICAgdGhpcy5fdGltZW91dCA9IHNldFRpbWVvdXQoc3RhcnQsIHRoaXMuX2RlbGF5KTtcbiAgICB9IGVsc2Uge1xuICAgICAgc3RhcnQoKTtcbiAgICB9XG4gIH1cblxuICBnZXRJbnRlcm5hbFN0YXRlKCk6IFNwcmluZ0FuaW1hdGlvbkludGVybmFsU3RhdGUge1xuICAgIHJldHVybiB7XG4gICAgICBsYXN0UG9zaXRpb246IHRoaXMuX2xhc3RQb3NpdGlvbixcbiAgICAgIGxhc3RWZWxvY2l0eTogdGhpcy5fbGFzdFZlbG9jaXR5LFxuICAgICAgbGFzdFRpbWU6IHRoaXMuX2xhc3RUaW1lLFxuICAgIH07XG4gIH1cblxuICAvKipcbiAgICogVGhpcyBzcHJpbmcgbW9kZWwgaXMgYmFzZWQgb2ZmIG9mIGEgZGFtcGVkIGhhcm1vbmljIG9zY2lsbGF0b3JcbiAgICogKGh0dHBzOi8vZW4ud2lraXBlZGlhLm9yZy93aWtpL0hhcm1vbmljX29zY2lsbGF0b3IjRGFtcGVkX2hhcm1vbmljX29zY2lsbGF0b3IpLlxuICAgKlxuICAgKiBXZSB1c2UgdGhlIGNsb3NlZCBmb3JtIG9mIHRoZSBzZWNvbmQgb3JkZXIgZGlmZmVyZW50aWFsIGVxdWF0aW9uOlxuICAgKlxuICAgKiB4JycgKyAoMs624o21XzApeCcgKyDijbVeMnggPSAwXG4gICAqXG4gICAqIHdoZXJlXG4gICAqICAgIOKNtV8wID0g4oiaKGsgLyBtKSAodW5kYW1wZWQgYW5ndWxhciBmcmVxdWVuY3kgb2YgdGhlIG9zY2lsbGF0b3IpLFxuICAgKiAgICDOtiA9IGMgLyAy4oiabWsgKGRhbXBpbmcgcmF0aW8pLFxuICAgKiAgICBjID0gZGFtcGluZyBjb25zdGFudFxuICAgKiAgICBrID0gc3RpZmZuZXNzXG4gICAqICAgIG0gPSBtYXNzXG4gICAqXG4gICAqIFRoZSBkZXJpdmF0aW9uIG9mIHRoZSBjbG9zZWQgZm9ybSBpcyBkZXNjcmliZWQgaW4gZGV0YWlsIGhlcmU6XG4gICAqIGh0dHA6Ly9wbGFuZXRtYXRoLm9yZy9zaXRlcy9kZWZhdWx0L2ZpbGVzL3RleHBkZi8zOTc0NS5wZGZcbiAgICpcbiAgICogVGhpcyBhbGdvcml0aG0gaGFwcGVucyB0byBtYXRjaCB0aGUgYWxnb3JpdGhtIHVzZWQgYnkgQ0FTcHJpbmdBbmltYXRpb24sXG4gICAqIGEgUXVhcnR6Q29yZSAoaU9TKSBBUEkgdGhhdCBjcmVhdGVzIHNwcmluZyBhbmltYXRpb25zLlxuICAgKi9cbiAgb25VcGRhdGUoKTogdm9pZCB7XG4gICAgLy8gSWYgZm9yIHNvbWUgcmVhc29uIHdlIGxvc3QgYSBsb3Qgb2YgZnJhbWVzIChlLmcuIHByb2Nlc3MgbGFyZ2UgcGF5bG9hZCBvclxuICAgIC8vIHN0b3BwZWQgaW4gdGhlIGRlYnVnZ2VyKSwgd2Ugb25seSBhZHZhbmNlIGJ5IDQgZnJhbWVzIHdvcnRoIG9mXG4gICAgLy8gY29tcHV0YXRpb24gYW5kIHdpbGwgY29udGludWUgb24gdGhlIG5leHQgZnJhbWUuIEl0J3MgYmV0dGVyIHRvIGhhdmUgaXRcbiAgICAvLyBydW5uaW5nIGF0IGZhc3RlciBzcGVlZCB0aGFuIGp1bXBpbmcgdG8gdGhlIGVuZC5cbiAgICBjb25zdCBNQVhfU1RFUFMgPSA2NDtcbiAgICBsZXQgbm93ID0gRGF0ZS5ub3coKTtcbiAgICBpZiAobm93ID4gdGhpcy5fbGFzdFRpbWUgKyBNQVhfU1RFUFMpIHtcbiAgICAgIG5vdyA9IHRoaXMuX2xhc3RUaW1lICsgTUFYX1NURVBTO1xuICAgIH1cblxuICAgIGNvbnN0IGRlbHRhVGltZSA9IChub3cgLSB0aGlzLl9sYXN0VGltZSkgLyAxMDAwO1xuICAgIHRoaXMuX2ZyYW1lVGltZSArPSBkZWx0YVRpbWU7XG5cbiAgICBjb25zdCBjOiBudW1iZXIgPSB0aGlzLl9kYW1waW5nO1xuICAgIGNvbnN0IG06IG51bWJlciA9IHRoaXMuX21hc3M7XG4gICAgY29uc3QgazogbnVtYmVyID0gdGhpcy5fc3RpZmZuZXNzO1xuICAgIGNvbnN0IHYwOiBudW1iZXIgPSAtdGhpcy5faW5pdGlhbFZlbG9jaXR5O1xuXG4gICAgY29uc3QgemV0YSA9IGMgLyAoMiAqIE1hdGguc3FydChrICogbSkpOyAvLyBkYW1waW5nIHJhdGlvXG4gICAgY29uc3Qgb21lZ2EwID0gTWF0aC5zcXJ0KGsgLyBtKTsgLy8gdW5kYW1wZWQgYW5ndWxhciBmcmVxdWVuY3kgb2YgdGhlIG9zY2lsbGF0b3IgKHJhZC9tcylcbiAgICBjb25zdCBvbWVnYTEgPSBvbWVnYTAgKiBNYXRoLnNxcnQoMS4wIC0gemV0YSAqIHpldGEpOyAvLyBleHBvbmVudGlhbCBkZWNheVxuICAgIGNvbnN0IHgwID0gdGhpcy5fdG9WYWx1ZSAtIHRoaXMuX3N0YXJ0UG9zaXRpb247IC8vIGNhbGN1bGF0ZSB0aGUgb3NjaWxsYXRpb24gZnJvbSB4MCA9IDEgdG8geCA9IDBcblxuICAgIGxldCBwb3NpdGlvbiA9IDAuMDtcbiAgICBsZXQgdmVsb2NpdHkgPSAwLjA7XG4gICAgY29uc3QgdCA9IHRoaXMuX2ZyYW1lVGltZTtcbiAgICBpZiAoemV0YSA8IDEpIHtcbiAgICAgIC8vIFVuZGVyIGRhbXBlZFxuICAgICAgY29uc3QgZW52ZWxvcGUgPSBNYXRoLmV4cCgtemV0YSAqIG9tZWdhMCAqIHQpO1xuICAgICAgcG9zaXRpb24gPVxuICAgICAgICB0aGlzLl90b1ZhbHVlIC1cbiAgICAgICAgZW52ZWxvcGUgKlxuICAgICAgICAgICgoKHYwICsgemV0YSAqIG9tZWdhMCAqIHgwKSAvIG9tZWdhMSkgKiBNYXRoLnNpbihvbWVnYTEgKiB0KSArXG4gICAgICAgICAgICB4MCAqIE1hdGguY29zKG9tZWdhMSAqIHQpKTtcbiAgICAgIC8vIFRoaXMgbG9va3MgY3JhenkgLS0gaXQncyBhY3R1YWxseSBqdXN0IHRoZSBkZXJpdmF0aXZlIG9mIHRoZVxuICAgICAgLy8gb3NjaWxsYXRpb24gZnVuY3Rpb25cbiAgICAgIHZlbG9jaXR5ID1cbiAgICAgICAgemV0YSAqXG4gICAgICAgICAgb21lZ2EwICpcbiAgICAgICAgICBlbnZlbG9wZSAqXG4gICAgICAgICAgKChNYXRoLnNpbihvbWVnYTEgKiB0KSAqICh2MCArIHpldGEgKiBvbWVnYTAgKiB4MCkpIC8gb21lZ2ExICtcbiAgICAgICAgICAgIHgwICogTWF0aC5jb3Mob21lZ2ExICogdCkpIC1cbiAgICAgICAgZW52ZWxvcGUgKlxuICAgICAgICAgIChNYXRoLmNvcyhvbWVnYTEgKiB0KSAqICh2MCArIHpldGEgKiBvbWVnYTAgKiB4MCkgLVxuICAgICAgICAgICAgb21lZ2ExICogeDAgKiBNYXRoLnNpbihvbWVnYTEgKiB0KSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIENyaXRpY2FsbHkgZGFtcGVkXG4gICAgICBjb25zdCBlbnZlbG9wZSA9IE1hdGguZXhwKC1vbWVnYTAgKiB0KTtcbiAgICAgIHBvc2l0aW9uID0gdGhpcy5fdG9WYWx1ZSAtIGVudmVsb3BlICogKHgwICsgKHYwICsgb21lZ2EwICogeDApICogdCk7XG4gICAgICB2ZWxvY2l0eSA9XG4gICAgICAgIGVudmVsb3BlICogKHYwICogKHQgKiBvbWVnYTAgLSAxKSArIHQgKiB4MCAqIChvbWVnYTAgKiBvbWVnYTApKTtcbiAgICB9XG5cbiAgICB0aGlzLl9sYXN0VGltZSA9IG5vdztcbiAgICB0aGlzLl9sYXN0UG9zaXRpb24gPSBwb3NpdGlvbjtcbiAgICB0aGlzLl9sYXN0VmVsb2NpdHkgPSB2ZWxvY2l0eTtcblxuICAgIHRoaXMuX29uVXBkYXRlKHBvc2l0aW9uKTtcbiAgICBpZiAoIXRoaXMuX19hY3RpdmUpIHtcbiAgICAgIC8vIGEgbGlzdGVuZXIgbWlnaHQgaGF2ZSBzdG9wcGVkIHVzIGluIF9vblVwZGF0ZVxuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIC8vIENvbmRpdGlvbnMgZm9yIHN0b3BwaW5nIHRoZSBzcHJpbmcgYW5pbWF0aW9uXG4gICAgbGV0IGlzT3ZlcnNob290aW5nID0gZmFsc2U7XG4gICAgaWYgKHRoaXMuX292ZXJzaG9vdENsYW1waW5nICYmIHRoaXMuX3N0aWZmbmVzcyAhPT0gMCkge1xuICAgICAgaWYgKHRoaXMuX3N0YXJ0UG9zaXRpb24gPCB0aGlzLl90b1ZhbHVlKSB7XG4gICAgICAgIGlzT3ZlcnNob290aW5nID0gcG9zaXRpb24gPiB0aGlzLl90b1ZhbHVlO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgaXNPdmVyc2hvb3RpbmcgPSBwb3NpdGlvbiA8IHRoaXMuX3RvVmFsdWU7XG4gICAgICB9XG4gICAgfVxuICAgIGNvbnN0IGlzVmVsb2NpdHkgPSBNYXRoLmFicyh2ZWxvY2l0eSkgPD0gdGhpcy5fcmVzdFNwZWVkVGhyZXNob2xkO1xuICAgIGxldCBpc0Rpc3BsYWNlbWVudCA9IHRydWU7XG4gICAgaWYgKHRoaXMuX3N0aWZmbmVzcyAhPT0gMCkge1xuICAgICAgaXNEaXNwbGFjZW1lbnQgPVxuICAgICAgICBNYXRoLmFicyh0aGlzLl90b1ZhbHVlIC0gcG9zaXRpb24pIDw9IHRoaXMuX3Jlc3REaXNwbGFjZW1lbnRUaHJlc2hvbGQ7XG4gICAgfVxuXG4gICAgaWYgKGlzT3ZlcnNob290aW5nIHx8IChpc1ZlbG9jaXR5ICYmIGlzRGlzcGxhY2VtZW50KSkge1xuICAgICAgaWYgKHRoaXMuX3N0aWZmbmVzcyAhPT0gMCkge1xuICAgICAgICAvLyBFbnN1cmUgdGhhdCB3ZSBlbmQgdXAgd2l0aCBhIHJvdW5kIHZhbHVlXG4gICAgICAgIHRoaXMuX2xhc3RQb3NpdGlvbiA9IHRoaXMuX3RvVmFsdWU7XG4gICAgICAgIHRoaXMuX2xhc3RWZWxvY2l0eSA9IDA7XG4gICAgICAgIHRoaXMuX29uVXBkYXRlKHRoaXMuX3RvVmFsdWUpO1xuICAgICAgfVxuXG4gICAgICB0aGlzLl9fbm90aWZ5QW5pbWF0aW9uRW5kKHtmaW5pc2hlZDogdHJ1ZX0pO1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICAvLyAkRmxvd0ZpeE1lW21ldGhvZC11bmJpbmRpbmddIGFkZGVkIHdoZW4gaW1wcm92aW5nIHR5cGluZyBmb3IgdGhpcyBwYXJhbWV0ZXJzXG4gICAgdGhpcy5fYW5pbWF0aW9uRnJhbWUgPSByZXF1ZXN0QW5pbWF0aW9uRnJhbWUodGhpcy5vblVwZGF0ZS5iaW5kKHRoaXMpKTtcbiAgfVxuXG4gIHN0b3AoKTogdm9pZCB7XG4gICAgc3VwZXIuc3RvcCgpO1xuICAgIGNsZWFyVGltZW91dCh0aGlzLl90aW1lb3V0KTtcbiAgICBpZiAodGhpcy5fYW5pbWF0aW9uRnJhbWUgIT0gbnVsbCkge1xuICAgICAgZ2xvYmFsLmNhbmNlbEFuaW1hdGlvbkZyYW1lKHRoaXMuX2FuaW1hdGlvbkZyYW1lKTtcbiAgICB9XG4gICAgdGhpcy5fX25vdGlmeUFuaW1hdGlvbkVuZCh7ZmluaXNoZWQ6IGZhbHNlfSk7XG4gIH1cbn1cbiJdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBZ0JBLElBQUFBLGNBQUEsR0FBQUMsc0JBQUEsQ0FBQUMsT0FBQTtBQUNBLElBQUFDLFlBQUEsR0FBQUMsdUJBQUEsQ0FBQUYsT0FBQTtBQUNBLElBQUFHLFdBQUEsR0FBQUosc0JBQUEsQ0FBQUMsT0FBQTtBQUNBLElBQUFJLFVBQUEsR0FBQUwsc0JBQUEsQ0FBQUMsT0FBQTtBQUFrQyxTQUFBRSx3QkFBQUcsQ0FBQSxFQUFBQyxDQUFBLDZCQUFBQyxPQUFBLE1BQUFDLENBQUEsT0FBQUQsT0FBQSxJQUFBRSxDQUFBLE9BQUFGLE9BQUEsWUFBQUwsdUJBQUEsWUFBQUEsd0JBQUFHLENBQUEsRUFBQUMsQ0FBQSxTQUFBQSxDQUFBLElBQUFELENBQUEsSUFBQUEsQ0FBQSxDQUFBSyxVQUFBLFNBQUFMLENBQUEsTUFBQU0sQ0FBQSxFQUFBQyxDQUFBLEVBQUFDLENBQUEsS0FBQUMsU0FBQSxRQUFBQyxPQUFBLEVBQUFWLENBQUEsaUJBQUFBLENBQUEsdUJBQUFBLENBQUEseUJBQUFBLENBQUEsU0FBQVEsQ0FBQSxNQUFBRixDQUFBLEdBQUFMLENBQUEsR0FBQUcsQ0FBQSxHQUFBRCxDQUFBLFFBQUFHLENBQUEsQ0FBQUssR0FBQSxDQUFBWCxDQUFBLFVBQUFNLENBQUEsQ0FBQU0sR0FBQSxDQUFBWixDQUFBLEdBQUFNLENBQUEsQ0FBQU8sR0FBQSxDQUFBYixDQUFBLEVBQUFRLENBQUEsY0FBQU0sRUFBQSxJQUFBZCxDQUFBLGdCQUFBYyxFQUFBLE9BQUFDLGNBQUEsQ0FBQUMsSUFBQSxDQUFBaEIsQ0FBQSxFQUFBYyxFQUFBLE9BQUFQLENBQUEsSUFBQUQsQ0FBQSxHQUFBVyxNQUFBLENBQUFDLGNBQUEsS0FBQUQsTUFBQSxDQUFBRSx3QkFBQSxDQUFBbkIsQ0FBQSxFQUFBYyxFQUFBLE9BQUFQLENBQUEsQ0FBQUssR0FBQSxJQUFBTCxDQUFBLENBQUFNLEdBQUEsSUFBQVAsQ0FBQSxDQUFBRSxDQUFBLEVBQUFNLEVBQUEsRUFBQVAsQ0FBQSxJQUFBQyxDQUFBLENBQUFNLEVBQUEsSUFBQWQsQ0FBQSxDQUFBYyxFQUFBLFdBQUFOLENBQUEsS0FBQVIsQ0FBQSxFQUFBQyxDQUFBO0FBQUEsU0FBQW1CLFdBQUFuQixDQUFBLEVBQUFLLENBQUEsRUFBQU4sQ0FBQSxXQUFBTSxDQUFBLE9BQUFlLGdCQUFBLENBQUFYLE9BQUEsRUFBQUosQ0FBQSxPQUFBZ0IsMkJBQUEsQ0FBQVosT0FBQSxFQUFBVCxDQUFBLEVBQUFzQix5QkFBQSxLQUFBQyxPQUFBLENBQUFDLFNBQUEsQ0FBQW5CLENBQUEsRUFBQU4sQ0FBQSxZQUFBcUIsZ0JBQUEsQ0FBQVgsT0FBQSxFQUFBVCxDQUFBLEVBQUF5QixXQUFBLElBQUFwQixDQUFBLENBQUFxQixLQUFBLENBQUExQixDQUFBLEVBQUFELENBQUE7QUFBQSxTQUFBdUIsMEJBQUEsY0FBQXRCLENBQUEsSUFBQTJCLE9BQUEsQ0FBQUMsU0FBQSxDQUFBQyxPQUFBLENBQUFkLElBQUEsQ0FBQVEsT0FBQSxDQUFBQyxTQUFBLENBQUFHLE9BQUEsaUNBQUEzQixDQUFBLGFBQUFzQix5QkFBQSxZQUFBQSwwQkFBQSxhQUFBdEIsQ0FBQTtBQUFBLFNBQUE4QixjQUFBOUIsQ0FBQSxFQUFBSyxDQUFBLEVBQUFOLENBQUEsRUFBQUcsQ0FBQSxRQUFBNkIsQ0FBQSxPQUFBQyxLQUFBLENBQUF2QixPQUFBLE1BQUFXLGdCQUFBLENBQUFYLE9BQUEsTUFBQVAsQ0FBQSxHQUFBRixDQUFBLENBQUE0QixTQUFBLEdBQUE1QixDQUFBLEdBQUFLLENBQUEsRUFBQU4sQ0FBQSxjQUFBRyxDQUFBLHlCQUFBNkIsQ0FBQSxhQUFBL0IsQ0FBQSxXQUFBK0IsQ0FBQSxDQUFBTCxLQUFBLENBQUEzQixDQUFBLEVBQUFDLENBQUEsT0FBQStCLENBQUE7QUFBQSxJQW1FYkUsZUFBZSxHQUFBQyxPQUFBLENBQUF6QixPQUFBLGFBQUEwQixVQUFBO0VBc0JsQyxTQUFBRixnQkFBWUcsTUFBbUMsRUFBRTtJQUFBLElBQUFDLHFCQUFBLEVBQUFDLHFCQUFBLEVBQUFDLHFCQUFBLEVBQUFDLGdCQUFBLEVBQUFDLGlCQUFBLEVBQUFDLGFBQUE7SUFBQSxJQUFBQyxLQUFBO0lBQUEsSUFBQUMsZ0JBQUEsQ0FBQW5DLE9BQUEsUUFBQXdCLGVBQUE7SUFDL0NVLEtBQUEsR0FBQXhCLFVBQUEsT0FBQWMsZUFBQSxHQUFNRyxNQUFNO0lBRVpPLEtBQUEsQ0FBS0Usa0JBQWtCLElBQUFSLHFCQUFBLEdBQUdELE1BQU0sQ0FBQ1UsaUJBQWlCLFlBQUFULHFCQUFBLEdBQUksS0FBSztJQUMzRE0sS0FBQSxDQUFLSSwwQkFBMEIsSUFBQVQscUJBQUEsR0FBR0YsTUFBTSxDQUFDWSx5QkFBeUIsWUFBQVYscUJBQUEsR0FBSSxLQUFLO0lBQzNFSyxLQUFBLENBQUtNLG1CQUFtQixJQUFBVixxQkFBQSxHQUFHSCxNQUFNLENBQUNjLGtCQUFrQixZQUFBWCxxQkFBQSxHQUFJLEtBQUs7SUFDN0RJLEtBQUEsQ0FBS1EsZ0JBQWdCLElBQUFYLGdCQUFBLEdBQUdKLE1BQU0sQ0FBQ2dCLFFBQVEsWUFBQVosZ0JBQUEsR0FBSSxDQUFDO0lBQzVDRyxLQUFBLENBQUtVLGFBQWEsSUFBQVosaUJBQUEsR0FBR0wsTUFBTSxDQUFDZ0IsUUFBUSxZQUFBWCxpQkFBQSxHQUFJLENBQUM7SUFDekNFLEtBQUEsQ0FBS1csUUFBUSxHQUFHbEIsTUFBTSxDQUFDbUIsT0FBTztJQUM5QlosS0FBQSxDQUFLYSxNQUFNLElBQUFkLGFBQUEsR0FBR04sTUFBTSxDQUFDcUIsS0FBSyxZQUFBZixhQUFBLEdBQUksQ0FBQztJQUMvQkMsS0FBQSxDQUFLZSxlQUFlLEdBQUd0QixNQUFNLENBQUN1QixjQUFjO0lBRTVDLElBQ0V2QixNQUFNLENBQUN3QixTQUFTLEtBQUtDLFNBQVMsSUFDOUJ6QixNQUFNLENBQUMwQixPQUFPLEtBQUtELFNBQVMsSUFDNUJ6QixNQUFNLENBQUMyQixJQUFJLEtBQUtGLFNBQVMsRUFDekI7TUFBQSxJQUFBRyxpQkFBQSxFQUFBQyxlQUFBLEVBQUFDLFlBQUE7TUFDQSxJQUFBQyxrQkFBUyxFQUNQL0IsTUFBTSxDQUFDZ0MsVUFBVSxLQUFLUCxTQUFTLElBQzdCekIsTUFBTSxDQUFDaUMsS0FBSyxLQUFLUixTQUFTLElBQzFCekIsTUFBTSxDQUFDa0MsT0FBTyxLQUFLVCxTQUFTLElBQzVCekIsTUFBTSxDQUFDbUMsUUFBUSxLQUFLVixTQUFTLEVBQy9CLDRHQUNGLENBQUM7TUFDRGxCLEtBQUEsQ0FBSzZCLFVBQVUsSUFBQVIsaUJBQUEsR0FBRzVCLE1BQU0sQ0FBQ3dCLFNBQVMsWUFBQUksaUJBQUEsR0FBSSxHQUFHO01BQ3pDckIsS0FBQSxDQUFLOEIsUUFBUSxJQUFBUixlQUFBLEdBQUc3QixNQUFNLENBQUMwQixPQUFPLFlBQUFHLGVBQUEsR0FBSSxFQUFFO01BQ3BDdEIsS0FBQSxDQUFLK0IsS0FBSyxJQUFBUixZQUFBLEdBQUc5QixNQUFNLENBQUMyQixJQUFJLFlBQUFHLFlBQUEsR0FBSSxDQUFDO0lBQy9CLENBQUMsTUFBTSxJQUFJOUIsTUFBTSxDQUFDZ0MsVUFBVSxLQUFLUCxTQUFTLElBQUl6QixNQUFNLENBQUNpQyxLQUFLLEtBQUtSLFNBQVMsRUFBRTtNQUFBLElBQUFjLGtCQUFBLEVBQUFDLGFBQUE7TUFHeEUsSUFBQVQsa0JBQVMsRUFDUC9CLE1BQU0sQ0FBQ2tDLE9BQU8sS0FBS1QsU0FBUyxJQUMxQnpCLE1BQU0sQ0FBQ21DLFFBQVEsS0FBS1YsU0FBUyxJQUM3QnpCLE1BQU0sQ0FBQ3dCLFNBQVMsS0FBS0MsU0FBUyxJQUM5QnpCLE1BQU0sQ0FBQzBCLE9BQU8sS0FBS0QsU0FBUyxJQUM1QnpCLE1BQU0sQ0FBQzJCLElBQUksS0FBS0YsU0FBUyxFQUMzQiw0R0FDRixDQUFDO01BQ0QsSUFBTWdCLFlBQVksR0FBR2xGLFlBQVksQ0FBQ21GLHNCQUFzQixFQUFBSCxrQkFBQSxHQUN0RHZDLE1BQU0sQ0FBQ2dDLFVBQVUsWUFBQU8sa0JBQUEsR0FBSSxDQUFDLEdBQUFDLGFBQUEsR0FDdEJ4QyxNQUFNLENBQUNpQyxLQUFLLFlBQUFPLGFBQUEsR0FBSSxFQUNsQixDQUFDO01BQ0RqQyxLQUFBLENBQUs2QixVQUFVLEdBQUdLLFlBQVksQ0FBQ2pCLFNBQVM7TUFDeENqQixLQUFBLENBQUs4QixRQUFRLEdBQUdJLFlBQVksQ0FBQ2YsT0FBTztNQUNwQ25CLEtBQUEsQ0FBSytCLEtBQUssR0FBRyxDQUFDO0lBQ2hCLENBQUMsTUFBTTtNQUFBLElBQUFLLGVBQUEsRUFBQUMsZ0JBQUE7TUFHTCxJQUFNSCxhQUFZLEdBQUdsRixZQUFZLENBQUNzRiw2QkFBNkIsRUFBQUYsZUFBQSxHQUM3RDNDLE1BQU0sQ0FBQ2tDLE9BQU8sWUFBQVMsZUFBQSxHQUFJLEVBQUUsR0FBQUMsZ0JBQUEsR0FDcEI1QyxNQUFNLENBQUNtQyxRQUFRLFlBQUFTLGdCQUFBLEdBQUksQ0FDckIsQ0FBQztNQUNEckMsS0FBQSxDQUFLNkIsVUFBVSxHQUFHSyxhQUFZLENBQUNqQixTQUFTO01BQ3hDakIsS0FBQSxDQUFLOEIsUUFBUSxHQUFHSSxhQUFZLENBQUNmLE9BQU87TUFDcENuQixLQUFBLENBQUsrQixLQUFLLEdBQUcsQ0FBQztJQUNoQjtJQUVBLElBQUFQLGtCQUFTLEVBQUN4QixLQUFBLENBQUs2QixVQUFVLEdBQUcsQ0FBQyxFQUFFLHdDQUF3QyxDQUFDO0lBQ3hFLElBQUFMLGtCQUFTLEVBQUN4QixLQUFBLENBQUs4QixRQUFRLEdBQUcsQ0FBQyxFQUFFLHNDQUFzQyxDQUFDO0lBQ3BFLElBQUFOLGtCQUFTLEVBQUN4QixLQUFBLENBQUsrQixLQUFLLEdBQUcsQ0FBQyxFQUFFLG1DQUFtQyxDQUFDO0lBQUMsT0FBQS9CLEtBQUE7RUFDakU7RUFBQyxJQUFBdUMsVUFBQSxDQUFBekUsT0FBQSxFQUFBd0IsZUFBQSxFQUFBRSxVQUFBO0VBQUEsV0FBQWdELGFBQUEsQ0FBQTFFLE9BQUEsRUFBQXdCLGVBQUE7SUFBQW1ELEdBQUE7SUFBQUMsS0FBQSxFQUVELFNBQUFDLDBCQUEwQkEsQ0FBQSxFQWF2QjtNQUFBLElBQUFDLHFCQUFBO01BQ0QsT0FBTztRQUNMQyxJQUFJLEVBQUUsUUFBUTtRQUNkMUMsaUJBQWlCLEVBQUUsSUFBSSxDQUFDRCxrQkFBa0I7UUFDMUNHLHlCQUF5QixFQUFFLElBQUksQ0FBQ0QsMEJBQTBCO1FBQzFERyxrQkFBa0IsRUFBRSxJQUFJLENBQUNELG1CQUFtQjtRQUM1Q1csU0FBUyxFQUFFLElBQUksQ0FBQ1ksVUFBVTtRQUMxQlYsT0FBTyxFQUFFLElBQUksQ0FBQ1csUUFBUTtRQUN0QlYsSUFBSSxFQUFFLElBQUksQ0FBQ1csS0FBSztRQUNoQmUsZUFBZSxHQUFBRixxQkFBQSxHQUFFLElBQUksQ0FBQ3BDLGdCQUFnQixZQUFBb0MscUJBQUEsR0FBSSxJQUFJLENBQUNsQyxhQUFhO1FBQzVERSxPQUFPLEVBQUUsSUFBSSxDQUFDRCxRQUFRO1FBQ3RCb0MsVUFBVSxFQUFFLElBQUksQ0FBQ0MsWUFBWTtRQUM3QmhDLGNBQWMsRUFBRSxJQUFJLENBQUNELGVBQWU7UUFDcENrQyxPQUFPLEVBQUUsSUFBSSxDQUFDQyxZQUFZLENBQUM7TUFDN0IsQ0FBQztJQUNIO0VBQUM7SUFBQVQsR0FBQTtJQUFBQyxLQUFBLEVBRUQsU0FBQVMsS0FBS0EsQ0FDSEMsU0FBaUIsRUFDakJDLFFBQWlDLEVBQ2pDQyxLQUFtQixFQUNuQkMsaUJBQTZCLEVBQzdCQyxhQUE0QixFQUN0QjtNQUFBLElBQUFDLE1BQUE7TUFDTnRFLGFBQUEsQ0FBQUcsZUFBQSxxQkFBWThELFNBQVMsRUFBRUMsUUFBUSxFQUFFQyxLQUFLLEVBQUVDLGlCQUFpQixFQUFFQyxhQUFhO01BRXhFLElBQUksQ0FBQ0UsY0FBYyxHQUFHTixTQUFTO01BQy9CLElBQUksQ0FBQ08sYUFBYSxHQUFHLElBQUksQ0FBQ0QsY0FBYztNQUV4QyxJQUFJLENBQUNFLFNBQVMsR0FBR1AsUUFBUTtNQUN6QixJQUFJLENBQUNRLFNBQVMsR0FBR0MsSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQztNQUMzQixJQUFJLENBQUNDLFVBQVUsR0FBRyxHQUFHO01BRXJCLElBQUlULGlCQUFpQixZQUFZakUsZUFBZSxFQUFFO1FBQ2hELElBQU0yRSxhQUFhLEdBQUdWLGlCQUFpQixDQUFDVyxnQkFBZ0IsQ0FBQyxDQUFDO1FBQzFELElBQUksQ0FBQ1AsYUFBYSxHQUFHTSxhQUFhLENBQUNFLFlBQVk7UUFDL0MsSUFBSSxDQUFDekQsYUFBYSxHQUFHdUQsYUFBYSxDQUFDRyxZQUFZO1FBRS9DLElBQUksQ0FBQzVELGdCQUFnQixHQUFHLElBQUksQ0FBQ0UsYUFBYTtRQUMxQyxJQUFJLENBQUNtRCxTQUFTLEdBQUdJLGFBQWEsQ0FBQ0ksUUFBUTtNQUN6QztNQUVBLElBQU1sQixLQUFLLEdBQUcsU0FBUkEsS0FBS0EsQ0FBQSxFQUFTO1FBQ2xCLElBQU1tQixlQUFlLEdBQUdiLE1BQUksQ0FBQ2Msd0JBQXdCLENBQUNmLGFBQWEsQ0FBQztRQUNwRSxJQUFJLENBQUNjLGVBQWUsRUFBRTtVQUNwQmIsTUFBSSxDQUFDSixRQUFRLENBQUMsQ0FBQztRQUNqQjtNQUNGLENBQUM7TUFHRCxJQUFJLElBQUksQ0FBQ3hDLE1BQU0sRUFBRTtRQUNmLElBQUksQ0FBQzJELFFBQVEsR0FBR0MsVUFBVSxDQUFDdEIsS0FBSyxFQUFFLElBQUksQ0FBQ3RDLE1BQU0sQ0FBQztNQUNoRCxDQUFDLE1BQU07UUFDTHNDLEtBQUssQ0FBQyxDQUFDO01BQ1Q7SUFDRjtFQUFDO0lBQUFWLEdBQUE7SUFBQUMsS0FBQSxFQUVELFNBQUF3QixnQkFBZ0JBLENBQUEsRUFBaUM7TUFDL0MsT0FBTztRQUNMQyxZQUFZLEVBQUUsSUFBSSxDQUFDUixhQUFhO1FBQ2hDUyxZQUFZLEVBQUUsSUFBSSxDQUFDMUQsYUFBYTtRQUNoQzJELFFBQVEsRUFBRSxJQUFJLENBQUNSO01BQ2pCLENBQUM7SUFDSDtFQUFDO0lBQUFwQixHQUFBO0lBQUFDLEtBQUEsRUF1QkQsU0FBQVcsUUFBUUEsQ0FBQSxFQUFTO01BS2YsSUFBTXFCLFNBQVMsR0FBRyxFQUFFO01BQ3BCLElBQUlYLEdBQUcsR0FBR0QsSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQztNQUNwQixJQUFJQSxHQUFHLEdBQUcsSUFBSSxDQUFDRixTQUFTLEdBQUdhLFNBQVMsRUFBRTtRQUNwQ1gsR0FBRyxHQUFHLElBQUksQ0FBQ0YsU0FBUyxHQUFHYSxTQUFTO01BQ2xDO01BRUEsSUFBTUMsU0FBUyxHQUFHLENBQUNaLEdBQUcsR0FBRyxJQUFJLENBQUNGLFNBQVMsSUFBSSxJQUFJO01BQy9DLElBQUksQ0FBQ0csVUFBVSxJQUFJVyxTQUFTO01BRTVCLElBQU1DLENBQVMsR0FBRyxJQUFJLENBQUM5QyxRQUFRO01BQy9CLElBQU0rQyxDQUFTLEdBQUcsSUFBSSxDQUFDOUMsS0FBSztNQUM1QixJQUFNK0MsQ0FBUyxHQUFHLElBQUksQ0FBQ2pELFVBQVU7TUFDakMsSUFBTWtELEVBQVUsR0FBRyxDQUFDLElBQUksQ0FBQ3ZFLGdCQUFnQjtNQUV6QyxJQUFNd0UsSUFBSSxHQUFHSixDQUFDLElBQUksQ0FBQyxHQUFHSyxJQUFJLENBQUNDLElBQUksQ0FBQ0osQ0FBQyxHQUFHRCxDQUFDLENBQUMsQ0FBQztNQUN2QyxJQUFNTSxNQUFNLEdBQUdGLElBQUksQ0FBQ0MsSUFBSSxDQUFDSixDQUFDLEdBQUdELENBQUMsQ0FBQztNQUMvQixJQUFNTyxNQUFNLEdBQUdELE1BQU0sR0FBR0YsSUFBSSxDQUFDQyxJQUFJLENBQUMsR0FBRyxHQUFHRixJQUFJLEdBQUdBLElBQUksQ0FBQztNQUNwRCxJQUFNSyxFQUFFLEdBQUcsSUFBSSxDQUFDMUUsUUFBUSxHQUFHLElBQUksQ0FBQytDLGNBQWM7TUFFOUMsSUFBSTRCLFFBQVEsR0FBRyxHQUFHO01BQ2xCLElBQUk3RSxRQUFRLEdBQUcsR0FBRztNQUNsQixJQUFNcEQsQ0FBQyxHQUFHLElBQUksQ0FBQzJHLFVBQVU7TUFDekIsSUFBSWdCLElBQUksR0FBRyxDQUFDLEVBQUU7UUFFWixJQUFNTyxRQUFRLEdBQUdOLElBQUksQ0FBQ08sR0FBRyxDQUFDLENBQUNSLElBQUksR0FBR0csTUFBTSxHQUFHOUgsQ0FBQyxDQUFDO1FBQzdDaUksUUFBUSxHQUNOLElBQUksQ0FBQzNFLFFBQVEsR0FDYjRFLFFBQVEsSUFDSixDQUFDUixFQUFFLEdBQUdDLElBQUksR0FBR0csTUFBTSxHQUFHRSxFQUFFLElBQUlELE1BQU0sR0FBSUgsSUFBSSxDQUFDUSxHQUFHLENBQUNMLE1BQU0sR0FBRy9ILENBQUMsQ0FBQyxHQUMxRGdJLEVBQUUsR0FBR0osSUFBSSxDQUFDUyxHQUFHLENBQUNOLE1BQU0sR0FBRy9ILENBQUMsQ0FBQyxDQUFDO1FBR2hDb0QsUUFBUSxHQUNOdUUsSUFBSSxHQUNGRyxNQUFNLEdBQ05JLFFBQVEsSUFDTk4sSUFBSSxDQUFDUSxHQUFHLENBQUNMLE1BQU0sR0FBRy9ILENBQUMsQ0FBQyxJQUFJMEgsRUFBRSxHQUFHQyxJQUFJLEdBQUdHLE1BQU0sR0FBR0UsRUFBRSxDQUFDLEdBQUlELE1BQU0sR0FDMURDLEVBQUUsR0FBR0osSUFBSSxDQUFDUyxHQUFHLENBQUNOLE1BQU0sR0FBRy9ILENBQUMsQ0FBQyxDQUFDLEdBQzlCa0ksUUFBUSxJQUNMTixJQUFJLENBQUNTLEdBQUcsQ0FBQ04sTUFBTSxHQUFHL0gsQ0FBQyxDQUFDLElBQUkwSCxFQUFFLEdBQUdDLElBQUksR0FBR0csTUFBTSxHQUFHRSxFQUFFLENBQUMsR0FDL0NELE1BQU0sR0FBR0MsRUFBRSxHQUFHSixJQUFJLENBQUNRLEdBQUcsQ0FBQ0wsTUFBTSxHQUFHL0gsQ0FBQyxDQUFDLENBQUM7TUFDM0MsQ0FBQyxNQUFNO1FBRUwsSUFBTWtJLFNBQVEsR0FBR04sSUFBSSxDQUFDTyxHQUFHLENBQUMsQ0FBQ0wsTUFBTSxHQUFHOUgsQ0FBQyxDQUFDO1FBQ3RDaUksUUFBUSxHQUFHLElBQUksQ0FBQzNFLFFBQVEsR0FBRzRFLFNBQVEsSUFBSUYsRUFBRSxHQUFHLENBQUNOLEVBQUUsR0FBR0ksTUFBTSxHQUFHRSxFQUFFLElBQUloSSxDQUFDLENBQUM7UUFDbkVvRCxRQUFRLEdBQ044RSxTQUFRLElBQUlSLEVBQUUsSUFBSTFILENBQUMsR0FBRzhILE1BQU0sR0FBRyxDQUFDLENBQUMsR0FBRzlILENBQUMsR0FBR2dJLEVBQUUsSUFBSUYsTUFBTSxHQUFHQSxNQUFNLENBQUMsQ0FBQztNQUNuRTtNQUVBLElBQUksQ0FBQ3RCLFNBQVMsR0FBR0UsR0FBRztNQUNwQixJQUFJLENBQUNKLGFBQWEsR0FBRzJCLFFBQVE7TUFDN0IsSUFBSSxDQUFDNUUsYUFBYSxHQUFHRCxRQUFRO01BRTdCLElBQUksQ0FBQ21ELFNBQVMsQ0FBQzBCLFFBQVEsQ0FBQztNQUN4QixJQUFJLENBQUMsSUFBSSxDQUFDSyxRQUFRLEVBQUU7UUFFbEI7TUFDRjtNQUdBLElBQUlDLGNBQWMsR0FBRyxLQUFLO01BQzFCLElBQUksSUFBSSxDQUFDMUYsa0JBQWtCLElBQUksSUFBSSxDQUFDMkIsVUFBVSxLQUFLLENBQUMsRUFBRTtRQUNwRCxJQUFJLElBQUksQ0FBQzZCLGNBQWMsR0FBRyxJQUFJLENBQUMvQyxRQUFRLEVBQUU7VUFDdkNpRixjQUFjLEdBQUdOLFFBQVEsR0FBRyxJQUFJLENBQUMzRSxRQUFRO1FBQzNDLENBQUMsTUFBTTtVQUNMaUYsY0FBYyxHQUFHTixRQUFRLEdBQUcsSUFBSSxDQUFDM0UsUUFBUTtRQUMzQztNQUNGO01BQ0EsSUFBTWtGLFVBQVUsR0FBR1osSUFBSSxDQUFDYSxHQUFHLENBQUNyRixRQUFRLENBQUMsSUFBSSxJQUFJLENBQUNILG1CQUFtQjtNQUNqRSxJQUFJeUYsY0FBYyxHQUFHLElBQUk7TUFDekIsSUFBSSxJQUFJLENBQUNsRSxVQUFVLEtBQUssQ0FBQyxFQUFFO1FBQ3pCa0UsY0FBYyxHQUNaZCxJQUFJLENBQUNhLEdBQUcsQ0FBQyxJQUFJLENBQUNuRixRQUFRLEdBQUcyRSxRQUFRLENBQUMsSUFBSSxJQUFJLENBQUNsRiwwQkFBMEI7TUFDekU7TUFFQSxJQUFJd0YsY0FBYyxJQUFLQyxVQUFVLElBQUlFLGNBQWUsRUFBRTtRQUNwRCxJQUFJLElBQUksQ0FBQ2xFLFVBQVUsS0FBSyxDQUFDLEVBQUU7VUFFekIsSUFBSSxDQUFDOEIsYUFBYSxHQUFHLElBQUksQ0FBQ2hELFFBQVE7VUFDbEMsSUFBSSxDQUFDRCxhQUFhLEdBQUcsQ0FBQztVQUN0QixJQUFJLENBQUNrRCxTQUFTLENBQUMsSUFBSSxDQUFDakQsUUFBUSxDQUFDO1FBQy9CO1FBRUEsSUFBSSxDQUFDcUYsb0JBQW9CLENBQUM7VUFBQ0MsUUFBUSxFQUFFO1FBQUksQ0FBQyxDQUFDO1FBQzNDO01BQ0Y7TUFFQSxJQUFJLENBQUNDLGVBQWUsR0FBR0MscUJBQXFCLENBQUMsSUFBSSxDQUFDOUMsUUFBUSxDQUFDK0MsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO0lBQ3hFO0VBQUM7SUFBQTNELEdBQUE7SUFBQUMsS0FBQSxFQUVELFNBQUEyRCxJQUFJQSxDQUFBLEVBQVM7TUFDWGxILGFBQUEsQ0FBQUcsZUFBQTtNQUNBZ0gsWUFBWSxDQUFDLElBQUksQ0FBQzlCLFFBQVEsQ0FBQztNQUMzQixJQUFJLElBQUksQ0FBQzBCLGVBQWUsSUFBSSxJQUFJLEVBQUU7UUFDaENLLE1BQU0sQ0FBQ0Msb0JBQW9CLENBQUMsSUFBSSxDQUFDTixlQUFlLENBQUM7TUFDbkQ7TUFDQSxJQUFJLENBQUNGLG9CQUFvQixDQUFDO1FBQUNDLFFBQVEsRUFBRTtNQUFLLENBQUMsQ0FBQztJQUM5QztFQUFDO0FBQUEsRUE3UjBDUSxtQkFBUyIsImlnbm9yZUxpc3QiOltdfQ==