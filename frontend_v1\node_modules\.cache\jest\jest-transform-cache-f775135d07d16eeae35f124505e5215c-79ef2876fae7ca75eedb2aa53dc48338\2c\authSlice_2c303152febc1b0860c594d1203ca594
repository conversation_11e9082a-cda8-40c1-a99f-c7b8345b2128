df6c1cce57840b224cb9d2f8bd1e4e53
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useSafeAuthStore = exports.useAuthStore = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _zustand = require("zustand");
var _middleware = require("zustand/middleware");
var _asyncStorage = _interopRequireDefault(require("@react-native-async-storage/async-storage"));
var initialState = {
  authToken: null,
  refreshToken: null,
  user: null,
  userRole: null,
  status: 'idle',
  error: null,
  tokenExpiresAt: null,
  isAuthenticated: false
};
var createAuthStore = function createAuthStore() {
  try {
    return (0, _zustand.create)()((0, _middleware.devtools)((0, _middleware.persist)(function (set, get) {
      return Object.assign({}, initialState, {
        isAuthenticated: false,
        loginStart: function loginStart() {
          return set(function (state) {
            return Object.assign({}, state, {
              status: 'loading',
              error: null
            });
          }, false, 'auth/loginStart');
        },
        loginSuccess: function loginSuccess(token, refreshToken, user) {
          return set(function (state) {
            return Object.assign({}, state, {
              authToken: token,
              refreshToken: refreshToken,
              user: user,
              userRole: user.role,
              status: 'success',
              error: null,
              isAuthenticated: true,
              tokenExpiresAt: Date.now() + 30 * 60 * 1000
            });
          }, false, 'auth/loginSuccess');
        },
        loginFailure: function loginFailure(error) {
          return set(function (state) {
            return Object.assign({}, state, {
              authToken: null,
              userRole: null,
              status: 'error',
              error: error,
              isAuthenticated: false
            });
          }, false, 'auth/loginFailure');
        },
        registerStart: function registerStart() {
          return set(function (state) {
            return Object.assign({}, state, {
              status: 'loading',
              error: null
            });
          }, false, 'auth/registerStart');
        },
        registerSuccess: function registerSuccess(token, refreshToken, user) {
          return set(function (state) {
            return Object.assign({}, state, {
              authToken: token,
              refreshToken: refreshToken,
              user: user,
              userRole: user.role,
              status: 'success',
              error: null,
              isAuthenticated: true,
              tokenExpiresAt: Date.now() + 30 * 60 * 1000
            });
          }, false, 'auth/registerSuccess');
        },
        registerFailure: function registerFailure(error) {
          return set(function (state) {
            return Object.assign({}, state, {
              authToken: null,
              refreshToken: null,
              user: null,
              userRole: null,
              status: 'error',
              error: error,
              isAuthenticated: false,
              tokenExpiresAt: null
            });
          }, false, 'auth/registerFailure');
        },
        updateProfile: function updateProfile(userUpdates) {
          return set(function (state) {
            return Object.assign({}, state, {
              user: state.user ? Object.assign({}, state.user, userUpdates) : null
            });
          }, false, 'auth/updateProfile');
        },
        updateTokens: function updateTokens(token, refreshToken) {
          return set(function (state) {
            return Object.assign({}, state, {
              authToken: token,
              refreshToken: refreshToken || state.refreshToken,
              tokenExpiresAt: Date.now() + 30 * 60 * 1000
            });
          }, false, 'auth/updateTokens');
        },
        logout: function logout() {
          return set(function () {
            return Object.assign({}, initialState);
          }, false, 'auth/logout');
        },
        reset: function reset() {
          return set(function () {
            return Object.assign({}, initialState);
          }, false, 'auth/reset');
        },
        checkAuthStatus: function () {
          var _checkAuthStatus = (0, _asyncToGenerator2.default)(function* () {
            try {
              var currentState = get();
              if (currentState.isAuthenticated && currentState.authToken && currentState.tokenExpiresAt) {
                if (Date.now() < currentState.tokenExpiresAt) {
                  return;
                }
              }
              var storedToken = yield _asyncStorage.default.getItem('auth_token');
              var storedRefreshToken = yield _asyncStorage.default.getItem('refresh_token');
              var storedUser = yield _asyncStorage.default.getItem('auth_user');
              if (storedToken && storedUser) {
                try {
                  var user = JSON.parse(storedUser);
                  set(function (state) {
                    return Object.assign({}, state, {
                      authToken: storedToken,
                      refreshToken: storedRefreshToken,
                      user: user,
                      userRole: user.role,
                      isAuthenticated: true,
                      status: 'success',
                      error: null
                    });
                  }, false, 'auth/checkAuthStatus');
                } catch (parseError) {
                  console.error('Failed to parse stored user data:', parseError);
                  yield _asyncStorage.default.multiRemove(['auth_token', 'refresh_token', 'auth_user']);
                  set(function () {
                    return initialState;
                  }, false, 'auth/checkAuthStatusError');
                }
              } else {
                set(function () {
                  return initialState;
                }, false, 'auth/checkAuthStatus');
              }
            } catch (error) {
              console.error('Auth status check failed:', error);
              set(function () {
                return initialState;
              }, false, 'auth/checkAuthStatusError');
            }
          });
          function checkAuthStatus() {
            return _checkAuthStatus.apply(this, arguments);
          }
          return checkAuthStatus;
        }(),
        validateToken: function () {
          var _validateToken = (0, _asyncToGenerator2.default)(function* () {
            try {
              var currentState = get();
              if (!currentState.authToken) {
                return false;
              }
              if (currentState.tokenExpiresAt && Date.now() >= currentState.tokenExpiresAt) {
                if (currentState.refreshToken) {
                  try {
                    var _yield$import = yield import("../services/authService"),
                      authService = _yield$import.authService;
                    var response = yield authService.refreshToken(currentState.refreshToken);
                    set(function (state) {
                      return Object.assign({}, state, {
                        authToken: response.access,
                        tokenExpiresAt: Date.now() + 30 * 60 * 1000
                      });
                    }, false, 'auth/tokenRefreshed');
                    yield _asyncStorage.default.setItem('auth_token', response.access);
                    return true;
                  } catch (refreshError) {
                    console.error('Token refresh failed:', refreshError);
                    set(function () {
                      return initialState;
                    }, false, 'auth/tokenExpired');
                    yield _asyncStorage.default.multiRemove(['auth_token', 'refresh_token', 'auth_user']);
                    return false;
                  }
                } else {
                  set(function () {
                    return initialState;
                  }, false, 'auth/tokenExpired');
                  yield _asyncStorage.default.multiRemove(['auth_token', 'refresh_token', 'auth_user']);
                  return false;
                }
              }
              return true;
            } catch (error) {
              console.error('Token validation failed:', error);
              return false;
            }
          });
          function validateToken() {
            return _validateToken.apply(this, arguments);
          }
          return validateToken;
        }()
      });
    }, {
      name: 'auth-store',
      storage: {
        getItem: function () {
          var _getItem = (0, _asyncToGenerator2.default)(function* (name) {
            try {
              var value = yield _asyncStorage.default.getItem(name);
              if (value) {
                var parsed = JSON.parse(value);
                if (parsed && typeof parsed === 'object' && 'loginSuccess' in parsed) {
                  console.log('🔄 Clearing old auth store data due to structure change');
                  yield _asyncStorage.default.removeItem(name);
                  return null;
                }
                return parsed;
              }
              return null;
            } catch (error) {
              console.error('Failed to load auth state:', error);
              try {
                yield _asyncStorage.default.removeItem(name);
              } catch (_unused) {}
              return null;
            }
          });
          function getItem(_x) {
            return _getItem.apply(this, arguments);
          }
          return getItem;
        }(),
        setItem: function () {
          var _setItem = (0, _asyncToGenerator2.default)(function* (name, value) {
            try {
              yield _asyncStorage.default.setItem(name, JSON.stringify(value));
            } catch (error) {
              console.error('Failed to save auth state:', error);
            }
          });
          function setItem(_x2, _x3) {
            return _setItem.apply(this, arguments);
          }
          return setItem;
        }(),
        removeItem: function () {
          var _removeItem = (0, _asyncToGenerator2.default)(function* (name) {
            try {
              yield _asyncStorage.default.removeItem(name);
            } catch (error) {
              console.error('Failed to remove auth state:', error);
            }
          });
          function removeItem(_x4) {
            return _removeItem.apply(this, arguments);
          }
          return removeItem;
        }()
      },
      partialize: function partialize(state) {
        return {
          authToken: state.authToken,
          refreshToken: state.refreshToken,
          user: state.user,
          userRole: state.userRole,
          isAuthenticated: state.isAuthenticated,
          tokenExpiresAt: state.tokenExpiresAt,
          _version: '2.0.0'
        };
      },
      migrate: function migrate(persistedState, version) {
        if (!persistedState || !persistedState._version || persistedState._version !== '2.0.0') {
          console.log('🔄 Migrating auth store to new version, clearing old data');
          return initialState;
        }
        return persistedState;
      },
      version: 1
    }, {
      name: 'auth-store'
    })));
  } catch (error) {
    console.error('Failed to create auth store:', error);
    return (0, _zustand.create)()(function () {
      return Object.assign({}, initialState, {
        isAuthenticated: false,
        loginStart: function loginStart() {},
        loginSuccess: function loginSuccess() {},
        loginFailure: function loginFailure() {},
        registerStart: function registerStart() {},
        registerSuccess: function registerSuccess() {},
        registerFailure: function registerFailure() {},
        updateProfile: function updateProfile() {},
        updateTokens: function updateTokens() {},
        logout: function logout() {},
        reset: function reset() {},
        checkAuthStatus: function () {
          var _checkAuthStatus2 = (0, _asyncToGenerator2.default)(function* () {});
          function checkAuthStatus() {
            return _checkAuthStatus2.apply(this, arguments);
          }
          return checkAuthStatus;
        }(),
        validateToken: function () {
          var _validateToken2 = (0, _asyncToGenerator2.default)(function* () {
            return false;
          });
          function validateToken() {
            return _validateToken2.apply(this, arguments);
          }
          return validateToken;
        }()
      });
    });
  }
};
var useAuthStore = exports.useAuthStore = createAuthStore();
var useSafeAuthStore = exports.useSafeAuthStore = function useSafeAuthStore() {
  try {
    var store = useAuthStore();
    if (!store || typeof store !== 'object') {
      console.warn('Auth store is not properly initialized');
      return Object.assign({}, initialState, {
        isAuthenticated: false,
        loginStart: function loginStart() {},
        loginSuccess: function loginSuccess() {},
        loginFailure: function loginFailure() {},
        registerStart: function registerStart() {},
        registerSuccess: function registerSuccess() {},
        registerFailure: function registerFailure() {},
        updateProfile: function updateProfile() {},
        updateTokens: function updateTokens() {},
        logout: function logout() {},
        reset: function reset() {},
        checkAuthStatus: function () {
          var _checkAuthStatus3 = (0, _asyncToGenerator2.default)(function* () {});
          function checkAuthStatus() {
            return _checkAuthStatus3.apply(this, arguments);
          }
          return checkAuthStatus;
        }(),
        validateToken: function () {
          var _validateToken3 = (0, _asyncToGenerator2.default)(function* () {
            return false;
          });
          function validateToken() {
            return _validateToken3.apply(this, arguments);
          }
          return validateToken;
        }()
      });
    }
    return store;
  } catch (error) {
    console.error('Error accessing auth store:', error);
    return Object.assign({}, initialState, {
      isAuthenticated: false,
      loginStart: function loginStart() {},
      loginSuccess: function loginSuccess() {},
      loginFailure: function loginFailure() {},
      registerStart: function registerStart() {},
      registerSuccess: function registerSuccess() {},
      registerFailure: function registerFailure() {},
      updateProfile: function updateProfile() {},
      updateTokens: function updateTokens() {},
      logout: function logout() {},
      reset: function reset() {},
      checkAuthStatus: function () {
        var _checkAuthStatus4 = (0, _asyncToGenerator2.default)(function* () {});
        function checkAuthStatus() {
          return _checkAuthStatus4.apply(this, arguments);
        }
        return checkAuthStatus;
      }(),
      validateToken: function () {
        var _validateToken4 = (0, _asyncToGenerator2.default)(function* () {
          return false;
        });
        function validateToken() {
          return _validateToken4.apply(this, arguments);
        }
        return validateToken;
      }()
    });
  }
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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