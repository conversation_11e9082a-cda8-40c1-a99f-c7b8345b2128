c9df8ba2062a204cbbcfa79aa92bda9d
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _NativePlatformConstantsIOS = _interopRequireDefault(require("./NativePlatformConstantsIOS"));
var Platform = {
  __constants: null,
  OS: 'ios',
  get Version() {
    return this.constants.osVersion;
  },
  get constants() {
    if (this.__constants == null) {
      this.__constants = _NativePlatformConstantsIOS.default.getConstants();
    }
    return this.__constants;
  },
  get isPad() {
    return this.constants.interfaceIdiom === 'pad';
  },
  get isTV() {
    return this.constants.interfaceIdiom === 'tv';
  },
  get isVision() {
    return this.constants.interfaceIdiom === 'vision';
  },
  get isTesting() {
    if (__DEV__) {
      return this.constants.isTesting;
    }
    return false;
  },
  get isDisableAnimations() {
    var _this$constants$isDis;
    return (_this$constants$isDis = this.constants.isDisableAnimations) != null ? _this$constants$isDis : this.isTesting;
  },
  get isMacCatalyst() {
    var _this$constants$isMac;
    return (_this$constants$isMac = this.constants.isMacCatalyst) != null ? _this$constants$isMac : false;
  },
  select: function select(spec) {
    return 'ios' in spec ? spec.ios : 'native' in spec ? spec.native : spec.default;
  }
};
var _default = exports.default = Platform;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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