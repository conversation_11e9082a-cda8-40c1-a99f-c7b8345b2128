/**
 * Messaging Service - Advanced Real-time Communication
 *
 * Service Contract:
 * - Provides comprehensive messaging functionality
 * - Handles real-time WebSocket communication
 * - Manages message delivery and read receipts
 * - Supports file attachments and media sharing
 * - Implements message encryption and security
 * - Handles offline message queuing
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { apiClient } from './apiClient';

export interface Message {
  id: string;
  conversationId: string;
  senderId: string;
  senderName: string;
  senderAvatar?: string;
  content: string;
  type: 'text' | 'image' | 'file' | 'location' | 'booking_update' | 'system';
  attachments?: MessageAttachment[];
  replyTo?: string;
  isEdited: boolean;
  editedAt?: string;
  deliveryStatus: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
  readBy: MessageReadReceipt[];
  reactions: MessageReaction[];
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface MessageAttachment {
  id: string;
  type: 'image' | 'file' | 'audio' | 'video';
  url: string;
  thumbnailUrl?: string;
  fileName: string;
  fileSize: number;
  mimeType: string;
  duration?: number; // for audio/video
  dimensions?: { width: number; height: number }; // for images/videos
}

export interface MessageReadReceipt {
  userId: string;
  userName: string;
  readAt: string;
}

export interface MessageReaction {
  userId: string;
  userName: string;
  emoji: string;
  createdAt: string;
}

export interface Conversation {
  id: string;
  type: 'direct' | 'group' | 'support';
  participants: ConversationParticipant[];
  title?: string;
  description?: string;
  avatar?: string;
  lastMessage?: Message;
  unreadCount: number;
  isArchived: boolean;
  isMuted: boolean;
  isPinned: boolean;
  settings: ConversationSettings;
  createdAt: string;
  updatedAt: string;
}

export interface ConversationParticipant {
  userId: string;
  userName: string;
  userAvatar?: string;
  role: 'admin' | 'member' | 'guest';
  isOnline: boolean;
  lastSeen?: string;
  joinedAt: string;
}

export interface ConversationSettings {
  allowFileSharing: boolean;
  allowLocationSharing: boolean;
  messageRetention: number; // days
  encryptionEnabled: boolean;
  notificationsEnabled: boolean;
}

export interface TypingIndicator {
  conversationId: string;
  userId: string;
  userName: string;
  isTyping: boolean;
  timestamp: string;
}

export interface MessageFilters {
  conversationId?: string;
  senderId?: string;
  type?: Message['type'];
  hasAttachments?: boolean;
  dateFrom?: string;
  dateTo?: string;
  searchQuery?: string;
}

class MessagingService {
  private readonly baseUrl = '/api/messaging';

  /**
   * Get conversations for current user
   */
  async getConversations(
    page: number = 1,
    limit: number = 20,
    archived: boolean = false
  ): Promise<{ conversations: Conversation[]; totalCount: number }> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/conversations/`, {
        params: { page, limit, archived },
      });

      // The backend returns an array directly, not an object with conversations property
      const conversations = Array.isArray(response.data) ? response.data : [];

      return {
        conversations,
        totalCount: conversations.length
      };
    } catch (error) {
      console.error('Failed to get conversations:', error);
      throw new Error('Failed to get conversations');
    }
  }

  /**
   * Get conversation by ID
   */
  async getConversation(conversationId: string): Promise<Conversation> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/conversations/${conversationId}/`);
      return response.data;
    } catch (error) {
      console.error('Failed to get conversation:', error);
      throw new Error('Failed to get conversation');
    }
  }

  /**
   * Create new conversation
   */
  async createConversation(
    participantIds: string[],
    type: Conversation['type'] = 'direct',
    title?: string
  ): Promise<Conversation> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/conversations/`, {
        participant_ids: participantIds,
        type,
        title,
      });

      return response.data;
    } catch (error) {
      console.error('Failed to create conversation:', error);
      throw new Error('Failed to create conversation');
    }
  }

  /**
   * Get messages for conversation
   */
  async getMessages(
    conversationId: string,
    page: number = 1,
    limit: number = 50,
    filters?: MessageFilters
  ): Promise<{ messages: Message[]; totalCount: number }> {
    try {
      const params = {
        page,
        limit,
        ...filters,
      };

      const response = await apiClient.get(
        `${this.baseUrl}/conversations/${conversationId}/messages/`,
        { params }
      );

      return response.data;
    } catch (error) {
      console.error('Failed to get messages:', error);
      throw new Error('Failed to get messages');
    }
  }

  /**
   * Send message
   */
  async sendMessage(
    conversationId: string,
    content: string,
    type: Message['type'] = 'text',
    attachments?: File[],
    replyTo?: string
  ): Promise<Message> {
    try {
      const formData = new FormData();
      formData.append('content', content);
      formData.append('type', type);
      
      if (replyTo) {
        formData.append('reply_to', replyTo);
      }

      if (attachments && attachments.length > 0) {
        attachments.forEach((file, index) => {
          formData.append(`attachments[${index}]`, file);
        });
      }

      const response = await apiClient.post(
        `${this.baseUrl}/conversations/${conversationId}/send_message/`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      return response.data;
    } catch (error) {
      console.error('Failed to send message:', error);
      throw new Error('Failed to send message');
    }
  }

  /**
   * Edit message
   */
  async editMessage(messageId: string, content: string): Promise<Message> {
    try {
      const response = await apiClient.patch(`${this.baseUrl}/messages/${messageId}/`, {
        content,
      });

      return response.data;
    } catch (error) {
      console.error('Failed to edit message:', error);
      throw new Error('Failed to edit message');
    }
  }

  /**
   * Delete message
   */
  async deleteMessage(messageId: string): Promise<void> {
    try {
      await apiClient.delete(`${this.baseUrl}/messages/${messageId}/`);
    } catch (error) {
      console.error('Failed to delete message:', error);
      throw new Error('Failed to delete message');
    }
  }

  /**
   * Mark messages as read
   */
  async markMessagesAsRead(conversationId: string, messageIds: string[]): Promise<void> {
    try {
      await apiClient.post(`${this.baseUrl}/conversations/${conversationId}/mark-read/`, {
        message_ids: messageIds,
      });
    } catch (error) {
      console.error('Failed to mark messages as read:', error);
      throw new Error('Failed to mark messages as read');
    }
  }

  /**
   * Add reaction to message
   */
  async addReaction(messageId: string, emoji: string): Promise<MessageReaction> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/messages/${messageId}/reactions/`, {
        emoji,
      });

      return response.data;
    } catch (error) {
      console.error('Failed to add reaction:', error);
      throw new Error('Failed to add reaction');
    }
  }

  /**
   * Remove reaction from message
   */
  async removeReaction(messageId: string, reactionId: string): Promise<void> {
    try {
      await apiClient.delete(`${this.baseUrl}/messages/${messageId}/reactions/${reactionId}/`);
    } catch (error) {
      console.error('Failed to remove reaction:', error);
      throw new Error('Failed to remove reaction');
    }
  }

  /**
   * Search messages
   */
  async searchMessages(
    query: string,
    conversationId?: string,
    limit: number = 20
  ): Promise<Message[]> {
    try {
      const params = {
        q: query,
        conversation_id: conversationId,
        limit,
      };

      const response = await apiClient.get(`${this.baseUrl}/search/`, { params });
      return response.data.messages;
    } catch (error) {
      console.error('Failed to search messages:', error);
      throw new Error('Failed to search messages');
    }
  }

  /**
   * Upload file attachment
   */
  async uploadAttachment(file: File): Promise<MessageAttachment> {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await apiClient.post(`${this.baseUrl}/attachments/`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    } catch (error) {
      console.error('Failed to upload attachment:', error);
      throw new Error('Failed to upload attachment');
    }
  }

  /**
   * Get conversation analytics
   */
  async getConversationAnalytics(conversationId: string): Promise<{
    messageCount: number;
    participantActivity: { userId: string; messageCount: number; lastActive: string }[];
    peakHours: { hour: number; messageCount: number }[];
    responseTime: number;
  }> {
    try {
      const response = await apiClient.get(
        `${this.baseUrl}/conversations/${conversationId}/analytics/`
      );

      return response.data;
    } catch (error) {
      console.error('Failed to get conversation analytics:', error);
      throw new Error('Failed to get conversation analytics');
    }
  }

  /**
   * Format message timestamp
   */
  formatMessageTime(timestamp: string): string {
    const messageDate = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - messageDate.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) {
      return 'Just now';
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`;
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)}h ago`;
    } else {
      return messageDate.toLocaleDateString();
    }
  }

  /**
   * Get file size display
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Validate message content
   */
  validateMessage(content: string, type: Message['type']): { isValid: boolean; error?: string } {
    if (!content.trim()) {
      return { isValid: false, error: 'Message cannot be empty' };
    }

    if (content.length > 5000) {
      return { isValid: false, error: 'Message is too long (max 5000 characters)' };
    }

    if (type === 'text' && content.includes('<script>')) {
      return { isValid: false, error: 'Invalid content detected' };
    }

    return { isValid: true };
  }
}

export const messagingService = new MessagingService();
