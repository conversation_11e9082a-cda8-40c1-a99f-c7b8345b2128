{"version": 3, "names": ["_getJest<PERSON>bj", "mock", "_interopRequireDefault", "require", "_asyncToGenerator2", "_slicedToArray2", "_react", "_reactNative", "_reactNative2", "_ErrorBoundary", "_performanceMonitor", "_jsxRuntime", "_require", "jest", "mockPerformanceMonitor", "performanceMonitor", "ErrorComponent", "Error", "ButtonThatThrows", "_ref", "onError", "_React$useState", "React", "useState", "_React$useState2", "default", "shouldThrow", "setShouldThrow", "error", "jsx", "<PERSON><PERSON>", "title", "onPress", "testID", "WorkingComponent", "View", "children", "Text", "describe", "beforeEach", "clearAllMocks", "spyOn", "console", "mockImplementation", "after<PERSON>ach", "restoreAllMocks", "it", "render", "Error<PERSON>ou<PERSON><PERSON>", "expect", "screen", "getByText", "toBeTruthy", "getByTestID", "fn", "toHaveBeenCalledWith", "objectContaining", "message", "trackUserInteraction", "componentStack", "any", "String", "CustomFallback", "_ref2", "retry", "jsxs", "fallback", "global", "__DEV__", "TestComponent", "_React$useState3", "_React$useState4", "shouldError", "setShouldError", "useEffect", "fireEvent", "press", "waitFor", "maxRetries", "i", "retryButton", "props", "disabled", "toBe", "TestWrapper", "_ref6", "_React$useState5", "_React$useState6", "key", "<PERSON><PERSON><PERSON>", "resetOnPropsChange", "k", "_render", "rerender", "OuterFallback", "InnerFallback", "queryByText", "toBeNull", "rn", "requireActual", "Object", "assign", "AccessibilityInfo", "announceForAccessibility", "mockAnnounce", "stringContaining", "<PERSON><PERSON><PERSON><PERSON>", "accessibilityRole", "accessibilityLabel"], "sources": ["ErrorBoundary.test.tsx"], "sourcesContent": ["/**\n * ErrorBoundary Component Tests\n *\n * Test Coverage:\n * - Error catching and handling\n * - Fallback UI rendering\n * - Reset functionality\n * - Retry mechanism\n * - Integration with performance monitoring\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\nimport React from 'react';\nimport { render, screen, fireEvent, waitFor } from '@testing-library/react-native';\nimport { Text, View, Button } from 'react-native';\nimport { ErrorBoundary } from '../ErrorBoundary';\nimport { performanceMonitor } from '../../../services/performanceMonitor';\n\n// Mock dependencies\njest.mock('../../../services/performanceMonitor');\n\nconst mockPerformanceMonitor = performanceMonitor as jest.Mocked<typeof performanceMonitor>;\n\n// Test components\nconst ErrorComponent: React.FC = () => {\n  throw new Error('Test error');\n  return null;\n};\n\nconst ButtonThatThrows: React.FC<{ onError?: () => void }> = ({ onError }) => {\n  const [shouldThrow, setShouldThrow] = React.useState(false);\n\n  if (shouldThrow) {\n    try {\n      throw new Error('Button error');\n    } catch (error) {\n      if (onError) onError();\n      throw error;\n    }\n  }\n\n  return (\n    <Button\n      title=\"Throw Error\"\n      onPress={() => setShouldThrow(true)}\n      testID=\"throw-button\"\n    />\n  );\n};\n\nconst WorkingComponent: React.FC = () => (\n  <View testID=\"working-component\">\n    <Text>Working Component</Text>\n  </View>\n);\n\ndescribe('ErrorBoundary', () => {\n  beforeEach(() => {\n    jest.clearAllMocks();\n    \n    // Suppress React error boundary warnings in tests\n    jest.spyOn(console, 'error').mockImplementation(() => {});\n  });\n\n  afterEach(() => {\n    jest.restoreAllMocks();\n  });\n\n  describe('Error Handling', () => {\n    it('catches errors in child components', () => {\n      render(\n        <ErrorBoundary>\n          <ErrorComponent />\n        </ErrorBoundary>\n      );\n\n      // Should render fallback UI instead of crashing\n      expect(screen.getByText('Something went wrong')).toBeTruthy();\n    });\n\n    it('renders children normally when no errors occur', () => {\n      render(\n        <ErrorBoundary>\n          <WorkingComponent />\n        </ErrorBoundary>\n      );\n\n      expect(screen.getByTestID('working-component')).toBeTruthy();\n      expect(screen.getByText('Working Component')).toBeTruthy();\n    });\n\n    it('calls onError callback when an error occurs', () => {\n      const onError = jest.fn();\n\n      render(\n        <ErrorBoundary onError={onError}>\n          <ErrorComponent />\n        </ErrorBoundary>\n      );\n\n      expect(onError).toHaveBeenCalledWith(expect.objectContaining({\n        message: 'Test error',\n      }));\n    });\n\n    it('tracks errors with performance monitor', () => {\n      render(\n        <ErrorBoundary>\n          <ErrorComponent />\n        </ErrorBoundary>\n      );\n\n      expect(mockPerformanceMonitor.trackUserInteraction).toHaveBeenCalledWith(\n        'error_boundary_catch',\n        0,\n        expect.objectContaining({\n          error: 'Test error',\n          componentStack: expect.any(String),\n        })\n      );\n    });\n  });\n\n  describe('Fallback UI', () => {\n    it('renders default fallback UI', () => {\n      render(\n        <ErrorBoundary>\n          <ErrorComponent />\n        </ErrorBoundary>\n      );\n\n      expect(screen.getByText('Something went wrong')).toBeTruthy();\n      expect(screen.getByText(/We're sorry, but an error occurred/)).toBeTruthy();\n      expect(screen.getByText('Try Again')).toBeTruthy();\n    });\n\n    it('renders custom fallback UI when provided', () => {\n      const CustomFallback = ({ error, retry }: { error: Error, retry: () => void }) => (\n        <View testID=\"custom-fallback\">\n          <Text>Custom Error: {error.message}</Text>\n          <Button title=\"Custom Retry\" onPress={retry} testID=\"custom-retry\" />\n        </View>\n      );\n\n      render(\n        <ErrorBoundary fallback={(error, retry) => <CustomFallback error={error} retry={retry} />}>\n          <ErrorComponent />\n        </ErrorBoundary>\n      );\n\n      expect(screen.getByTestID('custom-fallback')).toBeTruthy();\n      expect(screen.getByText('Custom Error: Test error')).toBeTruthy();\n      expect(screen.getByText('Custom Retry')).toBeTruthy();\n    });\n\n    it('shows technical details in development mode', () => {\n      // Mock __DEV__ to be true\n      global.__DEV__ = true;\n\n      render(\n        <ErrorBoundary>\n          <ErrorComponent />\n        </ErrorBoundary>\n      );\n\n      expect(screen.getByText(/Technical Details/)).toBeTruthy();\n      expect(screen.getByText('Test error')).toBeTruthy();\n\n      // Reset __DEV__\n      global.__DEV__ = false;\n    });\n  });\n\n  describe('Reset Functionality', () => {\n    it('resets error state when retry button is pressed', async () => {\n      const TestComponent = () => {\n        const [shouldError, setShouldError] = React.useState(true);\n\n        React.useEffect(() => {\n          // After first render with error, set up to not error on next render\n          if (shouldError) {\n            setShouldError(false);\n          }\n        }, [shouldError]);\n\n        if (shouldError) {\n          throw new Error('Initial error');\n        }\n\n        return <Text>Recovered Component</Text>;\n      };\n\n      render(\n        <ErrorBoundary>\n          <TestComponent />\n        </ErrorBoundary>\n      );\n\n      // Initially shows error\n      expect(screen.getByText('Something went wrong')).toBeTruthy();\n\n      // Press retry button\n      fireEvent.press(screen.getByText('Try Again'));\n\n      // Should recover and render component\n      await waitFor(() => {\n        expect(screen.getByText('Recovered Component')).toBeTruthy();\n      });\n    });\n\n    it('respects maxRetries limit', async () => {\n      const maxRetries = 2;\n      \n      render(\n        <ErrorBoundary maxRetries={maxRetries}>\n          <ErrorComponent />\n        </ErrorBoundary>\n      );\n\n      // Press retry button multiple times\n      for (let i = 0; i < maxRetries; i++) {\n        fireEvent.press(screen.getByText('Try Again'));\n      }\n\n      // After max retries, button should be disabled\n      const retryButton = screen.getByText('Try Again');\n      expect(retryButton.props.disabled).toBe(true);\n    });\n\n    it('resets on props change when resetOnPropsChange is true', async () => {\n      const TestWrapper = ({ children }: { children: React.ReactNode }) => {\n        const [key, setKey] = React.useState(1);\n\n        return (\n          <View>\n            <ErrorBoundary resetOnPropsChange>\n              {children}\n            </ErrorBoundary>\n            <Button \n              title=\"Change Props\" \n              onPress={() => setKey(k => k + 1)} \n              testID=\"change-props\"\n            />\n          </View>\n        );\n      };\n\n      const { rerender } = render(\n        <TestWrapper>\n          <ErrorComponent />\n        </TestWrapper>\n      );\n\n      // Initially shows error\n      expect(screen.getByText('Something went wrong')).toBeTruthy();\n\n      // Change props\n      fireEvent.press(screen.getByTestID('change-props'));\n\n      // Should attempt to re-render, but still error\n      expect(screen.getByText('Something went wrong')).toBeTruthy();\n    });\n  });\n\n  describe('Dynamic Error Handling', () => {\n    it('catches runtime errors from user interactions', async () => {\n      const onError = jest.fn();\n\n      render(\n        <ErrorBoundary onError={onError}>\n          <ButtonThatThrows onError={onError} />\n        </ErrorBoundary>\n      );\n\n      // Initially renders without error\n      expect(screen.getByTestID('throw-button')).toBeTruthy();\n\n      // Trigger error\n      fireEvent.press(screen.getByTestID('throw-button'));\n\n      // Should show error boundary fallback\n      await waitFor(() => {\n        expect(screen.getByText('Something went wrong')).toBeTruthy();\n      });\n\n      expect(onError).toHaveBeenCalledWith(expect.objectContaining({\n        message: 'Button error',\n      }));\n    });\n\n    it('handles nested error boundaries correctly', () => {\n      const OuterFallback = () => <Text>Outer Error</Text>;\n      const InnerFallback = () => <Text>Inner Error</Text>;\n\n      render(\n        <ErrorBoundary fallback={() => <OuterFallback />}>\n          <View>\n            <Text>Outer Content</Text>\n            <ErrorBoundary fallback={() => <InnerFallback />}>\n              <ErrorComponent />\n            </ErrorBoundary>\n          </View>\n        </ErrorBoundary>\n      );\n\n      // Inner error boundary should catch the error\n      expect(screen.getByText('Inner Error')).toBeTruthy();\n      expect(screen.getByText('Outer Content')).toBeTruthy();\n      expect(screen.queryByText('Outer Error')).toBeNull();\n    });\n  });\n\n  describe('Accessibility', () => {\n    it('announces errors to screen readers', () => {\n      const mockAnnounce = jest.fn();\n      \n      // Mock AccessibilityInfo\n      jest.mock('react-native', () => {\n        const rn = jest.requireActual('react-native');\n        return {\n          ...rn,\n          AccessibilityInfo: {\n            ...rn.AccessibilityInfo,\n            announceForAccessibility: mockAnnounce,\n          },\n        };\n      });\n\n      render(\n        <ErrorBoundary>\n          <ErrorComponent />\n        </ErrorBoundary>\n      );\n\n      expect(mockAnnounce).toHaveBeenCalledWith(\n        expect.stringContaining('error occurred')\n      );\n    });\n\n    it('provides proper accessibility props on fallback UI', () => {\n      render(\n        <ErrorBoundary>\n          <ErrorComponent />\n        </ErrorBoundary>\n      );\n\n      const errorContainer = screen.getByTestID('error-boundary-container');\n      expect(errorContainer.props.accessibilityRole).toBe('alert');\n      \n      const retryButton = screen.getByText('Try Again');\n      expect(retryButton.props.accessibilityRole).toBe('button');\n      expect(retryButton.props.accessibilityLabel).toBeTruthy();\n    });\n  });\n});\n"], "mappings": "AAqBAA,WAAA,GAAKC,IAAI,uCAAuC,CAAC;AAAC,IAAAC,sBAAA,GAAAC,OAAA;AAAA,IAAAC,kBAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAA,IAAAE,eAAA,GAAAH,sBAAA,CAAAC,OAAA;AAPlD,IAAAG,MAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,YAAA,GAAAJ,OAAA;AACA,IAAAK,aAAA,GAAAL,OAAA;AACA,IAAAM,cAAA,GAAAN,OAAA;AACA,IAAAO,mBAAA,GAAAP,OAAA;AAA0E,IAAAQ,WAAA,GAAAR,OAAA;AAAA,SAAAH,YAAA;EAAA,IAAAY,QAAA,GAAAT,OAAA;IAAAU,IAAA,GAAAD,QAAA,CAAAC,IAAA;EAAAb,WAAA,YAAAA,YAAA;IAAA,OAAAa,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAK1E,IAAMC,sBAAsB,GAAGC,sCAA4D;AAG3F,IAAMC,cAAwB,GAAG,SAA3BA,cAAwBA,CAAA,EAAS;EACrC,MAAM,IAAIC,KAAK,CAAC,YAAY,CAAC;EAC7B,OAAO,IAAI;AACb,CAAC;AAED,IAAMC,gBAAoD,GAAG,SAAvDA,gBAAoDA,CAAAC,IAAA,EAAoB;EAAA,IAAdC,OAAO,GAAAD,IAAA,CAAPC,OAAO;EACrE,IAAAC,eAAA,GAAsCC,cAAK,CAACC,QAAQ,CAAC,KAAK,CAAC;IAAAC,gBAAA,OAAAnB,eAAA,CAAAoB,OAAA,EAAAJ,eAAA;IAApDK,WAAW,GAAAF,gBAAA;IAAEG,cAAc,GAAAH,gBAAA;EAElC,IAAIE,WAAW,EAAE;IACf,IAAI;MACF,MAAM,IAAIT,KAAK,CAAC,cAAc,CAAC;IACjC,CAAC,CAAC,OAAOW,KAAK,EAAE;MACd,IAAIR,OAAO,EAAEA,OAAO,CAAC,CAAC;MACtB,MAAMQ,KAAK;IACb;EACF;EAEA,OACE,IAAAjB,WAAA,CAAAkB,GAAA,EAACrB,aAAA,CAAAsB,MAAM;IACLC,KAAK,EAAC,aAAa;IACnBC,OAAO,EAAE,SAATA,OAAOA,CAAA;MAAA,OAAQL,cAAc,CAAC,IAAI,CAAC;IAAA,CAAC;IACpCM,MAAM,EAAC;EAAc,CACtB,CAAC;AAEN,CAAC;AAED,IAAMC,gBAA0B,GAAG,SAA7BA,gBAA0BA,CAAA;EAAA,OAC9B,IAAAvB,WAAA,CAAAkB,GAAA,EAACrB,aAAA,CAAA2B,IAAI;IAACF,MAAM,EAAC,mBAAmB;IAAAG,QAAA,EAC9B,IAAAzB,WAAA,CAAAkB,GAAA,EAACrB,aAAA,CAAA6B,IAAI;MAAAD,QAAA,EAAC;IAAiB,CAAM;EAAC,CAC1B,CAAC;AAAA,CACR;AAEDE,QAAQ,CAAC,eAAe,EAAE,YAAM;EAC9BC,UAAU,CAAC,YAAM;IACf1B,IAAI,CAAC2B,aAAa,CAAC,CAAC;IAGpB3B,IAAI,CAAC4B,KAAK,CAACC,OAAO,EAAE,OAAO,CAAC,CAACC,kBAAkB,CAAC,YAAM,CAAC,CAAC,CAAC;EAC3D,CAAC,CAAC;EAEFC,SAAS,CAAC,YAAM;IACd/B,IAAI,CAACgC,eAAe,CAAC,CAAC;EACxB,CAAC,CAAC;EAEFP,QAAQ,CAAC,gBAAgB,EAAE,YAAM;IAC/BQ,EAAE,CAAC,oCAAoC,EAAE,YAAM;MAC7C,IAAAC,mBAAM,EACJ,IAAApC,WAAA,CAAAkB,GAAA,EAACpB,cAAA,CAAAuC,aAAa;QAAAZ,QAAA,EACZ,IAAAzB,WAAA,CAAAkB,GAAA,EAACb,cAAc,IAAE;MAAC,CACL,CACjB,CAAC;MAGDiC,MAAM,CAACC,mBAAM,CAACC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAACC,UAAU,CAAC,CAAC;IAC/D,CAAC,CAAC;IAEFN,EAAE,CAAC,gDAAgD,EAAE,YAAM;MACzD,IAAAC,mBAAM,EACJ,IAAApC,WAAA,CAAAkB,GAAA,EAACpB,cAAA,CAAAuC,aAAa;QAAAZ,QAAA,EACZ,IAAAzB,WAAA,CAAAkB,GAAA,EAACK,gBAAgB,IAAE;MAAC,CACP,CACjB,CAAC;MAEDe,MAAM,CAACC,mBAAM,CAACG,WAAW,CAAC,mBAAmB,CAAC,CAAC,CAACD,UAAU,CAAC,CAAC;MAC5DH,MAAM,CAACC,mBAAM,CAACC,SAAS,CAAC,mBAAmB,CAAC,CAAC,CAACC,UAAU,CAAC,CAAC;IAC5D,CAAC,CAAC;IAEFN,EAAE,CAAC,6CAA6C,EAAE,YAAM;MACtD,IAAM1B,OAAO,GAAGP,IAAI,CAACyC,EAAE,CAAC,CAAC;MAEzB,IAAAP,mBAAM,EACJ,IAAApC,WAAA,CAAAkB,GAAA,EAACpB,cAAA,CAAAuC,aAAa;QAAC5B,OAAO,EAAEA,OAAQ;QAAAgB,QAAA,EAC9B,IAAAzB,WAAA,CAAAkB,GAAA,EAACb,cAAc,IAAE;MAAC,CACL,CACjB,CAAC;MAEDiC,MAAM,CAAC7B,OAAO,CAAC,CAACmC,oBAAoB,CAACN,MAAM,CAACO,gBAAgB,CAAC;QAC3DC,OAAO,EAAE;MACX,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IAEFX,EAAE,CAAC,wCAAwC,EAAE,YAAM;MACjD,IAAAC,mBAAM,EACJ,IAAApC,WAAA,CAAAkB,GAAA,EAACpB,cAAA,CAAAuC,aAAa;QAAAZ,QAAA,EACZ,IAAAzB,WAAA,CAAAkB,GAAA,EAACb,cAAc,IAAE;MAAC,CACL,CACjB,CAAC;MAEDiC,MAAM,CAACnC,sBAAsB,CAAC4C,oBAAoB,CAAC,CAACH,oBAAoB,CACtE,sBAAsB,EACtB,CAAC,EACDN,MAAM,CAACO,gBAAgB,CAAC;QACtB5B,KAAK,EAAE,YAAY;QACnB+B,cAAc,EAAEV,MAAM,CAACW,GAAG,CAACC,MAAM;MACnC,CAAC,CACH,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFvB,QAAQ,CAAC,aAAa,EAAE,YAAM;IAC5BQ,EAAE,CAAC,6BAA6B,EAAE,YAAM;MACtC,IAAAC,mBAAM,EACJ,IAAApC,WAAA,CAAAkB,GAAA,EAACpB,cAAA,CAAAuC,aAAa;QAAAZ,QAAA,EACZ,IAAAzB,WAAA,CAAAkB,GAAA,EAACb,cAAc,IAAE;MAAC,CACL,CACjB,CAAC;MAEDiC,MAAM,CAACC,mBAAM,CAACC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAACC,UAAU,CAAC,CAAC;MAC7DH,MAAM,CAACC,mBAAM,CAACC,SAAS,CAAC,oCAAoC,CAAC,CAAC,CAACC,UAAU,CAAC,CAAC;MAC3EH,MAAM,CAACC,mBAAM,CAACC,SAAS,CAAC,WAAW,CAAC,CAAC,CAACC,UAAU,CAAC,CAAC;IACpD,CAAC,CAAC;IAEFN,EAAE,CAAC,0CAA0C,EAAE,YAAM;MACnD,IAAMgB,cAAc,GAAG,SAAjBA,cAAcA,CAAAC,KAAA;QAAA,IAAMnC,KAAK,GAAAmC,KAAA,CAALnC,KAAK;UAAEoC,KAAK,GAAAD,KAAA,CAALC,KAAK;QAAA,OACpC,IAAArD,WAAA,CAAAsD,IAAA,EAACzD,aAAA,CAAA2B,IAAI;UAACF,MAAM,EAAC,iBAAiB;UAAAG,QAAA,GAC5B,IAAAzB,WAAA,CAAAsD,IAAA,EAACzD,aAAA,CAAA6B,IAAI;YAAAD,QAAA,GAAC,gBAAc,EAACR,KAAK,CAAC6B,OAAO;UAAA,CAAO,CAAC,EAC1C,IAAA9C,WAAA,CAAAkB,GAAA,EAACrB,aAAA,CAAAsB,MAAM;YAACC,KAAK,EAAC,cAAc;YAACC,OAAO,EAAEgC,KAAM;YAAC/B,MAAM,EAAC;UAAc,CAAE,CAAC;QAAA,CACjE,CAAC;MAAA,CACR;MAED,IAAAc,mBAAM,EACJ,IAAApC,WAAA,CAAAkB,GAAA,EAACpB,cAAA,CAAAuC,aAAa;QAACkB,QAAQ,EAAE,SAAVA,QAAQA,CAAGtC,KAAK,EAAEoC,KAAK;UAAA,OAAK,IAAArD,WAAA,CAAAkB,GAAA,EAACiC,cAAc;YAAClC,KAAK,EAAEA,KAAM;YAACoC,KAAK,EAAEA;UAAM,CAAE,CAAC;QAAA,CAAC;QAAA5B,QAAA,EACxF,IAAAzB,WAAA,CAAAkB,GAAA,EAACb,cAAc,IAAE;MAAC,CACL,CACjB,CAAC;MAEDiC,MAAM,CAACC,mBAAM,CAACG,WAAW,CAAC,iBAAiB,CAAC,CAAC,CAACD,UAAU,CAAC,CAAC;MAC1DH,MAAM,CAACC,mBAAM,CAACC,SAAS,CAAC,0BAA0B,CAAC,CAAC,CAACC,UAAU,CAAC,CAAC;MACjEH,MAAM,CAACC,mBAAM,CAACC,SAAS,CAAC,cAAc,CAAC,CAAC,CAACC,UAAU,CAAC,CAAC;IACvD,CAAC,CAAC;IAEFN,EAAE,CAAC,6CAA6C,EAAE,YAAM;MAEtDqB,MAAM,CAACC,OAAO,GAAG,IAAI;MAErB,IAAArB,mBAAM,EACJ,IAAApC,WAAA,CAAAkB,GAAA,EAACpB,cAAA,CAAAuC,aAAa;QAAAZ,QAAA,EACZ,IAAAzB,WAAA,CAAAkB,GAAA,EAACb,cAAc,IAAE;MAAC,CACL,CACjB,CAAC;MAEDiC,MAAM,CAACC,mBAAM,CAACC,SAAS,CAAC,mBAAmB,CAAC,CAAC,CAACC,UAAU,CAAC,CAAC;MAC1DH,MAAM,CAACC,mBAAM,CAACC,SAAS,CAAC,YAAY,CAAC,CAAC,CAACC,UAAU,CAAC,CAAC;MAGnDe,MAAM,CAACC,OAAO,GAAG,KAAK;IACxB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF9B,QAAQ,CAAC,qBAAqB,EAAE,YAAM;IACpCQ,EAAE,CAAC,iDAAiD,MAAA1C,kBAAA,CAAAqB,OAAA,EAAE,aAAY;MAChE,IAAM4C,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;QAC1B,IAAAC,gBAAA,GAAsChD,cAAK,CAACC,QAAQ,CAAC,IAAI,CAAC;UAAAgD,gBAAA,OAAAlE,eAAA,CAAAoB,OAAA,EAAA6C,gBAAA;UAAnDE,WAAW,GAAAD,gBAAA;UAAEE,cAAc,GAAAF,gBAAA;QAElCjD,cAAK,CAACoD,SAAS,CAAC,YAAM;UAEpB,IAAIF,WAAW,EAAE;YACfC,cAAc,CAAC,KAAK,CAAC;UACvB;QACF,CAAC,EAAE,CAACD,WAAW,CAAC,CAAC;QAEjB,IAAIA,WAAW,EAAE;UACf,MAAM,IAAIvD,KAAK,CAAC,eAAe,CAAC;QAClC;QAEA,OAAO,IAAAN,WAAA,CAAAkB,GAAA,EAACrB,aAAA,CAAA6B,IAAI;UAAAD,QAAA,EAAC;QAAmB,CAAM,CAAC;MACzC,CAAC;MAED,IAAAW,mBAAM,EACJ,IAAApC,WAAA,CAAAkB,GAAA,EAACpB,cAAA,CAAAuC,aAAa;QAAAZ,QAAA,EACZ,IAAAzB,WAAA,CAAAkB,GAAA,EAACwC,aAAa,IAAE;MAAC,CACJ,CACjB,CAAC;MAGDpB,MAAM,CAACC,mBAAM,CAACC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAACC,UAAU,CAAC,CAAC;MAG7DuB,sBAAS,CAACC,KAAK,CAAC1B,mBAAM,CAACC,SAAS,CAAC,WAAW,CAAC,CAAC;MAG9C,MAAM,IAAA0B,oBAAO,EAAC,YAAM;QAClB5B,MAAM,CAACC,mBAAM,CAACC,SAAS,CAAC,qBAAqB,CAAC,CAAC,CAACC,UAAU,CAAC,CAAC;MAC9D,CAAC,CAAC;IACJ,CAAC,EAAC;IAEFN,EAAE,CAAC,2BAA2B,MAAA1C,kBAAA,CAAAqB,OAAA,EAAE,aAAY;MAC1C,IAAMqD,UAAU,GAAG,CAAC;MAEpB,IAAA/B,mBAAM,EACJ,IAAApC,WAAA,CAAAkB,GAAA,EAACpB,cAAA,CAAAuC,aAAa;QAAC8B,UAAU,EAAEA,UAAW;QAAA1C,QAAA,EACpC,IAAAzB,WAAA,CAAAkB,GAAA,EAACb,cAAc,IAAE;MAAC,CACL,CACjB,CAAC;MAGD,KAAK,IAAI+D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,UAAU,EAAEC,CAAC,EAAE,EAAE;QACnCJ,sBAAS,CAACC,KAAK,CAAC1B,mBAAM,CAACC,SAAS,CAAC,WAAW,CAAC,CAAC;MAChD;MAGA,IAAM6B,WAAW,GAAG9B,mBAAM,CAACC,SAAS,CAAC,WAAW,CAAC;MACjDF,MAAM,CAAC+B,WAAW,CAACC,KAAK,CAACC,QAAQ,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;IAC/C,CAAC,EAAC;IAEFrC,EAAE,CAAC,wDAAwD,MAAA1C,kBAAA,CAAAqB,OAAA,EAAE,aAAY;MACvE,IAAM2D,WAAW,GAAG,SAAdA,WAAWA,CAAAC,KAAA,EAAoD;QAAA,IAA9CjD,QAAQ,GAAAiD,KAAA,CAARjD,QAAQ;QAC7B,IAAAkD,gBAAA,GAAsBhE,cAAK,CAACC,QAAQ,CAAC,CAAC,CAAC;UAAAgE,gBAAA,OAAAlF,eAAA,CAAAoB,OAAA,EAAA6D,gBAAA;UAAhCE,GAAG,GAAAD,gBAAA;UAAEE,MAAM,GAAAF,gBAAA;QAElB,OACE,IAAA5E,WAAA,CAAAsD,IAAA,EAACzD,aAAA,CAAA2B,IAAI;UAAAC,QAAA,GACH,IAAAzB,WAAA,CAAAkB,GAAA,EAACpB,cAAA,CAAAuC,aAAa;YAAC0C,kBAAkB;YAAAtD,QAAA,EAC9BA;UAAQ,CACI,CAAC,EAChB,IAAAzB,WAAA,CAAAkB,GAAA,EAACrB,aAAA,CAAAsB,MAAM;YACLC,KAAK,EAAC,cAAc;YACpBC,OAAO,EAAE,SAATA,OAAOA,CAAA;cAAA,OAAQyD,MAAM,CAAC,UAAAE,CAAC;gBAAA,OAAIA,CAAC,GAAG,CAAC;cAAA,EAAC;YAAA,CAAC;YAClC1D,MAAM,EAAC;UAAc,CACtB,CAAC;QAAA,CACE,CAAC;MAEX,CAAC;MAED,IAAA2D,OAAA,GAAqB,IAAA7C,mBAAM,EACzB,IAAApC,WAAA,CAAAkB,GAAA,EAACuD,WAAW;UAAAhD,QAAA,EACV,IAAAzB,WAAA,CAAAkB,GAAA,EAACb,cAAc,IAAE;QAAC,CACP,CACf,CAAC;QAJO6E,QAAQ,GAAAD,OAAA,CAARC,QAAQ;MAOhB5C,MAAM,CAACC,mBAAM,CAACC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAACC,UAAU,CAAC,CAAC;MAG7DuB,sBAAS,CAACC,KAAK,CAAC1B,mBAAM,CAACG,WAAW,CAAC,cAAc,CAAC,CAAC;MAGnDJ,MAAM,CAACC,mBAAM,CAACC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAACC,UAAU,CAAC,CAAC;IAC/D,CAAC,EAAC;EACJ,CAAC,CAAC;EAEFd,QAAQ,CAAC,wBAAwB,EAAE,YAAM;IACvCQ,EAAE,CAAC,+CAA+C,MAAA1C,kBAAA,CAAAqB,OAAA,EAAE,aAAY;MAC9D,IAAML,OAAO,GAAGP,IAAI,CAACyC,EAAE,CAAC,CAAC;MAEzB,IAAAP,mBAAM,EACJ,IAAApC,WAAA,CAAAkB,GAAA,EAACpB,cAAA,CAAAuC,aAAa;QAAC5B,OAAO,EAAEA,OAAQ;QAAAgB,QAAA,EAC9B,IAAAzB,WAAA,CAAAkB,GAAA,EAACX,gBAAgB;UAACE,OAAO,EAAEA;QAAQ,CAAE;MAAC,CACzB,CACjB,CAAC;MAGD6B,MAAM,CAACC,mBAAM,CAACG,WAAW,CAAC,cAAc,CAAC,CAAC,CAACD,UAAU,CAAC,CAAC;MAGvDuB,sBAAS,CAACC,KAAK,CAAC1B,mBAAM,CAACG,WAAW,CAAC,cAAc,CAAC,CAAC;MAGnD,MAAM,IAAAwB,oBAAO,EAAC,YAAM;QAClB5B,MAAM,CAACC,mBAAM,CAACC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAACC,UAAU,CAAC,CAAC;MAC/D,CAAC,CAAC;MAEFH,MAAM,CAAC7B,OAAO,CAAC,CAACmC,oBAAoB,CAACN,MAAM,CAACO,gBAAgB,CAAC;QAC3DC,OAAO,EAAE;MACX,CAAC,CAAC,CAAC;IACL,CAAC,EAAC;IAEFX,EAAE,CAAC,2CAA2C,EAAE,YAAM;MACpD,IAAMgD,aAAa,GAAG,SAAhBA,aAAaA,CAAA;QAAA,OAAS,IAAAnF,WAAA,CAAAkB,GAAA,EAACrB,aAAA,CAAA6B,IAAI;UAAAD,QAAA,EAAC;QAAW,CAAM,CAAC;MAAA;MACpD,IAAM2D,aAAa,GAAG,SAAhBA,aAAaA,CAAA;QAAA,OAAS,IAAApF,WAAA,CAAAkB,GAAA,EAACrB,aAAA,CAAA6B,IAAI;UAAAD,QAAA,EAAC;QAAW,CAAM,CAAC;MAAA;MAEpD,IAAAW,mBAAM,EACJ,IAAApC,WAAA,CAAAkB,GAAA,EAACpB,cAAA,CAAAuC,aAAa;QAACkB,QAAQ,EAAE,SAAVA,QAAQA,CAAA;UAAA,OAAQ,IAAAvD,WAAA,CAAAkB,GAAA,EAACiE,aAAa,IAAE,CAAC;QAAA,CAAC;QAAA1D,QAAA,EAC/C,IAAAzB,WAAA,CAAAsD,IAAA,EAACzD,aAAA,CAAA2B,IAAI;UAAAC,QAAA,GACH,IAAAzB,WAAA,CAAAkB,GAAA,EAACrB,aAAA,CAAA6B,IAAI;YAAAD,QAAA,EAAC;UAAa,CAAM,CAAC,EAC1B,IAAAzB,WAAA,CAAAkB,GAAA,EAACpB,cAAA,CAAAuC,aAAa;YAACkB,QAAQ,EAAE,SAAVA,QAAQA,CAAA;cAAA,OAAQ,IAAAvD,WAAA,CAAAkB,GAAA,EAACkE,aAAa,IAAE,CAAC;YAAA,CAAC;YAAA3D,QAAA,EAC/C,IAAAzB,WAAA,CAAAkB,GAAA,EAACb,cAAc,IAAE;UAAC,CACL,CAAC;QAAA,CACZ;MAAC,CACM,CACjB,CAAC;MAGDiC,MAAM,CAACC,mBAAM,CAACC,SAAS,CAAC,aAAa,CAAC,CAAC,CAACC,UAAU,CAAC,CAAC;MACpDH,MAAM,CAACC,mBAAM,CAACC,SAAS,CAAC,eAAe,CAAC,CAAC,CAACC,UAAU,CAAC,CAAC;MACtDH,MAAM,CAACC,mBAAM,CAAC8C,WAAW,CAAC,aAAa,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;IACtD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF3D,QAAQ,CAAC,eAAe,EAAE,YAAM;IAC9BQ,EAAE,CAAC,oCAAoC,EAAE,YAAM;MAI7C9C,WAAA,GAAKC,IAAI,CAAC,cAAc,EAAE,YAAM;QAC9B,IAAMiG,EAAE,GAAGrF,IAAI,CAACsF,aAAa,CAAC,cAAc,CAAC;QAC7C,OAAAC,MAAA,CAAAC,MAAA,KACKH,EAAE;UACLI,iBAAiB,EAAAF,MAAA,CAAAC,MAAA,KACZH,EAAE,CAACI,iBAAiB;YACvBC,wBAAwB,EAAEC;UAAY;QACvC;MAEL,CAAC,CAAC;MAZF,IAAMA,YAAY,GAAG3F,IAAI,CAACyC,EAAE,CAAC,CAAC;MAc9B,IAAAP,mBAAM,EACJ,IAAApC,WAAA,CAAAkB,GAAA,EAACpB,cAAA,CAAAuC,aAAa;QAAAZ,QAAA,EACZ,IAAAzB,WAAA,CAAAkB,GAAA,EAACb,cAAc,IAAE;MAAC,CACL,CACjB,CAAC;MAEDiC,MAAM,CAACuD,YAAY,CAAC,CAACjD,oBAAoB,CACvCN,MAAM,CAACwD,gBAAgB,CAAC,gBAAgB,CAC1C,CAAC;IACH,CAAC,CAAC;IAEF3D,EAAE,CAAC,oDAAoD,EAAE,YAAM;MAC7D,IAAAC,mBAAM,EACJ,IAAApC,WAAA,CAAAkB,GAAA,EAACpB,cAAA,CAAAuC,aAAa;QAAAZ,QAAA,EACZ,IAAAzB,WAAA,CAAAkB,GAAA,EAACb,cAAc,IAAE;MAAC,CACL,CACjB,CAAC;MAED,IAAM0F,cAAc,GAAGxD,mBAAM,CAACG,WAAW,CAAC,0BAA0B,CAAC;MACrEJ,MAAM,CAACyD,cAAc,CAACzB,KAAK,CAAC0B,iBAAiB,CAAC,CAACxB,IAAI,CAAC,OAAO,CAAC;MAE5D,IAAMH,WAAW,GAAG9B,mBAAM,CAACC,SAAS,CAAC,WAAW,CAAC;MACjDF,MAAM,CAAC+B,WAAW,CAACC,KAAK,CAAC0B,iBAAiB,CAAC,CAACxB,IAAI,CAAC,QAAQ,CAAC;MAC1DlC,MAAM,CAAC+B,WAAW,CAACC,KAAK,CAAC2B,kBAAkB,CAAC,CAACxD,UAAU,CAAC,CAAC;IAC3D,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}