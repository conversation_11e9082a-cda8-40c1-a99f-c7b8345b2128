be45eece5c9646f4456a1f1776a0e6b7
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useNavigationPerformance = exports.useNavigationGuard = exports.useNavigationAnalytics = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _native = require("@react-navigation/native");
var _react = require("react");
var _authSlice = require("../store/authSlice");
var _navigationAnalytics = _interopRequireDefault(require("../services/navigationAnalytics"));
var _navigationGuards = _interopRequireDefault(require("../services/navigationGuards"));
var useNavigationGuard = exports.useNavigationGuard = function useNavigationGuard() {
  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  var _options$trackAnalyti = options.trackAnalytics,
    trackAnalytics = _options$trackAnalyti === void 0 ? true : _options$trackAnalyti,
    _options$enforceGuard = options.enforceGuards,
    enforceGuards = _options$enforceGuard === void 0 ? true : _options$enforceGuard,
    _options$logNavigatio = options.logNavigation,
    logNavigation = _options$logNavigatio === void 0 ? __DEV__ : _options$logNavigatio;
  var navigation = (0, _native.useNavigation)();
  var route = (0, _native.useRoute)();
  var _useAuthStore = (0, _authSlice.useAuthStore)(),
    userRole = _useAuthStore.userRole;
  var isNavigatingRef = (0, _react.useRef)(false);
  var screenStartTimeRef = (0, _react.useRef)(0);
  var currentScreenRef = (0, _react.useRef)(route.name);
  (0, _native.useFocusEffect)((0, _react.useCallback)(function () {
    var screenName = route.name;
    var startTime = Date.now();
    screenStartTimeRef.current = startTime;
    currentScreenRef.current = screenName;
    if (trackAnalytics) {
      _navigationAnalytics.default.trackScreenView(screenName, route.params, userRole);
      var loadTime = startTime - (screenStartTimeRef.current || startTime);
      if (loadTime > 0) {
        _navigationAnalytics.default.trackScreenLoadTime(screenName, loadTime);
      }
    }
    if (logNavigation) {
      console.log(`🧭 Navigation: Focused on ${screenName}`, route.params);
    }
    return function () {
      if (trackAnalytics && screenStartTimeRef.current > 0) {
        var timeSpent = Date.now() - screenStartTimeRef.current;
      }
    };
  }, [route.name, route.params, trackAnalytics, userRole, logNavigation]));
  var navigate = (0, _react.useCallback)(function () {
    var _ref = (0, _asyncToGenerator2.default)(function* (routeName, params) {
      if (isNavigatingRef.current) {
        if (logNavigation) {
          console.warn('🧭 Navigation: Already navigating, ignoring request');
        }
        return false;
      }
      isNavigatingRef.current = true;
      try {
        if (enforceGuards) {
          var guardResult = _navigationGuards.default.canNavigate(routeName, params);
          if (!guardResult.allowed) {
            if (logNavigation) {
              console.warn('🧭 Navigation: Blocked by guard', {
                route: routeName,
                reason: guardResult.reason,
                redirectTo: guardResult.redirectTo
              });
            }
            if (guardResult.redirectTo) {
              navigation.reset({
                index: 0,
                routes: [{
                  name: guardResult.redirectTo
                }]
              });
            }
            return false;
          }
        }
        var flowResult = _navigationGuards.default.validateNavigationFlow(currentScreenRef.current, routeName, params);
        if (!flowResult.allowed) {
          if (logNavigation) {
            console.warn('🧭 Navigation: Invalid flow', {
              from: currentScreenRef.current,
              to: routeName,
              reason: flowResult.reason
            });
          }
          return false;
        }
        if (trackAnalytics) {
          _navigationAnalytics.default.trackNavigationAction('button_press', currentScreenRef.current, routeName, params);
        }
        navigation.navigate(routeName, params);
        if (logNavigation) {
          console.log('🧭 Navigation: Success', {
            from: currentScreenRef.current,
            to: routeName,
            params: params
          });
        }
        return true;
      } catch (error) {
        console.error('🧭 Navigation: Error', error);
        if (trackAnalytics) {
          _navigationAnalytics.default.trackNavigationError(error instanceof Error ? error.message : 'Unknown navigation error', routeName, params);
        }
        return false;
      } finally {
        setTimeout(function () {
          isNavigatingRef.current = false;
        }, 100);
      }
    });
    return function (_x, _x2) {
      return _ref.apply(this, arguments);
    };
  }(), [navigation, enforceGuards, trackAnalytics, logNavigation]);
  var goBack = (0, _react.useCallback)(function () {
    if (trackAnalytics) {
      _navigationAnalytics.default.trackNavigationAction('back_button', currentScreenRef.current, 'previous_screen');
    }
    if (logNavigation) {
      console.log('🧭 Navigation: Going back from', currentScreenRef.current);
    }
    navigation.goBack();
  }, [navigation, trackAnalytics, logNavigation]);
  var reset = (0, _react.useCallback)(function (state) {
    if (trackAnalytics) {
      var _state$routes;
      _navigationAnalytics.default.trackNavigationAction('deep_link', currentScreenRef.current, ((_state$routes = state.routes) == null || (_state$routes = _state$routes[state.index]) == null ? void 0 : _state$routes.name) || 'unknown');
    }
    if (logNavigation) {
      console.log('🧭 Navigation: Reset to', state);
    }
    navigation.reset(state);
  }, [navigation, trackAnalytics, logNavigation]);
  var canNavigate = (0, _react.useCallback)(function (routeName, params) {
    return _navigationGuards.default.canNavigate(routeName, params);
  }, []);
  var trackScreenView = (0, _react.useCallback)(function (params) {
    if (trackAnalytics) {
      _navigationAnalytics.default.trackScreenView(route.name, params, userRole);
    }
  }, [route.name, trackAnalytics, userRole]);
  return {
    navigate: navigate,
    goBack: goBack,
    reset: reset,
    canNavigate: canNavigate,
    trackScreenView: trackScreenView,
    isNavigating: isNavigatingRef.current
  };
};
var useNavigationPerformance = exports.useNavigationPerformance = function useNavigationPerformance() {
  var route = (0, _native.useRoute)();
  var startTimeRef = (0, _react.useRef)(Date.now());
  (0, _react.useEffect)(function () {
    startTimeRef.current = Date.now();
  }, [route.name]);
  var trackLoadTime = (0, _react.useCallback)(function (customStartTime) {
    var loadTime = Date.now() - (customStartTime || startTimeRef.current);
    _navigationAnalytics.default.trackScreenLoadTime(route.name, loadTime);
    return loadTime;
  }, [route.name]);
  return {
    trackLoadTime: trackLoadTime,
    getLoadTime: function getLoadTime() {
      return Date.now() - startTimeRef.current;
    }
  };
};
var useNavigationAnalytics = exports.useNavigationAnalytics = function useNavigationAnalytics() {
  var route = (0, _native.useRoute)();
  var _useAuthStore2 = (0, _authSlice.useAuthStore)(),
    userRole = _useAuthStore2.userRole;
  var trackEvent = (0, _react.useCallback)(function (eventType, data) {
    switch (eventType) {
      case 'screen_view':
        _navigationAnalytics.default.trackScreenView(data.screenName, data.params, userRole);
        break;
      case 'navigation_action':
        _navigationAnalytics.default.trackNavigationAction(data.action, data.fromScreen, data.toScreen, data.params);
        break;
      case 'flow_completion':
        _navigationAnalytics.default.trackFlowCompletion(data.flowName, data.success, data.metadata);
        break;
      case 'error':
        _navigationAnalytics.default.trackNavigationError(data.error, data.screenName, data.params);
        break;
    }
  }, [userRole]);
  var getStats = (0, _react.useCallback)(function () {
    return _navigationAnalytics.default.getNavigationStats();
  }, []);
  return {
    trackEvent: trackEvent,
    getStats: getStats,
    currentScreen: route.name
  };
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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