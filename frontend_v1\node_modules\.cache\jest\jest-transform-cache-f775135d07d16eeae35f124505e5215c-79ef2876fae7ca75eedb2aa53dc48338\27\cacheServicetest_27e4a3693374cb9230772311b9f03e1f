4e6f4bb8a3ed08fec7360d2902f76659
_getJestObj().mock('@react-native-async-storage/async-storage', function () {
  return {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    getAllKeys: jest.fn(),
    multiRemove: jest.fn()
  };
});
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _asyncStorage = _interopRequireDefault(require("@react-native-async-storage/async-storage"));
var _cacheService = require("../cacheService");
function _getJestObj() {
  var _require = require("@jest/globals"),
    jest = _require.jest;
  _getJestObj = function _getJestObj() {
    return jest;
  };
  return jest;
}
var mockAsyncStorage = _asyncStorage.default;
describe('CacheService', function () {
  beforeEach(function () {
    jest.clearAllMocks();
    _cacheService.cacheService.clear();
    mockAsyncStorage.getItem.mockResolvedValue(null);
    mockAsyncStorage.setItem.mockResolvedValue();
    mockAsyncStorage.removeItem.mockResolvedValue();
    mockAsyncStorage.getAllKeys.mockResolvedValue([]);
    mockAsyncStorage.multiRemove.mockResolvedValue();
  });
  describe('Basic Cache Operations', function () {
    it('sets and gets data from memory cache', (0, _asyncToGenerator2.default)(function* () {
      var key = 'test-key';
      var data = {
        message: 'Hello, World!'
      };
      yield _cacheService.cacheService.set(key, data);
      var result = yield _cacheService.cacheService.get(key);
      expect(result).toEqual(data);
    }));
    it('returns null for non-existent keys', (0, _asyncToGenerator2.default)(function* () {
      var result = yield _cacheService.cacheService.get('non-existent-key');
      expect(result).toBeNull();
    }));
    it('removes data from cache', (0, _asyncToGenerator2.default)(function* () {
      var key = 'test-key';
      var data = {
        message: 'Hello, World!'
      };
      yield _cacheService.cacheService.set(key, data);
      yield _cacheService.cacheService.remove(key);
      var result = yield _cacheService.cacheService.get(key);
      expect(result).toBeNull();
    }));
    it('clears all cache data', (0, _asyncToGenerator2.default)(function* () {
      yield _cacheService.cacheService.set('key1', 'data1');
      yield _cacheService.cacheService.set('key2', 'data2');
      yield _cacheService.cacheService.clear();
      var result1 = yield _cacheService.cacheService.get('key1');
      var result2 = yield _cacheService.cacheService.get('key2');
      expect(result1).toBeNull();
      expect(result2).toBeNull();
    }));
  });
  describe('TTL and Expiration', function () {
    it('respects TTL for cache entries', (0, _asyncToGenerator2.default)(function* () {
      var key = 'expiring-key';
      var data = 'expiring-data';
      var ttl = 100;
      yield _cacheService.cacheService.set(key, data, ttl);
      var result = yield _cacheService.cacheService.get(key);
      expect(result).toBe(data);
      yield new Promise(function (resolve) {
        return setTimeout(resolve, 150);
      });
      result = yield _cacheService.cacheService.get(key);
      expect(result).toBeNull();
    }));
    it('uses default TTL when not specified', (0, _asyncToGenerator2.default)(function* () {
      var key = 'default-ttl-key';
      var data = 'default-ttl-data';
      yield _cacheService.cacheService.set(key, data);
      var entryInfo = _cacheService.cacheService.getEntryInfo(key);
      expect(entryInfo == null ? void 0 : entryInfo.ttl).toBe(5 * 60 * 1000);
    }));
    it('updates TTL on cache hit', (0, _asyncToGenerator2.default)(function* () {
      var key = 'update-ttl-key';
      var data = 'update-ttl-data';
      yield _cacheService.cacheService.set(key, data);
      var initialInfo = _cacheService.cacheService.getEntryInfo(key);
      var initialLastAccessed = initialInfo == null ? void 0 : initialInfo.lastAccessed;
      yield new Promise(function (resolve) {
        return setTimeout(resolve, 10);
      });
      yield _cacheService.cacheService.get(key);
      var updatedInfo = _cacheService.cacheService.getEntryInfo(key);
      expect(updatedInfo == null ? void 0 : updatedInfo.lastAccessed).toBeGreaterThan(initialLastAccessed);
    }));
  });
  describe('Storage Cache Integration', function () {
    it('falls back to storage cache when memory cache misses', (0, _asyncToGenerator2.default)(function* () {
      var key = 'storage-key';
      var data = {
        stored: 'data'
      };
      var storageData = JSON.stringify({
        data: data,
        timestamp: Date.now(),
        ttl: 5 * 60 * 1000,
        version: '1.0.0',
        accessCount: 0,
        lastAccessed: Date.now()
      });
      mockAsyncStorage.getItem.mockResolvedValue(storageData);
      var result = yield _cacheService.cacheService.get(key);
      expect(result).toEqual(data);
      expect(mockAsyncStorage.getItem).toHaveBeenCalledWith('@vierla_cache_' + key);
    }));
    it('stores data in both memory and storage by default', (0, _asyncToGenerator2.default)(function* () {
      var key = 'dual-storage-key';
      var data = {
        dual: 'storage'
      };
      yield _cacheService.cacheService.set(key, data);
      var memoryResult = yield _cacheService.cacheService.get(key);
      expect(memoryResult).toEqual(data);
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith('@vierla_cache_' + key, expect.stringContaining('"dual":"storage"'));
    }));
    it('supports memory-only storage option', (0, _asyncToGenerator2.default)(function* () {
      var key = 'memory-only-key';
      var data = {
        memory: 'only'
      };
      yield _cacheService.cacheService.set(key, data, undefined, {
        memoryOnly: true
      });
      var result = yield _cacheService.cacheService.get(key);
      expect(result).toEqual(data);
      expect(mockAsyncStorage.setItem).not.toHaveBeenCalled();
    }));
    it('supports storage-only option', (0, _asyncToGenerator2.default)(function* () {
      var key = 'storage-only-key';
      var data = {
        storage: 'only'
      };
      yield _cacheService.cacheService.set(key, data, undefined, {
        storageOnly: true
      });
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith('@vierla_cache_' + key, expect.stringContaining('"storage":"only"'));
    }));
  });
  describe('Memory Management', function () {
    it('enforces memory limits', (0, _asyncToGenerator2.default)(function* () {
      var testCache = new _cacheService.cacheService.constructor({
        maxMemorySize: 1000
      });
      var largeData = 'x'.repeat(500);
      yield testCache.set('key1', largeData);
      yield testCache.set('key2', largeData);
      yield testCache.set('key3', largeData);
      var stats = testCache.getStats();
      expect(stats.totalSize).toBeLessThanOrEqual(1000);
    }));
    it('uses LFU + LRU eviction strategy', (0, _asyncToGenerator2.default)(function* () {
      var testCache = new _cacheService.cacheService.constructor({
        maxMemorySize: 1000
      });
      var data = 'x'.repeat(200);
      yield testCache.set('frequent', data);
      yield testCache.set('infrequent', data);
      yield testCache.set('recent', data);
      yield testCache.get('frequent');
      yield testCache.get('frequent');
      yield testCache.get('frequent');
      yield new Promise(function (resolve) {
        return setTimeout(resolve, 10);
      });
      yield testCache.get('recent');
      yield testCache.set('trigger', data);
      yield testCache.set('eviction', data);
      var infrequentResult = yield testCache.get('infrequent');
      expect(infrequentResult).toBeNull();
      var frequentResult = yield testCache.get('frequent');
      expect(frequentResult).toBe(data);
    }));
  });
  describe('Cache Statistics', function () {
    it('tracks cache hits and misses', (0, _asyncToGenerator2.default)(function* () {
      var key = 'stats-key';
      var data = 'stats-data';
      yield _cacheService.cacheService.get('non-existent');
      yield _cacheService.cacheService.set(key, data);
      yield _cacheService.cacheService.get(key);
      var stats = _cacheService.cacheService.getStats();
      expect(stats.memoryHits).toBe(1);
      expect(stats.memoryMisses).toBeGreaterThan(0);
    }));
    it('calculates hit rate correctly', (0, _asyncToGenerator2.default)(function* () {
      yield _cacheService.cacheService.set('key1', 'data1');
      yield _cacheService.cacheService.set('key2', 'data2');
      yield _cacheService.cacheService.get('key1');
      yield _cacheService.cacheService.get('key2');
      yield _cacheService.cacheService.get('non-existent');
      var stats = _cacheService.cacheService.getStats();
      expect(stats.hitRate).toBeCloseTo(2 / 3, 2);
    }));
    it('tracks entry count and total size', (0, _asyncToGenerator2.default)(function* () {
      yield _cacheService.cacheService.set('key1', 'small');
      yield _cacheService.cacheService.set('key2', 'larger data string');
      var stats = _cacheService.cacheService.getStats();
      expect(stats.entryCount).toBe(2);
      expect(stats.totalSize).toBeGreaterThan(0);
    }));
  });
  describe('Cache Preloading', function () {
    it('preloads multiple entries', (0, _asyncToGenerator2.default)(function* () {
      var entries = [{
        key: 'preload1',
        data: 'data1'
      }, {
        key: 'preload2',
        data: 'data2',
        ttl: 1000
      }, {
        key: 'preload3',
        data: {
          complex: 'object'
        }
      }];
      yield _cacheService.cacheService.preload(entries);
      for (var entry of entries) {
        var result = yield _cacheService.cacheService.get(entry.key);
        expect(result).toEqual(entry.data);
      }
    }));
    it('handles preload failures gracefully', (0, _asyncToGenerator2.default)(function* () {
      var originalSet = _cacheService.cacheService.set;
      _cacheService.cacheService.set = jest.fn().mockRejectedValueOnce(new Error('Set failed'));
      var entries = [{
        key: 'success',
        data: 'data1'
      }, {
        key: 'failure',
        data: 'data2'
      }];
      yield expect(_cacheService.cacheService.preload(entries)).resolves.toBeUndefined();
      _cacheService.cacheService.set = originalSet;
    }));
  });
  describe('Pattern-based Invalidation', function () {
    it('invalidates entries matching pattern', (0, _asyncToGenerator2.default)(function* () {
      yield _cacheService.cacheService.set('user:1:profile', 'profile1');
      yield _cacheService.cacheService.set('user:1:settings', 'settings1');
      yield _cacheService.cacheService.set('user:2:profile', 'profile2');
      yield _cacheService.cacheService.set('other:data', 'other');
      yield _cacheService.cacheService.invalidatePattern(/^user:1:/);
      expect(yield _cacheService.cacheService.get('user:1:profile')).toBeNull();
      expect(yield _cacheService.cacheService.get('user:1:settings')).toBeNull();
      expect(yield _cacheService.cacheService.get('user:2:profile')).toBe('profile2');
      expect(yield _cacheService.cacheService.get('other:data')).toBe('other');
    }));
    it('invalidates storage entries matching pattern', (0, _asyncToGenerator2.default)(function* () {
      mockAsyncStorage.getAllKeys.mockResolvedValue(['@vierla_cache_user:1:profile', '@vierla_cache_user:1:settings', '@vierla_cache_user:2:profile', '@vierla_cache_other:data']);
      yield _cacheService.cacheService.invalidatePattern(/^user:1:/);
      expect(mockAsyncStorage.multiRemove).toHaveBeenCalledWith(['@vierla_cache_user:1:profile', '@vierla_cache_user:1:settings']);
    }));
  });
  describe('Entry Information', function () {
    it('provides entry metadata', (0, _asyncToGenerator2.default)(function* () {
      var key = 'info-key';
      var data = 'info-data';
      var ttl = 10000;
      yield _cacheService.cacheService.set(key, data, ttl);
      var info = _cacheService.cacheService.getEntryInfo(key);
      expect(info).toBeDefined();
      expect(info == null ? void 0 : info.ttl).toBe(ttl);
      expect(info == null ? void 0 : info.version).toBe('1.0.0');
      expect(info == null ? void 0 : info.accessCount).toBe(0);
      expect(info == null ? void 0 : info.timestamp).toBeGreaterThan(0);
      expect(info == null ? void 0 : info.lastAccessed).toBeGreaterThan(0);
    }));
    it('returns null for non-existent entries', function () {
      var info = _cacheService.cacheService.getEntryInfo('non-existent');
      expect(info).toBeNull();
    });
    it('updates access count on cache hits', (0, _asyncToGenerator2.default)(function* () {
      var key = 'access-count-key';
      yield _cacheService.cacheService.set(key, 'data');
      yield _cacheService.cacheService.get(key);
      yield _cacheService.cacheService.get(key);
      yield _cacheService.cacheService.get(key);
      var info = _cacheService.cacheService.getEntryInfo(key);
      expect(info == null ? void 0 : info.accessCount).toBe(3);
    }));
  });
  describe('Error Handling', function () {
    it('handles AsyncStorage errors gracefully', (0, _asyncToGenerator2.default)(function* () {
      mockAsyncStorage.getItem.mockRejectedValue(new Error('Storage error'));
      var result = yield _cacheService.cacheService.get('error-key');
      expect(result).toBeNull();
    }));
    it('handles set errors gracefully', (0, _asyncToGenerator2.default)(function* () {
      mockAsyncStorage.setItem.mockRejectedValue(new Error('Storage full'));
      yield expect(_cacheService.cacheService.set('error-key', 'data')).resolves.toBeUndefined();
    }));
    it('handles remove errors gracefully', (0, _asyncToGenerator2.default)(function* () {
      mockAsyncStorage.removeItem.mockRejectedValue(new Error('Remove error'));
      yield expect(_cacheService.cacheService.remove('error-key')).resolves.toBeUndefined();
    }));
    it('handles clear errors gracefully', (0, _asyncToGenerator2.default)(function* () {
      mockAsyncStorage.getAllKeys.mockRejectedValue(new Error('Keys error'));
      yield expect(_cacheService.cacheService.clear()).resolves.toBeUndefined();
    }));
  });
  describe('Service Lifecycle', function () {
    it('destroys service correctly', function () {
      var clearIntervalSpy = jest.spyOn(global, 'clearInterval');
      _cacheService.cacheService.destroy();
      expect(clearIntervalSpy).toHaveBeenCalled();
      clearIntervalSpy.mockRestore();
    });
    it('cleans up expired entries periodically', (0, _asyncToGenerator2.default)(function* () {
      var key = 'cleanup-key';
      var shortTtl = 50;
      yield _cacheService.cacheService.set(key, 'data', shortTtl);
      expect(yield _cacheService.cacheService.get(key)).toBe('data');
      yield new Promise(function (resolve) {
        return setTimeout(resolve, 100);
      });
      expect(yield _cacheService.cacheService.get(key)).toBeNull();
    }));
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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