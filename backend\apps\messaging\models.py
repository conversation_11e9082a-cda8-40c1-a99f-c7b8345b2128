from django.db import models
from django.utils import timezone as django_timezone
from django.contrib.auth import get_user_model

User = get_user_model()


class Conversation(models.Model):
    """Enhanced conversation model for real-time messaging"""
    CONVERSATION_TYPES = [
        ('booking', 'Booking Related'),
        ('general', 'General Inquiry'),
        ('support', 'Customer Support'),
    ]

    participants = models.ManyToManyField(User, related_name='conversations')
    conversation_type = models.CharField(
        max_length=20, choices=CONVERSATION_TYPES, default='general')
    booking = models.ForeignKey(
        'bookings.Booking',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='conversations'
    )
    title = models.CharField(max_length=255, blank=True)
    last_message = models.ForeignKey(
        'Message',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='+'
    )
    is_active = models.BooleanField(default=True)
    created_at = models.DateTime<PERSON>ield(default=django_timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'conversations'
        verbose_name = 'Conversation'
        verbose_name_plural = 'Conversations'
        ordering = ['-updated_at']

    def __str__(self):
        participant_names = ', '.join([p.get_full_name() for p in self.participants.all()[:2]])
        return f"Conversation: {participant_names}"

    @property
    def participant_count(self):
        return self.participants.count()

    def get_other_participant(self, user):
        """Get the other participant in a 2-person conversation"""
        return self.participants.exclude(id=user.id).first()

    def get_unread_count_for_user(self, user):
        """Get unread message count for a specific user"""
        return self.messages.filter(is_read=False).exclude(sender=user).count()


class Message(models.Model):
    """Enhanced message model for real-time messaging"""
    MESSAGE_TYPES = [
        ('text', 'Text Message'),
        ('image', 'Image'),
        ('file', 'File Attachment'),
        ('system', 'System Message'),
        ('booking_update', 'Booking Update'),
    ]

    DELIVERY_STATUS = [
        ('sending', 'Sending'),
        ('sent', 'Sent'),
        ('delivered', 'Delivered'),
        ('read', 'Read'),
        ('failed', 'Failed'),
    ]

    conversation = models.ForeignKey(
        Conversation, on_delete=models.CASCADE, related_name='messages')
    sender = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name='sent_messages')
    content = models.TextField()
    message_type = models.CharField(
        max_length=20, choices=MESSAGE_TYPES, default='text')
    reply_to = models.ForeignKey(
        'self', on_delete=models.SET_NULL, null=True, blank=True, related_name='replies')
    delivery_status = models.CharField(
        max_length=20, choices=DELIVERY_STATUS, default='sent')
    is_read = models.BooleanField(default=False)
    read_at = models.DateTimeField(null=True, blank=True)
    metadata = models.JSONField(default=dict, blank=True)
    created_at = models.DateTimeField(default=django_timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'messages'
        verbose_name = 'Message'
        verbose_name_plural = 'Messages'
        ordering = ['created_at']

    def __str__(self):
        return f"Message from {self.sender.get_full_name()} at {self.created_at}"

    def mark_as_read(self):
        """Mark message as read with timestamp"""
        if not self.is_read:
            self.is_read = True
            self.read_at = django_timezone.now()
            self.delivery_status = 'read'
            self.save(update_fields=['is_read', 'read_at', 'delivery_status'])

    def save(self, *args, **kwargs):
        """Override save to update conversation's last_message"""
        super().save(*args, **kwargs)

        # Update conversation's last message and timestamp
        if self.conversation:
            self.conversation.last_message = self
            self.conversation.updated_at = self.created_at
            self.conversation.save(update_fields=['last_message', 'updated_at'])


class MessageAttachment(models.Model):
    """Model for message file attachments"""
    message = models.ForeignKey(
        Message, on_delete=models.CASCADE, related_name='attachments')
    file = models.FileField(upload_to='message_attachments/%Y/%m/%d/')
    file_name = models.CharField(max_length=255)
    file_size = models.PositiveIntegerField()
    file_type = models.CharField(max_length=50)
    mime_type = models.CharField(max_length=100)
    created_at = models.DateTimeField(default=django_timezone.now)

    class Meta:
        db_table = 'message_attachments'
        verbose_name = 'Message Attachment'
        verbose_name_plural = 'Message Attachments'

    def __str__(self):
        return f"Attachment: {self.file_name}"
