{"name": "@types/styled-components", "version": "5.1.34", "description": "TypeScript definitions for styled-components", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/styled-components", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON>", "url": "https://github.com/<PERSON>bek"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Igmat"}, {"name": "<PERSON>", "githubUsername": "Jessidhia", "url": "https://github.com/Jessidhia"}, {"name": "<PERSON>", "githubUsername": "j<PERSON><PERSON>", "url": "https://github.com/jkillian"}, {"name": "<PERSON>", "githubUsername": "eps1lon", "url": "https://github.com/eps1lon"}, {"name": "<PERSON>", "githubUsername": "wagerfield", "url": "https://github.com/wagerfield"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Lazyuki"}, {"name": "<PERSON>", "githubUsername": "lifeiscontent", "url": "https://github.com/lifeiscontent"}, {"name": "<PERSON>", "githubUsername": "acdr", "url": "https://github.com/acdr"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/styled-components"}, "scripts": {}, "dependencies": {"@types/hoist-non-react-statics": "*", "@types/react": "*", "csstype": "^3.0.2"}, "typesPublisherContentHash": "2edc0da60aad58eb5fc185ddf9ac5e7fe3aceef9c11f79fa301fd6926d85b081", "typeScriptVersion": "4.6"}