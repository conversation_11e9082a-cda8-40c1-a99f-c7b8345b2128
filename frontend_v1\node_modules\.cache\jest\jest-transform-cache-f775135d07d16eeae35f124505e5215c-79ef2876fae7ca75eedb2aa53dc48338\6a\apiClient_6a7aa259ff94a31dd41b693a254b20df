d8aa12a2ed52099c8f3e8430e3678b4a
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.apiClient = void 0;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _asyncStorage = _interopRequireDefault(require("@react-native-async-storage/async-storage"));
var _authSlice = require("../store/authSlice");
var API_BASE_URL = 'http://192.168.2.65:8000';
var DEFAULT_TIMEOUT = 10000;
var ApiClient = function () {
  function ApiClient() {
    var baseURL = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : API_BASE_URL;
    var timeout = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : DEFAULT_TIMEOUT;
    (0, _classCallCheck2.default)(this, ApiClient);
    this.authToken = null;
    this.baseURL = baseURL;
    this.defaultTimeout = timeout;
    this.loadAuthToken();
  }
  return (0, _createClass2.default)(ApiClient, [{
    key: "loadAuthToken",
    value: (function () {
      var _loadAuthToken = (0, _asyncToGenerator2.default)(function* () {
        try {
          var token = yield _asyncStorage.default.getItem('auth_token');
          this.authToken = token;
        } catch (error) {
          console.warn('Failed to load auth token:', error);
        }
      });
      function loadAuthToken() {
        return _loadAuthToken.apply(this, arguments);
      }
      return loadAuthToken;
    }())
  }, {
    key: "setAuthToken",
    value: function setAuthToken(token) {
      this.authToken = token;
      if (token) {
        _asyncStorage.default.setItem('auth_token', token);
      } else {
        _asyncStorage.default.removeItem('auth_token');
      }
    }
  }, {
    key: "refreshAuthToken",
    value: (function () {
      var _refreshAuthToken = (0, _asyncToGenerator2.default)(function* () {
        try {
          var refreshToken = yield _asyncStorage.default.getItem('refresh_token');
          if (!refreshToken) {
            throw new Error('No refresh token available');
          }
          var refreshResponse = yield fetch(`${this.baseURL}/api/auth/token/refresh/`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json'
            },
            body: JSON.stringify({
              refresh: refreshToken
            })
          });
          if (!refreshResponse.ok) {
            var errorData = yield refreshResponse.json();
            throw new Error(errorData.detail || 'Token refresh failed');
          }
          var refreshData = yield refreshResponse.json();
          if (refreshData.access) {
            this.setAuthToken(refreshData.access);
            var authState = _authSlice.useAuthStore.getState();
            authState.loginSuccess(refreshData.access, authState.userRole || 'customer');
            console.log('✅ API: Token refreshed successfully');
          }
          if (refreshData.refresh) {
            yield _asyncStorage.default.setItem('refresh_token', refreshData.refresh);
          }
        } catch (error) {
          console.error('❌ API: Token refresh failed:', error.message);
          this.setAuthToken(null);
          yield _asyncStorage.default.removeItem('refresh_token');
          var _authState = _authSlice.useAuthStore.getState();
          _authState.logout();
          throw error;
        }
      });
      function refreshAuthToken() {
        return _refreshAuthToken.apply(this, arguments);
      }
      return refreshAuthToken;
    }())
  }, {
    key: "buildUrl",
    value: function buildUrl(url, params) {
      var fullUrl = url.startsWith('http') ? url : `${this.baseURL}${url}`;
      if (!params || Object.keys(params).length === 0) {
        return fullUrl;
      }
      var urlParams = new URLSearchParams();
      Object.entries(params).forEach(function (_ref) {
        var _ref2 = (0, _slicedToArray2.default)(_ref, 2),
          key = _ref2[0],
          value = _ref2[1];
        if (value !== undefined && value !== null) {
          urlParams.append(key, String(value));
        }
      });
      var separator = fullUrl.includes('?') ? '&' : '?';
      return `${fullUrl}${separator}${urlParams.toString()}`;
    }
  }, {
    key: "buildHeaders",
    value: function buildHeaders(customHeaders) {
      var requiresAuth = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;
      var headers = Object.assign({
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }, customHeaders);
      if (requiresAuth) {
        var authState = _authSlice.useAuthStore.getState();
        var token = authState.authToken || this.authToken;
        if (token) {
          headers['Authorization'] = `Bearer ${token}`;
        }
      }
      return headers;
    }
  }, {
    key: "makeRequest",
    value: (function () {
      var _makeRequest = (0, _asyncToGenerator2.default)(function* (config) {
        var method = config.method,
          url = config.url,
          data = config.data,
          params = config.params,
          customHeaders = config.headers,
          _config$timeout = config.timeout,
          timeout = _config$timeout === void 0 ? this.defaultTimeout : _config$timeout,
          _config$requiresAuth = config.requiresAuth,
          requiresAuth = _config$requiresAuth === void 0 ? true : _config$requiresAuth,
          onProgress = config.onProgress,
          onStatusUpdate = config.onStatusUpdate;
        var fullUrl = this.buildUrl(url, params);
        var headers = this.buildHeaders(customHeaders, requiresAuth);
        var controller = new AbortController();
        var timeoutId = setTimeout(function () {
          return controller.abort();
        }, timeout);
        try {
          onStatusUpdate == null || onStatusUpdate('Preparing request...');
          var requestInit = {
            method: method,
            headers: headers,
            signal: controller.signal
          };
          if (data && method !== 'GET') {
            requestInit.body = JSON.stringify(data);
          }
          onStatusUpdate == null || onStatusUpdate('Sending request...');
          onProgress == null || onProgress({
            loaded: 0,
            total: 100,
            percentage: 0
          });
          var response = yield fetch(fullUrl, requestInit);
          clearTimeout(timeoutId);
          onStatusUpdate == null || onStatusUpdate('Processing response...');
          onProgress == null || onProgress({
            loaded: 50,
            total: 100,
            percentage: 50
          });
          var responseData;
          var contentType = response.headers.get('content-type');
          onProgress == null || onProgress({
            loaded: 75,
            total: 100,
            percentage: 75
          });
          if (contentType && contentType.includes('application/json')) {
            responseData = yield response.json();
          } else {
            responseData = yield response.text();
          }
          if (!response.ok) {
            onStatusUpdate == null || onStatusUpdate('Request failed');
            if (response.status === 401 && requiresAuth) {
              console.warn('🔐 API: Authentication failed, attempting token refresh...');
              try {
                yield this.refreshAuthToken();
                console.log('🔄 API: Retrying request with refreshed token...');
                var retryHeaders = this.buildHeaders(customHeaders, requiresAuth);
                var retryResponse = yield fetch(fullUrl, Object.assign({}, requestInit, {
                  headers: retryHeaders
                }));
                if (retryResponse.ok) {
                  var retryData = yield retryResponse.json();
                  console.log('✅ API: Request succeeded after token refresh');
                  return {
                    data: retryData,
                    status: retryResponse.status,
                    statusText: retryResponse.statusText,
                    headers: Object.fromEntries(retryResponse.headers.entries())
                  };
                }
              } catch (refreshError) {
                console.error('❌ API: Token refresh failed:', refreshError);
              }
            }
            throw {
              message: responseData || response.statusText,
              status: response.status,
              details: responseData
            };
          }
          onStatusUpdate == null || onStatusUpdate('Request completed');
          onProgress == null || onProgress({
            loaded: 100,
            total: 100,
            percentage: 100
          });
          return {
            data: responseData,
            status: response.status,
            statusText: response.statusText,
            headers: Object.fromEntries(response.headers.entries())
          };
        } catch (error) {
          clearTimeout(timeoutId);
          if (error.name === 'AbortError') {
            throw {
              message: 'Request timeout',
              status: 408
            };
          }
          if (error.status) {
            throw error;
          }
          throw {
            message: error.message || 'Network error',
            status: 0,
            details: error
          };
        }
      });
      function makeRequest(_x) {
        return _makeRequest.apply(this, arguments);
      }
      return makeRequest;
    }())
  }, {
    key: "get",
    value: (function () {
      var _get = (0, _asyncToGenerator2.default)(function* (url, params) {
        var requiresAuth = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;
        return this.makeRequest({
          method: 'GET',
          url: url,
          params: params,
          requiresAuth: requiresAuth
        });
      });
      function get(_x2, _x3) {
        return _get.apply(this, arguments);
      }
      return get;
    }())
  }, {
    key: "post",
    value: (function () {
      var _post = (0, _asyncToGenerator2.default)(function* (url, data) {
        var requiresAuth = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;
        return this.makeRequest({
          method: 'POST',
          url: url,
          data: data,
          requiresAuth: requiresAuth
        });
      });
      function post(_x4, _x5) {
        return _post.apply(this, arguments);
      }
      return post;
    }())
  }, {
    key: "put",
    value: (function () {
      var _put = (0, _asyncToGenerator2.default)(function* (url, data) {
        var requiresAuth = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;
        return this.makeRequest({
          method: 'PUT',
          url: url,
          data: data,
          requiresAuth: requiresAuth
        });
      });
      function put(_x6, _x7) {
        return _put.apply(this, arguments);
      }
      return put;
    }())
  }, {
    key: "patch",
    value: (function () {
      var _patch = (0, _asyncToGenerator2.default)(function* (url, data) {
        var requiresAuth = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;
        return this.makeRequest({
          method: 'PATCH',
          url: url,
          data: data,
          requiresAuth: requiresAuth
        });
      });
      function patch(_x8, _x9) {
        return _patch.apply(this, arguments);
      }
      return patch;
    }())
  }, {
    key: "delete",
    value: (function () {
      var _delete2 = (0, _asyncToGenerator2.default)(function* (url) {
        var requiresAuth = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;
        return this.makeRequest({
          method: 'DELETE',
          url: url,
          requiresAuth: requiresAuth
        });
      });
      function _delete(_x0) {
        return _delete2.apply(this, arguments);
      }
      return _delete;
    }())
  }]);
}();
var apiClient = exports.apiClient = new ApiClient();
var _default = exports.default = apiClient;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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