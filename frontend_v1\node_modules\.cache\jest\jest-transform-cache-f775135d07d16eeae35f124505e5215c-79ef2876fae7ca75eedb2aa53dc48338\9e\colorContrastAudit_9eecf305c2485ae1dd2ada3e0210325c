782baf0b542950f27811023f24070a70
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.WCAG_CONTRAST_RATIOS = exports.VIERLA_COLOR_PAIRS = exports.TEXT_SIZE_THRESHOLDS = void 0;
exports.auditColorPair = auditColorPair;
exports.auditColorPalette = auditColorPalette;
exports.calculateContrastRatio = calculateContrastRatio;
exports.generateContrastReport = generateContrastReport;
exports.getWCAGCompliantColor = getWCAGCompliantColor;
exports.runVierlaContrastAudit = runVierlaContrastAudit;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var WCAG_CONTRAST_RATIOS = exports.WCAG_CONTRAST_RATIOS = {
  AA: {
    normalText: 4.5,
    largeText: 3.0,
    uiComponents: 3.0
  },
  AAA: {
    normalText: 7.0,
    largeText: 4.5,
    uiComponents: 4.5
  }
};
var TEXT_SIZE_THRESHOLDS = exports.TEXT_SIZE_THRESHOLDS = {
  largeText: {
    fontSize: 18,
    fontWeight: 'normal'
  },
  largeBoldText: {
    fontSize: 14,
    fontWeight: 'bold'
  }
};
function hexToRgb(hex) {
  var result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
}
function getRelativeLuminance(r, g, b) {
  var _map = [r, g, b].map(function (c) {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    }),
    _map2 = (0, _slicedToArray2.default)(_map, 3),
    rs = _map2[0],
    gs = _map2[1],
    bs = _map2[2];
  return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
}
function calculateContrastRatio(color1, color2) {
  var rgb1 = hexToRgb(color1);
  var rgb2 = hexToRgb(color2);
  if (!rgb1 || !rgb2) {
    throw new Error('Invalid color format. Please use hex colors.');
  }
  var lum1 = getRelativeLuminance(rgb1.r, rgb1.g, rgb1.b);
  var lum2 = getRelativeLuminance(rgb2.r, rgb2.g, rgb2.b);
  var brightest = Math.max(lum1, lum2);
  var darkest = Math.min(lum1, lum2);
  return (brightest + 0.05) / (darkest + 0.05);
}
function auditColorPair(pair) {
  var ratio = calculateContrastRatio(pair.foreground, pair.background);
  var requiredRatio;
  if (pair.isUIComponent) {
    requiredRatio = WCAG_CONTRAST_RATIOS.AA.uiComponents;
  } else if (pair.textSize === 'large') {
    requiredRatio = WCAG_CONTRAST_RATIOS.AA.largeText;
  } else {
    requiredRatio = WCAG_CONTRAST_RATIOS.AA.normalText;
  }
  var passesAA = ratio >= requiredRatio;
  var passesAAA = pair.isUIComponent ? ratio >= WCAG_CONTRAST_RATIOS.AAA.uiComponents : pair.textSize === 'large' ? ratio >= WCAG_CONTRAST_RATIOS.AAA.largeText : ratio >= WCAG_CONTRAST_RATIOS.AAA.normalText;
  var level;
  var recommendation;
  if (passesAAA) {
    level = 'AAA';
  } else if (passesAA) {
    level = 'AA';
  } else {
    level = 'FAIL';
    recommendation = generateRecommendation(pair, ratio, requiredRatio);
  }
  return {
    ratio: ratio,
    passes: {
      AA: passesAA,
      AAA: passesAAA
    },
    level: level,
    recommendation: recommendation
  };
}
function generateRecommendation(pair, currentRatio, requiredRatio) {
  var improvement = requiredRatio / currentRatio;
  if (improvement < 1.2) {
    return `Slightly adjust the ${pair.foreground} foreground or ${pair.background} background color to improve contrast.`;
  } else if (improvement < 2) {
    return `Consider using a darker foreground or lighter background color for better contrast.`;
  } else {
    return `Significant color changes needed. Consider using high-contrast color combinations from the design system.`;
  }
}
function auditColorPalette(colorPairs) {
  var results = colorPairs.map(function (pair) {
    return Object.assign({}, pair, {
      audit: auditColorPair(pair)
    });
  });
  var summary = {
    total: results.length,
    passing: results.filter(function (r) {
      return r.audit.passes.AA;
    }).length,
    failing: results.filter(function (r) {
      return !r.audit.passes.AA;
    }).length,
    aaCompliant: results.filter(function (r) {
      return r.audit.level === 'AA' || r.audit.level === 'AAA';
    }).length,
    aaaCompliant: results.filter(function (r) {
      return r.audit.level === 'AAA';
    }).length
  };
  return {
    results: results,
    summary: summary
  };
}
function getWCAGCompliantColor(foreground, background) {
  var level = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'AA';
  var textSize = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'normal';
  var isUIComponent = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : false;
  var currentRatio = calculateContrastRatio(foreground, background);
  var requiredRatio;
  if (isUIComponent) {
    requiredRatio = WCAG_CONTRAST_RATIOS[level].uiComponents;
  } else if (textSize === 'large') {
    requiredRatio = WCAG_CONTRAST_RATIOS[level].largeText;
  } else {
    requiredRatio = WCAG_CONTRAST_RATIOS[level].normalText;
  }
  if (currentRatio >= requiredRatio) {
    return foreground;
  }
  var fgRgb = hexToRgb(foreground);
  var bgRgb = hexToRgb(background);
  if (!fgRgb || !bgRgb) {
    throw new Error('Invalid color format');
  }
  var bgLuminance = getRelativeLuminance(bgRgb.r, bgRgb.g, bgRgb.b);
  var targetLuminance = bgLuminance > 0.5 ? (bgLuminance + 0.05) / requiredRatio - 0.05 : (bgLuminance + 0.05) * requiredRatio - 0.05;
  var factor = targetLuminance > bgLuminance ? 1.2 : 0.8;
  var adjustedR = Math.min(255, Math.max(0, Math.round(fgRgb.r * factor)));
  var adjustedG = Math.min(255, Math.max(0, Math.round(fgRgb.g * factor)));
  var adjustedB = Math.min(255, Math.max(0, Math.round(fgRgb.b * factor)));
  return `#${adjustedR.toString(16).padStart(2, '0')}${adjustedG.toString(16).padStart(2, '0')}${adjustedB.toString(16).padStart(2, '0')}`;
}
var VIERLA_COLOR_PAIRS = exports.VIERLA_COLOR_PAIRS = [{
  foreground: '#1F2937',
  background: '#FFFFFF',
  context: 'Primary text on white background',
  textSize: 'normal'
}, {
  foreground: '#FFFFFF',
  background: '#1F2937',
  context: 'White text on dark background',
  textSize: 'normal'
}, {
  foreground: '#FFFFFF',
  background: '#2A4B32',
  context: 'Primary button text',
  textSize: 'normal'
}, {
  foreground: '#6B7280',
  background: '#FFFFFF',
  context: 'Secondary text on white background',
  textSize: 'normal'
}, {
  foreground: '#DC2626',
  background: '#FFFFFF',
  context: 'Error text',
  textSize: 'normal'
}, {
  foreground: '#059669',
  background: '#FFFFFF',
  context: 'Success text',
  textSize: 'normal'
}, {
  foreground: '#2A4B32',
  background: '#F3F4F6',
  context: 'Primary button outline',
  isUIComponent: true
}, {
  foreground: '#2563EB',
  background: '#FFFFFF',
  context: 'Link text',
  textSize: 'normal'
}, {
  foreground: '#1F2937',
  background: '#F9FAFB',
  context: 'Form input text',
  textSize: 'normal'
}, {
  foreground: '#374151',
  background: '#FFFFFF',
  context: 'Navigation text',
  textSize: 'normal'
}];
function runVierlaContrastAudit() {
  return auditColorPalette(VIERLA_COLOR_PAIRS);
}
function generateContrastReport(auditResults) {
  var results = auditResults.results,
    summary = auditResults.summary;
  var report = `# Color Contrast Audit Report\n\n`;
  report += `## Summary\n`;
  report += `- Total color pairs tested: ${summary.total}\n`;
  report += `- Passing WCAG AA: ${summary.passing}/${summary.total} (${Math.round(summary.passing / summary.total * 100)}%)\n`;
  report += `- Failing WCAG AA: ${summary.failing}/${summary.total} (${Math.round(summary.failing / summary.total * 100)}%)\n`;
  report += `- WCAG AAA compliant: ${summary.aaaCompliant}/${summary.total} (${Math.round(summary.aaaCompliant / summary.total * 100)}%)\n\n`;
  if (summary.failing > 0) {
    report += `## Failing Color Pairs\n\n`;
    results.filter(function (r) {
      return !r.audit.passes.AA;
    }).forEach(function (result) {
      report += `### ${result.context}\n`;
      report += `- Foreground: ${result.foreground}\n`;
      report += `- Background: ${result.background}\n`;
      report += `- Contrast Ratio: ${result.audit.ratio.toFixed(2)}:1\n`;
      report += `- Status: ${result.audit.level}\n`;
      if (result.audit.recommendation) {
        report += `- Recommendation: ${result.audit.recommendation}\n`;
      }
      report += `\n`;
    });
  }
  report += `## All Results\n\n`;
  results.forEach(function (result) {
    var status = result.audit.passes.AA ? '✅' : '❌';
    report += `${status} ${result.context}: ${result.audit.ratio.toFixed(2)}:1 (${result.audit.level})\n`;
  });
  return report;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJXQ0FHX0NPTlRSQVNUX1JBVElPUyIsImV4cG9ydHMiLCJBQSIsIm5vcm1hbFRleHQiLCJsYXJnZVRleHQiLCJ1aUNvbXBvbmVudHMiLCJBQUEiLCJURVhUX1NJWkVfVEhSRVNIT0xEUyIsImZvbnRTaXplIiwiZm9udFdlaWdodCIsImxhcmdlQm9sZFRleHQiLCJoZXhUb1JnYiIsImhleCIsInJlc3VsdCIsImV4ZWMiLCJyIiwicGFyc2VJbnQiLCJnIiwiYiIsImdldFJlbGF0aXZlTHVtaW5hbmNlIiwiX21hcCIsIm1hcCIsImMiLCJNYXRoIiwicG93IiwiX21hcDIiLCJfc2xpY2VkVG9BcnJheTIiLCJkZWZhdWx0IiwicnMiLCJncyIsImJzIiwiY2FsY3VsYXRlQ29udHJhc3RSYXRpbyIsImNvbG9yMSIsImNvbG9yMiIsInJnYjEiLCJyZ2IyIiwiRXJyb3IiLCJsdW0xIiwibHVtMiIsImJyaWdodGVzdCIsIm1heCIsImRhcmtlc3QiLCJtaW4iLCJhdWRpdENvbG9yUGFpciIsInBhaXIiLCJyYXRpbyIsImZvcmVncm91bmQiLCJiYWNrZ3JvdW5kIiwicmVxdWlyZWRSYXRpbyIsImlzVUlDb21wb25lbnQiLCJ0ZXh0U2l6ZSIsInBhc3Nlc0FBIiwicGFzc2VzQUFBIiwibGV2ZWwiLCJyZWNvbW1lbmRhdGlvbiIsImdlbmVyYXRlUmVjb21tZW5kYXRpb24iLCJwYXNzZXMiLCJjdXJyZW50UmF0aW8iLCJpbXByb3ZlbWVudCIsImF1ZGl0Q29sb3JQYWxldHRlIiwiY29sb3JQYWlycyIsInJlc3VsdHMiLCJPYmplY3QiLCJhc3NpZ24iLCJhdWRpdCIsInN1bW1hcnkiLCJ0b3RhbCIsImxlbmd0aCIsInBhc3NpbmciLCJmaWx0ZXIiLCJmYWlsaW5nIiwiYWFDb21wbGlhbnQiLCJhYWFDb21wbGlhbnQiLCJnZXRXQ0FHQ29tcGxpYW50Q29sb3IiLCJhcmd1bWVudHMiLCJ1bmRlZmluZWQiLCJmZ1JnYiIsImJnUmdiIiwiYmdMdW1pbmFuY2UiLCJ0YXJnZXRMdW1pbmFuY2UiLCJmYWN0b3IiLCJhZGp1c3RlZFIiLCJyb3VuZCIsImFkanVzdGVkRyIsImFkanVzdGVkQiIsInRvU3RyaW5nIiwicGFkU3RhcnQiLCJWSUVSTEFfQ09MT1JfUEFJUlMiLCJjb250ZXh0IiwicnVuVmllcmxhQ29udHJhc3RBdWRpdCIsImdlbmVyYXRlQ29udHJhc3RSZXBvcnQiLCJhdWRpdFJlc3VsdHMiLCJyZXBvcnQiLCJmb3JFYWNoIiwidG9GaXhlZCIsInN0YXR1cyJdLCJzb3VyY2VzIjpbImNvbG9yQ29udHJhc3RBdWRpdC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIENvbG9yIENvbnRyYXN0IEF1ZGl0IFN5c3RlbVxuICogXG4gKiBDb21wcmVoZW5zaXZlIHN5c3RlbSBmb3IgdmVyaWZ5aW5nIFdDQUcgMi4yIEFBIGNvbG9yIGNvbnRyYXN0IGNvbXBsaWFuY2VcbiAqIGFjcm9zcyBhbGwgdGV4dCBhbmQgVUkgY29tcG9uZW50cyBpbiB0aGUgYXBwbGljYXRpb24uXG4gKiBcbiAqIEZlYXR1cmVzOlxuICogLSBBdXRvbWF0ZWQgY29udHJhc3QgcmF0aW8gY2FsY3VsYXRpb25cbiAqIC0gV0NBRyAyLjIgQUEvQUFBIGNvbXBsaWFuY2UgY2hlY2tpbmdcbiAqIC0gUmVhbC10aW1lIGNvbnRyYXN0IHZhbGlkYXRpb25cbiAqIC0gQ29sb3IgcGFsZXR0ZSBvcHRpbWl6YXRpb25cbiAqIC0gQWNjZXNzaWJpbGl0eSByZXBvcnRpbmdcbiAqIFxuICogQHZlcnNpb24gMS4wLjBcbiAqIEBhdXRob3IgVmllcmxhIERldmVsb3BtZW50IFRlYW1cbiAqL1xuXG4vLyBXQ0FHIGNvbnRyYXN0IHJhdGlvIHJlcXVpcmVtZW50c1xuZXhwb3J0IGNvbnN0IFdDQUdfQ09OVFJBU1RfUkFUSU9TID0ge1xuICBBQToge1xuICAgIG5vcm1hbFRleHQ6IDQuNSxcbiAgICBsYXJnZVRleHQ6IDMuMCxcbiAgICB1aUNvbXBvbmVudHM6IDMuMCxcbiAgfSxcbiAgQUFBOiB7XG4gICAgbm9ybWFsVGV4dDogNy4wLFxuICAgIGxhcmdlVGV4dDogNC41LFxuICAgIHVpQ29tcG9uZW50czogNC41LFxuICB9LFxufSBhcyBjb25zdDtcblxuLy8gVGV4dCBzaXplIHRocmVzaG9sZHMgZm9yIFdDQUdcbmV4cG9ydCBjb25zdCBURVhUX1NJWkVfVEhSRVNIT0xEUyA9IHtcbiAgbGFyZ2VUZXh0OiB7XG4gICAgZm9udFNpemU6IDE4LCAvLyAxOHB4IG9yIGxhcmdlclxuICAgIGZvbnRXZWlnaHQ6ICdub3JtYWwnLFxuICB9LFxuICBsYXJnZUJvbGRUZXh0OiB7XG4gICAgZm9udFNpemU6IDE0LCAvLyAxNHB4IG9yIGxhcmdlciBpZiBib2xkXG4gICAgZm9udFdlaWdodDogJ2JvbGQnLFxuICB9LFxufSBhcyBjb25zdDtcblxuLy8gQ29sb3IgY29udHJhc3QgYXVkaXQgcmVzdWx0XG5leHBvcnQgaW50ZXJmYWNlIENvbnRyYXN0QXVkaXRSZXN1bHQge1xuICByYXRpbzogbnVtYmVyO1xuICBwYXNzZXM6IHtcbiAgICBBQTogYm9vbGVhbjtcbiAgICBBQUE6IGJvb2xlYW47XG4gIH07XG4gIGxldmVsOiAnQUEnIHwgJ0FBQScgfCAnRkFJTCc7XG4gIHJlY29tbWVuZGF0aW9uPzogc3RyaW5nO1xufVxuXG4vLyBDb2xvciBwYWlyIGZvciB0ZXN0aW5nXG5leHBvcnQgaW50ZXJmYWNlIENvbG9yUGFpciB7XG4gIGZvcmVncm91bmQ6IHN0cmluZztcbiAgYmFja2dyb3VuZDogc3RyaW5nO1xuICBjb250ZXh0OiBzdHJpbmc7IC8vIERlc2NyaXB0aW9uIG9mIHdoZXJlIHRoaXMgcGFpciBpcyB1c2VkXG4gIHRleHRTaXplPzogJ25vcm1hbCcgfCAnbGFyZ2UnO1xuICBpc1VJQ29tcG9uZW50PzogYm9vbGVhbjtcbn1cblxuLyoqXG4gKiBDb252ZXJ0IGhleCBjb2xvciB0byBSR0JcbiAqL1xuZnVuY3Rpb24gaGV4VG9SZ2IoaGV4OiBzdHJpbmcpOiB7IHI6IG51bWJlcjsgZzogbnVtYmVyOyBiOiBudW1iZXIgfSB8IG51bGwge1xuICBjb25zdCByZXN1bHQgPSAvXiM/KFthLWZcXGRdezJ9KShbYS1mXFxkXXsyfSkoW2EtZlxcZF17Mn0pJC9pLmV4ZWMoaGV4KTtcbiAgcmV0dXJuIHJlc3VsdCA/IHtcbiAgICByOiBwYXJzZUludChyZXN1bHRbMV0sIDE2KSxcbiAgICBnOiBwYXJzZUludChyZXN1bHRbMl0sIDE2KSxcbiAgICBiOiBwYXJzZUludChyZXN1bHRbM10sIDE2KSxcbiAgfSA6IG51bGw7XG59XG5cbi8qKlxuICogQ2FsY3VsYXRlIHJlbGF0aXZlIGx1bWluYW5jZSBvZiBhIGNvbG9yXG4gKi9cbmZ1bmN0aW9uIGdldFJlbGF0aXZlTHVtaW5hbmNlKHI6IG51bWJlciwgZzogbnVtYmVyLCBiOiBudW1iZXIpOiBudW1iZXIge1xuICBjb25zdCBbcnMsIGdzLCBic10gPSBbciwgZywgYl0ubWFwKGMgPT4ge1xuICAgIGMgPSBjIC8gMjU1O1xuICAgIHJldHVybiBjIDw9IDAuMDM5MjggPyBjIC8gMTIuOTIgOiBNYXRoLnBvdygoYyArIDAuMDU1KSAvIDEuMDU1LCAyLjQpO1xuICB9KTtcbiAgXG4gIHJldHVybiAwLjIxMjYgKiBycyArIDAuNzE1MiAqIGdzICsgMC4wNzIyICogYnM7XG59XG5cbi8qKlxuICogQ2FsY3VsYXRlIGNvbnRyYXN0IHJhdGlvIGJldHdlZW4gdHdvIGNvbG9yc1xuICovXG5leHBvcnQgZnVuY3Rpb24gY2FsY3VsYXRlQ29udHJhc3RSYXRpbyhjb2xvcjE6IHN0cmluZywgY29sb3IyOiBzdHJpbmcpOiBudW1iZXIge1xuICBjb25zdCByZ2IxID0gaGV4VG9SZ2IoY29sb3IxKTtcbiAgY29uc3QgcmdiMiA9IGhleFRvUmdiKGNvbG9yMik7XG4gIFxuICBpZiAoIXJnYjEgfHwgIXJnYjIpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ0ludmFsaWQgY29sb3IgZm9ybWF0LiBQbGVhc2UgdXNlIGhleCBjb2xvcnMuJyk7XG4gIH1cbiAgXG4gIGNvbnN0IGx1bTEgPSBnZXRSZWxhdGl2ZUx1bWluYW5jZShyZ2IxLnIsIHJnYjEuZywgcmdiMS5iKTtcbiAgY29uc3QgbHVtMiA9IGdldFJlbGF0aXZlTHVtaW5hbmNlKHJnYjIuciwgcmdiMi5nLCByZ2IyLmIpO1xuICBcbiAgY29uc3QgYnJpZ2h0ZXN0ID0gTWF0aC5tYXgobHVtMSwgbHVtMik7XG4gIGNvbnN0IGRhcmtlc3QgPSBNYXRoLm1pbihsdW0xLCBsdW0yKTtcbiAgXG4gIHJldHVybiAoYnJpZ2h0ZXN0ICsgMC4wNSkgLyAoZGFya2VzdCArIDAuMDUpO1xufVxuXG4vKipcbiAqIENoZWNrIGlmIGNvbG9yIHBhaXIgbWVldHMgV0NBRyByZXF1aXJlbWVudHNcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGF1ZGl0Q29sb3JQYWlyKHBhaXI6IENvbG9yUGFpcik6IENvbnRyYXN0QXVkaXRSZXN1bHQge1xuICBjb25zdCByYXRpbyA9IGNhbGN1bGF0ZUNvbnRyYXN0UmF0aW8ocGFpci5mb3JlZ3JvdW5kLCBwYWlyLmJhY2tncm91bmQpO1xuICBcbiAgbGV0IHJlcXVpcmVkUmF0aW86IG51bWJlcjtcbiAgXG4gIGlmIChwYWlyLmlzVUlDb21wb25lbnQpIHtcbiAgICByZXF1aXJlZFJhdGlvID0gV0NBR19DT05UUkFTVF9SQVRJT1MuQUEudWlDb21wb25lbnRzO1xuICB9IGVsc2UgaWYgKHBhaXIudGV4dFNpemUgPT09ICdsYXJnZScpIHtcbiAgICByZXF1aXJlZFJhdGlvID0gV0NBR19DT05UUkFTVF9SQVRJT1MuQUEubGFyZ2VUZXh0O1xuICB9IGVsc2Uge1xuICAgIHJlcXVpcmVkUmF0aW8gPSBXQ0FHX0NPTlRSQVNUX1JBVElPUy5BQS5ub3JtYWxUZXh0O1xuICB9XG4gIFxuICBjb25zdCBwYXNzZXNBQSA9IHJhdGlvID49IHJlcXVpcmVkUmF0aW87XG4gIGNvbnN0IHBhc3Nlc0FBQSA9IHBhaXIuaXNVSUNvbXBvbmVudCBcbiAgICA/IHJhdGlvID49IFdDQUdfQ09OVFJBU1RfUkFUSU9TLkFBQS51aUNvbXBvbmVudHNcbiAgICA6IHBhaXIudGV4dFNpemUgPT09ICdsYXJnZSdcbiAgICAgID8gcmF0aW8gPj0gV0NBR19DT05UUkFTVF9SQVRJT1MuQUFBLmxhcmdlVGV4dFxuICAgICAgOiByYXRpbyA+PSBXQ0FHX0NPTlRSQVNUX1JBVElPUy5BQUEubm9ybWFsVGV4dDtcbiAgXG4gIGxldCBsZXZlbDogJ0FBJyB8ICdBQUEnIHwgJ0ZBSUwnO1xuICBsZXQgcmVjb21tZW5kYXRpb246IHN0cmluZyB8IHVuZGVmaW5lZDtcbiAgXG4gIGlmIChwYXNzZXNBQUEpIHtcbiAgICBsZXZlbCA9ICdBQUEnO1xuICB9IGVsc2UgaWYgKHBhc3Nlc0FBKSB7XG4gICAgbGV2ZWwgPSAnQUEnO1xuICB9IGVsc2Uge1xuICAgIGxldmVsID0gJ0ZBSUwnO1xuICAgIHJlY29tbWVuZGF0aW9uID0gZ2VuZXJhdGVSZWNvbW1lbmRhdGlvbihwYWlyLCByYXRpbywgcmVxdWlyZWRSYXRpbyk7XG4gIH1cbiAgXG4gIHJldHVybiB7XG4gICAgcmF0aW8sXG4gICAgcGFzc2VzOiB7XG4gICAgICBBQTogcGFzc2VzQUEsXG4gICAgICBBQUE6IHBhc3Nlc0FBQSxcbiAgICB9LFxuICAgIGxldmVsLFxuICAgIHJlY29tbWVuZGF0aW9uLFxuICB9O1xufVxuXG4vKipcbiAqIEdlbmVyYXRlIHJlY29tbWVuZGF0aW9uIGZvciBmYWlsaW5nIGNvbG9yIHBhaXJzXG4gKi9cbmZ1bmN0aW9uIGdlbmVyYXRlUmVjb21tZW5kYXRpb24ocGFpcjogQ29sb3JQYWlyLCBjdXJyZW50UmF0aW86IG51bWJlciwgcmVxdWlyZWRSYXRpbzogbnVtYmVyKTogc3RyaW5nIHtcbiAgY29uc3QgaW1wcm92ZW1lbnQgPSByZXF1aXJlZFJhdGlvIC8gY3VycmVudFJhdGlvO1xuICBcbiAgaWYgKGltcHJvdmVtZW50IDwgMS4yKSB7XG4gICAgcmV0dXJuIGBTbGlnaHRseSBhZGp1c3QgdGhlICR7cGFpci5mb3JlZ3JvdW5kfSBmb3JlZ3JvdW5kIG9yICR7cGFpci5iYWNrZ3JvdW5kfSBiYWNrZ3JvdW5kIGNvbG9yIHRvIGltcHJvdmUgY29udHJhc3QuYDtcbiAgfSBlbHNlIGlmIChpbXByb3ZlbWVudCA8IDIpIHtcbiAgICByZXR1cm4gYENvbnNpZGVyIHVzaW5nIGEgZGFya2VyIGZvcmVncm91bmQgb3IgbGlnaHRlciBiYWNrZ3JvdW5kIGNvbG9yIGZvciBiZXR0ZXIgY29udHJhc3QuYDtcbiAgfSBlbHNlIHtcbiAgICByZXR1cm4gYFNpZ25pZmljYW50IGNvbG9yIGNoYW5nZXMgbmVlZGVkLiBDb25zaWRlciB1c2luZyBoaWdoLWNvbnRyYXN0IGNvbG9yIGNvbWJpbmF0aW9ucyBmcm9tIHRoZSBkZXNpZ24gc3lzdGVtLmA7XG4gIH1cbn1cblxuLyoqXG4gKiBBdWRpdCBlbnRpcmUgY29sb3IgcGFsZXR0ZVxuICovXG5leHBvcnQgZnVuY3Rpb24gYXVkaXRDb2xvclBhbGV0dGUoY29sb3JQYWlyczogQ29sb3JQYWlyW10pOiB7XG4gIHJlc3VsdHM6IEFycmF5PENvbG9yUGFpciAmIHsgYXVkaXQ6IENvbnRyYXN0QXVkaXRSZXN1bHQgfT47XG4gIHN1bW1hcnk6IHtcbiAgICB0b3RhbDogbnVtYmVyO1xuICAgIHBhc3Npbmc6IG51bWJlcjtcbiAgICBmYWlsaW5nOiBudW1iZXI7XG4gICAgYWFDb21wbGlhbnQ6IG51bWJlcjtcbiAgICBhYWFDb21wbGlhbnQ6IG51bWJlcjtcbiAgfTtcbn0ge1xuICBjb25zdCByZXN1bHRzID0gY29sb3JQYWlycy5tYXAocGFpciA9PiAoe1xuICAgIC4uLnBhaXIsXG4gICAgYXVkaXQ6IGF1ZGl0Q29sb3JQYWlyKHBhaXIpLFxuICB9KSk7XG4gIFxuICBjb25zdCBzdW1tYXJ5ID0ge1xuICAgIHRvdGFsOiByZXN1bHRzLmxlbmd0aCxcbiAgICBwYXNzaW5nOiByZXN1bHRzLmZpbHRlcihyID0+IHIuYXVkaXQucGFzc2VzLkFBKS5sZW5ndGgsXG4gICAgZmFpbGluZzogcmVzdWx0cy5maWx0ZXIociA9PiAhci5hdWRpdC5wYXNzZXMuQUEpLmxlbmd0aCxcbiAgICBhYUNvbXBsaWFudDogcmVzdWx0cy5maWx0ZXIociA9PiByLmF1ZGl0LmxldmVsID09PSAnQUEnIHx8IHIuYXVkaXQubGV2ZWwgPT09ICdBQUEnKS5sZW5ndGgsXG4gICAgYWFhQ29tcGxpYW50OiByZXN1bHRzLmZpbHRlcihyID0+IHIuYXVkaXQubGV2ZWwgPT09ICdBQUEnKS5sZW5ndGgsXG4gIH07XG4gIFxuICByZXR1cm4geyByZXN1bHRzLCBzdW1tYXJ5IH07XG59XG5cbi8qKlxuICogR2V0IFdDQUcgY29tcGxpYW50IGNvbG9yXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRXQ0FHQ29tcGxpYW50Q29sb3IoXG4gIGZvcmVncm91bmQ6IHN0cmluZyxcbiAgYmFja2dyb3VuZDogc3RyaW5nLFxuICBsZXZlbDogJ0FBJyB8ICdBQUEnID0gJ0FBJyxcbiAgdGV4dFNpemU6ICdub3JtYWwnIHwgJ2xhcmdlJyA9ICdub3JtYWwnLFxuICBpc1VJQ29tcG9uZW50OiBib29sZWFuID0gZmFsc2Vcbik6IHN0cmluZyB7XG4gIGNvbnN0IGN1cnJlbnRSYXRpbyA9IGNhbGN1bGF0ZUNvbnRyYXN0UmF0aW8oZm9yZWdyb3VuZCwgYmFja2dyb3VuZCk7XG4gIFxuICBsZXQgcmVxdWlyZWRSYXRpbzogbnVtYmVyO1xuICBpZiAoaXNVSUNvbXBvbmVudCkge1xuICAgIHJlcXVpcmVkUmF0aW8gPSBXQ0FHX0NPTlRSQVNUX1JBVElPU1tsZXZlbF0udWlDb21wb25lbnRzO1xuICB9IGVsc2UgaWYgKHRleHRTaXplID09PSAnbGFyZ2UnKSB7XG4gICAgcmVxdWlyZWRSYXRpbyA9IFdDQUdfQ09OVFJBU1RfUkFUSU9TW2xldmVsXS5sYXJnZVRleHQ7XG4gIH0gZWxzZSB7XG4gICAgcmVxdWlyZWRSYXRpbyA9IFdDQUdfQ09OVFJBU1RfUkFUSU9TW2xldmVsXS5ub3JtYWxUZXh0O1xuICB9XG4gIFxuICBpZiAoY3VycmVudFJhdGlvID49IHJlcXVpcmVkUmF0aW8pIHtcbiAgICByZXR1cm4gZm9yZWdyb3VuZDsgLy8gQWxyZWFkeSBjb21wbGlhbnRcbiAgfVxuICBcbiAgLy8gQWRqdXN0IGZvcmVncm91bmQgY29sb3IgdG8gbWVldCByZXF1aXJlbWVudHNcbiAgY29uc3QgZmdSZ2IgPSBoZXhUb1JnYihmb3JlZ3JvdW5kKTtcbiAgY29uc3QgYmdSZ2IgPSBoZXhUb1JnYihiYWNrZ3JvdW5kKTtcbiAgXG4gIGlmICghZmdSZ2IgfHwgIWJnUmdiKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdJbnZhbGlkIGNvbG9yIGZvcm1hdCcpO1xuICB9XG4gIFxuICBjb25zdCBiZ0x1bWluYW5jZSA9IGdldFJlbGF0aXZlTHVtaW5hbmNlKGJnUmdiLnIsIGJnUmdiLmcsIGJnUmdiLmIpO1xuICBcbiAgLy8gRGV0ZXJtaW5lIGlmIHdlIHNob3VsZCBtYWtlIGZvcmVncm91bmQgZGFya2VyIG9yIGxpZ2h0ZXJcbiAgY29uc3QgdGFyZ2V0THVtaW5hbmNlID0gYmdMdW1pbmFuY2UgPiAwLjVcbiAgICA/IChiZ0x1bWluYW5jZSArIDAuMDUpIC8gcmVxdWlyZWRSYXRpbyAtIDAuMDUgIC8vIE1ha2UgZGFya2VyXG4gICAgOiAoYmdMdW1pbmFuY2UgKyAwLjA1KSAqIHJlcXVpcmVkUmF0aW8gLSAwLjA1OyAvLyBNYWtlIGxpZ2h0ZXJcbiAgXG4gIC8vIEFkanVzdCBSR0IgdmFsdWVzIHRvIGFjaGlldmUgdGFyZ2V0IGx1bWluYW5jZVxuICBjb25zdCBmYWN0b3IgPSB0YXJnZXRMdW1pbmFuY2UgPiBiZ0x1bWluYW5jZSA/IDEuMiA6IDAuODtcbiAgXG4gIGNvbnN0IGFkanVzdGVkUiA9IE1hdGgubWluKDI1NSwgTWF0aC5tYXgoMCwgTWF0aC5yb3VuZChmZ1JnYi5yICogZmFjdG9yKSkpO1xuICBjb25zdCBhZGp1c3RlZEcgPSBNYXRoLm1pbigyNTUsIE1hdGgubWF4KDAsIE1hdGgucm91bmQoZmdSZ2IuZyAqIGZhY3RvcikpKTtcbiAgY29uc3QgYWRqdXN0ZWRCID0gTWF0aC5taW4oMjU1LCBNYXRoLm1heCgwLCBNYXRoLnJvdW5kKGZnUmdiLmIgKiBmYWN0b3IpKSk7XG4gIFxuICByZXR1cm4gYCMke2FkanVzdGVkUi50b1N0cmluZygxNikucGFkU3RhcnQoMiwgJzAnKX0ke2FkanVzdGVkRy50b1N0cmluZygxNikucGFkU3RhcnQoMiwgJzAnKX0ke2FkanVzdGVkQi50b1N0cmluZygxNikucGFkU3RhcnQoMiwgJzAnKX1gO1xufVxuXG4vKipcbiAqIFZpZXJsYSBhcHAgY29sb3IgcGFpcnMgZm9yIGF1ZGl0XG4gKi9cbmV4cG9ydCBjb25zdCBWSUVSTEFfQ09MT1JfUEFJUlM6IENvbG9yUGFpcltdID0gW1xuICAvLyBQcmltYXJ5IHRleHQgY29tYmluYXRpb25zXG4gIHtcbiAgICBmb3JlZ3JvdW5kOiAnIzFGMjkzNycsIC8vIGdyYXktODAwXG4gICAgYmFja2dyb3VuZDogJyNGRkZGRkYnLCAvLyB3aGl0ZVxuICAgIGNvbnRleHQ6ICdQcmltYXJ5IHRleHQgb24gd2hpdGUgYmFja2dyb3VuZCcsXG4gICAgdGV4dFNpemU6ICdub3JtYWwnLFxuICB9LFxuICB7XG4gICAgZm9yZWdyb3VuZDogJyNGRkZGRkYnLCAvLyB3aGl0ZVxuICAgIGJhY2tncm91bmQ6ICcjMUYyOTM3JywgLy8gZ3JheS04MDBcbiAgICBjb250ZXh0OiAnV2hpdGUgdGV4dCBvbiBkYXJrIGJhY2tncm91bmQnLFxuICAgIHRleHRTaXplOiAnbm9ybWFsJyxcbiAgfSxcbiAgXG4gIC8vIFByaW1hcnkgYnV0dG9uXG4gIHtcbiAgICBmb3JlZ3JvdW5kOiAnI0ZGRkZGRicsIC8vIHdoaXRlXG4gICAgYmFja2dyb3VuZDogJyMyQTRCMzInLCAvLyBwcmltYXJ5IGdyZWVuXG4gICAgY29udGV4dDogJ1ByaW1hcnkgYnV0dG9uIHRleHQnLFxuICAgIHRleHRTaXplOiAnbm9ybWFsJyxcbiAgfSxcbiAgXG4gIC8vIFNlY29uZGFyeSB0ZXh0XG4gIHtcbiAgICBmb3JlZ3JvdW5kOiAnIzZCNzI4MCcsIC8vIGdyYXktNTAwXG4gICAgYmFja2dyb3VuZDogJyNGRkZGRkYnLCAvLyB3aGl0ZVxuICAgIGNvbnRleHQ6ICdTZWNvbmRhcnkgdGV4dCBvbiB3aGl0ZSBiYWNrZ3JvdW5kJyxcbiAgICB0ZXh0U2l6ZTogJ25vcm1hbCcsXG4gIH0sXG4gIFxuICAvLyBFcnJvciBzdGF0ZXNcbiAge1xuICAgIGZvcmVncm91bmQ6ICcjREMyNjI2JywgLy8gcmVkLTYwMFxuICAgIGJhY2tncm91bmQ6ICcjRkZGRkZGJywgLy8gd2hpdGVcbiAgICBjb250ZXh0OiAnRXJyb3IgdGV4dCcsXG4gICAgdGV4dFNpemU6ICdub3JtYWwnLFxuICB9LFxuICBcbiAgLy8gU3VjY2VzcyBzdGF0ZXNcbiAge1xuICAgIGZvcmVncm91bmQ6ICcjMDU5NjY5JywgLy8gZ3JlZW4tNjAwXG4gICAgYmFja2dyb3VuZDogJyNGRkZGRkYnLCAvLyB3aGl0ZVxuICAgIGNvbnRleHQ6ICdTdWNjZXNzIHRleHQnLFxuICAgIHRleHRTaXplOiAnbm9ybWFsJyxcbiAgfSxcbiAgXG4gIC8vIFVJIENvbXBvbmVudHNcbiAge1xuICAgIGZvcmVncm91bmQ6ICcjMkE0QjMyJywgLy8gcHJpbWFyeVxuICAgIGJhY2tncm91bmQ6ICcjRjNGNEY2JywgLy8gZ3JheS0xMDBcbiAgICBjb250ZXh0OiAnUHJpbWFyeSBidXR0b24gb3V0bGluZScsXG4gICAgaXNVSUNvbXBvbmVudDogdHJ1ZSxcbiAgfSxcbiAgXG4gIC8vIExpbmtzXG4gIHtcbiAgICBmb3JlZ3JvdW5kOiAnIzI1NjNFQicsIC8vIGJsdWUtNjAwXG4gICAgYmFja2dyb3VuZDogJyNGRkZGRkYnLCAvLyB3aGl0ZVxuICAgIGNvbnRleHQ6ICdMaW5rIHRleHQnLFxuICAgIHRleHRTaXplOiAnbm9ybWFsJyxcbiAgfSxcbiAgXG4gIC8vIEZvcm0gaW5wdXRzXG4gIHtcbiAgICBmb3JlZ3JvdW5kOiAnIzFGMjkzNycsIC8vIGdyYXktODAwXG4gICAgYmFja2dyb3VuZDogJyNGOUZBRkInLCAvLyBncmF5LTUwXG4gICAgY29udGV4dDogJ0Zvcm0gaW5wdXQgdGV4dCcsXG4gICAgdGV4dFNpemU6ICdub3JtYWwnLFxuICB9LFxuICBcbiAgLy8gTmF2aWdhdGlvblxuICB7XG4gICAgZm9yZWdyb3VuZDogJyMzNzQxNTEnLCAvLyBncmF5LTcwMFxuICAgIGJhY2tncm91bmQ6ICcjRkZGRkZGJywgLy8gd2hpdGVcbiAgICBjb250ZXh0OiAnTmF2aWdhdGlvbiB0ZXh0JyxcbiAgICB0ZXh0U2l6ZTogJ25vcm1hbCcsXG4gIH0sXG5dO1xuXG4vKipcbiAqIFJ1biBjb21wbGV0ZSBjb2xvciBjb250cmFzdCBhdWRpdCBmb3IgVmllcmxhIGFwcFxuICovXG5leHBvcnQgZnVuY3Rpb24gcnVuVmllcmxhQ29udHJhc3RBdWRpdCgpIHtcbiAgcmV0dXJuIGF1ZGl0Q29sb3JQYWxldHRlKFZJRVJMQV9DT0xPUl9QQUlSUyk7XG59XG5cbi8qKlxuICogR2VuZXJhdGUgY29udHJhc3QgYXVkaXQgcmVwb3J0XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZW5lcmF0ZUNvbnRyYXN0UmVwb3J0KGF1ZGl0UmVzdWx0czogUmV0dXJuVHlwZTx0eXBlb2YgYXVkaXRDb2xvclBhbGV0dGU+KTogc3RyaW5nIHtcbiAgY29uc3QgeyByZXN1bHRzLCBzdW1tYXJ5IH0gPSBhdWRpdFJlc3VsdHM7XG4gIFxuICBsZXQgcmVwb3J0ID0gYCMgQ29sb3IgQ29udHJhc3QgQXVkaXQgUmVwb3J0XFxuXFxuYDtcbiAgcmVwb3J0ICs9IGAjIyBTdW1tYXJ5XFxuYDtcbiAgcmVwb3J0ICs9IGAtIFRvdGFsIGNvbG9yIHBhaXJzIHRlc3RlZDogJHtzdW1tYXJ5LnRvdGFsfVxcbmA7XG4gIHJlcG9ydCArPSBgLSBQYXNzaW5nIFdDQUcgQUE6ICR7c3VtbWFyeS5wYXNzaW5nfS8ke3N1bW1hcnkudG90YWx9ICgke01hdGgucm91bmQoc3VtbWFyeS5wYXNzaW5nIC8gc3VtbWFyeS50b3RhbCAqIDEwMCl9JSlcXG5gO1xuICByZXBvcnQgKz0gYC0gRmFpbGluZyBXQ0FHIEFBOiAke3N1bW1hcnkuZmFpbGluZ30vJHtzdW1tYXJ5LnRvdGFsfSAoJHtNYXRoLnJvdW5kKHN1bW1hcnkuZmFpbGluZyAvIHN1bW1hcnkudG90YWwgKiAxMDApfSUpXFxuYDtcbiAgcmVwb3J0ICs9IGAtIFdDQUcgQUFBIGNvbXBsaWFudDogJHtzdW1tYXJ5LmFhYUNvbXBsaWFudH0vJHtzdW1tYXJ5LnRvdGFsfSAoJHtNYXRoLnJvdW5kKHN1bW1hcnkuYWFhQ29tcGxpYW50IC8gc3VtbWFyeS50b3RhbCAqIDEwMCl9JSlcXG5cXG5gO1xuICBcbiAgaWYgKHN1bW1hcnkuZmFpbGluZyA+IDApIHtcbiAgICByZXBvcnQgKz0gYCMjIEZhaWxpbmcgQ29sb3IgUGFpcnNcXG5cXG5gO1xuICAgIHJlc3VsdHNcbiAgICAgIC5maWx0ZXIociA9PiAhci5hdWRpdC5wYXNzZXMuQUEpXG4gICAgICAuZm9yRWFjaChyZXN1bHQgPT4ge1xuICAgICAgICByZXBvcnQgKz0gYCMjIyAke3Jlc3VsdC5jb250ZXh0fVxcbmA7XG4gICAgICAgIHJlcG9ydCArPSBgLSBGb3JlZ3JvdW5kOiAke3Jlc3VsdC5mb3JlZ3JvdW5kfVxcbmA7XG4gICAgICAgIHJlcG9ydCArPSBgLSBCYWNrZ3JvdW5kOiAke3Jlc3VsdC5iYWNrZ3JvdW5kfVxcbmA7XG4gICAgICAgIHJlcG9ydCArPSBgLSBDb250cmFzdCBSYXRpbzogJHtyZXN1bHQuYXVkaXQucmF0aW8udG9GaXhlZCgyKX06MVxcbmA7XG4gICAgICAgIHJlcG9ydCArPSBgLSBTdGF0dXM6ICR7cmVzdWx0LmF1ZGl0LmxldmVsfVxcbmA7XG4gICAgICAgIGlmIChyZXN1bHQuYXVkaXQucmVjb21tZW5kYXRpb24pIHtcbiAgICAgICAgICByZXBvcnQgKz0gYC0gUmVjb21tZW5kYXRpb246ICR7cmVzdWx0LmF1ZGl0LnJlY29tbWVuZGF0aW9ufVxcbmA7XG4gICAgICAgIH1cbiAgICAgICAgcmVwb3J0ICs9IGBcXG5gO1xuICAgICAgfSk7XG4gIH1cbiAgXG4gIHJlcG9ydCArPSBgIyMgQWxsIFJlc3VsdHNcXG5cXG5gO1xuICByZXN1bHRzLmZvckVhY2gocmVzdWx0ID0+IHtcbiAgICBjb25zdCBzdGF0dXMgPSByZXN1bHQuYXVkaXQucGFzc2VzLkFBID8gJ+KchScgOiAn4p2MJztcbiAgICByZXBvcnQgKz0gYCR7c3RhdHVzfSAke3Jlc3VsdC5jb250ZXh0fTogJHtyZXN1bHQuYXVkaXQucmF0aW8udG9GaXhlZCgyKX06MSAoJHtyZXN1bHQuYXVkaXQubGV2ZWx9KVxcbmA7XG4gIH0pO1xuICBcbiAgcmV0dXJuIHJlcG9ydDtcbn1cbiJdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBa0JPLElBQU1BLG9CQUFvQixHQUFBQyxPQUFBLENBQUFELG9CQUFBLEdBQUc7RUFDbENFLEVBQUUsRUFBRTtJQUNGQyxVQUFVLEVBQUUsR0FBRztJQUNmQyxTQUFTLEVBQUUsR0FBRztJQUNkQyxZQUFZLEVBQUU7RUFDaEIsQ0FBQztFQUNEQyxHQUFHLEVBQUU7SUFDSEgsVUFBVSxFQUFFLEdBQUc7SUFDZkMsU0FBUyxFQUFFLEdBQUc7SUFDZEMsWUFBWSxFQUFFO0VBQ2hCO0FBQ0YsQ0FBVTtBQUdILElBQU1FLG9CQUFvQixHQUFBTixPQUFBLENBQUFNLG9CQUFBLEdBQUc7RUFDbENILFNBQVMsRUFBRTtJQUNUSSxRQUFRLEVBQUUsRUFBRTtJQUNaQyxVQUFVLEVBQUU7RUFDZCxDQUFDO0VBQ0RDLGFBQWEsRUFBRTtJQUNiRixRQUFRLEVBQUUsRUFBRTtJQUNaQyxVQUFVLEVBQUU7RUFDZDtBQUNGLENBQVU7QUF5QlYsU0FBU0UsUUFBUUEsQ0FBQ0MsR0FBVyxFQUE4QztFQUN6RSxJQUFNQyxNQUFNLEdBQUcsMkNBQTJDLENBQUNDLElBQUksQ0FBQ0YsR0FBRyxDQUFDO0VBQ3BFLE9BQU9DLE1BQU0sR0FBRztJQUNkRSxDQUFDLEVBQUVDLFFBQVEsQ0FBQ0gsTUFBTSxDQUFDLENBQUMsQ0FBQyxFQUFFLEVBQUUsQ0FBQztJQUMxQkksQ0FBQyxFQUFFRCxRQUFRLENBQUNILE1BQU0sQ0FBQyxDQUFDLENBQUMsRUFBRSxFQUFFLENBQUM7SUFDMUJLLENBQUMsRUFBRUYsUUFBUSxDQUFDSCxNQUFNLENBQUMsQ0FBQyxDQUFDLEVBQUUsRUFBRTtFQUMzQixDQUFDLEdBQUcsSUFBSTtBQUNWO0FBS0EsU0FBU00sb0JBQW9CQSxDQUFDSixDQUFTLEVBQUVFLENBQVMsRUFBRUMsQ0FBUyxFQUFVO0VBQ3JFLElBQUFFLElBQUEsR0FBcUIsQ0FBQ0wsQ0FBQyxFQUFFRSxDQUFDLEVBQUVDLENBQUMsQ0FBQyxDQUFDRyxHQUFHLENBQUMsVUFBQUMsQ0FBQyxFQUFJO01BQ3RDQSxDQUFDLEdBQUdBLENBQUMsR0FBRyxHQUFHO01BQ1gsT0FBT0EsQ0FBQyxJQUFJLE9BQU8sR0FBR0EsQ0FBQyxHQUFHLEtBQUssR0FBR0MsSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQ0YsQ0FBQyxHQUFHLEtBQUssSUFBSSxLQUFLLEVBQUUsR0FBRyxDQUFDO0lBQ3RFLENBQUMsQ0FBQztJQUFBRyxLQUFBLE9BQUFDLGVBQUEsQ0FBQUMsT0FBQSxFQUFBUCxJQUFBO0lBSEtRLEVBQUUsR0FBQUgsS0FBQTtJQUFFSSxFQUFFLEdBQUFKLEtBQUE7SUFBRUssRUFBRSxHQUFBTCxLQUFBO0VBS2pCLE9BQU8sTUFBTSxHQUFHRyxFQUFFLEdBQUcsTUFBTSxHQUFHQyxFQUFFLEdBQUcsTUFBTSxHQUFHQyxFQUFFO0FBQ2hEO0FBS08sU0FBU0Msc0JBQXNCQSxDQUFDQyxNQUFjLEVBQUVDLE1BQWMsRUFBVTtFQUM3RSxJQUFNQyxJQUFJLEdBQUd2QixRQUFRLENBQUNxQixNQUFNLENBQUM7RUFDN0IsSUFBTUcsSUFBSSxHQUFHeEIsUUFBUSxDQUFDc0IsTUFBTSxDQUFDO0VBRTdCLElBQUksQ0FBQ0MsSUFBSSxJQUFJLENBQUNDLElBQUksRUFBRTtJQUNsQixNQUFNLElBQUlDLEtBQUssQ0FBQyw4Q0FBOEMsQ0FBQztFQUNqRTtFQUVBLElBQU1DLElBQUksR0FBR2xCLG9CQUFvQixDQUFDZSxJQUFJLENBQUNuQixDQUFDLEVBQUVtQixJQUFJLENBQUNqQixDQUFDLEVBQUVpQixJQUFJLENBQUNoQixDQUFDLENBQUM7RUFDekQsSUFBTW9CLElBQUksR0FBR25CLG9CQUFvQixDQUFDZ0IsSUFBSSxDQUFDcEIsQ0FBQyxFQUFFb0IsSUFBSSxDQUFDbEIsQ0FBQyxFQUFFa0IsSUFBSSxDQUFDakIsQ0FBQyxDQUFDO0VBRXpELElBQU1xQixTQUFTLEdBQUdoQixJQUFJLENBQUNpQixHQUFHLENBQUNILElBQUksRUFBRUMsSUFBSSxDQUFDO0VBQ3RDLElBQU1HLE9BQU8sR0FBR2xCLElBQUksQ0FBQ21CLEdBQUcsQ0FBQ0wsSUFBSSxFQUFFQyxJQUFJLENBQUM7RUFFcEMsT0FBTyxDQUFDQyxTQUFTLEdBQUcsSUFBSSxLQUFLRSxPQUFPLEdBQUcsSUFBSSxDQUFDO0FBQzlDO0FBS08sU0FBU0UsY0FBY0EsQ0FBQ0MsSUFBZSxFQUF1QjtFQUNuRSxJQUFNQyxLQUFLLEdBQUdkLHNCQUFzQixDQUFDYSxJQUFJLENBQUNFLFVBQVUsRUFBRUYsSUFBSSxDQUFDRyxVQUFVLENBQUM7RUFFdEUsSUFBSUMsYUFBcUI7RUFFekIsSUFBSUosSUFBSSxDQUFDSyxhQUFhLEVBQUU7SUFDdEJELGFBQWEsR0FBR2hELG9CQUFvQixDQUFDRSxFQUFFLENBQUNHLFlBQVk7RUFDdEQsQ0FBQyxNQUFNLElBQUl1QyxJQUFJLENBQUNNLFFBQVEsS0FBSyxPQUFPLEVBQUU7SUFDcENGLGFBQWEsR0FBR2hELG9CQUFvQixDQUFDRSxFQUFFLENBQUNFLFNBQVM7RUFDbkQsQ0FBQyxNQUFNO0lBQ0w0QyxhQUFhLEdBQUdoRCxvQkFBb0IsQ0FBQ0UsRUFBRSxDQUFDQyxVQUFVO0VBQ3BEO0VBRUEsSUFBTWdELFFBQVEsR0FBR04sS0FBSyxJQUFJRyxhQUFhO0VBQ3ZDLElBQU1JLFNBQVMsR0FBR1IsSUFBSSxDQUFDSyxhQUFhLEdBQ2hDSixLQUFLLElBQUk3QyxvQkFBb0IsQ0FBQ00sR0FBRyxDQUFDRCxZQUFZLEdBQzlDdUMsSUFBSSxDQUFDTSxRQUFRLEtBQUssT0FBTyxHQUN2QkwsS0FBSyxJQUFJN0Msb0JBQW9CLENBQUNNLEdBQUcsQ0FBQ0YsU0FBUyxHQUMzQ3lDLEtBQUssSUFBSTdDLG9CQUFvQixDQUFDTSxHQUFHLENBQUNILFVBQVU7RUFFbEQsSUFBSWtELEtBQTRCO0VBQ2hDLElBQUlDLGNBQWtDO0VBRXRDLElBQUlGLFNBQVMsRUFBRTtJQUNiQyxLQUFLLEdBQUcsS0FBSztFQUNmLENBQUMsTUFBTSxJQUFJRixRQUFRLEVBQUU7SUFDbkJFLEtBQUssR0FBRyxJQUFJO0VBQ2QsQ0FBQyxNQUFNO0lBQ0xBLEtBQUssR0FBRyxNQUFNO0lBQ2RDLGNBQWMsR0FBR0Msc0JBQXNCLENBQUNYLElBQUksRUFBRUMsS0FBSyxFQUFFRyxhQUFhLENBQUM7RUFDckU7RUFFQSxPQUFPO0lBQ0xILEtBQUssRUFBTEEsS0FBSztJQUNMVyxNQUFNLEVBQUU7TUFDTnRELEVBQUUsRUFBRWlELFFBQVE7TUFDWjdDLEdBQUcsRUFBRThDO0lBQ1AsQ0FBQztJQUNEQyxLQUFLLEVBQUxBLEtBQUs7SUFDTEMsY0FBYyxFQUFkQTtFQUNGLENBQUM7QUFDSDtBQUtBLFNBQVNDLHNCQUFzQkEsQ0FBQ1gsSUFBZSxFQUFFYSxZQUFvQixFQUFFVCxhQUFxQixFQUFVO0VBQ3BHLElBQU1VLFdBQVcsR0FBR1YsYUFBYSxHQUFHUyxZQUFZO0VBRWhELElBQUlDLFdBQVcsR0FBRyxHQUFHLEVBQUU7SUFDckIsT0FBTyx1QkFBdUJkLElBQUksQ0FBQ0UsVUFBVSxrQkFBa0JGLElBQUksQ0FBQ0csVUFBVSx3Q0FBd0M7RUFDeEgsQ0FBQyxNQUFNLElBQUlXLFdBQVcsR0FBRyxDQUFDLEVBQUU7SUFDMUIsT0FBTyxxRkFBcUY7RUFDOUYsQ0FBQyxNQUFNO0lBQ0wsT0FBTywyR0FBMkc7RUFDcEg7QUFDRjtBQUtPLFNBQVNDLGlCQUFpQkEsQ0FBQ0MsVUFBdUIsRUFTdkQ7RUFDQSxJQUFNQyxPQUFPLEdBQUdELFVBQVUsQ0FBQ3ZDLEdBQUcsQ0FBQyxVQUFBdUIsSUFBSTtJQUFBLE9BQUFrQixNQUFBLENBQUFDLE1BQUEsS0FDOUJuQixJQUFJO01BQ1BvQixLQUFLLEVBQUVyQixjQUFjLENBQUNDLElBQUk7SUFBQztFQUFBLENBQzNCLENBQUM7RUFFSCxJQUFNcUIsT0FBTyxHQUFHO0lBQ2RDLEtBQUssRUFBRUwsT0FBTyxDQUFDTSxNQUFNO0lBQ3JCQyxPQUFPLEVBQUVQLE9BQU8sQ0FBQ1EsTUFBTSxDQUFDLFVBQUF0RCxDQUFDO01BQUEsT0FBSUEsQ0FBQyxDQUFDaUQsS0FBSyxDQUFDUixNQUFNLENBQUN0RCxFQUFFO0lBQUEsRUFBQyxDQUFDaUUsTUFBTTtJQUN0REcsT0FBTyxFQUFFVCxPQUFPLENBQUNRLE1BQU0sQ0FBQyxVQUFBdEQsQ0FBQztNQUFBLE9BQUksQ0FBQ0EsQ0FBQyxDQUFDaUQsS0FBSyxDQUFDUixNQUFNLENBQUN0RCxFQUFFO0lBQUEsRUFBQyxDQUFDaUUsTUFBTTtJQUN2REksV0FBVyxFQUFFVixPQUFPLENBQUNRLE1BQU0sQ0FBQyxVQUFBdEQsQ0FBQztNQUFBLE9BQUlBLENBQUMsQ0FBQ2lELEtBQUssQ0FBQ1gsS0FBSyxLQUFLLElBQUksSUFBSXRDLENBQUMsQ0FBQ2lELEtBQUssQ0FBQ1gsS0FBSyxLQUFLLEtBQUs7SUFBQSxFQUFDLENBQUNjLE1BQU07SUFDMUZLLFlBQVksRUFBRVgsT0FBTyxDQUFDUSxNQUFNLENBQUMsVUFBQXRELENBQUM7TUFBQSxPQUFJQSxDQUFDLENBQUNpRCxLQUFLLENBQUNYLEtBQUssS0FBSyxLQUFLO0lBQUEsRUFBQyxDQUFDYztFQUM3RCxDQUFDO0VBRUQsT0FBTztJQUFFTixPQUFPLEVBQVBBLE9BQU87SUFBRUksT0FBTyxFQUFQQTtFQUFRLENBQUM7QUFDN0I7QUFLTyxTQUFTUSxxQkFBcUJBLENBQ25DM0IsVUFBa0IsRUFDbEJDLFVBQWtCLEVBSVY7RUFBQSxJQUhSTSxLQUFtQixHQUFBcUIsU0FBQSxDQUFBUCxNQUFBLFFBQUFPLFNBQUEsUUFBQUMsU0FBQSxHQUFBRCxTQUFBLE1BQUcsSUFBSTtFQUFBLElBQzFCeEIsUUFBNEIsR0FBQXdCLFNBQUEsQ0FBQVAsTUFBQSxRQUFBTyxTQUFBLFFBQUFDLFNBQUEsR0FBQUQsU0FBQSxNQUFHLFFBQVE7RUFBQSxJQUN2Q3pCLGFBQXNCLEdBQUF5QixTQUFBLENBQUFQLE1BQUEsUUFBQU8sU0FBQSxRQUFBQyxTQUFBLEdBQUFELFNBQUEsTUFBRyxLQUFLO0VBRTlCLElBQU1qQixZQUFZLEdBQUcxQixzQkFBc0IsQ0FBQ2UsVUFBVSxFQUFFQyxVQUFVLENBQUM7RUFFbkUsSUFBSUMsYUFBcUI7RUFDekIsSUFBSUMsYUFBYSxFQUFFO0lBQ2pCRCxhQUFhLEdBQUdoRCxvQkFBb0IsQ0FBQ3FELEtBQUssQ0FBQyxDQUFDaEQsWUFBWTtFQUMxRCxDQUFDLE1BQU0sSUFBSTZDLFFBQVEsS0FBSyxPQUFPLEVBQUU7SUFDL0JGLGFBQWEsR0FBR2hELG9CQUFvQixDQUFDcUQsS0FBSyxDQUFDLENBQUNqRCxTQUFTO0VBQ3ZELENBQUMsTUFBTTtJQUNMNEMsYUFBYSxHQUFHaEQsb0JBQW9CLENBQUNxRCxLQUFLLENBQUMsQ0FBQ2xELFVBQVU7RUFDeEQ7RUFFQSxJQUFJc0QsWUFBWSxJQUFJVCxhQUFhLEVBQUU7SUFDakMsT0FBT0YsVUFBVTtFQUNuQjtFQUdBLElBQU04QixLQUFLLEdBQUdqRSxRQUFRLENBQUNtQyxVQUFVLENBQUM7RUFDbEMsSUFBTStCLEtBQUssR0FBR2xFLFFBQVEsQ0FBQ29DLFVBQVUsQ0FBQztFQUVsQyxJQUFJLENBQUM2QixLQUFLLElBQUksQ0FBQ0MsS0FBSyxFQUFFO0lBQ3BCLE1BQU0sSUFBSXpDLEtBQUssQ0FBQyxzQkFBc0IsQ0FBQztFQUN6QztFQUVBLElBQU0wQyxXQUFXLEdBQUczRCxvQkFBb0IsQ0FBQzBELEtBQUssQ0FBQzlELENBQUMsRUFBRThELEtBQUssQ0FBQzVELENBQUMsRUFBRTRELEtBQUssQ0FBQzNELENBQUMsQ0FBQztFQUduRSxJQUFNNkQsZUFBZSxHQUFHRCxXQUFXLEdBQUcsR0FBRyxHQUNyQyxDQUFDQSxXQUFXLEdBQUcsSUFBSSxJQUFJOUIsYUFBYSxHQUFHLElBQUksR0FDM0MsQ0FBQzhCLFdBQVcsR0FBRyxJQUFJLElBQUk5QixhQUFhLEdBQUcsSUFBSTtFQUcvQyxJQUFNZ0MsTUFBTSxHQUFHRCxlQUFlLEdBQUdELFdBQVcsR0FBRyxHQUFHLEdBQUcsR0FBRztFQUV4RCxJQUFNRyxTQUFTLEdBQUcxRCxJQUFJLENBQUNtQixHQUFHLENBQUMsR0FBRyxFQUFFbkIsSUFBSSxDQUFDaUIsR0FBRyxDQUFDLENBQUMsRUFBRWpCLElBQUksQ0FBQzJELEtBQUssQ0FBQ04sS0FBSyxDQUFDN0QsQ0FBQyxHQUFHaUUsTUFBTSxDQUFDLENBQUMsQ0FBQztFQUMxRSxJQUFNRyxTQUFTLEdBQUc1RCxJQUFJLENBQUNtQixHQUFHLENBQUMsR0FBRyxFQUFFbkIsSUFBSSxDQUFDaUIsR0FBRyxDQUFDLENBQUMsRUFBRWpCLElBQUksQ0FBQzJELEtBQUssQ0FBQ04sS0FBSyxDQUFDM0QsQ0FBQyxHQUFHK0QsTUFBTSxDQUFDLENBQUMsQ0FBQztFQUMxRSxJQUFNSSxTQUFTLEdBQUc3RCxJQUFJLENBQUNtQixHQUFHLENBQUMsR0FBRyxFQUFFbkIsSUFBSSxDQUFDaUIsR0FBRyxDQUFDLENBQUMsRUFBRWpCLElBQUksQ0FBQzJELEtBQUssQ0FBQ04sS0FBSyxDQUFDMUQsQ0FBQyxHQUFHOEQsTUFBTSxDQUFDLENBQUMsQ0FBQztFQUUxRSxPQUFPLElBQUlDLFNBQVMsQ0FBQ0ksUUFBUSxDQUFDLEVBQUUsQ0FBQyxDQUFDQyxRQUFRLENBQUMsQ0FBQyxFQUFFLEdBQUcsQ0FBQyxHQUFHSCxTQUFTLENBQUNFLFFBQVEsQ0FBQyxFQUFFLENBQUMsQ0FBQ0MsUUFBUSxDQUFDLENBQUMsRUFBRSxHQUFHLENBQUMsR0FBR0YsU0FBUyxDQUFDQyxRQUFRLENBQUMsRUFBRSxDQUFDLENBQUNDLFFBQVEsQ0FBQyxDQUFDLEVBQUUsR0FBRyxDQUFDLEVBQUU7QUFDMUk7QUFLTyxJQUFNQyxrQkFBK0IsR0FBQXRGLE9BQUEsQ0FBQXNGLGtCQUFBLEdBQUcsQ0FFN0M7RUFDRXpDLFVBQVUsRUFBRSxTQUFTO0VBQ3JCQyxVQUFVLEVBQUUsU0FBUztFQUNyQnlDLE9BQU8sRUFBRSxrQ0FBa0M7RUFDM0N0QyxRQUFRLEVBQUU7QUFDWixDQUFDLEVBQ0Q7RUFDRUosVUFBVSxFQUFFLFNBQVM7RUFDckJDLFVBQVUsRUFBRSxTQUFTO0VBQ3JCeUMsT0FBTyxFQUFFLCtCQUErQjtFQUN4Q3RDLFFBQVEsRUFBRTtBQUNaLENBQUMsRUFHRDtFQUNFSixVQUFVLEVBQUUsU0FBUztFQUNyQkMsVUFBVSxFQUFFLFNBQVM7RUFDckJ5QyxPQUFPLEVBQUUscUJBQXFCO0VBQzlCdEMsUUFBUSxFQUFFO0FBQ1osQ0FBQyxFQUdEO0VBQ0VKLFVBQVUsRUFBRSxTQUFTO0VBQ3JCQyxVQUFVLEVBQUUsU0FBUztFQUNyQnlDLE9BQU8sRUFBRSxvQ0FBb0M7RUFDN0N0QyxRQUFRLEVBQUU7QUFDWixDQUFDLEVBR0Q7RUFDRUosVUFBVSxFQUFFLFNBQVM7RUFDckJDLFVBQVUsRUFBRSxTQUFTO0VBQ3JCeUMsT0FBTyxFQUFFLFlBQVk7RUFDckJ0QyxRQUFRLEVBQUU7QUFDWixDQUFDLEVBR0Q7RUFDRUosVUFBVSxFQUFFLFNBQVM7RUFDckJDLFVBQVUsRUFBRSxTQUFTO0VBQ3JCeUMsT0FBTyxFQUFFLGNBQWM7RUFDdkJ0QyxRQUFRLEVBQUU7QUFDWixDQUFDLEVBR0Q7RUFDRUosVUFBVSxFQUFFLFNBQVM7RUFDckJDLFVBQVUsRUFBRSxTQUFTO0VBQ3JCeUMsT0FBTyxFQUFFLHdCQUF3QjtFQUNqQ3ZDLGFBQWEsRUFBRTtBQUNqQixDQUFDLEVBR0Q7RUFDRUgsVUFBVSxFQUFFLFNBQVM7RUFDckJDLFVBQVUsRUFBRSxTQUFTO0VBQ3JCeUMsT0FBTyxFQUFFLFdBQVc7RUFDcEJ0QyxRQUFRLEVBQUU7QUFDWixDQUFDLEVBR0Q7RUFDRUosVUFBVSxFQUFFLFNBQVM7RUFDckJDLFVBQVUsRUFBRSxTQUFTO0VBQ3JCeUMsT0FBTyxFQUFFLGlCQUFpQjtFQUMxQnRDLFFBQVEsRUFBRTtBQUNaLENBQUMsRUFHRDtFQUNFSixVQUFVLEVBQUUsU0FBUztFQUNyQkMsVUFBVSxFQUFFLFNBQVM7RUFDckJ5QyxPQUFPLEVBQUUsaUJBQWlCO0VBQzFCdEMsUUFBUSxFQUFFO0FBQ1osQ0FBQyxDQUNGO0FBS00sU0FBU3VDLHNCQUFzQkEsQ0FBQSxFQUFHO0VBQ3ZDLE9BQU85QixpQkFBaUIsQ0FBQzRCLGtCQUFrQixDQUFDO0FBQzlDO0FBS08sU0FBU0csc0JBQXNCQSxDQUFDQyxZQUFrRCxFQUFVO0VBQ2pHLElBQVE5QixPQUFPLEdBQWM4QixZQUFZLENBQWpDOUIsT0FBTztJQUFFSSxPQUFPLEdBQUswQixZQUFZLENBQXhCMUIsT0FBTztFQUV4QixJQUFJMkIsTUFBTSxHQUFHLG1DQUFtQztFQUNoREEsTUFBTSxJQUFJLGNBQWM7RUFDeEJBLE1BQU0sSUFBSSwrQkFBK0IzQixPQUFPLENBQUNDLEtBQUssSUFBSTtFQUMxRDBCLE1BQU0sSUFBSSxzQkFBc0IzQixPQUFPLENBQUNHLE9BQU8sSUFBSUgsT0FBTyxDQUFDQyxLQUFLLEtBQUszQyxJQUFJLENBQUMyRCxLQUFLLENBQUNqQixPQUFPLENBQUNHLE9BQU8sR0FBR0gsT0FBTyxDQUFDQyxLQUFLLEdBQUcsR0FBRyxDQUFDLE1BQU07RUFDNUgwQixNQUFNLElBQUksc0JBQXNCM0IsT0FBTyxDQUFDSyxPQUFPLElBQUlMLE9BQU8sQ0FBQ0MsS0FBSyxLQUFLM0MsSUFBSSxDQUFDMkQsS0FBSyxDQUFDakIsT0FBTyxDQUFDSyxPQUFPLEdBQUdMLE9BQU8sQ0FBQ0MsS0FBSyxHQUFHLEdBQUcsQ0FBQyxNQUFNO0VBQzVIMEIsTUFBTSxJQUFJLHlCQUF5QjNCLE9BQU8sQ0FBQ08sWUFBWSxJQUFJUCxPQUFPLENBQUNDLEtBQUssS0FBSzNDLElBQUksQ0FBQzJELEtBQUssQ0FBQ2pCLE9BQU8sQ0FBQ08sWUFBWSxHQUFHUCxPQUFPLENBQUNDLEtBQUssR0FBRyxHQUFHLENBQUMsUUFBUTtFQUUzSSxJQUFJRCxPQUFPLENBQUNLLE9BQU8sR0FBRyxDQUFDLEVBQUU7SUFDdkJzQixNQUFNLElBQUksNEJBQTRCO0lBQ3RDL0IsT0FBTyxDQUNKUSxNQUFNLENBQUMsVUFBQXRELENBQUM7TUFBQSxPQUFJLENBQUNBLENBQUMsQ0FBQ2lELEtBQUssQ0FBQ1IsTUFBTSxDQUFDdEQsRUFBRTtJQUFBLEVBQUMsQ0FDL0IyRixPQUFPLENBQUMsVUFBQWhGLE1BQU0sRUFBSTtNQUNqQitFLE1BQU0sSUFBSSxPQUFPL0UsTUFBTSxDQUFDMkUsT0FBTyxJQUFJO01BQ25DSSxNQUFNLElBQUksaUJBQWlCL0UsTUFBTSxDQUFDaUMsVUFBVSxJQUFJO01BQ2hEOEMsTUFBTSxJQUFJLGlCQUFpQi9FLE1BQU0sQ0FBQ2tDLFVBQVUsSUFBSTtNQUNoRDZDLE1BQU0sSUFBSSxxQkFBcUIvRSxNQUFNLENBQUNtRCxLQUFLLENBQUNuQixLQUFLLENBQUNpRCxPQUFPLENBQUMsQ0FBQyxDQUFDLE1BQU07TUFDbEVGLE1BQU0sSUFBSSxhQUFhL0UsTUFBTSxDQUFDbUQsS0FBSyxDQUFDWCxLQUFLLElBQUk7TUFDN0MsSUFBSXhDLE1BQU0sQ0FBQ21ELEtBQUssQ0FBQ1YsY0FBYyxFQUFFO1FBQy9Cc0MsTUFBTSxJQUFJLHFCQUFxQi9FLE1BQU0sQ0FBQ21ELEtBQUssQ0FBQ1YsY0FBYyxJQUFJO01BQ2hFO01BQ0FzQyxNQUFNLElBQUksSUFBSTtJQUNoQixDQUFDLENBQUM7RUFDTjtFQUVBQSxNQUFNLElBQUksb0JBQW9CO0VBQzlCL0IsT0FBTyxDQUFDZ0MsT0FBTyxDQUFDLFVBQUFoRixNQUFNLEVBQUk7SUFDeEIsSUFBTWtGLE1BQU0sR0FBR2xGLE1BQU0sQ0FBQ21ELEtBQUssQ0FBQ1IsTUFBTSxDQUFDdEQsRUFBRSxHQUFHLEdBQUcsR0FBRyxHQUFHO0lBQ2pEMEYsTUFBTSxJQUFJLEdBQUdHLE1BQU0sSUFBSWxGLE1BQU0sQ0FBQzJFLE9BQU8sS0FBSzNFLE1BQU0sQ0FBQ21ELEtBQUssQ0FBQ25CLEtBQUssQ0FBQ2lELE9BQU8sQ0FBQyxDQUFDLENBQUMsT0FBT2pGLE1BQU0sQ0FBQ21ELEtBQUssQ0FBQ1gsS0FBSyxLQUFLO0VBQ3ZHLENBQUMsQ0FBQztFQUVGLE9BQU91QyxNQUFNO0FBQ2YiLCJpZ25vcmVMaXN0IjpbXX0=