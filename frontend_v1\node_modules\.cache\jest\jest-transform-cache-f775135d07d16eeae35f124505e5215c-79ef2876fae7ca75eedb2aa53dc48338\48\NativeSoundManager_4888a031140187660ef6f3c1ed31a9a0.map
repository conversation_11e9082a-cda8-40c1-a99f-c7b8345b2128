{"version": 3, "names": ["_NativeSoundManager", "_interopRequireWildcard", "require", "Object", "keys", "for<PERSON>ach", "key", "prototype", "hasOwnProperty", "call", "_exportNames", "exports", "defineProperty", "enumerable", "get", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "set", "_t", "getOwnPropertyDescriptor", "_default", "NativeSoundManager"], "sources": ["NativeSoundManager.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict\n * @format\n */\n\nexport * from '../../../src/private/specs_DEPRECATED/modules/NativeSoundManager';\nimport NativeSoundManager from '../../../src/private/specs_DEPRECATED/modules/NativeSoundManager';\nexport default NativeSoundManager;\n"], "mappings": ";;;;;AAUA,IAAAA,mBAAA,GAAAC,uBAAA,CAAAC,OAAA;AAAAC,MAAA,CAAAC,IAAA,CAAAJ,mBAAA,EAAAK,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAC,YAAA,EAAAJ,GAAA;EAAA,IAAAA,GAAA,IAAAK,OAAA,IAAAA,OAAA,CAAAL,GAAA,MAAAN,mBAAA,CAAAM,GAAA;EAAAH,MAAA,CAAAS,cAAA,CAAAD,OAAA,EAAAL,GAAA;IAAAO,UAAA;IAAAC,GAAA,WAAAA,IAAA;MAAA,OAAAd,mBAAA,CAAAM,GAAA;IAAA;EAAA;AAAA;AAAiF,SAAAL,wBAAAc,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAhB,uBAAA,YAAAA,wBAAAc,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAP,GAAA,CAAAC,CAAA,GAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,EAAAQ,CAAA,cAAAK,EAAA,IAAAb,CAAA,gBAAAa,EAAA,OAAApB,cAAA,CAAAC,IAAA,CAAAM,CAAA,EAAAa,EAAA,OAAAN,CAAA,IAAAD,CAAA,GAAAlB,MAAA,CAAAS,cAAA,KAAAT,MAAA,CAAA0B,wBAAA,CAAAd,CAAA,EAAAa,EAAA,OAAAN,CAAA,CAAAR,GAAA,IAAAQ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAK,EAAA,EAAAN,CAAA,IAAAC,CAAA,CAAAK,EAAA,IAAAb,CAAA,CAAAa,EAAA,WAAAL,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAAA,IAAAc,QAAA,GAAAnB,OAAA,CAAAc,OAAA,GAElEM,2BAAkB", "ignoreList": []}