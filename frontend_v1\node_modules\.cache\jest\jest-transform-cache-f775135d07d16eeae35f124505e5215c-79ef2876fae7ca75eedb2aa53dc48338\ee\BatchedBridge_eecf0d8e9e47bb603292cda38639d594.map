{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "MessageQueue", "require", "BatchedBridge", "global", "configurable", "_default"], "sources": ["BatchedBridge.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict\n */\n\n'use strict';\n\nimport typeof MessageQueueT from './MessageQueue';\n\nconst MessageQueue: MessageQueueT = require('./MessageQueue').default;\n\nconst BatchedBridge: MessageQueue = new MessageQueue();\n\n// Wire up the batched bridge on the global object so that we can call into it.\n// Ideally, this would be the inverse relationship. I.e. the native environment\n// provides this global directly with its script embedded. Then this module\n// would export it. A possible fix would be to trim the dependencies in\n// MessageQueue to its minimal features and embed that in the native runtime.\n\nObject.defineProperty(global, '__fbBatchedBridge', {\n  configurable: true,\n  value: BatchedBridge,\n});\n\nexport default BatchedBridge;\n"], "mappings": "AAUA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAIb,IAAMC,YAA2B,GAAGC,OAAO,iBAAiB,CAAC,CAACF,OAAO;AAErE,IAAMG,aAA2B,GAAG,IAAIF,YAAY,CAAC,CAAC;AAQtDL,MAAM,CAACC,cAAc,CAACO,MAAM,EAAE,mBAAmB,EAAE;EACjDC,YAAY,EAAE,IAAI;EAClBN,KAAK,EAAEI;AACT,CAAC,CAAC;AAAC,IAAAG,QAAA,GAAAR,OAAA,CAAAE,OAAA,GAEYG,aAAa", "ignoreList": []}