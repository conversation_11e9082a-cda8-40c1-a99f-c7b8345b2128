/**
 * Test Accounts Configuration - Backend Integration
 *
 * This file contains all test accounts available in the backend for easy
 * integration and testing in the frontend_v1 application.
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

export interface TestAccount {
  id?: string;
  email: string;
  password: string;
  role: 'customer' | 'service_provider';
  firstName: string;
  lastName: string;
  businessName?: string;
  category?: string;
  city?: string;
  description?: string;
  phone?: string;
  avatar?: string;
}

// Customer Test Accounts
export const CUSTOMER_TEST_ACCOUNTS: TestAccount[] = [
  {
    id: 'customer_1',
    email: '<EMAIL>',
    password: 'testpass123',
    role: 'customer',
    firstName: 'Test',
    lastName: 'User',
    description: 'Basic test customer account',
  },
  {
    id: 'customer_2',
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'customer',
    firstName: 'Sarah',
    lastName: 'Johnson',
    description: 'Premium customer account',
  },
  {
    id: 'customer_3',
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'customer',
    firstName: 'Michael',
    lastName: 'Chen',
    description: 'Regular customer account',
  },
  {
    id: 'customer_4',
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'customer',
    firstName: 'Emily',
    lastName: 'Davis',
    description: 'Frequent customer account',
  },
  {
    id: 'customer_5',
    email: '<EMAIL>',
    password: 'testpass123',
    role: 'customer',
    firstName: 'Vierla',
    lastName: 'Test',
    description: 'Vierla test customer account',
  },
  {
    id: 'customer_6',
    email: '<EMAIL>',
    password: 'testpass123',
    role: 'customer',
    firstName: 'Test',
    lastName: 'Customer',
    description: 'Basic test customer account',
  },
  {
    id: 'provider_test',
    email: '<EMAIL>',
    password: 'testpass123',
    role: 'service_provider',
    firstName: 'Test',
    lastName: 'Provider',
    businessName: 'Test Provider Business',
    category: 'General',
    city: 'Ottawa',
    description: 'General test provider account',
  },
];

// Service Provider Test Accounts by Category
export const BARBER_PROVIDERS: TestAccount[] = [
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'service_provider',
    firstName: 'Marcus',
    lastName: 'Johnson',
    businessName: 'Elite Cuts Barbershop',
    category: 'Barber',
    city: 'Ottawa',
    description: 'Traditional barbering, classic cuts, and beard grooming',
  },
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'service_provider',
    firstName: 'David',
    lastName: 'Thompson',
    businessName: 'Classic Barber Co',
    category: 'Barber',
    city: 'Toronto',
    description: 'Traditional and contemporary men\'s grooming services',
  },
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'service_provider',
    firstName: 'James',
    lastName: 'Wilson',
    businessName: 'Modern Cuts Barbershop',
    category: 'Barber',
    city: 'Ottawa',
    description: 'Modern barbering techniques with classic service',
  },
];

export const SALON_PROVIDERS: TestAccount[] = [
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'service_provider',
    firstName: 'Emma',
    lastName: 'Rodriguez',
    businessName: 'Trendy Cuts Salon',
    category: 'Salon',
    city: 'Ottawa',
    description: 'Creative hair designs and color specialists',
  },
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'service_provider',
    firstName: 'Lisa',
    lastName: 'Wang',
    businessName: 'Luxe Hair Boutique',
    category: 'Salon',
    city: 'Ottawa',
    description: 'Premium hair care and styling services',
  },
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'service_provider',
    firstName: 'Sarah',
    lastName: 'Mitchell',
    businessName: 'Bella Hair Studio',
    category: 'Salon',
    city: 'Toronto',
    description: 'Full-service salon specializing in color and styling',
  },
];

export const NAIL_SERVICES_PROVIDERS: TestAccount[] = [
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'service_provider',
    firstName: 'Provider1',
    lastName: 'Nail',
    businessName: 'Nail Services Studio 1',
    category: 'Nail Services',
    city: 'Ottawa',
    description: 'Manicures, pedicures, nail art, and nail care',
  },
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'service_provider',
    firstName: 'Provider2',
    lastName: 'Nail',
    businessName: 'Nail Services Studio 2',
    category: 'Nail Services',
    city: 'Ottawa',
    description: 'Manicures, pedicures, nail art, and nail care',
  },
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'service_provider',
    firstName: 'Provider3',
    lastName: 'Nail',
    businessName: 'Nail Services Studio 3',
    category: 'Nail Services',
    city: 'Toronto',
    description: 'Manicures, pedicures, nail art, and nail care',
  },
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'service_provider',
    firstName: 'Provider4',
    lastName: 'Nail',
    businessName: 'Nail Services Studio 4',
    category: 'Nail Services',
    city: 'Toronto',
    description: 'Manicures, pedicures, nail art, and nail care',
  },
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'service_provider',
    firstName: 'Provider5',
    lastName: 'Nail',
    businessName: 'Nail Services Studio 5',
    category: 'Nail Services',
    city: 'Ottawa',
    description: 'Manicures, pedicures, nail art, and nail care',
  },
];

export const LASH_SERVICES_PROVIDERS: TestAccount[] = [
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'service_provider',
    firstName: 'Provider1',
    lastName: 'Lash',
    businessName: 'Lash Services Studio 1',
    category: 'Lash Services',
    city: 'Ottawa',
    description: 'Eyelash extensions, lifts, tinting, and brow services',
  },
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'service_provider',
    firstName: 'Provider2',
    lastName: 'Lash',
    businessName: 'Lash Services Studio 2',
    category: 'Lash Services',
    city: 'Ottawa',
    description: 'Eyelash extensions, lifts, tinting, and brow services',
  },
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'service_provider',
    firstName: 'Provider3',
    lastName: 'Lash',
    businessName: 'Lash Services Studio 3',
    category: 'Lash Services',
    city: 'Toronto',
    description: 'Eyelash extensions, lifts, tinting, and brow services',
  },
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'service_provider',
    firstName: 'Provider4',
    lastName: 'Lash',
    businessName: 'Lash Services Studio 4',
    category: 'Lash Services',
    city: 'Toronto',
    description: 'Eyelash extensions, lifts, tinting, and brow services',
  },
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'service_provider',
    firstName: 'Provider5',
    lastName: 'Lash',
    businessName: 'Lash Services Studio 5',
    category: 'Lash Services',
    city: 'Ottawa',
    description: 'Eyelash extensions, lifts, tinting, and brow services',
  },
];

export const BRAIDING_PROVIDERS: TestAccount[] = [
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'service_provider',
    firstName: 'Provider1',
    lastName: 'Braiding',
    businessName: 'Braiding Studio 1',
    category: 'Braiding',
    city: 'Ottawa',
    description: 'Professional braiding and protective styling',
  },
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'service_provider',
    firstName: 'Provider2',
    lastName: 'Braiding',
    businessName: 'Braiding Studio 2',
    category: 'Braiding',
    city: 'Ottawa',
    description: 'Professional braiding and protective styling',
  },
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'service_provider',
    firstName: 'Provider3',
    lastName: 'Braiding',
    businessName: 'Braiding Studio 3',
    category: 'Braiding',
    city: 'Toronto',
    description: 'Professional braiding and protective styling',
  },
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'service_provider',
    firstName: 'Provider4',
    lastName: 'Braiding',
    businessName: 'Braiding Studio 4',
    category: 'Braiding',
    city: 'Toronto',
    description: 'Professional braiding and protective styling',
  },
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'service_provider',
    firstName: 'Provider5',
    lastName: 'Braiding',
    businessName: 'Braiding Studio 5',
    category: 'Braiding',
    city: 'Ottawa',
    description: 'Professional braiding and protective styling',
  },
];

export const MASSAGE_PROVIDERS: TestAccount[] = [
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'service_provider',
    firstName: 'Provider1',
    lastName: 'Massage',
    businessName: 'Massage Studio 1',
    category: 'Massage',
    city: 'Ottawa',
    description: 'Therapeutic and relaxation massage services',
  },
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'service_provider',
    firstName: 'Provider2',
    lastName: 'Massage',
    businessName: 'Massage Studio 2',
    category: 'Massage',
    city: 'Ottawa',
    description: 'Therapeutic and relaxation massage services',
  },
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'service_provider',
    firstName: 'Provider3',
    lastName: 'Massage',
    businessName: 'Massage Studio 3',
    category: 'Massage',
    city: 'Toronto',
    description: 'Therapeutic and relaxation massage services',
  },
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'service_provider',
    firstName: 'Provider4',
    lastName: 'Massage',
    businessName: 'Massage Studio 4',
    category: 'Massage',
    city: 'Toronto',
    description: 'Therapeutic and relaxation massage services',
  },
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'service_provider',
    firstName: 'Provider5',
    lastName: 'Massage',
    businessName: 'Massage Studio 5',
    category: 'Massage',
    city: 'Ottawa',
    description: 'Therapeutic and relaxation massage services',
  },
];

export const SKINCARE_PROVIDERS: TestAccount[] = [
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'service_provider',
    firstName: 'Provider1',
    lastName: 'Skincare',
    businessName: 'Skincare Studio 1',
    category: 'Skincare',
    city: 'Ottawa',
    description:
      'Facial treatments, skincare consultations, and beauty treatments',
  },
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'service_provider',
    firstName: 'Provider2',
    lastName: 'Skincare',
    businessName: 'Skincare Studio 2',
    category: 'Skincare',
    city: 'Ottawa',
    description:
      'Facial treatments, skincare consultations, and beauty treatments',
  },
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'service_provider',
    firstName: 'Provider3',
    lastName: 'Skincare',
    businessName: 'Skincare Studio 3',
    category: 'Skincare',
    city: 'Toronto',
    description:
      'Facial treatments, skincare consultations, and beauty treatments',
  },
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'service_provider',
    firstName: 'Provider4',
    lastName: 'Skincare',
    businessName: 'Skincare Studio 4',
    category: 'Skincare',
    city: 'Toronto',
    description:
      'Facial treatments, skincare consultations, and beauty treatments',
  },
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'service_provider',
    firstName: 'Provider5',
    lastName: 'Skincare',
    businessName: 'Skincare Studio 5',
    category: 'Skincare',
    city: 'Ottawa',
    description:
      'Facial treatments, skincare consultations, and beauty treatments',
  },
];

// All Service Provider Accounts Combined
export const ALL_SERVICE_PROVIDERS: TestAccount[] = [
  ...BARBER_PROVIDERS,
  ...SALON_PROVIDERS,
  ...NAIL_SERVICES_PROVIDERS,
  ...LASH_SERVICES_PROVIDERS,
  ...BRAIDING_PROVIDERS,
  ...MASSAGE_PROVIDERS,
  ...SKINCARE_PROVIDERS,
];

// All Test Accounts Combined
export const ALL_TEST_ACCOUNTS: TestAccount[] = [
  ...CUSTOMER_TEST_ACCOUNTS,
  ...ALL_SERVICE_PROVIDERS,
];

// Utility Functions
export const getTestAccountsByRole = (
  role: 'customer' | 'service_provider',
): TestAccount[] => {
  return ALL_TEST_ACCOUNTS.filter(account => account.role === role);
};

export const getTestAccountsByCategory = (category: string): TestAccount[] => {
  return ALL_SERVICE_PROVIDERS.filter(account => account.category === category);
};

export const getTestAccountsByCity = (city: string): TestAccount[] => {
  return ALL_TEST_ACCOUNTS.filter(account => account.city === city);
};

export const getRandomTestAccount = (
  role?: 'customer' | 'service_provider',
): TestAccount => {
  const accounts = role ? getTestAccountsByRole(role) : ALL_TEST_ACCOUNTS;
  const randomIndex = Math.floor(Math.random() * accounts.length);
  return accounts[randomIndex];
};

export const findTestAccountByEmail = (
  email: string,
): TestAccount | undefined => {
  return ALL_TEST_ACCOUNTS.find(account => account.email === email);
};

// Quick Access Constants
export const QUICK_LOGIN_ACCOUNTS = {
  CUSTOMER: CUSTOMER_TEST_ACCOUNTS[0],
  BARBER_PROVIDER: BARBER_PROVIDERS[0],
  SALON_PROVIDER: SALON_PROVIDERS[0],
  NAIL_PROVIDER: NAIL_SERVICES_PROVIDERS[0],
  LASH_PROVIDER: LASH_SERVICES_PROVIDERS[0],
  BRAIDING_PROVIDER: BRAIDING_PROVIDERS[0],
  MASSAGE_PROVIDER: MASSAGE_PROVIDERS[0],
  SKINCARE_PROVIDER: SKINCARE_PROVIDERS[0],
};

// Development Helper - Account Summary
export const TEST_ACCOUNTS_SUMMARY = {
  total: ALL_TEST_ACCOUNTS.length,
  customers: CUSTOMER_TEST_ACCOUNTS.length,
  providers: ALL_SERVICE_PROVIDERS.length,
  categories: {
    'Barber': BARBER_PROVIDERS.length,
    'Salon': SALON_PROVIDERS.length,
    'Nail Services': NAIL_SERVICES_PROVIDERS.length,
    'Lash Services': LASH_SERVICES_PROVIDERS.length,
    Braiding: BRAIDING_PROVIDERS.length,
    Massage: MASSAGE_PROVIDERS.length,
    Skincare: SKINCARE_PROVIDERS.length,
  },
  cities: {
    Ottawa: getTestAccountsByCity('Ottawa').length,
    Toronto: getTestAccountsByCity('Toronto').length,
  },
};
