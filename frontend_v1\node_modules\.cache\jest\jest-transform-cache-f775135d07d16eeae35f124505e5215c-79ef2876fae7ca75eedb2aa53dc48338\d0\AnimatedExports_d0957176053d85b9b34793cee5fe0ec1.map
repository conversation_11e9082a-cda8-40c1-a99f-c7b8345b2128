{"version": 3, "names": ["_Platform", "_interopRequireDefault", "require", "_AnimatedImplementation", "_AnimatedMock", "Animated", "Platform", "isDisableAnimations", "AnimatedMock", "AnimatedImplementation", "_default", "exports", "default", "Object", "assign", "FlatList", "Image", "ScrollView", "SectionList", "Text", "View"], "sources": ["AnimatedExports.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n * @format\n * @oncall react_native\n */\n\nimport typeof AnimatedFlatList from './components/AnimatedFlatList';\nimport typeof AnimatedImage from './components/AnimatedImage';\nimport typeof AnimatedScrollView from './components/AnimatedScrollView';\nimport typeof AnimatedSectionList from './components/AnimatedSectionList';\nimport typeof AnimatedText from './components/AnimatedText';\nimport typeof AnimatedView from './components/AnimatedView';\n\nimport Platform from '../Utilities/Platform';\nimport AnimatedImplementation from './AnimatedImplementation';\nimport AnimatedMock from './AnimatedMock';\n\nconst Animated: typeof AnimatedImplementation = Platform.isDisableAnimations\n  ? AnimatedMock\n  : AnimatedImplementation;\n\nexport default {\n  get FlatList(): AnimatedFlatList {\n    return require('./components/AnimatedFlatList').default;\n  },\n  get Image(): AnimatedImage {\n    return require('./components/AnimatedImage').default;\n  },\n  get ScrollView(): AnimatedScrollView {\n    return require('./components/AnimatedScrollView').default;\n  },\n  get SectionList(): AnimatedSectionList {\n    return require('./components/AnimatedSectionList').default;\n  },\n  get Text(): AnimatedText {\n    return require('./components/AnimatedText').default;\n  },\n  get View(): AnimatedView {\n    return require('./components/AnimatedView').default;\n  },\n  ...Animated,\n};\n"], "mappings": ";;;;;AAkBA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,uBAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,aAAA,GAAAH,sBAAA,CAAAC,OAAA;AAEA,IAAMG,QAAuC,GAAGC,iBAAQ,CAACC,mBAAmB,GACxEC,qBAAY,GACZC,+BAAsB;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAAAC,MAAA,CAAAC,MAAA;EAGzB,IAAIC,QAAQA,CAAA,EAAqB;IAC/B,OAAOb,OAAO,gCAAgC,CAAC,CAACU,OAAO;EACzD,CAAC;EACD,IAAII,KAAKA,CAAA,EAAkB;IACzB,OAAOd,OAAO,6BAA6B,CAAC,CAACU,OAAO;EACtD,CAAC;EACD,IAAIK,UAAUA,CAAA,EAAuB;IACnC,OAAOf,OAAO,kCAAkC,CAAC,CAACU,OAAO;EAC3D,CAAC;EACD,IAAIM,WAAWA,CAAA,EAAwB;IACrC,OAAOhB,OAAO,mCAAmC,CAAC,CAACU,OAAO;EAC5D,CAAC;EACD,IAAIO,IAAIA,CAAA,EAAiB;IACvB,OAAOjB,OAAO,4BAA4B,CAAC,CAACU,OAAO;EACrD,CAAC;EACD,IAAIQ,IAAIA,CAAA,EAAiB;IACvB,OAAOlB,OAAO,4BAA4B,CAAC,CAACU,OAAO;EACrD;AAAC,GACEP,QAAQ", "ignoreList": []}