{"version": 3, "names": ["_react", "require", "_performanceMonitor", "usePerformance", "exports", "options", "arguments", "length", "undefined", "_options$componentNam", "componentName", "_options$trackRenders", "trackRenders", "_options$trackInterac", "trackInteractions", "_options$enableProfil", "enableProfiling", "__DEV__", "renderStartRef", "useRef", "renderCountRef", "renderTimesRef", "mountTimeRef", "Date", "now", "useEffect", "mountTime", "current", "performanceMonitor", "trackRender", "type", "renderCount", "renderTime", "push", "slice", "measureRender", "useCallback", "trackInteraction", "_ref", "_asyncToGenerator2", "default", "name", "fn", "startTime", "duration", "trackUserInteraction", "_x", "_x2", "apply", "trackAsyncOperation", "_ref2", "operation", "result", "error", "Error", "message", "_x3", "_x4", "getComponentStats", "averageRenderTime", "reduce", "sum", "time", "lastRenderTime", "useMemo", "useNetworkPerformance", "trackRequest", "url", "method", "endTime", "statusCode", "cached", "responseTime", "trackNetworkRequest", "useOptimizedState", "initialValue", "_React$useState", "React", "useState", "_React$useState2", "_slicedToArray2", "state", "setState", "updateCountRef", "lastUpdateRef", "optimizedSetState", "value", "timeSinceLastUpdate", "setTimeout", "console", "warn", "usePerformanceMemo", "factory", "deps", "startTimeRef", "calculationTime", "usePerformanceEffect", "effect", "cleanup", "effectTime", "useLifecyclePerformance", "updateCount", "totalLifetime", "totalUpdates", "lifetime", "usePerformanceDebug", "propsRef", "props", "propsChanged", "Object", "keys", "some", "key"], "sources": ["usePerformance.ts"], "sourcesContent": ["/**\n * Performance Hook - React integration for performance monitoring\n *\n * Hook Contract:\n * - Automatically tracks component render performance\n * - Provides performance measurement utilities\n * - Integrates with performance monitoring service\n * - Offers performance optimization helpers\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\nimport { useEffect, useRef, useCallback, useMemo } from 'react';\nimport { performanceMonitor } from '../services/performanceMonitor';\n\ninterface UsePerformanceOptions {\n  componentName?: string;\n  trackRenders?: boolean;\n  trackInteractions?: boolean;\n  enableProfiling?: boolean;\n}\n\ninterface PerformanceHookResult {\n  trackInteraction: (name: string, fn: () => void | Promise<void>) => Promise<void>;\n  trackAsyncOperation: (name: string, operation: () => Promise<any>) => Promise<any>;\n  measureRender: () => void;\n  getComponentStats: () => {\n    renderCount: number;\n    averageRenderTime: number;\n    lastRenderTime: number;\n  };\n}\n\n/**\n * Main performance monitoring hook\n */\nexport const usePerformance = (\n  options: UsePerformanceOptions = {}\n): PerformanceHookResult => {\n  const {\n    componentName = 'UnknownComponent',\n    trackRenders = true,\n    trackInteractions = true,\n    enableProfiling = __DEV__,\n  } = options;\n\n  const renderStartRef = useRef<number>(0);\n  const renderCountRef = useRef<number>(0);\n  const renderTimesRef = useRef<number[]>([]);\n  const mountTimeRef = useRef<number>(Date.now());\n\n  // Track component mount\n  useEffect(() => {\n    if (enableProfiling) {\n      const mountTime = Date.now() - mountTimeRef.current;\n      performanceMonitor.trackRender(componentName, mountTime, {\n        type: 'mount',\n        renderCount: 1,\n      });\n    }\n  }, [componentName, enableProfiling]);\n\n  // Track renders\n  useEffect(() => {\n    if (trackRenders && enableProfiling) {\n      const renderTime = renderStartRef.current > 0 \n        ? Date.now() - renderStartRef.current \n        : 0;\n      \n      if (renderTime > 0) {\n        renderCountRef.current++;\n        renderTimesRef.current.push(renderTime);\n        \n        // Keep only last 10 render times\n        if (renderTimesRef.current.length > 10) {\n          renderTimesRef.current = renderTimesRef.current.slice(-10);\n        }\n\n        performanceMonitor.trackRender(componentName, renderTime, {\n          renderCount: renderCountRef.current,\n          type: 'update',\n        });\n      }\n    }\n  });\n\n  // Mark render start\n  const measureRender = useCallback(() => {\n    if (enableProfiling) {\n      renderStartRef.current = Date.now();\n    }\n  }, [enableProfiling]);\n\n  // Track user interactions\n  const trackInteraction = useCallback(async (\n    name: string, \n    fn: () => void | Promise<void>\n  ): Promise<void> => {\n    if (!trackInteractions || !enableProfiling) {\n      await fn();\n      return;\n    }\n\n    const startTime = Date.now();\n    try {\n      await fn();\n    } finally {\n      const duration = Date.now() - startTime;\n      performanceMonitor.trackUserInteraction(name, duration, {\n        componentName,\n      });\n    }\n  }, [trackInteractions, enableProfiling, componentName]);\n\n  // Track async operations\n  const trackAsyncOperation = useCallback(async <T>(\n    name: string,\n    operation: () => Promise<T>\n  ): Promise<T> => {\n    if (!enableProfiling) {\n      return await operation();\n    }\n\n    const startTime = Date.now();\n    try {\n      const result = await operation();\n      const duration = Date.now() - startTime;\n      \n      performanceMonitor.trackUserInteraction(`async_${name}`, duration, {\n        componentName,\n        type: 'async_operation',\n      });\n      \n      return result;\n    } catch (error) {\n      const duration = Date.now() - startTime;\n      performanceMonitor.trackUserInteraction(`async_${name}_error`, duration, {\n        componentName,\n        type: 'async_operation_error',\n        error: error instanceof Error ? error.message : 'Unknown error',\n      });\n      throw error;\n    }\n  }, [enableProfiling, componentName]);\n\n  // Get component performance stats\n  const getComponentStats = useCallback(() => {\n    const averageRenderTime = renderTimesRef.current.length > 0\n      ? renderTimesRef.current.reduce((sum, time) => sum + time, 0) / renderTimesRef.current.length\n      : 0;\n\n    return {\n      renderCount: renderCountRef.current,\n      averageRenderTime,\n      lastRenderTime: renderTimesRef.current[renderTimesRef.current.length - 1] || 0,\n    };\n  }, []);\n\n  // Auto-measure renders\n  useMemo(() => {\n    measureRender();\n  }, [measureRender]);\n\n  return {\n    trackInteraction,\n    trackAsyncOperation,\n    measureRender,\n    getComponentStats,\n  };\n};\n\n/**\n * Hook for tracking network request performance\n */\nexport const useNetworkPerformance = () => {\n  const trackRequest = useCallback((\n    url: string,\n    method: string,\n    startTime: number,\n    endTime: number,\n    statusCode: number,\n    cached: boolean = false\n  ) => {\n    const responseTime = endTime - startTime;\n    performanceMonitor.trackNetworkRequest(\n      url,\n      method,\n      responseTime,\n      statusCode,\n      0, // requestSize - would need to be calculated\n      0, // responseSize - would need to be calculated\n      cached\n    );\n  }, []);\n\n  return { trackRequest };\n};\n\n/**\n * Hook for performance-optimized state updates\n */\nexport const useOptimizedState = <T>(\n  initialValue: T,\n  componentName?: string\n): [T, (value: T | ((prev: T) => T)) => void] => {\n  const [state, setState] = React.useState<T>(initialValue);\n  const updateCountRef = useRef<number>(0);\n  const lastUpdateRef = useRef<number>(Date.now());\n\n  const optimizedSetState = useCallback((value: T | ((prev: T) => T)) => {\n    const now = Date.now();\n    const timeSinceLastUpdate = now - lastUpdateRef.current;\n    \n    // Throttle rapid updates\n    if (timeSinceLastUpdate < 16) { // 60fps threshold\n      setTimeout(() => {\n        setState(value);\n        updateCountRef.current++;\n        lastUpdateRef.current = Date.now();\n      }, 16 - timeSinceLastUpdate);\n    } else {\n      setState(value);\n      updateCountRef.current++;\n      lastUpdateRef.current = now;\n    }\n\n    // Track excessive state updates\n    if (updateCountRef.current > 100 && componentName) {\n      console.warn(`⚠️ Excessive state updates in ${componentName}: ${updateCountRef.current}`);\n    }\n  }, [componentName]);\n\n  return [state, optimizedSetState];\n};\n\n/**\n * Hook for memoizing expensive calculations with performance tracking\n */\nexport const usePerformanceMemo = <T>(\n  factory: () => T,\n  deps: React.DependencyList,\n  name?: string\n): T => {\n  const startTimeRef = useRef<number>(0);\n  \n  return useMemo(() => {\n    startTimeRef.current = Date.now();\n    const result = factory();\n    const calculationTime = Date.now() - startTimeRef.current;\n    \n    if (calculationTime > 10 && name) { // 10ms threshold\n      console.warn(`🐌 Expensive calculation in ${name}: ${calculationTime}ms`);\n      \n      if (__DEV__) {\n        performanceMonitor.trackUserInteraction(`memo_${name}`, calculationTime, {\n          type: 'expensive_calculation',\n        });\n      }\n    }\n    \n    return result;\n  }, deps);\n};\n\n/**\n * Hook for performance-aware effects\n */\nexport const usePerformanceEffect = (\n  effect: React.EffectCallback,\n  deps: React.DependencyList,\n  name?: string\n): void => {\n  useEffect(() => {\n    const startTime = Date.now();\n    const cleanup = effect();\n    const effectTime = Date.now() - startTime;\n    \n    if (effectTime > 16 && name) { // 16ms threshold\n      console.warn(`🐌 Slow effect in ${name}: ${effectTime}ms`);\n    }\n    \n    return cleanup;\n  }, deps);\n};\n\n/**\n * Hook for tracking component lifecycle performance\n */\nexport const useLifecyclePerformance = (componentName: string) => {\n  const mountTimeRef = useRef<number>(Date.now());\n  const updateCountRef = useRef<number>(0);\n\n  // Track mount time\n  useEffect(() => {\n    const mountTime = Date.now() - mountTimeRef.current;\n    performanceMonitor.trackRender(componentName, mountTime, {\n      type: 'mount',\n    });\n  }, [componentName]);\n\n  // Track updates\n  useEffect(() => {\n    updateCountRef.current++;\n    \n    if (updateCountRef.current > 1) { // Skip initial mount\n      performanceMonitor.trackRender(componentName, 0, {\n        type: 'update',\n        updateCount: updateCountRef.current - 1,\n      });\n    }\n  });\n\n  // Track unmount\n  useEffect(() => {\n    return () => {\n      const totalLifetime = Date.now() - mountTimeRef.current;\n      performanceMonitor.trackRender(componentName, totalLifetime, {\n        type: 'unmount',\n        totalUpdates: updateCountRef.current - 1,\n        lifetime: totalLifetime,\n      });\n    };\n  }, [componentName]);\n};\n\n/**\n * Hook for performance debugging\n */\nexport const usePerformanceDebug = (componentName: string) => {\n  const renderCountRef = useRef<number>(0);\n  const propsRef = useRef<any>({});\n  \n  return useCallback((props: any) => {\n    if (!__DEV__) return;\n    \n    renderCountRef.current++;\n    \n    // Check for unnecessary re-renders\n    const propsChanged = Object.keys(props).some(\n      key => props[key] !== propsRef.current[key]\n    );\n    \n    if (!propsChanged && renderCountRef.current > 1) {\n      console.warn(`🔄 Unnecessary re-render in ${componentName} (render #${renderCountRef.current})`);\n    }\n    \n    propsRef.current = props;\n  }, [componentName]);\n};\n"], "mappings": ";;;;;;;AAaA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,mBAAA,GAAAD,OAAA;AAuBO,IAAME,cAAc,GAAAC,OAAA,CAAAD,cAAA,GAAG,SAAjBA,cAAcA,CAAA,EAEC;EAAA,IAD1BE,OAA8B,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAEnC,IAAAG,qBAAA,GAKIJ,OAAO,CAJTK,aAAa;IAAbA,aAAa,GAAAD,qBAAA,cAAG,kBAAkB,GAAAA,qBAAA;IAAAE,qBAAA,GAIhCN,OAAO,CAHTO,YAAY;IAAZA,YAAY,GAAAD,qBAAA,cAAG,IAAI,GAAAA,qBAAA;IAAAE,qBAAA,GAGjBR,OAAO,CAFTS,iBAAiB;IAAjBA,iBAAiB,GAAAD,qBAAA,cAAG,IAAI,GAAAA,qBAAA;IAAAE,qBAAA,GAEtBV,OAAO,CADTW,eAAe;IAAfA,eAAe,GAAAD,qBAAA,cAAGE,OAAO,GAAAF,qBAAA;EAG3B,IAAMG,cAAc,GAAG,IAAAC,aAAM,EAAS,CAAC,CAAC;EACxC,IAAMC,cAAc,GAAG,IAAAD,aAAM,EAAS,CAAC,CAAC;EACxC,IAAME,cAAc,GAAG,IAAAF,aAAM,EAAW,EAAE,CAAC;EAC3C,IAAMG,YAAY,GAAG,IAAAH,aAAM,EAASI,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;EAG/C,IAAAC,gBAAS,EAAC,YAAM;IACd,IAAIT,eAAe,EAAE;MACnB,IAAMU,SAAS,GAAGH,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,YAAY,CAACK,OAAO;MACnDC,sCAAkB,CAACC,WAAW,CAACnB,aAAa,EAAEgB,SAAS,EAAE;QACvDI,IAAI,EAAE,OAAO;QACbC,WAAW,EAAE;MACf,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACrB,aAAa,EAAEM,eAAe,CAAC,CAAC;EAGpC,IAAAS,gBAAS,EAAC,YAAM;IACd,IAAIb,YAAY,IAAII,eAAe,EAAE;MACnC,IAAMgB,UAAU,GAAGd,cAAc,CAACS,OAAO,GAAG,CAAC,GACzCJ,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGN,cAAc,CAACS,OAAO,GACnC,CAAC;MAEL,IAAIK,UAAU,GAAG,CAAC,EAAE;QAClBZ,cAAc,CAACO,OAAO,EAAE;QACxBN,cAAc,CAACM,OAAO,CAACM,IAAI,CAACD,UAAU,CAAC;QAGvC,IAAIX,cAAc,CAACM,OAAO,CAACpB,MAAM,GAAG,EAAE,EAAE;UACtCc,cAAc,CAACM,OAAO,GAAGN,cAAc,CAACM,OAAO,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC;QAC5D;QAEAN,sCAAkB,CAACC,WAAW,CAACnB,aAAa,EAAEsB,UAAU,EAAE;UACxDD,WAAW,EAAEX,cAAc,CAACO,OAAO;UACnCG,IAAI,EAAE;QACR,CAAC,CAAC;MACJ;IACF;EACF,CAAC,CAAC;EAGF,IAAMK,aAAa,GAAG,IAAAC,kBAAW,EAAC,YAAM;IACtC,IAAIpB,eAAe,EAAE;MACnBE,cAAc,CAACS,OAAO,GAAGJ,IAAI,CAACC,GAAG,CAAC,CAAC;IACrC;EACF,CAAC,EAAE,CAACR,eAAe,CAAC,CAAC;EAGrB,IAAMqB,gBAAgB,GAAG,IAAAD,kBAAW;IAAA,IAAAE,IAAA,OAAAC,kBAAA,CAAAC,OAAA,EAAC,WACnCC,IAAY,EACZC,EAA8B,EACZ;MAClB,IAAI,CAAC5B,iBAAiB,IAAI,CAACE,eAAe,EAAE;QAC1C,MAAM0B,EAAE,CAAC,CAAC;QACV;MACF;MAEA,IAAMC,SAAS,GAAGpB,IAAI,CAACC,GAAG,CAAC,CAAC;MAC5B,IAAI;QACF,MAAMkB,EAAE,CAAC,CAAC;MACZ,CAAC,SAAS;QACR,IAAME,QAAQ,GAAGrB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGmB,SAAS;QACvCf,sCAAkB,CAACiB,oBAAoB,CAACJ,IAAI,EAAEG,QAAQ,EAAE;UACtDlC,aAAa,EAAbA;QACF,CAAC,CAAC;MACJ;IACF,CAAC;IAAA,iBAAAoC,EAAA,EAAAC,GAAA;MAAA,OAAAT,IAAA,CAAAU,KAAA,OAAA1C,SAAA;IAAA;EAAA,KAAE,CAACQ,iBAAiB,EAAEE,eAAe,EAAEN,aAAa,CAAC,CAAC;EAGvD,IAAMuC,mBAAmB,GAAG,IAAAb,kBAAW;IAAA,IAAAc,KAAA,OAAAX,kBAAA,CAAAC,OAAA,EAAC,WACtCC,IAAY,EACZU,SAA2B,EACZ;MACf,IAAI,CAACnC,eAAe,EAAE;QACpB,aAAamC,SAAS,CAAC,CAAC;MAC1B;MAEA,IAAMR,SAAS,GAAGpB,IAAI,CAACC,GAAG,CAAC,CAAC;MAC5B,IAAI;QACF,IAAM4B,MAAM,SAASD,SAAS,CAAC,CAAC;QAChC,IAAMP,QAAQ,GAAGrB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGmB,SAAS;QAEvCf,sCAAkB,CAACiB,oBAAoB,CAAC,SAASJ,IAAI,EAAE,EAAEG,QAAQ,EAAE;UACjElC,aAAa,EAAbA,aAAa;UACboB,IAAI,EAAE;QACR,CAAC,CAAC;QAEF,OAAOsB,MAAM;MACf,CAAC,CAAC,OAAOC,KAAK,EAAE;QACd,IAAMT,SAAQ,GAAGrB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGmB,SAAS;QACvCf,sCAAkB,CAACiB,oBAAoB,CAAC,SAASJ,IAAI,QAAQ,EAAEG,SAAQ,EAAE;UACvElC,aAAa,EAAbA,aAAa;UACboB,IAAI,EAAE,uBAAuB;UAC7BuB,KAAK,EAAEA,KAAK,YAAYC,KAAK,GAAGD,KAAK,CAACE,OAAO,GAAG;QAClD,CAAC,CAAC;QACF,MAAMF,KAAK;MACb;IACF,CAAC;IAAA,iBAAAG,GAAA,EAAAC,GAAA;MAAA,OAAAP,KAAA,CAAAF,KAAA,OAAA1C,SAAA;IAAA;EAAA,KAAE,CAACU,eAAe,EAAEN,aAAa,CAAC,CAAC;EAGpC,IAAMgD,iBAAiB,GAAG,IAAAtB,kBAAW,EAAC,YAAM;IAC1C,IAAMuB,iBAAiB,GAAGtC,cAAc,CAACM,OAAO,CAACpB,MAAM,GAAG,CAAC,GACvDc,cAAc,CAACM,OAAO,CAACiC,MAAM,CAAC,UAACC,GAAG,EAAEC,IAAI;MAAA,OAAKD,GAAG,GAAGC,IAAI;IAAA,GAAE,CAAC,CAAC,GAAGzC,cAAc,CAACM,OAAO,CAACpB,MAAM,GAC3F,CAAC;IAEL,OAAO;MACLwB,WAAW,EAAEX,cAAc,CAACO,OAAO;MACnCgC,iBAAiB,EAAjBA,iBAAiB;MACjBI,cAAc,EAAE1C,cAAc,CAACM,OAAO,CAACN,cAAc,CAACM,OAAO,CAACpB,MAAM,GAAG,CAAC,CAAC,IAAI;IAC/E,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAGN,IAAAyD,cAAO,EAAC,YAAM;IACZ7B,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EAEnB,OAAO;IACLE,gBAAgB,EAAhBA,gBAAgB;IAChBY,mBAAmB,EAAnBA,mBAAmB;IACnBd,aAAa,EAAbA,aAAa;IACbuB,iBAAiB,EAAjBA;EACF,CAAC;AACH,CAAC;AAKM,IAAMO,qBAAqB,GAAA7D,OAAA,CAAA6D,qBAAA,GAAG,SAAxBA,qBAAqBA,CAAA,EAAS;EACzC,IAAMC,YAAY,GAAG,IAAA9B,kBAAW,EAAC,UAC/B+B,GAAW,EACXC,MAAc,EACdzB,SAAiB,EACjB0B,OAAe,EACfC,UAAkB,EAEf;IAAA,IADHC,MAAe,GAAAjE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;IAEvB,IAAMkE,YAAY,GAAGH,OAAO,GAAG1B,SAAS;IACxCf,sCAAkB,CAAC6C,mBAAmB,CACpCN,GAAG,EACHC,MAAM,EACNI,YAAY,EACZF,UAAU,EACV,CAAC,EACD,CAAC,EACDC,MACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IAAEL,YAAY,EAAZA;EAAa,CAAC;AACzB,CAAC;AAKM,IAAMQ,iBAAiB,GAAAtE,OAAA,CAAAsE,iBAAA,GAAG,SAApBA,iBAAiBA,CAC5BC,YAAe,EACfjE,aAAsB,EACyB;EAC/C,IAAAkE,eAAA,GAA0BC,KAAK,CAACC,QAAQ,CAAIH,YAAY,CAAC;IAAAI,gBAAA,OAAAC,eAAA,CAAAxC,OAAA,EAAAoC,eAAA;IAAlDK,KAAK,GAAAF,gBAAA;IAAEG,QAAQ,GAAAH,gBAAA;EACtB,IAAMI,cAAc,GAAG,IAAAhE,aAAM,EAAS,CAAC,CAAC;EACxC,IAAMiE,aAAa,GAAG,IAAAjE,aAAM,EAASI,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;EAEhD,IAAM6D,iBAAiB,GAAG,IAAAjD,kBAAW,EAAC,UAACkD,KAA2B,EAAK;IACrE,IAAM9D,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;IACtB,IAAM+D,mBAAmB,GAAG/D,GAAG,GAAG4D,aAAa,CAACzD,OAAO;IAGvD,IAAI4D,mBAAmB,GAAG,EAAE,EAAE;MAC5BC,UAAU,CAAC,YAAM;QACfN,QAAQ,CAACI,KAAK,CAAC;QACfH,cAAc,CAACxD,OAAO,EAAE;QACxByD,aAAa,CAACzD,OAAO,GAAGJ,IAAI,CAACC,GAAG,CAAC,CAAC;MACpC,CAAC,EAAE,EAAE,GAAG+D,mBAAmB,CAAC;IAC9B,CAAC,MAAM;MACLL,QAAQ,CAACI,KAAK,CAAC;MACfH,cAAc,CAACxD,OAAO,EAAE;MACxByD,aAAa,CAACzD,OAAO,GAAGH,GAAG;IAC7B;IAGA,IAAI2D,cAAc,CAACxD,OAAO,GAAG,GAAG,IAAIjB,aAAa,EAAE;MACjD+E,OAAO,CAACC,IAAI,CAAC,iCAAiChF,aAAa,KAAKyE,cAAc,CAACxD,OAAO,EAAE,CAAC;IAC3F;EACF,CAAC,EAAE,CAACjB,aAAa,CAAC,CAAC;EAEnB,OAAO,CAACuE,KAAK,EAAEI,iBAAiB,CAAC;AACnC,CAAC;AAKM,IAAMM,kBAAkB,GAAAvF,OAAA,CAAAuF,kBAAA,GAAG,SAArBA,kBAAkBA,CAC7BC,OAAgB,EAChBC,IAA0B,EAC1BpD,IAAa,EACP;EACN,IAAMqD,YAAY,GAAG,IAAA3E,aAAM,EAAS,CAAC,CAAC;EAEtC,OAAO,IAAA6C,cAAO,EAAC,YAAM;IACnB8B,YAAY,CAACnE,OAAO,GAAGJ,IAAI,CAACC,GAAG,CAAC,CAAC;IACjC,IAAM4B,MAAM,GAAGwC,OAAO,CAAC,CAAC;IACxB,IAAMG,eAAe,GAAGxE,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGsE,YAAY,CAACnE,OAAO;IAEzD,IAAIoE,eAAe,GAAG,EAAE,IAAItD,IAAI,EAAE;MAChCgD,OAAO,CAACC,IAAI,CAAC,+BAA+BjD,IAAI,KAAKsD,eAAe,IAAI,CAAC;MAEzE,IAAI9E,OAAO,EAAE;QACXW,sCAAkB,CAACiB,oBAAoB,CAAC,QAAQJ,IAAI,EAAE,EAAEsD,eAAe,EAAE;UACvEjE,IAAI,EAAE;QACR,CAAC,CAAC;MACJ;IACF;IAEA,OAAOsB,MAAM;EACf,CAAC,EAAEyC,IAAI,CAAC;AACV,CAAC;AAKM,IAAMG,oBAAoB,GAAA5F,OAAA,CAAA4F,oBAAA,GAAG,SAAvBA,oBAAoBA,CAC/BC,MAA4B,EAC5BJ,IAA0B,EAC1BpD,IAAa,EACJ;EACT,IAAAhB,gBAAS,EAAC,YAAM;IACd,IAAMkB,SAAS,GAAGpB,IAAI,CAACC,GAAG,CAAC,CAAC;IAC5B,IAAM0E,OAAO,GAAGD,MAAM,CAAC,CAAC;IACxB,IAAME,UAAU,GAAG5E,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGmB,SAAS;IAEzC,IAAIwD,UAAU,GAAG,EAAE,IAAI1D,IAAI,EAAE;MAC3BgD,OAAO,CAACC,IAAI,CAAC,qBAAqBjD,IAAI,KAAK0D,UAAU,IAAI,CAAC;IAC5D;IAEA,OAAOD,OAAO;EAChB,CAAC,EAAEL,IAAI,CAAC;AACV,CAAC;AAKM,IAAMO,uBAAuB,GAAAhG,OAAA,CAAAgG,uBAAA,GAAG,SAA1BA,uBAAuBA,CAAI1F,aAAqB,EAAK;EAChE,IAAMY,YAAY,GAAG,IAAAH,aAAM,EAASI,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;EAC/C,IAAM2D,cAAc,GAAG,IAAAhE,aAAM,EAAS,CAAC,CAAC;EAGxC,IAAAM,gBAAS,EAAC,YAAM;IACd,IAAMC,SAAS,GAAGH,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,YAAY,CAACK,OAAO;IACnDC,sCAAkB,CAACC,WAAW,CAACnB,aAAa,EAAEgB,SAAS,EAAE;MACvDI,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC,EAAE,CAACpB,aAAa,CAAC,CAAC;EAGnB,IAAAe,gBAAS,EAAC,YAAM;IACd0D,cAAc,CAACxD,OAAO,EAAE;IAExB,IAAIwD,cAAc,CAACxD,OAAO,GAAG,CAAC,EAAE;MAC9BC,sCAAkB,CAACC,WAAW,CAACnB,aAAa,EAAE,CAAC,EAAE;QAC/CoB,IAAI,EAAE,QAAQ;QACduE,WAAW,EAAElB,cAAc,CAACxD,OAAO,GAAG;MACxC,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EAGF,IAAAF,gBAAS,EAAC,YAAM;IACd,OAAO,YAAM;MACX,IAAM6E,aAAa,GAAG/E,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,YAAY,CAACK,OAAO;MACvDC,sCAAkB,CAACC,WAAW,CAACnB,aAAa,EAAE4F,aAAa,EAAE;QAC3DxE,IAAI,EAAE,SAAS;QACfyE,YAAY,EAAEpB,cAAc,CAACxD,OAAO,GAAG,CAAC;QACxC6E,QAAQ,EAAEF;MACZ,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,EAAE,CAAC5F,aAAa,CAAC,CAAC;AACrB,CAAC;AAKM,IAAM+F,mBAAmB,GAAArG,OAAA,CAAAqG,mBAAA,GAAG,SAAtBA,mBAAmBA,CAAI/F,aAAqB,EAAK;EAC5D,IAAMU,cAAc,GAAG,IAAAD,aAAM,EAAS,CAAC,CAAC;EACxC,IAAMuF,QAAQ,GAAG,IAAAvF,aAAM,EAAM,CAAC,CAAC,CAAC;EAEhC,OAAO,IAAAiB,kBAAW,EAAC,UAACuE,KAAU,EAAK;IACjC,IAAI,CAAC1F,OAAO,EAAE;IAEdG,cAAc,CAACO,OAAO,EAAE;IAGxB,IAAMiF,YAAY,GAAGC,MAAM,CAACC,IAAI,CAACH,KAAK,CAAC,CAACI,IAAI,CAC1C,UAAAC,GAAG;MAAA,OAAIL,KAAK,CAACK,GAAG,CAAC,KAAKN,QAAQ,CAAC/E,OAAO,CAACqF,GAAG,CAAC;IAAA,CAC7C,CAAC;IAED,IAAI,CAACJ,YAAY,IAAIxF,cAAc,CAACO,OAAO,GAAG,CAAC,EAAE;MAC/C8D,OAAO,CAACC,IAAI,CAAC,+BAA+BhF,aAAa,aAAaU,cAAc,CAACO,OAAO,GAAG,CAAC;IAClG;IAEA+E,QAAQ,CAAC/E,OAAO,GAAGgF,KAAK;EAC1B,CAAC,EAAE,CAACjG,aAAa,CAAC,CAAC;AACrB,CAAC", "ignoreList": []}