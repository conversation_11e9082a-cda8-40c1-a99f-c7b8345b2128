/**
 * Enhanced Text Input Component
 *
 * Advanced text input with comprehensive UX improvements, validation,
 * and accessibility features following modern form design principles.
 *
 * Features:
 * - Real-time validation
 * - Progressive enhancement
 * - Accessibility compliance
 * - Auto-formatting
 * - Smart suggestions
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Platform } from 'react-native';
import { TextInput, StyleSheet, Animated,  } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
// Temporarily commented out to fix provider issues
// import { useHighContrastColors } from '../../contexts/HighContrastContext';
// import { useTouchTargetStyles, useHapticFeedback } from '../../contexts/MotorAccessibilityContext';
// import { useCognitiveAccessibility } from '../../contexts/CognitiveAccessibilityContext';
import {
  ValidationResult,
  FieldValidation,
  validateField,
  getFieldAccessibilityProps,
  formatInputValue,
  generateValidationSuggestions,
} from '../../utils/formValidationUtils';

// Component props
export interface EnhancedTextInputProps {
  // Basic props
  label: string;
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  
  // Input configuration
  inputType?: 'text' | 'email' | 'phone' | 'password' | 'number' | 'postalCode';
  multiline?: boolean;
  numberOfLines?: number;
  maxLength?: number;
  
  // Validation
  validation?: FieldValidation;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  showValidationIcons?: boolean;
  
  // Behavior
  autoFocus?: boolean;
  autoCorrect?: boolean;
  autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
  secureTextEntry?: boolean;
  editable?: boolean;
  
  // Styling
  style?: any;
  inputStyle?: any;
  labelStyle?: any;
  errorStyle?: any;
  
  // Accessibility
  accessibilityLabel?: string;
  accessibilityHint?: string;
  
  // Events
  onFocus?: () => void;
  onBlur?: () => void;
  onSubmitEditing?: () => void;
  
  // Testing
  testID?: string;
}

export const EnhancedTextInput: React.FC<EnhancedTextInputProps> = ({
  label,
  value,
  onChangeText,
  placeholder,
  inputType = 'text',
  multiline = false,
  numberOfLines = 1,
  maxLength,
  validation,
  validateOnChange = true,
  validateOnBlur = true,
  showValidationIcons = true,
  autoFocus = false,
  autoCorrect = true,
  autoCapitalize = 'sentences',
  secureTextEntry = false,
  editable = true,
  style,
  inputStyle,
  labelStyle,
  errorStyle,
  accessibilityLabel,
  accessibilityHint,
  onFocus,
  onBlur,
  onSubmitEditing,
  testID,
}) => {
  // State
  const [isFocused, setIsFocused] = useState(false);
  const [validationResult, setValidationResult] = useState<ValidationResult>({
    isValid: true,
    errors: [],
    warnings: [],
    suggestions: [],
  });
  const [showPassword, setShowPassword] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);

  // Refs
  const inputRef = useRef<TextInput>(null);
  const labelAnimation = useRef(new Animated.Value(value ? 1 : 0)).current;
  const borderAnimation = useRef(new Animated.Value(0)).current;

  // Hooks - temporarily commented out
  // const { colors } = useHighContrastColors();
  // const touchTargetStyles = useTouchTargetStyles();
  // const triggerHapticFeedback = useHapticFeedback();
  // const { settings } = useCognitiveAccessibility();

  // Temporary fallback values
  const colors = { background: '#FFFFFF', text: { primary: '#000000', secondary: '#666666' } };
  const touchTargetStyles = {};
  const triggerHapticFeedback = () => {};
  const settings = { simplifiedText: false };

  // Auto-focus effect
  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  // Label animation
  useEffect(() => {
    Animated.timing(labelAnimation, {
      toValue: isFocused || value ? 1 : 0,
      duration: 200,
      useNativeDriver: false,
    }).start();
  }, [isFocused, value, labelAnimation]);

  // Border animation
  useEffect(() => {
    Animated.timing(borderAnimation, {
      toValue: isFocused ? 1 : 0,
      duration: 200,
      useNativeDriver: false,
    }).start();
  }, [isFocused, borderAnimation]);

  // Validation effect
  useEffect(() => {
    if (validation && validateOnChange && value) {
      const result = validateField(value, label, validation);
      setValidationResult(result);
      
      // Generate smart suggestions
      const smartSuggestions = generateValidationSuggestions(value, label, validation);
      setSuggestions(smartSuggestions);
    }
  }, [value, validation, validateOnChange, label]);

  // Handle focus
  const handleFocus = useCallback(() => {
    setIsFocused(true);
    triggerHapticFeedback('light');
    
    if (onFocus) {
      onFocus();
    }
  }, [onFocus, triggerHapticFeedback]);

  // Handle blur
  const handleBlur = useCallback(() => {
    setIsFocused(false);
    
    if (validation && validateOnBlur) {
      const result = validateField(value, label, validation);
      setValidationResult(result);
    }
    
    if (onBlur) {
      onBlur();
    }
  }, [onBlur, validation, validateOnBlur, value, label]);

  // Handle text change
  const handleChangeText = useCallback((text: string) => {
    // Auto-format input
    const formattedText = formatInputValue(text, inputType);
    
    onChangeText(formattedText);
    
    // Real-time validation
    if (validation && validateOnChange) {
      const result = validateField(formattedText, label, validation);
      setValidationResult(result);
    }
  }, [onChangeText, inputType, validation, validateOnChange, label]);

  // Toggle password visibility
  const togglePasswordVisibility = useCallback(() => {
    setShowPassword(!showPassword);
    triggerHapticFeedback('light');
  }, [showPassword, triggerHapticFeedback]);

  // Get keyboard type
  const getKeyboardType = () => {
    switch (inputType) {
      case 'email':
        return 'email-address';
      case 'phone':
        return 'phone-pad';
      case 'number':
        return 'numeric';
      default:
        return 'default';
    }
  };

  // Get auto-capitalize setting
  const getAutoCapitalize = () => {
    if (inputType === 'email') return 'none';
    return autoCapitalize;
  };

  // Get styles
  const containerStyles = [
    styles.container,
    style,
    !editable && styles.disabled,
  ];

  const inputContainerStyles = [
    styles.inputContainer,
    isFocused && styles.focused,
    !validationResult.isValid && styles.error,
    validationResult.warnings.length > 0 && styles.warning,
  ];

  const textInputStyles = [
    styles.textInput,
    inputStyle,
    multiline && styles.multilineInput,
    { color: colors?.text?.primary || '#333' },
  ];

  const labelStyles = [
    styles.label,
    labelStyle,
    { color: colors?.text?.secondary || '#666' },
    isFocused && { color: colors?.primary?.default || '#5A7A63' },
  ];

  // Get accessibility props
  const accessibilityProps = validation 
    ? getFieldAccessibilityProps(label, validationResult, validation.required)
    : {};

  return (
    <View style={containerStyles}>
      {/* Floating Label */}
      <Animated.Text
        style={[
          labelStyles,
          {
            transform: [
              {
                translateY: labelAnimation.interpolate({
                  inputRange: [0, 1],
                  outputRange: [20, 0],
                }),
              },
              {
                scale: labelAnimation.interpolate({
                  inputRange: [0, 1],
                  outputRange: [1, 0.85],
                }),
              },
            ],
          },
        ]}
      >
        {label}
        {validation?.required && <Text style={styles.required}> *</Text>}
      </Animated.Text>

      {/* Input Container */}
      <View style={inputContainerStyles}>
        <TextInput
          ref={inputRef}
          style={textInputStyles}
          value={value}
          onChangeText={handleChangeText}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onSubmitEditing={onSubmitEditing}
          placeholder={isFocused ? placeholder : ''}
          placeholderTextColor={colors?.text?.tertiary || '#999'}
          keyboardType={getKeyboardType()}
          autoCapitalize={getAutoCapitalize()}
          autoCorrect={autoCorrect}
          secureTextEntry={inputType === 'password' && !showPassword}
          multiline={multiline}
          numberOfLines={numberOfLines}
          maxLength={maxLength}
          editable={editable}
          testID={testID}
          {...accessibilityProps}
          accessibilityLabel={accessibilityLabel || label}
          accessibilityHint={accessibilityHint}
        />

        {/* Validation Icon */}
        {showValidationIcons && value && (
          <View style={styles.iconContainer}>
            {!validationResult.isValid ? (
              <Ionicons name="alert-circle" size={20} color="#FF6B6B" />
            ) : validationResult.warnings.length > 0 ? (
              <Ionicons name="warning" size={20} color="#FFB347" />
            ) : (
              <Ionicons name="checkmark-circle" size={20} color="#4ECDC4" />
            )}
          </View>
        )}

        {/* Password Toggle */}
        {inputType === 'password' && (
          <TouchableOpacity
            style={[styles.iconContainer, touchTargetStyles]}
            onPress={togglePasswordVisibility}
            accessibilityRole="button"
            accessibilityLabel={showPassword ? 'Hide password' : 'Show password'}
          >
            <Ionicons
              name={showPassword ? 'eye-off' : 'eye'}
              size={20}
              color={colors?.text?.secondary || '#666'}
            />
          </TouchableOpacity>
        )}
      </View>

      {/* Character Count */}
      {maxLength && (
        <Text style={styles.characterCount}>
          {value.length}/{maxLength}
        </Text>
      )}

      {/* Error Messages */}
      {!validationResult.isValid && (
        <View style={styles.messageContainer}>
          {validationResult.errors.map((error, index) => (
            <Text key={index} style={[styles.errorText, errorStyle]}>
              {error}
            </Text>
          ))}
        </View>
      )}

      {/* Warning Messages */}
      {validationResult.warnings.length > 0 && (
        <View style={styles.messageContainer}>
          {validationResult.warnings.map((warning, index) => (
            <Text key={index} style={styles.warningText}>
              {warning}
            </Text>
          ))}
        </View>
      )}

      {/* Suggestions */}
      {settings.memoryAids && suggestions.length > 0 && (
        <View style={styles.suggestionsContainer}>
          {suggestions.map((suggestion, index) => (
            <Text key={index} style={styles.suggestionText}>
              💡 {suggestion}
            </Text>
          ))}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#DDD',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    minHeight: 48,
  },
  focused: {
    borderColor: '#5A7A63',
    borderWidth: 2,
    shadowColor: '#5A7A63',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 2,
  },
  error: {
    borderColor: '#FF6B6B',
  },
  warning: {
    borderColor: '#FFB347',
  },
  disabled: {
    opacity: 0.6,
  },
  label: {
    fontSize: 16,
    marginBottom: 4,
    fontWeight: '500',
  },
  required: {
    color: '#FF6B6B',
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    paddingVertical: 0,
  },
  multilineInput: {
    textAlignVertical: 'top',
    minHeight: 80,
  },
  iconContainer: {
    marginLeft: 8,
    padding: 4,
  },
  characterCount: {
    fontSize: 12,
    color: '#999',
    textAlign: 'right',
    marginTop: 4,
  },
  messageContainer: {
    marginTop: 4,
  },
  errorText: {
    fontSize: 12,
    color: '#FF6B6B',
    marginBottom: 2,
  },
  warningText: {
    fontSize: 12,
    color: '#FFB347',
    marginBottom: 2,
  },
  suggestionsContainer: {
    marginTop: 4,
    padding: 8,
    backgroundColor: '#F8F9FA',
    borderRadius: 4,
  },
  suggestionText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
});

export default EnhancedTextInput;
