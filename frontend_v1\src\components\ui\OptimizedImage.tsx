/**
 * Optimized Image Component - Drop-in replacement for React Native Image
 * 
 * This component automatically uses LazyImage for better performance
 * while maintaining compatibility with existing Image component usage.
 * 
 * Features:
 * - Automatic lazy loading for offscreen images
 * - Fallback to regular Image for critical above-the-fold images
 * - Performance optimization with minimal code changes
 * - Maintains all Image component props and behavior
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useCallback, useRef } from 'react';
import { Image } from 'react-native';
import { ImageProps } from 'react-native';
import { LazyImage, LazyImageProps } from './LazyImage';
import {
  ImageContext,
  ImageAccessibilityProps,
  enhanceImageWithAccessibility,
  generateImageAccessibilityProps
} from '../../utils/imageAccessibilityUtils';
import { performanceMonitor } from '../../services/performanceMonitor';

interface OptimizedImageProps extends ImageProps {
  // LazyImage specific props
  lazy?: boolean;
  threshold?: number;
  fadeInDuration?: number;
  placeholder?: { uri: string } | number;
  fallback?: { uri: string } | number;
  containerStyle?: any;
  imageStyle?: any;

  // Performance hints
  priority?: 'high' | 'normal' | 'low';
  loading?: 'eager' | 'lazy';

  // Enhanced accessibility props for WCAG compliance
  accessibilityLabel?: string;
  accessibilityHint?: string;
  accessibilityRole?: 'image' | 'imagebutton' | 'none';
  alt?: string; // Alternative text for screen readers
  isDecorative?: boolean; // Mark decorative images to hide from screen readers

  // New comprehensive accessibility features
  imageContext?: ImageContext; // Context for automatic accessibility generation
  validateAccessibility?: boolean; // Enable accessibility validation in development
}

/**
 * OptimizedImage - Smart image component that chooses between Image and LazyImage
 * 
 * Automatically uses LazyImage for better performance unless:
 * - priority is set to 'high' (for hero images, critical content)
 * - loading is set to 'eager' (for above-the-fold content)
 * - lazy is explicitly set to false
 */
export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  lazy,
  threshold = 0.1,
  fadeInDuration = 300,
  placeholder,
  fallback,
  containerStyle,
  imageStyle,
  priority = 'normal',
  loading = 'lazy',
  style,
  source,
  // Enhanced accessibility props
  accessibilityLabel,
  accessibilityHint,
  accessibilityRole = 'image',
  alt,
  isDecorative = false,
  // New accessibility features
  imageContext,
  validateAccessibility = __DEV__,
  ...imageProps
}) => {
  // Performance tracking
  const loadStartTimeRef = useRef<number>(0);

  // Track image load performance
  const trackImageLoad = useCallback((uri: string, cached: boolean = false) => {
    const loadTime = Date.now() - loadStartTimeRef.current;
    performanceMonitor.trackUserInteraction('image_load', loadTime, {
      uri,
      cached,
      priority,
      lazy: shouldUseLazyLoading,
    });
  }, [priority]);

  const handleLoadStart = useCallback(() => {
    loadStartTimeRef.current = Date.now();
    imageProps.onLoadStart?.();
  }, [imageProps.onLoadStart]);

  const handleLoad = useCallback(() => {
    if (typeof source === 'object' && source?.uri) {
      trackImageLoad(source.uri, false);
    }
    imageProps.onLoad?.();
  }, [source, trackImageLoad, imageProps.onLoad]);

  // Generate enhanced accessibility props if imageContext is provided
  const enhancedAccessibilityProps = React.useMemo(() => {
    if (imageContext) {
      return generateImageAccessibilityProps(imageContext, alt || accessibilityLabel);
    }

    // Fallback to manual accessibility props
    return {
      accessibilityLabel: alt || accessibilityLabel,
      accessibilityHint,
      accessibilityRole,
      accessible: !isDecorative,
      importantForAccessibility: isDecorative ? 'no' : 'yes',
    } as ImageAccessibilityProps;
  }, [imageContext, alt, accessibilityLabel, accessibilityHint, accessibilityRole, isDecorative]);

  // Determine if we should use lazy loading
  const shouldUseLazyLoading =
    lazy !== false &&
    priority !== 'high' &&
    loading !== 'eager';

  // If lazy loading is disabled or this is a high priority image, use regular Image
  if (!shouldUseLazyLoading) {
    return (
      <Image
        source={source}
        style={[style, imageStyle]}
        // Enhanced WCAG-compliant accessibility props
        {...enhancedAccessibilityProps}
        // Performance tracking
        onLoadStart={handleLoadStart}
        onLoad={handleLoad}
        {...imageProps}
      />
    );
  }

  // Convert source to LazyImage format
  const lazySource = typeof source === 'object' && 'uri' in source 
    ? source 
    : source;

  // Use LazyImage for better performance
  return (
    <LazyImage
      source={lazySource}
      placeholder={placeholder}
      fallback={fallback}
      lazy={true}
      threshold={threshold}
      fadeInDuration={fadeInDuration}
      containerStyle={containerStyle}
      imageStyle={[style, imageStyle]}
      // Enhanced WCAG-compliant accessibility props
      {...enhancedAccessibilityProps}
      alt={alt}
      isDecorative={isDecorative}
      // Performance tracking
      onLoadStart={handleLoadStart}
      onLoad={handleLoad}
      {...imageProps}
    />
  );
};

/**
 * Performance-optimized Image export
 * 
 * This can be used as a drop-in replacement for React Native's Image component:
 * 
 * Before:
 * import { Image } from 'react-native';
 * 
 * After:
 * import { PerformantImage as Image } from '../components/ui/OptimizedImage';
 * 
 * The component will automatically optimize images for performance while
 * maintaining the same API as the original Image component.
 */
export const PerformantImage = OptimizedImage;

// Default export for convenience
export default OptimizedImage;
