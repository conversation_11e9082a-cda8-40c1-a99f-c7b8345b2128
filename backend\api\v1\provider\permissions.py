"""
Provider API Permissions - Enhanced based on Backend Agent feedback
Role-based permissions for service provider endpoints
"""

from rest_framework import permissions
from django.contrib.auth import get_user_model

User = get_user_model()


class IsProviderUser(permissions.BasePermission):
    """
    Permission class to ensure user has service provider role
    """
    message = "You must be a service provider to access this endpoint."
    
    def has_permission(self, request, view):
        """Check if user is authenticated and has provider role"""
        if not request.user or not request.user.is_authenticated:
            return False
        
        # Check if user has provider role
        return (
            hasattr(request.user, 'service_provider') or
            request.user.role == 'service_provider'
        )
    
    def has_object_permission(self, request, view, obj):
        """Check object-level permissions for provider"""
        # Provider can only access their own objects
        if hasattr(obj, 'provider'):
            return obj.provider.user == request.user
        elif hasattr(obj, 'user'):
            return obj.user == request.user
        elif hasattr(obj, 'service') and hasattr(obj.service, 'provider'):
            return obj.service.provider.user == request.user
        
        return True


class IsProviderOwner(permissions.BasePermission):
    """
    Permission class to ensure provider can only access their own data
    """
    message = "You can only access your own business data."
    
    def has_object_permission(self, request, view, obj):
        """Check if provider owns the object"""
        if hasattr(obj, 'user'):
            return obj.user == request.user
        elif hasattr(obj, 'provider'):
            return obj.provider.user == request.user
        elif hasattr(obj, 'service') and hasattr(obj.service, 'provider'):
            return obj.service.provider.user == request.user
        
        return False


class CanManageBookings(permissions.BasePermission):
    """
    Permission class for booking management with business validation
    """
    message = "You cannot manage bookings at this time."
    
    def has_permission(self, request, view):
        """Check if provider can manage bookings"""
        if not request.user or not request.user.is_authenticated:
            return False
        
        # Check if user is a provider
        if not (hasattr(request.user, 'service_provider') or request.user.user_type == 'service_provider'):
            return False
        
        # Additional checks for booking management
        if hasattr(request.user, 'service_provider'):
            provider = request.user.service_provider
            
            # Check if provider is verified
            if not provider.is_verified:
                self.message = "Your business must be verified to manage bookings."
                return False
            
            # Check if provider is active
            if not provider.is_active:
                self.message = "Your business account is inactive. Contact support."
                return False
        
        return True
    
    def has_object_permission(self, request, view, obj):
        """Check if provider can manage specific booking"""
        # Must be a booking for provider's service
        if hasattr(obj, 'service') and hasattr(obj.service, 'provider'):
            return obj.service.provider.user == request.user
        
        return False


class CanManageServices(permissions.BasePermission):
    """
    Permission class for service management
    """
    message = "You cannot manage services at this time."
    
    def has_permission(self, request, view):
        """Check if provider can manage services"""
        if not request.user or not request.user.is_authenticated:
            return False
        
        # Check if user is a provider
        if not (hasattr(request.user, 'service_provider') or request.user.role == 'service_provider'):
            return False
        
        # Check if provider can create/modify services
        if request.method in ['POST', 'PUT', 'PATCH']:
            if hasattr(request.user, 'service_provider'):
                provider = request.user.service_provider
                
                # Check service limits for unverified providers
                if not provider.is_verified:
                    service_count = provider.services.filter(is_active=True).count()
                    if service_count >= 3:  # Limit unverified providers to 3 services
                        self.message = "Unverified providers can only have 3 active services. Please verify your business."
                        return False
        
        return True
    
    def has_object_permission(self, request, view, obj):
        """Check if provider can manage specific service"""
        if hasattr(obj, 'provider'):
            return obj.provider.user == request.user
        
        return False


class CanAccessAnalytics(permissions.BasePermission):
    """
    Permission class for analytics access
    """
    message = "You cannot access analytics at this time."
    
    def has_permission(self, request, view):
        """Check if provider can access analytics"""
        if not request.user or not request.user.is_authenticated:
            return False
        
        # Check if user is a provider
        if not (hasattr(request.user, 'service_provider') or request.user.role == 'service_provider'):
            return False
        
        # Analytics might require verification
        if hasattr(request.user, 'service_provider'):
            provider = request.user.service_provider
            
            # Basic analytics for all, advanced for verified
            action = getattr(view, 'action', None)
            if action in ['insights', 'advanced_metrics']:
                if not provider.is_verified:
                    self.message = "Advanced analytics require business verification."
                    return False
        
        return True


class ProviderAPIPermission(permissions.BasePermission):
    """
    Comprehensive permission class for provider API endpoints
    Combines multiple permission checks
    """
    
    def has_permission(self, request, view):
        """Check general API access permissions"""
        # Must be authenticated
        if not request.user or not request.user.is_authenticated:
            return False
        
        # Must be a provider
        if not (hasattr(request.user, 'service_provider') or request.user.role == 'service_provider'):
            return False
        
        # Check API access permissions based on view action
        action = getattr(view, 'action', None)
        
        if action in ['create', 'update', 'partial_update', 'destroy']:
            # Check if provider can perform write operations
            if hasattr(request.user, 'service_provider'):
                provider = request.user.service_provider
                
                # Suspended providers can only read
                if getattr(provider, 'is_suspended', False):
                    return False
                
                # Unverified providers have limited write access
                if not provider.is_verified and action in ['create']:
                    # Check limits for unverified providers
                    if view.__class__.__name__ == 'ProviderServiceViewSet':
                        service_count = provider.services.filter(is_active=True).count()
                        if service_count >= 3:
                            return False
        
        return True
    
    def has_object_permission(self, request, view, obj):
        """Check object-level permissions"""
        # Provider can only access their own data
        if hasattr(obj, 'provider'):
            return obj.provider.user == request.user
        elif hasattr(obj, 'user'):
            return obj.user == request.user
        elif hasattr(obj, 'service') and hasattr(obj.service, 'provider'):
            return obj.service.provider.user == request.user
        
        return False


class IsVerifiedProvider(permissions.BasePermission):
    """
    Permission class that requires provider to be verified
    """
    message = "This feature requires business verification."
    
    def has_permission(self, request, view):
        """Check if provider is verified"""
        if not request.user or not request.user.is_authenticated:
            return False
        
        if not (hasattr(request.user, 'service_provider') or request.user.role == 'service_provider'):
            return False
        
        if hasattr(request.user, 'service_provider'):
            return request.user.service_provider.is_verified
        
        return False
