/**
 * WebSocket Service - Real-time Communication
 *
 * Service Contract:
 * - Manages WebSocket connections for real-time messaging
 * - Handles connection lifecycle and reconnection
 * - Provides event-based messaging interface
 * - Supports typing indicators and message status updates
 * - Implements proper error handling and fallback mechanisms
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { authStore } from '../stores/authStore';

export interface WebSocketMessage {
  type: 'chat_message' | 'typing_indicator' | 'messages_read' | 'notification' | 'error';
  message?: any;
  user_id?: string;
  user_name?: string;
  is_typing?: boolean;
  message_ids?: string[];
  reader_id?: string;
  notification?: any;
  error?: string;
}

export interface WebSocketConfig {
  url: string;
  protocols?: string[];
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
}

class WebSocketService {
  private socket: WebSocket | null = null;
  private config: WebSocketConfig;
  private reconnectAttempts = 0;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private isConnecting = false;
  private eventListeners: Map<string, Set<(data: any) => void>> = new Map();
  private connectionPromise: Promise<void> | null = null;

  constructor(config: WebSocketConfig) {
    this.config = {
      reconnectInterval: 3000,
      maxReconnectAttempts: 5,
      ...config,
    };
  }

  /**
   * Connect to WebSocket server
   */
  async connect(): Promise<void> {
    if (this.socket?.readyState === WebSocket.OPEN) {
      return Promise.resolve();
    }

    if (this.connectionPromise) {
      return this.connectionPromise;
    }

    this.connectionPromise = new Promise((resolve, reject) => {
      try {
        this.isConnecting = true;
        
        // Get auth token for WebSocket authentication
        const token = authStore.getState().token;
        const wsUrl = token 
          ? `${this.config.url}?token=${token}`
          : this.config.url;

        this.socket = new WebSocket(wsUrl, this.config.protocols);

        this.socket.onopen = () => {
          console.log('WebSocket connected');
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          this.connectionPromise = null;
          this.emit('connected', {});
          resolve();
        };

        this.socket.onmessage = (event) => {
          try {
            const data: WebSocketMessage = JSON.parse(event.data);
            this.handleMessage(data);
          } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
          }
        };

        this.socket.onclose = (event) => {
          console.log('WebSocket disconnected:', event.code, event.reason);
          this.isConnecting = false;
          this.connectionPromise = null;
          this.emit('disconnected', { code: event.code, reason: event.reason });
          
          if (!event.wasClean && this.shouldReconnect()) {
            this.scheduleReconnect();
          }
        };

        this.socket.onerror = (error) => {
          console.error('WebSocket error:', error);
          this.isConnecting = false;
          this.connectionPromise = null;
          this.emit('error', { error });
          reject(error);
        };

      } catch (error) {
        this.isConnecting = false;
        this.connectionPromise = null;
        reject(error);
      }
    });

    return this.connectionPromise;
  }

  /**
   * Disconnect from WebSocket server
   */
  disconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    if (this.socket) {
      this.socket.close(1000, 'Client disconnect');
      this.socket = null;
    }

    this.connectionPromise = null;
    this.reconnectAttempts = 0;
  }

  /**
   * Send message through WebSocket
   */
  send(message: any): void {
    if (this.socket?.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket not connected, message not sent:', message);
    }
  }

  /**
   * Send chat message
   */
  sendChatMessage(content: string, replyTo?: string): void {
    this.send({
      type: 'chat_message',
      content,
      reply_to: replyTo,
    });
  }

  /**
   * Send typing indicator
   */
  sendTypingIndicator(isTyping: boolean): void {
    this.send({
      type: 'typing_indicator',
      is_typing: isTyping,
    });
  }

  /**
   * Mark messages as read
   */
  markMessagesRead(messageIds: string[]): void {
    this.send({
      type: 'mark_read',
      message_ids: messageIds,
    });
  }

  /**
   * Add event listener
   */
  on(event: string, callback: (data: any) => void): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }
    this.eventListeners.get(event)!.add(callback);
  }

  /**
   * Remove event listener
   */
  off(event: string, callback: (data: any) => void): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.delete(callback);
    }
  }

  /**
   * Get connection status
   */
  isConnected(): boolean {
    return this.socket?.readyState === WebSocket.OPEN;
  }

  /**
   * Get connection state
   */
  getConnectionState(): string {
    if (!this.socket) return 'disconnected';
    
    switch (this.socket.readyState) {
      case WebSocket.CONNECTING:
        return 'connecting';
      case WebSocket.OPEN:
        return 'connected';
      case WebSocket.CLOSING:
        return 'closing';
      case WebSocket.CLOSED:
        return 'disconnected';
      default:
        return 'unknown';
    }
  }

  // Private methods
  private handleMessage(data: WebSocketMessage): void {
    this.emit(data.type, data);
  }

  private emit(event: string, data: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in WebSocket event listener for ${event}:`, error);
        }
      });
    }
  }

  private shouldReconnect(): boolean {
    return this.reconnectAttempts < this.config.maxReconnectAttempts!;
  }

  private scheduleReconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }

    this.reconnectTimer = setTimeout(() => {
      this.reconnectAttempts++;
      console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.config.maxReconnectAttempts})`);
      this.connect().catch(error => {
        console.error('Reconnection failed:', error);
      });
    }, this.config.reconnectInterval);
  }
}

// WebSocket service factory
export const createWebSocketService = (config: WebSocketConfig): WebSocketService => {
  return new WebSocketService(config);
};

// Messaging WebSocket service
export const messagingWebSocketService = createWebSocketService({
  url: 'ws://192.168.2.65:8000/ws/messaging',
  reconnectInterval: 3000,
  maxReconnectAttempts: 5,
});

// Notifications WebSocket service
export const notificationsWebSocketService = createWebSocketService({
  url: 'ws://192.168.2.65:8000/ws/notifications',
  reconnectInterval: 5000,
  maxReconnectAttempts: 3,
});

export default WebSocketService;
