0b5f8f5563aa7ff9a57a55d3ef66ca46
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _get2 = _interopRequireDefault(require("@babel/runtime/helpers/get"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _AnimatedInterpolation = _interopRequireDefault(require("./AnimatedInterpolation"));
var _AnimatedValue = _interopRequireDefault(require("./AnimatedValue"));
var _AnimatedWithChildren2 = _interopRequireDefault(require("./AnimatedWithChildren"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && "function" == typeof p ? function (t) { return p.apply(e, t); } : p; }
var AnimatedSubtraction = exports.default = function (_AnimatedWithChildren) {
  function AnimatedSubtraction(a, b, config) {
    var _this;
    (0, _classCallCheck2.default)(this, AnimatedSubtraction);
    _this = _callSuper(this, AnimatedSubtraction, [config]);
    _this._a = typeof a === 'number' ? new _AnimatedValue.default(a) : a;
    _this._b = typeof b === 'number' ? new _AnimatedValue.default(b) : b;
    return _this;
  }
  (0, _inherits2.default)(AnimatedSubtraction, _AnimatedWithChildren);
  return (0, _createClass2.default)(AnimatedSubtraction, [{
    key: "__makeNative",
    value: function __makeNative(platformConfig) {
      this._a.__makeNative(platformConfig);
      this._b.__makeNative(platformConfig);
      _superPropGet(AnimatedSubtraction, "__makeNative", this, 3)([platformConfig]);
    }
  }, {
    key: "__getValue",
    value: function __getValue() {
      return this._a.__getValue() - this._b.__getValue();
    }
  }, {
    key: "interpolate",
    value: function interpolate(config) {
      return new _AnimatedInterpolation.default(this, config);
    }
  }, {
    key: "__attach",
    value: function __attach() {
      this._a.__addChild(this);
      this._b.__addChild(this);
      _superPropGet(AnimatedSubtraction, "__attach", this, 3)([]);
    }
  }, {
    key: "__detach",
    value: function __detach() {
      this._a.__removeChild(this);
      this._b.__removeChild(this);
      _superPropGet(AnimatedSubtraction, "__detach", this, 3)([]);
    }
  }, {
    key: "__getNativeConfig",
    value: function __getNativeConfig() {
      return {
        type: 'subtraction',
        input: [this._a.__getNativeTag(), this._b.__getNativeTag()],
        debugID: this.__getDebugID()
      };
    }
  }]);
}(_AnimatedWithChildren2.default);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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