/**
 * Error Boundary Component
 *
 * React Error Boundary component for graceful error handling
 * with user-friendly error displays and recovery options.
 *
 * Features:
 * - Graceful error catching
 * - User-friendly error display
 * - Error recovery actions
 * - Accessibility compliance
 * - Error reporting
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { Component, ReactNode } from 'react';
import { View } from 'react-native';
import { Platform } from 'react-native';
import { StyleSheet, AccessibilityInfo } from 'react-native';
import { Heading, Body } from '../typography/Typography';
import { AnimatedButton } from '../animation/AnimatedButton';
import { useHighContrastColors } from '../../contexts/HighContrastContext';
import { performanceMonitor } from '../../services/performanceMonitor';
import {
  createAppError,
  formatErrorForDisplay,
  logError,
  AppError,
  ErrorBoundaryState,
  createErrorBoundaryState,
} from '../../utils/errorHandlingUtils';

// Error boundary props
interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: (error: AppError, retry: () => void) => ReactNode;
  onError?: (error: AppError) => void;
  enableRetry?: boolean;
  maxRetries?: number;
  resetOnPropsChange?: boolean;
  resetKeys?: Array<string | number>;
}

// Error display component
interface ErrorDisplayProps {
  error: AppError;
  onRetry: () => void;
  enableRetry: boolean;
  retryCount: number;
  maxRetries: number;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  onRetry,
  enableRetry,
  retryCount,
  maxRetries,
}) => {
  const { colors } = useHighContrastColors();
  const { title, message, suggestions, actions } = formatErrorForDisplay(error);

  const canRetry = enableRetry && retryCount < maxRetries && error.retryable;

  return (
    <View style={[styles.container, { backgroundColor: colors?.background?.primary || '#FFFFFF' }]}>
      <View style={styles.content}>
        {/* Error Icon */}
        <View style={[styles.iconContainer, { backgroundColor: colors?.status?.error || '#FF6B6B' }]}>
          <Body color={colors?.text?.inverse || '#FFFFFF'}>⚠️</Body>
        </View>

        {/* Error Title */}
        <Heading
          level={2}
          color={colors?.text?.primary}
          align="center"
          style={styles.title}
        >
          {title}
        </Heading>

        {/* Error Message */}
        <Body
          color={colors?.text?.secondary}
          align="center"
          style={styles.message}
        >
          {message}
        </Body>

        {/* Suggestions */}
        {suggestions.length > 0 && (
          <View style={styles.suggestionsContainer}>
            <Body
              color={colors?.text?.primary}
              style={styles.suggestionsTitle}
            >
              Try these solutions:
            </Body>
            {suggestions.map((suggestion, index) => (
              <Body
                key={index}
                color={colors?.text?.secondary}
                style={styles.suggestion}
              >
                • {suggestion}
              </Body>
            ))}
          </View>
        )}

        {/* Action Buttons */}
        <View style={styles.actionsContainer}>
          {canRetry && (
            <AnimatedButton
              title="Try Again"
              onPress={onRetry}
              variant="primary"
              style={styles.actionButton}
              accessibilityLabel="Try again to resolve the error"
              accessibilityHint={`Attempt ${retryCount + 1} of ${maxRetries}`}
            />
          )}
          
          {actions.map((action, index) => (
            <AnimatedButton
              key={index}
              title={action.label}
              onPress={action.action}
              variant={action.primary ? 'primary' : 'outline'}
              style={styles.actionButton}
              accessibilityLabel={action.label}
            />
          ))}
        </View>

        {/* Error ID for support */}
        <Body
          color={colors?.text?.tertiary}
          align="center"
          style={styles.errorId}
        >
          Error ID: {error.id}
        </Body>
      </View>
    </View>
  );
};

// Error boundary class component
export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private resetTimeoutId: number | null = null;

  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = createErrorBoundaryState();
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    const appError = createAppError(error, {
      component: 'ErrorBoundary',
      action: 'render',
    });

    return {
      hasError: true,
      error: appError,
      errorId: appError.id,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    const appError = createAppError(error, {
      component: 'ErrorBoundary',
      action: 'componentDidCatch',
      additionalData: {
        componentStack: errorInfo.componentStack,
        errorBoundary: true,
      },
    });

    // Log the error
    logError(appError);

    // Track error in performance monitor
    performanceMonitor.trackUserInteraction('error_boundary_catch', 0, {
      error: error.message,
      errorType: error.name,
      componentStack: errorInfo.componentStack,
      errorId: appError.id,
      retryCount: this.state.retryCount,
    });

    // Call onError callback if provided
    if (this.props.onError) {
      this.props.onError(appError);
    }

    // Announce error to screen readers
    if (Platform.OS === 'ios' || Platform.OS === 'android') {
      AccessibilityInfo.announceForAccessibility(
        'An error occurred. Please check the error message and try again.'
      );
    }
  }

  componentDidUpdate(prevProps: ErrorBoundaryProps) {
    const { resetOnPropsChange, resetKeys } = this.props;
    const { hasError } = this.state;

    // Reset error state if props changed and resetOnPropsChange is enabled
    if (hasError && resetOnPropsChange && resetKeys) {
      const hasResetKeyChanged = resetKeys.some(
        (key, index) => key !== (prevProps.resetKeys?.[index])
      );

      if (hasResetKeyChanged) {
        this.resetErrorBoundary();
      }
    }
  }

  componentWillUnmount() {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
    }
  }

  resetErrorBoundary = () => {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
    }

    this.setState(createErrorBoundaryState());
  };

  handleRetry = () => {
    const { maxRetries = 3 } = this.props;
    const { retryCount } = this.state;

    if (retryCount < maxRetries) {
      this.setState(prevState => ({
        ...prevState,
        retryCount: prevState.retryCount + 1,
      }));

      // Reset error state after a short delay
      this.resetTimeoutId = window.setTimeout(() => {
        this.resetErrorBoundary();
      }, 100);
    }
  };

  render() {
    const { children, fallback, enableRetry = true, maxRetries = 3 } = this.props;
    const { hasError, error, retryCount } = this.state;

    if (hasError && error) {
      // Use custom fallback if provided
      if (fallback) {
        return fallback(error, this.handleRetry);
      }

      // Use default error display
      return (
        <ErrorDisplay
          error={error}
          onRetry={this.handleRetry}
          enableRetry={enableRetry}
          retryCount={retryCount}
          maxRetries={maxRetries}
        />
      );
    }

    return children;
  }
}

// Functional wrapper for easier usage with hooks
interface ErrorBoundaryWrapperProps extends Omit<ErrorBoundaryProps, 'children'> {
  children: ReactNode;
}

export const ErrorBoundaryWrapper: React.FC<ErrorBoundaryWrapperProps> = ({
  children,
  ...props
}) => {
  return (
    <ErrorBoundary {...props}>
      {children}
    </ErrorBoundary>
  );
};

// Hook for manual error throwing
export const useErrorHandler = () => {
  const throwError = (error: Error | string) => {
    const errorObj = typeof error === 'string' ? new Error(error) : error;
    throw errorObj;
  };

  return { throwError };
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  content: {
    maxWidth: 400,
    width: '100%',
    alignItems: 'center',
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    marginBottom: 16,
  },
  message: {
    marginBottom: 24,
    lineHeight: 24,
  },
  suggestionsContainer: {
    width: '100%',
    marginBottom: 32,
    padding: 16,
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
  },
  suggestionsTitle: {
    marginBottom: 12,
    fontWeight: '600',
  },
  suggestion: {
    marginBottom: 8,
    paddingLeft: 8,
  },
  actionsContainer: {
    width: '100%',
    gap: 12,
  },
  actionButton: {
    width: '100%',
  },
  errorId: {
    marginTop: 24,
    fontSize: 12,
    fontFamily: 'monospace',
  },
});

export default ErrorBoundary;
