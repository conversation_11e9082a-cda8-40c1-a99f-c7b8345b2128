c322f71a52cbc763c174e0a04e0083b5
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.safeModuleLoader = exports.initializeCriticalModules = exports.getModuleCacheStatus = exports.getModuleCache = exports.emergencyModuleReset = exports.clearModuleCache = exports.checkModuleHealth = void 0;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var moduleCache = new Map();
var safeModuleLoader = exports.safeModuleLoader = function safeModuleLoader(modulePath, moduleLoader, fallback) {
  var maxRetries = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 3;
  var cacheKey = `module_${modulePath}`;
  if (moduleCache.has(cacheKey)) {
    console.log(`[ModuleInitializer] Using cached module: ${modulePath}`);
    return moduleCache.get(cacheKey);
  }
  var attempts = 0;
  var lastError = null;
  while (attempts < maxRetries) {
    try {
      console.log(`[ModuleInitializer] Loading module: ${modulePath} (attempt ${attempts + 1})`);
      var module = moduleLoader();
      if (!module) {
        throw new Error(`Module ${modulePath} returned null or undefined`);
      }
      if (typeof module === 'object' && module !== null) {
        console.log(`[ModuleInitializer] ✅ Successfully loaded module: ${modulePath}`);
        moduleCache.set(cacheKey, module);
        return module;
      } else {
        throw new Error(`Module ${modulePath} is not a valid object`);
      }
    } catch (error) {
      lastError = error;
      attempts++;
      console.error(`[ModuleInitializer] ❌ Failed to load module ${modulePath} (attempt ${attempts}):`, error);
      if (attempts < maxRetries) {
        var delay = attempts * 100;
        console.log(`[ModuleInitializer] Retrying in ${delay}ms...`);
      }
    }
  }
  console.error(`[ModuleInitializer] ❌ Failed to load module ${modulePath} after ${maxRetries} attempts. Using fallback.`);
  console.error(`[ModuleInitializer] Last error:`, lastError);
  moduleCache.set(cacheKey, fallback);
  return fallback;
};
var initializeCriticalModules = exports.initializeCriticalModules = function initializeCriticalModules() {
  console.log('[ModuleInitializer] Initializing critical modules...');
  try {
    safeModuleLoader('Colors', function () {
      var colorsModule = require("../constants/Colors");
      if (!colorsModule.Colors) {
        throw new Error('Colors module does not export Colors object');
      }
      return colorsModule;
    }, {
      Colors: {
        primary: {
          default: '#4A6B52',
          light: '#6B8A74',
          dark: '#2A4B32',
          contrast: '#FFFFFF'
        },
        text: {
          primary: '#1A1A1A',
          secondary: '#6B7280',
          tertiary: '#9CA3AF'
        },
        background: {
          primary: '#FFFFFF',
          secondary: '#F9FAFB',
          tertiary: '#F3F4F6'
        },
        surface: {
          primary: '#FFFFFF',
          secondary: '#F9FAFB',
          tertiary: '#F3F4F6'
        }
      },
      DarkModeColors: {
        sage200: '#1F3A26',
        sage300: '#2A4B32',
        sage400: '#4A6B52',
        sage500: '#5A7A63',
        sage600: '#6B8A74'
      }
    });
    console.log('[ModuleInitializer] ✅ Critical modules initialized successfully');
  } catch (error) {
    console.error('[ModuleInitializer] ❌ Failed to initialize critical modules:', error);
  }
};
var checkModuleHealth = exports.checkModuleHealth = function checkModuleHealth(moduleName, moduleObject) {
  try {
    if (!moduleObject || typeof moduleObject !== 'object') {
      console.error(`[ModuleInitializer] Health check failed for ${moduleName}: not an object`);
      return false;
    }
    if (moduleName === 'Colors') {
      var requiredProperties = ['primary', 'text', 'background', 'surface'];
      for (var prop of requiredProperties) {
        if (!moduleObject[prop]) {
          console.error(`[ModuleInitializer] Health check failed for ${moduleName}: missing ${prop}`);
          return false;
        }
      }
      if (!moduleObject.primary.default) {
        console.error(`[ModuleInitializer] Health check failed for ${moduleName}: missing primary.default`);
        return false;
      }
    }
    console.log(`[ModuleInitializer] ✅ Health check passed for ${moduleName}`);
    return true;
  } catch (error) {
    console.error(`[ModuleInitializer] Health check error for ${moduleName}:`, error);
    return false;
  }
};
var clearModuleCache = exports.clearModuleCache = function clearModuleCache() {
  console.log('[ModuleInitializer] Clearing module cache');
  moduleCache.clear();
};
var getModuleCacheStatus = exports.getModuleCacheStatus = function getModuleCacheStatus() {
  var status = {};
  for (var _ref of moduleCache.entries()) {
    var _ref2 = (0, _slicedToArray2.default)(_ref, 2);
    var key = _ref2[0];
    var value = _ref2[1];
    status[key] = value !== null && value !== undefined;
  }
  return status;
};
var emergencyModuleReset = exports.emergencyModuleReset = function emergencyModuleReset() {
  console.warn('[ModuleInitializer] 🚨 Emergency module reset triggered');
  clearModuleCache();
  setTimeout(function () {
    initializeCriticalModules();
  }, 100);
};
var getModuleCache = exports.getModuleCache = function getModuleCache() {
  return moduleCache;
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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