{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "default", "_AnimatedEvent", "_AnimatedImplementation", "_createAnimatedComponent", "_AnimatedColor", "_AnimatedInterpolation", "_AnimatedNode", "_AnimatedValue", "_AnimatedValueXY", "inAnimationCallback", "mockAnimationStart", "start", "callback", "guarded<PERSON><PERSON>back", "console", "warn", "apply", "arguments", "emptyAnimation", "stop", "reset", "_startNativeLoop", "_isUsingNativeDriver", "mockCompositeAnimation", "animations", "assign", "for<PERSON>ach", "animation", "finished", "spring", "config", "anyValue", "setValue", "toValue", "timing", "decay", "sequence", "parallel", "delay", "time", "stagger", "loop", "_ref", "length", "undefined", "_ref$iterations", "iterations", "_default", "Value", "AnimatedValue", "ValueXY", "AnimatedValueXY", "Color", "AnimatedColor", "Interpolation", "AnimatedInterpolation", "Node", "AnimatedNode", "add", "AnimatedImplementation", "subtract", "divide", "multiply", "modulo", "diffClamp", "event", "createAnimatedComponent", "attachNativeEvent", "forkEvent", "unforkEvent", "Event", "AnimatedEvent"], "sources": ["AnimatedMock.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n * @format\n */\n\n'use strict';\n\nimport type {Numeric as AnimatedNumeric} from './AnimatedImplementation';\nimport type {EndResult} from './animations/Animation';\nimport type {EndCallback} from './animations/Animation';\nimport type {DecayAnimationConfig} from './animations/DecayAnimation';\nimport type {SpringAnimationConfig} from './animations/SpringAnimation';\nimport type {TimingAnimationConfig} from './animations/TimingAnimation';\n\nimport {AnimatedEvent, attachNativeEvent} from './AnimatedEvent';\nimport AnimatedImplementation from './AnimatedImplementation';\nimport createAnimatedComponent from './createAnimatedComponent';\nimport AnimatedColor from './nodes/AnimatedColor';\nimport AnimatedInterpolation from './nodes/AnimatedInterpolation';\nimport AnimatedNode from './nodes/AnimatedNode';\nimport AnimatedValue from './nodes/AnimatedValue';\nimport AnimatedValueXY from './nodes/AnimatedValueXY';\n\n/**\n * Animations are a source of flakiness in snapshot testing. This mock replaces\n * animation functions from AnimatedImplementation with empty animations for\n * predictability in tests. When possible the animation will run immediately\n * to the final state.\n */\n\n// Prevent any callback invocation from recursively triggering another\n// callback, which may trigger another animation\nlet inAnimationCallback = false;\nfunction mockAnimationStart(\n  start: (callback?: ?EndCallback) => void,\n): (callback?: ?EndCallback) => void {\n  return callback => {\n    const guardedCallback =\n      callback == null\n        ? callback\n        : (...args: Array<EndResult>) => {\n            if (inAnimationCallback) {\n              console.warn(\n                'Ignoring recursive animation callback when running mock animations',\n              );\n              return;\n            }\n            inAnimationCallback = true;\n            try {\n              callback(...args);\n            } finally {\n              inAnimationCallback = false;\n            }\n          };\n    start(guardedCallback);\n  };\n}\n\nexport type CompositeAnimation = {\n  start: (callback?: ?EndCallback) => void,\n  stop: () => void,\n  reset: () => void,\n  _startNativeLoop: (iterations?: number) => void,\n  _isUsingNativeDriver: () => boolean,\n  ...\n};\n\nconst emptyAnimation = {\n  start: () => {},\n  stop: () => {},\n  reset: () => {},\n  _startNativeLoop: () => {},\n  _isUsingNativeDriver: () => {\n    return false;\n  },\n};\n\nconst mockCompositeAnimation = (\n  animations: Array<CompositeAnimation>,\n): CompositeAnimation => ({\n  ...emptyAnimation,\n  start: mockAnimationStart((callback?: ?EndCallback): void => {\n    animations.forEach(animation => animation.start());\n    callback?.({finished: true});\n  }),\n});\n\nconst spring = function (\n  value: AnimatedValue | AnimatedValueXY | AnimatedColor,\n  config: SpringAnimationConfig,\n): CompositeAnimation {\n  const anyValue: any = value;\n  return {\n    ...emptyAnimation,\n    start: mockAnimationStart((callback?: ?EndCallback): void => {\n      anyValue.setValue(config.toValue);\n      callback?.({finished: true});\n    }),\n  };\n};\n\nconst timing = function (\n  value: AnimatedValue | AnimatedValueXY | AnimatedColor,\n  config: TimingAnimationConfig,\n): CompositeAnimation {\n  const anyValue: any = value;\n  return {\n    ...emptyAnimation,\n    start: mockAnimationStart((callback?: ?EndCallback): void => {\n      anyValue.setValue(config.toValue);\n      callback?.({finished: true});\n    }),\n  };\n};\n\nconst decay = function (\n  value: AnimatedValue | AnimatedValueXY | AnimatedColor,\n  config: DecayAnimationConfig,\n): CompositeAnimation {\n  return emptyAnimation;\n};\n\nconst sequence = function (\n  animations: Array<CompositeAnimation>,\n): CompositeAnimation {\n  return mockCompositeAnimation(animations);\n};\n\ntype ParallelConfig = {stopTogether?: boolean, ...};\nconst parallel = function (\n  animations: Array<CompositeAnimation>,\n  config?: ?ParallelConfig,\n): CompositeAnimation {\n  return mockCompositeAnimation(animations);\n};\n\nconst delay = function (time: number): CompositeAnimation {\n  return emptyAnimation;\n};\n\nconst stagger = function (\n  time: number,\n  animations: Array<CompositeAnimation>,\n): CompositeAnimation {\n  return mockCompositeAnimation(animations);\n};\n\ntype LoopAnimationConfig = {\n  iterations: number,\n  resetBeforeIteration?: boolean,\n  ...\n};\n\nconst loop = function (\n  animation: CompositeAnimation,\n  // $FlowFixMe[prop-missing]\n  {iterations = -1}: LoopAnimationConfig = {},\n): CompositeAnimation {\n  return emptyAnimation;\n};\n\nexport type {AnimatedNumeric as Numeric};\n\nexport default {\n  Value: AnimatedValue,\n  ValueXY: AnimatedValueXY,\n  Color: AnimatedColor,\n  Interpolation: AnimatedInterpolation,\n  Node: AnimatedNode,\n  decay,\n  timing,\n  spring,\n  add: AnimatedImplementation.add,\n  subtract: AnimatedImplementation.subtract,\n  divide: AnimatedImplementation.divide,\n  multiply: AnimatedImplementation.multiply,\n  modulo: AnimatedImplementation.modulo,\n  diffClamp: AnimatedImplementation.diffClamp,\n  delay,\n  sequence,\n  parallel,\n  stagger,\n  loop,\n  event: AnimatedImplementation.event,\n  createAnimatedComponent,\n  attachNativeEvent,\n  forkEvent: AnimatedImplementation.forkEvent,\n  unforkEvent: AnimatedImplementation.unforkEvent,\n  Event: AnimatedEvent,\n} as typeof AnimatedImplementation;\n"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AASb,IAAAC,cAAA,GAAAN,OAAA;AACA,IAAAO,uBAAA,GAAAR,sBAAA,CAAAC,OAAA;AACA,IAAAQ,wBAAA,GAAAT,sBAAA,CAAAC,OAAA;AACA,IAAAS,cAAA,GAAAV,sBAAA,CAAAC,OAAA;AACA,IAAAU,sBAAA,GAAAX,sBAAA,CAAAC,OAAA;AACA,IAAAW,aAAA,GAAAZ,sBAAA,CAAAC,OAAA;AACA,IAAAY,cAAA,GAAAb,sBAAA,CAAAC,OAAA;AACA,IAAAa,gBAAA,GAAAd,sBAAA,CAAAC,OAAA;AAWA,IAAIc,mBAAmB,GAAG,KAAK;AAC/B,SAASC,kBAAkBA,CACzBC,KAAwC,EACL;EACnC,OAAO,UAAAC,QAAQ,EAAI;IACjB,IAAMC,eAAe,GACnBD,QAAQ,IAAI,IAAI,GACZA,QAAQ,GACR,YAA+B;MAC7B,IAAIH,mBAAmB,EAAE;QACvBK,OAAO,CAACC,IAAI,CACV,oEACF,CAAC;QACD;MACF;MACAN,mBAAmB,GAAG,IAAI;MAC1B,IAAI;QACFG,QAAQ,CAAAI,KAAA,SAAAC,SAAQ,CAAC;MACnB,CAAC,SAAS;QACRR,mBAAmB,GAAG,KAAK;MAC7B;IACF,CAAC;IACPE,KAAK,CAACE,eAAe,CAAC;EACxB,CAAC;AACH;AAWA,IAAMK,cAAc,GAAG;EACrBP,KAAK,EAAE,SAAPA,KAAKA,CAAA,EAAQ,CAAC,CAAC;EACfQ,IAAI,EAAE,SAANA,IAAIA,CAAA,EAAQ,CAAC,CAAC;EACdC,KAAK,EAAE,SAAPA,KAAKA,CAAA,EAAQ,CAAC,CAAC;EACfC,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAA,EAAQ,CAAC,CAAC;EAC1BC,oBAAoB,EAAE,SAAtBA,oBAAoBA,CAAA,EAAQ;IAC1B,OAAO,KAAK;EACd;AACF,CAAC;AAED,IAAMC,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAC1BC,UAAqC;EAAA,OAAA5B,MAAA,CAAA6B,MAAA,KAElCP,cAAc;IACjBP,KAAK,EAAED,kBAAkB,CAAC,UAACE,QAAuB,EAAW;MAC3DY,UAAU,CAACE,OAAO,CAAC,UAAAC,SAAS;QAAA,OAAIA,SAAS,CAAChB,KAAK,CAAC,CAAC;MAAA,EAAC;MAClDC,QAAQ,YAARA,QAAQ,CAAG;QAACgB,QAAQ,EAAE;MAAI,CAAC,CAAC;IAC9B,CAAC;EAAC;AAAA,CACF;AAEF,IAAMC,MAAM,GAAG,SAATA,MAAMA,CACV9B,KAAsD,EACtD+B,MAA6B,EACT;EACpB,IAAMC,QAAa,GAAGhC,KAAK;EAC3B,OAAAH,MAAA,CAAA6B,MAAA,KACKP,cAAc;IACjBP,KAAK,EAAED,kBAAkB,CAAC,UAACE,QAAuB,EAAW;MAC3DmB,QAAQ,CAACC,QAAQ,CAACF,MAAM,CAACG,OAAO,CAAC;MACjCrB,QAAQ,YAARA,QAAQ,CAAG;QAACgB,QAAQ,EAAE;MAAI,CAAC,CAAC;IAC9B,CAAC;EAAC;AAEN,CAAC;AAED,IAAMM,MAAM,GAAG,SAATA,MAAMA,CACVnC,KAAsD,EACtD+B,MAA6B,EACT;EACpB,IAAMC,QAAa,GAAGhC,KAAK;EAC3B,OAAAH,MAAA,CAAA6B,MAAA,KACKP,cAAc;IACjBP,KAAK,EAAED,kBAAkB,CAAC,UAACE,QAAuB,EAAW;MAC3DmB,QAAQ,CAACC,QAAQ,CAACF,MAAM,CAACG,OAAO,CAAC;MACjCrB,QAAQ,YAARA,QAAQ,CAAG;QAACgB,QAAQ,EAAE;MAAI,CAAC,CAAC;IAC9B,CAAC;EAAC;AAEN,CAAC;AAED,IAAMO,KAAK,GAAG,SAARA,KAAKA,CACTpC,KAAsD,EACtD+B,MAA4B,EACR;EACpB,OAAOZ,cAAc;AACvB,CAAC;AAED,IAAMkB,QAAQ,GAAG,SAAXA,QAAQA,CACZZ,UAAqC,EACjB;EACpB,OAAOD,sBAAsB,CAACC,UAAU,CAAC;AAC3C,CAAC;AAGD,IAAMa,QAAQ,GAAG,SAAXA,QAAQA,CACZb,UAAqC,EACrCM,MAAwB,EACJ;EACpB,OAAOP,sBAAsB,CAACC,UAAU,CAAC;AAC3C,CAAC;AAED,IAAMc,KAAK,GAAG,SAARA,KAAKA,CAAaC,IAAY,EAAsB;EACxD,OAAOrB,cAAc;AACvB,CAAC;AAED,IAAMsB,OAAO,GAAG,SAAVA,OAAOA,CACXD,IAAY,EACZf,UAAqC,EACjB;EACpB,OAAOD,sBAAsB,CAACC,UAAU,CAAC;AAC3C,CAAC;AAQD,IAAMiB,IAAI,GAAG,SAAPA,IAAIA,CACRd,SAA6B,EAGT;EAAA,IAAAe,IAAA,GAAAzB,SAAA,CAAA0B,MAAA,QAAA1B,SAAA,QAAA2B,SAAA,GAAA3B,SAAA,MADqB,CAAC,CAAC;IAAA4B,eAAA,GAAAH,IAAA,CAA1CI,UAAU;IAAVA,UAAU,GAAAD,eAAA,cAAG,CAAC,CAAC,GAAAA,eAAA;EAEhB,OAAO3B,cAAc;AACvB,CAAC;AAAC,IAAA6B,QAAA,GAAAjD,OAAA,CAAAE,OAAA,GAIa;EACbgD,KAAK,EAAEC,sBAAa;EACpBC,OAAO,EAAEC,wBAAe;EACxBC,KAAK,EAAEC,sBAAa;EACpBC,aAAa,EAAEC,8BAAqB;EACpCC,IAAI,EAAEC,qBAAY;EAClBtB,KAAK,EAALA,KAAK;EACLD,MAAM,EAANA,MAAM;EACNL,MAAM,EAANA,MAAM;EACN6B,GAAG,EAAEC,+BAAsB,CAACD,GAAG;EAC/BE,QAAQ,EAAED,+BAAsB,CAACC,QAAQ;EACzCC,MAAM,EAAEF,+BAAsB,CAACE,MAAM;EACrCC,QAAQ,EAAEH,+BAAsB,CAACG,QAAQ;EACzCC,MAAM,EAAEJ,+BAAsB,CAACI,MAAM;EACrCC,SAAS,EAAEL,+BAAsB,CAACK,SAAS;EAC3C1B,KAAK,EAALA,KAAK;EACLF,QAAQ,EAARA,QAAQ;EACRC,QAAQ,EAARA,QAAQ;EACRG,OAAO,EAAPA,OAAO;EACPC,IAAI,EAAJA,IAAI;EACJwB,KAAK,EAAEN,+BAAsB,CAACM,KAAK;EACnCC,uBAAuB,EAAvBA,gCAAuB;EACvBC,iBAAiB,EAAjBA,gCAAiB;EACjBC,SAAS,EAAET,+BAAsB,CAACS,SAAS;EAC3CC,WAAW,EAAEV,+BAAsB,CAACU,WAAW;EAC/CC,KAAK,EAAEC;AACT,CAAC", "ignoreList": []}