be2e47cad96f7e53eff435b88ed5ebad
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
var stringifySafe = require("../Utilities/stringifySafe").default;
var invariant = require('invariant');
function processTransform(transform) {
  if (typeof transform === 'string') {
    var regex = new RegExp(/(\w+)\(([^)]+)\)/g);
    var transformArray = [];
    var matches;
    while (matches = regex.exec(transform)) {
      var _getKeyAndValueFromCS = _getKeyAndValueFromCSSTransform(matches[1], matches[2]),
        _key = _getKeyAndValueFromCS.key,
        value = _getKeyAndValueFromCS.value;
      if (value !== undefined) {
        transformArray.push((0, _defineProperty2.default)({}, _key, value));
      }
    }
    transform = transformArray;
  }
  if (__DEV__) {
    _validateTransforms(transform);
  }
  return transform;
}
var _getKeyAndValueFromCSSTransform = function _getKeyAndValueFromCSSTransform(key, args) {
  var _args$match;
  var argsWithUnitsRegex = new RegExp(/([+-]?\d+(\.\d+)?)([a-zA-Z]+|%)?/g);
  switch (key) {
    case 'matrix':
      return {
        key: key,
        value: (_args$match = args.match(/[+-]?\d+(\.\d+)?/g)) == null ? void 0 : _args$match.map(Number)
      };
    case 'translate':
    case 'translate3d':
      var parsedArgs = [];
      var missingUnitOfMeasurement = false;
      var matches;
      while (matches = argsWithUnitsRegex.exec(args)) {
        var _value = Number(matches[1]);
        var _unitOfMeasurement = matches[3];
        if (_value !== 0 && !_unitOfMeasurement) {
          missingUnitOfMeasurement = true;
        }
        if (_unitOfMeasurement === '%') {
          parsedArgs.push(`${_value}%`);
        } else {
          parsedArgs.push(_value);
        }
      }
      if (__DEV__) {
        invariant(!missingUnitOfMeasurement, `Transform with key ${key} must have units unless the provided value is 0, found %s`, `${key}(${args})`);
        if (key === 'translate') {
          invariant((parsedArgs == null ? void 0 : parsedArgs.length) === 1 || (parsedArgs == null ? void 0 : parsedArgs.length) === 2, 'Transform with key translate must be an string with 1 or 2 parameters, found %s: %s', parsedArgs == null ? void 0 : parsedArgs.length, `${key}(${args})`);
        } else {
          invariant((parsedArgs == null ? void 0 : parsedArgs.length) === 3, 'Transform with key translate3d must be an string with 3 parameters, found %s: %s', parsedArgs == null ? void 0 : parsedArgs.length, `${key}(${args})`);
        }
      }
      if ((parsedArgs == null ? void 0 : parsedArgs.length) === 1) {
        parsedArgs.push(0);
      }
      return {
        key: 'translate',
        value: parsedArgs
      };
    case 'translateX':
    case 'translateY':
    case 'perspective':
      var argMatches = argsWithUnitsRegex.exec(args);
      if (!(argMatches != null && argMatches.length)) {
        return {
          key: key,
          value: undefined
        };
      }
      var value = Number(argMatches[1]);
      var unitOfMeasurement = argMatches[3];
      if (__DEV__) {
        invariant(value === 0 || unitOfMeasurement, `Transform with key ${key} must have units unless the provided value is 0, found %s`, `${key}(${args})`);
      }
      return {
        key: key,
        value: value
      };
    default:
      return {
        key: key,
        value: !isNaN(args) ? Number(args) : args
      };
  }
};
function _validateTransforms(transform) {
  transform.forEach(function (transformation) {
    var keys = Object.keys(transformation);
    invariant(keys.length === 1, 'You must specify exactly one property per transform object. Passed properties: %s', stringifySafe(transformation));
    var key = keys[0];
    var value = transformation[key];
    if (key === 'matrix' && transform.length > 1) {
      console.error('When using a matrix transform, you must specify exactly one transform object. Passed transform: ' + stringifySafe(transform));
    }
    _validateTransform(key, value, transformation);
  });
}
function _validateTransform(key, value, transformation) {
  invariant(!value.getValue, 'You passed an Animated.Value to a normal component. ' + 'You need to wrap that component in an Animated. For example, ' + 'replace <View /> by <Animated.View />.');
  var multivalueTransforms = ['matrix', 'translate'];
  if (multivalueTransforms.indexOf(key) !== -1) {
    invariant(Array.isArray(value), 'Transform with key of %s must have an array as the value: %s', key, stringifySafe(transformation));
  }
  switch (key) {
    case 'matrix':
      invariant(value.length === 9 || value.length === 16, 'Matrix transform must have a length of 9 (2d) or 16 (3d). ' + 'Provided matrix has a length of %s: %s', value.length, stringifySafe(transformation));
      break;
    case 'translate':
      invariant(value.length === 2 || value.length === 3, 'Transform with key translate must be an array of length 2 or 3, found %s: %s', value.length, stringifySafe(transformation));
      break;
    case 'rotateX':
    case 'rotateY':
    case 'rotateZ':
    case 'rotate':
    case 'skewX':
    case 'skewY':
      invariant(typeof value === 'string', 'Transform with key of "%s" must be a string: %s', key, stringifySafe(transformation));
      invariant(value.indexOf('deg') > -1 || value.indexOf('rad') > -1, 'Rotate transform must be expressed in degrees (deg) or radians ' + '(rad): %s', stringifySafe(transformation));
      break;
    case 'perspective':
      invariant(typeof value === 'number', 'Transform with key of "%s" must be a number: %s', key, stringifySafe(transformation));
      invariant(value !== 0, 'Transform with key of "%s" cannot be zero: %s', key, stringifySafe(transformation));
      break;
    case 'translateX':
    case 'translateY':
      invariant(typeof value === 'number' || typeof value === 'string' && value.endsWith('%'), 'Transform with key of "%s" must be number or a percentage. Passed value: %s.', key, stringifySafe(transformation));
      break;
    case 'scale':
    case 'scaleX':
    case 'scaleY':
      invariant(typeof value === 'number', 'Transform with key of "%s" must be a number: %s', key, stringifySafe(transformation));
      break;
    default:
      invariant(false, 'Invalid transform %s: %s', key, stringifySafe(transformation));
  }
}
var _default = exports.default = processTransform;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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