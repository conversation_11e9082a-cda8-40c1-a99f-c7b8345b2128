8ecba253a5739c71f357bd209f2f7c1d
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.navigationAnalytics = exports.default = void 0;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var NavigationAnalyticsService = function () {
  function NavigationAnalyticsService() {
    (0, _classCallCheck2.default)(this, NavigationAnalyticsService);
    this.screenStartTime = 0;
    this.currentScreen = '';
    this.previousScreen = '';
    this.currentSession = this.generateSessionId();
    this.currentFlow = {
      sessionId: this.currentSession,
      startTime: Date.now(),
      screens: []
    };
  }
  return (0, _createClass2.default)(NavigationAnalyticsService, [{
    key: "generateSessionId",
    value: function generateSessionId() {
      return `nav_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
  }, {
    key: "trackScreenView",
    value: function trackScreenView(screenName, params, userRole) {
      var now = Date.now();
      if (this.currentScreen && this.screenStartTime > 0) {
        var timeSpent = now - this.screenStartTime;
        this.trackScreenMetrics({
          screenName: this.currentScreen,
          loadTime: 0,
          timeSpent: timeSpent,
          exitMethod: 'navigation'
        });
      }
      var event = {
        screenName: screenName,
        previousScreen: this.currentScreen || undefined,
        timestamp: now,
        params: params,
        userRole: userRole,
        sessionId: this.currentSession
      };
      this.currentFlow.screens.push(event);
      if (userRole) {
        this.currentFlow.userRole = userRole;
      }
      this.previousScreen = this.currentScreen;
      this.currentScreen = screenName;
      this.screenStartTime = now;
      if (__DEV__) {
        console.log('📊 Navigation Analytics - Screen View:', {
          screen: screenName,
          previous: this.previousScreen,
          params: params,
          userRole: userRole
        });
      }
      this.sendAnalyticsEvent('screen_view', event);
    }
  }, {
    key: "trackScreenLoadTime",
    value: function trackScreenLoadTime(screenName, loadTime) {
      if (__DEV__) {
        console.log('⏱️ Navigation Analytics - Screen Load Time:', {
          screen: screenName,
          loadTime: `${loadTime}ms`
        });
      }
      this.sendAnalyticsEvent('screen_load_time', {
        screenName: screenName,
        loadTime: loadTime,
        timestamp: Date.now(),
        sessionId: this.currentSession
      });
    }
  }, {
    key: "trackNavigationAction",
    value: function trackNavigationAction(action, fromScreen, toScreen, params) {
      var event = {
        action: action,
        fromScreen: fromScreen,
        toScreen: toScreen,
        params: params,
        timestamp: Date.now(),
        sessionId: this.currentSession
      };
      if (__DEV__) {
        console.log('🎯 Navigation Analytics - Action:', event);
      }
      this.sendAnalyticsEvent('navigation_action', event);
    }
  }, {
    key: "trackScreenMetrics",
    value: function trackScreenMetrics(metrics) {
      if (__DEV__) {
        console.log('📈 Navigation Analytics - Screen Metrics:', {
          screen: metrics.screenName,
          timeSpent: `${metrics.timeSpent}ms`,
          exitMethod: metrics.exitMethod
        });
      }
      this.sendAnalyticsEvent('screen_metrics', Object.assign({}, metrics, {
        timestamp: Date.now(),
        sessionId: this.currentSession
      }));
    }
  }, {
    key: "trackFlowCompletion",
    value: function trackFlowCompletion(flowName, success, metadata) {
      var event = {
        flowName: flowName,
        success: success,
        metadata: metadata,
        duration: Date.now() - this.currentFlow.startTime,
        screenCount: this.currentFlow.screens.length,
        timestamp: Date.now(),
        sessionId: this.currentSession
      };
      if (__DEV__) {
        console.log('🎯 Navigation Analytics - Flow Completion:', event);
      }
      this.sendAnalyticsEvent('flow_completion', event);
    }
  }, {
    key: "trackNavigationError",
    value: function trackNavigationError(error, screenName, params) {
      var event = {
        error: error,
        screenName: screenName,
        params: params,
        timestamp: Date.now(),
        sessionId: this.currentSession
      };
      if (__DEV__) {
        console.error('❌ Navigation Analytics - Error:', event);
      }
      this.sendAnalyticsEvent('navigation_error', event);
    }
  }, {
    key: "getCurrentFlow",
    value: function getCurrentFlow() {
      return Object.assign({}, this.currentFlow, {
        totalDuration: Date.now() - this.currentFlow.startTime
      });
    }
  }, {
    key: "startNewSession",
    value: function startNewSession(userRole) {
      this.currentFlow.totalDuration = Date.now() - this.currentFlow.startTime;
      this.currentSession = this.generateSessionId();
      this.currentFlow = {
        sessionId: this.currentSession,
        startTime: Date.now(),
        screens: [],
        userRole: userRole
      };
      this.currentScreen = '';
      this.previousScreen = '';
      this.screenStartTime = 0;
      if (__DEV__) {
        console.log('🔄 Navigation Analytics - New Session Started:', this.currentSession);
      }
    }
  }, {
    key: "getNavigationStats",
    value: function getNavigationStats() {
      var _Object$entries$sort$;
      var flow = this.getCurrentFlow();
      var screenTimes = {};
      var screenCounts = {};
      flow.screens.forEach(function (screen, index) {
        var screenName = screen.screenName;
        screenCounts[screenName] = (screenCounts[screenName] || 0) + 1;
        if (index < flow.screens.length - 1) {
          var timeSpent = flow.screens[index + 1].timestamp - screen.timestamp;
          if (!screenTimes[screenName]) {
            screenTimes[screenName] = [];
          }
          screenTimes[screenName].push(timeSpent);
        }
      });
      var mostVisitedScreen = ((_Object$entries$sort$ = Object.entries(screenCounts).sort(function (_ref, _ref2) {
        var _ref3 = (0, _slicedToArray2.default)(_ref, 2),
          a = _ref3[1];
        var _ref4 = (0, _slicedToArray2.default)(_ref2, 2),
          b = _ref4[1];
        return b - a;
      })[0]) == null ? void 0 : _Object$entries$sort$[0]) || '';
      var allTimes = Object.values(screenTimes).flat();
      var averageScreenTime = allTimes.length > 0 ? allTimes.reduce(function (sum, time) {
        return sum + time;
      }, 0) / allTimes.length : 0;
      return {
        sessionDuration: flow.totalDuration || 0,
        screenCount: flow.screens.length,
        averageScreenTime: averageScreenTime,
        mostVisitedScreen: mostVisitedScreen,
        navigationPattern: flow.screens.map(function (s) {
          return s.screenName;
        })
      };
    }
  }, {
    key: "sendAnalyticsEvent",
    value: function sendAnalyticsEvent(eventType, data) {
      if (__DEV__) {
        return;
      }
    }
  }]);
}();
var navigationAnalytics = exports.navigationAnalytics = new NavigationAnalyticsService();
var _default = exports.default = navigationAnalytics;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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