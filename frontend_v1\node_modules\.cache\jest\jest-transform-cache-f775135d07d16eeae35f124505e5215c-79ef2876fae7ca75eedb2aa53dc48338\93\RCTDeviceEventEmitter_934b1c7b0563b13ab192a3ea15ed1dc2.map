{"version": 3, "names": ["_Systrace", "require", "_EventEmitter2", "_interopRequireDefault", "_callSuper", "t", "o", "e", "_getPrototypeOf2", "default", "_possibleConstructorReturn2", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_superPropGet", "r", "p", "_get2", "RCTDeviceEventEmitterImpl", "_EventEmitter", "_classCallCheck2", "arguments", "_inherits2", "_createClass2", "key", "value", "emit", "eventType", "beginEvent", "_len", "length", "args", "Array", "_key", "concat", "endEvent", "EventEmitter", "RCTDeviceEventEmitter", "Object", "defineProperty", "global", "configurable", "_default", "exports"], "sources": ["RCTDeviceEventEmitter.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict\n * @format\n */\n\nimport type {IEventEmitter} from '../vendor/emitter/EventEmitter';\n\nimport {beginEvent, endEvent} from '../Performance/Systrace';\nimport EventEmitter from '../vendor/emitter/EventEmitter';\n\n// FIXME: use typed events\n/* $FlowFixMe[unclear-type] unclear type of events */\ntype RCTDeviceEventDefinitions = {[name: string]: Array<any>};\n\n/**\n * Global EventEmitter used by the native platform to emit events to JavaScript.\n * Events are identified by globally unique event names.\n *\n * NativeModules that emit events should instead subclass `NativeEventEmitter`.\n */\nclass RCTDeviceEventEmitterImpl extends EventEmitter<RCTDeviceEventDefinitions> {\n  // Add systrace to RCTDeviceEventEmitter.emit method for debugging\n  emit<TEvent: $Keys<RCTDeviceEventDefinitions>>(\n    eventType: TEvent,\n    ...args: RCTDeviceEventDefinitions[TEvent]\n  ): void {\n    beginEvent(() => `RCTDeviceEventEmitter.emit#${eventType}`);\n    super.emit(eventType, ...args);\n    endEvent();\n  }\n}\nconst RCTDeviceEventEmitter: IEventEmitter<RCTDeviceEventDefinitions> =\n  new RCTDeviceEventEmitterImpl();\n\nObject.defineProperty(global, '__rctDeviceEventEmitter', {\n  configurable: true,\n  value: RCTDeviceEventEmitter,\n});\n\nexport default (RCTDeviceEventEmitter: IEventEmitter<RCTDeviceEventDefinitions>);\n"], "mappings": ";;;;;;;;;;;AAYA,IAAAA,SAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAC,sBAAA,CAAAF,OAAA;AAA0D,SAAAG,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAE,gBAAA,CAAAC,OAAA,EAAAH,CAAA,OAAAI,2BAAA,CAAAD,OAAA,EAAAJ,CAAA,EAAAM,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAP,CAAA,EAAAC,CAAA,YAAAC,gBAAA,CAAAC,OAAA,EAAAJ,CAAA,EAAAS,WAAA,IAAAR,CAAA,CAAAS,KAAA,CAAAV,CAAA,EAAAE,CAAA;AAAA,SAAAI,0BAAA,cAAAN,CAAA,IAAAW,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAX,CAAA,aAAAM,yBAAA,YAAAA,0BAAA,aAAAN,CAAA;AAAA,SAAAe,cAAAf,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAc,CAAA,QAAAC,CAAA,OAAAC,KAAA,CAAAd,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAY,CAAA,GAAAhB,CAAA,CAAAY,SAAA,GAAAZ,CAAA,GAAAC,CAAA,EAAAC,CAAA,cAAAc,CAAA,yBAAAC,CAAA,aAAAjB,CAAA,WAAAiB,CAAA,CAAAP,KAAA,CAAAR,CAAA,EAAAF,CAAA,OAAAiB,CAAA;AAAA,IAYpDE,yBAAyB,aAAAC,aAAA;EAAA,SAAAD,0BAAA;IAAA,IAAAE,gBAAA,CAAAjB,OAAA,QAAAe,yBAAA;IAAA,OAAApB,UAAA,OAAAoB,yBAAA,EAAAG,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAnB,OAAA,EAAAe,yBAAA,EAAAC,aAAA;EAAA,WAAAI,aAAA,CAAApB,OAAA,EAAAe,yBAAA;IAAAM,GAAA;IAAAC,KAAA,EAE7B,SAAAC,IAAIA,CACFC,SAAiB,EAEX;MACN,IAAAC,oBAAU,EAAC;QAAA,OAAM,8BAA8BD,SAAS,EAAE;MAAA,EAAC;MAAC,SAAAE,IAAA,GAAAR,SAAA,CAAAS,MAAA,EAFzDC,IAAI,OAAAC,KAAA,CAAAH,IAAA,OAAAA,IAAA,WAAAI,IAAA,MAAAA,IAAA,GAAAJ,IAAA,EAAAI,IAAA;QAAJF,IAAI,CAAAE,IAAA,QAAAZ,SAAA,CAAAY,IAAA;MAAA;MAGPnB,aAAA,CAAAI,yBAAA,oBAAWS,SAAS,EAAAO,MAAA,CAAKH,IAAI;MAC7B,IAAAI,kBAAQ,EAAC,CAAC;IACZ;EAAC;AAAA,EATqCC,sBAAY;AAWpD,IAAMC,qBAA+D,GACnE,IAAInB,yBAAyB,CAAC,CAAC;AAEjCoB,MAAM,CAACC,cAAc,CAACC,MAAM,EAAE,yBAAyB,EAAE;EACvDC,YAAY,EAAE,IAAI;EAClBhB,KAAK,EAAEY;AACT,CAAC,CAAC;AAAC,IAAAK,QAAA,GAAAC,OAAA,CAAAxC,OAAA,GAEakC,qBAAqB", "ignoreList": []}