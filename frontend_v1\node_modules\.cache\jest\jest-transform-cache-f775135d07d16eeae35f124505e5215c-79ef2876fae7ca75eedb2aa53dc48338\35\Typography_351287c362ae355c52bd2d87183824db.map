{"version": 3, "names": ["_reactNative", "require", "BASE_FONT_SIZE", "SCALE_RATIO", "LINE_HEIGHT_RATIO", "FONT_FAMILIES", "exports", "primary", "Platform", "select", "ios", "android", "web", "default", "secondary", "monospace", "FONT_WEIGHTS", "light", "regular", "medium", "semibold", "bold", "extrabold", "getResponsiveFontSize", "size", "scale", "PixelRatio", "getFontScale", "Math", "round", "generateScale", "base", "ratio", "steps", "i", "push", "pow", "SCALE", "FONT_SIZES", "xs", "sm", "lg", "xl", "LINE_HEIGHTS", "tight", "normal", "relaxed", "loose", "LETTER_SPACING", "tighter", "wide", "wider", "widest", "TYPOGRAPHY_VARIANTS", "h1", "fontSize", "fontFamily", "fontWeight", "lineHeight", "letterSpacing", "h2", "h3", "h4", "h5", "h6", "body1", "body2", "subtitle1", "subtitle2", "caption", "overline", "textTransform", "button", "buttonSmall", "buttonLarge", "input", "label", "helper", "navItem", "tabItem", "code", "ACCESSIBILITY_FONT_SIZES", "minimum", "comfortable", "large", "extraLarge", "READING_WIDTH", "optimal", "maximum", "getOptimalLineHeight", "getAccessibleFontSize", "baseFontSize", "userPreference", "arguments", "length", "undefined", "multipliers", "small", "max", "isTextSizeAccessible", "getPlatformAdjustedSize", "OS", "_default"], "sources": ["Typography.ts"], "sourcesContent": ["/**\n * Typography System\n *\n * Comprehensive typography scale and hierarchy system following\n * modern design principles and accessibility guidelines.\n *\n * Features:\n * - Modular scale typography\n * - Responsive font sizes\n * - Accessibility compliance\n * - Platform optimization\n * - Visual hierarchy support\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\nimport { Platform, PixelRatio } from 'react-native';\n\n// Base typography configuration\nconst BASE_FONT_SIZE = 16;\nconst SCALE_RATIO = 1.25; // Major third scale\nconst LINE_HEIGHT_RATIO = 1.5;\n\n// Font families\nexport const FONT_FAMILIES = {\n  primary: Platform?.select ? Platform.select({\n    ios: 'SF Pro Display',\n    android: 'Roboto',\n    web: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif',\n    default: 'System',\n  }) : 'System',\n  secondary: Platform?.select ? Platform.select({\n    ios: 'SF Pro Text',\n    android: 'Roboto',\n    web: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif',\n    default: 'System',\n  }) : 'System',\n  monospace: Platform?.select ? Platform.select({\n    ios: 'SF Mono',\n    android: 'Roboto Mono',\n    web: 'SFMono-Regular, Consolas, \"Liberation Mono\", Menlo, monospace',\n    default: 'monospace',\n  }) : 'monospace',\n} as const;\n\n// Font weights\nexport const FONT_WEIGHTS = {\n  light: '300',\n  regular: '400',\n  medium: '500',\n  semibold: '600',\n  bold: '700',\n  extrabold: '800',\n} as const;\n\n// Calculate responsive font size\nconst getResponsiveFontSize = (size: number): number => {\n  const scale = PixelRatio?.getFontScale ? PixelRatio.getFontScale() : 1;\n  return Math.round(size * scale);\n};\n\n// Generate modular scale\nconst generateScale = (base: number, ratio: number, steps: number): number[] => {\n  const scale: number[] = [];\n  for (let i = -2; i <= steps; i++) {\n    scale.push(Math.round(base * Math.pow(ratio, i)));\n  }\n  return scale;\n};\n\nconst SCALE = generateScale(BASE_FONT_SIZE, SCALE_RATIO, 6);\n\n// Typography scale\nexport const FONT_SIZES = {\n  xs: getResponsiveFontSize(SCALE[0]), // 10px\n  sm: getResponsiveFontSize(SCALE[1]), // 13px\n  base: getResponsiveFontSize(SCALE[2]), // 16px\n  lg: getResponsiveFontSize(SCALE[3]), // 20px\n  xl: getResponsiveFontSize(SCALE[4]), // 25px\n  '2xl': getResponsiveFontSize(SCALE[5]), // 31px\n  '3xl': getResponsiveFontSize(SCALE[6]), // 39px\n  '4xl': getResponsiveFontSize(SCALE[7]), // 49px\n  '5xl': getResponsiveFontSize(SCALE[8]), // 61px\n} as const;\n\n// Line heights\nexport const LINE_HEIGHTS = {\n  tight: 1.25,\n  normal: 1.5,\n  relaxed: 1.75,\n  loose: 2,\n} as const;\n\n// Letter spacing\nexport const LETTER_SPACING = {\n  tighter: -0.05,\n  tight: -0.025,\n  normal: 0,\n  wide: 0.025,\n  wider: 0.05,\n  widest: 0.1,\n} as const;\n\n// Typography variants for semantic usage\nexport const TYPOGRAPHY_VARIANTS = {\n  // Headings\n  h1: {\n    fontSize: FONT_SIZES['4xl'],\n    fontFamily: FONT_FAMILIES.primary,\n    fontWeight: FONT_WEIGHTS.bold,\n    lineHeight: FONT_SIZES['4xl'] * LINE_HEIGHTS.tight,\n    letterSpacing: LETTER_SPACING.tight,\n  },\n  h2: {\n    fontSize: FONT_SIZES['3xl'],\n    fontFamily: FONT_FAMILIES.primary,\n    fontWeight: FONT_WEIGHTS.bold,\n    lineHeight: FONT_SIZES['3xl'] * LINE_HEIGHTS.tight,\n    letterSpacing: LETTER_SPACING.tight,\n  },\n  h3: {\n    fontSize: FONT_SIZES['2xl'],\n    fontFamily: FONT_FAMILIES.primary,\n    fontWeight: FONT_WEIGHTS.semibold,\n    lineHeight: FONT_SIZES['2xl'] * LINE_HEIGHTS.normal,\n    letterSpacing: LETTER_SPACING.normal,\n  },\n  h4: {\n    fontSize: FONT_SIZES.xl,\n    fontFamily: FONT_FAMILIES.primary,\n    fontWeight: FONT_WEIGHTS.semibold,\n    lineHeight: FONT_SIZES.xl * LINE_HEIGHTS.normal,\n    letterSpacing: LETTER_SPACING.normal,\n  },\n  h5: {\n    fontSize: FONT_SIZES.lg,\n    fontFamily: FONT_FAMILIES.primary,\n    fontWeight: FONT_WEIGHTS.medium,\n    lineHeight: FONT_SIZES.lg * LINE_HEIGHTS.normal,\n    letterSpacing: LETTER_SPACING.normal,\n  },\n  h6: {\n    fontSize: FONT_SIZES.base,\n    fontFamily: FONT_FAMILIES.primary,\n    fontWeight: FONT_WEIGHTS.medium,\n    lineHeight: FONT_SIZES.base * LINE_HEIGHTS.normal,\n    letterSpacing: LETTER_SPACING.normal,\n  },\n\n  // Body text\n  body1: {\n    fontSize: FONT_SIZES.base,\n    fontFamily: FONT_FAMILIES.secondary,\n    fontWeight: FONT_WEIGHTS.regular,\n    lineHeight: FONT_SIZES.base * LINE_HEIGHTS.normal,\n    letterSpacing: LETTER_SPACING.normal,\n  },\n  body2: {\n    fontSize: FONT_SIZES.sm,\n    fontFamily: FONT_FAMILIES.secondary,\n    fontWeight: FONT_WEIGHTS.regular,\n    lineHeight: FONT_SIZES.sm * LINE_HEIGHTS.normal,\n    letterSpacing: LETTER_SPACING.normal,\n  },\n\n  // Specialized text\n  subtitle1: {\n    fontSize: FONT_SIZES.lg,\n    fontFamily: FONT_FAMILIES.secondary,\n    fontWeight: FONT_WEIGHTS.medium,\n    lineHeight: FONT_SIZES.lg * LINE_HEIGHTS.normal,\n    letterSpacing: LETTER_SPACING.wide,\n  },\n  subtitle2: {\n    fontSize: FONT_SIZES.base,\n    fontFamily: FONT_FAMILIES.secondary,\n    fontWeight: FONT_WEIGHTS.medium,\n    lineHeight: FONT_SIZES.base * LINE_HEIGHTS.normal,\n    letterSpacing: LETTER_SPACING.wide,\n  },\n  caption: {\n    fontSize: FONT_SIZES.xs,\n    fontFamily: FONT_FAMILIES.secondary,\n    fontWeight: FONT_WEIGHTS.regular,\n    lineHeight: FONT_SIZES.xs * LINE_HEIGHTS.normal,\n    letterSpacing: LETTER_SPACING.wide,\n  },\n  overline: {\n    fontSize: FONT_SIZES.xs,\n    fontFamily: FONT_FAMILIES.secondary,\n    fontWeight: FONT_WEIGHTS.medium,\n    lineHeight: FONT_SIZES.xs * LINE_HEIGHTS.normal,\n    letterSpacing: LETTER_SPACING.widest,\n    textTransform: 'uppercase' as const,\n  },\n\n  // Interactive elements\n  button: {\n    fontSize: FONT_SIZES.base,\n    fontFamily: FONT_FAMILIES.primary,\n    fontWeight: FONT_WEIGHTS.semibold,\n    lineHeight: FONT_SIZES.base * LINE_HEIGHTS.tight,\n    letterSpacing: LETTER_SPACING.wide,\n  },\n  buttonSmall: {\n    fontSize: FONT_SIZES.sm,\n    fontFamily: FONT_FAMILIES.primary,\n    fontWeight: FONT_WEIGHTS.semibold,\n    lineHeight: FONT_SIZES.sm * LINE_HEIGHTS.tight,\n    letterSpacing: LETTER_SPACING.wide,\n  },\n  buttonLarge: {\n    fontSize: FONT_SIZES.lg,\n    fontFamily: FONT_FAMILIES.primary,\n    fontWeight: FONT_WEIGHTS.semibold,\n    lineHeight: FONT_SIZES.lg * LINE_HEIGHTS.tight,\n    letterSpacing: LETTER_SPACING.wide,\n  },\n\n  // Form elements\n  input: {\n    fontSize: FONT_SIZES.base,\n    fontFamily: FONT_FAMILIES.secondary,\n    fontWeight: FONT_WEIGHTS.regular,\n    lineHeight: FONT_SIZES.base * LINE_HEIGHTS.normal,\n    letterSpacing: LETTER_SPACING.normal,\n  },\n  label: {\n    fontSize: FONT_SIZES.sm,\n    fontFamily: FONT_FAMILIES.secondary,\n    fontWeight: FONT_WEIGHTS.medium,\n    lineHeight: FONT_SIZES.sm * LINE_HEIGHTS.normal,\n    letterSpacing: LETTER_SPACING.wide,\n  },\n  helper: {\n    fontSize: FONT_SIZES.xs,\n    fontFamily: FONT_FAMILIES.secondary,\n    fontWeight: FONT_WEIGHTS.regular,\n    lineHeight: FONT_SIZES.xs * LINE_HEIGHTS.normal,\n    letterSpacing: LETTER_SPACING.normal,\n  },\n\n  // Navigation\n  navItem: {\n    fontSize: FONT_SIZES.base,\n    fontFamily: FONT_FAMILIES.primary,\n    fontWeight: FONT_WEIGHTS.medium,\n    lineHeight: FONT_SIZES.base * LINE_HEIGHTS.tight,\n    letterSpacing: LETTER_SPACING.normal,\n  },\n  tabItem: {\n    fontSize: FONT_SIZES.sm,\n    fontFamily: FONT_FAMILIES.primary,\n    fontWeight: FONT_WEIGHTS.medium,\n    lineHeight: FONT_SIZES.sm * LINE_HEIGHTS.tight,\n    letterSpacing: LETTER_SPACING.wide,\n  },\n\n  // Code and monospace\n  code: {\n    fontSize: FONT_SIZES.sm,\n    fontFamily: FONT_FAMILIES.monospace,\n    fontWeight: FONT_WEIGHTS.regular,\n    lineHeight: FONT_SIZES.sm * LINE_HEIGHTS.relaxed,\n    letterSpacing: LETTER_SPACING.normal,\n  },\n} as const;\n\n// Accessibility helpers\nexport const ACCESSIBILITY_FONT_SIZES = {\n  minimum: 12, // Minimum readable size\n  comfortable: 16, // Comfortable reading size\n  large: 20, // Large text for accessibility\n  extraLarge: 24, // Extra large for low vision\n} as const;\n\n// Reading width guidelines\nexport const READING_WIDTH = {\n  optimal: 65, // 45-75 characters per line is optimal\n  maximum: 75,\n  minimum: 45,\n} as const;\n\n// Typography utilities\nexport const getOptimalLineHeight = (fontSize: number): number => {\n  // Larger text needs tighter line height\n  if (fontSize >= FONT_SIZES.xl) return fontSize * 1.2;\n  if (fontSize >= FONT_SIZES.lg) return fontSize * 1.3;\n  return fontSize * 1.5;\n};\n\nexport const getAccessibleFontSize = (\n  baseFontSize: number,\n  userPreference: 'small' | 'normal' | 'large' | 'extraLarge' = 'normal'\n): number => {\n  const multipliers = {\n    small: 0.875,\n    normal: 1,\n    large: 1.125,\n    extraLarge: 1.25,\n  };\n  \n  return Math.max(\n    ACCESSIBILITY_FONT_SIZES.minimum,\n    Math.round(baseFontSize * multipliers[userPreference])\n  );\n};\n\nexport const isTextSizeAccessible = (fontSize: number): boolean => {\n  return fontSize >= ACCESSIBILITY_FONT_SIZES.minimum;\n};\n\n// Platform-specific adjustments\nexport const getPlatformAdjustedSize = (size: number): number => {\n  if (Platform.OS === 'android') {\n    // Android typically needs slightly larger sizes\n    return Math.round(size * 1.05);\n  }\n  return size;\n};\n\n// Export default typography object\nexport default {\n  FONT_FAMILIES,\n  FONT_WEIGHTS,\n  FONT_SIZES,\n  LINE_HEIGHTS,\n  LETTER_SPACING,\n  TYPOGRAPHY_VARIANTS,\n  ACCESSIBILITY_FONT_SIZES,\n  READING_WIDTH,\n  getOptimalLineHeight,\n  getAccessibleFontSize,\n  isTextSizeAccessible,\n  getPlatformAdjustedSize,\n};\n"], "mappings": ";;;;AAiBA,IAAAA,YAAA,GAAAC,OAAA;AAGA,IAAMC,cAAc,GAAG,EAAE;AACzB,IAAMC,WAAW,GAAG,IAAI;AACxB,IAAMC,iBAAiB,GAAG,GAAG;AAGtB,IAAMC,aAAa,GAAAC,OAAA,CAAAD,aAAA,GAAG;EAC3BE,OAAO,EAAEC,qBAAQ,YAARA,qBAAQ,CAAEC,MAAM,GAAGD,qBAAQ,CAACC,MAAM,CAAC;IAC1CC,GAAG,EAAE,gBAAgB;IACrBC,OAAO,EAAE,QAAQ;IACjBC,GAAG,EAAE,4FAA4F;IACjGC,OAAO,EAAE;EACX,CAAC,CAAC,GAAG,QAAQ;EACbC,SAAS,EAAEN,qBAAQ,YAARA,qBAAQ,CAAEC,MAAM,GAAGD,qBAAQ,CAACC,MAAM,CAAC;IAC5CC,GAAG,EAAE,aAAa;IAClBC,OAAO,EAAE,QAAQ;IACjBC,GAAG,EAAE,4FAA4F;IACjGC,OAAO,EAAE;EACX,CAAC,CAAC,GAAG,QAAQ;EACbE,SAAS,EAAEP,qBAAQ,YAARA,qBAAQ,CAAEC,MAAM,GAAGD,qBAAQ,CAACC,MAAM,CAAC;IAC5CC,GAAG,EAAE,SAAS;IACdC,OAAO,EAAE,aAAa;IACtBC,GAAG,EAAE,+DAA+D;IACpEC,OAAO,EAAE;EACX,CAAC,CAAC,GAAG;AACP,CAAU;AAGH,IAAMG,YAAY,GAAAV,OAAA,CAAAU,YAAA,GAAG;EAC1BC,KAAK,EAAE,KAAK;EACZC,OAAO,EAAE,KAAK;EACdC,MAAM,EAAE,KAAK;EACbC,QAAQ,EAAE,KAAK;EACfC,IAAI,EAAE,KAAK;EACXC,SAAS,EAAE;AACb,CAAU;AAGV,IAAMC,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAIC,IAAY,EAAa;EACtD,IAAMC,KAAK,GAAGC,uBAAU,YAAVA,uBAAU,CAAEC,YAAY,GAAGD,uBAAU,CAACC,YAAY,CAAC,CAAC,GAAG,CAAC;EACtE,OAAOC,IAAI,CAACC,KAAK,CAACL,IAAI,GAAGC,KAAK,CAAC;AACjC,CAAC;AAGD,IAAMK,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,IAAY,EAAEC,KAAa,EAAEC,KAAa,EAAe;EAC9E,IAAMR,KAAe,GAAG,EAAE;EAC1B,KAAK,IAAIS,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,IAAID,KAAK,EAAEC,CAAC,EAAE,EAAE;IAChCT,KAAK,CAACU,IAAI,CAACP,IAAI,CAACC,KAAK,CAACE,IAAI,GAAGH,IAAI,CAACQ,GAAG,CAACJ,KAAK,EAAEE,CAAC,CAAC,CAAC,CAAC;EACnD;EACA,OAAOT,KAAK;AACd,CAAC;AAED,IAAMY,KAAK,GAAGP,aAAa,CAAC5B,cAAc,EAAEC,WAAW,EAAE,CAAC,CAAC;AAGpD,IAAMmC,UAAU,GAAAhC,OAAA,CAAAgC,UAAA,GAAG;EACxBC,EAAE,EAAEhB,qBAAqB,CAACc,KAAK,CAAC,CAAC,CAAC,CAAC;EACnCG,EAAE,EAAEjB,qBAAqB,CAACc,KAAK,CAAC,CAAC,CAAC,CAAC;EACnCN,IAAI,EAAER,qBAAqB,CAACc,KAAK,CAAC,CAAC,CAAC,CAAC;EACrCI,EAAE,EAAElB,qBAAqB,CAACc,KAAK,CAAC,CAAC,CAAC,CAAC;EACnCK,EAAE,EAAEnB,qBAAqB,CAACc,KAAK,CAAC,CAAC,CAAC,CAAC;EACnC,KAAK,EAAEd,qBAAqB,CAACc,KAAK,CAAC,CAAC,CAAC,CAAC;EACtC,KAAK,EAAEd,qBAAqB,CAACc,KAAK,CAAC,CAAC,CAAC,CAAC;EACtC,KAAK,EAAEd,qBAAqB,CAACc,KAAK,CAAC,CAAC,CAAC,CAAC;EACtC,KAAK,EAAEd,qBAAqB,CAACc,KAAK,CAAC,CAAC,CAAC;AACvC,CAAU;AAGH,IAAMM,YAAY,GAAArC,OAAA,CAAAqC,YAAA,GAAG;EAC1BC,KAAK,EAAE,IAAI;EACXC,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,IAAI;EACbC,KAAK,EAAE;AACT,CAAU;AAGH,IAAMC,cAAc,GAAA1C,OAAA,CAAA0C,cAAA,GAAG;EAC5BC,OAAO,EAAE,CAAC,IAAI;EACdL,KAAK,EAAE,CAAC,KAAK;EACbC,MAAM,EAAE,CAAC;EACTK,IAAI,EAAE,KAAK;EACXC,KAAK,EAAE,IAAI;EACXC,MAAM,EAAE;AACV,CAAU;AAGH,IAAMC,mBAAmB,GAAA/C,OAAA,CAAA+C,mBAAA,GAAG;EAEjCC,EAAE,EAAE;IACFC,QAAQ,EAAEjB,UAAU,CAAC,KAAK,CAAC;IAC3BkB,UAAU,EAAEnD,aAAa,CAACE,OAAO;IACjCkD,UAAU,EAAEzC,YAAY,CAACK,IAAI;IAC7BqC,UAAU,EAAEpB,UAAU,CAAC,KAAK,CAAC,GAAGK,YAAY,CAACC,KAAK;IAClDe,aAAa,EAAEX,cAAc,CAACJ;EAChC,CAAC;EACDgB,EAAE,EAAE;IACFL,QAAQ,EAAEjB,UAAU,CAAC,KAAK,CAAC;IAC3BkB,UAAU,EAAEnD,aAAa,CAACE,OAAO;IACjCkD,UAAU,EAAEzC,YAAY,CAACK,IAAI;IAC7BqC,UAAU,EAAEpB,UAAU,CAAC,KAAK,CAAC,GAAGK,YAAY,CAACC,KAAK;IAClDe,aAAa,EAAEX,cAAc,CAACJ;EAChC,CAAC;EACDiB,EAAE,EAAE;IACFN,QAAQ,EAAEjB,UAAU,CAAC,KAAK,CAAC;IAC3BkB,UAAU,EAAEnD,aAAa,CAACE,OAAO;IACjCkD,UAAU,EAAEzC,YAAY,CAACI,QAAQ;IACjCsC,UAAU,EAAEpB,UAAU,CAAC,KAAK,CAAC,GAAGK,YAAY,CAACE,MAAM;IACnDc,aAAa,EAAEX,cAAc,CAACH;EAChC,CAAC;EACDiB,EAAE,EAAE;IACFP,QAAQ,EAAEjB,UAAU,CAACI,EAAE;IACvBc,UAAU,EAAEnD,aAAa,CAACE,OAAO;IACjCkD,UAAU,EAAEzC,YAAY,CAACI,QAAQ;IACjCsC,UAAU,EAAEpB,UAAU,CAACI,EAAE,GAAGC,YAAY,CAACE,MAAM;IAC/Cc,aAAa,EAAEX,cAAc,CAACH;EAChC,CAAC;EACDkB,EAAE,EAAE;IACFR,QAAQ,EAAEjB,UAAU,CAACG,EAAE;IACvBe,UAAU,EAAEnD,aAAa,CAACE,OAAO;IACjCkD,UAAU,EAAEzC,YAAY,CAACG,MAAM;IAC/BuC,UAAU,EAAEpB,UAAU,CAACG,EAAE,GAAGE,YAAY,CAACE,MAAM;IAC/Cc,aAAa,EAAEX,cAAc,CAACH;EAChC,CAAC;EACDmB,EAAE,EAAE;IACFT,QAAQ,EAAEjB,UAAU,CAACP,IAAI;IACzByB,UAAU,EAAEnD,aAAa,CAACE,OAAO;IACjCkD,UAAU,EAAEzC,YAAY,CAACG,MAAM;IAC/BuC,UAAU,EAAEpB,UAAU,CAACP,IAAI,GAAGY,YAAY,CAACE,MAAM;IACjDc,aAAa,EAAEX,cAAc,CAACH;EAChC,CAAC;EAGDoB,KAAK,EAAE;IACLV,QAAQ,EAAEjB,UAAU,CAACP,IAAI;IACzByB,UAAU,EAAEnD,aAAa,CAACS,SAAS;IACnC2C,UAAU,EAAEzC,YAAY,CAACE,OAAO;IAChCwC,UAAU,EAAEpB,UAAU,CAACP,IAAI,GAAGY,YAAY,CAACE,MAAM;IACjDc,aAAa,EAAEX,cAAc,CAACH;EAChC,CAAC;EACDqB,KAAK,EAAE;IACLX,QAAQ,EAAEjB,UAAU,CAACE,EAAE;IACvBgB,UAAU,EAAEnD,aAAa,CAACS,SAAS;IACnC2C,UAAU,EAAEzC,YAAY,CAACE,OAAO;IAChCwC,UAAU,EAAEpB,UAAU,CAACE,EAAE,GAAGG,YAAY,CAACE,MAAM;IAC/Cc,aAAa,EAAEX,cAAc,CAACH;EAChC,CAAC;EAGDsB,SAAS,EAAE;IACTZ,QAAQ,EAAEjB,UAAU,CAACG,EAAE;IACvBe,UAAU,EAAEnD,aAAa,CAACS,SAAS;IACnC2C,UAAU,EAAEzC,YAAY,CAACG,MAAM;IAC/BuC,UAAU,EAAEpB,UAAU,CAACG,EAAE,GAAGE,YAAY,CAACE,MAAM;IAC/Cc,aAAa,EAAEX,cAAc,CAACE;EAChC,CAAC;EACDkB,SAAS,EAAE;IACTb,QAAQ,EAAEjB,UAAU,CAACP,IAAI;IACzByB,UAAU,EAAEnD,aAAa,CAACS,SAAS;IACnC2C,UAAU,EAAEzC,YAAY,CAACG,MAAM;IAC/BuC,UAAU,EAAEpB,UAAU,CAACP,IAAI,GAAGY,YAAY,CAACE,MAAM;IACjDc,aAAa,EAAEX,cAAc,CAACE;EAChC,CAAC;EACDmB,OAAO,EAAE;IACPd,QAAQ,EAAEjB,UAAU,CAACC,EAAE;IACvBiB,UAAU,EAAEnD,aAAa,CAACS,SAAS;IACnC2C,UAAU,EAAEzC,YAAY,CAACE,OAAO;IAChCwC,UAAU,EAAEpB,UAAU,CAACC,EAAE,GAAGI,YAAY,CAACE,MAAM;IAC/Cc,aAAa,EAAEX,cAAc,CAACE;EAChC,CAAC;EACDoB,QAAQ,EAAE;IACRf,QAAQ,EAAEjB,UAAU,CAACC,EAAE;IACvBiB,UAAU,EAAEnD,aAAa,CAACS,SAAS;IACnC2C,UAAU,EAAEzC,YAAY,CAACG,MAAM;IAC/BuC,UAAU,EAAEpB,UAAU,CAACC,EAAE,GAAGI,YAAY,CAACE,MAAM;IAC/Cc,aAAa,EAAEX,cAAc,CAACI,MAAM;IACpCmB,aAAa,EAAE;EACjB,CAAC;EAGDC,MAAM,EAAE;IACNjB,QAAQ,EAAEjB,UAAU,CAACP,IAAI;IACzByB,UAAU,EAAEnD,aAAa,CAACE,OAAO;IACjCkD,UAAU,EAAEzC,YAAY,CAACI,QAAQ;IACjCsC,UAAU,EAAEpB,UAAU,CAACP,IAAI,GAAGY,YAAY,CAACC,KAAK;IAChDe,aAAa,EAAEX,cAAc,CAACE;EAChC,CAAC;EACDuB,WAAW,EAAE;IACXlB,QAAQ,EAAEjB,UAAU,CAACE,EAAE;IACvBgB,UAAU,EAAEnD,aAAa,CAACE,OAAO;IACjCkD,UAAU,EAAEzC,YAAY,CAACI,QAAQ;IACjCsC,UAAU,EAAEpB,UAAU,CAACE,EAAE,GAAGG,YAAY,CAACC,KAAK;IAC9Ce,aAAa,EAAEX,cAAc,CAACE;EAChC,CAAC;EACDwB,WAAW,EAAE;IACXnB,QAAQ,EAAEjB,UAAU,CAACG,EAAE;IACvBe,UAAU,EAAEnD,aAAa,CAACE,OAAO;IACjCkD,UAAU,EAAEzC,YAAY,CAACI,QAAQ;IACjCsC,UAAU,EAAEpB,UAAU,CAACG,EAAE,GAAGE,YAAY,CAACC,KAAK;IAC9Ce,aAAa,EAAEX,cAAc,CAACE;EAChC,CAAC;EAGDyB,KAAK,EAAE;IACLpB,QAAQ,EAAEjB,UAAU,CAACP,IAAI;IACzByB,UAAU,EAAEnD,aAAa,CAACS,SAAS;IACnC2C,UAAU,EAAEzC,YAAY,CAACE,OAAO;IAChCwC,UAAU,EAAEpB,UAAU,CAACP,IAAI,GAAGY,YAAY,CAACE,MAAM;IACjDc,aAAa,EAAEX,cAAc,CAACH;EAChC,CAAC;EACD+B,KAAK,EAAE;IACLrB,QAAQ,EAAEjB,UAAU,CAACE,EAAE;IACvBgB,UAAU,EAAEnD,aAAa,CAACS,SAAS;IACnC2C,UAAU,EAAEzC,YAAY,CAACG,MAAM;IAC/BuC,UAAU,EAAEpB,UAAU,CAACE,EAAE,GAAGG,YAAY,CAACE,MAAM;IAC/Cc,aAAa,EAAEX,cAAc,CAACE;EAChC,CAAC;EACD2B,MAAM,EAAE;IACNtB,QAAQ,EAAEjB,UAAU,CAACC,EAAE;IACvBiB,UAAU,EAAEnD,aAAa,CAACS,SAAS;IACnC2C,UAAU,EAAEzC,YAAY,CAACE,OAAO;IAChCwC,UAAU,EAAEpB,UAAU,CAACC,EAAE,GAAGI,YAAY,CAACE,MAAM;IAC/Cc,aAAa,EAAEX,cAAc,CAACH;EAChC,CAAC;EAGDiC,OAAO,EAAE;IACPvB,QAAQ,EAAEjB,UAAU,CAACP,IAAI;IACzByB,UAAU,EAAEnD,aAAa,CAACE,OAAO;IACjCkD,UAAU,EAAEzC,YAAY,CAACG,MAAM;IAC/BuC,UAAU,EAAEpB,UAAU,CAACP,IAAI,GAAGY,YAAY,CAACC,KAAK;IAChDe,aAAa,EAAEX,cAAc,CAACH;EAChC,CAAC;EACDkC,OAAO,EAAE;IACPxB,QAAQ,EAAEjB,UAAU,CAACE,EAAE;IACvBgB,UAAU,EAAEnD,aAAa,CAACE,OAAO;IACjCkD,UAAU,EAAEzC,YAAY,CAACG,MAAM;IAC/BuC,UAAU,EAAEpB,UAAU,CAACE,EAAE,GAAGG,YAAY,CAACC,KAAK;IAC9Ce,aAAa,EAAEX,cAAc,CAACE;EAChC,CAAC;EAGD8B,IAAI,EAAE;IACJzB,QAAQ,EAAEjB,UAAU,CAACE,EAAE;IACvBgB,UAAU,EAAEnD,aAAa,CAACU,SAAS;IACnC0C,UAAU,EAAEzC,YAAY,CAACE,OAAO;IAChCwC,UAAU,EAAEpB,UAAU,CAACE,EAAE,GAAGG,YAAY,CAACG,OAAO;IAChDa,aAAa,EAAEX,cAAc,CAACH;EAChC;AACF,CAAU;AAGH,IAAMoC,wBAAwB,GAAA3E,OAAA,CAAA2E,wBAAA,GAAG;EACtCC,OAAO,EAAE,EAAE;EACXC,WAAW,EAAE,EAAE;EACfC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE;AACd,CAAU;AAGH,IAAMC,aAAa,GAAAhF,OAAA,CAAAgF,aAAA,GAAG;EAC3BC,OAAO,EAAE,EAAE;EACXC,OAAO,EAAE,EAAE;EACXN,OAAO,EAAE;AACX,CAAU;AAGH,IAAMO,oBAAoB,GAAAnF,OAAA,CAAAmF,oBAAA,GAAG,SAAvBA,oBAAoBA,CAAIlC,QAAgB,EAAa;EAEhE,IAAIA,QAAQ,IAAIjB,UAAU,CAACI,EAAE,EAAE,OAAOa,QAAQ,GAAG,GAAG;EACpD,IAAIA,QAAQ,IAAIjB,UAAU,CAACG,EAAE,EAAE,OAAOc,QAAQ,GAAG,GAAG;EACpD,OAAOA,QAAQ,GAAG,GAAG;AACvB,CAAC;AAEM,IAAMmC,qBAAqB,GAAApF,OAAA,CAAAoF,qBAAA,GAAG,SAAxBA,qBAAqBA,CAChCC,YAAoB,EAET;EAAA,IADXC,cAA2D,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,QAAQ;EAEtE,IAAMG,WAAW,GAAG;IAClBC,KAAK,EAAE,KAAK;IACZpD,MAAM,EAAE,CAAC;IACTuC,KAAK,EAAE,KAAK;IACZC,UAAU,EAAE;EACd,CAAC;EAED,OAAOzD,IAAI,CAACsE,GAAG,CACbjB,wBAAwB,CAACC,OAAO,EAChCtD,IAAI,CAACC,KAAK,CAAC8D,YAAY,GAAGK,WAAW,CAACJ,cAAc,CAAC,CACvD,CAAC;AACH,CAAC;AAEM,IAAMO,oBAAoB,GAAA7F,OAAA,CAAA6F,oBAAA,GAAG,SAAvBA,oBAAoBA,CAAI5C,QAAgB,EAAc;EACjE,OAAOA,QAAQ,IAAI0B,wBAAwB,CAACC,OAAO;AACrD,CAAC;AAGM,IAAMkB,uBAAuB,GAAA9F,OAAA,CAAA8F,uBAAA,GAAG,SAA1BA,uBAAuBA,CAAI5E,IAAY,EAAa;EAC/D,IAAIhB,qBAAQ,CAAC6F,EAAE,KAAK,SAAS,EAAE;IAE7B,OAAOzE,IAAI,CAACC,KAAK,CAACL,IAAI,GAAG,IAAI,CAAC;EAChC;EACA,OAAOA,IAAI;AACb,CAAC;AAAC,IAAA8E,QAAA,GAAAhG,OAAA,CAAAO,OAAA,GAGa;EACbR,aAAa,EAAbA,aAAa;EACbW,YAAY,EAAZA,YAAY;EACZsB,UAAU,EAAVA,UAAU;EACVK,YAAY,EAAZA,YAAY;EACZK,cAAc,EAAdA,cAAc;EACdK,mBAAmB,EAAnBA,mBAAmB;EACnB4B,wBAAwB,EAAxBA,wBAAwB;EACxBK,aAAa,EAAbA,aAAa;EACbG,oBAAoB,EAApBA,oBAAoB;EACpBC,qBAAqB,EAArBA,qBAAqB;EACrBS,oBAAoB,EAApBA,oBAAoB;EACpBC,uBAAuB,EAAvBA;AACF,CAAC", "ignoreList": []}