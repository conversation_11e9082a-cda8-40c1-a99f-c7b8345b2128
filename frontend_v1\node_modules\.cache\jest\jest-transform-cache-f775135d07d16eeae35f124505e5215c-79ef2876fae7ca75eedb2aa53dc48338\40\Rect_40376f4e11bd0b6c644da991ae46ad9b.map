{"version": 3, "names": ["createSquare", "size", "bottom", "left", "right", "top", "normalizeRect", "rectOrSize"], "sources": ["Rect.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict\n */\n\nexport type Rect = $ReadOnly<{\n  bottom?: ?number,\n  left?: ?number,\n  right?: ?number,\n  top?: ?number,\n}>;\n\nexport type RectOrSize = Rect | number;\n\nexport function createSquare(size: number): Rect {\n  return {bottom: size, left: size, right: size, top: size};\n}\n\nexport function normalizeRect(rectOrSize: ?RectOrSize): ?Rect {\n  return typeof rectOrSize === 'number' ? createSquare(rectOrSize) : rectOrSize;\n}\n"], "mappings": ";;;;;AAmBO,SAASA,YAAYA,CAACC,IAAY,EAAQ;EAC/C,OAAO;IAACC,MAAM,EAAED,IAAI;IAAEE,IAAI,EAAEF,IAAI;IAAEG,KAAK,EAAEH,IAAI;IAAEI,GAAG,EAAEJ;EAAI,CAAC;AAC3D;AAEO,SAASK,aAAaA,CAACC,UAAuB,EAAS;EAC5D,OAAO,OAAOA,UAAU,KAAK,QAAQ,GAAGP,YAAY,CAACO,UAAU,CAAC,GAAGA,UAAU;AAC/E", "ignoreList": []}