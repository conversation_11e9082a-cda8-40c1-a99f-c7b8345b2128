"""
URL configuration for Vierla Beauty Services Marketplace
Enhanced Django 4.2.16 configuration with API endpoints
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.http import JsonResponse
from drf_spectacular.views import (
    SpectacularAPIView,
    SpectacularRedocView,
    SpectacularSwaggerView,
)


def api_docs_view(request):
    """Simple API documentation endpoint"""
    docs = {
        "title": "Vierla Beauty Services API",
        "version": "2.0.0",
        "description": "Comprehensive API for beauty services marketplace with mobile optimizations",
        "endpoints": {
            "authentication": {
                "base_url": "/api/auth/",
                "endpoints": [
                    "POST /api/auth/login/ - User login",
                    "POST /api/auth/register/ - User registration",
                    "POST /api/auth/logout/ - User logout",
                    "POST /api/auth/token/refresh/ - Refresh JWT token",
                    "GET /api/auth/profile/ - Get user profile",
                    "PUT /api/auth/profile/details/ - Update user profile",
                    "GET /api/auth/status/ - Check authentication status",
                    "POST /api/auth/password/change/ - Change password",
                    "POST /api/auth/password/reset/ - Request password reset",
                    "POST /api/auth/password/reset/confirm/ - Confirm password reset",
                    "POST /api/auth/email/verify/ - Verify email",
                    "POST /api/auth/email/resend/ - Resend verification email"
                ]
            },
            "catalog": {
                "base_url": "/api/catalog/",
                "endpoints": [
                    "GET /api/catalog/categories/ - List service categories",
                    "GET /api/catalog/categories/{id}/ - Get category details",
                    "GET /api/catalog/providers/ - List service providers",
                    "GET /api/catalog/providers/{id}/ - Get provider details",
                    "GET /api/catalog/services/ - List services",
                    "GET /api/catalog/services/{id}/ - Get service details"
                ]
            },
            "bookings": {
                "base_url": "/api/bookings/",
                "endpoints": [
                    "GET /api/bookings/ - List user bookings",
                    "POST /api/bookings/ - Create new booking",
                    "GET /api/bookings/{id}/ - Get booking details",
                    "PUT /api/bookings/{id}/ - Update booking",
                    "DELETE /api/bookings/{id}/ - Cancel booking",
                    "POST /api/bookings/{id}/confirm/ - Confirm booking",
                    "POST /api/bookings/{id}/complete/ - Mark booking complete"
                ]
            }
        },
        "authentication": {
            "type": "JWT Bearer Token",
            "header": "Authorization: Bearer <token>",
            "obtain_token": "POST /api/auth/login/ with email and password"
        }
    }
    return JsonResponse(docs, json_dumps_params={'indent': 2})


def health_check_view(request):
    """Health check endpoint for monitoring"""
    from django.db import connection
    from django.core.cache import cache
    import time

    try:
        # Check database connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            db_status = "healthy"
    except Exception as e:
        db_status = f"error: {str(e)}"

    # Check cache (if configured)
    try:
        cache.set('health_check', 'test', 1)
        cache_status = "healthy" if cache.get('health_check') == 'test' else "error"
    except Exception as e:
        cache_status = f"error: {str(e)}"

    health_data = {
        "status": "healthy" if db_status == "healthy" else "unhealthy",
        "timestamp": time.time(),
        "services": {
            "database": db_status,
            "cache": cache_status,
        },
        "version": "2.0.0"
    }

    status_code = 200 if health_data["status"] == "healthy" else 503
    return JsonResponse(health_data, status=status_code)


urlpatterns = [
    # Admin interface
    path("admin/", admin.site.urls),

    # Health check endpoint
    path('api/health/', health_check_view, name='health-check'),

    # API Documentation
    path('api/docs/', SpectacularSwaggerView.as_view(url_name='schema'), name='api-docs'),
    path('api/docs/json/', api_docs_view, name='api-docs-json'),
    path('api/schema/', SpectacularAPIView.as_view(), name='schema'),
    path('api/schema/swagger-ui/',
         SpectacularSwaggerView.as_view(url_name='schema'), name='swagger-ui'),
    path('api/schema/redoc/',
         SpectacularRedocView.as_view(url_name='schema'), name='redoc'),

    # API endpoints (Legacy v0)
    path('api/auth/', include('apps.authentication.urls')),
    path('api/catalog/', include('apps.catalog.urls')),
    # Booking system enabled
    path('api/bookings/', include('apps.bookings.urls')),
    path('api/payments/', include('apps.payments.urls')),  # MVP Critical Feature - Payment Processing
    path('api/messaging/', include('apps.messaging.urls')),  # Messaging system enabled
    # path('api/reviews/', include('apps.reviews.urls')),  # To be added

    # Enhanced API v1 URLs (Backend Agent feedback implementation)
    path('api/v1/', include('api.v1.urls')),

    # Users endpoint (for API connectivity testing)
    path('api/users/', include('apps.authentication.urls')),

    # Performance Monitoring endpoints - ENABLED FOR PRODUCTION READINESS
    path('api/monitoring/', include('vierla.monitoring.urls')),

    # Performance monitoring enabled
    # path('api/analytics/', include('apps.analytics.urls')),  # Temporarily disabled
    # path('api/notifications/', include('apps.notifications.urls')),  # To be added
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL,
                          document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL,
                          document_root=settings.STATIC_ROOT)
