{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "default", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_get2", "_inherits2", "_AnimatedInterpolation", "_AnimatedValue", "_AnimatedWithChildren2", "_callSuper", "t", "o", "e", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_superPropGet", "r", "p", "AnimatedAddition", "_Animated<PERSON>ith<PERSON><PERSON><PERSON><PERSON>", "a", "b", "config", "_this", "_a", "AnimatedValue", "_b", "key", "__makeNative", "platformConfig", "__getValue", "interpolate", "AnimatedInterpolation", "__attach", "__add<PERSON><PERSON>d", "__detach", "__remove<PERSON><PERSON>d", "__getNativeConfig", "type", "input", "__getNativeTag", "debugID", "__getDebugID", "AnimatedWithChildren"], "sources": ["AnimatedAddition.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n * @format\n */\n\n'use strict';\n\nimport type {PlatformConfig} from '../AnimatedPlatformConfig';\nimport type {InterpolationConfigType} from './AnimatedInterpolation';\nimport type AnimatedNode from './AnimatedNode';\nimport type {AnimatedNodeConfig} from './AnimatedNode';\n\nimport AnimatedInterpolation from './AnimatedInterpolation';\nimport AnimatedValue from './AnimatedValue';\nimport AnimatedWithChildren from './AnimatedWithChildren';\n\nexport default class AnimatedAddition extends AnimatedWithChildren {\n  _a: AnimatedNode;\n  _b: AnimatedNode;\n\n  constructor(\n    a: AnimatedNode | number,\n    b: AnimatedNode | number,\n    config?: ?AnimatedNodeConfig,\n  ) {\n    super(config);\n    this._a = typeof a === 'number' ? new AnimatedValue(a) : a;\n    this._b = typeof b === 'number' ? new AnimatedValue(b) : b;\n  }\n\n  __makeNative(platformConfig: ?PlatformConfig) {\n    this._a.__makeNative(platformConfig);\n    this._b.__makeNative(platformConfig);\n    super.__makeNative(platformConfig);\n  }\n\n  __getValue(): number {\n    return this._a.__getValue() + this._b.__getValue();\n  }\n\n  interpolate<OutputT: number | string>(\n    config: InterpolationConfigType<OutputT>,\n  ): AnimatedInterpolation<OutputT> {\n    return new AnimatedInterpolation(this, config);\n  }\n\n  __attach(): void {\n    this._a.__addChild(this);\n    this._b.__addChild(this);\n    super.__attach();\n  }\n\n  __detach(): void {\n    this._a.__removeChild(this);\n    this._b.__removeChild(this);\n    super.__detach();\n  }\n\n  __getNativeConfig(): any {\n    return {\n      type: 'addition',\n      input: [this._a.__getNativeTag(), this._b.__getNativeTag()],\n      debugID: this.__getDebugID(),\n    };\n  }\n}\n"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAAA,IAAAC,gBAAA,GAAAP,sBAAA,CAAAC,OAAA;AAAA,IAAAO,aAAA,GAAAR,sBAAA,CAAAC,OAAA;AAAA,IAAAQ,2BAAA,GAAAT,sBAAA,CAAAC,OAAA;AAAA,IAAAS,gBAAA,GAAAV,sBAAA,CAAAC,OAAA;AAAA,IAAAU,KAAA,GAAAX,sBAAA,CAAAC,OAAA;AAAA,IAAAW,UAAA,GAAAZ,sBAAA,CAAAC,OAAA;AAOb,IAAAY,sBAAA,GAAAb,sBAAA,CAAAC,OAAA;AACA,IAAAa,cAAA,GAAAd,sBAAA,CAAAC,OAAA;AACA,IAAAc,sBAAA,GAAAf,sBAAA,CAAAC,OAAA;AAA0D,SAAAe,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAR,gBAAA,CAAAJ,OAAA,EAAAY,CAAA,OAAAT,2BAAA,CAAAH,OAAA,EAAAW,CAAA,EAAAG,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAJ,CAAA,EAAAC,CAAA,YAAAT,gBAAA,CAAAJ,OAAA,EAAAW,CAAA,EAAAM,WAAA,IAAAL,CAAA,CAAAM,KAAA,CAAAP,CAAA,EAAAE,CAAA;AAAA,SAAAC,0BAAA,cAAAH,CAAA,IAAAQ,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAR,CAAA,aAAAG,yBAAA,YAAAA,0BAAA,aAAAH,CAAA;AAAA,SAAAY,cAAAZ,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAW,CAAA,QAAAC,CAAA,OAAApB,KAAA,CAAAL,OAAA,MAAAI,gBAAA,CAAAJ,OAAA,MAAAwB,CAAA,GAAAb,CAAA,CAAAS,SAAA,GAAAT,CAAA,GAAAC,CAAA,EAAAC,CAAA,cAAAW,CAAA,yBAAAC,CAAA,aAAAd,CAAA,WAAAc,CAAA,CAAAP,KAAA,CAAAL,CAAA,EAAAF,CAAA,OAAAc,CAAA;AAAA,IAErCC,gBAAgB,GAAA5B,OAAA,CAAAE,OAAA,aAAA2B,qBAAA;EAInC,SAAAD,iBACEE,CAAwB,EACxBC,CAAwB,EACxBC,MAA4B,EAC5B;IAAA,IAAAC,KAAA;IAAA,IAAA9B,gBAAA,CAAAD,OAAA,QAAA0B,gBAAA;IACAK,KAAA,GAAArB,UAAA,OAAAgB,gBAAA,GAAMI,MAAM;IACZC,KAAA,CAAKC,EAAE,GAAG,OAAOJ,CAAC,KAAK,QAAQ,GAAG,IAAIK,sBAAa,CAACL,CAAC,CAAC,GAAGA,CAAC;IAC1DG,KAAA,CAAKG,EAAE,GAAG,OAAOL,CAAC,KAAK,QAAQ,GAAG,IAAII,sBAAa,CAACJ,CAAC,CAAC,GAAGA,CAAC;IAAC,OAAAE,KAAA;EAC7D;EAAC,IAAAzB,UAAA,CAAAN,OAAA,EAAA0B,gBAAA,EAAAC,qBAAA;EAAA,WAAAzB,aAAA,CAAAF,OAAA,EAAA0B,gBAAA;IAAAS,GAAA;IAAApC,KAAA,EAED,SAAAqC,YAAYA,CAACC,cAA+B,EAAE;MAC5C,IAAI,CAACL,EAAE,CAACI,YAAY,CAACC,cAAc,CAAC;MACpC,IAAI,CAACH,EAAE,CAACE,YAAY,CAACC,cAAc,CAAC;MACpCd,aAAA,CAAAG,gBAAA,4BAAmBW,cAAc;IACnC;EAAC;IAAAF,GAAA;IAAApC,KAAA,EAED,SAAAuC,UAAUA,CAAA,EAAW;MACnB,OAAO,IAAI,CAACN,EAAE,CAACM,UAAU,CAAC,CAAC,GAAG,IAAI,CAACJ,EAAE,CAACI,UAAU,CAAC,CAAC;IACpD;EAAC;IAAAH,GAAA;IAAApC,KAAA,EAED,SAAAwC,WAAWA,CACTT,MAAwC,EACR;MAChC,OAAO,IAAIU,8BAAqB,CAAC,IAAI,EAAEV,MAAM,CAAC;IAChD;EAAC;IAAAK,GAAA;IAAApC,KAAA,EAED,SAAA0C,QAAQA,CAAA,EAAS;MACf,IAAI,CAACT,EAAE,CAACU,UAAU,CAAC,IAAI,CAAC;MACxB,IAAI,CAACR,EAAE,CAACQ,UAAU,CAAC,IAAI,CAAC;MACxBnB,aAAA,CAAAG,gBAAA;IACF;EAAC;IAAAS,GAAA;IAAApC,KAAA,EAED,SAAA4C,QAAQA,CAAA,EAAS;MACf,IAAI,CAACX,EAAE,CAACY,aAAa,CAAC,IAAI,CAAC;MAC3B,IAAI,CAACV,EAAE,CAACU,aAAa,CAAC,IAAI,CAAC;MAC3BrB,aAAA,CAAAG,gBAAA;IACF;EAAC;IAAAS,GAAA;IAAApC,KAAA,EAED,SAAA8C,iBAAiBA,CAAA,EAAQ;MACvB,OAAO;QACLC,IAAI,EAAE,UAAU;QAChBC,KAAK,EAAE,CAAC,IAAI,CAACf,EAAE,CAACgB,cAAc,CAAC,CAAC,EAAE,IAAI,CAACd,EAAE,CAACc,cAAc,CAAC,CAAC,CAAC;QAC3DC,OAAO,EAAE,IAAI,CAACC,YAAY,CAAC;MAC7B,CAAC;IACH;EAAC;AAAA,EAhD2CC,8BAAoB", "ignoreList": []}