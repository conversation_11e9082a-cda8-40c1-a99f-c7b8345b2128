1ea8ac0037d3c2f75eac3cbffd7b9d4b
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _classPrivateFieldLooseBase2 = _interopRequireDefault(require("@babel/runtime/helpers/classPrivateFieldLooseBase"));
var _classPrivateFieldLooseKey2 = _interopRequireDefault(require("@babel/runtime/helpers/classPrivateFieldLooseKey"));
var _registry = (0, _classPrivateFieldLooseKey2.default)("registry");
var EventEmitter = exports.default = function () {
  function EventEmitter() {
    (0, _classCallCheck2.default)(this, EventEmitter);
    Object.defineProperty(this, _registry, {
      writable: true,
      value: {}
    });
  }
  return (0, _createClass2.default)(EventEmitter, [{
    key: "addListener",
    value: function addListener(eventType, listener, context) {
      if (typeof listener !== 'function') {
        throw new TypeError('EventEmitter.addListener(...): 2nd argument must be a function.');
      }
      var registrations = allocate((0, _classPrivateFieldLooseBase2.default)(this, _registry)[_registry], eventType);
      var registration = {
        context: context,
        listener: listener,
        remove: function remove() {
          registrations.delete(registration);
        }
      };
      registrations.add(registration);
      return registration;
    }
  }, {
    key: "emit",
    value: function emit(eventType) {
      var registrations = (0, _classPrivateFieldLooseBase2.default)(this, _registry)[_registry][eventType];
      if (registrations != null) {
        for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
          args[_key - 1] = arguments[_key];
        }
        for (var registration of Array.from(registrations)) {
          registration.listener.apply(registration.context, args);
        }
      }
    }
  }, {
    key: "removeAllListeners",
    value: function removeAllListeners(eventType) {
      if (eventType == null) {
        (0, _classPrivateFieldLooseBase2.default)(this, _registry)[_registry] = {};
      } else {
        delete (0, _classPrivateFieldLooseBase2.default)(this, _registry)[_registry][eventType];
      }
    }
  }, {
    key: "listenerCount",
    value: function listenerCount(eventType) {
      var registrations = (0, _classPrivateFieldLooseBase2.default)(this, _registry)[_registry][eventType];
      return registrations == null ? 0 : registrations.size;
    }
  }]);
}();
function allocate(registry, eventType) {
  var registrations = registry[eventType];
  if (registrations == null) {
    registrations = new Set();
    registry[eventType] = registrations;
  }
  return registrations;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJFdmVudEVtaXR0ZXIiLCJleHBvcnRzIiwiZGVmYXVsdCIsIl9jbGFzc0NhbGxDaGVjazIiLCJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsIl9yZWdpc3RyeSIsIndyaXRhYmxlIiwidmFsdWUiLCJfY3JlYXRlQ2xhc3MyIiwia2V5IiwiYWRkTGlzdGVuZXIiLCJldmVudFR5cGUiLCJsaXN0ZW5lciIsImNvbnRleHQiLCJUeXBlRXJyb3IiLCJyZWdpc3RyYXRpb25zIiwiYWxsb2NhdGUiLCJfY2xhc3NQcml2YXRlRmllbGRMb29zZUJhc2UyIiwicmVnaXN0cmF0aW9uIiwicmVtb3ZlIiwiZGVsZXRlIiwiYWRkIiwiZW1pdCIsIl9sZW4iLCJhcmd1bWVudHMiLCJsZW5ndGgiLCJhcmdzIiwiQXJyYXkiLCJfa2V5IiwiZnJvbSIsImFwcGx5IiwicmVtb3ZlQWxsTGlzdGVuZXJzIiwibGlzdGVuZXJDb3VudCIsInNpemUiLCJyZWdpc3RyeSIsIlNldCJdLCJzb3VyY2VzIjpbIkV2ZW50RW1pdHRlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIENvcHlyaWdodCAoYykgTWV0YSBQbGF0Zm9ybXMsIEluYy4gYW5kIGFmZmlsaWF0ZXMuXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2UgZm91bmQgaW4gdGhlXG4gKiBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKlxuICogQGZsb3cgc3RyaWN0XG4gKiBAZm9ybWF0XG4gKi9cblxuLy8gJEZsb3dGaXhNZVt1bmNsZWFyLXR5cGVdIHVuY2xlYXIgdHlwZSBvZiBldmVudHNcbnR5cGUgVW5zYWZlT2JqZWN0ID0gT2JqZWN0O1xuXG5leHBvcnQgaW50ZXJmYWNlIEV2ZW50U3Vic2NyaXB0aW9uIHtcbiAgcmVtb3ZlKCk6IHZvaWQ7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgSUV2ZW50RW1pdHRlcjxcbiAgVEV2ZW50VG9BcmdzTWFwOiAkUmVhZE9ubHk8UmVjb3JkPHN0cmluZywgJFJlYWRPbmx5QXJyYXk8VW5zYWZlT2JqZWN0Pj4+LFxuPiB7XG4gIGFkZExpc3RlbmVyPFRFdmVudDogJEtleXM8VEV2ZW50VG9BcmdzTWFwPj4oXG4gICAgZXZlbnRUeXBlOiBURXZlbnQsXG4gICAgbGlzdGVuZXI6ICguLi5hcmdzOiBURXZlbnRUb0FyZ3NNYXBbVEV2ZW50XSkgPT4gbWl4ZWQsXG4gICAgY29udGV4dD86IG1peGVkLFxuICApOiBFdmVudFN1YnNjcmlwdGlvbjtcblxuICBlbWl0PFRFdmVudDogJEtleXM8VEV2ZW50VG9BcmdzTWFwPj4oXG4gICAgZXZlbnRUeXBlOiBURXZlbnQsXG4gICAgLi4uYXJnczogVEV2ZW50VG9BcmdzTWFwW1RFdmVudF1cbiAgKTogdm9pZDtcblxuICByZW1vdmVBbGxMaXN0ZW5lcnM8VEV2ZW50OiAkS2V5czxURXZlbnRUb0FyZ3NNYXA+PihldmVudFR5cGU/OiA/VEV2ZW50KTogdm9pZDtcblxuICBsaXN0ZW5lckNvdW50PFRFdmVudDogJEtleXM8VEV2ZW50VG9BcmdzTWFwPj4oZXZlbnRUeXBlOiBURXZlbnQpOiBudW1iZXI7XG59XG5cbmludGVyZmFjZSBSZWdpc3RyYXRpb248VEFyZ3M+IHtcbiAgK2NvbnRleHQ6IG1peGVkO1xuICArbGlzdGVuZXI6ICguLi5hcmdzOiBUQXJncykgPT4gbWl4ZWQ7XG4gICtyZW1vdmU6ICgpID0+IHZvaWQ7XG59XG5cbnR5cGUgUmVnaXN0cnk8XG4gIFRFdmVudFRvQXJnc01hcDogJFJlYWRPbmx5PFJlY29yZDxzdHJpbmcsICRSZWFkT25seUFycmF5PFVuc2FmZU9iamVjdD4+Pixcbj4gPSB7XG4gIFtLIGluIGtleW9mIFRFdmVudFRvQXJnc01hcF06IFNldDxSZWdpc3RyYXRpb248VEV2ZW50VG9BcmdzTWFwW0tdPj4sXG59O1xuXG4vKipcbiAqIEV2ZW50RW1pdHRlciBtYW5hZ2VzIGxpc3RlbmVycyBhbmQgcHVibGlzaGVzIGV2ZW50cyB0byB0aGVtLlxuICpcbiAqIEV2ZW50RW1pdHRlciBhY2NlcHRzIGEgc2luZ2xlIHR5cGUgcGFyYW1ldGVyIHRoYXQgZGVmaW5lcyB0aGUgdmFsaWQgZXZlbnRzXG4gKiBhbmQgYXNzb2NpYXRlZCBsaXN0ZW5lciBhcmd1bWVudChzKS5cbiAqXG4gKiBAZXhhbXBsZVxuICpcbiAqICAgY29uc3QgZW1pdHRlciA9IG5ldyBFdmVudEVtaXR0ZXI8e1xuICogICAgIHN1Y2Nlc3M6IFtudW1iZXIsIHN0cmluZ10sXG4gKiAgICAgZXJyb3I6IFtFcnJvcl0sXG4gKiAgIH0+KCk7XG4gKlxuICogICBlbWl0dGVyLm9uKCdzdWNjZXNzJywgKHN0YXR1c0NvZGUsIHJlc3BvbnNlVGV4dCkgPT4gey4uLn0pO1xuICogICBlbWl0dGVyLmVtaXQoJ3N1Y2Nlc3MnLCAyMDAsICcuLi4nKTtcbiAqXG4gKiAgIGVtaXR0ZXIub24oJ2Vycm9yJywgZXJyb3IgPT4gey4uLn0pO1xuICogICBlbWl0dGVyLmVtaXQoJ2Vycm9yJywgbmV3IEVycm9yKCdSZXNvdXJjZSBub3QgZm91bmQnKSk7XG4gKlxuICovXG5leHBvcnQgZGVmYXVsdCBjbGFzcyBFdmVudEVtaXR0ZXI8XG4gIFRFdmVudFRvQXJnc01hcDogJFJlYWRPbmx5PFxuICAgIFJlY29yZDxzdHJpbmcsICRSZWFkT25seUFycmF5PFVuc2FmZU9iamVjdD4+LFxuICA+ID0gJFJlYWRPbmx5PFJlY29yZDxzdHJpbmcsICRSZWFkT25seUFycmF5PFVuc2FmZU9iamVjdD4+Pixcbj4gaW1wbGVtZW50cyBJRXZlbnRFbWl0dGVyPFRFdmVudFRvQXJnc01hcD5cbntcbiAgLy8gJEZsb3dGaXhNZVtpbmNvbXBhdGlibGUtdHlwZV1cbiAgI3JlZ2lzdHJ5OiBSZWdpc3RyeTxURXZlbnRUb0FyZ3NNYXA+ID0ge307XG5cbiAgLyoqXG4gICAqIFJlZ2lzdGVycyBhIGxpc3RlbmVyIHRoYXQgaXMgY2FsbGVkIHdoZW4gdGhlIHN1cHBsaWVkIGV2ZW50IGlzIGVtaXR0ZWQuXG4gICAqIFJldHVybnMgYSBzdWJzY3JpcHRpb24gdGhhdCBoYXMgYSBgcmVtb3ZlYCBtZXRob2QgdG8gdW5kbyByZWdpc3RyYXRpb24uXG4gICAqL1xuICBhZGRMaXN0ZW5lcjxURXZlbnQ6ICRLZXlzPFRFdmVudFRvQXJnc01hcD4+KFxuICAgIGV2ZW50VHlwZTogVEV2ZW50LFxuICAgIGxpc3RlbmVyOiAoLi4uYXJnczogVEV2ZW50VG9BcmdzTWFwW1RFdmVudF0pID0+IG1peGVkLFxuICAgIGNvbnRleHQ6IG1peGVkLFxuICApOiBFdmVudFN1YnNjcmlwdGlvbiB7XG4gICAgaWYgKHR5cGVvZiBsaXN0ZW5lciAhPT0gJ2Z1bmN0aW9uJykge1xuICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcihcbiAgICAgICAgJ0V2ZW50RW1pdHRlci5hZGRMaXN0ZW5lciguLi4pOiAybmQgYXJndW1lbnQgbXVzdCBiZSBhIGZ1bmN0aW9uLicsXG4gICAgICApO1xuICAgIH1cbiAgICBjb25zdCByZWdpc3RyYXRpb25zID0gYWxsb2NhdGU8XG4gICAgICBURXZlbnRUb0FyZ3NNYXAsXG4gICAgICAkS2V5czxURXZlbnRUb0FyZ3NNYXA+LFxuICAgICAgVEV2ZW50VG9BcmdzTWFwW1RFdmVudF0sXG4gICAgPih0aGlzLiNyZWdpc3RyeSwgZXZlbnRUeXBlKTtcbiAgICBjb25zdCByZWdpc3RyYXRpb246IFJlZ2lzdHJhdGlvbjxURXZlbnRUb0FyZ3NNYXBbVEV2ZW50XT4gPSB7XG4gICAgICBjb250ZXh0LFxuICAgICAgbGlzdGVuZXIsXG4gICAgICByZW1vdmUoKTogdm9pZCB7XG4gICAgICAgIHJlZ2lzdHJhdGlvbnMuZGVsZXRlKHJlZ2lzdHJhdGlvbik7XG4gICAgICB9LFxuICAgIH07XG4gICAgcmVnaXN0cmF0aW9ucy5hZGQocmVnaXN0cmF0aW9uKTtcbiAgICByZXR1cm4gcmVnaXN0cmF0aW9uO1xuICB9XG5cbiAgLyoqXG4gICAqIEVtaXRzIHRoZSBzdXBwbGllZCBldmVudC4gQWRkaXRpb25hbCBhcmd1bWVudHMgc3VwcGxpZWQgdG8gYGVtaXRgIHdpbGwgYmVcbiAgICogcGFzc2VkIHRocm91Z2ggdG8gZWFjaCBvZiB0aGUgcmVnaXN0ZXJlZCBsaXN0ZW5lcnMuXG4gICAqXG4gICAqIElmIGEgbGlzdGVuZXIgbW9kaWZpZXMgdGhlIGxpc3RlbmVycyByZWdpc3RlcmVkIGZvciB0aGUgc2FtZSBldmVudCwgdGhvc2VcbiAgICogY2hhbmdlcyB3aWxsIG5vdCBiZSByZWZsZWN0ZWQgaW4gdGhlIGN1cnJlbnQgaW52b2NhdGlvbiBvZiBgZW1pdGAuXG4gICAqL1xuICBlbWl0PFRFdmVudDogJEtleXM8VEV2ZW50VG9BcmdzTWFwPj4oXG4gICAgZXZlbnRUeXBlOiBURXZlbnQsXG4gICAgLi4uYXJnczogVEV2ZW50VG9BcmdzTWFwW1RFdmVudF1cbiAgKTogdm9pZCB7XG4gICAgY29uc3QgcmVnaXN0cmF0aW9uczogP1NldDxSZWdpc3RyYXRpb248VEV2ZW50VG9BcmdzTWFwW1RFdmVudF0+PiA9XG4gICAgICB0aGlzLiNyZWdpc3RyeVtldmVudFR5cGVdO1xuICAgIGlmIChyZWdpc3RyYXRpb25zICE9IG51bGwpIHtcbiAgICAgIC8vIENvcHkgYHJlZ2lzdHJhdGlvbnNgIHRvIHRha2UgYSBzbmFwc2hvdCB3aGVuIHdlIGludm9rZSBgZW1pdGAsIGluIGNhc2VcbiAgICAgIC8vIHJlZ2lzdHJhdGlvbnMgYXJlIGFkZGVkIG9yIHJlbW92ZWQgd2hlbiBsaXN0ZW5lcnMgYXJlIGludm9rZWQuXG4gICAgICBmb3IgKGNvbnN0IHJlZ2lzdHJhdGlvbiBvZiBBcnJheS5mcm9tKHJlZ2lzdHJhdGlvbnMpKSB7XG4gICAgICAgIC8vICRGbG93Rml4TWVbaW5jb21wYXRpYmxlLWNhbGxdXG4gICAgICAgIHJlZ2lzdHJhdGlvbi5saXN0ZW5lci5hcHBseShyZWdpc3RyYXRpb24uY29udGV4dCwgYXJncyk7XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIFJlbW92ZXMgYWxsIHJlZ2lzdGVyZWQgbGlzdGVuZXJzLlxuICAgKi9cbiAgcmVtb3ZlQWxsTGlzdGVuZXJzPFRFdmVudDogJEtleXM8VEV2ZW50VG9BcmdzTWFwPj4oXG4gICAgZXZlbnRUeXBlPzogP1RFdmVudCxcbiAgKTogdm9pZCB7XG4gICAgaWYgKGV2ZW50VHlwZSA9PSBudWxsKSB7XG4gICAgICAvLyAkRmxvd0ZpeE1lW2luY29tcGF0aWJsZS10eXBlXVxuICAgICAgdGhpcy4jcmVnaXN0cnkgPSB7fTtcbiAgICB9IGVsc2Uge1xuICAgICAgZGVsZXRlIHRoaXMuI3JlZ2lzdHJ5W2V2ZW50VHlwZV07XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIFJldHVybnMgdGhlIG51bWJlciBvZiByZWdpc3RlcmVkIGxpc3RlbmVycyBmb3IgdGhlIHN1cHBsaWVkIGV2ZW50LlxuICAgKi9cbiAgbGlzdGVuZXJDb3VudDxURXZlbnQ6ICRLZXlzPFRFdmVudFRvQXJnc01hcD4+KGV2ZW50VHlwZTogVEV2ZW50KTogbnVtYmVyIHtcbiAgICBjb25zdCByZWdpc3RyYXRpb25zOiA/U2V0PFJlZ2lzdHJhdGlvbjxURXZlbnRUb0FyZ3NNYXBbVEV2ZW50XT4+ID1cbiAgICAgIHRoaXMuI3JlZ2lzdHJ5W2V2ZW50VHlwZV07XG4gICAgcmV0dXJuIHJlZ2lzdHJhdGlvbnMgPT0gbnVsbCA/IDAgOiByZWdpc3RyYXRpb25zLnNpemU7XG4gIH1cbn1cblxuZnVuY3Rpb24gYWxsb2NhdGU8XG4gIFRFdmVudFRvQXJnc01hcDogJFJlYWRPbmx5PFJlY29yZDxzdHJpbmcsICRSZWFkT25seUFycmF5PFVuc2FmZU9iamVjdD4+PixcbiAgVEV2ZW50OiAkS2V5czxURXZlbnRUb0FyZ3NNYXA+LFxuICBURXZlbnRBcmdzOiBURXZlbnRUb0FyZ3NNYXBbVEV2ZW50XSxcbj4oXG4gIHJlZ2lzdHJ5OiBSZWdpc3RyeTxURXZlbnRUb0FyZ3NNYXA+LFxuICBldmVudFR5cGU6IFRFdmVudCxcbik6IFNldDxSZWdpc3RyYXRpb248VEV2ZW50VG9BcmdzTWFwW1RFdmVudF0+PiB7XG4gIGxldCByZWdpc3RyYXRpb25zOiA/U2V0PFJlZ2lzdHJhdGlvbjxURXZlbnRUb0FyZ3NNYXBbVEV2ZW50XT4+ID1cbiAgICByZWdpc3RyeVtldmVudFR5cGVdO1xuICBpZiAocmVnaXN0cmF0aW9ucyA9PSBudWxsKSB7XG4gICAgcmVnaXN0cmF0aW9ucyA9IG5ldyBTZXQoKTtcbiAgICByZWdpc3RyeVtldmVudFR5cGVdID0gcmVnaXN0cmF0aW9ucztcbiAgfVxuICByZXR1cm4gcmVnaXN0cmF0aW9ucztcbn1cbiJdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztJQW9FcUJBLFlBQVksR0FBQUMsT0FBQSxDQUFBQyxPQUFBO0VBQUEsU0FBQUYsYUFBQTtJQUFBLElBQUFHLGdCQUFBLENBQUFELE9BQUEsUUFBQUYsWUFBQTtJQUFBSSxNQUFBLENBQUFDLGNBQUEsT0FBQUMsU0FBQTtNQUFBQyxRQUFBO01BQUFDLEtBQUEsRUFPUSxDQUFDO0lBQUM7RUFBQTtFQUFBLFdBQUFDLGFBQUEsQ0FBQVAsT0FBQSxFQUFBRixZQUFBO0lBQUFVLEdBQUE7SUFBQUYsS0FBQSxFQU16QyxTQUFBRyxXQUFXQSxDQUNUQyxTQUFpQixFQUNqQkMsUUFBcUQsRUFDckRDLE9BQWMsRUFDSztNQUNuQixJQUFJLE9BQU9ELFFBQVEsS0FBSyxVQUFVLEVBQUU7UUFDbEMsTUFBTSxJQUFJRSxTQUFTLENBQ2pCLGlFQUNGLENBQUM7TUFDSDtNQUNBLElBQU1DLGFBQWEsR0FBR0MsUUFBUSxLQUFBQyw0QkFBQSxDQUFBaEIsT0FBQSxFQUk1QixJQUFJLEVBQUFJLFNBQUEsRUFBQUEsU0FBQSxHQUFZTSxTQUFTLENBQUM7TUFDNUIsSUFBTU8sWUFBbUQsR0FBRztRQUMxREwsT0FBTyxFQUFQQSxPQUFPO1FBQ1BELFFBQVEsRUFBUkEsUUFBUTtRQUNSTyxNQUFNLFdBQU5BLE1BQU1BLENBQUEsRUFBUztVQUNiSixhQUFhLENBQUNLLE1BQU0sQ0FBQ0YsWUFBWSxDQUFDO1FBQ3BDO01BQ0YsQ0FBQztNQUNESCxhQUFhLENBQUNNLEdBQUcsQ0FBQ0gsWUFBWSxDQUFDO01BQy9CLE9BQU9BLFlBQVk7SUFDckI7RUFBQztJQUFBVCxHQUFBO0lBQUFGLEtBQUEsRUFTRCxTQUFBZSxJQUFJQSxDQUNGWCxTQUFpQixFQUVYO01BQ04sSUFBTUksYUFBMEQsR0FDOUQsSUFBQUUsNEJBQUEsQ0FBQWhCLE9BQUEsTUFBSSxFQUFBSSxTQUFBLEVBQUFBLFNBQUEsRUFBV00sU0FBUyxDQUFDO01BQzNCLElBQUlJLGFBQWEsSUFBSSxJQUFJLEVBQUU7UUFBQSxTQUFBUSxJQUFBLEdBQUFDLFNBQUEsQ0FBQUMsTUFBQSxFQUp4QkMsSUFBSSxPQUFBQyxLQUFBLENBQUFKLElBQUEsT0FBQUEsSUFBQSxXQUFBSyxJQUFBLE1BQUFBLElBQUEsR0FBQUwsSUFBQSxFQUFBSyxJQUFBO1VBQUpGLElBQUksQ0FBQUUsSUFBQSxRQUFBSixTQUFBLENBQUFJLElBQUE7UUFBQTtRQU9MLEtBQUssSUFBTVYsWUFBWSxJQUFJUyxLQUFLLENBQUNFLElBQUksQ0FBQ2QsYUFBYSxDQUFDLEVBQUU7VUFFcERHLFlBQVksQ0FBQ04sUUFBUSxDQUFDa0IsS0FBSyxDQUFDWixZQUFZLENBQUNMLE9BQU8sRUFBRWEsSUFBSSxDQUFDO1FBQ3pEO01BQ0Y7SUFDRjtFQUFDO0lBQUFqQixHQUFBO0lBQUFGLEtBQUEsRUFLRCxTQUFBd0Isa0JBQWtCQSxDQUNoQnBCLFNBQW1CLEVBQ2I7TUFDTixJQUFJQSxTQUFTLElBQUksSUFBSSxFQUFFO1FBRXJCLElBQUFNLDRCQUFBLENBQUFoQixPQUFBLE1BQUksRUFBQUksU0FBQSxFQUFBQSxTQUFBLElBQWEsQ0FBQyxDQUFDO01BQ3JCLENBQUMsTUFBTTtRQUNMLE9BQU8sSUFBQVksNEJBQUEsQ0FBQWhCLE9BQUEsTUFBSSxFQUFBSSxTQUFBLEVBQUFBLFNBQUEsRUFBV00sU0FBUyxDQUFDO01BQ2xDO0lBQ0Y7RUFBQztJQUFBRixHQUFBO0lBQUFGLEtBQUEsRUFLRCxTQUFBeUIsYUFBYUEsQ0FBaUNyQixTQUFpQixFQUFVO01BQ3ZFLElBQU1JLGFBQTBELEdBQzlELElBQUFFLDRCQUFBLENBQUFoQixPQUFBLE1BQUksRUFBQUksU0FBQSxFQUFBQSxTQUFBLEVBQVdNLFNBQVMsQ0FBQztNQUMzQixPQUFPSSxhQUFhLElBQUksSUFBSSxHQUFHLENBQUMsR0FBR0EsYUFBYSxDQUFDa0IsSUFBSTtJQUN2RDtFQUFDO0FBQUE7QUFHSCxTQUFTakIsUUFBUUEsQ0FLZmtCLFFBQW1DLEVBQ25DdkIsU0FBaUIsRUFDMkI7RUFDNUMsSUFBSUksYUFBMEQsR0FDNURtQixRQUFRLENBQUN2QixTQUFTLENBQUM7RUFDckIsSUFBSUksYUFBYSxJQUFJLElBQUksRUFBRTtJQUN6QkEsYUFBYSxHQUFHLElBQUlvQixHQUFHLENBQUMsQ0FBQztJQUN6QkQsUUFBUSxDQUFDdkIsU0FBUyxDQUFDLEdBQUdJLGFBQWE7RUFDckM7RUFDQSxPQUFPQSxhQUFhO0FBQ3RCIiwiaWdub3JlTGlzdCI6W119