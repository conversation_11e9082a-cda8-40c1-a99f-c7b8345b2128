{"C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\services\\__tests__\\passwordlessAuthService.test.ts": [1, 278], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\services\\__tests__\\testAccountsService.test.ts": [1, 235], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\utils\\__tests__\\enhancedTestingQA.test.ts": [1, 455], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\__tests__\\components\\AnimatedButton.test.tsx": [1, 352], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\utils\\__tests__\\imageAccessibilityValidation.test.ts": [1, 222], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\utils\\__tests__\\performance.test.ts": [1, 273], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\utils\\__tests__\\accessibility.test.ts": [1, 279], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\__tests__\\integration\\complete-implementation.test.ts": [1, 0], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\utils\\__tests__\\formAccessibilityValidation.test.ts": [1, 196], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\features\\authentication\\__tests__\\authSlice.test.ts": [1, 160], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\utils\\__tests__\\formValidation.test.ts": [1, 311], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\utils\\__tests__\\focusIndicatorValidation.test.ts": [1, 180], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\utils\\__tests__\\contrastValidation.test.ts": [1, 313], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\__tests__\\setup.ts": [1, 0], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\services\\__tests__\\authService.test.ts": [1, 222], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\utils\\__tests__\\contrastEnhancer.test.ts": [1, 234], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\store\\__tests__\\providersSlice.test.ts": [1, 1693], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\navigation\\__tests__\\types.test.ts": [1, 113], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\utils\\__tests__\\wcag-standards-fix.test.ts": [1, 143], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\services\\__tests__\\authService.integration.test.ts": [1, 120], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\__tests__\\jestSetup.js": [1, 0], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\utils\\__tests__\\errorHandler.test.ts": [1, 1148], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\__tests__\\integration\\CustomerHomeFlow.integration.test.tsx": [0, 4902], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\screens\\__tests__\\CustomerHomeScreen.test.tsx": [0, 673], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\services\\__tests__\\cacheService.test.ts": [0, 545], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\hooks\\__tests__\\useErrorHandling.test.ts": [0, 10433], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\services\\__tests__\\performanceMonitor.test.ts": [0, 366], "C:\\Users\\<USER>\\Desktop\\Files\\Services Startup\\Workspace\\code\\vierla-codebase\\frontend_v1\\src\\components\\error\\__tests__\\ErrorBoundary.test.tsx": [0, 3485]}