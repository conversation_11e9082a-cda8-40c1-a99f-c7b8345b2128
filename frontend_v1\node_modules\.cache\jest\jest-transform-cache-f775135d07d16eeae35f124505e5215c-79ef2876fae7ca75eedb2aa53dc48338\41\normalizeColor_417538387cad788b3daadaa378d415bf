8bfdaadc54a65b81fb985a32552df700
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _normalizeColors = _interopRequireDefault(require("@react-native/normalize-colors"));
function normalizeColor(color) {
  if (typeof color === 'object' && color != null) {
    var _require = require("./PlatformColorValueTypes"),
      normalizeColorObject = _require.normalizeColorObject;
    var normalizedColor = normalizeColorObject(color);
    if (normalizedColor != null) {
      return normalizedColor;
    }
  }
  if (typeof color === 'string' || typeof color === 'number') {
    return (0, _normalizeColors.default)(color);
  }
}
var _default = exports.default = normalizeColor;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfbm9ybWFsaXplQ29sb3JzIiwiX2ludGVyb3BSZXF1aXJlRGVmYXVsdCIsInJlcXVpcmUiLCJub3JtYWxpemVDb2xvciIsImNvbG9yIiwiX3JlcXVpcmUiLCJub3JtYWxpemVDb2xvck9iamVjdCIsIm5vcm1hbGl6ZWRDb2xvciIsIl9ub3JtYWxpemVDb2xvciIsIl9kZWZhdWx0IiwiZXhwb3J0cyIsImRlZmF1bHQiXSwic291cmNlcyI6WyJub3JtYWxpemVDb2xvci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIENvcHlyaWdodCAoYykgTWV0YSBQbGF0Zm9ybXMsIEluYy4gYW5kIGFmZmlsaWF0ZXMuXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2UgZm91bmQgaW4gdGhlXG4gKiBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKlxuICogQGZvcm1hdFxuICogQGZsb3cgc3RyaWN0LWxvY2FsXG4gKi9cblxuLyogZXNsaW50IG5vLWJpdHdpc2U6IDAgKi9cblxuaW1wb3J0IHR5cGUge1Byb2Nlc3NlZENvbG9yVmFsdWV9IGZyb20gJy4vcHJvY2Vzc0NvbG9yJztcbmltcG9ydCB0eXBlIHtDb2xvclZhbHVlfSBmcm9tICcuL1N0eWxlU2hlZXQnO1xuXG5pbXBvcnQgX25vcm1hbGl6ZUNvbG9yIGZyb20gJ0ByZWFjdC1uYXRpdmUvbm9ybWFsaXplLWNvbG9ycyc7XG5cbmZ1bmN0aW9uIG5vcm1hbGl6ZUNvbG9yKFxuICBjb2xvcjogPyhDb2xvclZhbHVlIHwgUHJvY2Vzc2VkQ29sb3JWYWx1ZSksXG4pOiA/UHJvY2Vzc2VkQ29sb3JWYWx1ZSB7XG4gIGlmICh0eXBlb2YgY29sb3IgPT09ICdvYmplY3QnICYmIGNvbG9yICE9IG51bGwpIHtcbiAgICBjb25zdCB7bm9ybWFsaXplQ29sb3JPYmplY3R9ID0gcmVxdWlyZSgnLi9QbGF0Zm9ybUNvbG9yVmFsdWVUeXBlcycpO1xuICAgIGNvbnN0IG5vcm1hbGl6ZWRDb2xvciA9IG5vcm1hbGl6ZUNvbG9yT2JqZWN0KGNvbG9yKTtcbiAgICBpZiAobm9ybWFsaXplZENvbG9yICE9IG51bGwpIHtcbiAgICAgIHJldHVybiBub3JtYWxpemVkQ29sb3I7XG4gICAgfVxuICB9XG5cbiAgaWYgKHR5cGVvZiBjb2xvciA9PT0gJ3N0cmluZycgfHwgdHlwZW9mIGNvbG9yID09PSAnbnVtYmVyJykge1xuICAgIHJldHVybiBfbm9ybWFsaXplQ29sb3IoY29sb3IpO1xuICB9XG59XG5cbmV4cG9ydCBkZWZhdWx0IG5vcm1hbGl6ZUNvbG9yO1xuIl0sIm1hcHBpbmdzIjoiOzs7OztBQWVBLElBQUFBLGdCQUFBLEdBQUFDLHNCQUFBLENBQUFDLE9BQUE7QUFFQSxTQUFTQyxjQUFjQSxDQUNyQkMsS0FBMEMsRUFDcEI7RUFDdEIsSUFBSSxPQUFPQSxLQUFLLEtBQUssUUFBUSxJQUFJQSxLQUFLLElBQUksSUFBSSxFQUFFO0lBQzlDLElBQUFDLFFBQUEsR0FBK0JILE9BQU8sNEJBQTRCLENBQUM7TUFBNURJLG9CQUFvQixHQUFBRCxRQUFBLENBQXBCQyxvQkFBb0I7SUFDM0IsSUFBTUMsZUFBZSxHQUFHRCxvQkFBb0IsQ0FBQ0YsS0FBSyxDQUFDO0lBQ25ELElBQUlHLGVBQWUsSUFBSSxJQUFJLEVBQUU7TUFDM0IsT0FBT0EsZUFBZTtJQUN4QjtFQUNGO0VBRUEsSUFBSSxPQUFPSCxLQUFLLEtBQUssUUFBUSxJQUFJLE9BQU9BLEtBQUssS0FBSyxRQUFRLEVBQUU7SUFDMUQsT0FBTyxJQUFBSSx3QkFBZSxFQUFDSixLQUFLLENBQUM7RUFDL0I7QUFDRjtBQUFDLElBQUFLLFFBQUEsR0FBQUMsT0FBQSxDQUFBQyxPQUFBLEdBRWNSLGNBQWMiLCJpZ25vcmVMaXN0IjpbXX0=