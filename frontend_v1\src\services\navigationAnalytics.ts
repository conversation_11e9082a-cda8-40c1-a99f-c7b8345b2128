/**
 * Navigation Analytics Service - Track navigation events and user flow
 *
 * Service Contract:
 * - Tracks navigation events for analytics
 * - Monitors user flow and screen transitions
 * - Provides performance metrics for navigation
 * - Implements privacy-compliant tracking
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

interface NavigationEvent {
  screenName: string;
  previousScreen?: string;
  timestamp: number;
  params?: Record<string, any>;
  userRole?: 'customer' | 'provider';
  sessionId: string;
}

interface ScreenMetrics {
  screenName: string;
  loadTime: number;
  timeSpent: number;
  exitMethod: 'navigation' | 'back' | 'tab' | 'deep_link' | 'app_close';
}

interface NavigationFlow {
  sessionId: string;
  startTime: number;
  screens: NavigationEvent[];
  totalDuration?: number;
  userRole?: 'customer' | 'provider';
}

class NavigationAnalyticsService {
  private currentSession: string;
  private currentFlow: NavigationFlow;
  private screenStartTime: number = 0;
  private currentScreen: string = '';
  private previousScreen: string = '';

  constructor() {
    this.currentSession = this.generateSessionId();
    this.currentFlow = {
      sessionId: this.currentSession,
      startTime: Date.now(),
      screens: [],
    };
  }

  /**
   * Generate a unique session ID
   */
  private generateSessionId(): string {
    return `nav_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Track screen view event
   */
  trackScreenView(screenName: string, params?: Record<string, any>, userRole?: 'customer' | 'provider'): void {
    const now = Date.now();
    
    // Calculate time spent on previous screen
    if (this.currentScreen && this.screenStartTime > 0) {
      const timeSpent = now - this.screenStartTime;
      this.trackScreenMetrics({
        screenName: this.currentScreen,
        loadTime: 0, // Will be set separately
        timeSpent,
        exitMethod: 'navigation',
      });
    }

    // Create navigation event
    const event: NavigationEvent = {
      screenName,
      previousScreen: this.currentScreen || undefined,
      timestamp: now,
      params,
      userRole,
      sessionId: this.currentSession,
    };

    // Add to current flow
    this.currentFlow.screens.push(event);
    if (userRole) {
      this.currentFlow.userRole = userRole;
    }

    // Update current state
    this.previousScreen = this.currentScreen;
    this.currentScreen = screenName;
    this.screenStartTime = now;

    // Log for development
    if (__DEV__) {
      console.log('📊 Navigation Analytics - Screen View:', {
        screen: screenName,
        previous: this.previousScreen,
        params,
        userRole,
      });
    }

    // Send to analytics service (implement based on your analytics provider)
    this.sendAnalyticsEvent('screen_view', event);
  }

  /**
   * Track screen load performance
   */
  trackScreenLoadTime(screenName: string, loadTime: number): void {
    if (__DEV__) {
      console.log('⏱️ Navigation Analytics - Screen Load Time:', {
        screen: screenName,
        loadTime: `${loadTime}ms`,
      });
    }

    this.sendAnalyticsEvent('screen_load_time', {
      screenName,
      loadTime,
      timestamp: Date.now(),
      sessionId: this.currentSession,
    });
  }

  /**
   * Track navigation action (button press, tab switch, etc.)
   */
  trackNavigationAction(
    action: 'tab_switch' | 'button_press' | 'back_button' | 'deep_link' | 'swipe',
    fromScreen: string,
    toScreen: string,
    params?: Record<string, any>
  ): void {
    const event = {
      action,
      fromScreen,
      toScreen,
      params,
      timestamp: Date.now(),
      sessionId: this.currentSession,
    };

    if (__DEV__) {
      console.log('🎯 Navigation Analytics - Action:', event);
    }

    this.sendAnalyticsEvent('navigation_action', event);
  }

  /**
   * Track screen metrics (time spent, exit method)
   */
  private trackScreenMetrics(metrics: ScreenMetrics): void {
    if (__DEV__) {
      console.log('📈 Navigation Analytics - Screen Metrics:', {
        screen: metrics.screenName,
        timeSpent: `${metrics.timeSpent}ms`,
        exitMethod: metrics.exitMethod,
      });
    }

    this.sendAnalyticsEvent('screen_metrics', {
      ...metrics,
      timestamp: Date.now(),
      sessionId: this.currentSession,
    });
  }

  /**
   * Track user flow completion
   */
  trackFlowCompletion(flowName: string, success: boolean, metadata?: Record<string, any>): void {
    const event = {
      flowName,
      success,
      metadata,
      duration: Date.now() - this.currentFlow.startTime,
      screenCount: this.currentFlow.screens.length,
      timestamp: Date.now(),
      sessionId: this.currentSession,
    };

    if (__DEV__) {
      console.log('🎯 Navigation Analytics - Flow Completion:', event);
    }

    this.sendAnalyticsEvent('flow_completion', event);
  }

  /**
   * Track navigation error
   */
  trackNavigationError(error: string, screenName: string, params?: Record<string, any>): void {
    const event = {
      error,
      screenName,
      params,
      timestamp: Date.now(),
      sessionId: this.currentSession,
    };

    if (__DEV__) {
      console.error('❌ Navigation Analytics - Error:', event);
    }

    this.sendAnalyticsEvent('navigation_error', event);
  }

  /**
   * Get current navigation flow
   */
  getCurrentFlow(): NavigationFlow {
    return {
      ...this.currentFlow,
      totalDuration: Date.now() - this.currentFlow.startTime,
    };
  }

  /**
   * Start a new session
   */
  startNewSession(userRole?: 'customer' | 'provider'): void {
    // Complete current flow
    this.currentFlow.totalDuration = Date.now() - this.currentFlow.startTime;
    
    // Start new session
    this.currentSession = this.generateSessionId();
    this.currentFlow = {
      sessionId: this.currentSession,
      startTime: Date.now(),
      screens: [],
      userRole,
    };
    this.currentScreen = '';
    this.previousScreen = '';
    this.screenStartTime = 0;

    if (__DEV__) {
      console.log('🔄 Navigation Analytics - New Session Started:', this.currentSession);
    }
  }

  /**
   * Get navigation statistics
   */
  getNavigationStats(): {
    sessionDuration: number;
    screenCount: number;
    averageScreenTime: number;
    mostVisitedScreen: string;
    navigationPattern: string[];
  } {
    const flow = this.getCurrentFlow();
    const screenTimes: Record<string, number[]> = {};
    const screenCounts: Record<string, number> = {};

    // Calculate screen statistics
    flow.screens.forEach((screen, index) => {
      const screenName = screen.screenName;
      
      // Count visits
      screenCounts[screenName] = (screenCounts[screenName] || 0) + 1;
      
      // Calculate time spent (if not the last screen)
      if (index < flow.screens.length - 1) {
        const timeSpent = flow.screens[index + 1].timestamp - screen.timestamp;
        if (!screenTimes[screenName]) {
          screenTimes[screenName] = [];
        }
        screenTimes[screenName].push(timeSpent);
      }
    });

    // Find most visited screen
    const mostVisitedScreen = Object.entries(screenCounts)
      .sort(([, a], [, b]) => b - a)[0]?.[0] || '';

    // Calculate average screen time
    const allTimes = Object.values(screenTimes).flat();
    const averageScreenTime = allTimes.length > 0 
      ? allTimes.reduce((sum, time) => sum + time, 0) / allTimes.length 
      : 0;

    return {
      sessionDuration: flow.totalDuration || 0,
      screenCount: flow.screens.length,
      averageScreenTime,
      mostVisitedScreen,
      navigationPattern: flow.screens.map(s => s.screenName),
    };
  }

  /**
   * Send analytics event to external service
   */
  private sendAnalyticsEvent(eventType: string, data: any): void {
    // Implement based on your analytics provider (Firebase, Mixpanel, etc.)
    // For now, just store locally for development
    
    if (__DEV__) {
      // In development, just log to console
      return;
    }

    // Example implementation for Firebase Analytics:
    // analytics().logEvent(eventType, data);
    
    // Example implementation for custom analytics:
    // analyticsService.track(eventType, data);
  }
}

// Export singleton instance
export const navigationAnalytics = new NavigationAnalyticsService();
export default navigationAnalytics;
