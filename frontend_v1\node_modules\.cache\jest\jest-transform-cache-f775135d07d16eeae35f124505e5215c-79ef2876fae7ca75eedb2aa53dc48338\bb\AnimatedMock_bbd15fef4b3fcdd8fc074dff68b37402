b4271b5dc1f7bed34e19acf5d0b86d88
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _AnimatedEvent = require("./AnimatedEvent");
var _AnimatedImplementation = _interopRequireDefault(require("./AnimatedImplementation"));
var _createAnimatedComponent = _interopRequireDefault(require("./createAnimatedComponent"));
var _AnimatedColor = _interopRequireDefault(require("./nodes/AnimatedColor"));
var _AnimatedInterpolation = _interopRequireDefault(require("./nodes/AnimatedInterpolation"));
var _AnimatedNode = _interopRequireDefault(require("./nodes/AnimatedNode"));
var _AnimatedValue = _interopRequireDefault(require("./nodes/AnimatedValue"));
var _AnimatedValueXY = _interopRequireDefault(require("./nodes/AnimatedValueXY"));
var inAnimationCallback = false;
function mockAnimationStart(start) {
  return function (callback) {
    var guardedCallback = callback == null ? callback : function () {
      if (inAnimationCallback) {
        console.warn('Ignoring recursive animation callback when running mock animations');
        return;
      }
      inAnimationCallback = true;
      try {
        callback.apply(void 0, arguments);
      } finally {
        inAnimationCallback = false;
      }
    };
    start(guardedCallback);
  };
}
var emptyAnimation = {
  start: function start() {},
  stop: function stop() {},
  reset: function reset() {},
  _startNativeLoop: function _startNativeLoop() {},
  _isUsingNativeDriver: function _isUsingNativeDriver() {
    return false;
  }
};
var mockCompositeAnimation = function mockCompositeAnimation(animations) {
  return Object.assign({}, emptyAnimation, {
    start: mockAnimationStart(function (callback) {
      animations.forEach(function (animation) {
        return animation.start();
      });
      callback == null || callback({
        finished: true
      });
    })
  });
};
var spring = function spring(value, config) {
  var anyValue = value;
  return Object.assign({}, emptyAnimation, {
    start: mockAnimationStart(function (callback) {
      anyValue.setValue(config.toValue);
      callback == null || callback({
        finished: true
      });
    })
  });
};
var timing = function timing(value, config) {
  var anyValue = value;
  return Object.assign({}, emptyAnimation, {
    start: mockAnimationStart(function (callback) {
      anyValue.setValue(config.toValue);
      callback == null || callback({
        finished: true
      });
    })
  });
};
var decay = function decay(value, config) {
  return emptyAnimation;
};
var sequence = function sequence(animations) {
  return mockCompositeAnimation(animations);
};
var parallel = function parallel(animations, config) {
  return mockCompositeAnimation(animations);
};
var delay = function delay(time) {
  return emptyAnimation;
};
var stagger = function stagger(time, animations) {
  return mockCompositeAnimation(animations);
};
var loop = function loop(animation) {
  var _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},
    _ref$iterations = _ref.iterations,
    iterations = _ref$iterations === void 0 ? -1 : _ref$iterations;
  return emptyAnimation;
};
var _default = exports.default = {
  Value: _AnimatedValue.default,
  ValueXY: _AnimatedValueXY.default,
  Color: _AnimatedColor.default,
  Interpolation: _AnimatedInterpolation.default,
  Node: _AnimatedNode.default,
  decay: decay,
  timing: timing,
  spring: spring,
  add: _AnimatedImplementation.default.add,
  subtract: _AnimatedImplementation.default.subtract,
  divide: _AnimatedImplementation.default.divide,
  multiply: _AnimatedImplementation.default.multiply,
  modulo: _AnimatedImplementation.default.modulo,
  diffClamp: _AnimatedImplementation.default.diffClamp,
  delay: delay,
  sequence: sequence,
  parallel: parallel,
  stagger: stagger,
  loop: loop,
  event: _AnimatedImplementation.default.event,
  createAnimatedComponent: _createAnimatedComponent.default,
  attachNativeEvent: _AnimatedEvent.attachNativeEvent,
  forkEvent: _AnimatedImplementation.default.forkEvent,
  unforkEvent: _AnimatedImplementation.default.unforkEvent,
  Event: _AnimatedEvent.AnimatedEvent
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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