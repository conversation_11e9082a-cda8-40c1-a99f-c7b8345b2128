4ea7c6104e14615b8c01a30fd0a8a2c1
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.validatePerformance = exports.validateCodeQuality = exports.validateAccessibility = exports.testSuiteUtils = exports.testDataFactories = exports.renderWithEnhancedProviders = exports.enhancedTestingQA = exports.enhancedAssertions = exports.default = exports.EnhancedTestingQA = exports.DEFAULT_ENHANCED_TEST_CONFIG = void 0;
var _toConsumableArray2 = _interopRequireDefault(require("@babel/runtime/helpers/toConsumableArray"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _reactNative = require("@testing-library/react-native");
var DEFAULT_ENHANCED_TEST_CONFIG = exports.DEFAULT_ENHANCED_TEST_CONFIG = {
  enablePerformanceTesting: true,
  enableAccessibilityTesting: true,
  enableVisualRegression: false,
  enableCodeQualityChecks: true,
  testTimeout: 10000,
  coverageThreshold: {
    statements: 85,
    branches: 80,
    functions: 85,
    lines: 85
  },
  performanceBudget: {
    renderTime: 16,
    memoryUsage: 50,
    bundleSize: 500
  }
};
var EnhancedTestingQA = exports.EnhancedTestingQA = (0, _createClass2.default)(function EnhancedTestingQA() {
  var _this = this;
  var config = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  (0, _classCallCheck2.default)(this, EnhancedTestingQA);
  this.renderWithEnhancedProviders = function (ui) {
    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
    var startTime = performance.now();
    var result = (0, _reactNative.render)(ui, options);
    if (options.enablePerformanceMonitoring && _this.config.enablePerformanceTesting) {
      var renderTime = performance.now() - startTime;
      _this.validatePerformance({
        renderTime: renderTime
      });
    }
    if (options.enableAccessibilityChecks && _this.config.enableAccessibilityTesting) {
      _this.validateAccessibility(result.container);
    }
    return result;
  };
  this.validatePerformance = function (metrics) {
    var renderTime = metrics.renderTime,
      memoryUsage = metrics.memoryUsage;
    var budget = _this.config.performanceBudget;
    if (renderTime > budget.renderTime) {
      console.warn(`⚠️ Performance Warning: Render time ${renderTime.toFixed(2)}ms exceeds budget ${budget.renderTime}ms`);
    }
    if (memoryUsage && memoryUsage > budget.memoryUsage) {
      console.warn(`⚠️ Performance Warning: Memory usage ${memoryUsage}MB exceeds budget ${budget.memoryUsage}MB`);
    }
    var renderRatio = renderTime / budget.renderTime;
    var renderScore = Math.max(0, 100 - (renderRatio - 1) * 100);
    var memoryScore = memoryUsage ? Math.max(0, 100 - (memoryUsage / budget.memoryUsage - 1) * 100) : 100;
    _this.metrics.performanceScore = (renderScore + memoryScore) / 2;
  };
  this.validateAccessibility = function (container) {
    var accessibilityIssues = [];
    try {
      var interactiveElements = container.querySelectorAll('button, input, select, textarea, [role="button"]');
      interactiveElements.forEach(function (element, index) {
        var hasLabel = element.accessibilityLabel || element.getAttribute && element.getAttribute('aria-label');
        if (!hasLabel) {
          accessibilityIssues.push(`Interactive element ${index} missing accessibility label`);
        }
      });
      var headings = container.querySelectorAll('h1, h2, h3, h4, h5, h6, [role="heading"]');
      if (headings.length === 0) {
        accessibilityIssues.push('No heading elements found - consider adding semantic headings');
      }
      var maxIssues = Math.max(1, interactiveElements.length);
      _this.metrics.accessibilityScore = Math.max(0, 100 - accessibilityIssues.length / maxIssues * 100);
      if (accessibilityIssues.length > 0) {
        console.warn('♿ Accessibility Issues:', accessibilityIssues);
      }
    } catch (error) {
      console.warn('Accessibility validation failed:', error);
      _this.metrics.accessibilityScore = 0;
    }
    return accessibilityIssues;
  };
  this.validateCodeQuality = function (componentCode) {
    var qualityIssues = [];
    if (componentCode.includes('console.log')) {
      qualityIssues.push('Console.log statements found - remove before production');
    }
    var todoCount = (componentCode.match(/TODO|FIXME|HACK/gi) || []).length;
    if (todoCount > 0) {
      qualityIssues.push(`${todoCount} TODO/FIXME comments found`);
    }
    if (componentCode.includes(': any')) {
      qualityIssues.push('Avoid using "any" type - use specific types instead');
    }
    _this.metrics.codeQualityScore = Math.max(0, 100 - qualityIssues.length * 10);
    return qualityIssues;
  };
  this.createTestDataFactory = function (template) {
    return function () {
      var overrides = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
      return Object.assign({}, template, overrides);
    };
  };
  this.testDataFactories = {
    user: this.createTestDataFactory({
      id: '1',
      firstName: 'Test',
      lastName: 'User',
      email: '<EMAIL>',
      phone: '+**********',
      isActive: true,
      createdAt: new Date().toISOString()
    }),
    service: this.createTestDataFactory({
      id: '1',
      name: 'Test Service',
      description: 'A test service for testing purposes',
      price: 50,
      duration: 60,
      category: 'Test Category',
      isActive: true
    }),
    booking: this.createTestDataFactory({
      id: '1',
      userId: '1',
      serviceId: '1',
      providerId: '1',
      date: new Date().toISOString(),
      status: 'confirmed',
      totalAmount: 50
    }),
    provider: this.createTestDataFactory({
      id: '1',
      businessName: 'Test Provider',
      description: 'A test provider for testing',
      rating: 4.5,
      reviewCount: 10,
      isVerified: true,
      location: {
        address: '123 Test St',
        city: 'Test City',
        state: 'TS',
        zipCode: '12345'
      }
    })
  };
  this.enhancedAssertions = {
    expectToRenderWithoutErrors: function expectToRenderWithoutErrors(component) {
      expect(function () {
        return _this.renderWithEnhancedProviders(component);
      }).not.toThrow();
    },
    expectToBeAccessible: function expectToBeAccessible(component) {
      var _this$renderWithEnhan = _this.renderWithEnhancedProviders(component, {
          enableAccessibilityChecks: true
        }),
        container = _this$renderWithEnhan.container;
      var issues = _this.validateAccessibility(container);
      expect(issues).toHaveLength(0);
    },
    expectToMeetPerformanceBudget: function expectToMeetPerformanceBudget(component) {
      var startTime = performance.now();
      _this.renderWithEnhancedProviders(component, {
        enablePerformanceMonitoring: true
      });
      var renderTime = performance.now() - startTime;
      expect(renderTime).toBeLessThan(_this.config.performanceBudget.renderTime);
    },
    expectToHaveTestCoverage: function expectToHaveTestCoverage(testResults) {
      var _coverage$statements, _coverage$branches, _coverage$functions, _coverage$lines;
      var coverage = testResults.coverage || {};
      expect(((_coverage$statements = coverage.statements) == null ? void 0 : _coverage$statements.pct) || 0).toBeGreaterThanOrEqual(_this.config.coverageThreshold.statements);
      expect(((_coverage$branches = coverage.branches) == null ? void 0 : _coverage$branches.pct) || 0).toBeGreaterThanOrEqual(_this.config.coverageThreshold.branches);
      expect(((_coverage$functions = coverage.functions) == null ? void 0 : _coverage$functions.pct) || 0).toBeGreaterThanOrEqual(_this.config.coverageThreshold.functions);
      expect(((_coverage$lines = coverage.lines) == null ? void 0 : _coverage$lines.pct) || 0).toBeGreaterThanOrEqual(_this.config.coverageThreshold.lines);
    }
  };
  this.testSuiteUtils = {
    createComprehensiveTestSuite: function createComprehensiveTestSuite(componentName, component, testCases) {
      return {
        suiteName: `${componentName} - Comprehensive Test Suite`,
        tests: [{
          name: 'should render without errors',
          test: function test() {
            return _this.enhancedAssertions.expectToRenderWithoutErrors(component);
          }
        }, {
          name: 'should be accessible',
          test: function test() {
            return _this.enhancedAssertions.expectToBeAccessible(component);
          }
        }, {
          name: 'should meet performance budget',
          test: function test() {
            return _this.enhancedAssertions.expectToMeetPerformanceBudget(component);
          }
        }].concat((0, _toConsumableArray2.default)(testCases))
      };
    },
    createIntegrationTestSuite: function createIntegrationTestSuite(suiteName, integrationTests) {
      return {
        suiteName: `${suiteName} - Integration Tests`,
        tests: integrationTests.map(function (_ref) {
          var name = _ref.name,
            test = _ref.test;
          return {
            name: name,
            test: test,
            timeout: _this.config.testTimeout
          };
        })
      };
    }
  };
  this.getMetrics = function () {
    return Object.assign({}, _this.metrics);
  };
  this.generateQualityReport = function () {
    var metrics = _this.getMetrics();
    var overallScore = (metrics.performanceScore + metrics.accessibilityScore + metrics.codeQualityScore) / 3;
    return `
📊 Test Quality Report
=====================
Total Tests: ${metrics.totalTests}
Passing: ${metrics.passingTests}
Failing: ${metrics.failingTests}
Skipped: ${metrics.skippedTests}

📈 Quality Scores
Performance: ${metrics.performanceScore.toFixed(1)}/100
Accessibility: ${metrics.accessibilityScore.toFixed(1)}/100
Code Quality: ${metrics.codeQualityScore.toFixed(1)}/100
Overall: ${overallScore.toFixed(1)}/100

⏱️ Execution Time: ${metrics.testExecutionTime.toFixed(2)}ms
📊 Coverage: ${metrics.coveragePercentage.toFixed(1)}%
    `.trim();
  };
  this.config = Object.assign({}, DEFAULT_ENHANCED_TEST_CONFIG, config);
  this.metrics = {
    totalTests: 0,
    passingTests: 0,
    failingTests: 0,
    skippedTests: 0,
    coveragePercentage: 0,
    performanceScore: 0,
    accessibilityScore: 0,
    codeQualityScore: 0,
    testExecutionTime: 0
  };
});
var enhancedTestingQA = exports.enhancedTestingQA = new EnhancedTestingQA();
var renderWithEnhancedProviders = exports.renderWithEnhancedProviders = enhancedTestingQA.renderWithEnhancedProviders,
  validatePerformance = exports.validatePerformance = enhancedTestingQA.validatePerformance,
  validateAccessibility = exports.validateAccessibility = enhancedTestingQA.validateAccessibility,
  validateCodeQuality = exports.validateCodeQuality = enhancedTestingQA.validateCodeQuality,
  testDataFactories = exports.testDataFactories = enhancedTestingQA.testDataFactories,
  enhancedAssertions = exports.enhancedAssertions = enhancedTestingQA.enhancedAssertions,
  testSuiteUtils = exports.testSuiteUtils = enhancedTestingQA.testSuiteUtils;
var _default = exports.default = enhancedTestingQA;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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