{"version": 3, "names": ["_processColor", "_interopRequireDefault", "require", "processBoxShadow", "rawBoxShadows", "result", "boxShadowList", "parseBoxShadowString", "replace", "rawBoxShadow", "parsedBoxShadow", "offsetX", "offsetY", "value", "arg", "parse<PERSON><PERSON>th", "spreadDistance", "blurRadius", "color", "processColor", "inset", "push", "split", "map", "bS", "trim", "filter", "boxShadow", "keywordDetectedAfterLength", "lengthCount", "args", "processedColor", "length", "argsWithUnitsRegex", "match", "exec", "Number", "isNaN"], "sources": ["processBoxShadow.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict-local\n * @format\n * @oncall react-native\n */\n\nimport type {ProcessedColorValue} from './processColor';\nimport type {BoxShadowValue} from './StyleSheetTypes';\n\nimport processColor from './processColor';\n\nexport type ParsedBoxShadow = {\n  offsetX: number,\n  offsetY: number,\n  color?: ProcessedColorValue,\n  blurRadius?: number,\n  spreadDistance?: number,\n  inset?: boolean,\n};\n\nexport default function processBoxShadow(\n  rawBoxShadows: ?($ReadOnlyArray<BoxShadowValue> | string),\n): Array<ParsedBoxShadow> {\n  const result: Array<ParsedBoxShadow> = [];\n  if (rawBoxShadows == null) {\n    return result;\n  }\n\n  const boxShadowList =\n    typeof rawBoxShadows === 'string'\n      ? parseBoxShadowString(rawBoxShadows.replace(/\\n/g, ' '))\n      : rawBoxShadows;\n\n  for (const rawBoxShadow of boxShadowList) {\n    const parsedBoxShadow: ParsedBoxShadow = {\n      offsetX: 0,\n      offsetY: 0,\n    };\n\n    let value;\n    for (const arg in rawBoxShadow) {\n      switch (arg) {\n        case 'offsetX':\n          value =\n            typeof rawBoxShadow.offsetX === 'string'\n              ? parseLength(rawBoxShadow.offsetX)\n              : rawBoxShadow.offsetX;\n          if (value == null) {\n            return [];\n          }\n\n          parsedBoxShadow.offsetX = value;\n          break;\n        case 'offsetY':\n          value =\n            typeof rawBoxShadow.offsetY === 'string'\n              ? parseLength(rawBoxShadow.offsetY)\n              : rawBoxShadow.offsetY;\n          if (value == null) {\n            return [];\n          }\n\n          parsedBoxShadow.offsetY = value;\n          break;\n        case 'spreadDistance':\n          value =\n            typeof rawBoxShadow.spreadDistance === 'string'\n              ? parseLength(rawBoxShadow.spreadDistance)\n              : rawBoxShadow.spreadDistance;\n          if (value == null) {\n            return [];\n          }\n\n          parsedBoxShadow.spreadDistance = value;\n          break;\n        case 'blurRadius':\n          value =\n            typeof rawBoxShadow.blurRadius === 'string'\n              ? parseLength(rawBoxShadow.blurRadius)\n              : rawBoxShadow.blurRadius;\n          if (value == null || value < 0) {\n            return [];\n          }\n\n          parsedBoxShadow.blurRadius = value;\n          break;\n        case 'color':\n          const color = processColor(rawBoxShadow.color);\n          if (color == null) {\n            return [];\n          }\n\n          parsedBoxShadow.color = color;\n          break;\n        case 'inset':\n          parsedBoxShadow.inset = rawBoxShadow.inset;\n      }\n    }\n    result.push(parsedBoxShadow);\n  }\n  return result;\n}\n\nfunction parseBoxShadowString(rawBoxShadows: string): Array<BoxShadowValue> {\n  let result: Array<BoxShadowValue> = [];\n\n  for (const rawBoxShadow of rawBoxShadows\n    .split(/,(?![^()]*\\))/) // split by comma that is not in parenthesis\n    .map(bS => bS.trim())\n    .filter(bS => bS !== '')) {\n    const boxShadow: BoxShadowValue = {\n      offsetX: 0,\n      offsetY: 0,\n    };\n    let offsetX: number | string;\n    let offsetY: number | string;\n    let keywordDetectedAfterLength = false;\n\n    let lengthCount = 0;\n\n    // split rawBoxShadow string by all whitespaces that are not in parenthesis\n    const args = rawBoxShadow.split(/\\s+(?![^(]*\\))/);\n    for (const arg of args) {\n      const processedColor = processColor(arg);\n      if (processedColor != null) {\n        if (boxShadow.color != null) {\n          return [];\n        }\n        if (offsetX != null) {\n          keywordDetectedAfterLength = true;\n        }\n        boxShadow.color = arg;\n        continue;\n      }\n\n      if (arg === 'inset') {\n        if (boxShadow.inset != null) {\n          return [];\n        }\n        if (offsetX != null) {\n          keywordDetectedAfterLength = true;\n        }\n        boxShadow.inset = true;\n        continue;\n      }\n\n      switch (lengthCount) {\n        case 0:\n          offsetX = arg;\n          lengthCount++;\n          break;\n        case 1:\n          if (keywordDetectedAfterLength) {\n            return [];\n          }\n          offsetY = arg;\n          lengthCount++;\n          break;\n        case 2:\n          if (keywordDetectedAfterLength) {\n            return [];\n          }\n          boxShadow.blurRadius = arg;\n          lengthCount++;\n          break;\n        case 3:\n          if (keywordDetectedAfterLength) {\n            return [];\n          }\n          boxShadow.spreadDistance = arg;\n          lengthCount++;\n          break;\n        default:\n          return [];\n      }\n    }\n\n    if (offsetX == null || offsetY == null) {\n      return [];\n    }\n\n    boxShadow.offsetX = offsetX;\n    boxShadow.offsetY = offsetY;\n\n    result.push(boxShadow);\n  }\n  return result;\n}\n\nfunction parseLength(length: string): ?number {\n  // matches on args with units like \"1.5 5% -80deg\"\n  const argsWithUnitsRegex = /([+-]?\\d*(\\.\\d+)?)([\\w\\W]+)?/g;\n  const match = argsWithUnitsRegex.exec(length);\n\n  if (!match || Number.isNaN(match[1])) {\n    return null;\n  }\n\n  if (match[3] != null && match[3] !== 'px') {\n    return null;\n  }\n\n  if (match[3] == null && match[1] !== '0') {\n    return null;\n  }\n\n  return Number(match[1]);\n}\n"], "mappings": ";;;;;AAcA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AAWe,SAASC,gBAAgBA,CACtCC,aAAyD,EACjC;EACxB,IAAMC,MAA8B,GAAG,EAAE;EACzC,IAAID,aAAa,IAAI,IAAI,EAAE;IACzB,OAAOC,MAAM;EACf;EAEA,IAAMC,aAAa,GACjB,OAAOF,aAAa,KAAK,QAAQ,GAC7BG,oBAAoB,CAACH,aAAa,CAACI,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,GACvDJ,aAAa;EAEnB,KAAK,IAAMK,YAAY,IAAIH,aAAa,EAAE;IACxC,IAAMI,eAAgC,GAAG;MACvCC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE;IACX,CAAC;IAED,IAAIC,KAAK;IACT,KAAK,IAAMC,GAAG,IAAIL,YAAY,EAAE;MAC9B,QAAQK,GAAG;QACT,KAAK,SAAS;UACZD,KAAK,GACH,OAAOJ,YAAY,CAACE,OAAO,KAAK,QAAQ,GACpCI,WAAW,CAACN,YAAY,CAACE,OAAO,CAAC,GACjCF,YAAY,CAACE,OAAO;UAC1B,IAAIE,KAAK,IAAI,IAAI,EAAE;YACjB,OAAO,EAAE;UACX;UAEAH,eAAe,CAACC,OAAO,GAAGE,KAAK;UAC/B;QACF,KAAK,SAAS;UACZA,KAAK,GACH,OAAOJ,YAAY,CAACG,OAAO,KAAK,QAAQ,GACpCG,WAAW,CAACN,YAAY,CAACG,OAAO,CAAC,GACjCH,YAAY,CAACG,OAAO;UAC1B,IAAIC,KAAK,IAAI,IAAI,EAAE;YACjB,OAAO,EAAE;UACX;UAEAH,eAAe,CAACE,OAAO,GAAGC,KAAK;UAC/B;QACF,KAAK,gBAAgB;UACnBA,KAAK,GACH,OAAOJ,YAAY,CAACO,cAAc,KAAK,QAAQ,GAC3CD,WAAW,CAACN,YAAY,CAACO,cAAc,CAAC,GACxCP,YAAY,CAACO,cAAc;UACjC,IAAIH,KAAK,IAAI,IAAI,EAAE;YACjB,OAAO,EAAE;UACX;UAEAH,eAAe,CAACM,cAAc,GAAGH,KAAK;UACtC;QACF,KAAK,YAAY;UACfA,KAAK,GACH,OAAOJ,YAAY,CAACQ,UAAU,KAAK,QAAQ,GACvCF,WAAW,CAACN,YAAY,CAACQ,UAAU,CAAC,GACpCR,YAAY,CAACQ,UAAU;UAC7B,IAAIJ,KAAK,IAAI,IAAI,IAAIA,KAAK,GAAG,CAAC,EAAE;YAC9B,OAAO,EAAE;UACX;UAEAH,eAAe,CAACO,UAAU,GAAGJ,KAAK;UAClC;QACF,KAAK,OAAO;UACV,IAAMK,KAAK,GAAG,IAAAC,qBAAY,EAACV,YAAY,CAACS,KAAK,CAAC;UAC9C,IAAIA,KAAK,IAAI,IAAI,EAAE;YACjB,OAAO,EAAE;UACX;UAEAR,eAAe,CAACQ,KAAK,GAAGA,KAAK;UAC7B;QACF,KAAK,OAAO;UACVR,eAAe,CAACU,KAAK,GAAGX,YAAY,CAACW,KAAK;MAC9C;IACF;IACAf,MAAM,CAACgB,IAAI,CAACX,eAAe,CAAC;EAC9B;EACA,OAAOL,MAAM;AACf;AAEA,SAASE,oBAAoBA,CAACH,aAAqB,EAAyB;EAC1E,IAAIC,MAA6B,GAAG,EAAE;EAEtC,KAAK,IAAMI,YAAY,IAAIL,aAAa,CACrCkB,KAAK,CAAC,eAAe,CAAC,CACtBC,GAAG,CAAC,UAAAC,EAAE;IAAA,OAAIA,EAAE,CAACC,IAAI,CAAC,CAAC;EAAA,EAAC,CACpBC,MAAM,CAAC,UAAAF,EAAE;IAAA,OAAIA,EAAE,KAAK,EAAE;EAAA,EAAC,EAAE;IAC1B,IAAMG,SAAyB,GAAG;MAChChB,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE;IACX,CAAC;IACD,IAAID,OAAwB;IAC5B,IAAIC,OAAwB;IAC5B,IAAIgB,0BAA0B,GAAG,KAAK;IAEtC,IAAIC,WAAW,GAAG,CAAC;IAGnB,IAAMC,IAAI,GAAGrB,YAAY,CAACa,KAAK,CAAC,gBAAgB,CAAC;IACjD,KAAK,IAAMR,GAAG,IAAIgB,IAAI,EAAE;MACtB,IAAMC,cAAc,GAAG,IAAAZ,qBAAY,EAACL,GAAG,CAAC;MACxC,IAAIiB,cAAc,IAAI,IAAI,EAAE;QAC1B,IAAIJ,SAAS,CAACT,KAAK,IAAI,IAAI,EAAE;UAC3B,OAAO,EAAE;QACX;QACA,IAAIP,OAAO,IAAI,IAAI,EAAE;UACnBiB,0BAA0B,GAAG,IAAI;QACnC;QACAD,SAAS,CAACT,KAAK,GAAGJ,GAAG;QACrB;MACF;MAEA,IAAIA,GAAG,KAAK,OAAO,EAAE;QACnB,IAAIa,SAAS,CAACP,KAAK,IAAI,IAAI,EAAE;UAC3B,OAAO,EAAE;QACX;QACA,IAAIT,OAAO,IAAI,IAAI,EAAE;UACnBiB,0BAA0B,GAAG,IAAI;QACnC;QACAD,SAAS,CAACP,KAAK,GAAG,IAAI;QACtB;MACF;MAEA,QAAQS,WAAW;QACjB,KAAK,CAAC;UACJlB,OAAO,GAAGG,GAAG;UACbe,WAAW,EAAE;UACb;QACF,KAAK,CAAC;UACJ,IAAID,0BAA0B,EAAE;YAC9B,OAAO,EAAE;UACX;UACAhB,OAAO,GAAGE,GAAG;UACbe,WAAW,EAAE;UACb;QACF,KAAK,CAAC;UACJ,IAAID,0BAA0B,EAAE;YAC9B,OAAO,EAAE;UACX;UACAD,SAAS,CAACV,UAAU,GAAGH,GAAG;UAC1Be,WAAW,EAAE;UACb;QACF,KAAK,CAAC;UACJ,IAAID,0BAA0B,EAAE;YAC9B,OAAO,EAAE;UACX;UACAD,SAAS,CAACX,cAAc,GAAGF,GAAG;UAC9Be,WAAW,EAAE;UACb;QACF;UACE,OAAO,EAAE;MACb;IACF;IAEA,IAAIlB,OAAO,IAAI,IAAI,IAAIC,OAAO,IAAI,IAAI,EAAE;MACtC,OAAO,EAAE;IACX;IAEAe,SAAS,CAAChB,OAAO,GAAGA,OAAO;IAC3BgB,SAAS,CAACf,OAAO,GAAGA,OAAO;IAE3BP,MAAM,CAACgB,IAAI,CAACM,SAAS,CAAC;EACxB;EACA,OAAOtB,MAAM;AACf;AAEA,SAASU,WAAWA,CAACiB,MAAc,EAAW;EAE5C,IAAMC,kBAAkB,GAAG,+BAA+B;EAC1D,IAAMC,KAAK,GAAGD,kBAAkB,CAACE,IAAI,CAACH,MAAM,CAAC;EAE7C,IAAI,CAACE,KAAK,IAAIE,MAAM,CAACC,KAAK,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;IACpC,OAAO,IAAI;EACb;EAEA,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,IAAIA,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;IACzC,OAAO,IAAI;EACb;EAEA,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,IAAIA,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACxC,OAAO,IAAI;EACb;EAEA,OAAOE,MAAM,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC;AACzB", "ignoreList": []}