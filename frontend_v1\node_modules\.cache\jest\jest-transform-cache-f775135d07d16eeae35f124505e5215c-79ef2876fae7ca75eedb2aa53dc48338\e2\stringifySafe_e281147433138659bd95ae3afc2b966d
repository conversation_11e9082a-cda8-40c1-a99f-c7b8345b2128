255e94e34141437475fd2145430e3955
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.createStringifySafeWithLimits = createStringifySafeWithLimits;
exports.default = void 0;
var _invariant = _interopRequireDefault(require("invariant"));
function createStringifySafeWithLimits(limits) {
  var _limits$maxDepth = limits.maxDepth,
    maxDepth = _limits$maxDepth === void 0 ? Number.POSITIVE_INFINITY : _limits$maxDepth,
    _limits$maxStringLimi = limits.maxStringLimit,
    maxStringLimit = _limits$maxStringLimi === void 0 ? Number.POSITIVE_INFINITY : _limits$maxStringLimi,
    _limits$maxArrayLimit = limits.maxArrayLimit,
    maxArrayLimit = _limits$maxArrayLimit === void 0 ? Number.POSITIVE_INFINITY : _limits$maxArrayLimit,
    _limits$maxObjectKeys = limits.maxObjectKeysLimit,
    maxObjectKeysLimit = _limits$maxObjectKeys === void 0 ? Number.POSITIVE_INFINITY : _limits$maxObjectKeys;
  var stack = [];
  function replacer(key, value) {
    while (stack.length && this !== stack[0]) {
      stack.shift();
    }
    if (typeof value === 'string') {
      var truncatedString = '...(truncated)...';
      if (value.length > maxStringLimit + truncatedString.length) {
        return value.substring(0, maxStringLimit) + truncatedString;
      }
      return value;
    }
    if (typeof value !== 'object' || value === null) {
      return value;
    }
    var retval = value;
    if (Array.isArray(value)) {
      if (stack.length >= maxDepth) {
        retval = `[ ... array with ${value.length} values ... ]`;
      } else if (value.length > maxArrayLimit) {
        retval = value.slice(0, maxArrayLimit).concat([`... extra ${value.length - maxArrayLimit} values truncated ...`]);
      }
    } else {
      (0, _invariant.default)(typeof value === 'object', 'This was already found earlier');
      var keys = Object.keys(value);
      if (stack.length >= maxDepth) {
        retval = `{ ... object with ${keys.length} keys ... }`;
      } else if (keys.length > maxObjectKeysLimit) {
        retval = {};
        for (var k of keys.slice(0, maxObjectKeysLimit)) {
          retval[k] = value[k];
        }
        var truncatedKey = '...(truncated keys)...';
        retval[truncatedKey] = keys.length - maxObjectKeysLimit;
      }
    }
    stack.unshift(retval);
    return retval;
  }
  return function stringifySafe(arg) {
    if (arg === undefined) {
      return 'undefined';
    } else if (arg === null) {
      return 'null';
    } else if (typeof arg === 'function') {
      try {
        return arg.toString();
      } catch (e) {
        return '[function unknown]';
      }
    } else if (arg instanceof Error) {
      return arg.name + ': ' + arg.message;
    } else {
      try {
        var ret = JSON.stringify(arg, replacer);
        if (ret === undefined) {
          return '["' + typeof arg + '" failed to stringify]';
        }
        return ret;
      } catch (e) {
        if (typeof arg.toString === 'function') {
          try {
            return arg.toString();
          } catch (E) {}
        }
      }
    }
    return '["' + typeof arg + '" failed to stringify]';
  };
}
var stringifySafe = createStringifySafeWithLimits({
  maxDepth: 10,
  maxStringLimit: 100,
  maxArrayLimit: 50,
  maxObjectKeysLimit: 50
});
var _default = exports.default = stringifySafe;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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