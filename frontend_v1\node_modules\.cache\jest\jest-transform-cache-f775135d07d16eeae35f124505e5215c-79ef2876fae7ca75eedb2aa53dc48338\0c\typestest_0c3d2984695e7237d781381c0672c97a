fe983d8fae2f7f9a3d96ce9a0518cfc7
Object.defineProperty(exports, "__esModule", {
  value: true
});
describe('Navigation Types', function () {
  describe('Type Definitions', function () {
    it('should define RootStackParamList correctly', function () {
      var mockParams = {
        Auth: undefined,
        CustomerTabs: undefined,
        ProviderTabs: undefined
      };
      expect(mockParams).toBeDefined();
      expect(mockParams.Auth).toBeUndefined();
      expect(mockParams.CustomerTabs).toBeUndefined();
      expect(mockParams.ProviderTabs).toBeUndefined();
    });
    it('should define AuthStackParamList correctly', function () {
      var mockParams = {
        Login: undefined,
        Register: undefined
      };
      expect(mockParams).toBeDefined();
      expect(mockParams.Login).toBeUndefined();
      expect(mockParams.Register).toBeUndefined();
    });
    it('should define CustomerTabParamList correctly', function () {
      var mockParams = {
        Home: undefined,
        Search: undefined,
        Bookings: undefined,
        Messages: undefined,
        Profile: undefined
      };
      expect(mockParams).toBeDefined();
      expect(mockParams.Home).toBeUndefined();
      expect(mockParams.Search).toBeUndefined();
      expect(mockParams.Bookings).toBeUndefined();
      expect(mockParams.Messages).toBeUndefined();
      expect(mockParams.Profile).toBeUndefined();
    });
    it('should define ProviderTabParamList correctly', function () {
      var mockParams = {
        Dashboard: undefined,
        Services: undefined,
        Bookings: undefined,
        Messages: undefined,
        Profile: undefined
      };
      expect(mockParams).toBeDefined();
      expect(mockParams.Dashboard).toBeUndefined();
      expect(mockParams.Services).toBeUndefined();
      expect(mockParams.Bookings).toBeUndefined();
      expect(mockParams.Messages).toBeUndefined();
      expect(mockParams.Profile).toBeUndefined();
    });
  });
  describe('Type Safety', function () {
    it('should enforce correct parameter types', function () {
      var rootKeys = ['Auth', 'CustomerTabs', 'ProviderTabs'];
      var authKeys = ['Login', 'Register'];
      var customerKeys = ['Home', 'Search', 'Bookings', 'Messages', 'Profile'];
      var providerKeys = ['Dashboard', 'Services', 'Bookings', 'Messages', 'Profile'];
      expect(rootKeys).toHaveLength(3);
      expect(authKeys).toHaveLength(2);
      expect(customerKeys).toHaveLength(5);
      expect(providerKeys).toHaveLength(5);
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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