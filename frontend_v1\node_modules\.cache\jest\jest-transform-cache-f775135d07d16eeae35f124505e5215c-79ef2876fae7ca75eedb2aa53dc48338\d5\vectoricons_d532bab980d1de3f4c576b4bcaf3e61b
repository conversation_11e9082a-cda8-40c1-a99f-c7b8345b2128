afb886baf75b948223f682c1e4453dac
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.Zocial = exports.SimpleLineIcons = exports.Octicons = exports.MaterialIcons = exports.MaterialCommunityIcons = exports.Ionicons = exports.Foundation = exports.FontAwesome = exports.Feather = exports.EvilIcons = exports.Entypo = exports.AntDesign = void 0;
var React = require('react');
var _require = require('react-native'),
  Text = _require.Text;
var createIconComponent = function createIconComponent(name) {
  return React.forwardRef(function (props, ref) {
    return React.createElement(Text, Object.assign({}, props, {
      ref: ref,
      testID: props.testID || `icon-${name}`,
      accessibilityLabel: props.accessibilityLabel || `${name} icon`
    }), props.name || name);
  });
};
var Ionicons = exports.Ionicons = createIconComponent('Ionicons');
var MaterialIcons = exports.MaterialIcons = createIconComponent('MaterialIcons');
var FontAwesome = exports.FontAwesome = createIconComponent('FontAwesome');
var Entypo = exports.Entypo = createIconComponent('Entypo');
var AntDesign = exports.AntDesign = createIconComponent('AntDesign');
var MaterialCommunityIcons = exports.MaterialCommunityIcons = createIconComponent('MaterialCommunityIcons');
var Feather = exports.Feather = createIconComponent('Feather');
var Foundation = exports.Foundation = createIconComponent('Foundation');
var EvilIcons = exports.EvilIcons = createIconComponent('EvilIcons');
var Octicons = exports.Octicons = createIconComponent('Octicons');
var SimpleLineIcons = exports.SimpleLineIcons = createIconComponent('SimpleLineIcons');
var Zocial = exports.Zocial = createIconComponent('Zocial');
var _default = exports.default = {
  Ionicons: Ionicons,
  MaterialIcons: MaterialIcons,
  FontAwesome: FontAwesome,
  Entypo: Entypo,
  AntDesign: AntDesign,
  MaterialCommunityIcons: MaterialCommunityIcons,
  Feather: Feather,
  Foundation: Foundation,
  EvilIcons: EvilIcons,
  Octicons: Octicons,
  SimpleLineIcons: SimpleLineIcons,
  Zocial: Zocial
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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