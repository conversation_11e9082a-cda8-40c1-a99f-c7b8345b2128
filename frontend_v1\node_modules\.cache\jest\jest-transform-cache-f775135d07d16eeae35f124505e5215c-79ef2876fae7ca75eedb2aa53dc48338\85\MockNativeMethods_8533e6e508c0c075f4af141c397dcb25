05c5995cd71ca0d75c50ee3a5e0c8a17
'use strict';

var MockNativeMethods = {
  measure: jest.fn(),
  measureInWindow: jest.fn(),
  measureLayout: jest.fn(),
  setNativeProps: jest.fn(),
  focus: jest.fn(),
  blur: jest.fn()
};
module.exports = MockNativeMethods;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJNb2NrTmF0aXZlTWV0aG9kcyIsIm1lYXN1cmUiLCJqZXN0IiwiZm4iLCJtZWFzdXJlSW5XaW5kb3ciLCJtZWFzdXJlTGF5b3V0Iiwic2V0TmF0aXZlUHJvcHMiLCJmb2N1cyIsImJsdXIiLCJtb2R1bGUiLCJleHBvcnRzIl0sInNvdXJjZXMiOlsiTW9ja05hdGl2ZU1ldGhvZHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBDb3B5cmlnaHQgKGMpIE1ldGEgUGxhdGZvcm1zLCBJbmMuIGFuZCBhZmZpbGlhdGVzLlxuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlIGZvdW5kIGluIHRoZVxuICogTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICpcbiAqIEBmb3JtYXRcbiAqL1xuXG4ndXNlIHN0cmljdCc7XG5cbmNvbnN0IE1vY2tOYXRpdmVNZXRob2RzID0ge1xuICBtZWFzdXJlOiBqZXN0LmZuKCksXG4gIG1lYXN1cmVJbldpbmRvdzogamVzdC5mbigpLFxuICBtZWFzdXJlTGF5b3V0OiBqZXN0LmZuKCksXG4gIHNldE5hdGl2ZVByb3BzOiBqZXN0LmZuKCksXG4gIGZvY3VzOiBqZXN0LmZuKCksXG4gIGJsdXI6IGplc3QuZm4oKSxcbn07XG5cbm1vZHVsZS5leHBvcnRzID0gTW9ja05hdGl2ZU1ldGhvZHM7XG4iXSwibWFwcGluZ3MiOiJBQVNBLFlBQVk7O0FBRVosSUFBTUEsaUJBQWlCLEdBQUc7RUFDeEJDLE9BQU8sRUFBRUMsSUFBSSxDQUFDQyxFQUFFLENBQUMsQ0FBQztFQUNsQkMsZUFBZSxFQUFFRixJQUFJLENBQUNDLEVBQUUsQ0FBQyxDQUFDO0VBQzFCRSxhQUFhLEVBQUVILElBQUksQ0FBQ0MsRUFBRSxDQUFDLENBQUM7RUFDeEJHLGNBQWMsRUFBRUosSUFBSSxDQUFDQyxFQUFFLENBQUMsQ0FBQztFQUN6QkksS0FBSyxFQUFFTCxJQUFJLENBQUNDLEVBQUUsQ0FBQyxDQUFDO0VBQ2hCSyxJQUFJLEVBQUVOLElBQUksQ0FBQ0MsRUFBRSxDQUFDO0FBQ2hCLENBQUM7QUFFRE0sTUFBTSxDQUFDQyxPQUFPLEdBQUdWLGlCQUFpQiIsImlnbm9yZUxpc3QiOltdfQ==