f62f1115ea0d9449bb960317af4eecc1
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _get2 = _interopRequireDefault(require("@babel/runtime/helpers/get"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _AnimatedInterpolation = _interopRequireDefault(require("./AnimatedInterpolation"));
var _AnimatedValue = _interopRequireDefault(require("./AnimatedValue"));
var _AnimatedWithChildren2 = _interopRequireDefault(require("./AnimatedWithChildren"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && "function" == typeof p ? function (t) { return p.apply(e, t); } : p; }
var AnimatedMultiplication = exports.default = function (_AnimatedWithChildren) {
  function AnimatedMultiplication(a, b, config) {
    var _this;
    (0, _classCallCheck2.default)(this, AnimatedMultiplication);
    _this = _callSuper(this, AnimatedMultiplication, [config]);
    _this._a = typeof a === 'number' ? new _AnimatedValue.default(a) : a;
    _this._b = typeof b === 'number' ? new _AnimatedValue.default(b) : b;
    return _this;
  }
  (0, _inherits2.default)(AnimatedMultiplication, _AnimatedWithChildren);
  return (0, _createClass2.default)(AnimatedMultiplication, [{
    key: "__makeNative",
    value: function __makeNative(platformConfig) {
      this._a.__makeNative(platformConfig);
      this._b.__makeNative(platformConfig);
      _superPropGet(AnimatedMultiplication, "__makeNative", this, 3)([platformConfig]);
    }
  }, {
    key: "__getValue",
    value: function __getValue() {
      return this._a.__getValue() * this._b.__getValue();
    }
  }, {
    key: "interpolate",
    value: function interpolate(config) {
      return new _AnimatedInterpolation.default(this, config);
    }
  }, {
    key: "__attach",
    value: function __attach() {
      this._a.__addChild(this);
      this._b.__addChild(this);
      _superPropGet(AnimatedMultiplication, "__attach", this, 3)([]);
    }
  }, {
    key: "__detach",
    value: function __detach() {
      this._a.__removeChild(this);
      this._b.__removeChild(this);
      _superPropGet(AnimatedMultiplication, "__detach", this, 3)([]);
    }
  }, {
    key: "__getNativeConfig",
    value: function __getNativeConfig() {
      return {
        type: 'multiplication',
        input: [this._a.__getNativeTag(), this._b.__getNativeTag()],
        debugID: this.__getDebugID()
      };
    }
  }]);
}(_AnimatedWithChildren2.default);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfaW50ZXJvcFJlcXVpcmVEZWZhdWx0IiwicmVxdWlyZSIsIk9iamVjdCIsImRlZmluZVByb3BlcnR5IiwiZXhwb3J0cyIsInZhbHVlIiwiZGVmYXVsdCIsIl9jbGFzc0NhbGxDaGVjazIiLCJfY3JlYXRlQ2xhc3MyIiwiX3Bvc3NpYmxlQ29uc3RydWN0b3JSZXR1cm4yIiwiX2dldFByb3RvdHlwZU9mMiIsIl9nZXQyIiwiX2luaGVyaXRzMiIsIl9BbmltYXRlZEludGVycG9sYXRpb24iLCJfQW5pbWF0ZWRWYWx1ZSIsIl9BbmltYXRlZFdpdGhDaGlsZHJlbjIiLCJfY2FsbFN1cGVyIiwidCIsIm8iLCJlIiwiX2lzTmF0aXZlUmVmbGVjdENvbnN0cnVjdCIsIlJlZmxlY3QiLCJjb25zdHJ1Y3QiLCJjb25zdHJ1Y3RvciIsImFwcGx5IiwiQm9vbGVhbiIsInByb3RvdHlwZSIsInZhbHVlT2YiLCJjYWxsIiwiX3N1cGVyUHJvcEdldCIsInIiLCJwIiwiQW5pbWF0ZWRNdWx0aXBsaWNhdGlvbiIsIl9BbmltYXRlZFdpdGhDaGlsZHJlbiIsImEiLCJiIiwiY29uZmlnIiwiX3RoaXMiLCJfYSIsIkFuaW1hdGVkVmFsdWUiLCJfYiIsImtleSIsIl9fbWFrZU5hdGl2ZSIsInBsYXRmb3JtQ29uZmlnIiwiX19nZXRWYWx1ZSIsImludGVycG9sYXRlIiwiQW5pbWF0ZWRJbnRlcnBvbGF0aW9uIiwiX19hdHRhY2giLCJfX2FkZENoaWxkIiwiX19kZXRhY2giLCJfX3JlbW92ZUNoaWxkIiwiX19nZXROYXRpdmVDb25maWciLCJ0eXBlIiwiaW5wdXQiLCJfX2dldE5hdGl2ZVRhZyIsImRlYnVnSUQiLCJfX2dldERlYnVnSUQiLCJBbmltYXRlZFdpdGhDaGlsZHJlbiJdLCJzb3VyY2VzIjpbIkFuaW1hdGVkTXVsdGlwbGljYXRpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBDb3B5cmlnaHQgKGMpIE1ldGEgUGxhdGZvcm1zLCBJbmMuIGFuZCBhZmZpbGlhdGVzLlxuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlIGZvdW5kIGluIHRoZVxuICogTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICpcbiAqIEBmbG93XG4gKiBAZm9ybWF0XG4gKi9cblxuJ3VzZSBzdHJpY3QnO1xuXG5pbXBvcnQgdHlwZSB7UGxhdGZvcm1Db25maWd9IGZyb20gJy4uL0FuaW1hdGVkUGxhdGZvcm1Db25maWcnO1xuaW1wb3J0IHR5cGUge0ludGVycG9sYXRpb25Db25maWdUeXBlfSBmcm9tICcuL0FuaW1hdGVkSW50ZXJwb2xhdGlvbic7XG5pbXBvcnQgdHlwZSBBbmltYXRlZE5vZGUgZnJvbSAnLi9BbmltYXRlZE5vZGUnO1xuaW1wb3J0IHR5cGUge0FuaW1hdGVkTm9kZUNvbmZpZ30gZnJvbSAnLi9BbmltYXRlZE5vZGUnO1xuXG5pbXBvcnQgQW5pbWF0ZWRJbnRlcnBvbGF0aW9uIGZyb20gJy4vQW5pbWF0ZWRJbnRlcnBvbGF0aW9uJztcbmltcG9ydCBBbmltYXRlZFZhbHVlIGZyb20gJy4vQW5pbWF0ZWRWYWx1ZSc7XG5pbXBvcnQgQW5pbWF0ZWRXaXRoQ2hpbGRyZW4gZnJvbSAnLi9BbmltYXRlZFdpdGhDaGlsZHJlbic7XG5cbmV4cG9ydCBkZWZhdWx0IGNsYXNzIEFuaW1hdGVkTXVsdGlwbGljYXRpb24gZXh0ZW5kcyBBbmltYXRlZFdpdGhDaGlsZHJlbiB7XG4gIF9hOiBBbmltYXRlZE5vZGU7XG4gIF9iOiBBbmltYXRlZE5vZGU7XG5cbiAgY29uc3RydWN0b3IoXG4gICAgYTogQW5pbWF0ZWROb2RlIHwgbnVtYmVyLFxuICAgIGI6IEFuaW1hdGVkTm9kZSB8IG51bWJlcixcbiAgICBjb25maWc/OiA/QW5pbWF0ZWROb2RlQ29uZmlnLFxuICApIHtcbiAgICBzdXBlcihjb25maWcpO1xuICAgIHRoaXMuX2EgPSB0eXBlb2YgYSA9PT0gJ251bWJlcicgPyBuZXcgQW5pbWF0ZWRWYWx1ZShhKSA6IGE7XG4gICAgdGhpcy5fYiA9IHR5cGVvZiBiID09PSAnbnVtYmVyJyA/IG5ldyBBbmltYXRlZFZhbHVlKGIpIDogYjtcbiAgfVxuXG4gIF9fbWFrZU5hdGl2ZShwbGF0Zm9ybUNvbmZpZzogP1BsYXRmb3JtQ29uZmlnKSB7XG4gICAgdGhpcy5fYS5fX21ha2VOYXRpdmUocGxhdGZvcm1Db25maWcpO1xuICAgIHRoaXMuX2IuX19tYWtlTmF0aXZlKHBsYXRmb3JtQ29uZmlnKTtcbiAgICBzdXBlci5fX21ha2VOYXRpdmUocGxhdGZvcm1Db25maWcpO1xuICB9XG5cbiAgX19nZXRWYWx1ZSgpOiBudW1iZXIge1xuICAgIHJldHVybiB0aGlzLl9hLl9fZ2V0VmFsdWUoKSAqIHRoaXMuX2IuX19nZXRWYWx1ZSgpO1xuICB9XG5cbiAgaW50ZXJwb2xhdGU8T3V0cHV0VDogbnVtYmVyIHwgc3RyaW5nPihcbiAgICBjb25maWc6IEludGVycG9sYXRpb25Db25maWdUeXBlPE91dHB1dFQ+LFxuICApOiBBbmltYXRlZEludGVycG9sYXRpb248T3V0cHV0VD4ge1xuICAgIHJldHVybiBuZXcgQW5pbWF0ZWRJbnRlcnBvbGF0aW9uKHRoaXMsIGNvbmZpZyk7XG4gIH1cbiAgX19hdHRhY2goKTogdm9pZCB7XG4gICAgdGhpcy5fYS5fX2FkZENoaWxkKHRoaXMpO1xuICAgIHRoaXMuX2IuX19hZGRDaGlsZCh0aGlzKTtcbiAgICBzdXBlci5fX2F0dGFjaCgpO1xuICB9XG5cbiAgX19kZXRhY2goKTogdm9pZCB7XG4gICAgdGhpcy5fYS5fX3JlbW92ZUNoaWxkKHRoaXMpO1xuICAgIHRoaXMuX2IuX19yZW1vdmVDaGlsZCh0aGlzKTtcbiAgICBzdXBlci5fX2RldGFjaCgpO1xuICB9XG5cbiAgX19nZXROYXRpdmVDb25maWcoKTogYW55IHtcbiAgICByZXR1cm4ge1xuICAgICAgdHlwZTogJ211bHRpcGxpY2F0aW9uJyxcbiAgICAgIGlucHV0OiBbdGhpcy5fYS5fX2dldE5hdGl2ZVRhZygpLCB0aGlzLl9iLl9fZ2V0TmF0aXZlVGFnKCldLFxuICAgICAgZGVidWdJRDogdGhpcy5fX2dldERlYnVnSUQoKSxcbiAgICB9O1xuICB9XG59XG4iXSwibWFwcGluZ3MiOiJBQVVBLFlBQVk7O0FBQUMsSUFBQUEsc0JBQUEsR0FBQUMsT0FBQTtBQUFBQyxNQUFBLENBQUFDLGNBQUEsQ0FBQUMsT0FBQTtFQUFBQyxLQUFBO0FBQUE7QUFBQUQsT0FBQSxDQUFBRSxPQUFBO0FBQUEsSUFBQUMsZ0JBQUEsR0FBQVAsc0JBQUEsQ0FBQUMsT0FBQTtBQUFBLElBQUFPLGFBQUEsR0FBQVIsc0JBQUEsQ0FBQUMsT0FBQTtBQUFBLElBQUFRLDJCQUFBLEdBQUFULHNCQUFBLENBQUFDLE9BQUE7QUFBQSxJQUFBUyxnQkFBQSxHQUFBVixzQkFBQSxDQUFBQyxPQUFBO0FBQUEsSUFBQVUsS0FBQSxHQUFBWCxzQkFBQSxDQUFBQyxPQUFBO0FBQUEsSUFBQVcsVUFBQSxHQUFBWixzQkFBQSxDQUFBQyxPQUFBO0FBT2IsSUFBQVksc0JBQUEsR0FBQWIsc0JBQUEsQ0FBQUMsT0FBQTtBQUNBLElBQUFhLGNBQUEsR0FBQWQsc0JBQUEsQ0FBQUMsT0FBQTtBQUNBLElBQUFjLHNCQUFBLEdBQUFmLHNCQUFBLENBQUFDLE9BQUE7QUFBMEQsU0FBQWUsV0FBQUMsQ0FBQSxFQUFBQyxDQUFBLEVBQUFDLENBQUEsV0FBQUQsQ0FBQSxPQUFBUixnQkFBQSxDQUFBSixPQUFBLEVBQUFZLENBQUEsT0FBQVQsMkJBQUEsQ0FBQUgsT0FBQSxFQUFBVyxDQUFBLEVBQUFHLHlCQUFBLEtBQUFDLE9BQUEsQ0FBQUMsU0FBQSxDQUFBSixDQUFBLEVBQUFDLENBQUEsWUFBQVQsZ0JBQUEsQ0FBQUosT0FBQSxFQUFBVyxDQUFBLEVBQUFNLFdBQUEsSUFBQUwsQ0FBQSxDQUFBTSxLQUFBLENBQUFQLENBQUEsRUFBQUUsQ0FBQTtBQUFBLFNBQUFDLDBCQUFBLGNBQUFILENBQUEsSUFBQVEsT0FBQSxDQUFBQyxTQUFBLENBQUFDLE9BQUEsQ0FBQUMsSUFBQSxDQUFBUCxPQUFBLENBQUFDLFNBQUEsQ0FBQUcsT0FBQSxpQ0FBQVIsQ0FBQSxhQUFBRyx5QkFBQSxZQUFBQSwwQkFBQSxhQUFBSCxDQUFBO0FBQUEsU0FBQVksY0FBQVosQ0FBQSxFQUFBQyxDQUFBLEVBQUFDLENBQUEsRUFBQVcsQ0FBQSxRQUFBQyxDQUFBLE9BQUFwQixLQUFBLENBQUFMLE9BQUEsTUFBQUksZ0JBQUEsQ0FBQUosT0FBQSxNQUFBd0IsQ0FBQSxHQUFBYixDQUFBLENBQUFTLFNBQUEsR0FBQVQsQ0FBQSxHQUFBQyxDQUFBLEVBQUFDLENBQUEsY0FBQVcsQ0FBQSx5QkFBQUMsQ0FBQSxhQUFBZCxDQUFBLFdBQUFjLENBQUEsQ0FBQVAsS0FBQSxDQUFBTCxDQUFBLEVBQUFGLENBQUEsT0FBQWMsQ0FBQTtBQUFBLElBRXJDQyxzQkFBc0IsR0FBQTVCLE9BQUEsQ0FBQUUsT0FBQSxhQUFBMkIscUJBQUE7RUFJekMsU0FBQUQsdUJBQ0VFLENBQXdCLEVBQ3hCQyxDQUF3QixFQUN4QkMsTUFBNEIsRUFDNUI7SUFBQSxJQUFBQyxLQUFBO0lBQUEsSUFBQTlCLGdCQUFBLENBQUFELE9BQUEsUUFBQTBCLHNCQUFBO0lBQ0FLLEtBQUEsR0FBQXJCLFVBQUEsT0FBQWdCLHNCQUFBLEdBQU1JLE1BQU07SUFDWkMsS0FBQSxDQUFLQyxFQUFFLEdBQUcsT0FBT0osQ0FBQyxLQUFLLFFBQVEsR0FBRyxJQUFJSyxzQkFBYSxDQUFDTCxDQUFDLENBQUMsR0FBR0EsQ0FBQztJQUMxREcsS0FBQSxDQUFLRyxFQUFFLEdBQUcsT0FBT0wsQ0FBQyxLQUFLLFFBQVEsR0FBRyxJQUFJSSxzQkFBYSxDQUFDSixDQUFDLENBQUMsR0FBR0EsQ0FBQztJQUFDLE9BQUFFLEtBQUE7RUFDN0Q7RUFBQyxJQUFBekIsVUFBQSxDQUFBTixPQUFBLEVBQUEwQixzQkFBQSxFQUFBQyxxQkFBQTtFQUFBLFdBQUF6QixhQUFBLENBQUFGLE9BQUEsRUFBQTBCLHNCQUFBO0lBQUFTLEdBQUE7SUFBQXBDLEtBQUEsRUFFRCxTQUFBcUMsWUFBWUEsQ0FBQ0MsY0FBK0IsRUFBRTtNQUM1QyxJQUFJLENBQUNMLEVBQUUsQ0FBQ0ksWUFBWSxDQUFDQyxjQUFjLENBQUM7TUFDcEMsSUFBSSxDQUFDSCxFQUFFLENBQUNFLFlBQVksQ0FBQ0MsY0FBYyxDQUFDO01BQ3BDZCxhQUFBLENBQUFHLHNCQUFBLDRCQUFtQlcsY0FBYztJQUNuQztFQUFDO0lBQUFGLEdBQUE7SUFBQXBDLEtBQUEsRUFFRCxTQUFBdUMsVUFBVUEsQ0FBQSxFQUFXO01BQ25CLE9BQU8sSUFBSSxDQUFDTixFQUFFLENBQUNNLFVBQVUsQ0FBQyxDQUFDLEdBQUcsSUFBSSxDQUFDSixFQUFFLENBQUNJLFVBQVUsQ0FBQyxDQUFDO0lBQ3BEO0VBQUM7SUFBQUgsR0FBQTtJQUFBcEMsS0FBQSxFQUVELFNBQUF3QyxXQUFXQSxDQUNUVCxNQUF3QyxFQUNSO01BQ2hDLE9BQU8sSUFBSVUsOEJBQXFCLENBQUMsSUFBSSxFQUFFVixNQUFNLENBQUM7SUFDaEQ7RUFBQztJQUFBSyxHQUFBO0lBQUFwQyxLQUFBLEVBQ0QsU0FBQTBDLFFBQVFBLENBQUEsRUFBUztNQUNmLElBQUksQ0FBQ1QsRUFBRSxDQUFDVSxVQUFVLENBQUMsSUFBSSxDQUFDO01BQ3hCLElBQUksQ0FBQ1IsRUFBRSxDQUFDUSxVQUFVLENBQUMsSUFBSSxDQUFDO01BQ3hCbkIsYUFBQSxDQUFBRyxzQkFBQTtJQUNGO0VBQUM7SUFBQVMsR0FBQTtJQUFBcEMsS0FBQSxFQUVELFNBQUE0QyxRQUFRQSxDQUFBLEVBQVM7TUFDZixJQUFJLENBQUNYLEVBQUUsQ0FBQ1ksYUFBYSxDQUFDLElBQUksQ0FBQztNQUMzQixJQUFJLENBQUNWLEVBQUUsQ0FBQ1UsYUFBYSxDQUFDLElBQUksQ0FBQztNQUMzQnJCLGFBQUEsQ0FBQUcsc0JBQUE7SUFDRjtFQUFDO0lBQUFTLEdBQUE7SUFBQXBDLEtBQUEsRUFFRCxTQUFBOEMsaUJBQWlCQSxDQUFBLEVBQVE7TUFDdkIsT0FBTztRQUNMQyxJQUFJLEVBQUUsZ0JBQWdCO1FBQ3RCQyxLQUFLLEVBQUUsQ0FBQyxJQUFJLENBQUNmLEVBQUUsQ0FBQ2dCLGNBQWMsQ0FBQyxDQUFDLEVBQUUsSUFBSSxDQUFDZCxFQUFFLENBQUNjLGNBQWMsQ0FBQyxDQUFDLENBQUM7UUFDM0RDLE9BQU8sRUFBRSxJQUFJLENBQUNDLFlBQVksQ0FBQztNQUM3QixDQUFDO0lBQ0g7RUFBQztBQUFBLEVBL0NpREMsOEJBQW9CIiwiaWdub3JlTGlzdCI6W119