8ec0c4155e0870a98e2e45a93573661b
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _toConsumableArray2 = _interopRequireDefault(require("@babel/runtime/helpers/toConsumableArray"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _classPrivateFieldLooseBase2 = _interopRequireDefault(require("@babel/runtime/helpers/classPrivateFieldLooseBase"));
var _classPrivateFieldLooseKey2 = _interopRequireDefault(require("@babel/runtime/helpers/classPrivateFieldLooseKey"));
var _NativeAnimatedHelper = _interopRequireDefault(require("../../../src/private/animated/NativeAnimatedHelper"));
var ReactNativeFeatureFlags = _interopRequireWildcard(require("../../../src/private/featureflags/ReactNativeFeatureFlags"));
var _AnimatedProps = _interopRequireDefault(require("../nodes/AnimatedProps"));
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var startNativeAnimationNextId = 1;
var _nativeID = (0, _classPrivateFieldLooseKey2.default)("nativeID");
var _onEnd = (0, _classPrivateFieldLooseKey2.default)("onEnd");
var _useNativeDriver = (0, _classPrivateFieldLooseKey2.default)("useNativeDriver");
var Animation = exports.default = function () {
  function Animation(config) {
    var _config$isInteraction, _config$iterations;
    (0, _classCallCheck2.default)(this, Animation);
    Object.defineProperty(this, _nativeID, {
      writable: true,
      value: void 0
    });
    Object.defineProperty(this, _onEnd, {
      writable: true,
      value: void 0
    });
    Object.defineProperty(this, _useNativeDriver, {
      writable: true,
      value: void 0
    });
    (0, _classPrivateFieldLooseBase2.default)(this, _useNativeDriver)[_useNativeDriver] = _NativeAnimatedHelper.default.shouldUseNativeDriver(config);
    this.__active = false;
    this.__isInteraction = (_config$isInteraction = config.isInteraction) != null ? _config$isInteraction : !(0, _classPrivateFieldLooseBase2.default)(this, _useNativeDriver)[_useNativeDriver];
    this.__isLooping = config.isLooping;
    this.__iterations = (_config$iterations = config.iterations) != null ? _config$iterations : 1;
    if (__DEV__) {
      this.__debugID = config.debugID;
    }
  }
  return (0, _createClass2.default)(Animation, [{
    key: "start",
    value: function start(fromValue, onUpdate, onEnd, previousAnimation, animatedValue) {
      if (!(0, _classPrivateFieldLooseBase2.default)(this, _useNativeDriver)[_useNativeDriver] && animatedValue.__isNative === true) {
        throw new Error('Attempting to run JS driven animation on animated node ' + 'that has been moved to "native" earlier by starting an ' + 'animation with `useNativeDriver: true`');
      }
      (0, _classPrivateFieldLooseBase2.default)(this, _onEnd)[_onEnd] = onEnd;
      this.__active = true;
    }
  }, {
    key: "stop",
    value: function stop() {
      if ((0, _classPrivateFieldLooseBase2.default)(this, _nativeID)[_nativeID] != null) {
        var nativeID = (0, _classPrivateFieldLooseBase2.default)(this, _nativeID)[_nativeID];
        var identifier = `${nativeID}:stopAnimation`;
        try {
          _NativeAnimatedHelper.default.API.setWaitingForIdentifier(identifier);
          _NativeAnimatedHelper.default.API.stopAnimation(nativeID);
        } finally {
          _NativeAnimatedHelper.default.API.unsetWaitingForIdentifier(identifier);
        }
      }
      this.__active = false;
    }
  }, {
    key: "__getNativeAnimationConfig",
    value: function __getNativeAnimationConfig() {
      throw new Error('This animation type cannot be offloaded to native');
    }
  }, {
    key: "__findAnimatedPropsNodes",
    value: function __findAnimatedPropsNodes(node) {
      var result = [];
      if (node instanceof _AnimatedProps.default) {
        result.push(node);
        return result;
      }
      for (var child of node.__getChildren()) {
        result.push.apply(result, (0, _toConsumableArray2.default)(this.__findAnimatedPropsNodes(child)));
      }
      return result;
    }
  }, {
    key: "__startAnimationIfNative",
    value: function __startAnimationIfNative(animatedValue) {
      var _this = this;
      if (!(0, _classPrivateFieldLooseBase2.default)(this, _useNativeDriver)[_useNativeDriver]) {
        return false;
      }
      var startNativeAnimationWaitId = `${startNativeAnimationNextId}:startAnimation`;
      startNativeAnimationNextId += 1;
      _NativeAnimatedHelper.default.API.setWaitingForIdentifier(startNativeAnimationWaitId);
      try {
        var config = this.__getNativeAnimationConfig();
        animatedValue.__makeNative(config.platformConfig);
        (0, _classPrivateFieldLooseBase2.default)(this, _nativeID)[_nativeID] = _NativeAnimatedHelper.default.generateNewAnimationId();
        _NativeAnimatedHelper.default.API.startAnimatingNode((0, _classPrivateFieldLooseBase2.default)(this, _nativeID)[_nativeID], animatedValue.__getNativeTag(), config, function (result) {
          _this.__notifyAnimationEnd(result);
          var value = result.value;
          if (value != null) {
            animatedValue.__onAnimatedValueUpdateReceived(value);
            if (_this.__isLooping === true) {
              return;
            }
            _this.__findAnimatedPropsNodes(animatedValue).forEach(function (node) {
              return node.update();
            });
          }
        });
        return true;
      } catch (e) {
        throw e;
      } finally {
        _NativeAnimatedHelper.default.API.unsetWaitingForIdentifier(startNativeAnimationWaitId);
      }
    }
  }, {
    key: "__notifyAnimationEnd",
    value: function __notifyAnimationEnd(result) {
      var callback = (0, _classPrivateFieldLooseBase2.default)(this, _onEnd)[_onEnd];
      if (callback != null) {
        (0, _classPrivateFieldLooseBase2.default)(this, _onEnd)[_onEnd] = null;
        callback(result);
      }
    }
  }, {
    key: "__getDebugID",
    value: function __getDebugID() {
      if (__DEV__) {
        return this.__debugID;
      }
      return undefined;
    }
  }]);
}();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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