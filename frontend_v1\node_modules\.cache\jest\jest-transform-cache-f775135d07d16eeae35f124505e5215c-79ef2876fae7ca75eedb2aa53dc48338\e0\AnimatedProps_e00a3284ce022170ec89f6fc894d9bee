82d8f5f2a6041c154c0175fcee12f278
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _get2 = _interopRequireDefault(require("@babel/runtime/helpers/get"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _classPrivateFieldLooseBase2 = _interopRequireDefault(require("@babel/runtime/helpers/classPrivateFieldLooseBase"));
var _classPrivateFieldLooseKey2 = _interopRequireDefault(require("@babel/runtime/helpers/classPrivateFieldLooseKey"));
var _NativeAnimatedHelper = _interopRequireDefault(require("../../../src/private/animated/NativeAnimatedHelper"));
var _RendererProxy = require("../../ReactNative/RendererProxy");
var _AnimatedEvent = require("../AnimatedEvent");
var _AnimatedNode2 = _interopRequireDefault(require("./AnimatedNode"));
var _AnimatedObject = _interopRequireDefault(require("./AnimatedObject"));
var _AnimatedStyle = _interopRequireDefault(require("./AnimatedStyle"));
var _invariant = _interopRequireDefault(require("invariant"));
var _Object$hasOwn;
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && "function" == typeof p ? function (t) { return p.apply(e, t); } : p; }
function createAnimatedProps(inputProps, allowlist) {
  var nodeKeys = [];
  var nodes = [];
  var props = {};
  var keys = Object.keys(inputProps);
  for (var ii = 0, length = keys.length; ii < length; ii++) {
    var key = keys[ii];
    var value = inputProps[key];
    if (allowlist == null || hasOwn(allowlist, key)) {
      var node = void 0;
      if (key === 'style') {
        node = _AnimatedStyle.default.from(value, allowlist == null ? void 0 : allowlist.style);
      } else if (value instanceof _AnimatedNode2.default) {
        node = value;
      } else {
        node = _AnimatedObject.default.from(value);
      }
      if (node == null) {
        props[key] = value;
      } else {
        nodeKeys.push(key);
        nodes.push(node);
        props[key] = node;
      }
    } else {
      if (__DEV__) {
        if (_AnimatedObject.default.from(inputProps[key]) != null) {
          console.error(`AnimatedProps: ${key} is not allowlisted for animation, but it ` + 'contains AnimatedNode values; props allowing animation: ', allowlist);
        }
      }
      props[key] = value;
    }
  }
  return [nodeKeys, nodes, props];
}
var _animatedView = (0, _classPrivateFieldLooseKey2.default)("animatedView");
var _callback = (0, _classPrivateFieldLooseKey2.default)("callback");
var _nodeKeys = (0, _classPrivateFieldLooseKey2.default)("nodeKeys");
var _nodes = (0, _classPrivateFieldLooseKey2.default)("nodes");
var _props = (0, _classPrivateFieldLooseKey2.default)("props");
var AnimatedProps = exports.default = function (_AnimatedNode) {
  function AnimatedProps(inputProps, callback, allowlist, config) {
    var _this;
    (0, _classCallCheck2.default)(this, AnimatedProps);
    _this = _callSuper(this, AnimatedProps, [config]);
    Object.defineProperty(_this, _animatedView, {
      writable: true,
      value: null
    });
    Object.defineProperty(_this, _callback, {
      writable: true,
      value: void 0
    });
    Object.defineProperty(_this, _nodeKeys, {
      writable: true,
      value: void 0
    });
    Object.defineProperty(_this, _nodes, {
      writable: true,
      value: void 0
    });
    Object.defineProperty(_this, _props, {
      writable: true,
      value: void 0
    });
    var _createAnimatedProps = createAnimatedProps(inputProps, allowlist),
      _createAnimatedProps2 = (0, _slicedToArray2.default)(_createAnimatedProps, 3),
      nodeKeys = _createAnimatedProps2[0],
      nodes = _createAnimatedProps2[1],
      props = _createAnimatedProps2[2];
    (0, _classPrivateFieldLooseBase2.default)(_this, _nodeKeys)[_nodeKeys] = nodeKeys;
    (0, _classPrivateFieldLooseBase2.default)(_this, _nodes)[_nodes] = nodes;
    (0, _classPrivateFieldLooseBase2.default)(_this, _props)[_props] = props;
    (0, _classPrivateFieldLooseBase2.default)(_this, _callback)[_callback] = callback;
    return _this;
  }
  (0, _inherits2.default)(AnimatedProps, _AnimatedNode);
  return (0, _createClass2.default)(AnimatedProps, [{
    key: "__getValue",
    value: function __getValue() {
      var props = {};
      var keys = Object.keys((0, _classPrivateFieldLooseBase2.default)(this, _props)[_props]);
      for (var ii = 0, length = keys.length; ii < length; ii++) {
        var key = keys[ii];
        var value = (0, _classPrivateFieldLooseBase2.default)(this, _props)[_props][key];
        if (value instanceof _AnimatedNode2.default) {
          props[key] = value.__getValue();
        } else if (value instanceof _AnimatedEvent.AnimatedEvent) {
          props[key] = value.__getHandler();
        } else {
          props[key] = value;
        }
      }
      return props;
    }
  }, {
    key: "__getValueWithStaticProps",
    value: function __getValueWithStaticProps(staticProps) {
      var props = Object.assign({}, staticProps);
      var keys = Object.keys(staticProps);
      for (var ii = 0, length = keys.length; ii < length; ii++) {
        var key = keys[ii];
        var maybeNode = (0, _classPrivateFieldLooseBase2.default)(this, _props)[_props][key];
        if (key === 'style' && maybeNode instanceof _AnimatedStyle.default) {
          props[key] = maybeNode.__getValueWithStaticStyle(staticProps.style);
        } else if (maybeNode instanceof _AnimatedNode2.default) {
          props[key] = maybeNode.__getValue();
        } else if (maybeNode instanceof _AnimatedEvent.AnimatedEvent) {
          props[key] = maybeNode.__getHandler();
        }
      }
      return props;
    }
  }, {
    key: "__getAnimatedValue",
    value: function __getAnimatedValue() {
      var props = {};
      var nodeKeys = (0, _classPrivateFieldLooseBase2.default)(this, _nodeKeys)[_nodeKeys];
      var nodes = (0, _classPrivateFieldLooseBase2.default)(this, _nodes)[_nodes];
      for (var ii = 0, length = nodes.length; ii < length; ii++) {
        var key = nodeKeys[ii];
        var node = nodes[ii];
        props[key] = node.__getAnimatedValue();
      }
      return props;
    }
  }, {
    key: "__attach",
    value: function __attach() {
      var nodes = (0, _classPrivateFieldLooseBase2.default)(this, _nodes)[_nodes];
      for (var ii = 0, length = nodes.length; ii < length; ii++) {
        var node = nodes[ii];
        node.__addChild(this);
      }
      _superPropGet(AnimatedProps, "__attach", this, 3)([]);
    }
  }, {
    key: "__detach",
    value: function __detach() {
      if (this.__isNative && (0, _classPrivateFieldLooseBase2.default)(this, _animatedView)[_animatedView]) {
        this.__disconnectAnimatedView();
      }
      (0, _classPrivateFieldLooseBase2.default)(this, _animatedView)[_animatedView] = null;
      var nodes = (0, _classPrivateFieldLooseBase2.default)(this, _nodes)[_nodes];
      for (var ii = 0, length = nodes.length; ii < length; ii++) {
        var node = nodes[ii];
        node.__removeChild(this);
      }
      _superPropGet(AnimatedProps, "__detach", this, 3)([]);
    }
  }, {
    key: "update",
    value: function update() {
      (0, _classPrivateFieldLooseBase2.default)(this, _callback)[_callback]();
    }
  }, {
    key: "__makeNative",
    value: function __makeNative(platformConfig) {
      var nodes = (0, _classPrivateFieldLooseBase2.default)(this, _nodes)[_nodes];
      for (var ii = 0, length = nodes.length; ii < length; ii++) {
        var node = nodes[ii];
        node.__makeNative(platformConfig);
      }
      if (!this.__isNative) {
        this.__isNative = true;
        _superPropGet(AnimatedProps, "__setPlatformConfig", this, 3)([platformConfig]);
        if ((0, _classPrivateFieldLooseBase2.default)(this, _animatedView)[_animatedView]) {
          this.__connectAnimatedView();
        }
      }
    }
  }, {
    key: "setNativeView",
    value: function setNativeView(animatedView) {
      if ((0, _classPrivateFieldLooseBase2.default)(this, _animatedView)[_animatedView] === animatedView) {
        return;
      }
      (0, _classPrivateFieldLooseBase2.default)(this, _animatedView)[_animatedView] = animatedView;
      if (this.__isNative) {
        this.__connectAnimatedView();
      }
    }
  }, {
    key: "__connectAnimatedView",
    value: function __connectAnimatedView() {
      (0, _invariant.default)(this.__isNative, 'Expected node to be marked as "native"');
      var nativeViewTag = (0, _RendererProxy.findNodeHandle)((0, _classPrivateFieldLooseBase2.default)(this, _animatedView)[_animatedView]);
      if (nativeViewTag == null) {
        if (process.env.NODE_ENV === 'test') {
          nativeViewTag = -1;
        } else {
          throw new Error('Unable to locate attached view in the native tree');
        }
      }
      _NativeAnimatedHelper.default.API.connectAnimatedNodeToView(this.__getNativeTag(), nativeViewTag);
    }
  }, {
    key: "__disconnectAnimatedView",
    value: function __disconnectAnimatedView() {
      (0, _invariant.default)(this.__isNative, 'Expected node to be marked as "native"');
      var nativeViewTag = (0, _RendererProxy.findNodeHandle)((0, _classPrivateFieldLooseBase2.default)(this, _animatedView)[_animatedView]);
      if (nativeViewTag == null) {
        if (process.env.NODE_ENV === 'test') {
          nativeViewTag = -1;
        } else {
          throw new Error('Unable to locate attached view in the native tree');
        }
      }
      _NativeAnimatedHelper.default.API.disconnectAnimatedNodeFromView(this.__getNativeTag(), nativeViewTag);
    }
  }, {
    key: "__restoreDefaultValues",
    value: function __restoreDefaultValues() {
      if (this.__isNative) {
        _NativeAnimatedHelper.default.API.restoreDefaultValues(this.__getNativeTag());
      }
    }
  }, {
    key: "__getNativeConfig",
    value: function __getNativeConfig() {
      var platformConfig = this.__getPlatformConfig();
      var propsConfig = {};
      var nodeKeys = (0, _classPrivateFieldLooseBase2.default)(this, _nodeKeys)[_nodeKeys];
      var nodes = (0, _classPrivateFieldLooseBase2.default)(this, _nodes)[_nodes];
      for (var ii = 0, length = nodes.length; ii < length; ii++) {
        var key = nodeKeys[ii];
        var node = nodes[ii];
        node.__makeNative(platformConfig);
        propsConfig[key] = node.__getNativeTag();
      }
      return {
        type: 'props',
        props: propsConfig,
        debugID: this.__getDebugID()
      };
    }
  }]);
}(_AnimatedNode2.default);
var _hasOwnProp = Object.prototype.hasOwnProperty;
var hasOwn = (_Object$hasOwn = Object.hasOwn) != null ? _Object$hasOwn : function (obj, prop) {
  return _hasOwnProp.call(obj, prop);
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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