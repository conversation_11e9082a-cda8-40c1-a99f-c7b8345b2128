{"version": 3, "names": ["WCAG_THRESHOLDS", "exports", "AA_NORMAL", "AA_LARGE", "AAA_NORMAL", "AAA_LARGE", "hexToRgb", "hex", "result", "exec", "r", "parseInt", "g", "b", "rgbToHex", "toString", "slice", "getRelativeLuminance", "color", "rsRGB", "gsRGB", "bsRGB", "rLinear", "Math", "pow", "gLinear", "bLinear", "getContrastRatio", "color1", "color2", "lum1", "lum2", "lighter", "max", "darker", "min", "checkContrastCompliance", "foreground", "background", "isLargeText", "arguments", "length", "undefined", "fgColor", "bgColor", "Error", "ratio", "wcagAA", "wcagAAA", "wcagAALarge", "wcagAAALarge", "level", "recommendation", "targetRatio", "round", "generateAccessibleColors", "baseColor", "base", "primary", "secondary", "adjustColorForContrast", "text", "textSecondary", "surface", "minRatio", "colorRgb", "bgRgb", "adjustedColor", "Object", "assign", "currentRatio", "bgLuminance", "<PERSON><PERSON><PERSON><PERSON>", "step", "iterations", "maxIterations", "getHighContrastColor", "simulateColorBlindness", "type", "rgb", "validateColorPalette", "palette", "issues", "recommendations", "combinations", "fg", "bg", "name", "combo", "push", "error", "valid", "generateAccessibilityReport", "report", "_default", "default"], "sources": ["colorContrastUtils.ts"], "sourcesContent": ["/**\n * Color Contrast Utilities\n *\n * Comprehensive utilities for color contrast validation and accessibility\n * compliance following WCAG 2.2 AA/AAA guidelines.\n *\n * Features:\n * - Contrast ratio calculation\n * - WCAG compliance validation\n * - Color accessibility testing\n * - High contrast mode support\n * - Color blindness simulation\n *\n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\n// WCAG contrast ratio thresholds\nexport const WCAG_THRESHOLDS = {\n  AA_NORMAL: 4.5,\n  AA_LARGE: 3.0,\n  AAA_NORMAL: 7.0,\n  AAA_LARGE: 4.5,\n} as const;\n\n// Color interface\nexport interface Color {\n  r: number;\n  g: number;\n  b: number;\n  a?: number;\n}\n\n// Contrast result interface\nexport interface ContrastResult {\n  ratio: number;\n  wcagAA: boolean;\n  wcagAAA: boolean;\n  wcagAALarge: boolean;\n  wcagAAALarge: boolean;\n  level: 'fail' | 'aa' | 'aaa';\n  recommendation?: string;\n}\n\n/**\n * Convert hex color to RGB\n */\nexport const hexToRgb = (hex: string): Color | null => {\n  const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex);\n  return result ? {\n    r: parseInt(result[1], 16),\n    g: parseInt(result[2], 16),\n    b: parseInt(result[3], 16),\n  } : null;\n};\n\n/**\n * Convert RGB to hex\n */\nexport const rgbToHex = (r: number, g: number, b: number): string => {\n  return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;\n};\n\n/**\n * Calculate relative luminance of a color\n * Based on WCAG 2.2 specification\n */\nexport const getRelativeLuminance = (color: Color): number => {\n  const { r, g, b } = color;\n  \n  // Convert to sRGB\n  const rsRGB = r / 255;\n  const gsRGB = g / 255;\n  const bsRGB = b / 255;\n  \n  // Apply gamma correction\n  const rLinear = rsRGB <= 0.03928 ? rsRGB / 12.92 : Math.pow((rsRGB + 0.055) / 1.055, 2.4);\n  const gLinear = gsRGB <= 0.03928 ? gsRGB / 12.92 : Math.pow((gsRGB + 0.055) / 1.055, 2.4);\n  const bLinear = bsRGB <= 0.03928 ? bsRGB / 12.92 : Math.pow((bsRGB + 0.055) / 1.055, 2.4);\n  \n  // Calculate relative luminance\n  return 0.2126 * rLinear + 0.7152 * gLinear + 0.0722 * bLinear;\n};\n\n/**\n * Calculate contrast ratio between two colors\n * Based on WCAG 2.2 specification\n */\nexport const getContrastRatio = (color1: Color, color2: Color): number => {\n  const lum1 = getRelativeLuminance(color1);\n  const lum2 = getRelativeLuminance(color2);\n  \n  const lighter = Math.max(lum1, lum2);\n  const darker = Math.min(lum1, lum2);\n  \n  return (lighter + 0.05) / (darker + 0.05);\n};\n\n/**\n * Check if contrast ratio meets WCAG requirements\n */\nexport const checkContrastCompliance = (\n  foreground: string | Color,\n  background: string | Color,\n  isLargeText: boolean = false\n): ContrastResult => {\n  // Convert hex strings to Color objects if needed\n  const fgColor = typeof foreground === 'string' ? hexToRgb(foreground) : foreground;\n  const bgColor = typeof background === 'string' ? hexToRgb(background) : background;\n  \n  if (!fgColor || !bgColor) {\n    throw new Error('Invalid color format');\n  }\n  \n  const ratio = getContrastRatio(fgColor, bgColor);\n  \n  const wcagAA = ratio >= (isLargeText ? WCAG_THRESHOLDS.AA_LARGE : WCAG_THRESHOLDS.AA_NORMAL);\n  const wcagAAA = ratio >= (isLargeText ? WCAG_THRESHOLDS.AAA_LARGE : WCAG_THRESHOLDS.AAA_NORMAL);\n  const wcagAALarge = ratio >= WCAG_THRESHOLDS.AA_LARGE;\n  const wcagAAALarge = ratio >= WCAG_THRESHOLDS.AAA_LARGE;\n  \n  let level: 'fail' | 'aa' | 'aaa' = 'fail';\n  if (wcagAAA) {\n    level = 'aaa';\n  } else if (wcagAA) {\n    level = 'aa';\n  }\n  \n  let recommendation: string | undefined;\n  if (!wcagAA) {\n    const targetRatio = isLargeText ? WCAG_THRESHOLDS.AA_LARGE : WCAG_THRESHOLDS.AA_NORMAL;\n    recommendation = `Increase contrast ratio to at least ${targetRatio}:1 for WCAG AA compliance`;\n  }\n  \n  return {\n    ratio: Math.round(ratio * 100) / 100,\n    wcagAA,\n    wcagAAA,\n    wcagAALarge,\n    wcagAAALarge,\n    level,\n    recommendation,\n  };\n};\n\n/**\n * Generate accessible color palette\n */\nexport const generateAccessibleColors = (baseColor: string): {\n  primary: string;\n  secondary: string;\n  text: string;\n  textSecondary: string;\n  background: string;\n  surface: string;\n} => {\n  const base = hexToRgb(baseColor);\n  if (!base) throw new Error('Invalid base color');\n  \n  // Generate variations with proper contrast\n  return {\n    primary: baseColor,\n    secondary: adjustColorForContrast(baseColor, '#FFFFFF', 3.0),\n    text: '#000000',\n    textSecondary: '#666666',\n    background: '#FFFFFF',\n    surface: '#F8F9FA',\n  };\n};\n\n/**\n * Adjust color to meet minimum contrast ratio\n */\nexport const adjustColorForContrast = (\n  color: string,\n  background: string,\n  minRatio: number\n): string => {\n  const colorRgb = hexToRgb(color);\n  const bgRgb = hexToRgb(background);\n  \n  if (!colorRgb || !bgRgb) throw new Error('Invalid color format');\n  \n  let adjustedColor = { ...colorRgb };\n  let currentRatio = getContrastRatio(adjustedColor, bgRgb);\n  \n  // If already meets requirement, return original\n  if (currentRatio >= minRatio) {\n    return color;\n  }\n  \n  // Determine if we need to make it lighter or darker\n  const bgLuminance = getRelativeLuminance(bgRgb);\n  const shouldDarken = bgLuminance > 0.5;\n  \n  // Adjust color iteratively\n  let step = shouldDarken ? -5 : 5;\n  let iterations = 0;\n  const maxIterations = 50;\n  \n  while (currentRatio < minRatio && iterations < maxIterations) {\n    adjustedColor.r = Math.max(0, Math.min(255, adjustedColor.r + step));\n    adjustedColor.g = Math.max(0, Math.min(255, adjustedColor.g + step));\n    adjustedColor.b = Math.max(0, Math.min(255, adjustedColor.b + step));\n    \n    currentRatio = getContrastRatio(adjustedColor, bgRgb);\n    iterations++;\n  }\n  \n  return rgbToHex(adjustedColor.r, adjustedColor.g, adjustedColor.b);\n};\n\n/**\n * Get high contrast version of a color\n */\nexport const getHighContrastColor = (\n  color: string,\n  background: string = '#FFFFFF'\n): string => {\n  const colorRgb = hexToRgb(color);\n  const bgRgb = hexToRgb(background);\n  \n  if (!colorRgb || !bgRgb) return color;\n  \n  const bgLuminance = getRelativeLuminance(bgRgb);\n  \n  // Return high contrast black or white based on background\n  return bgLuminance > 0.5 ? '#000000' : '#FFFFFF';\n};\n\n/**\n * Simulate color blindness\n */\nexport const simulateColorBlindness = (\n  color: string,\n  type: 'protanopia' | 'deuteranopia' | 'tritanopia'\n): string => {\n  const rgb = hexToRgb(color);\n  if (!rgb) return color;\n  \n  let { r, g, b } = rgb;\n  \n  // Apply color blindness transformation matrices\n  switch (type) {\n    case 'protanopia': // Red-blind\n      r = 0.567 * r + 0.433 * g;\n      g = 0.558 * r + 0.442 * g;\n      b = 0.242 * g + 0.758 * b;\n      break;\n    case 'deuteranopia': // Green-blind\n      r = 0.625 * r + 0.375 * g;\n      g = 0.7 * r + 0.3 * g;\n      b = 0.3 * g + 0.7 * b;\n      break;\n    case 'tritanopia': // Blue-blind\n      r = 0.95 * r + 0.05 * g;\n      g = 0.433 * g + 0.567 * b;\n      b = 0.475 * g + 0.525 * b;\n      break;\n  }\n  \n  return rgbToHex(\n    Math.round(Math.max(0, Math.min(255, r))),\n    Math.round(Math.max(0, Math.min(255, g))),\n    Math.round(Math.max(0, Math.min(255, b)))\n  );\n};\n\n/**\n * Validate color palette accessibility\n */\nexport const validateColorPalette = (palette: Record<string, string>): {\n  valid: boolean;\n  issues: string[];\n  recommendations: string[];\n} => {\n  const issues: string[] = [];\n  const recommendations: string[] = [];\n  \n  // Common color combinations to check\n  const combinations = [\n    { fg: 'text', bg: 'background', name: 'Primary text on background' },\n    { fg: 'textSecondary', bg: 'background', name: 'Secondary text on background' },\n    { fg: 'primary', bg: 'background', name: 'Primary color on background' },\n    { fg: 'background', bg: 'primary', name: 'Background on primary' },\n  ];\n  \n  for (const combo of combinations) {\n    if (palette[combo.fg] && palette[combo.bg]) {\n      try {\n        const result = checkContrastCompliance(palette[combo.fg], palette[combo.bg]);\n        \n        if (!result.wcagAA) {\n          issues.push(`${combo.name}: Contrast ratio ${result.ratio}:1 fails WCAG AA`);\n          if (result.recommendation) {\n            recommendations.push(`${combo.name}: ${result.recommendation}`);\n          }\n        }\n      } catch (error) {\n        issues.push(`${combo.name}: Invalid color format`);\n      }\n    }\n  }\n  \n  return {\n    valid: issues.length === 0,\n    issues,\n    recommendations,\n  };\n};\n\n/**\n * Generate accessibility report for colors\n */\nexport const generateAccessibilityReport = (\n  foreground: string,\n  background: string,\n  isLargeText: boolean = false\n): string => {\n  try {\n    const result = checkContrastCompliance(foreground, background, isLargeText);\n    \n    let report = `Color Accessibility Report\\n`;\n    report += `========================\\n`;\n    report += `Foreground: ${foreground}\\n`;\n    report += `Background: ${background}\\n`;\n    report += `Text Size: ${isLargeText ? 'Large (18pt+)' : 'Normal'}\\n`;\n    report += `Contrast Ratio: ${result.ratio}:1\\n\\n`;\n    \n    report += `WCAG Compliance:\\n`;\n    report += `- AA Normal: ${result.wcagAA ? '✓ Pass' : '✗ Fail'}\\n`;\n    report += `- AA Large: ${result.wcagAALarge ? '✓ Pass' : '✗ Fail'}\\n`;\n    report += `- AAA Normal: ${result.wcagAAA ? '✓ Pass' : '✗ Fail'}\\n`;\n    report += `- AAA Large: ${result.wcagAAALarge ? '✓ Pass' : '✗ Fail'}\\n\\n`;\n    \n    if (result.recommendation) {\n      report += `Recommendation: ${result.recommendation}\\n`;\n    }\n    \n    return report;\n  } catch (error) {\n    return `Error generating report: ${error}`;\n  }\n};\n\nexport default {\n  hexToRgb,\n  rgbToHex,\n  getRelativeLuminance,\n  getContrastRatio,\n  checkContrastCompliance,\n  generateAccessibleColors,\n  adjustColorForContrast,\n  getHighContrastColor,\n  simulateColorBlindness,\n  validateColorPalette,\n  generateAccessibilityReport,\n  WCAG_THRESHOLDS,\n};\n"], "mappings": ";;;;AAkBO,IAAMA,eAAe,GAAAC,OAAA,CAAAD,eAAA,GAAG;EAC7BE,SAAS,EAAE,GAAG;EACdC,QAAQ,EAAE,GAAG;EACbC,UAAU,EAAE,GAAG;EACfC,SAAS,EAAE;AACb,CAAU;AAwBH,IAAMC,QAAQ,GAAAL,OAAA,CAAAK,QAAA,GAAG,SAAXA,QAAQA,CAAIC,GAAW,EAAmB;EACrD,IAAMC,MAAM,GAAG,2CAA2C,CAACC,IAAI,CAACF,GAAG,CAAC;EACpE,OAAOC,MAAM,GAAG;IACdE,CAAC,EAAEC,QAAQ,CAACH,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAC1BI,CAAC,EAAED,QAAQ,CAACH,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAC1BK,CAAC,EAAEF,QAAQ,CAACH,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE;EAC3B,CAAC,GAAG,IAAI;AACV,CAAC;AAKM,IAAMM,QAAQ,GAAAb,OAAA,CAAAa,QAAA,GAAG,SAAXA,QAAQA,CAAIJ,CAAS,EAAEE,CAAS,EAAEC,CAAS,EAAa;EACnE,OAAO,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,KAAKH,CAAC,IAAI,EAAE,CAAC,IAAIE,CAAC,IAAI,CAAC,CAAC,GAAGC,CAAC,EAAEE,QAAQ,CAAC,EAAE,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,EAAE;AAC3E,CAAC;AAMM,IAAMC,oBAAoB,GAAAhB,OAAA,CAAAgB,oBAAA,GAAG,SAAvBA,oBAAoBA,CAAIC,KAAY,EAAa;EAC5D,IAAQR,CAAC,GAAWQ,KAAK,CAAjBR,CAAC;IAAEE,CAAC,GAAQM,KAAK,CAAdN,CAAC;IAAEC,CAAC,GAAKK,KAAK,CAAXL,CAAC;EAGf,IAAMM,KAAK,GAAGT,CAAC,GAAG,GAAG;EACrB,IAAMU,KAAK,GAAGR,CAAC,GAAG,GAAG;EACrB,IAAMS,KAAK,GAAGR,CAAC,GAAG,GAAG;EAGrB,IAAMS,OAAO,GAAGH,KAAK,IAAI,OAAO,GAAGA,KAAK,GAAG,KAAK,GAAGI,IAAI,CAACC,GAAG,CAAC,CAACL,KAAK,GAAG,KAAK,IAAI,KAAK,EAAE,GAAG,CAAC;EACzF,IAAMM,OAAO,GAAGL,KAAK,IAAI,OAAO,GAAGA,KAAK,GAAG,KAAK,GAAGG,IAAI,CAACC,GAAG,CAAC,CAACJ,KAAK,GAAG,KAAK,IAAI,KAAK,EAAE,GAAG,CAAC;EACzF,IAAMM,OAAO,GAAGL,KAAK,IAAI,OAAO,GAAGA,KAAK,GAAG,KAAK,GAAGE,IAAI,CAACC,GAAG,CAAC,CAACH,KAAK,GAAG,KAAK,IAAI,KAAK,EAAE,GAAG,CAAC;EAGzF,OAAO,MAAM,GAAGC,OAAO,GAAG,MAAM,GAAGG,OAAO,GAAG,MAAM,GAAGC,OAAO;AAC/D,CAAC;AAMM,IAAMC,gBAAgB,GAAA1B,OAAA,CAAA0B,gBAAA,GAAG,SAAnBA,gBAAgBA,CAAIC,MAAa,EAAEC,MAAa,EAAa;EACxE,IAAMC,IAAI,GAAGb,oBAAoB,CAACW,MAAM,CAAC;EACzC,IAAMG,IAAI,GAAGd,oBAAoB,CAACY,MAAM,CAAC;EAEzC,IAAMG,OAAO,GAAGT,IAAI,CAACU,GAAG,CAACH,IAAI,EAAEC,IAAI,CAAC;EACpC,IAAMG,MAAM,GAAGX,IAAI,CAACY,GAAG,CAACL,IAAI,EAAEC,IAAI,CAAC;EAEnC,OAAO,CAACC,OAAO,GAAG,IAAI,KAAKE,MAAM,GAAG,IAAI,CAAC;AAC3C,CAAC;AAKM,IAAME,uBAAuB,GAAAnC,OAAA,CAAAmC,uBAAA,GAAG,SAA1BA,uBAAuBA,CAClCC,UAA0B,EAC1BC,UAA0B,EAEP;EAAA,IADnBC,WAAoB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAG5B,IAAMG,OAAO,GAAG,OAAON,UAAU,KAAK,QAAQ,GAAG/B,QAAQ,CAAC+B,UAAU,CAAC,GAAGA,UAAU;EAClF,IAAMO,OAAO,GAAG,OAAON,UAAU,KAAK,QAAQ,GAAGhC,QAAQ,CAACgC,UAAU,CAAC,GAAGA,UAAU;EAElF,IAAI,CAACK,OAAO,IAAI,CAACC,OAAO,EAAE;IACxB,MAAM,IAAIC,KAAK,CAAC,sBAAsB,CAAC;EACzC;EAEA,IAAMC,KAAK,GAAGnB,gBAAgB,CAACgB,OAAO,EAAEC,OAAO,CAAC;EAEhD,IAAMG,MAAM,GAAGD,KAAK,KAAKP,WAAW,GAAGvC,eAAe,CAACG,QAAQ,GAAGH,eAAe,CAACE,SAAS,CAAC;EAC5F,IAAM8C,OAAO,GAAGF,KAAK,KAAKP,WAAW,GAAGvC,eAAe,CAACK,SAAS,GAAGL,eAAe,CAACI,UAAU,CAAC;EAC/F,IAAM6C,WAAW,GAAGH,KAAK,IAAI9C,eAAe,CAACG,QAAQ;EACrD,IAAM+C,YAAY,GAAGJ,KAAK,IAAI9C,eAAe,CAACK,SAAS;EAEvD,IAAI8C,KAA4B,GAAG,MAAM;EACzC,IAAIH,OAAO,EAAE;IACXG,KAAK,GAAG,KAAK;EACf,CAAC,MAAM,IAAIJ,MAAM,EAAE;IACjBI,KAAK,GAAG,IAAI;EACd;EAEA,IAAIC,cAAkC;EACtC,IAAI,CAACL,MAAM,EAAE;IACX,IAAMM,WAAW,GAAGd,WAAW,GAAGvC,eAAe,CAACG,QAAQ,GAAGH,eAAe,CAACE,SAAS;IACtFkD,cAAc,GAAG,uCAAuCC,WAAW,2BAA2B;EAChG;EAEA,OAAO;IACLP,KAAK,EAAEvB,IAAI,CAAC+B,KAAK,CAACR,KAAK,GAAG,GAAG,CAAC,GAAG,GAAG;IACpCC,MAAM,EAANA,MAAM;IACNC,OAAO,EAAPA,OAAO;IACPC,WAAW,EAAXA,WAAW;IACXC,YAAY,EAAZA,YAAY;IACZC,KAAK,EAALA,KAAK;IACLC,cAAc,EAAdA;EACF,CAAC;AACH,CAAC;AAKM,IAAMG,wBAAwB,GAAAtD,OAAA,CAAAsD,wBAAA,GAAG,SAA3BA,wBAAwBA,CAAIC,SAAiB,EAOrD;EACH,IAAMC,IAAI,GAAGnD,QAAQ,CAACkD,SAAS,CAAC;EAChC,IAAI,CAACC,IAAI,EAAE,MAAM,IAAIZ,KAAK,CAAC,oBAAoB,CAAC;EAGhD,OAAO;IACLa,OAAO,EAAEF,SAAS;IAClBG,SAAS,EAAEC,sBAAsB,CAACJ,SAAS,EAAE,SAAS,EAAE,GAAG,CAAC;IAC5DK,IAAI,EAAE,SAAS;IACfC,aAAa,EAAE,SAAS;IACxBxB,UAAU,EAAE,SAAS;IACrByB,OAAO,EAAE;EACX,CAAC;AACH,CAAC;AAKM,IAAMH,sBAAsB,GAAA3D,OAAA,CAAA2D,sBAAA,GAAG,SAAzBA,sBAAsBA,CACjC1C,KAAa,EACboB,UAAkB,EAClB0B,QAAgB,EACL;EACX,IAAMC,QAAQ,GAAG3D,QAAQ,CAACY,KAAK,CAAC;EAChC,IAAMgD,KAAK,GAAG5D,QAAQ,CAACgC,UAAU,CAAC;EAElC,IAAI,CAAC2B,QAAQ,IAAI,CAACC,KAAK,EAAE,MAAM,IAAIrB,KAAK,CAAC,sBAAsB,CAAC;EAEhE,IAAIsB,aAAa,GAAAC,MAAA,CAAAC,MAAA,KAAQJ,QAAQ,CAAE;EACnC,IAAIK,YAAY,GAAG3C,gBAAgB,CAACwC,aAAa,EAAED,KAAK,CAAC;EAGzD,IAAII,YAAY,IAAIN,QAAQ,EAAE;IAC5B,OAAO9C,KAAK;EACd;EAGA,IAAMqD,WAAW,GAAGtD,oBAAoB,CAACiD,KAAK,CAAC;EAC/C,IAAMM,YAAY,GAAGD,WAAW,GAAG,GAAG;EAGtC,IAAIE,IAAI,GAAGD,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC;EAChC,IAAIE,UAAU,GAAG,CAAC;EAClB,IAAMC,aAAa,GAAG,EAAE;EAExB,OAAOL,YAAY,GAAGN,QAAQ,IAAIU,UAAU,GAAGC,aAAa,EAAE;IAC5DR,aAAa,CAACzD,CAAC,GAAGa,IAAI,CAACU,GAAG,CAAC,CAAC,EAAEV,IAAI,CAACY,GAAG,CAAC,GAAG,EAAEgC,aAAa,CAACzD,CAAC,GAAG+D,IAAI,CAAC,CAAC;IACpEN,aAAa,CAACvD,CAAC,GAAGW,IAAI,CAACU,GAAG,CAAC,CAAC,EAAEV,IAAI,CAACY,GAAG,CAAC,GAAG,EAAEgC,aAAa,CAACvD,CAAC,GAAG6D,IAAI,CAAC,CAAC;IACpEN,aAAa,CAACtD,CAAC,GAAGU,IAAI,CAACU,GAAG,CAAC,CAAC,EAAEV,IAAI,CAACY,GAAG,CAAC,GAAG,EAAEgC,aAAa,CAACtD,CAAC,GAAG4D,IAAI,CAAC,CAAC;IAEpEH,YAAY,GAAG3C,gBAAgB,CAACwC,aAAa,EAAED,KAAK,CAAC;IACrDQ,UAAU,EAAE;EACd;EAEA,OAAO5D,QAAQ,CAACqD,aAAa,CAACzD,CAAC,EAAEyD,aAAa,CAACvD,CAAC,EAAEuD,aAAa,CAACtD,CAAC,CAAC;AACpE,CAAC;AAKM,IAAM+D,oBAAoB,GAAA3E,OAAA,CAAA2E,oBAAA,GAAG,SAAvBA,oBAAoBA,CAC/B1D,KAAa,EAEF;EAAA,IADXoB,UAAkB,GAAAE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,SAAS;EAE9B,IAAMyB,QAAQ,GAAG3D,QAAQ,CAACY,KAAK,CAAC;EAChC,IAAMgD,KAAK,GAAG5D,QAAQ,CAACgC,UAAU,CAAC;EAElC,IAAI,CAAC2B,QAAQ,IAAI,CAACC,KAAK,EAAE,OAAOhD,KAAK;EAErC,IAAMqD,WAAW,GAAGtD,oBAAoB,CAACiD,KAAK,CAAC;EAG/C,OAAOK,WAAW,GAAG,GAAG,GAAG,SAAS,GAAG,SAAS;AAClD,CAAC;AAKM,IAAMM,sBAAsB,GAAA5E,OAAA,CAAA4E,sBAAA,GAAG,SAAzBA,sBAAsBA,CACjC3D,KAAa,EACb4D,IAAkD,EACvC;EACX,IAAMC,GAAG,GAAGzE,QAAQ,CAACY,KAAK,CAAC;EAC3B,IAAI,CAAC6D,GAAG,EAAE,OAAO7D,KAAK;EAEtB,IAAMR,CAAC,GAAWqE,GAAG,CAAfrE,CAAC;IAAEE,CAAC,GAAQmE,GAAG,CAAZnE,CAAC;IAAEC,CAAC,GAAKkE,GAAG,CAATlE,CAAC;EAGb,QAAQiE,IAAI;IACV,KAAK,YAAY;MACfpE,CAAC,GAAG,KAAK,GAAGA,CAAC,GAAG,KAAK,GAAGE,CAAC;MACzBA,CAAC,GAAG,KAAK,GAAGF,CAAC,GAAG,KAAK,GAAGE,CAAC;MACzBC,CAAC,GAAG,KAAK,GAAGD,CAAC,GAAG,KAAK,GAAGC,CAAC;MACzB;IACF,KAAK,cAAc;MACjBH,CAAC,GAAG,KAAK,GAAGA,CAAC,GAAG,KAAK,GAAGE,CAAC;MACzBA,CAAC,GAAG,GAAG,GAAGF,CAAC,GAAG,GAAG,GAAGE,CAAC;MACrBC,CAAC,GAAG,GAAG,GAAGD,CAAC,GAAG,GAAG,GAAGC,CAAC;MACrB;IACF,KAAK,YAAY;MACfH,CAAC,GAAG,IAAI,GAAGA,CAAC,GAAG,IAAI,GAAGE,CAAC;MACvBA,CAAC,GAAG,KAAK,GAAGA,CAAC,GAAG,KAAK,GAAGC,CAAC;MACzBA,CAAC,GAAG,KAAK,GAAGD,CAAC,GAAG,KAAK,GAAGC,CAAC;MACzB;EACJ;EAEA,OAAOC,QAAQ,CACbS,IAAI,CAAC+B,KAAK,CAAC/B,IAAI,CAACU,GAAG,CAAC,CAAC,EAAEV,IAAI,CAACY,GAAG,CAAC,GAAG,EAAEzB,CAAC,CAAC,CAAC,CAAC,EACzCa,IAAI,CAAC+B,KAAK,CAAC/B,IAAI,CAACU,GAAG,CAAC,CAAC,EAAEV,IAAI,CAACY,GAAG,CAAC,GAAG,EAAEvB,CAAC,CAAC,CAAC,CAAC,EACzCW,IAAI,CAAC+B,KAAK,CAAC/B,IAAI,CAACU,GAAG,CAAC,CAAC,EAAEV,IAAI,CAACY,GAAG,CAAC,GAAG,EAAEtB,CAAC,CAAC,CAAC,CAC1C,CAAC;AACH,CAAC;AAKM,IAAMmE,oBAAoB,GAAA/E,OAAA,CAAA+E,oBAAA,GAAG,SAAvBA,oBAAoBA,CAAIC,OAA+B,EAI/D;EACH,IAAMC,MAAgB,GAAG,EAAE;EAC3B,IAAMC,eAAyB,GAAG,EAAE;EAGpC,IAAMC,YAAY,GAAG,CACnB;IAAEC,EAAE,EAAE,MAAM;IAAEC,EAAE,EAAE,YAAY;IAAEC,IAAI,EAAE;EAA6B,CAAC,EACpE;IAAEF,EAAE,EAAE,eAAe;IAAEC,EAAE,EAAE,YAAY;IAAEC,IAAI,EAAE;EAA+B,CAAC,EAC/E;IAAEF,EAAE,EAAE,SAAS;IAAEC,EAAE,EAAE,YAAY;IAAEC,IAAI,EAAE;EAA8B,CAAC,EACxE;IAAEF,EAAE,EAAE,YAAY;IAAEC,EAAE,EAAE,SAAS;IAAEC,IAAI,EAAE;EAAwB,CAAC,CACnE;EAED,KAAK,IAAMC,KAAK,IAAIJ,YAAY,EAAE;IAChC,IAAIH,OAAO,CAACO,KAAK,CAACH,EAAE,CAAC,IAAIJ,OAAO,CAACO,KAAK,CAACF,EAAE,CAAC,EAAE;MAC1C,IAAI;QACF,IAAM9E,MAAM,GAAG4B,uBAAuB,CAAC6C,OAAO,CAACO,KAAK,CAACH,EAAE,CAAC,EAAEJ,OAAO,CAACO,KAAK,CAACF,EAAE,CAAC,CAAC;QAE5E,IAAI,CAAC9E,MAAM,CAACuC,MAAM,EAAE;UAClBmC,MAAM,CAACO,IAAI,CAAC,GAAGD,KAAK,CAACD,IAAI,oBAAoB/E,MAAM,CAACsC,KAAK,kBAAkB,CAAC;UAC5E,IAAItC,MAAM,CAAC4C,cAAc,EAAE;YACzB+B,eAAe,CAACM,IAAI,CAAC,GAAGD,KAAK,CAACD,IAAI,KAAK/E,MAAM,CAAC4C,cAAc,EAAE,CAAC;UACjE;QACF;MACF,CAAC,CAAC,OAAOsC,KAAK,EAAE;QACdR,MAAM,CAACO,IAAI,CAAC,GAAGD,KAAK,CAACD,IAAI,wBAAwB,CAAC;MACpD;IACF;EACF;EAEA,OAAO;IACLI,KAAK,EAAET,MAAM,CAACzC,MAAM,KAAK,CAAC;IAC1ByC,MAAM,EAANA,MAAM;IACNC,eAAe,EAAfA;EACF,CAAC;AACH,CAAC;AAKM,IAAMS,2BAA2B,GAAA3F,OAAA,CAAA2F,2BAAA,GAAG,SAA9BA,2BAA2BA,CACtCvD,UAAkB,EAClBC,UAAkB,EAEP;EAAA,IADXC,WAAoB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAE5B,IAAI;IACF,IAAMhC,MAAM,GAAG4B,uBAAuB,CAACC,UAAU,EAAEC,UAAU,EAAEC,WAAW,CAAC;IAE3E,IAAIsD,MAAM,GAAG,8BAA8B;IAC3CA,MAAM,IAAI,4BAA4B;IACtCA,MAAM,IAAI,eAAexD,UAAU,IAAI;IACvCwD,MAAM,IAAI,eAAevD,UAAU,IAAI;IACvCuD,MAAM,IAAI,cAActD,WAAW,GAAG,eAAe,GAAG,QAAQ,IAAI;IACpEsD,MAAM,IAAI,mBAAmBrF,MAAM,CAACsC,KAAK,QAAQ;IAEjD+C,MAAM,IAAI,oBAAoB;IAC9BA,MAAM,IAAI,gBAAgBrF,MAAM,CAACuC,MAAM,GAAG,QAAQ,GAAG,QAAQ,IAAI;IACjE8C,MAAM,IAAI,eAAerF,MAAM,CAACyC,WAAW,GAAG,QAAQ,GAAG,QAAQ,IAAI;IACrE4C,MAAM,IAAI,iBAAiBrF,MAAM,CAACwC,OAAO,GAAG,QAAQ,GAAG,QAAQ,IAAI;IACnE6C,MAAM,IAAI,gBAAgBrF,MAAM,CAAC0C,YAAY,GAAG,QAAQ,GAAG,QAAQ,MAAM;IAEzE,IAAI1C,MAAM,CAAC4C,cAAc,EAAE;MACzByC,MAAM,IAAI,mBAAmBrF,MAAM,CAAC4C,cAAc,IAAI;IACxD;IAEA,OAAOyC,MAAM;EACf,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd,OAAO,4BAA4BA,KAAK,EAAE;EAC5C;AACF,CAAC;AAAC,IAAAI,QAAA,GAAA7F,OAAA,CAAA8F,OAAA,GAEa;EACbzF,QAAQ,EAARA,QAAQ;EACRQ,QAAQ,EAARA,QAAQ;EACRG,oBAAoB,EAApBA,oBAAoB;EACpBU,gBAAgB,EAAhBA,gBAAgB;EAChBS,uBAAuB,EAAvBA,uBAAuB;EACvBmB,wBAAwB,EAAxBA,wBAAwB;EACxBK,sBAAsB,EAAtBA,sBAAsB;EACtBgB,oBAAoB,EAApBA,oBAAoB;EACpBC,sBAAsB,EAAtBA,sBAAsB;EACtBG,oBAAoB,EAApBA,oBAAoB;EACpBY,2BAA2B,EAA3BA,2BAA2B;EAC3B5F,eAAe,EAAfA;AACF,CAAC", "ignoreList": []}