#!/usr/bin/env python
"""
Script to test login functionality for all test accounts
"""
import os
import sys
import django
import requests
import json

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from apps.authentication.models import User

def test_login_functionality():
    """Test login functionality for all test accounts"""
    
    # Test accounts with their correct passwords
    test_accounts = [
        {'email': '<EMAIL>', 'password': 'testpass123'},
        {'email': '<EMAIL>', 'password': 'testpass123'},
        {'email': '<EMAIL>', 'password': 'testpass123'},
        {'email': '<EMAIL>', 'password': 'TestPass123!'},
        {'email': '<EMAIL>', 'password': 'testpass123'},
    ]
    
    login_url = "http://************:8000/api/auth/login/"
    
    print("=== LOGIN FUNCTIONALITY TEST ===\n")
    
    successful_logins = 0
    failed_logins = 0
    
    for account in test_accounts:
        try:
            response = requests.post(login_url, json=account, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ {account['email']}: Login successful")
                print(f"   - Access token: {data.get('access', 'N/A')[:50]}...")
                print(f"   - Refresh token: {data.get('refresh', 'N/A')[:50]}...")
                successful_logins += 1
            else:
                print(f"❌ {account['email']}: Login failed")
                print(f"   - Status: {response.status_code}")
                print(f"   - Response: {response.text}")
                failed_logins += 1
                
        except requests.exceptions.RequestException as e:
            print(f"❌ {account['email']}: Network error")
            print(f"   - Error: {str(e)}")
            failed_logins += 1
        
        print()
    
    print(f"=== SUMMARY ===")
    print(f"Successful logins: {successful_logins}")
    print(f"Failed logins: {failed_logins}")
    print(f"Total tested: {len(test_accounts)}")
    
    if failed_logins == 0:
        print("🎉 All login tests passed!")
        return True
    else:
        print("⚠️  Some login tests failed!")
        return False

if __name__ == '__main__':
    test_login_functionality()
