{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "default", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_get2", "_inherits2", "_NativeAnimatedHelper", "_AnimatedNode2", "_callSuper", "t", "o", "e", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_superPropGet", "r", "p", "AnimatedTracking", "_AnimatedNode", "parent", "animationClass", "animationConfig", "callback", "config", "_this", "_value", "_parent", "_animationClass", "_animationConfig", "_useNativeDriver", "NativeAnimatedHelper", "shouldUseNativeDriver", "_callback", "__attach", "key", "__makeNative", "platformConfig", "__isNative", "__getValue", "__add<PERSON><PERSON>d", "__detach", "__remove<PERSON><PERSON>d", "update", "animate", "assign", "toValue", "__getNativeConfig", "animation", "undefined", "__getNativeAnimationConfig", "type", "animationId", "generateNewAnimationId", "__getNativeTag", "debugID", "__getDebugID", "AnimatedNode"], "sources": ["AnimatedTracking.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n * @format\n */\n\n'use strict';\n\nimport type {PlatformConfig} from '../AnimatedPlatformConfig';\nimport type {EndCallback} from '../animations/Animation';\nimport type {AnimatedNodeConfig} from './AnimatedNode';\nimport type AnimatedValue from './AnimatedValue';\n\nimport NativeAnimatedHelper from '../../../src/private/animated/NativeAnimatedHelper';\nimport AnimatedNode from './AnimatedNode';\n\nexport default class AnimatedTracking extends AnimatedNode {\n  _value: AnimatedValue;\n  _parent: AnimatedNode;\n  _callback: ?EndCallback;\n  _animationConfig: Object;\n  _animationClass: any;\n  _useNativeDriver: boolean;\n\n  constructor(\n    value: AnimatedValue,\n    parent: AnimatedNode,\n    animationClass: any,\n    animationConfig: Object,\n    callback?: ?EndCallback,\n    config?: ?AnimatedNodeConfig,\n  ) {\n    super(config);\n    this._value = value;\n    this._parent = parent;\n    this._animationClass = animationClass;\n    this._animationConfig = animationConfig;\n    this._useNativeDriver =\n      NativeAnimatedHelper.shouldUseNativeDriver(animationConfig);\n    this._callback = callback;\n    this.__attach();\n  }\n\n  __makeNative(platformConfig: ?PlatformConfig) {\n    this.__isNative = true;\n    this._parent.__makeNative(platformConfig);\n    super.__makeNative(platformConfig);\n    this._value.__makeNative(platformConfig);\n  }\n\n  __getValue(): Object {\n    return this._parent.__getValue();\n  }\n\n  __attach(): void {\n    this._parent.__addChild(this);\n    if (this._useNativeDriver) {\n      // when the tracking starts we need to convert this node to a \"native node\"\n      // so that the parent node will be made \"native\" too. This is necessary as\n      // if we don't do this `update` method will get called. At that point it\n      // may be too late as it would mean the JS driver has already started\n      // updating node values\n      let {platformConfig} = this._animationConfig;\n      this.__makeNative(platformConfig);\n    }\n    super.__attach();\n  }\n\n  __detach(): void {\n    this._parent.__removeChild(this);\n    super.__detach();\n  }\n\n  update(): void {\n    this._value.animate(\n      new this._animationClass({\n        ...this._animationConfig,\n        toValue: (this._animationConfig.toValue: any).__getValue(),\n      }),\n      this._callback,\n    );\n  }\n\n  __getNativeConfig(): any {\n    const animation = new this._animationClass({\n      ...this._animationConfig,\n      // remove toValue from the config as it's a ref to Animated.Value\n      toValue: undefined,\n    });\n    const animationConfig = animation.__getNativeAnimationConfig();\n    return {\n      type: 'tracking',\n      animationId: NativeAnimatedHelper.generateNewAnimationId(),\n      animationConfig,\n      toValue: this._parent.__getNativeTag(),\n      value: this._value.__getNativeTag(),\n      debugID: this.__getDebugID(),\n    };\n  }\n}\n"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAAA,IAAAC,gBAAA,GAAAP,sBAAA,CAAAC,OAAA;AAAA,IAAAO,aAAA,GAAAR,sBAAA,CAAAC,OAAA;AAAA,IAAAQ,2BAAA,GAAAT,sBAAA,CAAAC,OAAA;AAAA,IAAAS,gBAAA,GAAAV,sBAAA,CAAAC,OAAA;AAAA,IAAAU,KAAA,GAAAX,sBAAA,CAAAC,OAAA;AAAA,IAAAW,UAAA,GAAAZ,sBAAA,CAAAC,OAAA;AAOb,IAAAY,qBAAA,GAAAb,sBAAA,CAAAC,OAAA;AACA,IAAAa,cAAA,GAAAd,sBAAA,CAAAC,OAAA;AAA0C,SAAAc,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAP,gBAAA,CAAAJ,OAAA,EAAAW,CAAA,OAAAR,2BAAA,CAAAH,OAAA,EAAAU,CAAA,EAAAG,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAJ,CAAA,EAAAC,CAAA,YAAAR,gBAAA,CAAAJ,OAAA,EAAAU,CAAA,EAAAM,WAAA,IAAAL,CAAA,CAAAM,KAAA,CAAAP,CAAA,EAAAE,CAAA;AAAA,SAAAC,0BAAA,cAAAH,CAAA,IAAAQ,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAR,CAAA,aAAAG,yBAAA,YAAAA,0BAAA,aAAAH,CAAA;AAAA,SAAAY,cAAAZ,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAW,CAAA,QAAAC,CAAA,OAAAnB,KAAA,CAAAL,OAAA,MAAAI,gBAAA,CAAAJ,OAAA,MAAAuB,CAAA,GAAAb,CAAA,CAAAS,SAAA,GAAAT,CAAA,GAAAC,CAAA,EAAAC,CAAA,cAAAW,CAAA,yBAAAC,CAAA,aAAAd,CAAA,WAAAc,CAAA,CAAAP,KAAA,CAAAL,CAAA,EAAAF,CAAA,OAAAc,CAAA;AAAA,IAErBC,gBAAgB,GAAA3B,OAAA,CAAAE,OAAA,aAAA0B,aAAA;EAQnC,SAAAD,iBACE1B,KAAoB,EACpB4B,MAAoB,EACpBC,cAAmB,EACnBC,eAAuB,EACvBC,QAAuB,EACvBC,MAA4B,EAC5B;IAAA,IAAAC,KAAA;IAAA,IAAA/B,gBAAA,CAAAD,OAAA,QAAAyB,gBAAA;IACAO,KAAA,GAAAvB,UAAA,OAAAgB,gBAAA,GAAMM,MAAM;IACZC,KAAA,CAAKC,MAAM,GAAGlC,KAAK;IACnBiC,KAAA,CAAKE,OAAO,GAAGP,MAAM;IACrBK,KAAA,CAAKG,eAAe,GAAGP,cAAc;IACrCI,KAAA,CAAKI,gBAAgB,GAAGP,eAAe;IACvCG,KAAA,CAAKK,gBAAgB,GACnBC,6BAAoB,CAACC,qBAAqB,CAACV,eAAe,CAAC;IAC7DG,KAAA,CAAKQ,SAAS,GAAGV,QAAQ;IACzBE,KAAA,CAAKS,QAAQ,CAAC,CAAC;IAAC,OAAAT,KAAA;EAClB;EAAC,IAAA1B,UAAA,CAAAN,OAAA,EAAAyB,gBAAA,EAAAC,aAAA;EAAA,WAAAxB,aAAA,CAAAF,OAAA,EAAAyB,gBAAA;IAAAiB,GAAA;IAAA3C,KAAA,EAED,SAAA4C,YAAYA,CAACC,cAA+B,EAAE;MAC5C,IAAI,CAACC,UAAU,GAAG,IAAI;MACtB,IAAI,CAACX,OAAO,CAACS,YAAY,CAACC,cAAc,CAAC;MACzCtB,aAAA,CAAAG,gBAAA,4BAAmBmB,cAAc;MACjC,IAAI,CAACX,MAAM,CAACU,YAAY,CAACC,cAAc,CAAC;IAC1C;EAAC;IAAAF,GAAA;IAAA3C,KAAA,EAED,SAAA+C,UAAUA,CAAA,EAAW;MACnB,OAAO,IAAI,CAACZ,OAAO,CAACY,UAAU,CAAC,CAAC;IAClC;EAAC;IAAAJ,GAAA;IAAA3C,KAAA,EAED,SAAA0C,QAAQA,CAAA,EAAS;MACf,IAAI,CAACP,OAAO,CAACa,UAAU,CAAC,IAAI,CAAC;MAC7B,IAAI,IAAI,CAACV,gBAAgB,EAAE;QAMzB,IAAKO,cAAc,GAAI,IAAI,CAACR,gBAAgB,CAAvCQ,cAAc;QACnB,IAAI,CAACD,YAAY,CAACC,cAAc,CAAC;MACnC;MACAtB,aAAA,CAAAG,gBAAA;IACF;EAAC;IAAAiB,GAAA;IAAA3C,KAAA,EAED,SAAAiD,QAAQA,CAAA,EAAS;MACf,IAAI,CAACd,OAAO,CAACe,aAAa,CAAC,IAAI,CAAC;MAChC3B,aAAA,CAAAG,gBAAA;IACF;EAAC;IAAAiB,GAAA;IAAA3C,KAAA,EAED,SAAAmD,MAAMA,CAAA,EAAS;MACb,IAAI,CAACjB,MAAM,CAACkB,OAAO,CACjB,IAAI,IAAI,CAAChB,eAAe,CAAAvC,MAAA,CAAAwD,MAAA,KACnB,IAAI,CAAChB,gBAAgB;QACxBiB,OAAO,EAAG,IAAI,CAACjB,gBAAgB,CAACiB,OAAO,CAAOP,UAAU,CAAC;MAAC,EAC3D,CAAC,EACF,IAAI,CAACN,SACP,CAAC;IACH;EAAC;IAAAE,GAAA;IAAA3C,KAAA,EAED,SAAAuD,iBAAiBA,CAAA,EAAQ;MACvB,IAAMC,SAAS,GAAG,IAAI,IAAI,CAACpB,eAAe,CAAAvC,MAAA,CAAAwD,MAAA,KACrC,IAAI,CAAChB,gBAAgB;QAExBiB,OAAO,EAAEG;MAAS,EACnB,CAAC;MACF,IAAM3B,eAAe,GAAG0B,SAAS,CAACE,0BAA0B,CAAC,CAAC;MAC9D,OAAO;QACLC,IAAI,EAAE,UAAU;QAChBC,WAAW,EAAErB,6BAAoB,CAACsB,sBAAsB,CAAC,CAAC;QAC1D/B,eAAe,EAAfA,eAAe;QACfwB,OAAO,EAAE,IAAI,CAACnB,OAAO,CAAC2B,cAAc,CAAC,CAAC;QACtC9D,KAAK,EAAE,IAAI,CAACkC,MAAM,CAAC4B,cAAc,CAAC,CAAC;QACnCC,OAAO,EAAE,IAAI,CAACC,YAAY,CAAC;MAC7B,CAAC;IACH;EAAC;AAAA,EAlF2CC,sBAAY", "ignoreList": []}