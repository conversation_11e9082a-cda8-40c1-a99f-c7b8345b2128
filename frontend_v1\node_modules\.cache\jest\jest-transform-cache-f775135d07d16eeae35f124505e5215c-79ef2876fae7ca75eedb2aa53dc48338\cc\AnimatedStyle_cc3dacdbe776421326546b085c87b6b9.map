{"version": 3, "names": ["_NativeAnimatedValidation", "require", "ReactNativeFeatureFlags", "_interopRequireWildcard", "_flattenStyle", "_interopRequireDefault", "_Platform", "_AnimatedNode", "_AnimatedObject", "_AnimatedTransform", "_AnimatedWithChildren2", "_Object$hasOwn", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "_callSuper", "_getPrototypeOf2", "_possibleConstructorReturn2", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "_superPropGet", "p", "_get2", "createAnimatedStyle", "inputStyle", "allowlist", "keepUnanimated<PERSON><PERSON>ues", "nodeKeys", "nodes", "style", "keys", "ii", "length", "key", "value", "hasOwn", "node", "shouldUseAnimatedObjectForTransform", "AnimatedObject", "from", "AnimatedTransform", "AnimatedNode", "push", "__DEV__", "console", "error", "_inputStyle", "_classPrivateFieldLooseKey2", "_nodeKeys", "_nodes", "_style", "AnimatedStyle", "exports", "_Animated<PERSON>ith<PERSON><PERSON><PERSON><PERSON>", "config", "_this", "_classCallCheck2", "writable", "_classPrivateFieldLooseBase2", "_inherits2", "_createClass2", "__getValue", "Platform", "OS", "__getValueWithStaticStyle", "staticStyle", "flatStaticStyle", "flattenStyle", "assign", "maybeNode", "__getValueWithStaticTransforms", "Array", "isArray", "__getValueWithStaticObject", "__getAnimatedValue", "__attach", "__add<PERSON><PERSON>d", "__detach", "__remove<PERSON><PERSON>d", "__makeNative", "platformConfig", "__getNativeConfig", "__getPlatformConfig", "styleConfig", "__getNativeTag", "validateStyles", "type", "debugID", "__getDebugID", "flatStyle", "_createAnimatedStyle", "_createAnimatedStyle2", "_slicedToArray2", "AnimatedWithChildren", "_hasOwnProp", "obj", "prop"], "sources": ["AnimatedStyle.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n * @format\n */\n\nimport type {PlatformConfig} from '../AnimatedPlatformConfig';\nimport type {AnimatedNodeConfig} from './AnimatedNode';\n\nimport {validateStyles} from '../../../src/private/animated/NativeAnimatedValidation';\nimport * as ReactNativeFeatureFlags from '../../../src/private/featureflags/ReactNativeFeatureFlags';\nimport flattenStyle from '../../StyleSheet/flattenStyle';\nimport Platform from '../../Utilities/Platform';\nimport AnimatedNode from './AnimatedNode';\nimport AnimatedObject from './AnimatedObject';\nimport AnimatedTransform from './AnimatedTransform';\nimport AnimatedWithChildren from './AnimatedWithChildren';\n\nexport type AnimatedStyleAllowlist = $ReadOnly<{[string]: true}>;\n\nfunction createAnimatedStyle(\n  inputStyle: {[string]: mixed},\n  allowlist: ?AnimatedStyleAllowlist,\n  keepUnanimatedValues: boolean,\n): [$ReadOnlyArray<string>, $ReadOnlyArray<AnimatedNode>, {[string]: mixed}] {\n  const nodeKeys: Array<string> = [];\n  const nodes: Array<AnimatedNode> = [];\n  const style: {[string]: mixed} = {};\n\n  const keys = Object.keys(inputStyle);\n  for (let ii = 0, length = keys.length; ii < length; ii++) {\n    const key = keys[ii];\n    const value = inputStyle[key];\n\n    if (allowlist == null || hasOwn(allowlist, key)) {\n      let node;\n      if (value != null && key === 'transform') {\n        node = ReactNativeFeatureFlags.shouldUseAnimatedObjectForTransform()\n          ? AnimatedObject.from(value)\n          : // $FlowFixMe[incompatible-call] - `value` is mixed.\n            AnimatedTransform.from(value);\n      } else if (value instanceof AnimatedNode) {\n        node = value;\n      } else {\n        node = AnimatedObject.from(value);\n      }\n      if (node == null) {\n        if (keepUnanimatedValues) {\n          style[key] = value;\n        }\n      } else {\n        nodeKeys.push(key);\n        nodes.push(node);\n        style[key] = node;\n      }\n    } else {\n      if (__DEV__) {\n        // WARNING: This is a potentially expensive check that we should only\n        // do in development. Without this check in development, it might be\n        // difficult to identify which styles need to be allowlisted.\n        if (AnimatedObject.from(inputStyle[key]) != null) {\n          console.error(\n            `AnimatedStyle: ${key} is not allowlisted for animation, but it ` +\n              'contains AnimatedNode values; styles allowing animation: ',\n            allowlist,\n          );\n        }\n      }\n      if (keepUnanimatedValues) {\n        style[key] = value;\n      }\n    }\n  }\n\n  return [nodeKeys, nodes, style];\n}\n\nexport default class AnimatedStyle extends AnimatedWithChildren {\n  #inputStyle: any;\n  #nodeKeys: $ReadOnlyArray<string>;\n  #nodes: $ReadOnlyArray<AnimatedNode>;\n  #style: {[string]: mixed};\n\n  /**\n   * Creates an `AnimatedStyle` if `value` contains `AnimatedNode` instances.\n   * Otherwise, returns `null`.\n   */\n  static from(\n    inputStyle: any,\n    allowlist: ?AnimatedStyleAllowlist,\n  ): ?AnimatedStyle {\n    const flatStyle = flattenStyle(inputStyle);\n    if (flatStyle == null) {\n      return null;\n    }\n    const [nodeKeys, nodes, style] = createAnimatedStyle(\n      flatStyle,\n      allowlist,\n      Platform.OS !== 'web',\n    );\n    if (nodes.length === 0) {\n      return null;\n    }\n    return new AnimatedStyle(nodeKeys, nodes, style, inputStyle);\n  }\n\n  constructor(\n    nodeKeys: $ReadOnlyArray<string>,\n    nodes: $ReadOnlyArray<AnimatedNode>,\n    style: {[string]: mixed},\n    inputStyle: any,\n    config?: ?AnimatedNodeConfig,\n  ) {\n    super(config);\n    this.#nodeKeys = nodeKeys;\n    this.#nodes = nodes;\n    this.#style = style;\n    this.#inputStyle = inputStyle;\n  }\n\n  __getValue(): Object | Array<Object> {\n    const style: {[string]: mixed} = {};\n\n    const keys = Object.keys(this.#style);\n    for (let ii = 0, length = keys.length; ii < length; ii++) {\n      const key = keys[ii];\n      const value = this.#style[key];\n\n      if (value instanceof AnimatedNode) {\n        style[key] = value.__getValue();\n      } else {\n        style[key] = value;\n      }\n    }\n\n    /* $FlowFixMe[incompatible-type] Error found due to incomplete typing of\n     * Platform.flow.js */\n    return Platform.OS === 'web' ? [this.#inputStyle, style] : style;\n  }\n\n  /**\n   * Creates a new `style` object that contains the same style properties as\n   * the supplied `staticStyle` object, except with animated nodes for any\n   * style properties that were created by this `AnimatedStyle` instance.\n   */\n  __getValueWithStaticStyle(staticStyle: Object): Object | Array<Object> {\n    const flatStaticStyle = flattenStyle(staticStyle);\n    const style: {[string]: mixed} =\n      flatStaticStyle == null\n        ? {}\n        : flatStaticStyle === staticStyle\n          ? // Copy the input style, since we'll mutate it below.\n            {...flatStaticStyle}\n          : // Reuse `flatStaticStyle` if it is a newly created object.\n            flatStaticStyle;\n\n    const keys = Object.keys(style);\n    for (let ii = 0, length = keys.length; ii < length; ii++) {\n      const key = keys[ii];\n      const maybeNode = this.#style[key];\n\n      if (key === 'transform' && maybeNode instanceof AnimatedTransform) {\n        style[key] = maybeNode.__getValueWithStaticTransforms(\n          // NOTE: This check should not be necessary, but the types are not\n          // enforced as of this writing.\n          Array.isArray(style[key]) ? style[key] : [],\n        );\n      } else if (maybeNode instanceof AnimatedObject) {\n        style[key] = maybeNode.__getValueWithStaticObject(style[key]);\n      } else if (maybeNode instanceof AnimatedNode) {\n        style[key] = maybeNode.__getValue();\n      }\n    }\n\n    /* $FlowFixMe[incompatible-type] Error found due to incomplete typing of\n     * Platform.flow.js */\n    return Platform.OS === 'web' ? [this.#inputStyle, style] : style;\n  }\n\n  __getAnimatedValue(): Object {\n    const style: {[string]: mixed} = {};\n\n    const nodeKeys = this.#nodeKeys;\n    const nodes = this.#nodes;\n    for (let ii = 0, length = nodes.length; ii < length; ii++) {\n      const key = nodeKeys[ii];\n      const node = nodes[ii];\n      style[key] = node.__getAnimatedValue();\n    }\n\n    return style;\n  }\n\n  __attach(): void {\n    const nodes = this.#nodes;\n    for (let ii = 0, length = nodes.length; ii < length; ii++) {\n      const node = nodes[ii];\n      node.__addChild(this);\n    }\n    super.__attach();\n  }\n\n  __detach(): void {\n    const nodes = this.#nodes;\n    for (let ii = 0, length = nodes.length; ii < length; ii++) {\n      const node = nodes[ii];\n      node.__removeChild(this);\n    }\n    super.__detach();\n  }\n\n  __makeNative(platformConfig: ?PlatformConfig) {\n    const nodes = this.#nodes;\n    for (let ii = 0, length = nodes.length; ii < length; ii++) {\n      const node = nodes[ii];\n      node.__makeNative(platformConfig);\n    }\n    super.__makeNative(platformConfig);\n  }\n\n  __getNativeConfig(): Object {\n    const platformConfig = this.__getPlatformConfig();\n    const styleConfig: {[string]: ?number} = {};\n\n    const nodeKeys = this.#nodeKeys;\n    const nodes = this.#nodes;\n    for (let ii = 0, length = nodes.length; ii < length; ii++) {\n      const key = nodeKeys[ii];\n      const node = nodes[ii];\n      node.__makeNative(platformConfig);\n      styleConfig[key] = node.__getNativeTag();\n    }\n\n    if (__DEV__) {\n      validateStyles(styleConfig);\n    }\n    return {\n      type: 'style',\n      style: styleConfig,\n      debugID: this.__getDebugID(),\n    };\n  }\n}\n\n// Supported versions of JSC do not implement the newer Object.hasOwn. Remove\n// this shim when they do.\n// $FlowIgnore[method-unbinding]\nconst _hasOwnProp = Object.prototype.hasOwnProperty;\nconst hasOwn: (obj: $ReadOnly<{...}>, prop: string) => boolean =\n  // $FlowIgnore[method-unbinding]\n  Object.hasOwn ?? ((obj, prop) => _hasOwnProp.call(obj, prop));\n"], "mappings": ";;;;;;;;;;;;;;AAaA,IAAAA,yBAAA,GAAAC,OAAA;AACA,IAAAC,uBAAA,GAAAC,uBAAA,CAAAF,OAAA;AACA,IAAAG,aAAA,GAAAC,sBAAA,CAAAJ,OAAA;AACA,IAAAK,SAAA,GAAAD,sBAAA,CAAAJ,OAAA;AACA,IAAAM,aAAA,GAAAF,sBAAA,CAAAJ,OAAA;AACA,IAAAO,eAAA,GAAAH,sBAAA,CAAAJ,OAAA;AACA,IAAAQ,kBAAA,GAAAJ,sBAAA,CAAAJ,OAAA;AACA,IAAAS,sBAAA,GAAAL,sBAAA,CAAAJ,OAAA;AAA0D,IAAAU,cAAA;AAAA,SAAAR,wBAAAS,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAX,uBAAA,YAAAA,wBAAAS,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAAA,SAAAmB,WAAAnB,CAAA,EAAAK,CAAA,EAAAN,CAAA,WAAAM,CAAA,OAAAe,gBAAA,CAAAX,OAAA,EAAAJ,CAAA,OAAAgB,2BAAA,CAAAZ,OAAA,EAAAT,CAAA,EAAAsB,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAnB,CAAA,EAAAN,CAAA,YAAAqB,gBAAA,CAAAX,OAAA,EAAAT,CAAA,EAAAyB,WAAA,IAAApB,CAAA,CAAAqB,KAAA,CAAA1B,CAAA,EAAAD,CAAA;AAAA,SAAAuB,0BAAA,cAAAtB,CAAA,IAAA2B,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAd,IAAA,CAAAQ,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAA3B,CAAA,aAAAsB,yBAAA,YAAAA,0BAAA,aAAAtB,CAAA;AAAA,SAAA8B,cAAA9B,CAAA,EAAAK,CAAA,EAAAN,CAAA,EAAAG,CAAA,QAAA6B,CAAA,OAAAC,KAAA,CAAAvB,OAAA,MAAAW,gBAAA,CAAAX,OAAA,MAAAP,CAAA,GAAAF,CAAA,CAAA4B,SAAA,GAAA5B,CAAA,GAAAK,CAAA,EAAAN,CAAA,cAAAG,CAAA,yBAAA6B,CAAA,aAAA/B,CAAA,WAAA+B,CAAA,CAAAL,KAAA,CAAA3B,CAAA,EAAAC,CAAA,OAAA+B,CAAA;AAI1D,SAASE,mBAAmBA,CAC1BC,UAA6B,EAC7BC,SAAkC,EAClCC,oBAA6B,EAC8C;EAC3E,IAAMC,QAAuB,GAAG,EAAE;EAClC,IAAMC,KAA0B,GAAG,EAAE;EACrC,IAAMC,KAAwB,GAAG,CAAC,CAAC;EAEnC,IAAMC,IAAI,GAAGxB,MAAM,CAACwB,IAAI,CAACN,UAAU,CAAC;EACpC,KAAK,IAAIO,EAAE,GAAG,CAAC,EAAEC,MAAM,GAAGF,IAAI,CAACE,MAAM,EAAED,EAAE,GAAGC,MAAM,EAAED,EAAE,EAAE,EAAE;IACxD,IAAME,GAAG,GAAGH,IAAI,CAACC,EAAE,CAAC;IACpB,IAAMG,KAAK,GAAGV,UAAU,CAACS,GAAG,CAAC;IAE7B,IAAIR,SAAS,IAAI,IAAI,IAAIU,MAAM,CAACV,SAAS,EAAEQ,GAAG,CAAC,EAAE;MAC/C,IAAIG,IAAI;MACR,IAAIF,KAAK,IAAI,IAAI,IAAID,GAAG,KAAK,WAAW,EAAE;QACxCG,IAAI,GAAGzD,uBAAuB,CAAC0D,mCAAmC,CAAC,CAAC,GAChEC,uBAAc,CAACC,IAAI,CAACL,KAAK,CAAC,GAE1BM,0BAAiB,CAACD,IAAI,CAACL,KAAK,CAAC;MACnC,CAAC,MAAM,IAAIA,KAAK,YAAYO,qBAAY,EAAE;QACxCL,IAAI,GAAGF,KAAK;MACd,CAAC,MAAM;QACLE,IAAI,GAAGE,uBAAc,CAACC,IAAI,CAACL,KAAK,CAAC;MACnC;MACA,IAAIE,IAAI,IAAI,IAAI,EAAE;QAChB,IAAIV,oBAAoB,EAAE;UACxBG,KAAK,CAACI,GAAG,CAAC,GAAGC,KAAK;QACpB;MACF,CAAC,MAAM;QACLP,QAAQ,CAACe,IAAI,CAACT,GAAG,CAAC;QAClBL,KAAK,CAACc,IAAI,CAACN,IAAI,CAAC;QAChBP,KAAK,CAACI,GAAG,CAAC,GAAGG,IAAI;MACnB;IACF,CAAC,MAAM;MACL,IAAIO,OAAO,EAAE;QAIX,IAAIL,uBAAc,CAACC,IAAI,CAACf,UAAU,CAACS,GAAG,CAAC,CAAC,IAAI,IAAI,EAAE;UAChDW,OAAO,CAACC,KAAK,CACX,kBAAkBZ,GAAG,4CAA4C,GAC/D,2DAA2D,EAC7DR,SACF,CAAC;QACH;MACF;MACA,IAAIC,oBAAoB,EAAE;QACxBG,KAAK,CAACI,GAAG,CAAC,GAAGC,KAAK;MACpB;IACF;EACF;EAEA,OAAO,CAACP,QAAQ,EAAEC,KAAK,EAAEC,KAAK,CAAC;AACjC;AAAC,IAAAiB,WAAA,OAAAC,2BAAA,CAAAhD,OAAA;AAAA,IAAAiD,SAAA,OAAAD,2BAAA,CAAAhD,OAAA;AAAA,IAAAkD,MAAA,OAAAF,2BAAA,CAAAhD,OAAA;AAAA,IAAAmD,MAAA,OAAAH,2BAAA,CAAAhD,OAAA;AAAA,IAEoBoD,aAAa,GAAAC,OAAA,CAAArD,OAAA,aAAAsD,qBAAA;EA6BhC,SAAAF,cACExB,QAAgC,EAChCC,KAAmC,EACnCC,KAAwB,EACxBL,UAAe,EACf8B,MAA4B,EAC5B;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAAzD,OAAA,QAAAoD,aAAA;IACAI,KAAA,GAAA9C,UAAA,OAAA0C,aAAA,GAAMG,MAAM;IAAEhD,MAAA,CAAAC,cAAA,CAAAgD,KAAA,EAAAT,WAAA;MAAAW,QAAA;MAAAvB,KAAA;IAAA;IAAA5B,MAAA,CAAAC,cAAA,CAAAgD,KAAA,EAAAP,SAAA;MAAAS,QAAA;MAAAvB,KAAA;IAAA;IAAA5B,MAAA,CAAAC,cAAA,CAAAgD,KAAA,EAAAN,MAAA;MAAAQ,QAAA;MAAAvB,KAAA;IAAA;IAAA5B,MAAA,CAAAC,cAAA,CAAAgD,KAAA,EAAAL,MAAA;MAAAO,QAAA;MAAAvB,KAAA;IAAA;IACd,IAAAwB,4BAAA,CAAA3D,OAAA,EAAAwD,KAAA,EAAAP,SAAA,EAAAA,SAAA,IAAiBrB,QAAQ;IACzB,IAAA+B,4BAAA,CAAA3D,OAAA,EAAAwD,KAAA,EAAAN,MAAA,EAAAA,MAAA,IAAcrB,KAAK;IACnB,IAAA8B,4BAAA,CAAA3D,OAAA,EAAAwD,KAAA,EAAAL,MAAA,EAAAA,MAAA,IAAcrB,KAAK;IACnB,IAAA6B,4BAAA,CAAA3D,OAAA,EAAAwD,KAAA,EAAAT,WAAA,EAAAA,WAAA,IAAmBtB,UAAU;IAAC,OAAA+B,KAAA;EAChC;EAAC,IAAAI,UAAA,CAAA5D,OAAA,EAAAoD,aAAA,EAAAE,qBAAA;EAAA,WAAAO,aAAA,CAAA7D,OAAA,EAAAoD,aAAA;IAAAlB,GAAA;IAAAC,KAAA,EAED,SAAA2B,UAAUA,CAAA,EAA2B;MACnC,IAAMhC,KAAwB,GAAG,CAAC,CAAC;MAEnC,IAAMC,IAAI,GAAGxB,MAAM,CAACwB,IAAI,KAAA4B,4BAAA,CAAA3D,OAAA,EAAC,IAAI,EAAAmD,MAAA,EAAAA,MAAA,CAAO,CAAC;MACrC,KAAK,IAAInB,EAAE,GAAG,CAAC,EAAEC,MAAM,GAAGF,IAAI,CAACE,MAAM,EAAED,EAAE,GAAGC,MAAM,EAAED,EAAE,EAAE,EAAE;QACxD,IAAME,GAAG,GAAGH,IAAI,CAACC,EAAE,CAAC;QACpB,IAAMG,KAAK,GAAG,IAAAwB,4BAAA,CAAA3D,OAAA,MAAI,EAAAmD,MAAA,EAAAA,MAAA,EAAQjB,GAAG,CAAC;QAE9B,IAAIC,KAAK,YAAYO,qBAAY,EAAE;UACjCZ,KAAK,CAACI,GAAG,CAAC,GAAGC,KAAK,CAAC2B,UAAU,CAAC,CAAC;QACjC,CAAC,MAAM;UACLhC,KAAK,CAACI,GAAG,CAAC,GAAGC,KAAK;QACpB;MACF;MAIA,OAAO4B,iBAAQ,CAACC,EAAE,KAAK,KAAK,GAAG,KAAAL,4BAAA,CAAA3D,OAAA,EAAC,IAAI,EAAA+C,WAAA,EAAAA,WAAA,GAAcjB,KAAK,CAAC,GAAGA,KAAK;IAClE;EAAC;IAAAI,GAAA;IAAAC,KAAA,EAOD,SAAA8B,yBAAyBA,CAACC,WAAmB,EAA0B;MACrE,IAAMC,eAAe,GAAG,IAAAC,qBAAY,EAACF,WAAW,CAAC;MACjD,IAAMpC,KAAwB,GAC5BqC,eAAe,IAAI,IAAI,GACnB,CAAC,CAAC,GACFA,eAAe,KAAKD,WAAW,GAAA3D,MAAA,CAAA8D,MAAA,KAEzBF,eAAe,IAEnBA,eAAe;MAEvB,IAAMpC,IAAI,GAAGxB,MAAM,CAACwB,IAAI,CAACD,KAAK,CAAC;MAC/B,KAAK,IAAIE,EAAE,GAAG,CAAC,EAAEC,MAAM,GAAGF,IAAI,CAACE,MAAM,EAAED,EAAE,GAAGC,MAAM,EAAED,EAAE,EAAE,EAAE;QACxD,IAAME,GAAG,GAAGH,IAAI,CAACC,EAAE,CAAC;QACpB,IAAMsC,SAAS,GAAG,IAAAX,4BAAA,CAAA3D,OAAA,MAAI,EAAAmD,MAAA,EAAAA,MAAA,EAAQjB,GAAG,CAAC;QAElC,IAAIA,GAAG,KAAK,WAAW,IAAIoC,SAAS,YAAY7B,0BAAiB,EAAE;UACjEX,KAAK,CAACI,GAAG,CAAC,GAAGoC,SAAS,CAACC,8BAA8B,CAGnDC,KAAK,CAACC,OAAO,CAAC3C,KAAK,CAACI,GAAG,CAAC,CAAC,GAAGJ,KAAK,CAACI,GAAG,CAAC,GAAG,EAC3C,CAAC;QACH,CAAC,MAAM,IAAIoC,SAAS,YAAY/B,uBAAc,EAAE;UAC9CT,KAAK,CAACI,GAAG,CAAC,GAAGoC,SAAS,CAACI,0BAA0B,CAAC5C,KAAK,CAACI,GAAG,CAAC,CAAC;QAC/D,CAAC,MAAM,IAAIoC,SAAS,YAAY5B,qBAAY,EAAE;UAC5CZ,KAAK,CAACI,GAAG,CAAC,GAAGoC,SAAS,CAACR,UAAU,CAAC,CAAC;QACrC;MACF;MAIA,OAAOC,iBAAQ,CAACC,EAAE,KAAK,KAAK,GAAG,KAAAL,4BAAA,CAAA3D,OAAA,EAAC,IAAI,EAAA+C,WAAA,EAAAA,WAAA,GAAcjB,KAAK,CAAC,GAAGA,KAAK;IAClE;EAAC;IAAAI,GAAA;IAAAC,KAAA,EAED,SAAAwC,kBAAkBA,CAAA,EAAW;MAC3B,IAAM7C,KAAwB,GAAG,CAAC,CAAC;MAEnC,IAAMF,QAAQ,OAAA+B,4BAAA,CAAA3D,OAAA,EAAG,IAAI,EAAAiD,SAAA,EAAAA,SAAA,CAAU;MAC/B,IAAMpB,KAAK,OAAA8B,4BAAA,CAAA3D,OAAA,EAAG,IAAI,EAAAkD,MAAA,EAAAA,MAAA,CAAO;MACzB,KAAK,IAAIlB,EAAE,GAAG,CAAC,EAAEC,MAAM,GAAGJ,KAAK,CAACI,MAAM,EAAED,EAAE,GAAGC,MAAM,EAAED,EAAE,EAAE,EAAE;QACzD,IAAME,GAAG,GAAGN,QAAQ,CAACI,EAAE,CAAC;QACxB,IAAMK,IAAI,GAAGR,KAAK,CAACG,EAAE,CAAC;QACtBF,KAAK,CAACI,GAAG,CAAC,GAAGG,IAAI,CAACsC,kBAAkB,CAAC,CAAC;MACxC;MAEA,OAAO7C,KAAK;IACd;EAAC;IAAAI,GAAA;IAAAC,KAAA,EAED,SAAAyC,QAAQA,CAAA,EAAS;MACf,IAAM/C,KAAK,OAAA8B,4BAAA,CAAA3D,OAAA,EAAG,IAAI,EAAAkD,MAAA,EAAAA,MAAA,CAAO;MACzB,KAAK,IAAIlB,EAAE,GAAG,CAAC,EAAEC,MAAM,GAAGJ,KAAK,CAACI,MAAM,EAAED,EAAE,GAAGC,MAAM,EAAED,EAAE,EAAE,EAAE;QACzD,IAAMK,IAAI,GAAGR,KAAK,CAACG,EAAE,CAAC;QACtBK,IAAI,CAACwC,UAAU,CAAC,IAAI,CAAC;MACvB;MACAxD,aAAA,CAAA+B,aAAA;IACF;EAAC;IAAAlB,GAAA;IAAAC,KAAA,EAED,SAAA2C,QAAQA,CAAA,EAAS;MACf,IAAMjD,KAAK,OAAA8B,4BAAA,CAAA3D,OAAA,EAAG,IAAI,EAAAkD,MAAA,EAAAA,MAAA,CAAO;MACzB,KAAK,IAAIlB,EAAE,GAAG,CAAC,EAAEC,MAAM,GAAGJ,KAAK,CAACI,MAAM,EAAED,EAAE,GAAGC,MAAM,EAAED,EAAE,EAAE,EAAE;QACzD,IAAMK,IAAI,GAAGR,KAAK,CAACG,EAAE,CAAC;QACtBK,IAAI,CAAC0C,aAAa,CAAC,IAAI,CAAC;MAC1B;MACA1D,aAAA,CAAA+B,aAAA;IACF;EAAC;IAAAlB,GAAA;IAAAC,KAAA,EAED,SAAA6C,YAAYA,CAACC,cAA+B,EAAE;MAC5C,IAAMpD,KAAK,OAAA8B,4BAAA,CAAA3D,OAAA,EAAG,IAAI,EAAAkD,MAAA,EAAAA,MAAA,CAAO;MACzB,KAAK,IAAIlB,EAAE,GAAG,CAAC,EAAEC,MAAM,GAAGJ,KAAK,CAACI,MAAM,EAAED,EAAE,GAAGC,MAAM,EAAED,EAAE,EAAE,EAAE;QACzD,IAAMK,IAAI,GAAGR,KAAK,CAACG,EAAE,CAAC;QACtBK,IAAI,CAAC2C,YAAY,CAACC,cAAc,CAAC;MACnC;MACA5D,aAAA,CAAA+B,aAAA,4BAAmB6B,cAAc;IACnC;EAAC;IAAA/C,GAAA;IAAAC,KAAA,EAED,SAAA+C,iBAAiBA,CAAA,EAAW;MAC1B,IAAMD,cAAc,GAAG,IAAI,CAACE,mBAAmB,CAAC,CAAC;MACjD,IAAMC,WAAgC,GAAG,CAAC,CAAC;MAE3C,IAAMxD,QAAQ,OAAA+B,4BAAA,CAAA3D,OAAA,EAAG,IAAI,EAAAiD,SAAA,EAAAA,SAAA,CAAU;MAC/B,IAAMpB,KAAK,OAAA8B,4BAAA,CAAA3D,OAAA,EAAG,IAAI,EAAAkD,MAAA,EAAAA,MAAA,CAAO;MACzB,KAAK,IAAIlB,EAAE,GAAG,CAAC,EAAEC,MAAM,GAAGJ,KAAK,CAACI,MAAM,EAAED,EAAE,GAAGC,MAAM,EAAED,EAAE,EAAE,EAAE;QACzD,IAAME,GAAG,GAAGN,QAAQ,CAACI,EAAE,CAAC;QACxB,IAAMK,IAAI,GAAGR,KAAK,CAACG,EAAE,CAAC;QACtBK,IAAI,CAAC2C,YAAY,CAACC,cAAc,CAAC;QACjCG,WAAW,CAAClD,GAAG,CAAC,GAAGG,IAAI,CAACgD,cAAc,CAAC,CAAC;MAC1C;MAEA,IAAIzC,OAAO,EAAE;QACX,IAAA0C,wCAAc,EAACF,WAAW,CAAC;MAC7B;MACA,OAAO;QACLG,IAAI,EAAE,OAAO;QACbzD,KAAK,EAAEsD,WAAW;QAClBI,OAAO,EAAE,IAAI,CAACC,YAAY,CAAC;MAC7B,CAAC;IACH;EAAC;IAAAvD,GAAA;IAAAC,KAAA,EA1JD,SAAOK,IAAIA,CACTf,UAAe,EACfC,SAAkC,EAClB;MAChB,IAAMgE,SAAS,GAAG,IAAAtB,qBAAY,EAAC3C,UAAU,CAAC;MAC1C,IAAIiE,SAAS,IAAI,IAAI,EAAE;QACrB,OAAO,IAAI;MACb;MACA,IAAAC,oBAAA,GAAiCnE,mBAAmB,CAClDkE,SAAS,EACThE,SAAS,EACTqC,iBAAQ,CAACC,EAAE,KAAK,KAClB,CAAC;QAAA4B,qBAAA,OAAAC,eAAA,CAAA7F,OAAA,EAAA2F,oBAAA;QAJM/D,QAAQ,GAAAgE,qBAAA;QAAE/D,KAAK,GAAA+D,qBAAA;QAAE9D,KAAK,GAAA8D,qBAAA;MAK7B,IAAI/D,KAAK,CAACI,MAAM,KAAK,CAAC,EAAE;QACtB,OAAO,IAAI;MACb;MACA,OAAO,IAAImB,aAAa,CAACxB,QAAQ,EAAEC,KAAK,EAAEC,KAAK,EAAEL,UAAU,CAAC;IAC9D;EAAC;AAAA,EA3BwCqE,8BAAoB;AA0K/D,IAAMC,WAAW,GAAGxF,MAAM,CAACY,SAAS,CAACd,cAAc;AACnD,IAAM+B,MAAwD,IAAA/C,cAAA,GAE5DkB,MAAM,CAAC6B,MAAM,YAAA/C,cAAA,GAAK,UAAC2G,GAAG,EAAEC,IAAI;EAAA,OAAKF,WAAW,CAACzF,IAAI,CAAC0F,GAAG,EAAEC,IAAI,CAAC;AAAA,CAAC", "ignoreList": []}