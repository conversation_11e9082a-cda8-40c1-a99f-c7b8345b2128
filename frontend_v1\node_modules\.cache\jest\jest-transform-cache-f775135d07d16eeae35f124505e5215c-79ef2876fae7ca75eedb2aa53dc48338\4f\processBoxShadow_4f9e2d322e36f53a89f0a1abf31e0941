f8d57e08a3d49d2419049601e299ba57
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = processBoxShadow;
var _processColor = _interopRequireDefault(require("./processColor"));
function processBoxShadow(rawBoxShadows) {
  var result = [];
  if (rawBoxShadows == null) {
    return result;
  }
  var boxShadowList = typeof rawBoxShadows === 'string' ? parseBoxShadowString(rawBoxShadows.replace(/\n/g, ' ')) : rawBoxShadows;
  for (var rawBoxShadow of boxShadowList) {
    var parsedBoxShadow = {
      offsetX: 0,
      offsetY: 0
    };
    var value = void 0;
    for (var arg in rawBoxShadow) {
      switch (arg) {
        case 'offsetX':
          value = typeof rawBoxShadow.offsetX === 'string' ? parseLength(rawBoxShadow.offsetX) : rawBoxShadow.offsetX;
          if (value == null) {
            return [];
          }
          parsedBoxShadow.offsetX = value;
          break;
        case 'offsetY':
          value = typeof rawBoxShadow.offsetY === 'string' ? parseLength(rawBoxShadow.offsetY) : rawBoxShadow.offsetY;
          if (value == null) {
            return [];
          }
          parsedBoxShadow.offsetY = value;
          break;
        case 'spreadDistance':
          value = typeof rawBoxShadow.spreadDistance === 'string' ? parseLength(rawBoxShadow.spreadDistance) : rawBoxShadow.spreadDistance;
          if (value == null) {
            return [];
          }
          parsedBoxShadow.spreadDistance = value;
          break;
        case 'blurRadius':
          value = typeof rawBoxShadow.blurRadius === 'string' ? parseLength(rawBoxShadow.blurRadius) : rawBoxShadow.blurRadius;
          if (value == null || value < 0) {
            return [];
          }
          parsedBoxShadow.blurRadius = value;
          break;
        case 'color':
          var color = (0, _processColor.default)(rawBoxShadow.color);
          if (color == null) {
            return [];
          }
          parsedBoxShadow.color = color;
          break;
        case 'inset':
          parsedBoxShadow.inset = rawBoxShadow.inset;
      }
    }
    result.push(parsedBoxShadow);
  }
  return result;
}
function parseBoxShadowString(rawBoxShadows) {
  var result = [];
  for (var rawBoxShadow of rawBoxShadows.split(/,(?![^()]*\))/).map(function (bS) {
    return bS.trim();
  }).filter(function (bS) {
    return bS !== '';
  })) {
    var boxShadow = {
      offsetX: 0,
      offsetY: 0
    };
    var offsetX = void 0;
    var offsetY = void 0;
    var keywordDetectedAfterLength = false;
    var lengthCount = 0;
    var args = rawBoxShadow.split(/\s+(?![^(]*\))/);
    for (var arg of args) {
      var processedColor = (0, _processColor.default)(arg);
      if (processedColor != null) {
        if (boxShadow.color != null) {
          return [];
        }
        if (offsetX != null) {
          keywordDetectedAfterLength = true;
        }
        boxShadow.color = arg;
        continue;
      }
      if (arg === 'inset') {
        if (boxShadow.inset != null) {
          return [];
        }
        if (offsetX != null) {
          keywordDetectedAfterLength = true;
        }
        boxShadow.inset = true;
        continue;
      }
      switch (lengthCount) {
        case 0:
          offsetX = arg;
          lengthCount++;
          break;
        case 1:
          if (keywordDetectedAfterLength) {
            return [];
          }
          offsetY = arg;
          lengthCount++;
          break;
        case 2:
          if (keywordDetectedAfterLength) {
            return [];
          }
          boxShadow.blurRadius = arg;
          lengthCount++;
          break;
        case 3:
          if (keywordDetectedAfterLength) {
            return [];
          }
          boxShadow.spreadDistance = arg;
          lengthCount++;
          break;
        default:
          return [];
      }
    }
    if (offsetX == null || offsetY == null) {
      return [];
    }
    boxShadow.offsetX = offsetX;
    boxShadow.offsetY = offsetY;
    result.push(boxShadow);
  }
  return result;
}
function parseLength(length) {
  var argsWithUnitsRegex = /([+-]?\d*(\.\d+)?)([\w\W]+)?/g;
  var match = argsWithUnitsRegex.exec(length);
  if (!match || Number.isNaN(match[1])) {
    return null;
  }
  if (match[3] != null && match[3] !== 'px') {
    return null;
  }
  if (match[3] == null && match[1] !== '0') {
    return null;
  }
  return Number(match[1]);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfcHJvY2Vzc0NvbG9yIiwiX2ludGVyb3BSZXF1aXJlRGVmYXVsdCIsInJlcXVpcmUiLCJwcm9jZXNzQm94U2hhZG93IiwicmF3Qm94U2hhZG93cyIsInJlc3VsdCIsImJveFNoYWRvd0xpc3QiLCJwYXJzZUJveFNoYWRvd1N0cmluZyIsInJlcGxhY2UiLCJyYXdCb3hTaGFkb3ciLCJwYXJzZWRCb3hTaGFkb3ciLCJvZmZzZXRYIiwib2Zmc2V0WSIsInZhbHVlIiwiYXJnIiwicGFyc2VMZW5ndGgiLCJzcHJlYWREaXN0YW5jZSIsImJsdXJSYWRpdXMiLCJjb2xvciIsInByb2Nlc3NDb2xvciIsImluc2V0IiwicHVzaCIsInNwbGl0IiwibWFwIiwiYlMiLCJ0cmltIiwiZmlsdGVyIiwiYm94U2hhZG93Iiwia2V5d29yZERldGVjdGVkQWZ0ZXJMZW5ndGgiLCJsZW5ndGhDb3VudCIsImFyZ3MiLCJwcm9jZXNzZWRDb2xvciIsImxlbmd0aCIsImFyZ3NXaXRoVW5pdHNSZWdleCIsIm1hdGNoIiwiZXhlYyIsIk51bWJlciIsImlzTmFOIl0sInNvdXJjZXMiOlsicHJvY2Vzc0JveFNoYWRvdy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIENvcHlyaWdodCAoYykgTWV0YSBQbGF0Zm9ybXMsIEluYy4gYW5kIGFmZmlsaWF0ZXMuXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2UgZm91bmQgaW4gdGhlXG4gKiBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKlxuICogQGZsb3cgc3RyaWN0LWxvY2FsXG4gKiBAZm9ybWF0XG4gKiBAb25jYWxsIHJlYWN0LW5hdGl2ZVxuICovXG5cbmltcG9ydCB0eXBlIHtQcm9jZXNzZWRDb2xvclZhbHVlfSBmcm9tICcuL3Byb2Nlc3NDb2xvcic7XG5pbXBvcnQgdHlwZSB7Qm94U2hhZG93VmFsdWV9IGZyb20gJy4vU3R5bGVTaGVldFR5cGVzJztcblxuaW1wb3J0IHByb2Nlc3NDb2xvciBmcm9tICcuL3Byb2Nlc3NDb2xvcic7XG5cbmV4cG9ydCB0eXBlIFBhcnNlZEJveFNoYWRvdyA9IHtcbiAgb2Zmc2V0WDogbnVtYmVyLFxuICBvZmZzZXRZOiBudW1iZXIsXG4gIGNvbG9yPzogUHJvY2Vzc2VkQ29sb3JWYWx1ZSxcbiAgYmx1clJhZGl1cz86IG51bWJlcixcbiAgc3ByZWFkRGlzdGFuY2U/OiBudW1iZXIsXG4gIGluc2V0PzogYm9vbGVhbixcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHByb2Nlc3NCb3hTaGFkb3coXG4gIHJhd0JveFNoYWRvd3M6ID8oJFJlYWRPbmx5QXJyYXk8Qm94U2hhZG93VmFsdWU+IHwgc3RyaW5nKSxcbik6IEFycmF5PFBhcnNlZEJveFNoYWRvdz4ge1xuICBjb25zdCByZXN1bHQ6IEFycmF5PFBhcnNlZEJveFNoYWRvdz4gPSBbXTtcbiAgaWYgKHJhd0JveFNoYWRvd3MgPT0gbnVsbCkge1xuICAgIHJldHVybiByZXN1bHQ7XG4gIH1cblxuICBjb25zdCBib3hTaGFkb3dMaXN0ID1cbiAgICB0eXBlb2YgcmF3Qm94U2hhZG93cyA9PT0gJ3N0cmluZydcbiAgICAgID8gcGFyc2VCb3hTaGFkb3dTdHJpbmcocmF3Qm94U2hhZG93cy5yZXBsYWNlKC9cXG4vZywgJyAnKSlcbiAgICAgIDogcmF3Qm94U2hhZG93cztcblxuICBmb3IgKGNvbnN0IHJhd0JveFNoYWRvdyBvZiBib3hTaGFkb3dMaXN0KSB7XG4gICAgY29uc3QgcGFyc2VkQm94U2hhZG93OiBQYXJzZWRCb3hTaGFkb3cgPSB7XG4gICAgICBvZmZzZXRYOiAwLFxuICAgICAgb2Zmc2V0WTogMCxcbiAgICB9O1xuXG4gICAgbGV0IHZhbHVlO1xuICAgIGZvciAoY29uc3QgYXJnIGluIHJhd0JveFNoYWRvdykge1xuICAgICAgc3dpdGNoIChhcmcpIHtcbiAgICAgICAgY2FzZSAnb2Zmc2V0WCc6XG4gICAgICAgICAgdmFsdWUgPVxuICAgICAgICAgICAgdHlwZW9mIHJhd0JveFNoYWRvdy5vZmZzZXRYID09PSAnc3RyaW5nJ1xuICAgICAgICAgICAgICA/IHBhcnNlTGVuZ3RoKHJhd0JveFNoYWRvdy5vZmZzZXRYKVxuICAgICAgICAgICAgICA6IHJhd0JveFNoYWRvdy5vZmZzZXRYO1xuICAgICAgICAgIGlmICh2YWx1ZSA9PSBudWxsKSB7XG4gICAgICAgICAgICByZXR1cm4gW107XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgcGFyc2VkQm94U2hhZG93Lm9mZnNldFggPSB2YWx1ZTtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAnb2Zmc2V0WSc6XG4gICAgICAgICAgdmFsdWUgPVxuICAgICAgICAgICAgdHlwZW9mIHJhd0JveFNoYWRvdy5vZmZzZXRZID09PSAnc3RyaW5nJ1xuICAgICAgICAgICAgICA/IHBhcnNlTGVuZ3RoKHJhd0JveFNoYWRvdy5vZmZzZXRZKVxuICAgICAgICAgICAgICA6IHJhd0JveFNoYWRvdy5vZmZzZXRZO1xuICAgICAgICAgIGlmICh2YWx1ZSA9PSBudWxsKSB7XG4gICAgICAgICAgICByZXR1cm4gW107XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgcGFyc2VkQm94U2hhZG93Lm9mZnNldFkgPSB2YWx1ZTtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAnc3ByZWFkRGlzdGFuY2UnOlxuICAgICAgICAgIHZhbHVlID1cbiAgICAgICAgICAgIHR5cGVvZiByYXdCb3hTaGFkb3cuc3ByZWFkRGlzdGFuY2UgPT09ICdzdHJpbmcnXG4gICAgICAgICAgICAgID8gcGFyc2VMZW5ndGgocmF3Qm94U2hhZG93LnNwcmVhZERpc3RhbmNlKVxuICAgICAgICAgICAgICA6IHJhd0JveFNoYWRvdy5zcHJlYWREaXN0YW5jZTtcbiAgICAgICAgICBpZiAodmFsdWUgPT0gbnVsbCkge1xuICAgICAgICAgICAgcmV0dXJuIFtdO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIHBhcnNlZEJveFNoYWRvdy5zcHJlYWREaXN0YW5jZSA9IHZhbHVlO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlICdibHVyUmFkaXVzJzpcbiAgICAgICAgICB2YWx1ZSA9XG4gICAgICAgICAgICB0eXBlb2YgcmF3Qm94U2hhZG93LmJsdXJSYWRpdXMgPT09ICdzdHJpbmcnXG4gICAgICAgICAgICAgID8gcGFyc2VMZW5ndGgocmF3Qm94U2hhZG93LmJsdXJSYWRpdXMpXG4gICAgICAgICAgICAgIDogcmF3Qm94U2hhZG93LmJsdXJSYWRpdXM7XG4gICAgICAgICAgaWYgKHZhbHVlID09IG51bGwgfHwgdmFsdWUgPCAwKSB7XG4gICAgICAgICAgICByZXR1cm4gW107XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgcGFyc2VkQm94U2hhZG93LmJsdXJSYWRpdXMgPSB2YWx1ZTtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAnY29sb3InOlxuICAgICAgICAgIGNvbnN0IGNvbG9yID0gcHJvY2Vzc0NvbG9yKHJhd0JveFNoYWRvdy5jb2xvcik7XG4gICAgICAgICAgaWYgKGNvbG9yID09IG51bGwpIHtcbiAgICAgICAgICAgIHJldHVybiBbXTtcbiAgICAgICAgICB9XG5cbiAgICAgICAgICBwYXJzZWRCb3hTaGFkb3cuY29sb3IgPSBjb2xvcjtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAnaW5zZXQnOlxuICAgICAgICAgIHBhcnNlZEJveFNoYWRvdy5pbnNldCA9IHJhd0JveFNoYWRvdy5pbnNldDtcbiAgICAgIH1cbiAgICB9XG4gICAgcmVzdWx0LnB1c2gocGFyc2VkQm94U2hhZG93KTtcbiAgfVxuICByZXR1cm4gcmVzdWx0O1xufVxuXG5mdW5jdGlvbiBwYXJzZUJveFNoYWRvd1N0cmluZyhyYXdCb3hTaGFkb3dzOiBzdHJpbmcpOiBBcnJheTxCb3hTaGFkb3dWYWx1ZT4ge1xuICBsZXQgcmVzdWx0OiBBcnJheTxCb3hTaGFkb3dWYWx1ZT4gPSBbXTtcblxuICBmb3IgKGNvbnN0IHJhd0JveFNoYWRvdyBvZiByYXdCb3hTaGFkb3dzXG4gICAgLnNwbGl0KC8sKD8hW14oKV0qXFwpKS8pIC8vIHNwbGl0IGJ5IGNvbW1hIHRoYXQgaXMgbm90IGluIHBhcmVudGhlc2lzXG4gICAgLm1hcChiUyA9PiBiUy50cmltKCkpXG4gICAgLmZpbHRlcihiUyA9PiBiUyAhPT0gJycpKSB7XG4gICAgY29uc3QgYm94U2hhZG93OiBCb3hTaGFkb3dWYWx1ZSA9IHtcbiAgICAgIG9mZnNldFg6IDAsXG4gICAgICBvZmZzZXRZOiAwLFxuICAgIH07XG4gICAgbGV0IG9mZnNldFg6IG51bWJlciB8IHN0cmluZztcbiAgICBsZXQgb2Zmc2V0WTogbnVtYmVyIHwgc3RyaW5nO1xuICAgIGxldCBrZXl3b3JkRGV0ZWN0ZWRBZnRlckxlbmd0aCA9IGZhbHNlO1xuXG4gICAgbGV0IGxlbmd0aENvdW50ID0gMDtcblxuICAgIC8vIHNwbGl0IHJhd0JveFNoYWRvdyBzdHJpbmcgYnkgYWxsIHdoaXRlc3BhY2VzIHRoYXQgYXJlIG5vdCBpbiBwYXJlbnRoZXNpc1xuICAgIGNvbnN0IGFyZ3MgPSByYXdCb3hTaGFkb3cuc3BsaXQoL1xccysoPyFbXihdKlxcKSkvKTtcbiAgICBmb3IgKGNvbnN0IGFyZyBvZiBhcmdzKSB7XG4gICAgICBjb25zdCBwcm9jZXNzZWRDb2xvciA9IHByb2Nlc3NDb2xvcihhcmcpO1xuICAgICAgaWYgKHByb2Nlc3NlZENvbG9yICE9IG51bGwpIHtcbiAgICAgICAgaWYgKGJveFNoYWRvdy5jb2xvciAhPSBudWxsKSB7XG4gICAgICAgICAgcmV0dXJuIFtdO1xuICAgICAgICB9XG4gICAgICAgIGlmIChvZmZzZXRYICE9IG51bGwpIHtcbiAgICAgICAgICBrZXl3b3JkRGV0ZWN0ZWRBZnRlckxlbmd0aCA9IHRydWU7XG4gICAgICAgIH1cbiAgICAgICAgYm94U2hhZG93LmNvbG9yID0gYXJnO1xuICAgICAgICBjb250aW51ZTtcbiAgICAgIH1cblxuICAgICAgaWYgKGFyZyA9PT0gJ2luc2V0Jykge1xuICAgICAgICBpZiAoYm94U2hhZG93Lmluc2V0ICE9IG51bGwpIHtcbiAgICAgICAgICByZXR1cm4gW107XG4gICAgICAgIH1cbiAgICAgICAgaWYgKG9mZnNldFggIT0gbnVsbCkge1xuICAgICAgICAgIGtleXdvcmREZXRlY3RlZEFmdGVyTGVuZ3RoID0gdHJ1ZTtcbiAgICAgICAgfVxuICAgICAgICBib3hTaGFkb3cuaW5zZXQgPSB0cnVlO1xuICAgICAgICBjb250aW51ZTtcbiAgICAgIH1cblxuICAgICAgc3dpdGNoIChsZW5ndGhDb3VudCkge1xuICAgICAgICBjYXNlIDA6XG4gICAgICAgICAgb2Zmc2V0WCA9IGFyZztcbiAgICAgICAgICBsZW5ndGhDb3VudCsrO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIDE6XG4gICAgICAgICAgaWYgKGtleXdvcmREZXRlY3RlZEFmdGVyTGVuZ3RoKSB7XG4gICAgICAgICAgICByZXR1cm4gW107XG4gICAgICAgICAgfVxuICAgICAgICAgIG9mZnNldFkgPSBhcmc7XG4gICAgICAgICAgbGVuZ3RoQ291bnQrKztcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAyOlxuICAgICAgICAgIGlmIChrZXl3b3JkRGV0ZWN0ZWRBZnRlckxlbmd0aCkge1xuICAgICAgICAgICAgcmV0dXJuIFtdO1xuICAgICAgICAgIH1cbiAgICAgICAgICBib3hTaGFkb3cuYmx1clJhZGl1cyA9IGFyZztcbiAgICAgICAgICBsZW5ndGhDb3VudCsrO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIDM6XG4gICAgICAgICAgaWYgKGtleXdvcmREZXRlY3RlZEFmdGVyTGVuZ3RoKSB7XG4gICAgICAgICAgICByZXR1cm4gW107XG4gICAgICAgICAgfVxuICAgICAgICAgIGJveFNoYWRvdy5zcHJlYWREaXN0YW5jZSA9IGFyZztcbiAgICAgICAgICBsZW5ndGhDb3VudCsrO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgIHJldHVybiBbXTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICBpZiAob2Zmc2V0WCA9PSBudWxsIHx8IG9mZnNldFkgPT0gbnVsbCkge1xuICAgICAgcmV0dXJuIFtdO1xuICAgIH1cblxuICAgIGJveFNoYWRvdy5vZmZzZXRYID0gb2Zmc2V0WDtcbiAgICBib3hTaGFkb3cub2Zmc2V0WSA9IG9mZnNldFk7XG5cbiAgICByZXN1bHQucHVzaChib3hTaGFkb3cpO1xuICB9XG4gIHJldHVybiByZXN1bHQ7XG59XG5cbmZ1bmN0aW9uIHBhcnNlTGVuZ3RoKGxlbmd0aDogc3RyaW5nKTogP251bWJlciB7XG4gIC8vIG1hdGNoZXMgb24gYXJncyB3aXRoIHVuaXRzIGxpa2UgXCIxLjUgNSUgLTgwZGVnXCJcbiAgY29uc3QgYXJnc1dpdGhVbml0c1JlZ2V4ID0gLyhbKy1dP1xcZCooXFwuXFxkKyk/KShbXFx3XFxXXSspPy9nO1xuICBjb25zdCBtYXRjaCA9IGFyZ3NXaXRoVW5pdHNSZWdleC5leGVjKGxlbmd0aCk7XG5cbiAgaWYgKCFtYXRjaCB8fCBOdW1iZXIuaXNOYU4obWF0Y2hbMV0pKSB7XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cblxuICBpZiAobWF0Y2hbM10gIT0gbnVsbCAmJiBtYXRjaFszXSAhPT0gJ3B4Jykge1xuICAgIHJldHVybiBudWxsO1xuICB9XG5cbiAgaWYgKG1hdGNoWzNdID09IG51bGwgJiYgbWF0Y2hbMV0gIT09ICcwJykge1xuICAgIHJldHVybiBudWxsO1xuICB9XG5cbiAgcmV0dXJuIE51bWJlcihtYXRjaFsxXSk7XG59XG4iXSwibWFwcGluZ3MiOiI7Ozs7O0FBY0EsSUFBQUEsYUFBQSxHQUFBQyxzQkFBQSxDQUFBQyxPQUFBO0FBV2UsU0FBU0MsZ0JBQWdCQSxDQUN0Q0MsYUFBeUQsRUFDakM7RUFDeEIsSUFBTUMsTUFBOEIsR0FBRyxFQUFFO0VBQ3pDLElBQUlELGFBQWEsSUFBSSxJQUFJLEVBQUU7SUFDekIsT0FBT0MsTUFBTTtFQUNmO0VBRUEsSUFBTUMsYUFBYSxHQUNqQixPQUFPRixhQUFhLEtBQUssUUFBUSxHQUM3Qkcsb0JBQW9CLENBQUNILGFBQWEsQ0FBQ0ksT0FBTyxDQUFDLEtBQUssRUFBRSxHQUFHLENBQUMsQ0FBQyxHQUN2REosYUFBYTtFQUVuQixLQUFLLElBQU1LLFlBQVksSUFBSUgsYUFBYSxFQUFFO0lBQ3hDLElBQU1JLGVBQWdDLEdBQUc7TUFDdkNDLE9BQU8sRUFBRSxDQUFDO01BQ1ZDLE9BQU8sRUFBRTtJQUNYLENBQUM7SUFFRCxJQUFJQyxLQUFLO0lBQ1QsS0FBSyxJQUFNQyxHQUFHLElBQUlMLFlBQVksRUFBRTtNQUM5QixRQUFRSyxHQUFHO1FBQ1QsS0FBSyxTQUFTO1VBQ1pELEtBQUssR0FDSCxPQUFPSixZQUFZLENBQUNFLE9BQU8sS0FBSyxRQUFRLEdBQ3BDSSxXQUFXLENBQUNOLFlBQVksQ0FBQ0UsT0FBTyxDQUFDLEdBQ2pDRixZQUFZLENBQUNFLE9BQU87VUFDMUIsSUFBSUUsS0FBSyxJQUFJLElBQUksRUFBRTtZQUNqQixPQUFPLEVBQUU7VUFDWDtVQUVBSCxlQUFlLENBQUNDLE9BQU8sR0FBR0UsS0FBSztVQUMvQjtRQUNGLEtBQUssU0FBUztVQUNaQSxLQUFLLEdBQ0gsT0FBT0osWUFBWSxDQUFDRyxPQUFPLEtBQUssUUFBUSxHQUNwQ0csV0FBVyxDQUFDTixZQUFZLENBQUNHLE9BQU8sQ0FBQyxHQUNqQ0gsWUFBWSxDQUFDRyxPQUFPO1VBQzFCLElBQUlDLEtBQUssSUFBSSxJQUFJLEVBQUU7WUFDakIsT0FBTyxFQUFFO1VBQ1g7VUFFQUgsZUFBZSxDQUFDRSxPQUFPLEdBQUdDLEtBQUs7VUFDL0I7UUFDRixLQUFLLGdCQUFnQjtVQUNuQkEsS0FBSyxHQUNILE9BQU9KLFlBQVksQ0FBQ08sY0FBYyxLQUFLLFFBQVEsR0FDM0NELFdBQVcsQ0FBQ04sWUFBWSxDQUFDTyxjQUFjLENBQUMsR0FDeENQLFlBQVksQ0FBQ08sY0FBYztVQUNqQyxJQUFJSCxLQUFLLElBQUksSUFBSSxFQUFFO1lBQ2pCLE9BQU8sRUFBRTtVQUNYO1VBRUFILGVBQWUsQ0FBQ00sY0FBYyxHQUFHSCxLQUFLO1VBQ3RDO1FBQ0YsS0FBSyxZQUFZO1VBQ2ZBLEtBQUssR0FDSCxPQUFPSixZQUFZLENBQUNRLFVBQVUsS0FBSyxRQUFRLEdBQ3ZDRixXQUFXLENBQUNOLFlBQVksQ0FBQ1EsVUFBVSxDQUFDLEdBQ3BDUixZQUFZLENBQUNRLFVBQVU7VUFDN0IsSUFBSUosS0FBSyxJQUFJLElBQUksSUFBSUEsS0FBSyxHQUFHLENBQUMsRUFBRTtZQUM5QixPQUFPLEVBQUU7VUFDWDtVQUVBSCxlQUFlLENBQUNPLFVBQVUsR0FBR0osS0FBSztVQUNsQztRQUNGLEtBQUssT0FBTztVQUNWLElBQU1LLEtBQUssR0FBRyxJQUFBQyxxQkFBWSxFQUFDVixZQUFZLENBQUNTLEtBQUssQ0FBQztVQUM5QyxJQUFJQSxLQUFLLElBQUksSUFBSSxFQUFFO1lBQ2pCLE9BQU8sRUFBRTtVQUNYO1VBRUFSLGVBQWUsQ0FBQ1EsS0FBSyxHQUFHQSxLQUFLO1VBQzdCO1FBQ0YsS0FBSyxPQUFPO1VBQ1ZSLGVBQWUsQ0FBQ1UsS0FBSyxHQUFHWCxZQUFZLENBQUNXLEtBQUs7TUFDOUM7SUFDRjtJQUNBZixNQUFNLENBQUNnQixJQUFJLENBQUNYLGVBQWUsQ0FBQztFQUM5QjtFQUNBLE9BQU9MLE1BQU07QUFDZjtBQUVBLFNBQVNFLG9CQUFvQkEsQ0FBQ0gsYUFBcUIsRUFBeUI7RUFDMUUsSUFBSUMsTUFBNkIsR0FBRyxFQUFFO0VBRXRDLEtBQUssSUFBTUksWUFBWSxJQUFJTCxhQUFhLENBQ3JDa0IsS0FBSyxDQUFDLGVBQWUsQ0FBQyxDQUN0QkMsR0FBRyxDQUFDLFVBQUFDLEVBQUU7SUFBQSxPQUFJQSxFQUFFLENBQUNDLElBQUksQ0FBQyxDQUFDO0VBQUEsRUFBQyxDQUNwQkMsTUFBTSxDQUFDLFVBQUFGLEVBQUU7SUFBQSxPQUFJQSxFQUFFLEtBQUssRUFBRTtFQUFBLEVBQUMsRUFBRTtJQUMxQixJQUFNRyxTQUF5QixHQUFHO01BQ2hDaEIsT0FBTyxFQUFFLENBQUM7TUFDVkMsT0FBTyxFQUFFO0lBQ1gsQ0FBQztJQUNELElBQUlELE9BQXdCO0lBQzVCLElBQUlDLE9BQXdCO0lBQzVCLElBQUlnQiwwQkFBMEIsR0FBRyxLQUFLO0lBRXRDLElBQUlDLFdBQVcsR0FBRyxDQUFDO0lBR25CLElBQU1DLElBQUksR0FBR3JCLFlBQVksQ0FBQ2EsS0FBSyxDQUFDLGdCQUFnQixDQUFDO0lBQ2pELEtBQUssSUFBTVIsR0FBRyxJQUFJZ0IsSUFBSSxFQUFFO01BQ3RCLElBQU1DLGNBQWMsR0FBRyxJQUFBWixxQkFBWSxFQUFDTCxHQUFHLENBQUM7TUFDeEMsSUFBSWlCLGNBQWMsSUFBSSxJQUFJLEVBQUU7UUFDMUIsSUFBSUosU0FBUyxDQUFDVCxLQUFLLElBQUksSUFBSSxFQUFFO1VBQzNCLE9BQU8sRUFBRTtRQUNYO1FBQ0EsSUFBSVAsT0FBTyxJQUFJLElBQUksRUFBRTtVQUNuQmlCLDBCQUEwQixHQUFHLElBQUk7UUFDbkM7UUFDQUQsU0FBUyxDQUFDVCxLQUFLLEdBQUdKLEdBQUc7UUFDckI7TUFDRjtNQUVBLElBQUlBLEdBQUcsS0FBSyxPQUFPLEVBQUU7UUFDbkIsSUFBSWEsU0FBUyxDQUFDUCxLQUFLLElBQUksSUFBSSxFQUFFO1VBQzNCLE9BQU8sRUFBRTtRQUNYO1FBQ0EsSUFBSVQsT0FBTyxJQUFJLElBQUksRUFBRTtVQUNuQmlCLDBCQUEwQixHQUFHLElBQUk7UUFDbkM7UUFDQUQsU0FBUyxDQUFDUCxLQUFLLEdBQUcsSUFBSTtRQUN0QjtNQUNGO01BRUEsUUFBUVMsV0FBVztRQUNqQixLQUFLLENBQUM7VUFDSmxCLE9BQU8sR0FBR0csR0FBRztVQUNiZSxXQUFXLEVBQUU7VUFDYjtRQUNGLEtBQUssQ0FBQztVQUNKLElBQUlELDBCQUEwQixFQUFFO1lBQzlCLE9BQU8sRUFBRTtVQUNYO1VBQ0FoQixPQUFPLEdBQUdFLEdBQUc7VUFDYmUsV0FBVyxFQUFFO1VBQ2I7UUFDRixLQUFLLENBQUM7VUFDSixJQUFJRCwwQkFBMEIsRUFBRTtZQUM5QixPQUFPLEVBQUU7VUFDWDtVQUNBRCxTQUFTLENBQUNWLFVBQVUsR0FBR0gsR0FBRztVQUMxQmUsV0FBVyxFQUFFO1VBQ2I7UUFDRixLQUFLLENBQUM7VUFDSixJQUFJRCwwQkFBMEIsRUFBRTtZQUM5QixPQUFPLEVBQUU7VUFDWDtVQUNBRCxTQUFTLENBQUNYLGNBQWMsR0FBR0YsR0FBRztVQUM5QmUsV0FBVyxFQUFFO1VBQ2I7UUFDRjtVQUNFLE9BQU8sRUFBRTtNQUNiO0lBQ0Y7SUFFQSxJQUFJbEIsT0FBTyxJQUFJLElBQUksSUFBSUMsT0FBTyxJQUFJLElBQUksRUFBRTtNQUN0QyxPQUFPLEVBQUU7SUFDWDtJQUVBZSxTQUFTLENBQUNoQixPQUFPLEdBQUdBLE9BQU87SUFDM0JnQixTQUFTLENBQUNmLE9BQU8sR0FBR0EsT0FBTztJQUUzQlAsTUFBTSxDQUFDZ0IsSUFBSSxDQUFDTSxTQUFTLENBQUM7RUFDeEI7RUFDQSxPQUFPdEIsTUFBTTtBQUNmO0FBRUEsU0FBU1UsV0FBV0EsQ0FBQ2lCLE1BQWMsRUFBVztFQUU1QyxJQUFNQyxrQkFBa0IsR0FBRywrQkFBK0I7RUFDMUQsSUFBTUMsS0FBSyxHQUFHRCxrQkFBa0IsQ0FBQ0UsSUFBSSxDQUFDSCxNQUFNLENBQUM7RUFFN0MsSUFBSSxDQUFDRSxLQUFLLElBQUlFLE1BQU0sQ0FBQ0MsS0FBSyxDQUFDSCxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRTtJQUNwQyxPQUFPLElBQUk7RUFDYjtFQUVBLElBQUlBLEtBQUssQ0FBQyxDQUFDLENBQUMsSUFBSSxJQUFJLElBQUlBLEtBQUssQ0FBQyxDQUFDLENBQUMsS0FBSyxJQUFJLEVBQUU7SUFDekMsT0FBTyxJQUFJO0VBQ2I7RUFFQSxJQUFJQSxLQUFLLENBQUMsQ0FBQyxDQUFDLElBQUksSUFBSSxJQUFJQSxLQUFLLENBQUMsQ0FBQyxDQUFDLEtBQUssR0FBRyxFQUFFO0lBQ3hDLE9BQU8sSUFBSTtFQUNiO0VBRUEsT0FBT0UsTUFBTSxDQUFDRixLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUM7QUFDekIiLCJpZ25vcmVMaXN0IjpbXX0=