/**
 * Data Loading Fallback Component - Handles data loading states and errors
 *
 * Component Contract:
 * - Displays appropriate UI for loading, error, and empty states
 * - Provides retry functionality for failed data loads
 * - Supports skeleton loading placeholders
 * - Implements accessibility features
 * - Tracks loading performance metrics
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useEffect } from 'react';
import { View, Text, StyleSheet, ActivityIndicator, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import { performanceMonitor } from '../../services/performanceMonitor';
import NetworkErrorFallback from './NetworkErrorFallback';

interface DataLoadingFallbackProps {
  // State flags
  isLoading: boolean;
  isError: boolean;
  isEmpty?: boolean;
  
  // Data and error info
  error?: Error;
  retryCount?: number;
  maxRetries?: number;
  
  // Content
  loadingMessage?: string;
  emptyMessage?: string;
  emptyIcon?: string;
  
  // Actions
  onRetry?: () => void;
  onEmptyAction?: () => void;
  emptyActionText?: string;
  
  // Customization
  loadingComponent?: React.ReactNode;
  errorComponent?: React.ReactNode;
  emptyComponent?: React.ReactNode;
  
  // Behavior
  loadingDelay?: number;
  showLoadingIndicator?: boolean;
  
  // Accessibility
  testID?: string;
}

export const DataLoadingFallback: React.FC<DataLoadingFallbackProps> = ({
  // State flags
  isLoading,
  isError,
  isEmpty = false,
  
  // Data and error info
  error,
  retryCount = 0,
  maxRetries = 3,
  
  // Content
  loadingMessage = 'Loading data...',
  emptyMessage = 'No data available',
  emptyIcon = 'document-outline',
  
  // Actions
  onRetry,
  onEmptyAction,
  emptyActionText = 'Refresh',
  
  // Customization
  loadingComponent,
  errorComponent,
  emptyComponent,
  
  // Behavior
  loadingDelay = 300,
  showLoadingIndicator = true,
  
  // Accessibility
  testID = 'data-loading-fallback',
}) => {
  const { colors } = useTheme();
  
  // Track loading performance
  useEffect(() => {
    if (isLoading) {
      const startTime = Date.now();
      
      return () => {
        const loadTime = Date.now() - startTime;
        
        // Only track if loading took more than the delay
        if (loadTime > loadingDelay) {
          performanceMonitor.trackUserInteraction('data_loading', loadTime, {
            success: !isError,
            retryCount,
          });
        }
      };
    }
  }, [isLoading, isError, loadingDelay, retryCount]);
  
  // Handle retry with performance tracking
  const handleRetry = () => {
    performanceMonitor.trackUserInteraction('data_loading_retry', 0, {
      errorType: error?.name || 'DataLoadingError',
      retryCount: retryCount + 1,
      errorMessage: error?.message,
    });
    
    onRetry?.();
  };
  
  // Handle empty action
  const handleEmptyAction = () => {
    performanceMonitor.trackUserInteraction('empty_state_action', 0, {
      actionText: emptyActionText,
    });
    
    onEmptyAction?.();
  };
  
  // Render loading state
  if (isLoading) {
    if (loadingComponent) {
      return <>{loadingComponent}</>;
    }
    
    return (
      <View 
        style={[styles.container, { backgroundColor: colors.background.primary }]}
        testID={`${testID}-loading`}
        accessibilityLabel={loadingMessage}
        accessibilityRole="progressbar"
        accessibilityState={{ busy: true }}
      >
        {showLoadingIndicator && (
          <ActivityIndicator
            size="large"
            color={colors.primary.default}
            style={styles.loadingIndicator}
          />
        )}
        <Text style={[styles.loadingText, { color: colors.text.secondary }]}>
          {loadingMessage}
        </Text>
      </View>
    );
  }
  
  // Render error state
  if (isError) {
    if (errorComponent) {
      return <>{errorComponent}</>;
    }
    
    // Use NetworkErrorFallback for network errors
    if (error?.name === 'NetworkError' || 
        error?.message?.includes('network') || 
        error?.message?.includes('connection') ||
        error?.message?.includes('timeout')) {
      return (
        <NetworkErrorFallback
          error={error}
          onRetry={handleRetry}
          retryCount={retryCount}
          maxRetries={maxRetries}
          testID={`${testID}-network-error`}
        />
      );
    }
    
    // Generic error fallback
    return (
      <View 
        style={[styles.container, { backgroundColor: colors.background.primary }]}
        testID={`${testID}-error`}
        accessibilityLabel="Error loading data"
        accessibilityRole="alert"
      >
        <View style={[styles.iconContainer, { backgroundColor: colors.errorLight }]}>
          <Ionicons
            name="alert-circle-outline"
            size={48}
            color={colors.error}
          />
        </View>
        <Text style={[styles.errorTitle, { color: colors.text.primary }]}>
          Unable to Load Data
        </Text>
        <Text style={[styles.errorMessage, { color: colors.text.secondary }]}>
          {error?.message || 'Something went wrong while loading the data.'}
        </Text>
        
        {retryCount < maxRetries && (
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: colors.interactive.primary.default }]}
            onPress={handleRetry}
            testID={`${testID}-retry-button`}
            accessibilityRole="button"
            accessibilityLabel="Retry loading data"
          >
            <Ionicons
              name="refresh-outline"
              size={20}
              color={colors.interactive.primary.text}
              style={styles.buttonIcon}
            />
            <Text style={[styles.buttonText, { color: colors.interactive.primary.text }]}>
              Try Again
            </Text>
          </TouchableOpacity>
        )}
        
        {/* Show debug info in development */}
        {__DEV__ && error && (
          <View style={styles.debugContainer}>
            <Text style={[styles.debugText, { color: colors.text.tertiary }]}>
              {error.stack || error.message}
            </Text>
          </View>
        )}
      </View>
    );
  }
  
  // Render empty state
  if (isEmpty) {
    if (emptyComponent) {
      return <>{emptyComponent}</>;
    }
    
    return (
      <View 
        style={[styles.container, { backgroundColor: colors.background.primary }]}
        testID={`${testID}-empty`}
        accessibilityLabel={emptyMessage}
        accessibilityRole="text"
      >
        <View style={[styles.iconContainer, { backgroundColor: colors.background.secondary }]}>
          <Ionicons
            name={emptyIcon as any}
            size={48}
            color={colors.text.secondary}
          />
        </View>
        <Text style={[styles.emptyText, { color: colors.text.secondary }]}>
          {emptyMessage}
        </Text>
        
        {onEmptyAction && (
          <TouchableOpacity
            style={[styles.emptyActionButton, { 
              backgroundColor: colors.interactive.secondary.default,
              borderColor: colors.interactive.secondary.border,
            }]}
            onPress={handleEmptyAction}
            testID={`${testID}-empty-action-button`}
            accessibilityRole="button"
            accessibilityLabel={emptyActionText}
          >
            <Text style={[styles.emptyActionText, { color: colors.interactive.secondary.text }]}>
              {emptyActionText}
            </Text>
          </TouchableOpacity>
        )}
      </View>
    );
  }
  
  // If none of the states match, return null
  return null;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingIndicator: {
    marginBottom: 16,
  },
  loadingText: {
    fontSize: 16,
    textAlign: 'center',
  },
  iconContainer: {
    width: 96,
    height: 96,
    borderRadius: 48,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  errorMessage: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
    maxWidth: 300,
    lineHeight: 22,
  },
  retryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  buttonIcon: {
    marginRight: 8,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
    maxWidth: 300,
    lineHeight: 22,
  },
  emptyActionButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    borderWidth: 1,
  },
  emptyActionText: {
    fontSize: 16,
    fontWeight: '500',
  },
  debugContainer: {
    marginTop: 24,
    padding: 12,
    borderRadius: 8,
    maxWidth: '100%',
    maxHeight: 200,
    overflow: 'scroll',
  },
  debugText: {
    fontSize: 12,
    fontFamily: 'monospace',
  },
});

export default DataLoadingFallback;
