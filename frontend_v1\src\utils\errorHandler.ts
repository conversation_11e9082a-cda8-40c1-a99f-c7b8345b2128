/**
 * Comprehensive Error Handling System
 * Provides centralized error handling, logging, and user feedback
 */

import { Alert } from 'react-native';
import * as Haptics from 'expo-haptics';

// Error types
export enum ErrorType {
  NETWORK = 'NETWORK',
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  NOT_FOUND = 'NOT_FOUND',
  SERVER = 'SERVER',
  CLIENT = 'CLIENT',
  UNKNOWN = 'UNKNOWN',
}

// Error severity levels
export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

// Error interface
export interface AppError {
  id: string;
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  userMessage?: string;
  details?: any;
  timestamp: Date;
  stack?: string;
  context?: Record<string, any>;
}

// Error handler configuration
interface ErrorHandlerConfig {
  enableLogging: boolean;
  enableHaptics: boolean;
  enableUserNotification: boolean;
  logToConsole: boolean;
  logToRemote: boolean;
}

class ErrorHandler {
  private config: ErrorHandlerConfig;
  private errorQueue: AppError[] = [];
  private maxQueueSize = 100;

  constructor(config: Partial<ErrorHandlerConfig> = {}) {
    this.config = {
      enableLogging: true,
      enableHaptics: true,
      enableUserNotification: true,
      logToConsole: __DEV__,
      logToRemote: !__DEV__,
      ...config,
    };
  }

  /**
   * Handle an error with comprehensive logging and user feedback
   */
  handleError(error: Error | AppError, context?: Record<string, any>): AppError {
    const appError = this.normalizeError(error, context);
    
    // Log the error
    if (this.config.enableLogging) {
      this.logError(appError);
    }
    
    // Provide haptic feedback for user errors
    if (this.config.enableHaptics && appError.severity !== ErrorSeverity.LOW) {
      this.provideHapticFeedback(appError.severity);
    }
    
    // Show user notification if appropriate
    if (this.config.enableUserNotification && appError.userMessage) {
      this.showUserNotification(appError);
    }
    
    // Add to error queue for analytics
    this.addToQueue(appError);
    
    return appError;
  }

  /**
   * Handle network errors specifically
   */
  handleNetworkError(error: Error, context?: Record<string, any>): AppError {
    const appError: AppError = {
      id: this.generateErrorId(),
      type: ErrorType.NETWORK,
      severity: ErrorSeverity.MEDIUM,
      message: error.message,
      userMessage: 'Network connection issue. Please check your internet connection and try again.',
      details: error,
      timestamp: new Date(),
      stack: error.stack,
      context,
    };
    
    return this.handleError(appError, context);
  }

  /**
   * Handle validation errors
   */
  handleValidationError(message: string, field?: string, context?: Record<string, any>): AppError {
    const appError: AppError = {
      id: this.generateErrorId(),
      type: ErrorType.VALIDATION,
      severity: ErrorSeverity.LOW,
      message,
      userMessage: message,
      details: { field },
      timestamp: new Date(),
      context,
    };
    
    return this.handleError(appError, context);
  }

  /**
   * Handle authentication errors
   */
  handleAuthError(error: Error, context?: Record<string, any>): AppError {
    const appError: AppError = {
      id: this.generateErrorId(),
      type: ErrorType.AUTHENTICATION,
      severity: ErrorSeverity.HIGH,
      message: error.message,
      userMessage: 'Authentication failed. Please log in again.',
      details: error,
      timestamp: new Date(),
      stack: error.stack,
      context,
    };
    
    return this.handleError(appError, context);
  }

  /**
   * Handle critical system errors
   */
  handleCriticalError(error: Error, context?: Record<string, any>): AppError {
    const appError: AppError = {
      id: this.generateErrorId(),
      type: ErrorType.UNKNOWN,
      severity: ErrorSeverity.CRITICAL,
      message: error.message,
      userMessage: 'A critical error occurred. The app will restart.',
      details: error,
      timestamp: new Date(),
      stack: error.stack,
      context,
    };
    
    return this.handleError(appError, context);
  }

  /**
   * Normalize different error types to AppError
   */
  private normalizeError(error: Error | AppError, context?: Record<string, any>): AppError {
    if (this.isAppError(error)) {
      return { ...error, context: { ...error.context, ...context } };
    }
    
    return {
      id: this.generateErrorId(),
      type: this.determineErrorType(error),
      severity: this.determineSeverity(error),
      message: error.message,
      userMessage: this.generateUserMessage(error),
      details: error,
      timestamp: new Date(),
      stack: error.stack,
      context,
    };
  }

  /**
   * Log error to console and/or remote service
   */
  private logError(error: AppError): void {
    if (this.config.logToConsole) {
      console.group(`🚨 Error [${error.severity}] - ${error.type}`);
      console.error('Message:', error.message);
      console.error('User Message:', error.userMessage);
      console.error('Details:', error.details);
      console.error('Context:', error.context);
      console.error('Stack:', error.stack);
      console.groupEnd();
    }
    
    if (this.config.logToRemote) {
      // In a real app, send to remote logging service
      // this.sendToRemoteLogging(error);
    }
  }

  /**
   * Provide haptic feedback based on error severity
   */
  private provideHapticFeedback(severity: ErrorSeverity): void {
    try {
      switch (severity) {
        case ErrorSeverity.LOW:
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
          break;
        case ErrorSeverity.MEDIUM:
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
          break;
        case ErrorSeverity.HIGH:
        case ErrorSeverity.CRITICAL:
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
          setTimeout(() => {
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
          }, 100);
          break;
      }
    } catch (hapticError) {
      // Haptics might not be available on all devices
      console.warn('Haptic feedback failed:', hapticError);
    }
  }

  /**
   * Show user notification
   */
  private showUserNotification(error: AppError): void {
    const title = this.getErrorTitle(error.type);
    
    Alert.alert(
      title,
      error.userMessage || error.message,
      [
        {
          text: 'OK',
          style: 'default',
        },
        ...(error.severity === ErrorSeverity.CRITICAL ? [{
          text: 'Report Issue',
          style: 'default',
          onPress: () => this.reportIssue(error),
        }] : []),
      ]
    );
  }

  /**
   * Add error to queue for analytics
   */
  private addToQueue(error: AppError): void {
    this.errorQueue.push(error);
    
    // Maintain queue size
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue.shift();
    }
  }

  /**
   * Get error title for user notification
   */
  private getErrorTitle(type: ErrorType): string {
    switch (type) {
      case ErrorType.NETWORK:
        return 'Connection Issue';
      case ErrorType.VALIDATION:
        return 'Input Error';
      case ErrorType.AUTHENTICATION:
        return 'Authentication Required';
      case ErrorType.AUTHORIZATION:
        return 'Access Denied';
      case ErrorType.NOT_FOUND:
        return 'Not Found';
      case ErrorType.SERVER:
        return 'Server Error';
      default:
        return 'Error';
    }
  }

  /**
   * Determine error type from error object
   */
  private determineErrorType(error: Error): ErrorType {
    const message = error.message.toLowerCase();
    
    if (message.includes('network') || message.includes('fetch')) {
      return ErrorType.NETWORK;
    }
    if (message.includes('unauthorized') || message.includes('401')) {
      return ErrorType.AUTHENTICATION;
    }
    if (message.includes('forbidden') || message.includes('403')) {
      return ErrorType.AUTHORIZATION;
    }
    if (message.includes('not found') || message.includes('404')) {
      return ErrorType.NOT_FOUND;
    }
    if (message.includes('server') || message.includes('500')) {
      return ErrorType.SERVER;
    }
    
    return ErrorType.UNKNOWN;
  }

  /**
   * Determine error severity
   */
  private determineSeverity(error: Error): ErrorSeverity {
    const message = error.message.toLowerCase();
    
    if (message.includes('critical') || message.includes('fatal')) {
      return ErrorSeverity.CRITICAL;
    }
    if (message.includes('unauthorized') || message.includes('forbidden')) {
      return ErrorSeverity.HIGH;
    }
    if (message.includes('network') || message.includes('server')) {
      return ErrorSeverity.MEDIUM;
    }
    
    return ErrorSeverity.LOW;
  }

  /**
   * Generate user-friendly error message
   */
  private generateUserMessage(error: Error): string {
    const type = this.determineErrorType(error);
    
    switch (type) {
      case ErrorType.NETWORK:
        return 'Please check your internet connection and try again.';
      case ErrorType.AUTHENTICATION:
        return 'Please log in to continue.';
      case ErrorType.AUTHORIZATION:
        return 'You don\'t have permission to perform this action.';
      case ErrorType.NOT_FOUND:
        return 'The requested item could not be found.';
      case ErrorType.SERVER:
        return 'Server is temporarily unavailable. Please try again later.';
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  }

  /**
   * Generate unique error ID
   */
  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Check if error is already an AppError
   */
  private isAppError(error: any): error is AppError {
    return error && typeof error === 'object' && 'id' in error && 'type' in error;
  }

  /**
   * Report issue to support
   */
  private reportIssue(error: AppError): void {
    // In a real app, this would open a support ticket or email
    console.log('Reporting issue:', error.id);
  }

  /**
   * Get error statistics
   */
  getErrorStats(): {
    total: number;
    byType: Record<ErrorType, number>;
    bySeverity: Record<ErrorSeverity, number>;
  } {
    const stats = {
      total: this.errorQueue.length,
      byType: {} as Record<ErrorType, number>,
      bySeverity: {} as Record<ErrorSeverity, number>,
    };
    
    this.errorQueue.forEach(error => {
      stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
      stats.bySeverity[error.severity] = (stats.bySeverity[error.severity] || 0) + 1;
    });
    
    return stats;
  }

  /**
   * Clear error queue
   */
  clearErrors(): void {
    this.errorQueue = [];
  }
}

// Create singleton instance
export const errorHandler = new ErrorHandler();

// Convenience functions
export const handleError = (error: Error | AppError, context?: Record<string, any>) => 
  errorHandler.handleError(error, context);

export const handleNetworkError = (error: Error, context?: Record<string, any>) => 
  errorHandler.handleNetworkError(error, context);

export const handleValidationError = (message: string, field?: string, context?: Record<string, any>) => 
  errorHandler.handleValidationError(message, field, context);

export const handleAuthError = (error: Error, context?: Record<string, any>) => 
  errorHandler.handleAuthError(error, context);

export const handleCriticalError = (error: Error, context?: Record<string, any>) => 
  errorHandler.handleCriticalError(error, context);
