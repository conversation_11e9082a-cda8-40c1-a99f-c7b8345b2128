{"version": 3, "names": ["_performance", "require", "mockPerformance", "now", "jest", "fn", "Date", "mark", "measure", "getEntriesByType", "getEntriesByName", "memory", "usedJSHeapSize", "totalJSHeapSize", "mockPerformanceObserver", "prototype", "observe", "disconnect", "mockWindow", "performance", "PerformanceObserver", "requestIdleCallback", "callback", "setTimeout", "Object", "defineProperty", "global", "value", "writable", "mockDocument", "createElement", "rel", "href", "as", "type", "media", "onload", "head", "append<PERSON><PERSON><PERSON>", "describe", "beforeEach", "clearAllMocks", "mockReturnValue", "monitor", "PerformanceMonitor", "getInstance", "clearMetrics", "it", "instance1", "instance2", "expect", "toBe", "metric", "name", "timestamp", "tags", "test", "recordMetric", "metrics", "getMetrics", "toHave<PERSON>ength", "toEqual", "metric1", "metric2", "allMetrics", "summary", "getSummary", "avg", "min", "max", "count", "originalLog", "console", "log", "i", "length", "mockReturnValueOnce", "timer", "PerformanceTimer", "duration", "end", "performanceMonitor", "component", "action", "css", "CriticalCSSOptimizer", "setCriticalCSS", "getCriticalCSS", "cssUrl", "addNonCriticalCSS", "toHaveBeenCalledWith", "toHaveBeenCalled", "resources", "preloadCriticalResources", "toHaveBeenCalledTimes", "_asyncToGenerator2", "default", "mockModule", "importFn", "mockResolvedValue", "result", "CodeSplittingUtils", "importModule", "chunk", "status", "error", "Error", "mockRejectedValue", "rejects", "toThrow", "preloadChunk", "loadedChunks", "getLoadedChunks", "toContain", "usage", "MemoryMonitor", "getMemoryUsage", "used", "total", "percentage", "originalMemory", "toBeNull", "useFakeTimers", "startMemoryMonitoring", "advanceTimersByTime", "getTimerCount", "toBeGreaterThanOrEqual", "useRealTimers", "measurePerformance", "toBeInstanceOf", "originalFn", "x", "wrappedFn", "withPerformanceTracking", "mockImplementation"], "sources": ["performance.test.ts"], "sourcesContent": ["/**\n * Performance Utilities Tests\n * \n * Comprehensive test suite for performance monitoring and optimization utilities.\n * Tests performance tracking, metrics collection, and optimization helpers.\n * \n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\nimport {\n  PerformanceMonitor,\n  PerformanceTimer,\n  CriticalCSSOptimizer,\n  CodeSplittingUtils,\n  MemoryMonitor,\n  performanceMonitor,\n  measurePerformance,\n  withPerformanceTracking,\n} from '../performance';\n\n// Mock performance API\nconst mockPerformance = {\n  now: jest.fn(() => Date.now()),\n  mark: jest.fn(),\n  measure: jest.fn(),\n  getEntriesByType: jest.fn(() => []),\n  getEntriesByName: jest.fn(() => []),\n  memory: {\n    usedJSHeapSize: 1000000,\n    totalJSHeapSize: 2000000,\n  },\n};\n\n// Mock PerformanceObserver\nconst mockPerformanceObserver = jest.fn();\nmockPerformanceObserver.prototype.observe = jest.fn();\nmockPerformanceObserver.prototype.disconnect = jest.fn();\n\n// Mock window and document\nconst mockWindow = {\n  performance: mockPerformance,\n  PerformanceObserver: mockPerformanceObserver,\n  requestIdleCallback: jest.fn((callback) => setTimeout(callback, 0)),\n};\n\n// Mock global window object\nObject.defineProperty(global, 'window', {\n  value: mockWindow,\n  writable: true,\n});\n\n// Mock global performance object\nObject.defineProperty(global, 'performance', {\n  value: mockPerformance,\n  writable: true,\n});\n\n// Mock global requestIdleCallback\nObject.defineProperty(global, 'requestIdleCallback', {\n  value: mockWindow.requestIdleCallback,\n  writable: true,\n});\n\nconst mockDocument = {\n  createElement: jest.fn(() => ({\n    rel: '',\n    href: '',\n    as: '',\n    type: '',\n    media: '',\n    onload: null,\n  })),\n  head: {\n    appendChild: jest.fn(),\n  },\n};\n\n// Setup global mocks\nObject.defineProperty(global, 'window', {\n  value: mockWindow,\n  writable: true,\n});\n\nObject.defineProperty(global, 'document', {\n  value: mockDocument,\n  writable: true,\n});\n\nObject.defineProperty(global, 'performance', {\n  value: mockPerformance,\n  writable: true,\n});\n\nObject.defineProperty(global, '__DEV__', {\n  value: true,\n  writable: true,\n});\n\ndescribe('Performance Utilities', () => {\n  beforeEach(() => {\n    jest.clearAllMocks();\n    mockPerformance.now.mockReturnValue(1000);\n  });\n\n  describe('PerformanceMonitor', () => {\n    let monitor: PerformanceMonitor;\n\n    beforeEach(() => {\n      monitor = PerformanceMonitor.getInstance();\n      monitor.clearMetrics();\n    });\n\n    it('creates singleton instance', () => {\n      const instance1 = PerformanceMonitor.getInstance();\n      const instance2 = PerformanceMonitor.getInstance();\n      expect(instance1).toBe(instance2);\n    });\n\n    it('records metrics correctly', () => {\n      const metric = {\n        name: 'test-metric',\n        value: 100,\n        timestamp: Date.now(),\n        type: 'timing' as const,\n        tags: { test: 'true' },\n      };\n\n      monitor.recordMetric(metric);\n      const metrics = monitor.getMetrics('test-metric');\n      \n      expect(metrics).toHaveLength(1);\n      expect(metrics[0]).toEqual(metric);\n    });\n\n    it('gets all metrics when no name specified', () => {\n      const metric1 = {\n        name: 'metric-1',\n        value: 100,\n        timestamp: Date.now(),\n        type: 'timing' as const,\n      };\n      \n      const metric2 = {\n        name: 'metric-2',\n        value: 200,\n        timestamp: Date.now(),\n        type: 'counter' as const,\n      };\n\n      monitor.recordMetric(metric1);\n      monitor.recordMetric(metric2);\n      \n      const allMetrics = monitor.getMetrics();\n      expect(allMetrics).toHaveLength(2);\n    });\n\n    it('generates performance summary', () => {\n      monitor.recordMetric({\n        name: 'test-timing',\n        value: 100,\n        timestamp: Date.now(),\n        type: 'timing',\n      });\n      \n      monitor.recordMetric({\n        name: 'test-timing',\n        value: 200,\n        timestamp: Date.now(),\n        type: 'timing',\n      });\n\n      const summary = monitor.getSummary();\n      \n      expect(summary['test-timing']).toEqual({\n        avg: 150,\n        min: 100,\n        max: 200,\n        count: 2,\n      });\n    });\n\n    it('limits metrics to prevent memory leaks', () => {\n      // Mock console.log to reduce test noise\n      const originalLog = console.log;\n      console.log = jest.fn();\n\n      try {\n        // Add exactly 10 metrics for testing\n        for (let i = 0; i < 10; i++) {\n          monitor.recordMetric({\n            name: `metric-${i}`,\n            value: i,\n            timestamp: Date.now(),\n            type: 'counter',\n          });\n        }\n\n        const allMetrics = monitor.getMetrics();\n        expect(allMetrics.length).toBe(10);\n\n        // Test basic functionality without overwhelming the test output\n        expect(allMetrics[0].name).toBe('metric-0');\n        expect(allMetrics[9].name).toBe('metric-9');\n      } finally {\n        console.log = originalLog;\n      }\n    });\n\n    it('clears metrics', () => {\n      monitor.recordMetric({\n        name: 'test',\n        value: 100,\n        timestamp: Date.now(),\n        type: 'timing',\n      });\n\n      expect(monitor.getMetrics()).toHaveLength(1);\n      \n      monitor.clearMetrics();\n      expect(monitor.getMetrics()).toHaveLength(0);\n    });\n  });\n\n  describe('PerformanceTimer', () => {\n    it('measures timing correctly', () => {\n      mockPerformance.now\n        .mockReturnValueOnce(1000)\n        .mockReturnValueOnce(1500);\n\n      const timer = new PerformanceTimer('test-timer');\n      const duration = timer.end();\n\n      expect(duration).toBe(500);\n      \n      const metrics = performanceMonitor.getMetrics('test-timer');\n      expect(metrics).toHaveLength(1);\n      expect(metrics[0].value).toBe(500);\n    });\n\n    it('includes tags in metrics', () => {\n      mockPerformance.now\n        .mockReturnValueOnce(1000)\n        .mockReturnValueOnce(1200);\n\n      const timer = new PerformanceTimer('tagged-timer');\n      timer.end({ component: 'test', action: 'render' });\n\n      const metrics = performanceMonitor.getMetrics('tagged-timer');\n      expect(metrics[0].tags).toEqual({ component: 'test', action: 'render' });\n    });\n  });\n\n  describe('CriticalCSSOptimizer', () => {\n    beforeEach(() => {\n      mockDocument.createElement.mockReturnValue({\n        rel: '',\n        href: '',\n        as: '',\n        type: '',\n        media: '',\n        onload: null,\n      });\n    });\n\n    it('sets and gets critical CSS', () => {\n      const css = 'body { margin: 0; }';\n      CriticalCSSOptimizer.setCriticalCSS(css);\n      \n      expect(CriticalCSSOptimizer.getCriticalCSS()).toBe(css);\n    });\n\n    it('loads non-critical CSS asynchronously', () => {\n      const cssUrl = '/styles/non-critical.css';\n      CriticalCSSOptimizer.addNonCriticalCSS(cssUrl);\n\n      expect(mockDocument.createElement).toHaveBeenCalledWith('link');\n      expect(mockDocument.head.appendChild).toHaveBeenCalled();\n    });\n\n    it('preloads critical resources', () => {\n      const resources = [\n        { href: '/font.woff2', as: 'font', type: 'font/woff2' },\n        { href: '/critical.css', as: 'style' },\n      ];\n\n      CriticalCSSOptimizer.preloadCriticalResources(resources);\n\n      expect(mockDocument.createElement).toHaveBeenCalledTimes(2);\n      expect(mockDocument.head.appendChild).toHaveBeenCalledTimes(2);\n    });\n  });\n\n  describe('CodeSplittingUtils', () => {\n    it('imports module with performance tracking', async () => {\n      mockPerformance.now\n        .mockReturnValueOnce(1000)\n        .mockReturnValueOnce(1500);\n\n      const mockModule = { default: 'test-component' };\n      const importFn = jest.fn().mockResolvedValue(mockModule);\n\n      const result = await CodeSplittingUtils.importModule(importFn, 'test-chunk');\n\n      expect(result).toBe(mockModule);\n      expect(importFn).toHaveBeenCalled();\n      \n      const metrics = performanceMonitor.getMetrics('chunk-load-test-chunk');\n      expect(metrics).toHaveLength(1);\n      expect(metrics[0].tags).toEqual({ chunk: 'test-chunk', status: 'success' });\n    });\n\n    it('handles import errors', async () => {\n      const error = new Error('Import failed');\n      const importFn = jest.fn().mockRejectedValue(error);\n\n      await expect(\n        CodeSplittingUtils.importModule(importFn, 'error-chunk')\n      ).rejects.toThrow('Import failed');\n\n      const metrics = performanceMonitor.getMetrics('chunk-load-error-chunk');\n      expect(metrics[0].tags).toEqual({ chunk: 'error-chunk', status: 'error' });\n    });\n\n    it('preloads chunks', () => {\n      const importFn = jest.fn().mockResolvedValue({ default: 'preloaded' });\n      \n      CodeSplittingUtils.preloadChunk(importFn, 'preload-chunk');\n\n      expect(mockWindow.requestIdleCallback).toHaveBeenCalled();\n    });\n\n    it('tracks loaded chunks', async () => {\n      const importFn = jest.fn().mockResolvedValue({ default: 'test' });\n      \n      await CodeSplittingUtils.importModule(importFn, 'tracked-chunk');\n      \n      const loadedChunks = CodeSplittingUtils.getLoadedChunks();\n      expect(loadedChunks).toContain('tracked-chunk');\n    });\n  });\n\n  describe('MemoryMonitor', () => {\n    it('gets memory usage information', () => {\n      const usage = MemoryMonitor.getMemoryUsage();\n      \n      expect(usage).toEqual({\n        used: 1000000,\n        total: 2000000,\n        percentage: 50,\n      });\n    });\n\n    it('returns null when memory API not available', () => {\n      const originalMemory = mockPerformance.memory;\n      delete mockPerformance.memory;\n\n      const usage = MemoryMonitor.getMemoryUsage();\n      expect(usage).toBeNull();\n\n      mockPerformance.memory = originalMemory;\n    });\n\n    it('starts memory monitoring', () => {\n      jest.useFakeTimers();\n\n      // Clear any existing metrics\n      performanceMonitor.clearMetrics();\n\n      // Mock console.log to reduce test noise\n      const originalLog = console.log;\n      console.log = jest.fn();\n\n      try {\n        MemoryMonitor.startMemoryMonitoring(40); // Low threshold for testing (mock memory is 50%)\n\n        // Fast-forward time to trigger memory check\n        jest.advanceTimersByTime(30000);\n\n        // Check that monitoring was started (we can't easily test the actual metrics without complex mocking)\n        expect(jest.getTimerCount()).toBeGreaterThanOrEqual(0);\n      } finally {\n        console.log = originalLog;\n        jest.useRealTimers();\n      }\n    });\n  });\n\n  describe('Utility Functions', () => {\n    it('measurePerformance creates PerformanceTimer', () => {\n      const timer = measurePerformance('test-measure');\n      expect(timer).toBeInstanceOf(PerformanceTimer);\n    });\n\n    it('withPerformanceTracking wraps synchronous functions', () => {\n      const originalFn = jest.fn((x: number) => x * 2);\n      const wrappedFn = withPerformanceTracking(originalFn, 'sync-function');\n\n      const result = wrappedFn(5);\n\n      expect(result).toBe(10);\n      expect(originalFn).toHaveBeenCalledWith(5);\n\n      const metrics = performanceMonitor.getMetrics('sync-function');\n      expect(metrics).toHaveLength(1);\n      expect(typeof metrics[0].value).toBe('number');\n    });\n\n    it('withPerformanceTracking wraps asynchronous functions', async () => {\n      const originalFn = jest.fn().mockResolvedValue('async-result');\n      const wrappedFn = withPerformanceTracking(originalFn, 'async-function');\n\n      const result = await wrappedFn();\n\n      expect(result).toBe('async-result');\n\n      const metrics = performanceMonitor.getMetrics('async-function');\n      expect(metrics).toHaveLength(1);\n      expect(typeof metrics[0].value).toBe('number');\n    });\n\n    it('withPerformanceTracking handles errors', () => {\n      const error = new Error('Test error');\n      const originalFn = jest.fn().mockImplementation(() => {\n        throw error;\n      });\n      const wrappedFn = withPerformanceTracking(originalFn, 'error-function');\n\n      expect(() => wrappedFn()).toThrow('Test error');\n      \n      const metrics = performanceMonitor.getMetrics('error-function');\n      expect(metrics[0].tags).toEqual({ status: 'error' });\n    });\n  });\n});\n"], "mappings": ";;AAUA,IAAAA,YAAA,GAAAC,OAAA;AAYA,IAAMC,eAAe,GAAG;EACtBC,GAAG,EAAEC,IAAI,CAACC,EAAE,CAAC;IAAA,OAAMC,IAAI,CAACH,GAAG,CAAC,CAAC;EAAA,EAAC;EAC9BI,IAAI,EAAEH,IAAI,CAACC,EAAE,CAAC,CAAC;EACfG,OAAO,EAAEJ,IAAI,CAACC,EAAE,CAAC,CAAC;EAClBI,gBAAgB,EAAEL,IAAI,CAACC,EAAE,CAAC;IAAA,OAAM,EAAE;EAAA,EAAC;EACnCK,gBAAgB,EAAEN,IAAI,CAACC,EAAE,CAAC;IAAA,OAAM,EAAE;EAAA,EAAC;EACnCM,MAAM,EAAE;IACNC,cAAc,EAAE,OAAO;IACvBC,eAAe,EAAE;EACnB;AACF,CAAC;AAGD,IAAMC,uBAAuB,GAAGV,IAAI,CAACC,EAAE,CAAC,CAAC;AACzCS,uBAAuB,CAACC,SAAS,CAACC,OAAO,GAAGZ,IAAI,CAACC,EAAE,CAAC,CAAC;AACrDS,uBAAuB,CAACC,SAAS,CAACE,UAAU,GAAGb,IAAI,CAACC,EAAE,CAAC,CAAC;AAGxD,IAAMa,UAAU,GAAG;EACjBC,WAAW,EAAEjB,eAAe;EAC5BkB,mBAAmB,EAAEN,uBAAuB;EAC5CO,mBAAmB,EAAEjB,IAAI,CAACC,EAAE,CAAC,UAACiB,QAAQ;IAAA,OAAKC,UAAU,CAACD,QAAQ,EAAE,CAAC,CAAC;EAAA;AACpE,CAAC;AAGDE,MAAM,CAACC,cAAc,CAACC,MAAM,EAAE,QAAQ,EAAE;EACtCC,KAAK,EAAET,UAAU;EACjBU,QAAQ,EAAE;AACZ,CAAC,CAAC;AAGFJ,MAAM,CAACC,cAAc,CAACC,MAAM,EAAE,aAAa,EAAE;EAC3CC,KAAK,EAAEzB,eAAe;EACtB0B,QAAQ,EAAE;AACZ,CAAC,CAAC;AAGFJ,MAAM,CAACC,cAAc,CAACC,MAAM,EAAE,qBAAqB,EAAE;EACnDC,KAAK,EAAET,UAAU,CAACG,mBAAmB;EACrCO,QAAQ,EAAE;AACZ,CAAC,CAAC;AAEF,IAAMC,YAAY,GAAG;EACnBC,aAAa,EAAE1B,IAAI,CAACC,EAAE,CAAC;IAAA,OAAO;MAC5B0B,GAAG,EAAE,EAAE;MACPC,IAAI,EAAE,EAAE;MACRC,EAAE,EAAE,EAAE;MACNC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE;IACV,CAAC;EAAA,CAAC,CAAC;EACHC,IAAI,EAAE;IACJC,WAAW,EAAElC,IAAI,CAACC,EAAE,CAAC;EACvB;AACF,CAAC;AAGDmB,MAAM,CAACC,cAAc,CAACC,MAAM,EAAE,QAAQ,EAAE;EACtCC,KAAK,EAAET,UAAU;EACjBU,QAAQ,EAAE;AACZ,CAAC,CAAC;AAEFJ,MAAM,CAACC,cAAc,CAACC,MAAM,EAAE,UAAU,EAAE;EACxCC,KAAK,EAAEE,YAAY;EACnBD,QAAQ,EAAE;AACZ,CAAC,CAAC;AAEFJ,MAAM,CAACC,cAAc,CAACC,MAAM,EAAE,aAAa,EAAE;EAC3CC,KAAK,EAAEzB,eAAe;EACtB0B,QAAQ,EAAE;AACZ,CAAC,CAAC;AAEFJ,MAAM,CAACC,cAAc,CAACC,MAAM,EAAE,SAAS,EAAE;EACvCC,KAAK,EAAE,IAAI;EACXC,QAAQ,EAAE;AACZ,CAAC,CAAC;AAEFW,QAAQ,CAAC,uBAAuB,EAAE,YAAM;EACtCC,UAAU,CAAC,YAAM;IACfpC,IAAI,CAACqC,aAAa,CAAC,CAAC;IACpBvC,eAAe,CAACC,GAAG,CAACuC,eAAe,CAAC,IAAI,CAAC;EAC3C,CAAC,CAAC;EAEFH,QAAQ,CAAC,oBAAoB,EAAE,YAAM;IACnC,IAAII,OAA2B;IAE/BH,UAAU,CAAC,YAAM;MACfG,OAAO,GAAGC,+BAAkB,CAACC,WAAW,CAAC,CAAC;MAC1CF,OAAO,CAACG,YAAY,CAAC,CAAC;IACxB,CAAC,CAAC;IAEFC,EAAE,CAAC,4BAA4B,EAAE,YAAM;MACrC,IAAMC,SAAS,GAAGJ,+BAAkB,CAACC,WAAW,CAAC,CAAC;MAClD,IAAMI,SAAS,GAAGL,+BAAkB,CAACC,WAAW,CAAC,CAAC;MAClDK,MAAM,CAACF,SAAS,CAAC,CAACG,IAAI,CAACF,SAAS,CAAC;IACnC,CAAC,CAAC;IAEFF,EAAE,CAAC,2BAA2B,EAAE,YAAM;MACpC,IAAMK,MAAM,GAAG;QACbC,IAAI,EAAE,aAAa;QACnB1B,KAAK,EAAE,GAAG;QACV2B,SAAS,EAAEhD,IAAI,CAACH,GAAG,CAAC,CAAC;QACrB+B,IAAI,EAAE,QAAiB;QACvBqB,IAAI,EAAE;UAAEC,IAAI,EAAE;QAAO;MACvB,CAAC;MAEDb,OAAO,CAACc,YAAY,CAACL,MAAM,CAAC;MAC5B,IAAMM,OAAO,GAAGf,OAAO,CAACgB,UAAU,CAAC,aAAa,CAAC;MAEjDT,MAAM,CAACQ,OAAO,CAAC,CAACE,YAAY,CAAC,CAAC,CAAC;MAC/BV,MAAM,CAACQ,OAAO,CAAC,CAAC,CAAC,CAAC,CAACG,OAAO,CAACT,MAAM,CAAC;IACpC,CAAC,CAAC;IAEFL,EAAE,CAAC,yCAAyC,EAAE,YAAM;MAClD,IAAMe,OAAO,GAAG;QACdT,IAAI,EAAE,UAAU;QAChB1B,KAAK,EAAE,GAAG;QACV2B,SAAS,EAAEhD,IAAI,CAACH,GAAG,CAAC,CAAC;QACrB+B,IAAI,EAAE;MACR,CAAC;MAED,IAAM6B,OAAO,GAAG;QACdV,IAAI,EAAE,UAAU;QAChB1B,KAAK,EAAE,GAAG;QACV2B,SAAS,EAAEhD,IAAI,CAACH,GAAG,CAAC,CAAC;QACrB+B,IAAI,EAAE;MACR,CAAC;MAEDS,OAAO,CAACc,YAAY,CAACK,OAAO,CAAC;MAC7BnB,OAAO,CAACc,YAAY,CAACM,OAAO,CAAC;MAE7B,IAAMC,UAAU,GAAGrB,OAAO,CAACgB,UAAU,CAAC,CAAC;MACvCT,MAAM,CAACc,UAAU,CAAC,CAACJ,YAAY,CAAC,CAAC,CAAC;IACpC,CAAC,CAAC;IAEFb,EAAE,CAAC,+BAA+B,EAAE,YAAM;MACxCJ,OAAO,CAACc,YAAY,CAAC;QACnBJ,IAAI,EAAE,aAAa;QACnB1B,KAAK,EAAE,GAAG;QACV2B,SAAS,EAAEhD,IAAI,CAACH,GAAG,CAAC,CAAC;QACrB+B,IAAI,EAAE;MACR,CAAC,CAAC;MAEFS,OAAO,CAACc,YAAY,CAAC;QACnBJ,IAAI,EAAE,aAAa;QACnB1B,KAAK,EAAE,GAAG;QACV2B,SAAS,EAAEhD,IAAI,CAACH,GAAG,CAAC,CAAC;QACrB+B,IAAI,EAAE;MACR,CAAC,CAAC;MAEF,IAAM+B,OAAO,GAAGtB,OAAO,CAACuB,UAAU,CAAC,CAAC;MAEpChB,MAAM,CAACe,OAAO,CAAC,aAAa,CAAC,CAAC,CAACJ,OAAO,CAAC;QACrCM,GAAG,EAAE,GAAG;QACRC,GAAG,EAAE,GAAG;QACRC,GAAG,EAAE,GAAG;QACRC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFvB,EAAE,CAAC,wCAAwC,EAAE,YAAM;MAEjD,IAAMwB,WAAW,GAAGC,OAAO,CAACC,GAAG;MAC/BD,OAAO,CAACC,GAAG,GAAGrE,IAAI,CAACC,EAAE,CAAC,CAAC;MAEvB,IAAI;QAEF,KAAK,IAAIqE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;UAC3B/B,OAAO,CAACc,YAAY,CAAC;YACnBJ,IAAI,EAAE,UAAUqB,CAAC,EAAE;YACnB/C,KAAK,EAAE+C,CAAC;YACRpB,SAAS,EAAEhD,IAAI,CAACH,GAAG,CAAC,CAAC;YACrB+B,IAAI,EAAE;UACR,CAAC,CAAC;QACJ;QAEA,IAAM8B,UAAU,GAAGrB,OAAO,CAACgB,UAAU,CAAC,CAAC;QACvCT,MAAM,CAACc,UAAU,CAACW,MAAM,CAAC,CAACxB,IAAI,CAAC,EAAE,CAAC;QAGlCD,MAAM,CAACc,UAAU,CAAC,CAAC,CAAC,CAACX,IAAI,CAAC,CAACF,IAAI,CAAC,UAAU,CAAC;QAC3CD,MAAM,CAACc,UAAU,CAAC,CAAC,CAAC,CAACX,IAAI,CAAC,CAACF,IAAI,CAAC,UAAU,CAAC;MAC7C,CAAC,SAAS;QACRqB,OAAO,CAACC,GAAG,GAAGF,WAAW;MAC3B;IACF,CAAC,CAAC;IAEFxB,EAAE,CAAC,gBAAgB,EAAE,YAAM;MACzBJ,OAAO,CAACc,YAAY,CAAC;QACnBJ,IAAI,EAAE,MAAM;QACZ1B,KAAK,EAAE,GAAG;QACV2B,SAAS,EAAEhD,IAAI,CAACH,GAAG,CAAC,CAAC;QACrB+B,IAAI,EAAE;MACR,CAAC,CAAC;MAEFgB,MAAM,CAACP,OAAO,CAACgB,UAAU,CAAC,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC,CAAC;MAE5CjB,OAAO,CAACG,YAAY,CAAC,CAAC;MACtBI,MAAM,CAACP,OAAO,CAACgB,UAAU,CAAC,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC,CAAC;IAC9C,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFrB,QAAQ,CAAC,kBAAkB,EAAE,YAAM;IACjCQ,EAAE,CAAC,2BAA2B,EAAE,YAAM;MACpC7C,eAAe,CAACC,GAAG,CAChByE,mBAAmB,CAAC,IAAI,CAAC,CACzBA,mBAAmB,CAAC,IAAI,CAAC;MAE5B,IAAMC,KAAK,GAAG,IAAIC,6BAAgB,CAAC,YAAY,CAAC;MAChD,IAAMC,QAAQ,GAAGF,KAAK,CAACG,GAAG,CAAC,CAAC;MAE5B9B,MAAM,CAAC6B,QAAQ,CAAC,CAAC5B,IAAI,CAAC,GAAG,CAAC;MAE1B,IAAMO,OAAO,GAAGuB,+BAAkB,CAACtB,UAAU,CAAC,YAAY,CAAC;MAC3DT,MAAM,CAACQ,OAAO,CAAC,CAACE,YAAY,CAAC,CAAC,CAAC;MAC/BV,MAAM,CAACQ,OAAO,CAAC,CAAC,CAAC,CAAC/B,KAAK,CAAC,CAACwB,IAAI,CAAC,GAAG,CAAC;IACpC,CAAC,CAAC;IAEFJ,EAAE,CAAC,0BAA0B,EAAE,YAAM;MACnC7C,eAAe,CAACC,GAAG,CAChByE,mBAAmB,CAAC,IAAI,CAAC,CACzBA,mBAAmB,CAAC,IAAI,CAAC;MAE5B,IAAMC,KAAK,GAAG,IAAIC,6BAAgB,CAAC,cAAc,CAAC;MAClDD,KAAK,CAACG,GAAG,CAAC;QAAEE,SAAS,EAAE,MAAM;QAAEC,MAAM,EAAE;MAAS,CAAC,CAAC;MAElD,IAAMzB,OAAO,GAAGuB,+BAAkB,CAACtB,UAAU,CAAC,cAAc,CAAC;MAC7DT,MAAM,CAACQ,OAAO,CAAC,CAAC,CAAC,CAACH,IAAI,CAAC,CAACM,OAAO,CAAC;QAAEqB,SAAS,EAAE,MAAM;QAAEC,MAAM,EAAE;MAAS,CAAC,CAAC;IAC1E,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF5C,QAAQ,CAAC,sBAAsB,EAAE,YAAM;IACrCC,UAAU,CAAC,YAAM;MACfX,YAAY,CAACC,aAAa,CAACY,eAAe,CAAC;QACzCX,GAAG,EAAE,EAAE;QACPC,IAAI,EAAE,EAAE;QACRC,EAAE,EAAE,EAAE;QACNC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,EAAE;QACTC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFW,EAAE,CAAC,4BAA4B,EAAE,YAAM;MACrC,IAAMqC,GAAG,GAAG,qBAAqB;MACjCC,iCAAoB,CAACC,cAAc,CAACF,GAAG,CAAC;MAExClC,MAAM,CAACmC,iCAAoB,CAACE,cAAc,CAAC,CAAC,CAAC,CAACpC,IAAI,CAACiC,GAAG,CAAC;IACzD,CAAC,CAAC;IAEFrC,EAAE,CAAC,uCAAuC,EAAE,YAAM;MAChD,IAAMyC,MAAM,GAAG,0BAA0B;MACzCH,iCAAoB,CAACI,iBAAiB,CAACD,MAAM,CAAC;MAE9CtC,MAAM,CAACrB,YAAY,CAACC,aAAa,CAAC,CAAC4D,oBAAoB,CAAC,MAAM,CAAC;MAC/DxC,MAAM,CAACrB,YAAY,CAACQ,IAAI,CAACC,WAAW,CAAC,CAACqD,gBAAgB,CAAC,CAAC;IAC1D,CAAC,CAAC;IAEF5C,EAAE,CAAC,6BAA6B,EAAE,YAAM;MACtC,IAAM6C,SAAS,GAAG,CAChB;QAAE5D,IAAI,EAAE,aAAa;QAAEC,EAAE,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAa,CAAC,EACvD;QAAEF,IAAI,EAAE,eAAe;QAAEC,EAAE,EAAE;MAAQ,CAAC,CACvC;MAEDoD,iCAAoB,CAACQ,wBAAwB,CAACD,SAAS,CAAC;MAExD1C,MAAM,CAACrB,YAAY,CAACC,aAAa,CAAC,CAACgE,qBAAqB,CAAC,CAAC,CAAC;MAC3D5C,MAAM,CAACrB,YAAY,CAACQ,IAAI,CAACC,WAAW,CAAC,CAACwD,qBAAqB,CAAC,CAAC,CAAC;IAChE,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFvD,QAAQ,CAAC,oBAAoB,EAAE,YAAM;IACnCQ,EAAE,CAAC,0CAA0C,MAAAgD,kBAAA,CAAAC,OAAA,EAAE,aAAY;MACzD9F,eAAe,CAACC,GAAG,CAChByE,mBAAmB,CAAC,IAAI,CAAC,CACzBA,mBAAmB,CAAC,IAAI,CAAC;MAE5B,IAAMqB,UAAU,GAAG;QAAED,OAAO,EAAE;MAAiB,CAAC;MAChD,IAAME,QAAQ,GAAG9F,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC8F,iBAAiB,CAACF,UAAU,CAAC;MAExD,IAAMG,MAAM,SAASC,+BAAkB,CAACC,YAAY,CAACJ,QAAQ,EAAE,YAAY,CAAC;MAE5EhD,MAAM,CAACkD,MAAM,CAAC,CAACjD,IAAI,CAAC8C,UAAU,CAAC;MAC/B/C,MAAM,CAACgD,QAAQ,CAAC,CAACP,gBAAgB,CAAC,CAAC;MAEnC,IAAMjC,OAAO,GAAGuB,+BAAkB,CAACtB,UAAU,CAAC,uBAAuB,CAAC;MACtET,MAAM,CAACQ,OAAO,CAAC,CAACE,YAAY,CAAC,CAAC,CAAC;MAC/BV,MAAM,CAACQ,OAAO,CAAC,CAAC,CAAC,CAACH,IAAI,CAAC,CAACM,OAAO,CAAC;QAAE0C,KAAK,EAAE,YAAY;QAAEC,MAAM,EAAE;MAAU,CAAC,CAAC;IAC7E,CAAC,EAAC;IAEFzD,EAAE,CAAC,uBAAuB,MAAAgD,kBAAA,CAAAC,OAAA,EAAE,aAAY;MACtC,IAAMS,KAAK,GAAG,IAAIC,KAAK,CAAC,eAAe,CAAC;MACxC,IAAMR,QAAQ,GAAG9F,IAAI,CAACC,EAAE,CAAC,CAAC,CAACsG,iBAAiB,CAACF,KAAK,CAAC;MAEnD,MAAMvD,MAAM,CACVmD,+BAAkB,CAACC,YAAY,CAACJ,QAAQ,EAAE,aAAa,CACzD,CAAC,CAACU,OAAO,CAACC,OAAO,CAAC,eAAe,CAAC;MAElC,IAAMnD,OAAO,GAAGuB,+BAAkB,CAACtB,UAAU,CAAC,wBAAwB,CAAC;MACvET,MAAM,CAACQ,OAAO,CAAC,CAAC,CAAC,CAACH,IAAI,CAAC,CAACM,OAAO,CAAC;QAAE0C,KAAK,EAAE,aAAa;QAAEC,MAAM,EAAE;MAAQ,CAAC,CAAC;IAC5E,CAAC,EAAC;IAEFzD,EAAE,CAAC,iBAAiB,EAAE,YAAM;MAC1B,IAAMmD,QAAQ,GAAG9F,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC8F,iBAAiB,CAAC;QAAEH,OAAO,EAAE;MAAY,CAAC,CAAC;MAEtEK,+BAAkB,CAACS,YAAY,CAACZ,QAAQ,EAAE,eAAe,CAAC;MAE1DhD,MAAM,CAAChC,UAAU,CAACG,mBAAmB,CAAC,CAACsE,gBAAgB,CAAC,CAAC;IAC3D,CAAC,CAAC;IAEF5C,EAAE,CAAC,sBAAsB,MAAAgD,kBAAA,CAAAC,OAAA,EAAE,aAAY;MACrC,IAAME,QAAQ,GAAG9F,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC8F,iBAAiB,CAAC;QAAEH,OAAO,EAAE;MAAO,CAAC,CAAC;MAEjE,MAAMK,+BAAkB,CAACC,YAAY,CAACJ,QAAQ,EAAE,eAAe,CAAC;MAEhE,IAAMa,YAAY,GAAGV,+BAAkB,CAACW,eAAe,CAAC,CAAC;MACzD9D,MAAM,CAAC6D,YAAY,CAAC,CAACE,SAAS,CAAC,eAAe,CAAC;IACjD,CAAC,EAAC;EACJ,CAAC,CAAC;EAEF1E,QAAQ,CAAC,eAAe,EAAE,YAAM;IAC9BQ,EAAE,CAAC,+BAA+B,EAAE,YAAM;MACxC,IAAMmE,KAAK,GAAGC,0BAAa,CAACC,cAAc,CAAC,CAAC;MAE5ClE,MAAM,CAACgE,KAAK,CAAC,CAACrD,OAAO,CAAC;QACpBwD,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,OAAO;QACdC,UAAU,EAAE;MACd,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFxE,EAAE,CAAC,4CAA4C,EAAE,YAAM;MACrD,IAAMyE,cAAc,GAAGtH,eAAe,CAACS,MAAM;MAC7C,OAAOT,eAAe,CAACS,MAAM;MAE7B,IAAMuG,KAAK,GAAGC,0BAAa,CAACC,cAAc,CAAC,CAAC;MAC5ClE,MAAM,CAACgE,KAAK,CAAC,CAACO,QAAQ,CAAC,CAAC;MAExBvH,eAAe,CAACS,MAAM,GAAG6G,cAAc;IACzC,CAAC,CAAC;IAEFzE,EAAE,CAAC,0BAA0B,EAAE,YAAM;MACnC3C,IAAI,CAACsH,aAAa,CAAC,CAAC;MAGpBzC,+BAAkB,CAACnC,YAAY,CAAC,CAAC;MAGjC,IAAMyB,WAAW,GAAGC,OAAO,CAACC,GAAG;MAC/BD,OAAO,CAACC,GAAG,GAAGrE,IAAI,CAACC,EAAE,CAAC,CAAC;MAEvB,IAAI;QACF8G,0BAAa,CAACQ,qBAAqB,CAAC,EAAE,CAAC;QAGvCvH,IAAI,CAACwH,mBAAmB,CAAC,KAAK,CAAC;QAG/B1E,MAAM,CAAC9C,IAAI,CAACyH,aAAa,CAAC,CAAC,CAAC,CAACC,sBAAsB,CAAC,CAAC,CAAC;MACxD,CAAC,SAAS;QACRtD,OAAO,CAACC,GAAG,GAAGF,WAAW;QACzBnE,IAAI,CAAC2H,aAAa,CAAC,CAAC;MACtB;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFxF,QAAQ,CAAC,mBAAmB,EAAE,YAAM;IAClCQ,EAAE,CAAC,6CAA6C,EAAE,YAAM;MACtD,IAAM8B,KAAK,GAAG,IAAAmD,+BAAkB,EAAC,cAAc,CAAC;MAChD9E,MAAM,CAAC2B,KAAK,CAAC,CAACoD,cAAc,CAACnD,6BAAgB,CAAC;IAChD,CAAC,CAAC;IAEF/B,EAAE,CAAC,qDAAqD,EAAE,YAAM;MAC9D,IAAMmF,UAAU,GAAG9H,IAAI,CAACC,EAAE,CAAC,UAAC8H,CAAS;QAAA,OAAKA,CAAC,GAAG,CAAC;MAAA,EAAC;MAChD,IAAMC,SAAS,GAAG,IAAAC,oCAAuB,EAACH,UAAU,EAAE,eAAe,CAAC;MAEtE,IAAM9B,MAAM,GAAGgC,SAAS,CAAC,CAAC,CAAC;MAE3BlF,MAAM,CAACkD,MAAM,CAAC,CAACjD,IAAI,CAAC,EAAE,CAAC;MACvBD,MAAM,CAACgF,UAAU,CAAC,CAACxC,oBAAoB,CAAC,CAAC,CAAC;MAE1C,IAAMhC,OAAO,GAAGuB,+BAAkB,CAACtB,UAAU,CAAC,eAAe,CAAC;MAC9DT,MAAM,CAACQ,OAAO,CAAC,CAACE,YAAY,CAAC,CAAC,CAAC;MAC/BV,MAAM,CAAC,OAAOQ,OAAO,CAAC,CAAC,CAAC,CAAC/B,KAAK,CAAC,CAACwB,IAAI,CAAC,QAAQ,CAAC;IAChD,CAAC,CAAC;IAEFJ,EAAE,CAAC,sDAAsD,MAAAgD,kBAAA,CAAAC,OAAA,EAAE,aAAY;MACrE,IAAMkC,UAAU,GAAG9H,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC8F,iBAAiB,CAAC,cAAc,CAAC;MAC9D,IAAMiC,SAAS,GAAG,IAAAC,oCAAuB,EAACH,UAAU,EAAE,gBAAgB,CAAC;MAEvE,IAAM9B,MAAM,SAASgC,SAAS,CAAC,CAAC;MAEhClF,MAAM,CAACkD,MAAM,CAAC,CAACjD,IAAI,CAAC,cAAc,CAAC;MAEnC,IAAMO,OAAO,GAAGuB,+BAAkB,CAACtB,UAAU,CAAC,gBAAgB,CAAC;MAC/DT,MAAM,CAACQ,OAAO,CAAC,CAACE,YAAY,CAAC,CAAC,CAAC;MAC/BV,MAAM,CAAC,OAAOQ,OAAO,CAAC,CAAC,CAAC,CAAC/B,KAAK,CAAC,CAACwB,IAAI,CAAC,QAAQ,CAAC;IAChD,CAAC,EAAC;IAEFJ,EAAE,CAAC,wCAAwC,EAAE,YAAM;MACjD,IAAM0D,KAAK,GAAG,IAAIC,KAAK,CAAC,YAAY,CAAC;MACrC,IAAMwB,UAAU,GAAG9H,IAAI,CAACC,EAAE,CAAC,CAAC,CAACiI,kBAAkB,CAAC,YAAM;QACpD,MAAM7B,KAAK;MACb,CAAC,CAAC;MACF,IAAM2B,SAAS,GAAG,IAAAC,oCAAuB,EAACH,UAAU,EAAE,gBAAgB,CAAC;MAEvEhF,MAAM,CAAC;QAAA,OAAMkF,SAAS,CAAC,CAAC;MAAA,EAAC,CAACvB,OAAO,CAAC,YAAY,CAAC;MAE/C,IAAMnD,OAAO,GAAGuB,+BAAkB,CAACtB,UAAU,CAAC,gBAAgB,CAAC;MAC/DT,MAAM,CAACQ,OAAO,CAAC,CAAC,CAAC,CAACH,IAAI,CAAC,CAACM,OAAO,CAAC;QAAE2C,MAAM,EAAE;MAAQ,CAAC,CAAC;IACtD,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}