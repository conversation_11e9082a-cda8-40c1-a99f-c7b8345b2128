/**
 * Navigation Guard Hook - Enhanced navigation with guards and analytics
 *
 * Hook Contract:
 * - Provides navigation with built-in guards and analytics
 * - Handles authentication and role-based navigation
 * - Tracks navigation events automatically
 * - Provides error handling for navigation failures
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { useNavigation, useRoute, useFocusEffect } from '@react-navigation/native';
import { useCallback, useRef, useEffect } from 'react';
import { useAuthStore } from '../store/authSlice';
import navigationAnalytics from '../services/navigationAnalytics';
import navigationGuards, { NavigationGuardResult } from '../services/navigationGuards';

interface UseNavigationGuardOptions {
  trackAnalytics?: boolean;
  enforceGuards?: boolean;
  logNavigation?: boolean;
}

interface GuardedNavigationResult {
  navigate: (routeName: string, params?: any) => Promise<boolean>;
  goBack: () => void;
  reset: (state: any) => void;
  canNavigate: (routeName: string, params?: any) => NavigationGuardResult;
  trackScreenView: (params?: any) => void;
  isNavigating: boolean;
}

export const useNavigationGuard = (
  options: UseNavigationGuardOptions = {}
): GuardedNavigationResult => {
  const {
    trackAnalytics = true,
    enforceGuards = true,
    logNavigation = __DEV__,
  } = options;

  const navigation = useNavigation();
  const route = useRoute();
  const { userRole } = useAuthStore();
  
  const isNavigatingRef = useRef(false);
  const screenStartTimeRef = useRef<number>(0);
  const currentScreenRef = useRef<string>(route.name);

  // Track screen focus for analytics
  useFocusEffect(
    useCallback(() => {
      const screenName = route.name;
      const startTime = Date.now();
      screenStartTimeRef.current = startTime;
      currentScreenRef.current = screenName;

      if (trackAnalytics) {
        navigationAnalytics.trackScreenView(screenName, route.params, userRole);
        
        // Track screen load time
        const loadTime = startTime - (screenStartTimeRef.current || startTime);
        if (loadTime > 0) {
          navigationAnalytics.trackScreenLoadTime(screenName, loadTime);
        }
      }

      if (logNavigation) {
        console.log(`🧭 Navigation: Focused on ${screenName}`, route.params);
      }

      // Cleanup function when screen loses focus
      return () => {
        if (trackAnalytics && screenStartTimeRef.current > 0) {
          const timeSpent = Date.now() - screenStartTimeRef.current;
          // Track time spent on screen (this would be handled by navigationAnalytics internally)
        }
      };
    }, [route.name, route.params, trackAnalytics, userRole, logNavigation])
  );

  /**
   * Enhanced navigate function with guards and analytics
   */
  const navigate = useCallback(async (
    routeName: string, 
    params?: any
  ): Promise<boolean> => {
    if (isNavigatingRef.current) {
      if (logNavigation) {
        console.warn('🧭 Navigation: Already navigating, ignoring request');
      }
      return false;
    }

    isNavigatingRef.current = true;

    try {
      // Check navigation guards
      if (enforceGuards) {
        const guardResult = navigationGuards.canNavigate(routeName, params);
        
        if (!guardResult.allowed) {
          if (logNavigation) {
            console.warn('🧭 Navigation: Blocked by guard', {
              route: routeName,
              reason: guardResult.reason,
              redirectTo: guardResult.redirectTo,
            });
          }

          // Handle guard failure
          if (guardResult.redirectTo) {
            navigation.reset({
              index: 0,
              routes: [{ name: guardResult.redirectTo }],
            });
          }

          return false;
        }
      }

      // Validate navigation flow
      const flowResult = navigationGuards.validateNavigationFlow(
        currentScreenRef.current,
        routeName,
        params
      );

      if (!flowResult.allowed) {
        if (logNavigation) {
          console.warn('🧭 Navigation: Invalid flow', {
            from: currentScreenRef.current,
            to: routeName,
            reason: flowResult.reason,
          });
        }
        return false;
      }

      // Track navigation action
      if (trackAnalytics) {
        navigationAnalytics.trackNavigationAction(
          'button_press',
          currentScreenRef.current,
          routeName,
          params
        );
      }

      // Perform navigation
      navigation.navigate(routeName as never, params as never);

      if (logNavigation) {
        console.log('🧭 Navigation: Success', { from: currentScreenRef.current, to: routeName, params });
      }

      return true;
    } catch (error) {
      console.error('🧭 Navigation: Error', error);
      
      if (trackAnalytics) {
        navigationAnalytics.trackNavigationError(
          error instanceof Error ? error.message : 'Unknown navigation error',
          routeName,
          params
        );
      }

      return false;
    } finally {
      // Reset navigation flag after a short delay
      setTimeout(() => {
        isNavigatingRef.current = false;
      }, 100);
    }
  }, [navigation, enforceGuards, trackAnalytics, logNavigation]);

  /**
   * Enhanced goBack function with analytics
   */
  const goBack = useCallback(() => {
    if (trackAnalytics) {
      navigationAnalytics.trackNavigationAction(
        'back_button',
        currentScreenRef.current,
        'previous_screen'
      );
    }

    if (logNavigation) {
      console.log('🧭 Navigation: Going back from', currentScreenRef.current);
    }

    navigation.goBack();
  }, [navigation, trackAnalytics, logNavigation]);

  /**
   * Enhanced reset function with analytics
   */
  const reset = useCallback((state: any) => {
    if (trackAnalytics) {
      navigationAnalytics.trackNavigationAction(
        'deep_link',
        currentScreenRef.current,
        state.routes?.[state.index]?.name || 'unknown'
      );
    }

    if (logNavigation) {
      console.log('🧭 Navigation: Reset to', state);
    }

    navigation.reset(state);
  }, [navigation, trackAnalytics, logNavigation]);

  /**
   * Check if navigation is allowed without actually navigating
   */
  const canNavigate = useCallback((
    routeName: string, 
    params?: any
  ): NavigationGuardResult => {
    return navigationGuards.canNavigate(routeName, params);
  }, []);

  /**
   * Manually track screen view (useful for modal screens or custom flows)
   */
  const trackScreenView = useCallback((params?: any) => {
    if (trackAnalytics) {
      navigationAnalytics.trackScreenView(route.name, params, userRole);
    }
  }, [route.name, trackAnalytics, userRole]);

  return {
    navigate,
    goBack,
    reset,
    canNavigate,
    trackScreenView,
    isNavigating: isNavigatingRef.current,
  };
};

/**
 * Hook for tracking navigation performance
 */
export const useNavigationPerformance = () => {
  const route = useRoute();
  const startTimeRef = useRef<number>(Date.now());

  useEffect(() => {
    startTimeRef.current = Date.now();
  }, [route.name]);

  const trackLoadTime = useCallback((customStartTime?: number) => {
    const loadTime = Date.now() - (customStartTime || startTimeRef.current);
    navigationAnalytics.trackScreenLoadTime(route.name, loadTime);
    return loadTime;
  }, [route.name]);

  return {
    trackLoadTime,
    getLoadTime: () => Date.now() - startTimeRef.current,
  };
};

/**
 * Hook for navigation analytics only (without guards)
 */
export const useNavigationAnalytics = () => {
  const route = useRoute();
  const { userRole } = useAuthStore();

  const trackEvent = useCallback((
    eventType: 'screen_view' | 'navigation_action' | 'flow_completion' | 'error',
    data: any
  ) => {
    switch (eventType) {
      case 'screen_view':
        navigationAnalytics.trackScreenView(data.screenName, data.params, userRole);
        break;
      case 'navigation_action':
        navigationAnalytics.trackNavigationAction(
          data.action,
          data.fromScreen,
          data.toScreen,
          data.params
        );
        break;
      case 'flow_completion':
        navigationAnalytics.trackFlowCompletion(
          data.flowName,
          data.success,
          data.metadata
        );
        break;
      case 'error':
        navigationAnalytics.trackNavigationError(
          data.error,
          data.screenName,
          data.params
        );
        break;
    }
  }, [userRole]);

  const getStats = useCallback(() => {
    return navigationAnalytics.getNavigationStats();
  }, []);

  return {
    trackEvent,
    getStats,
    currentScreen: route.name,
  };
};
