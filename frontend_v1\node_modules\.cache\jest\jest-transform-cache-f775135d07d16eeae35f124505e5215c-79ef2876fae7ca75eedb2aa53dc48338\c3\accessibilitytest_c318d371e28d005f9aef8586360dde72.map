{"version": 3, "names": ["_getJest<PERSON>bj", "mock", "AccessibilityInfo", "isScreenReaderEnabled", "jest", "fn", "announceForAccessibility", "setAccessibilityFocus", "Platform", "OS", "_interopRequireDefault", "require", "_asyncToGenerator2", "_accessibility", "_require", "describe", "beforeEach", "clearAllMocks", "it", "expect", "WCAG_CONSTANTS", "CONTRAST_RATIOS", "NORMAL_TEXT", "toBe", "LARGE_TEXT", "NON_TEXT", "TOUCH_TARGET", "MIN_SIZE", "RECOMMENDED_SIZE", "ANIMATION", "MAX_DURATION", "REDUCED_MOTION_DURATION", "label", "ScreenReaderUtils", "generateFormFieldLabel", "hint", "generateInteractionHint", "luminance", "ColorContrastUtils", "getRelativeLuminance", "toBeCloseTo", "toBeGreaterThan", "toBeLessThan", "ratio", "getContrastRatio", "passes", "meetsWCAGAA", "normalText", "largeText", "suggestion", "suggestAccessibleColor", "toBeNull", "toBeTruthy", "toMatch", "props", "SemanticMarkupUtils", "generateHeadingProps", "toEqual", "accessibilityRole", "accessibilityLevel", "accessibilityLabel", "generateListProps", "generateListItemProps", "generateButtonProps", "accessibilityHint", "state", "disabled", "undefined", "accessibilityState", "generateInputProps", "accessibilityValue", "text", "onPress", "issues", "AccessibilityTestUtils", "validateAccessibilityProps", "toHave<PERSON>ength", "toContain", "style", "width", "height", "componentTree", "type", "report", "generateAccessibilityReport", "passed", "failed", "component", "mockAccessibilityInfo", "AdvancedAccessibilityUtils", "announceLiveRegion", "toHaveBeenCalledWith", "announceContentChange", "announceFormValidation", "announceProgress", "announceLoadingState", "default", "mockResolvedValue", "stats", "AccessibilityMonitoringUtils", "trackAccessibilityUsage", "screenReaderUsage", "reducedMotionPreference", "compliance", "validateCompliance", "screenReaderSupport", "keyboardNavigation", "colorContrast", "touchTargets", "textScaling"], "sources": ["accessibility.test.ts"], "sourcesContent": ["/**\n * Accessibility Utilities Tests\n * \n * Comprehensive test suite for accessibility utilities.\n * Tests WCAG 2.2 AA compliance features and screen reader support.\n * \n * @version 1.0.0\n * <AUTHOR> Development Team\n */\n\nimport {\n  ScreenReaderUtils,\n  ColorContrastUtils,\n  FocusManagementUtils,\n  SemanticMarkupUtils,\n  AccessibilityTestUtils,\n  AdvancedAccessibilityUtils,\n  AccessibilityMonitoringUtils,\n  WCAG_CONSTANTS,\n} from '../accessibility';\n\n// Mock React Native modules\njest.mock('react-native', () => ({\n  AccessibilityInfo: {\n    isScreenReaderEnabled: jest.fn(),\n    announceForAccessibility: jest.fn(),\n    setAccessibilityFocus: jest.fn(),\n  },\n  Platform: {\n    OS: 'ios',\n  },\n}));\n\ndescribe('Accessibility Utilities', () => {\n  beforeEach(() => {\n    jest.clearAllMocks();\n  });\n\n  describe('WCAG_CONSTANTS', () => {\n    it('defines correct contrast ratios', () => {\n      expect(WCAG_CONSTANTS.CONTRAST_RATIOS.NORMAL_TEXT).toBe(4.5);\n      expect(WCAG_CONSTANTS.CONTRAST_RATIOS.LARGE_TEXT).toBe(3.0);\n      expect(WCAG_CONSTANTS.CONTRAST_RATIOS.NON_TEXT).toBe(3.0);\n    });\n\n    it('defines correct touch target sizes', () => {\n      expect(WCAG_CONSTANTS.TOUCH_TARGET.MIN_SIZE).toBe(44);\n      expect(WCAG_CONSTANTS.TOUCH_TARGET.RECOMMENDED_SIZE).toBe(48);\n    });\n\n    it('defines correct animation timing', () => {\n      expect(WCAG_CONSTANTS.ANIMATION.MAX_DURATION).toBe(5000);\n      expect(WCAG_CONSTANTS.ANIMATION.REDUCED_MOTION_DURATION).toBe(200);\n    });\n  });\n\n  describe('ScreenReaderUtils', () => {\n    describe('generateFormFieldLabel', () => {\n      it('generates basic label', () => {\n        const label = ScreenReaderUtils.generateFormFieldLabel('Email');\n        expect(label).toBe('Email');\n      });\n\n      it('adds required indicator', () => {\n        const label = ScreenReaderUtils.generateFormFieldLabel('Email', true);\n        expect(label).toBe('Email, required');\n      });\n\n      it('adds error message', () => {\n        const label = ScreenReaderUtils.generateFormFieldLabel('Email', false, 'Invalid email');\n        expect(label).toBe('Email, error: Invalid email');\n      });\n\n      it('combines required and error', () => {\n        const label = ScreenReaderUtils.generateFormFieldLabel('Email', true, 'Invalid email');\n        expect(label).toBe('Email, required, error: Invalid email');\n      });\n    });\n\n    describe('generateInteractionHint', () => {\n      it('generates basic hint', () => {\n        const hint = ScreenReaderUtils.generateInteractionHint('submit');\n        expect(hint).toBe('Double tap to submit');\n      });\n\n      it('adds additional info', () => {\n        const hint = ScreenReaderUtils.generateInteractionHint('submit', 'This will save your changes');\n        expect(hint).toBe('Double tap to submit. This will save your changes');\n      });\n    });\n  });\n\n  describe('ColorContrastUtils', () => {\n    describe('getRelativeLuminance', () => {\n      it('calculates luminance for white', () => {\n        const luminance = ColorContrastUtils.getRelativeLuminance('#FFFFFF');\n        expect(luminance).toBeCloseTo(1, 2);\n      });\n\n      it('calculates luminance for black', () => {\n        const luminance = ColorContrastUtils.getRelativeLuminance('#000000');\n        expect(luminance).toBeCloseTo(0, 2);\n      });\n\n      it('calculates luminance for gray', () => {\n        const luminance = ColorContrastUtils.getRelativeLuminance('#808080');\n        expect(luminance).toBeGreaterThan(0);\n        expect(luminance).toBeLessThan(1);\n      });\n    });\n\n    describe('getContrastRatio', () => {\n      it('calculates maximum contrast ratio', () => {\n        const ratio = ColorContrastUtils.getContrastRatio('#FFFFFF', '#000000');\n        expect(ratio).toBeCloseTo(21, 0);\n      });\n\n      it('calculates minimum contrast ratio', () => {\n        const ratio = ColorContrastUtils.getContrastRatio('#FFFFFF', '#FFFFFF');\n        expect(ratio).toBeCloseTo(1, 2);\n      });\n\n      it('calculates intermediate contrast ratio', () => {\n        const ratio = ColorContrastUtils.getContrastRatio('#FFFFFF', '#808080');\n        expect(ratio).toBeGreaterThan(1);\n        expect(ratio).toBeLessThan(21);\n      });\n    });\n\n    describe('meetsWCAGAA', () => {\n      it('passes for high contrast combinations', () => {\n        const passes = ColorContrastUtils.meetsWCAGAA('#FFFFFF', '#000000');\n        expect(passes).toBe(true);\n      });\n\n      it('fails for low contrast combinations', () => {\n        const passes = ColorContrastUtils.meetsWCAGAA('#FFFFFF', '#F0F0F0');\n        expect(passes).toBe(false);\n      });\n\n      it('uses different thresholds for large text', () => {\n        // Use a color combination that passes for large text but fails for normal text\n        const normalText = ColorContrastUtils.meetsWCAGAA('#FFFFFF', '#777777', false);\n        const largeText = ColorContrastUtils.meetsWCAGAA('#FFFFFF', '#777777', true);\n\n        // Large text has lower requirements (3.0 vs 4.5), so it should be more permissive\n        expect(normalText).toBe(false);\n        expect(largeText).toBe(true);\n      });\n    });\n\n    describe('suggestAccessibleColor', () => {\n      it('returns null for already accessible combinations', () => {\n        const suggestion = ColorContrastUtils.suggestAccessibleColor('#FFFFFF', '#000000');\n        expect(suggestion).toBeNull();\n      });\n\n      it('suggests darker color for low contrast', () => {\n        const suggestion = ColorContrastUtils.suggestAccessibleColor('#F0F0F0', '#FFFFFF');\n        expect(suggestion).toBeTruthy();\n        if (suggestion) {\n          expect(suggestion).toMatch(/^#[0-9A-Fa-f]{6}$/);\n        }\n      });\n    });\n  });\n\n  describe('SemanticMarkupUtils', () => {\n    describe('generateHeadingProps', () => {\n      it('generates heading props', () => {\n        const props = SemanticMarkupUtils.generateHeadingProps(1, 'Main Title');\n        expect(props).toEqual({\n          accessibilityRole: 'header',\n          accessibilityLevel: 1,\n          accessibilityLabel: 'Main Title',\n        });\n      });\n\n      it('handles different heading levels', () => {\n        const props = SemanticMarkupUtils.generateHeadingProps(3, 'Subtitle');\n        expect(props.accessibilityLevel).toBe(3);\n      });\n    });\n\n    describe('generateListProps', () => {\n      it('generates list props', () => {\n        const props = SemanticMarkupUtils.generateListProps(5);\n        expect(props).toEqual({\n          accessibilityRole: 'list',\n          accessibilityLabel: 'List with 5 items',\n        });\n      });\n    });\n\n    describe('generateListItemProps', () => {\n      it('generates list item props', () => {\n        const props = SemanticMarkupUtils.generateListItemProps(0, 3, 'First item');\n        expect(props).toEqual({\n          accessibilityRole: 'listitem',\n          accessibilityLabel: 'First item, 1 of 3',\n        });\n      });\n    });\n\n    describe('generateButtonProps', () => {\n      it('generates basic button props', () => {\n        const props = SemanticMarkupUtils.generateButtonProps('Submit');\n        expect(props.accessibilityRole).toBe('button');\n        expect(props.accessibilityLabel).toBe('Submit');\n      });\n\n      it('includes action hint', () => {\n        const props = SemanticMarkupUtils.generateButtonProps('Submit', 'save form');\n        expect(props.accessibilityHint).toBe('Double tap to save form');\n      });\n\n      it('includes state', () => {\n        const state = { disabled: true };\n        const props = SemanticMarkupUtils.generateButtonProps('Submit', undefined, state);\n        expect(props.accessibilityState).toEqual(state);\n      });\n    });\n\n    describe('generateInputProps', () => {\n      it('generates basic input props', () => {\n        const props = SemanticMarkupUtils.generateInputProps('Email');\n        expect(props.accessibilityLabel).toBe('Email');\n        expect(props.accessibilityState).toEqual({ disabled: false });\n      });\n\n      it('includes value', () => {\n        const props = SemanticMarkupUtils.generateInputProps('Email', '<EMAIL>');\n        expect(props.accessibilityValue).toEqual({ text: '<EMAIL>' });\n      });\n\n      it('includes required and error info', () => {\n        const props = SemanticMarkupUtils.generateInputProps('Email', undefined, true, 'Invalid email');\n        expect(props.accessibilityLabel).toBe('Email, required, error: Invalid email');\n      });\n    });\n  });\n\n  describe('AccessibilityTestUtils', () => {\n    describe('validateAccessibilityProps', () => {\n      it('passes for well-formed interactive element', () => {\n        const props = {\n          accessibilityRole: 'button',\n          accessibilityLabel: 'Submit',\n          onPress: jest.fn(),\n        };\n        \n        const issues = AccessibilityTestUtils.validateAccessibilityProps(props);\n        expect(issues).toHaveLength(0);\n      });\n\n      it('flags missing accessibility role', () => {\n        const props = {\n          onPress: jest.fn(),\n        };\n        \n        const issues = AccessibilityTestUtils.validateAccessibilityProps(props);\n        expect(issues).toContain('Interactive element missing accessibilityRole');\n      });\n\n      it('flags missing accessibility label', () => {\n        const props = {\n          accessibilityRole: 'button',\n        };\n        \n        const issues = AccessibilityTestUtils.validateAccessibilityProps(props);\n        expect(issues).toContain('Element missing accessibilityLabel or text content');\n      });\n\n      it('flags small touch targets', () => {\n        const props = {\n          accessibilityRole: 'button',\n          accessibilityLabel: 'Small button',\n          style: { width: 30, height: 30 },\n        };\n        \n        const issues = AccessibilityTestUtils.validateAccessibilityProps(props);\n        expect(issues).toContain('Touch target too small: 30x30. Minimum: 44x44');\n      });\n\n      it('passes for adequate touch targets', () => {\n        const props = {\n          accessibilityRole: 'button',\n          accessibilityLabel: 'Good button',\n          style: { width: 48, height: 48 },\n        };\n        \n        const issues = AccessibilityTestUtils.validateAccessibilityProps(props);\n        expect(issues).toHaveLength(0);\n      });\n    });\n\n    describe('generateAccessibilityReport', () => {\n      it('generates report for component tree', () => {\n        const componentTree = [\n          {\n            type: 'Button',\n            props: {\n              accessibilityRole: 'button',\n              accessibilityLabel: 'Good button',\n              onPress: jest.fn(),\n            },\n          },\n          {\n            type: 'Button',\n            props: {\n              onPress: jest.fn(),\n            },\n          },\n        ];\n        \n        const report = AccessibilityTestUtils.generateAccessibilityReport(componentTree);\n        \n        expect(report.passed).toBe(1);\n        expect(report.failed).toBe(1);\n        expect(report.issues).toHaveLength(1);\n        expect(report.issues[0].component).toBe('Button');\n        expect(report.issues[0].issues).toContain('Interactive element missing accessibilityRole');\n      });\n\n      it('handles empty component tree', () => {\n        const report = AccessibilityTestUtils.generateAccessibilityReport([]);\n\n        expect(report.passed).toBe(0);\n        expect(report.failed).toBe(0);\n        expect(report.issues).toHaveLength(0);\n      });\n    });\n  });\n\n  describe('AdvancedAccessibilityUtils', () => {\n    beforeEach(() => {\n      jest.clearAllMocks();\n    });\n\n    describe('announceLiveRegion', () => {\n      it('should announce with polite priority', () => {\n        const mockAccessibilityInfo = require('react-native').AccessibilityInfo;\n\n        AdvancedAccessibilityUtils.announceLiveRegion('Test message', 'polite');\n        expect(mockAccessibilityInfo.announceForAccessibility).toHaveBeenCalledWith('Test message');\n      });\n\n      it('should announce with assertive priority', () => {\n        const mockAccessibilityInfo = require('react-native').AccessibilityInfo;\n\n        AdvancedAccessibilityUtils.announceLiveRegion('Urgent message', 'assertive');\n        expect(mockAccessibilityInfo.announceForAccessibility).toHaveBeenCalledWith('Urgent message');\n      });\n    });\n\n    describe('announceContentChange', () => {\n      it('should announce content changes', () => {\n        const mockAccessibilityInfo = require('react-native').AccessibilityInfo;\n\n        AdvancedAccessibilityUtils.announceContentChange('Item', 'added', 'to cart');\n        expect(mockAccessibilityInfo.announceForAccessibility).toHaveBeenCalledWith('Item added: to cart');\n      });\n    });\n\n    describe('announceFormValidation', () => {\n      it('should announce valid form field', () => {\n        const mockAccessibilityInfo = require('react-native').AccessibilityInfo;\n\n        AdvancedAccessibilityUtils.announceFormValidation('Email', true);\n        expect(mockAccessibilityInfo.announceForAccessibility).toHaveBeenCalledWith('Email is valid');\n      });\n\n      it('should announce invalid form field', () => {\n        const mockAccessibilityInfo = require('react-native').AccessibilityInfo;\n\n        AdvancedAccessibilityUtils.announceFormValidation('Email', false, 'Invalid format');\n        expect(mockAccessibilityInfo.announceForAccessibility).toHaveBeenCalledWith('Email error: Invalid format');\n      });\n    });\n\n    describe('announceProgress', () => {\n      it('should announce progress updates', () => {\n        const mockAccessibilityInfo = require('react-native').AccessibilityInfo;\n\n        AdvancedAccessibilityUtils.announceProgress(3, 5, 'Upload');\n        expect(mockAccessibilityInfo.announceForAccessibility).toHaveBeenCalledWith('Upload: 60% complete, 3 of 5');\n      });\n    });\n\n    describe('announceLoadingState', () => {\n      it('should announce loading start', () => {\n        const mockAccessibilityInfo = require('react-native').AccessibilityInfo;\n\n        AdvancedAccessibilityUtils.announceLoadingState(true, 'Data');\n        expect(mockAccessibilityInfo.announceForAccessibility).toHaveBeenCalledWith('Data loading');\n      });\n\n      it('should announce loading complete', () => {\n        const mockAccessibilityInfo = require('react-native').AccessibilityInfo;\n\n        AdvancedAccessibilityUtils.announceLoadingState(false, 'Data');\n        expect(mockAccessibilityInfo.announceForAccessibility).toHaveBeenCalledWith('Data loaded');\n      });\n    });\n  });\n\n  describe('AccessibilityMonitoringUtils', () => {\n    beforeEach(() => {\n      jest.clearAllMocks();\n    });\n\n    describe('trackAccessibilityUsage', () => {\n      it('should track accessibility usage statistics', async () => {\n        const mockAccessibilityInfo = require('react-native').AccessibilityInfo;\n        mockAccessibilityInfo.isScreenReaderEnabled.mockResolvedValue(true);\n\n        const stats = await AccessibilityMonitoringUtils.trackAccessibilityUsage();\n\n        expect(stats.screenReaderUsage).toBe(1);\n        expect(stats.reducedMotionPreference).toBe(false);\n      });\n    });\n\n    describe('validateCompliance', () => {\n      it('should validate accessibility compliance', async () => {\n        const mockAccessibilityInfo = require('react-native').AccessibilityInfo;\n        mockAccessibilityInfo.isScreenReaderEnabled.mockResolvedValue(true);\n\n        const compliance = await AccessibilityMonitoringUtils.validateCompliance();\n\n        expect(compliance.screenReaderSupport).toBe(true);\n        expect(compliance.keyboardNavigation).toBe(true);\n        expect(compliance.colorContrast).toBe(true);\n        expect(compliance.touchTargets).toBe(true);\n        expect(compliance.textScaling).toBe(true);\n      });\n    });\n  });\n});\n"], "mappings": "AAsBAA,WAAA,GAAKC,IAAI,CAAC,cAAc,EAAE;EAAA,OAAO;IAC/BC,iBAAiB,EAAE;MACjBC,qBAAqB,EAAEC,IAAI,CAACC,EAAE,CAAC,CAAC;MAChCC,wBAAwB,EAAEF,IAAI,CAACC,EAAE,CAAC,CAAC;MACnCE,qBAAqB,EAAEH,IAAI,CAACC,EAAE,CAAC;IACjC,CAAC;IACDG,QAAQ,EAAE;MACRC,EAAE,EAAE;IACN;EACF,CAAC;AAAA,CAAC,CAAC;AAAC,IAAAC,sBAAA,GAAAC,OAAA;AAAA,IAAAC,kBAAA,GAAAF,sBAAA,CAAAC,OAAA;AArBJ,IAAAE,cAAA,GAAAF,OAAA;AAS0B,SAAAX,YAAA;EAAA,IAAAc,QAAA,GAAAH,OAAA;IAAAP,IAAA,GAAAU,QAAA,CAAAV,IAAA;EAAAJ,WAAA,YAAAA,YAAA;IAAA,OAAAI,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAc1BW,QAAQ,CAAC,yBAAyB,EAAE,YAAM;EACxCC,UAAU,CAAC,YAAM;IACfZ,IAAI,CAACa,aAAa,CAAC,CAAC;EACtB,CAAC,CAAC;EAEFF,QAAQ,CAAC,gBAAgB,EAAE,YAAM;IAC/BG,EAAE,CAAC,iCAAiC,EAAE,YAAM;MAC1CC,MAAM,CAACC,6BAAc,CAACC,eAAe,CAACC,WAAW,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;MAC5DJ,MAAM,CAACC,6BAAc,CAACC,eAAe,CAACG,UAAU,CAAC,CAACD,IAAI,CAAC,GAAG,CAAC;MAC3DJ,MAAM,CAACC,6BAAc,CAACC,eAAe,CAACI,QAAQ,CAAC,CAACF,IAAI,CAAC,GAAG,CAAC;IAC3D,CAAC,CAAC;IAEFL,EAAE,CAAC,oCAAoC,EAAE,YAAM;MAC7CC,MAAM,CAACC,6BAAc,CAACM,YAAY,CAACC,QAAQ,CAAC,CAACJ,IAAI,CAAC,EAAE,CAAC;MACrDJ,MAAM,CAACC,6BAAc,CAACM,YAAY,CAACE,gBAAgB,CAAC,CAACL,IAAI,CAAC,EAAE,CAAC;IAC/D,CAAC,CAAC;IAEFL,EAAE,CAAC,kCAAkC,EAAE,YAAM;MAC3CC,MAAM,CAACC,6BAAc,CAACS,SAAS,CAACC,YAAY,CAAC,CAACP,IAAI,CAAC,IAAI,CAAC;MACxDJ,MAAM,CAACC,6BAAc,CAACS,SAAS,CAACE,uBAAuB,CAAC,CAACR,IAAI,CAAC,GAAG,CAAC;IACpE,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFR,QAAQ,CAAC,mBAAmB,EAAE,YAAM;IAClCA,QAAQ,CAAC,wBAAwB,EAAE,YAAM;MACvCG,EAAE,CAAC,uBAAuB,EAAE,YAAM;QAChC,IAAMc,KAAK,GAAGC,gCAAiB,CAACC,sBAAsB,CAAC,OAAO,CAAC;QAC/Df,MAAM,CAACa,KAAK,CAAC,CAACT,IAAI,CAAC,OAAO,CAAC;MAC7B,CAAC,CAAC;MAEFL,EAAE,CAAC,yBAAyB,EAAE,YAAM;QAClC,IAAMc,KAAK,GAAGC,gCAAiB,CAACC,sBAAsB,CAAC,OAAO,EAAE,IAAI,CAAC;QACrEf,MAAM,CAACa,KAAK,CAAC,CAACT,IAAI,CAAC,iBAAiB,CAAC;MACvC,CAAC,CAAC;MAEFL,EAAE,CAAC,oBAAoB,EAAE,YAAM;QAC7B,IAAMc,KAAK,GAAGC,gCAAiB,CAACC,sBAAsB,CAAC,OAAO,EAAE,KAAK,EAAE,eAAe,CAAC;QACvFf,MAAM,CAACa,KAAK,CAAC,CAACT,IAAI,CAAC,6BAA6B,CAAC;MACnD,CAAC,CAAC;MAEFL,EAAE,CAAC,6BAA6B,EAAE,YAAM;QACtC,IAAMc,KAAK,GAAGC,gCAAiB,CAACC,sBAAsB,CAAC,OAAO,EAAE,IAAI,EAAE,eAAe,CAAC;QACtFf,MAAM,CAACa,KAAK,CAAC,CAACT,IAAI,CAAC,uCAAuC,CAAC;MAC7D,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFR,QAAQ,CAAC,yBAAyB,EAAE,YAAM;MACxCG,EAAE,CAAC,sBAAsB,EAAE,YAAM;QAC/B,IAAMiB,IAAI,GAAGF,gCAAiB,CAACG,uBAAuB,CAAC,QAAQ,CAAC;QAChEjB,MAAM,CAACgB,IAAI,CAAC,CAACZ,IAAI,CAAC,sBAAsB,CAAC;MAC3C,CAAC,CAAC;MAEFL,EAAE,CAAC,sBAAsB,EAAE,YAAM;QAC/B,IAAMiB,IAAI,GAAGF,gCAAiB,CAACG,uBAAuB,CAAC,QAAQ,EAAE,6BAA6B,CAAC;QAC/FjB,MAAM,CAACgB,IAAI,CAAC,CAACZ,IAAI,CAAC,mDAAmD,CAAC;MACxE,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFR,QAAQ,CAAC,oBAAoB,EAAE,YAAM;IACnCA,QAAQ,CAAC,sBAAsB,EAAE,YAAM;MACrCG,EAAE,CAAC,gCAAgC,EAAE,YAAM;QACzC,IAAMmB,SAAS,GAAGC,iCAAkB,CAACC,oBAAoB,CAAC,SAAS,CAAC;QACpEpB,MAAM,CAACkB,SAAS,CAAC,CAACG,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC;MACrC,CAAC,CAAC;MAEFtB,EAAE,CAAC,gCAAgC,EAAE,YAAM;QACzC,IAAMmB,SAAS,GAAGC,iCAAkB,CAACC,oBAAoB,CAAC,SAAS,CAAC;QACpEpB,MAAM,CAACkB,SAAS,CAAC,CAACG,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC;MACrC,CAAC,CAAC;MAEFtB,EAAE,CAAC,+BAA+B,EAAE,YAAM;QACxC,IAAMmB,SAAS,GAAGC,iCAAkB,CAACC,oBAAoB,CAAC,SAAS,CAAC;QACpEpB,MAAM,CAACkB,SAAS,CAAC,CAACI,eAAe,CAAC,CAAC,CAAC;QACpCtB,MAAM,CAACkB,SAAS,CAAC,CAACK,YAAY,CAAC,CAAC,CAAC;MACnC,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF3B,QAAQ,CAAC,kBAAkB,EAAE,YAAM;MACjCG,EAAE,CAAC,mCAAmC,EAAE,YAAM;QAC5C,IAAMyB,KAAK,GAAGL,iCAAkB,CAACM,gBAAgB,CAAC,SAAS,EAAE,SAAS,CAAC;QACvEzB,MAAM,CAACwB,KAAK,CAAC,CAACH,WAAW,CAAC,EAAE,EAAE,CAAC,CAAC;MAClC,CAAC,CAAC;MAEFtB,EAAE,CAAC,mCAAmC,EAAE,YAAM;QAC5C,IAAMyB,KAAK,GAAGL,iCAAkB,CAACM,gBAAgB,CAAC,SAAS,EAAE,SAAS,CAAC;QACvEzB,MAAM,CAACwB,KAAK,CAAC,CAACH,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC;MACjC,CAAC,CAAC;MAEFtB,EAAE,CAAC,wCAAwC,EAAE,YAAM;QACjD,IAAMyB,KAAK,GAAGL,iCAAkB,CAACM,gBAAgB,CAAC,SAAS,EAAE,SAAS,CAAC;QACvEzB,MAAM,CAACwB,KAAK,CAAC,CAACF,eAAe,CAAC,CAAC,CAAC;QAChCtB,MAAM,CAACwB,KAAK,CAAC,CAACD,YAAY,CAAC,EAAE,CAAC;MAChC,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF3B,QAAQ,CAAC,aAAa,EAAE,YAAM;MAC5BG,EAAE,CAAC,uCAAuC,EAAE,YAAM;QAChD,IAAM2B,MAAM,GAAGP,iCAAkB,CAACQ,WAAW,CAAC,SAAS,EAAE,SAAS,CAAC;QACnE3B,MAAM,CAAC0B,MAAM,CAAC,CAACtB,IAAI,CAAC,IAAI,CAAC;MAC3B,CAAC,CAAC;MAEFL,EAAE,CAAC,qCAAqC,EAAE,YAAM;QAC9C,IAAM2B,MAAM,GAAGP,iCAAkB,CAACQ,WAAW,CAAC,SAAS,EAAE,SAAS,CAAC;QACnE3B,MAAM,CAAC0B,MAAM,CAAC,CAACtB,IAAI,CAAC,KAAK,CAAC;MAC5B,CAAC,CAAC;MAEFL,EAAE,CAAC,0CAA0C,EAAE,YAAM;QAEnD,IAAM6B,UAAU,GAAGT,iCAAkB,CAACQ,WAAW,CAAC,SAAS,EAAE,SAAS,EAAE,KAAK,CAAC;QAC9E,IAAME,SAAS,GAAGV,iCAAkB,CAACQ,WAAW,CAAC,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC;QAG5E3B,MAAM,CAAC4B,UAAU,CAAC,CAACxB,IAAI,CAAC,KAAK,CAAC;QAC9BJ,MAAM,CAAC6B,SAAS,CAAC,CAACzB,IAAI,CAAC,IAAI,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFR,QAAQ,CAAC,wBAAwB,EAAE,YAAM;MACvCG,EAAE,CAAC,kDAAkD,EAAE,YAAM;QAC3D,IAAM+B,UAAU,GAAGX,iCAAkB,CAACY,sBAAsB,CAAC,SAAS,EAAE,SAAS,CAAC;QAClF/B,MAAM,CAAC8B,UAAU,CAAC,CAACE,QAAQ,CAAC,CAAC;MAC/B,CAAC,CAAC;MAEFjC,EAAE,CAAC,wCAAwC,EAAE,YAAM;QACjD,IAAM+B,UAAU,GAAGX,iCAAkB,CAACY,sBAAsB,CAAC,SAAS,EAAE,SAAS,CAAC;QAClF/B,MAAM,CAAC8B,UAAU,CAAC,CAACG,UAAU,CAAC,CAAC;QAC/B,IAAIH,UAAU,EAAE;UACd9B,MAAM,CAAC8B,UAAU,CAAC,CAACI,OAAO,CAAC,mBAAmB,CAAC;QACjD;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFtC,QAAQ,CAAC,qBAAqB,EAAE,YAAM;IACpCA,QAAQ,CAAC,sBAAsB,EAAE,YAAM;MACrCG,EAAE,CAAC,yBAAyB,EAAE,YAAM;QAClC,IAAMoC,KAAK,GAAGC,kCAAmB,CAACC,oBAAoB,CAAC,CAAC,EAAE,YAAY,CAAC;QACvErC,MAAM,CAACmC,KAAK,CAAC,CAACG,OAAO,CAAC;UACpBC,iBAAiB,EAAE,QAAQ;UAC3BC,kBAAkB,EAAE,CAAC;UACrBC,kBAAkB,EAAE;QACtB,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF1C,EAAE,CAAC,kCAAkC,EAAE,YAAM;QAC3C,IAAMoC,KAAK,GAAGC,kCAAmB,CAACC,oBAAoB,CAAC,CAAC,EAAE,UAAU,CAAC;QACrErC,MAAM,CAACmC,KAAK,CAACK,kBAAkB,CAAC,CAACpC,IAAI,CAAC,CAAC,CAAC;MAC1C,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFR,QAAQ,CAAC,mBAAmB,EAAE,YAAM;MAClCG,EAAE,CAAC,sBAAsB,EAAE,YAAM;QAC/B,IAAMoC,KAAK,GAAGC,kCAAmB,CAACM,iBAAiB,CAAC,CAAC,CAAC;QACtD1C,MAAM,CAACmC,KAAK,CAAC,CAACG,OAAO,CAAC;UACpBC,iBAAiB,EAAE,MAAM;UACzBE,kBAAkB,EAAE;QACtB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF7C,QAAQ,CAAC,uBAAuB,EAAE,YAAM;MACtCG,EAAE,CAAC,2BAA2B,EAAE,YAAM;QACpC,IAAMoC,KAAK,GAAGC,kCAAmB,CAACO,qBAAqB,CAAC,CAAC,EAAE,CAAC,EAAE,YAAY,CAAC;QAC3E3C,MAAM,CAACmC,KAAK,CAAC,CAACG,OAAO,CAAC;UACpBC,iBAAiB,EAAE,UAAU;UAC7BE,kBAAkB,EAAE;QACtB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF7C,QAAQ,CAAC,qBAAqB,EAAE,YAAM;MACpCG,EAAE,CAAC,8BAA8B,EAAE,YAAM;QACvC,IAAMoC,KAAK,GAAGC,kCAAmB,CAACQ,mBAAmB,CAAC,QAAQ,CAAC;QAC/D5C,MAAM,CAACmC,KAAK,CAACI,iBAAiB,CAAC,CAACnC,IAAI,CAAC,QAAQ,CAAC;QAC9CJ,MAAM,CAACmC,KAAK,CAACM,kBAAkB,CAAC,CAACrC,IAAI,CAAC,QAAQ,CAAC;MACjD,CAAC,CAAC;MAEFL,EAAE,CAAC,sBAAsB,EAAE,YAAM;QAC/B,IAAMoC,KAAK,GAAGC,kCAAmB,CAACQ,mBAAmB,CAAC,QAAQ,EAAE,WAAW,CAAC;QAC5E5C,MAAM,CAACmC,KAAK,CAACU,iBAAiB,CAAC,CAACzC,IAAI,CAAC,yBAAyB,CAAC;MACjE,CAAC,CAAC;MAEFL,EAAE,CAAC,gBAAgB,EAAE,YAAM;QACzB,IAAM+C,KAAK,GAAG;UAAEC,QAAQ,EAAE;QAAK,CAAC;QAChC,IAAMZ,KAAK,GAAGC,kCAAmB,CAACQ,mBAAmB,CAAC,QAAQ,EAAEI,SAAS,EAAEF,KAAK,CAAC;QACjF9C,MAAM,CAACmC,KAAK,CAACc,kBAAkB,CAAC,CAACX,OAAO,CAACQ,KAAK,CAAC;MACjD,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFlD,QAAQ,CAAC,oBAAoB,EAAE,YAAM;MACnCG,EAAE,CAAC,6BAA6B,EAAE,YAAM;QACtC,IAAMoC,KAAK,GAAGC,kCAAmB,CAACc,kBAAkB,CAAC,OAAO,CAAC;QAC7DlD,MAAM,CAACmC,KAAK,CAACM,kBAAkB,CAAC,CAACrC,IAAI,CAAC,OAAO,CAAC;QAC9CJ,MAAM,CAACmC,KAAK,CAACc,kBAAkB,CAAC,CAACX,OAAO,CAAC;UAAES,QAAQ,EAAE;QAAM,CAAC,CAAC;MAC/D,CAAC,CAAC;MAEFhD,EAAE,CAAC,gBAAgB,EAAE,YAAM;QACzB,IAAMoC,KAAK,GAAGC,kCAAmB,CAACc,kBAAkB,CAAC,OAAO,EAAE,kBAAkB,CAAC;QACjFlD,MAAM,CAACmC,KAAK,CAACgB,kBAAkB,CAAC,CAACb,OAAO,CAAC;UAAEc,IAAI,EAAE;QAAmB,CAAC,CAAC;MACxE,CAAC,CAAC;MAEFrD,EAAE,CAAC,kCAAkC,EAAE,YAAM;QAC3C,IAAMoC,KAAK,GAAGC,kCAAmB,CAACc,kBAAkB,CAAC,OAAO,EAAEF,SAAS,EAAE,IAAI,EAAE,eAAe,CAAC;QAC/FhD,MAAM,CAACmC,KAAK,CAACM,kBAAkB,CAAC,CAACrC,IAAI,CAAC,uCAAuC,CAAC;MAChF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFR,QAAQ,CAAC,wBAAwB,EAAE,YAAM;IACvCA,QAAQ,CAAC,4BAA4B,EAAE,YAAM;MAC3CG,EAAE,CAAC,4CAA4C,EAAE,YAAM;QACrD,IAAMoC,KAAK,GAAG;UACZI,iBAAiB,EAAE,QAAQ;UAC3BE,kBAAkB,EAAE,QAAQ;UAC5BY,OAAO,EAAEpE,IAAI,CAACC,EAAE,CAAC;QACnB,CAAC;QAED,IAAMoE,MAAM,GAAGC,qCAAsB,CAACC,0BAA0B,CAACrB,KAAK,CAAC;QACvEnC,MAAM,CAACsD,MAAM,CAAC,CAACG,YAAY,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC;MAEF1D,EAAE,CAAC,kCAAkC,EAAE,YAAM;QAC3C,IAAMoC,KAAK,GAAG;UACZkB,OAAO,EAAEpE,IAAI,CAACC,EAAE,CAAC;QACnB,CAAC;QAED,IAAMoE,MAAM,GAAGC,qCAAsB,CAACC,0BAA0B,CAACrB,KAAK,CAAC;QACvEnC,MAAM,CAACsD,MAAM,CAAC,CAACI,SAAS,CAAC,+CAA+C,CAAC;MAC3E,CAAC,CAAC;MAEF3D,EAAE,CAAC,mCAAmC,EAAE,YAAM;QAC5C,IAAMoC,KAAK,GAAG;UACZI,iBAAiB,EAAE;QACrB,CAAC;QAED,IAAMe,MAAM,GAAGC,qCAAsB,CAACC,0BAA0B,CAACrB,KAAK,CAAC;QACvEnC,MAAM,CAACsD,MAAM,CAAC,CAACI,SAAS,CAAC,oDAAoD,CAAC;MAChF,CAAC,CAAC;MAEF3D,EAAE,CAAC,2BAA2B,EAAE,YAAM;QACpC,IAAMoC,KAAK,GAAG;UACZI,iBAAiB,EAAE,QAAQ;UAC3BE,kBAAkB,EAAE,cAAc;UAClCkB,KAAK,EAAE;YAAEC,KAAK,EAAE,EAAE;YAAEC,MAAM,EAAE;UAAG;QACjC,CAAC;QAED,IAAMP,MAAM,GAAGC,qCAAsB,CAACC,0BAA0B,CAACrB,KAAK,CAAC;QACvEnC,MAAM,CAACsD,MAAM,CAAC,CAACI,SAAS,CAAC,+CAA+C,CAAC;MAC3E,CAAC,CAAC;MAEF3D,EAAE,CAAC,mCAAmC,EAAE,YAAM;QAC5C,IAAMoC,KAAK,GAAG;UACZI,iBAAiB,EAAE,QAAQ;UAC3BE,kBAAkB,EAAE,aAAa;UACjCkB,KAAK,EAAE;YAAEC,KAAK,EAAE,EAAE;YAAEC,MAAM,EAAE;UAAG;QACjC,CAAC;QAED,IAAMP,MAAM,GAAGC,qCAAsB,CAACC,0BAA0B,CAACrB,KAAK,CAAC;QACvEnC,MAAM,CAACsD,MAAM,CAAC,CAACG,YAAY,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF7D,QAAQ,CAAC,6BAA6B,EAAE,YAAM;MAC5CG,EAAE,CAAC,qCAAqC,EAAE,YAAM;QAC9C,IAAM+D,aAAa,GAAG,CACpB;UACEC,IAAI,EAAE,QAAQ;UACd5B,KAAK,EAAE;YACLI,iBAAiB,EAAE,QAAQ;YAC3BE,kBAAkB,EAAE,aAAa;YACjCY,OAAO,EAAEpE,IAAI,CAACC,EAAE,CAAC;UACnB;QACF,CAAC,EACD;UACE6E,IAAI,EAAE,QAAQ;UACd5B,KAAK,EAAE;YACLkB,OAAO,EAAEpE,IAAI,CAACC,EAAE,CAAC;UACnB;QACF,CAAC,CACF;QAED,IAAM8E,MAAM,GAAGT,qCAAsB,CAACU,2BAA2B,CAACH,aAAa,CAAC;QAEhF9D,MAAM,CAACgE,MAAM,CAACE,MAAM,CAAC,CAAC9D,IAAI,CAAC,CAAC,CAAC;QAC7BJ,MAAM,CAACgE,MAAM,CAACG,MAAM,CAAC,CAAC/D,IAAI,CAAC,CAAC,CAAC;QAC7BJ,MAAM,CAACgE,MAAM,CAACV,MAAM,CAAC,CAACG,YAAY,CAAC,CAAC,CAAC;QACrCzD,MAAM,CAACgE,MAAM,CAACV,MAAM,CAAC,CAAC,CAAC,CAACc,SAAS,CAAC,CAAChE,IAAI,CAAC,QAAQ,CAAC;QACjDJ,MAAM,CAACgE,MAAM,CAACV,MAAM,CAAC,CAAC,CAAC,CAACA,MAAM,CAAC,CAACI,SAAS,CAAC,+CAA+C,CAAC;MAC5F,CAAC,CAAC;MAEF3D,EAAE,CAAC,8BAA8B,EAAE,YAAM;QACvC,IAAMiE,MAAM,GAAGT,qCAAsB,CAACU,2BAA2B,CAAC,EAAE,CAAC;QAErEjE,MAAM,CAACgE,MAAM,CAACE,MAAM,CAAC,CAAC9D,IAAI,CAAC,CAAC,CAAC;QAC7BJ,MAAM,CAACgE,MAAM,CAACG,MAAM,CAAC,CAAC/D,IAAI,CAAC,CAAC,CAAC;QAC7BJ,MAAM,CAACgE,MAAM,CAACV,MAAM,CAAC,CAACG,YAAY,CAAC,CAAC,CAAC;MACvC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7D,QAAQ,CAAC,4BAA4B,EAAE,YAAM;IAC3CC,UAAU,CAAC,YAAM;MACfZ,IAAI,CAACa,aAAa,CAAC,CAAC;IACtB,CAAC,CAAC;IAEFF,QAAQ,CAAC,oBAAoB,EAAE,YAAM;MACnCG,EAAE,CAAC,sCAAsC,EAAE,YAAM;QAC/C,IAAMsE,qBAAqB,GAAG7E,OAAO,CAAC,cAAc,CAAC,CAACT,iBAAiB;QAEvEuF,yCAA0B,CAACC,kBAAkB,CAAC,cAAc,EAAE,QAAQ,CAAC;QACvEvE,MAAM,CAACqE,qBAAqB,CAAClF,wBAAwB,CAAC,CAACqF,oBAAoB,CAAC,cAAc,CAAC;MAC7F,CAAC,CAAC;MAEFzE,EAAE,CAAC,yCAAyC,EAAE,YAAM;QAClD,IAAMsE,qBAAqB,GAAG7E,OAAO,CAAC,cAAc,CAAC,CAACT,iBAAiB;QAEvEuF,yCAA0B,CAACC,kBAAkB,CAAC,gBAAgB,EAAE,WAAW,CAAC;QAC5EvE,MAAM,CAACqE,qBAAqB,CAAClF,wBAAwB,CAAC,CAACqF,oBAAoB,CAAC,gBAAgB,CAAC;MAC/F,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF5E,QAAQ,CAAC,uBAAuB,EAAE,YAAM;MACtCG,EAAE,CAAC,iCAAiC,EAAE,YAAM;QAC1C,IAAMsE,qBAAqB,GAAG7E,OAAO,CAAC,cAAc,CAAC,CAACT,iBAAiB;QAEvEuF,yCAA0B,CAACG,qBAAqB,CAAC,MAAM,EAAE,OAAO,EAAE,SAAS,CAAC;QAC5EzE,MAAM,CAACqE,qBAAqB,CAAClF,wBAAwB,CAAC,CAACqF,oBAAoB,CAAC,qBAAqB,CAAC;MACpG,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF5E,QAAQ,CAAC,wBAAwB,EAAE,YAAM;MACvCG,EAAE,CAAC,kCAAkC,EAAE,YAAM;QAC3C,IAAMsE,qBAAqB,GAAG7E,OAAO,CAAC,cAAc,CAAC,CAACT,iBAAiB;QAEvEuF,yCAA0B,CAACI,sBAAsB,CAAC,OAAO,EAAE,IAAI,CAAC;QAChE1E,MAAM,CAACqE,qBAAqB,CAAClF,wBAAwB,CAAC,CAACqF,oBAAoB,CAAC,gBAAgB,CAAC;MAC/F,CAAC,CAAC;MAEFzE,EAAE,CAAC,oCAAoC,EAAE,YAAM;QAC7C,IAAMsE,qBAAqB,GAAG7E,OAAO,CAAC,cAAc,CAAC,CAACT,iBAAiB;QAEvEuF,yCAA0B,CAACI,sBAAsB,CAAC,OAAO,EAAE,KAAK,EAAE,gBAAgB,CAAC;QACnF1E,MAAM,CAACqE,qBAAqB,CAAClF,wBAAwB,CAAC,CAACqF,oBAAoB,CAAC,6BAA6B,CAAC;MAC5G,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF5E,QAAQ,CAAC,kBAAkB,EAAE,YAAM;MACjCG,EAAE,CAAC,kCAAkC,EAAE,YAAM;QAC3C,IAAMsE,qBAAqB,GAAG7E,OAAO,CAAC,cAAc,CAAC,CAACT,iBAAiB;QAEvEuF,yCAA0B,CAACK,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC;QAC3D3E,MAAM,CAACqE,qBAAqB,CAAClF,wBAAwB,CAAC,CAACqF,oBAAoB,CAAC,8BAA8B,CAAC;MAC7G,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF5E,QAAQ,CAAC,sBAAsB,EAAE,YAAM;MACrCG,EAAE,CAAC,+BAA+B,EAAE,YAAM;QACxC,IAAMsE,qBAAqB,GAAG7E,OAAO,CAAC,cAAc,CAAC,CAACT,iBAAiB;QAEvEuF,yCAA0B,CAACM,oBAAoB,CAAC,IAAI,EAAE,MAAM,CAAC;QAC7D5E,MAAM,CAACqE,qBAAqB,CAAClF,wBAAwB,CAAC,CAACqF,oBAAoB,CAAC,cAAc,CAAC;MAC7F,CAAC,CAAC;MAEFzE,EAAE,CAAC,kCAAkC,EAAE,YAAM;QAC3C,IAAMsE,qBAAqB,GAAG7E,OAAO,CAAC,cAAc,CAAC,CAACT,iBAAiB;QAEvEuF,yCAA0B,CAACM,oBAAoB,CAAC,KAAK,EAAE,MAAM,CAAC;QAC9D5E,MAAM,CAACqE,qBAAqB,CAAClF,wBAAwB,CAAC,CAACqF,oBAAoB,CAAC,aAAa,CAAC;MAC5F,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF5E,QAAQ,CAAC,8BAA8B,EAAE,YAAM;IAC7CC,UAAU,CAAC,YAAM;MACfZ,IAAI,CAACa,aAAa,CAAC,CAAC;IACtB,CAAC,CAAC;IAEFF,QAAQ,CAAC,yBAAyB,EAAE,YAAM;MACxCG,EAAE,CAAC,6CAA6C,MAAAN,kBAAA,CAAAoF,OAAA,EAAE,aAAY;QAC5D,IAAMR,qBAAqB,GAAG7E,OAAO,CAAC,cAAc,CAAC,CAACT,iBAAiB;QACvEsF,qBAAqB,CAACrF,qBAAqB,CAAC8F,iBAAiB,CAAC,IAAI,CAAC;QAEnE,IAAMC,KAAK,SAASC,2CAA4B,CAACC,uBAAuB,CAAC,CAAC;QAE1EjF,MAAM,CAAC+E,KAAK,CAACG,iBAAiB,CAAC,CAAC9E,IAAI,CAAC,CAAC,CAAC;QACvCJ,MAAM,CAAC+E,KAAK,CAACI,uBAAuB,CAAC,CAAC/E,IAAI,CAAC,KAAK,CAAC;MACnD,CAAC,EAAC;IACJ,CAAC,CAAC;IAEFR,QAAQ,CAAC,oBAAoB,EAAE,YAAM;MACnCG,EAAE,CAAC,0CAA0C,MAAAN,kBAAA,CAAAoF,OAAA,EAAE,aAAY;QACzD,IAAMR,qBAAqB,GAAG7E,OAAO,CAAC,cAAc,CAAC,CAACT,iBAAiB;QACvEsF,qBAAqB,CAACrF,qBAAqB,CAAC8F,iBAAiB,CAAC,IAAI,CAAC;QAEnE,IAAMM,UAAU,SAASJ,2CAA4B,CAACK,kBAAkB,CAAC,CAAC;QAE1ErF,MAAM,CAACoF,UAAU,CAACE,mBAAmB,CAAC,CAAClF,IAAI,CAAC,IAAI,CAAC;QACjDJ,MAAM,CAACoF,UAAU,CAACG,kBAAkB,CAAC,CAACnF,IAAI,CAAC,IAAI,CAAC;QAChDJ,MAAM,CAACoF,UAAU,CAACI,aAAa,CAAC,CAACpF,IAAI,CAAC,IAAI,CAAC;QAC3CJ,MAAM,CAACoF,UAAU,CAACK,YAAY,CAAC,CAACrF,IAAI,CAAC,IAAI,CAAC;QAC1CJ,MAAM,CAACoF,UAAU,CAACM,WAAW,CAAC,CAACtF,IAAI,CAAC,IAAI,CAAC;MAC3C,CAAC,EAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}