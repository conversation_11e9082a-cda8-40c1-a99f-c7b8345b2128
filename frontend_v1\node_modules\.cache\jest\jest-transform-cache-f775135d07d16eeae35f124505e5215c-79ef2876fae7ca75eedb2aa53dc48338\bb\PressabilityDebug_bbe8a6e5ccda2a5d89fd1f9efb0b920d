495d44f0bd854d3cc08d428d8a876599
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.PressabilityDebugView = PressabilityDebugView;
exports.isEnabled = isEnabled;
exports.setEnabled = setEnabled;
var _View = _interopRequireDefault(require("../Components/View/View"));
var _normalizeColor = _interopRequireDefault(require("../StyleSheet/normalizeColor"));
var _Rect = require("../StyleSheet/Rect");
var React = _interopRequireWildcard(require("react"));
var _jsxRuntime = require("react/jsx-runtime");
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
function PressabilityDebugView(props) {
  if (__DEV__) {
    if (isEnabled()) {
      var _hitSlop$bottom, _hitSlop$left, _hitSlop$right, _hitSlop$top;
      var normalizedColor = (0, _normalizeColor.default)(props.color);
      if (typeof normalizedColor !== 'number') {
        return null;
      }
      var baseColor = '#' + (normalizedColor != null ? normalizedColor : 0).toString(16).padStart(8, '0');
      var hitSlop = (0, _Rect.normalizeRect)(props.hitSlop);
      return (0, _jsxRuntime.jsx)(_View.default, {
        pointerEvents: "none",
        style: {
          backgroundColor: baseColor.slice(0, -2) + '0F',
          borderColor: baseColor.slice(0, -2) + '55',
          borderStyle: 'dashed',
          borderWidth: 1,
          bottom: -((_hitSlop$bottom = hitSlop == null ? void 0 : hitSlop.bottom) != null ? _hitSlop$bottom : 0),
          left: -((_hitSlop$left = hitSlop == null ? void 0 : hitSlop.left) != null ? _hitSlop$left : 0),
          position: 'absolute',
          right: -((_hitSlop$right = hitSlop == null ? void 0 : hitSlop.right) != null ? _hitSlop$right : 0),
          top: -((_hitSlop$top = hitSlop == null ? void 0 : hitSlop.top) != null ? _hitSlop$top : 0)
        }
      });
    }
  }
  return null;
}
var isDebugEnabled = false;
function isEnabled() {
  if (__DEV__) {
    return isDebugEnabled;
  }
  return false;
}
function setEnabled(value) {
  if (__DEV__) {
    isDebugEnabled = value;
  }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfVmlldyIsIl9pbnRlcm9wUmVxdWlyZURlZmF1bHQiLCJyZXF1aXJlIiwiX25vcm1hbGl6ZUNvbG9yIiwiX1JlY3QiLCJSZWFjdCIsIl9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkIiwiX2pzeFJ1bnRpbWUiLCJlIiwidCIsIldlYWtNYXAiLCJyIiwibiIsIl9fZXNNb2R1bGUiLCJvIiwiaSIsImYiLCJfX3Byb3RvX18iLCJkZWZhdWx0IiwiaGFzIiwiZ2V0Iiwic2V0IiwiX3QiLCJoYXNPd25Qcm9wZXJ0eSIsImNhbGwiLCJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImdldE93blByb3BlcnR5RGVzY3JpcHRvciIsIlByZXNzYWJpbGl0eURlYnVnVmlldyIsInByb3BzIiwiX19ERVZfXyIsImlzRW5hYmxlZCIsIl9oaXRTbG9wJGJvdHRvbSIsIl9oaXRTbG9wJGxlZnQiLCJfaGl0U2xvcCRyaWdodCIsIl9oaXRTbG9wJHRvcCIsIm5vcm1hbGl6ZWRDb2xvciIsIm5vcm1hbGl6ZUNvbG9yIiwiY29sb3IiLCJiYXNlQ29sb3IiLCJ0b1N0cmluZyIsInBhZFN0YXJ0IiwiaGl0U2xvcCIsIm5vcm1hbGl6ZVJlY3QiLCJqc3giLCJwb2ludGVyRXZlbnRzIiwic3R5bGUiLCJiYWNrZ3JvdW5kQ29sb3IiLCJzbGljZSIsImJvcmRlckNvbG9yIiwiYm9yZGVyU3R5bGUiLCJib3JkZXJXaWR0aCIsImJvdHRvbSIsImxlZnQiLCJwb3NpdGlvbiIsInJpZ2h0IiwidG9wIiwiaXNEZWJ1Z0VuYWJsZWQiLCJzZXRFbmFibGVkIiwidmFsdWUiXSwic291cmNlcyI6WyJQcmVzc2FiaWxpdHlEZWJ1Zy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIENvcHlyaWdodCAoYykgTWV0YSBQbGF0Zm9ybXMsIEluYy4gYW5kIGFmZmlsaWF0ZXMuXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2UgZm91bmQgaW4gdGhlXG4gKiBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKlxuICogQGZsb3cgc3RyaWN0LWxvY2FsXG4gKiBAZm9ybWF0XG4gKi9cblxuaW1wb3J0IHR5cGUge0NvbG9yVmFsdWV9IGZyb20gJy4uL1N0eWxlU2hlZXQvU3R5bGVTaGVldCc7XG5cbmltcG9ydCBWaWV3IGZyb20gJy4uL0NvbXBvbmVudHMvVmlldy9WaWV3JztcbmltcG9ydCBub3JtYWxpemVDb2xvciBmcm9tICcuLi9TdHlsZVNoZWV0L25vcm1hbGl6ZUNvbG9yJztcbmltcG9ydCB7dHlwZSBSZWN0T3JTaXplLCBub3JtYWxpemVSZWN0fSBmcm9tICcuLi9TdHlsZVNoZWV0L1JlY3QnO1xuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuXG50eXBlIFByb3BzID0gJFJlYWRPbmx5PHtcbiAgY29sb3I6IENvbG9yVmFsdWUsXG4gIGhpdFNsb3A6ID9SZWN0T3JTaXplLFxufT47XG5cbi8qKlxuICogRGlzcGxheXMgYSBkZWJ1ZyBvdmVybGF5IHRvIHZpc3VhbGl6ZSBwcmVzcyB0YXJnZXRzIHdoZW4gZW5hYmxlZCB2aWEgdGhlXG4gKiBSZWFjdCBOYXRpdmUgSW5zcGVjdG9yLiBDYWxscyB0byB0aGlzIG1vZHVsZSBzaG91bGQgYmUgZ3VhcmRlZCBieSBgX19ERVZfX2AsXG4gKiBmb3IgZXhhbXBsZTpcbiAqXG4gKiAgIHJldHVybiAoXG4gKiAgICAgPFZpZXc+XG4gKiAgICAgICB7Y2hpbGRyZW59XG4gKiAgICAgICB7X19ERVZfXyA/IChcbiAqICAgICAgICAgPFByZXNzYWJpbGl0eURlYnVnVmlldyBjb2xvcj1cIi4uLlwiIGhpdFNsb3A9e3Byb3BzLmhpdFNsb3B9IC8+XG4gKiAgICAgICApIDogbnVsbH1cbiAqICAgICA8L1ZpZXc+XG4gKiAgICk7XG4gKlxuICovXG5leHBvcnQgZnVuY3Rpb24gUHJlc3NhYmlsaXR5RGVidWdWaWV3KHByb3BzOiBQcm9wcyk6IFJlYWN0Lk5vZGUge1xuICBpZiAoX19ERVZfXykge1xuICAgIGlmIChpc0VuYWJsZWQoKSkge1xuICAgICAgY29uc3Qgbm9ybWFsaXplZENvbG9yID0gbm9ybWFsaXplQ29sb3IocHJvcHMuY29sb3IpO1xuICAgICAgaWYgKHR5cGVvZiBub3JtYWxpemVkQ29sb3IgIT09ICdudW1iZXInKSB7XG4gICAgICAgIHJldHVybiBudWxsO1xuICAgICAgfVxuICAgICAgY29uc3QgYmFzZUNvbG9yID1cbiAgICAgICAgJyMnICsgKG5vcm1hbGl6ZWRDb2xvciA/PyAwKS50b1N0cmluZygxNikucGFkU3RhcnQoOCwgJzAnKTtcbiAgICAgIGNvbnN0IGhpdFNsb3AgPSBub3JtYWxpemVSZWN0KHByb3BzLmhpdFNsb3ApO1xuICAgICAgcmV0dXJuIChcbiAgICAgICAgPFZpZXdcbiAgICAgICAgICBwb2ludGVyRXZlbnRzPVwibm9uZVwiXG4gICAgICAgICAgc3R5bGU9e1xuICAgICAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHJlYWN0LW5hdGl2ZS9uby1pbmxpbmUtc3R5bGVzXG4gICAgICAgICAgICB7XG4gICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogYmFzZUNvbG9yLnNsaWNlKDAsIC0yKSArICcwRicsIC8vIDE1JVxuICAgICAgICAgICAgICBib3JkZXJDb2xvcjogYmFzZUNvbG9yLnNsaWNlKDAsIC0yKSArICc1NScsIC8vIDg1JVxuICAgICAgICAgICAgICBib3JkZXJTdHlsZTogJ2Rhc2hlZCcsXG4gICAgICAgICAgICAgIGJvcmRlcldpZHRoOiAxLFxuICAgICAgICAgICAgICBib3R0b206IC0oaGl0U2xvcD8uYm90dG9tID8/IDApLFxuICAgICAgICAgICAgICBsZWZ0OiAtKGhpdFNsb3A/LmxlZnQgPz8gMCksXG4gICAgICAgICAgICAgIHBvc2l0aW9uOiAnYWJzb2x1dGUnLFxuICAgICAgICAgICAgICByaWdodDogLShoaXRTbG9wPy5yaWdodCA/PyAwKSxcbiAgICAgICAgICAgICAgdG9wOiAtKGhpdFNsb3A/LnRvcCA/PyAwKSxcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIC8+XG4gICAgICApO1xuICAgIH1cbiAgfVxuICByZXR1cm4gbnVsbDtcbn1cblxubGV0IGlzRGVidWdFbmFibGVkID0gZmFsc2U7XG5cbmV4cG9ydCBmdW5jdGlvbiBpc0VuYWJsZWQoKTogYm9vbGVhbiB7XG4gIGlmIChfX0RFVl9fKSB7XG4gICAgcmV0dXJuIGlzRGVidWdFbmFibGVkO1xuICB9XG4gIHJldHVybiBmYWxzZTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIHNldEVuYWJsZWQodmFsdWU6IGJvb2xlYW4pOiB2b2lkIHtcbiAgaWYgKF9fREVWX18pIHtcbiAgICBpc0RlYnVnRW5hYmxlZCA9IHZhbHVlO1xuICB9XG59XG4iXSwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFZQSxJQUFBQSxLQUFBLEdBQUFDLHNCQUFBLENBQUFDLE9BQUE7QUFDQSxJQUFBQyxlQUFBLEdBQUFGLHNCQUFBLENBQUFDLE9BQUE7QUFDQSxJQUFBRSxLQUFBLEdBQUFGLE9BQUE7QUFDQSxJQUFBRyxLQUFBLEdBQUFDLHVCQUFBLENBQUFKLE9BQUE7QUFBK0IsSUFBQUssV0FBQSxHQUFBTCxPQUFBO0FBQUEsU0FBQUksd0JBQUFFLENBQUEsRUFBQUMsQ0FBQSw2QkFBQUMsT0FBQSxNQUFBQyxDQUFBLE9BQUFELE9BQUEsSUFBQUUsQ0FBQSxPQUFBRixPQUFBLFlBQUFKLHVCQUFBLFlBQUFBLHdCQUFBRSxDQUFBLEVBQUFDLENBQUEsU0FBQUEsQ0FBQSxJQUFBRCxDQUFBLElBQUFBLENBQUEsQ0FBQUssVUFBQSxTQUFBTCxDQUFBLE1BQUFNLENBQUEsRUFBQUMsQ0FBQSxFQUFBQyxDQUFBLEtBQUFDLFNBQUEsUUFBQUMsT0FBQSxFQUFBVixDQUFBLGlCQUFBQSxDQUFBLHVCQUFBQSxDQUFBLHlCQUFBQSxDQUFBLFNBQUFRLENBQUEsTUFBQUYsQ0FBQSxHQUFBTCxDQUFBLEdBQUFHLENBQUEsR0FBQUQsQ0FBQSxRQUFBRyxDQUFBLENBQUFLLEdBQUEsQ0FBQVgsQ0FBQSxVQUFBTSxDQUFBLENBQUFNLEdBQUEsQ0FBQVosQ0FBQSxHQUFBTSxDQUFBLENBQUFPLEdBQUEsQ0FBQWIsQ0FBQSxFQUFBUSxDQUFBLGNBQUFNLEVBQUEsSUFBQWQsQ0FBQSxnQkFBQWMsRUFBQSxPQUFBQyxjQUFBLENBQUFDLElBQUEsQ0FBQWhCLENBQUEsRUFBQWMsRUFBQSxPQUFBUCxDQUFBLElBQUFELENBQUEsR0FBQVcsTUFBQSxDQUFBQyxjQUFBLEtBQUFELE1BQUEsQ0FBQUUsd0JBQUEsQ0FBQW5CLENBQUEsRUFBQWMsRUFBQSxPQUFBUCxDQUFBLENBQUFLLEdBQUEsSUFBQUwsQ0FBQSxDQUFBTSxHQUFBLElBQUFQLENBQUEsQ0FBQUUsQ0FBQSxFQUFBTSxFQUFBLEVBQUFQLENBQUEsSUFBQUMsQ0FBQSxDQUFBTSxFQUFBLElBQUFkLENBQUEsQ0FBQWMsRUFBQSxXQUFBTixDQUFBLEtBQUFSLENBQUEsRUFBQUMsQ0FBQTtBQXNCeEIsU0FBU21CLHFCQUFxQkEsQ0FBQ0MsS0FBWSxFQUFjO0VBQzlELElBQUlDLE9BQU8sRUFBRTtJQUNYLElBQUlDLFNBQVMsQ0FBQyxDQUFDLEVBQUU7TUFBQSxJQUFBQyxlQUFBLEVBQUFDLGFBQUEsRUFBQUMsY0FBQSxFQUFBQyxZQUFBO01BQ2YsSUFBTUMsZUFBZSxHQUFHLElBQUFDLHVCQUFjLEVBQUNSLEtBQUssQ0FBQ1MsS0FBSyxDQUFDO01BQ25ELElBQUksT0FBT0YsZUFBZSxLQUFLLFFBQVEsRUFBRTtRQUN2QyxPQUFPLElBQUk7TUFDYjtNQUNBLElBQU1HLFNBQVMsR0FDYixHQUFHLEdBQUcsQ0FBQ0gsZUFBZSxXQUFmQSxlQUFlLEdBQUksQ0FBQyxFQUFFSSxRQUFRLENBQUMsRUFBRSxDQUFDLENBQUNDLFFBQVEsQ0FBQyxDQUFDLEVBQUUsR0FBRyxDQUFDO01BQzVELElBQU1DLE9BQU8sR0FBRyxJQUFBQyxtQkFBYSxFQUFDZCxLQUFLLENBQUNhLE9BQU8sQ0FBQztNQUM1QyxPQUNFLElBQUFuQyxXQUFBLENBQUFxQyxHQUFBLEVBQUM1QyxLQUFBLENBQUFrQixPQUFJO1FBQ0gyQixhQUFhLEVBQUMsTUFBTTtRQUNwQkMsS0FBSyxFQUVIO1VBQ0VDLGVBQWUsRUFBRVIsU0FBUyxDQUFDUyxLQUFLLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLEdBQUcsSUFBSTtVQUM5Q0MsV0FBVyxFQUFFVixTQUFTLENBQUNTLEtBQUssQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsR0FBRyxJQUFJO1VBQzFDRSxXQUFXLEVBQUUsUUFBUTtVQUNyQkMsV0FBVyxFQUFFLENBQUM7VUFDZEMsTUFBTSxFQUFFLEdBQUFwQixlQUFBLEdBQUVVLE9BQU8sb0JBQVBBLE9BQU8sQ0FBRVUsTUFBTSxZQUFBcEIsZUFBQSxHQUFJLENBQUMsQ0FBQztVQUMvQnFCLElBQUksRUFBRSxHQUFBcEIsYUFBQSxHQUFFUyxPQUFPLG9CQUFQQSxPQUFPLENBQUVXLElBQUksWUFBQXBCLGFBQUEsR0FBSSxDQUFDLENBQUM7VUFDM0JxQixRQUFRLEVBQUUsVUFBVTtVQUNwQkMsS0FBSyxFQUFFLEdBQUFyQixjQUFBLEdBQUVRLE9BQU8sb0JBQVBBLE9BQU8sQ0FBRWEsS0FBSyxZQUFBckIsY0FBQSxHQUFJLENBQUMsQ0FBQztVQUM3QnNCLEdBQUcsRUFBRSxHQUFBckIsWUFBQSxHQUFFTyxPQUFPLG9CQUFQQSxPQUFPLENBQUVjLEdBQUcsWUFBQXJCLFlBQUEsR0FBSSxDQUFDO1FBQzFCO01BQ0QsQ0FDRixDQUFDO0lBRU47RUFDRjtFQUNBLE9BQU8sSUFBSTtBQUNiO0FBRUEsSUFBSXNCLGNBQWMsR0FBRyxLQUFLO0FBRW5CLFNBQVMxQixTQUFTQSxDQUFBLEVBQVk7RUFDbkMsSUFBSUQsT0FBTyxFQUFFO0lBQ1gsT0FBTzJCLGNBQWM7RUFDdkI7RUFDQSxPQUFPLEtBQUs7QUFDZDtBQUVPLFNBQVNDLFVBQVVBLENBQUNDLEtBQWMsRUFBUTtFQUMvQyxJQUFJN0IsT0FBTyxFQUFFO0lBQ1gyQixjQUFjLEdBQUdFLEtBQUs7RUFDeEI7QUFDRiIsImlnbm9yZUxpc3QiOltdfQ==