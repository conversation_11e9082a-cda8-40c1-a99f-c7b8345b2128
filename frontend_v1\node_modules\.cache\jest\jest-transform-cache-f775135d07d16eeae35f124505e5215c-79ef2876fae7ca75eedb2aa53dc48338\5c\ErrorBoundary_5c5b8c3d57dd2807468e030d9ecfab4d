3170ac4015181b33cb64370dc8a83a3d
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useErrorHandler = exports.default = exports.ErrorBoundaryWrapper = exports.ErrorBoundary = void 0;
var _objectWithoutProperties2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _react = _interopRequireWildcard(require("react"));
var _reactNative = require("react-native");
var _Typography = require("../typography/Typography");
var _AnimatedButton = require("../animation/AnimatedButton");
var _HighContrastContext = require("../../contexts/HighContrastContext");
var _performanceMonitor = require("../../services/performanceMonitor");
var _errorHandlingUtils = require("../../utils/errorHandlingUtils");
var _jsxRuntime = require("react/jsx-runtime");
var _excluded = ["children"];
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
var ErrorDisplay = function ErrorDisplay(_ref) {
  var _colors$background, _colors$status, _colors$text, _colors$text2, _colors$text3, _colors$text4, _colors$text6;
  var error = _ref.error,
    onRetry = _ref.onRetry,
    enableRetry = _ref.enableRetry,
    retryCount = _ref.retryCount,
    maxRetries = _ref.maxRetries;
  var _useHighContrastColor = (0, _HighContrastContext.useHighContrastColors)(),
    colors = _useHighContrastColor.colors;
  var _formatErrorForDispla = (0, _errorHandlingUtils.formatErrorForDisplay)(error),
    title = _formatErrorForDispla.title,
    message = _formatErrorForDispla.message,
    suggestions = _formatErrorForDispla.suggestions,
    actions = _formatErrorForDispla.actions;
  var canRetry = enableRetry && retryCount < maxRetries && error.retryable;
  return (0, _jsxRuntime.jsx)(_reactNative.View, {
    style: [styles.container, {
      backgroundColor: (colors == null || (_colors$background = colors.background) == null ? void 0 : _colors$background.primary) || '#FFFFFF'
    }],
    children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.content,
      children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: [styles.iconContainer, {
          backgroundColor: (colors == null || (_colors$status = colors.status) == null ? void 0 : _colors$status.error) || '#FF6B6B'
        }],
        children: (0, _jsxRuntime.jsx)(_Typography.Body, {
          color: (colors == null || (_colors$text = colors.text) == null ? void 0 : _colors$text.inverse) || '#FFFFFF',
          children: "\u26A0\uFE0F"
        })
      }), (0, _jsxRuntime.jsx)(_Typography.Heading, {
        level: 2,
        color: colors == null || (_colors$text2 = colors.text) == null ? void 0 : _colors$text2.primary,
        align: "center",
        style: styles.title,
        children: title
      }), (0, _jsxRuntime.jsx)(_Typography.Body, {
        color: colors == null || (_colors$text3 = colors.text) == null ? void 0 : _colors$text3.secondary,
        align: "center",
        style: styles.message,
        children: message
      }), suggestions.length > 0 && (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.suggestionsContainer,
        children: [(0, _jsxRuntime.jsx)(_Typography.Body, {
          color: colors == null || (_colors$text4 = colors.text) == null ? void 0 : _colors$text4.primary,
          style: styles.suggestionsTitle,
          children: "Try these solutions:"
        }), suggestions.map(function (suggestion, index) {
          var _colors$text5;
          return (0, _jsxRuntime.jsxs)(_Typography.Body, {
            color: colors == null || (_colors$text5 = colors.text) == null ? void 0 : _colors$text5.secondary,
            style: styles.suggestion,
            children: ["\u2022 ", suggestion]
          }, index);
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.actionsContainer,
        children: [canRetry && (0, _jsxRuntime.jsx)(_AnimatedButton.AnimatedButton, {
          title: "Try Again",
          onPress: onRetry,
          variant: "primary",
          style: styles.actionButton,
          accessibilityLabel: "Try again to resolve the error",
          accessibilityHint: `Attempt ${retryCount + 1} of ${maxRetries}`
        }), actions.map(function (action, index) {
          return (0, _jsxRuntime.jsx)(_AnimatedButton.AnimatedButton, {
            title: action.label,
            onPress: action.action,
            variant: action.primary ? 'primary' : 'outline',
            style: styles.actionButton,
            accessibilityLabel: action.label
          }, index);
        })]
      }), (0, _jsxRuntime.jsxs)(_Typography.Body, {
        color: colors == null || (_colors$text6 = colors.text) == null ? void 0 : _colors$text6.tertiary,
        align: "center",
        style: styles.errorId,
        children: ["Error ID: ", error.id]
      })]
    })
  });
};
var ErrorBoundary = exports.ErrorBoundary = function (_Component) {
  function ErrorBoundary(props) {
    var _this;
    (0, _classCallCheck2.default)(this, ErrorBoundary);
    _this = _callSuper(this, ErrorBoundary, [props]);
    _this.resetTimeoutId = null;
    _this.resetErrorBoundary = function () {
      if (_this.resetTimeoutId) {
        clearTimeout(_this.resetTimeoutId);
      }
      _this.setState((0, _errorHandlingUtils.createErrorBoundaryState)());
    };
    _this.handleRetry = function () {
      var _this$props$maxRetrie = _this.props.maxRetries,
        maxRetries = _this$props$maxRetrie === void 0 ? 3 : _this$props$maxRetrie;
      var retryCount = _this.state.retryCount;
      if (retryCount < maxRetries) {
        _this.setState(function (prevState) {
          return Object.assign({}, prevState, {
            retryCount: prevState.retryCount + 1
          });
        });
        _this.resetTimeoutId = window.setTimeout(function () {
          _this.resetErrorBoundary();
        }, 100);
      }
    };
    _this.state = (0, _errorHandlingUtils.createErrorBoundaryState)();
    return _this;
  }
  (0, _inherits2.default)(ErrorBoundary, _Component);
  return (0, _createClass2.default)(ErrorBoundary, [{
    key: "componentDidCatch",
    value: function componentDidCatch(error, errorInfo) {
      var appError = (0, _errorHandlingUtils.createAppError)(error, {
        component: 'ErrorBoundary',
        action: 'componentDidCatch',
        additionalData: {
          componentStack: errorInfo.componentStack,
          errorBoundary: true
        }
      });
      (0, _errorHandlingUtils.logError)(appError);
      _performanceMonitor.performanceMonitor.trackUserInteraction('error_boundary_catch', 0, {
        error: error.message,
        errorType: error.name,
        componentStack: errorInfo.componentStack,
        errorId: appError.id,
        retryCount: this.state.retryCount
      });
      if (this.props.onError) {
        this.props.onError(appError);
      }
      if (_reactNative.Platform.OS === 'ios' || _reactNative.Platform.OS === 'android') {
        _reactNative.AccessibilityInfo.announceForAccessibility('An error occurred. Please check the error message and try again.');
      }
    }
  }, {
    key: "componentDidUpdate",
    value: function componentDidUpdate(prevProps) {
      var _this$props = this.props,
        resetOnPropsChange = _this$props.resetOnPropsChange,
        resetKeys = _this$props.resetKeys;
      var hasError = this.state.hasError;
      if (hasError && resetOnPropsChange && resetKeys) {
        var hasResetKeyChanged = resetKeys.some(function (key, index) {
          var _prevProps$resetKeys;
          return key !== ((_prevProps$resetKeys = prevProps.resetKeys) == null ? void 0 : _prevProps$resetKeys[index]);
        });
        if (hasResetKeyChanged) {
          this.resetErrorBoundary();
        }
      }
    }
  }, {
    key: "componentWillUnmount",
    value: function componentWillUnmount() {
      if (this.resetTimeoutId) {
        clearTimeout(this.resetTimeoutId);
      }
    }
  }, {
    key: "render",
    value: function render() {
      var _this$props2 = this.props,
        children = _this$props2.children,
        fallback = _this$props2.fallback,
        _this$props2$enableRe = _this$props2.enableRetry,
        enableRetry = _this$props2$enableRe === void 0 ? true : _this$props2$enableRe,
        _this$props2$maxRetri = _this$props2.maxRetries,
        maxRetries = _this$props2$maxRetri === void 0 ? 3 : _this$props2$maxRetri;
      var _this$state = this.state,
        hasError = _this$state.hasError,
        error = _this$state.error,
        retryCount = _this$state.retryCount;
      if (hasError && error) {
        if (fallback) {
          return fallback(error, this.handleRetry);
        }
        return (0, _jsxRuntime.jsx)(ErrorDisplay, {
          error: error,
          onRetry: this.handleRetry,
          enableRetry: enableRetry,
          retryCount: retryCount,
          maxRetries: maxRetries
        });
      }
      return children;
    }
  }], [{
    key: "getDerivedStateFromError",
    value: function getDerivedStateFromError(error) {
      var appError = (0, _errorHandlingUtils.createAppError)(error, {
        component: 'ErrorBoundary',
        action: 'render'
      });
      return {
        hasError: true,
        error: appError,
        errorId: appError.id
      };
    }
  }]);
}(_react.Component);
var ErrorBoundaryWrapper = exports.ErrorBoundaryWrapper = function ErrorBoundaryWrapper(_ref2) {
  var children = _ref2.children,
    props = (0, _objectWithoutProperties2.default)(_ref2, _excluded);
  return (0, _jsxRuntime.jsx)(ErrorBoundary, Object.assign({}, props, {
    children: children
  }));
};
var useErrorHandler = exports.useErrorHandler = function useErrorHandler() {
  var throwError = function throwError(error) {
    var errorObj = typeof error === 'string' ? new Error(error) : error;
    throw errorObj;
  };
  return {
    throwError: throwError
  };
};
var styles = _reactNative.StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20
  },
  content: {
    maxWidth: 400,
    width: '100%',
    alignItems: 'center'
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24
  },
  title: {
    marginBottom: 16
  },
  message: {
    marginBottom: 24,
    lineHeight: 24
  },
  suggestionsContainer: {
    width: '100%',
    marginBottom: 32,
    padding: 16,
    backgroundColor: '#F8F9FA',
    borderRadius: 8
  },
  suggestionsTitle: {
    marginBottom: 12,
    fontWeight: '600'
  },
  suggestion: {
    marginBottom: 8,
    paddingLeft: 8
  },
  actionsContainer: {
    width: '100%',
    gap: 12
  },
  actionButton: {
    width: '100%'
  },
  errorId: {
    marginTop: 24,
    fontSize: 12,
    fontFamily: 'monospace'
  }
});
var _default = exports.default = ErrorBoundary;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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