7331a9bac0e8de6ea87c389d7ee11ac0
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _react = _interopRequireWildcard(require("react"));
var _reactNative = require("react-native");
var _vectorIcons = require("@expo/vector-icons");
var _FocusableButton = require("../components/accessibility/FocusableButton");
var _Typography = require("../components/typography/Typography");
var _ErrorBoundary = require("../components/error/ErrorBoundary");
var _DataLoadingFallback = require("../components/error/DataLoadingFallback");
var _useCustomerHomeData2 = require("../hooks/useCustomerHomeData");
var _authSlice = require("../store/authSlice");
var _ThemeContext = require("../contexts/ThemeContext");
var _useNavigationGuard2 = require("../hooks/useNavigationGuard");
var _usePerformance2 = require("../hooks/usePerformance");
var _useErrorHandling2 = require("../hooks/useErrorHandling");
var _accessibilityUtils = require("../utils/accessibilityUtils");
var _jsxRuntime = require("react/jsx-runtime");
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var CustomerHomeScreen = function CustomerHomeScreen() {
  var _data$dashboard;
  var _useTheme = (0, _ThemeContext.useTheme)(),
    colors = _useTheme.colors;
  var _useAuthStore = (0, _authSlice.useAuthStore)(),
    isAuthenticated = _useAuthStore.isAuthenticated,
    user = _useAuthStore.user;
  var _useNavigationGuard = (0, _useNavigationGuard2.useNavigationGuard)(),
    navigate = _useNavigationGuard.navigate;
  var _usePerformance = (0, _usePerformance2.usePerformance)({
      componentName: 'CustomerHomeScreen',
      trackRenders: true,
      trackInteractions: true
    }),
    trackInteraction = _usePerformance.trackInteraction,
    trackAsyncOperation = _usePerformance.trackAsyncOperation;
  (0, _usePerformance2.useLifecyclePerformance)('CustomerHomeScreen');
  var _useErrorHandling = (0, _useErrorHandling2.useErrorHandling)({
      maxRetries: 3,
      errorContext: {
        component: 'CustomerHomeScreen'
      }
    }),
    globalError = _useErrorHandling.error,
    hasGlobalError = _useErrorHandling.isError,
    handleError = _useErrorHandling.handleError,
    clearError = _useErrorHandling.clearError,
    retryGlobalError = _useErrorHandling.retry;
  var _useCustomerHomeData = (0, _useCustomerHomeData2.useCustomerHomeData)(),
    data = _useCustomerHomeData.data,
    loading = _useCustomerHomeData.loading,
    error = _useCustomerHomeData.error,
    refreshing = _useCustomerHomeData.refreshing,
    refresh = _useCustomerHomeData.refresh;
  var styles = (0, _react.useMemo)(function () {
    return createStyles(colors);
  }, [colors]);
  var getGreeting = (0, _react.useCallback)(function () {
    var hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 17) return 'Good afternoon';
    return 'Good evening';
  }, []);
  var greeting = ((_data$dashboard = data.dashboard) == null ? void 0 : _data$dashboard.greeting) || getGreeting();
  var handleCategoryPress = (0, _react.useCallback)(function () {
    var _ref = (0, _asyncToGenerator2.default)(function* (category) {
      yield trackInteraction('category_press', (0, _asyncToGenerator2.default)(function* () {
        console.log('Category pressed:', category.slug);
        yield navigate('Search', {
          category: category.slug
        });
      }));
    });
    return function (_x) {
      return _ref.apply(this, arguments);
    };
  }(), [navigate, trackInteraction]);
  var handleProviderPress = (0, _react.useCallback)(function () {
    var _ref3 = (0, _asyncToGenerator2.default)(function* (provider) {
      yield trackInteraction('provider_press', (0, _asyncToGenerator2.default)(function* () {
        console.log('Provider pressed:', provider.id);
        yield navigate('ProviderDetails', {
          providerId: provider.id
        });
      }));
    });
    return function (_x2) {
      return _ref3.apply(this, arguments);
    };
  }(), [navigate, trackInteraction]);
  var handleSeeAllPress = (0, _react.useCallback)(function () {
    var _ref5 = (0, _asyncToGenerator2.default)(function* (section) {
      yield trackInteraction('see_all_press', (0, _asyncToGenerator2.default)(function* () {
        console.log('See all pressed for:', section);
        switch (section) {
          case 'featured':
            yield navigate('Search', {
              filter: 'featured'
            });
            break;
          case 'favorites':
            yield navigate('Search', {
              filter: 'favorites'
            });
            break;
          case 'nearby':
            yield navigate('Search', {
              filter: 'nearby'
            });
            break;
          case 'quick-book':
            yield navigate('Bookings', {
              tab: 'quick-book'
            });
            break;
          default:
            yield navigate('Search');
        }
      }));
    });
    return function (_x3) {
      return _ref5.apply(this, arguments);
    };
  }(), [navigate, trackInteraction]);
  (0, _react.useEffect)(function () {
    var announceScreenContent = function () {
      var _ref7 = (0, _asyncToGenerator2.default)(function* () {
        var isScreenReaderEnabled = yield _accessibilityUtils.ScreenReaderUtils.isScreenReaderEnabled();
        if (isScreenReaderEnabled && data.categories.length > 0) {
          setTimeout(function () {
            _accessibilityUtils.ScreenReaderUtils.announceForAccessibility(`Customer Home Screen loaded. ${greeting} ${(user == null ? void 0 : user.firstName) || 'User'}. Browse ${data.categories.length} service categories including ${data.categories.map(function (c) {
              return c.name;
            }).join(', ')}.`);
          }, 1000);
        }
      });
      return function announceScreenContent() {
        return _ref7.apply(this, arguments);
      };
    }();
    announceScreenContent();
  }, [data.categories, greeting, user]);
  var renderBrowseServicesSection = (0, _react.useCallback)(function () {
    if (loading.categories && data.categories.length === 0) {
      return (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.section,
        children: [(0, _jsxRuntime.jsx)(_Typography.Heading, {
          level: 2,
          color: colors.text.primary,
          children: "Browse Services"
        }), (0, _jsxRuntime.jsx)(_reactNative.ActivityIndicator, {
          size: "large",
          color: colors.sage400,
          style: {
            marginTop: 20
          }
        })]
      });
    }
    if (error.categories && data.categories.length === 0) {
      return (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.section,
        children: [(0, _jsxRuntime.jsx)(_Typography.Heading, {
          level: 2,
          color: colors.text.primary,
          children: "Browse Services"
        }), (0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: styles.errorText,
          children: "Failed to load categories. Pull to refresh."
        })]
      });
    }
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.section,
      accessibilityRole: "none",
      accessibilityLabel: "Browse Services section",
      children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.sectionHeader,
        children: (0, _jsxRuntime.jsx)(_Typography.Heading, {
          level: 2,
          color: colors.text.primary,
          children: "Browse Services"
        })
      }), (0, _jsxRuntime.jsx)(_reactNative.ScrollView, {
        horizontal: true,
        showsHorizontalScrollIndicator: false,
        contentContainerStyle: styles.categoriesScroll,
        children: data.categories.map(function (category) {
          return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
            style: [styles.categoryCard, {
              backgroundColor: category.color
            }],
            onPress: function onPress() {
              return handleCategoryPress(category);
            },
            accessibilityRole: "button",
            accessibilityLabel: `${category.name} category, ${category.serviceCount} services`,
            children: [(0, _jsxRuntime.jsx)(_vectorIcons.Ionicons, {
              name: category.icon,
              size: 24,
              color: "#FFFFFF"
            }), (0, _jsxRuntime.jsx)(_reactNative.Text, {
              style: styles.categoryName,
              children: category.name
            }), (0, _jsxRuntime.jsxs)(_reactNative.Text, {
              style: styles.categoryCount,
              children: [category.serviceCount, " services"]
            })]
          }, category.id);
        })
      })]
    });
  }, [data.categories, loading.categories, error.categories, colors, handleCategoryPress]);
  var renderFeaturedProvidersSection = (0, _react.useCallback)(function () {
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.section,
      accessibilityRole: "none",
      accessibilityLabel: "Featured Providers section",
      children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.sectionHeader,
        children: [(0, _jsxRuntime.jsx)(_Typography.Heading, {
          level: 2,
          color: colors.text.primary,
          children: "Featured Providers"
        }), (0, _jsxRuntime.jsx)(_FocusableButton.FocusableButton, {
          title: "See All",
          onPress: function onPress() {
            return handleSeeAllPress('featured');
          },
          variant: "ghost",
          size: "small",
          accessibilityLabel: "See all featured providers"
        })]
      }), loading.featuredProviders ? (0, _jsxRuntime.jsx)(_reactNative.ActivityIndicator, {
        size: "large",
        color: colors.sage400,
        style: {
          marginTop: 20
        }
      }) : error.featuredProviders ? (0, _jsxRuntime.jsx)(_reactNative.Text, {
        style: styles.errorText,
        children: "Failed to load featured providers"
      }) : data.featuredProviders.length > 0 ? (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
        horizontal: true,
        data: data.featuredProviders,
        keyExtractor: function keyExtractor(item) {
          return item.id;
        },
        renderItem: function renderItem(_ref8) {
          var item = _ref8.item;
          return renderProviderCard(item);
        },
        showsHorizontalScrollIndicator: false,
        contentContainerStyle: styles.categoriesScroll
      }) : (0, _jsxRuntime.jsx)(_reactNative.Text, {
        style: styles.placeholderText,
        children: "No featured providers available"
      })]
    });
  }, [data.featuredProviders, loading.featuredProviders, error.featuredProviders, colors, handleSeeAllPress]);
  var renderFavoriteProvidersSection = (0, _react.useCallback)(function () {
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.section,
      accessibilityRole: "none",
      accessibilityLabel: "Favorite Providers section",
      children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.sectionHeader,
        children: [(0, _jsxRuntime.jsx)(_Typography.Heading, {
          level: 2,
          color: colors.text.primary,
          children: "Favorite Providers"
        }), (0, _jsxRuntime.jsx)(_FocusableButton.FocusableButton, {
          title: "See All",
          onPress: function onPress() {
            return handleSeeAllPress('favorites');
          },
          variant: "ghost",
          size: "small",
          accessibilityLabel: "See all favorite providers"
        })]
      }), loading.favoriteProviders ? (0, _jsxRuntime.jsx)(_reactNative.ActivityIndicator, {
        size: "large",
        color: colors.sage400,
        style: {
          marginTop: 20
        }
      }) : error.favoriteProviders ? (0, _jsxRuntime.jsx)(_reactNative.Text, {
        style: styles.placeholderText,
        children: "Failed to load favorite providers"
      }) : data.favoriteProviders.length > 0 ? (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
        horizontal: true,
        data: data.favoriteProviders,
        keyExtractor: function keyExtractor(item) {
          return item.id;
        },
        renderItem: function renderItem(_ref9) {
          var item = _ref9.item;
          return renderProviderCard(item);
        },
        showsHorizontalScrollIndicator: false,
        contentContainerStyle: styles.categoriesScroll
      }) : (0, _jsxRuntime.jsx)(_reactNative.Text, {
        style: styles.placeholderText,
        children: "No favorite providers yet"
      })]
    });
  }, [data.favoriteProviders, loading.favoriteProviders, error.favoriteProviders, colors, handleSeeAllPress]);
  var renderNearbyProvidersSection = (0, _react.useCallback)(function () {
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.section,
      accessibilityRole: "none",
      accessibilityLabel: "Nearby Providers section",
      children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.sectionHeader,
        children: [(0, _jsxRuntime.jsx)(_Typography.Heading, {
          level: 2,
          color: colors.text.primary,
          children: "Nearby Providers"
        }), (0, _jsxRuntime.jsx)(_FocusableButton.FocusableButton, {
          title: "See All",
          onPress: function onPress() {
            return handleSeeAllPress('nearby');
          },
          variant: "ghost",
          size: "small",
          accessibilityLabel: "See all nearby providers"
        })]
      }), loading.nearbyProviders ? (0, _jsxRuntime.jsx)(_reactNative.ActivityIndicator, {
        size: "large",
        color: colors.sage400,
        style: {
          marginTop: 20
        }
      }) : error.nearbyProviders ? (0, _jsxRuntime.jsx)(_reactNative.Text, {
        style: styles.placeholderText,
        children: "Failed to load nearby providers"
      }) : data.nearbyProviders.length > 0 ? (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
        horizontal: true,
        data: data.nearbyProviders,
        keyExtractor: function keyExtractor(item) {
          return item.id;
        },
        renderItem: function renderItem(_ref0) {
          var item = _ref0.item;
          return renderProviderCard(item);
        },
        showsHorizontalScrollIndicator: false,
        contentContainerStyle: styles.categoriesScroll
      }) : (0, _jsxRuntime.jsx)(_reactNative.Text, {
        style: styles.placeholderText,
        children: "No nearby providers found"
      })]
    });
  }, [data.nearbyProviders, loading.nearbyProviders, error.nearbyProviders, colors, handleSeeAllPress]);
  var renderProviderCard = (0, _react.useCallback)(function (provider) {
    return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
      style: styles.providerCard,
      onPress: function onPress() {
        return handleProviderPress(provider);
      },
      accessibilityRole: "button",
      accessibilityLabel: `${provider.name}, ${provider.rating} stars, ${provider.reviewCount} reviews`,
      children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.providerImageContainer,
        children: provider.avatar ? (0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: styles.providerInitials,
          children: provider.name.split(' ').map(function (n) {
            return n[0];
          }).join('').toUpperCase()
        }) : (0, _jsxRuntime.jsx)(_vectorIcons.Ionicons, {
          name: "person-outline",
          size: 24,
          color: colors.text.secondary
        })
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.providerInfo,
        children: [(0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: styles.providerName,
          numberOfLines: 1,
          children: provider.name
        }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.providerRating,
          children: [(0, _jsxRuntime.jsx)(_vectorIcons.Ionicons, {
            name: "star",
            size: 12,
            color: "#FFD700"
          }), (0, _jsxRuntime.jsx)(_reactNative.Text, {
            style: styles.ratingText,
            children: provider.rating.toFixed(1)
          }), (0, _jsxRuntime.jsxs)(_reactNative.Text, {
            style: styles.reviewCount,
            children: ["(", provider.reviewCount, ")"]
          })]
        }), (0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: styles.providerLocation,
          numberOfLines: 1,
          children: provider.location.city
        })]
      })]
    }, provider.id);
  }, [colors, handleProviderPress]);
  var renderRecentBookingsSection = (0, _react.useCallback)(function () {
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.section,
      accessibilityRole: "none",
      accessibilityLabel: "Recent Bookings and Quick Booking section",
      children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.sectionHeader,
        children: [(0, _jsxRuntime.jsx)(_Typography.Heading, {
          level: 2,
          color: colors.text.primary,
          children: "Recent Bookings"
        }), (0, _jsxRuntime.jsx)(_FocusableButton.FocusableButton, {
          title: "Quick Book",
          onPress: function onPress() {
            return handleSeeAllPress('quick-book');
          },
          variant: "primary",
          size: "small",
          accessibilityLabel: "Quick booking"
        })]
      }), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.section,
        children: (0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: styles.placeholderText,
          children: "Your recent bookings and quick booking options will appear here"
        })
      })]
    });
  }, [colors, handleSeeAllPress]);
  console.log('🏠 CustomerHomeScreen rendering... v2');
  console.log('🎨 Colors:', colors);
  console.log('📱 Categories:', data.categories.length);
  console.log('🔄 Loading:', loading.overall);
  if (hasGlobalError) {
    return (0, _jsxRuntime.jsx)(_reactNative.SafeAreaView, {
      style: [styles.container, {
        backgroundColor: colors.background.primary
      }],
      children: (0, _jsxRuntime.jsx)(_DataLoadingFallback.DataLoadingFallback, {
        isLoading: false,
        isError: true,
        error: globalError,
        onRetry: retryGlobalError,
        testID: "customer-home-global-error"
      })
    });
  }
  return (0, _jsxRuntime.jsx)(_ErrorBoundary.ErrorBoundary, {
    onError: function onError(error) {
      return handleError(error);
    },
    maxRetries: 3,
    enableRetry: true,
    children: (0, _jsxRuntime.jsx)(_reactNative.SafeAreaView, {
      style: [styles.container, {
        backgroundColor: colors.background.primary
      }],
      accessibilityRole: "none",
      accessibilityLabel: "Customer Home Screen",
      children: (0, _jsxRuntime.jsxs)(_reactNative.ScrollView, {
        style: styles.scrollView,
        contentContainerStyle: styles.scrollContent,
        refreshControl: (0, _jsxRuntime.jsx)(_reactNative.RefreshControl, {
          refreshing: refreshing,
          onRefresh: refresh,
          colors: [colors.sage400 || '#5A7A63'],
          tintColor: colors.sage400 || '#5A7A63',
          accessibilityLabel: refreshing ? "Refreshing content" : "Pull to refresh"
        }),
        showsVerticalScrollIndicator: false,
        accessibilityRole: "scrollbar",
        accessibilityLabel: "Main content area",
        accessibilityHint: "Scroll to browse services and providers",
        children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.welcomeSection,
          accessibilityRole: "none",
          accessibilityLabel: "Welcome section",
          children: [(0, _jsxRuntime.jsx)(_reactNative.Text, {
            style: styles.greeting,
            children: greeting
          }), (0, _jsxRuntime.jsxs)(_reactNative.Text, {
            style: styles.userName,
            children: ["Welcome", isAuthenticated && user != null && user.firstName ? `, ${user.firstName}` : ' to Vierla']
          })]
        }), renderBrowseServicesSection(), renderFeaturedProvidersSection(), isAuthenticated && renderFavoriteProvidersSection(), renderNearbyProvidersSection(), isAuthenticated && renderRecentBookingsSection()]
      })
    })
  });
};
var createStyles = function createStyles(colors) {
  var _colors$background, _colors$background2, _colors$text, _colors$text2, _colors$text3, _colors$primary, _colors$text4, _colors$background3, _colors$text5, _colors$text6, _colors$text7, _colors$text8;
  return _reactNative.StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: (colors == null || (_colors$background = colors.background) == null ? void 0 : _colors$background.primary) || '#FFFFFF'
    },
    scrollView: {
      flex: 1
    },
    scrollContent: {
      paddingBottom: 100,
      paddingHorizontal: 16
    },
    welcomeSection: {
      paddingHorizontal: 16,
      paddingVertical: 20,
      backgroundColor: (colors == null || (_colors$background2 = colors.background) == null ? void 0 : _colors$background2.secondary) || '#F8F9FA',
      borderRadius: 12,
      marginBottom: 24
    },
    greeting: {
      fontSize: 16,
      color: (colors == null || (_colors$text = colors.text) == null ? void 0 : _colors$text.secondary) || '#666',
      fontFamily: 'Inter-Regular',
      textAlign: 'center'
    },
    userName: {
      fontSize: 24,
      fontWeight: 'bold',
      color: (colors == null || (_colors$text2 = colors.text) == null ? void 0 : _colors$text2.primary) || '#333',
      fontFamily: 'Inter-Bold',
      marginTop: 4,
      textAlign: 'center'
    },
    section: {
      marginBottom: 24
    },
    sectionHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 16,
      paddingHorizontal: 0
    },
    sectionTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      color: (colors == null || (_colors$text3 = colors.text) == null ? void 0 : _colors$text3.primary) || '#333',
      fontFamily: 'Inter-Bold'
    },
    seeAllText: {
      fontSize: 14,
      color: (colors == null || (_colors$primary = colors.primary) == null ? void 0 : _colors$primary.default) || '#5A7A63',
      fontFamily: 'Inter-Medium',
      fontWeight: '500'
    },
    categoriesScroll: {
      paddingLeft: 0,
      paddingRight: 16
    },
    categoryCard: {
      width: 120,
      height: 100,
      borderRadius: 12,
      padding: 12,
      marginRight: 12,
      justifyContent: 'center',
      alignItems: 'center'
    },
    categoryName: {
      color: '#FFFFFF',
      fontSize: 14,
      fontWeight: 'bold',
      marginTop: 8,
      textAlign: 'center'
    },
    categoryCount: {
      color: '#FFFFFF',
      fontSize: 12,
      marginTop: 4,
      textAlign: 'center',
      opacity: 0.9
    },
    placeholderText: {
      fontSize: 16,
      color: (colors == null || (_colors$text4 = colors.text) == null ? void 0 : _colors$text4.tertiary) || '#999',
      textAlign: 'center',
      paddingHorizontal: 16,
      paddingVertical: 20
    },
    errorText: {
      fontSize: 14,
      color: (colors == null ? void 0 : colors.error) || '#FF6B6B',
      textAlign: 'center',
      paddingHorizontal: 16,
      paddingVertical: 20
    },
    providerCard: {
      width: 160,
      backgroundColor: (colors == null || (_colors$background3 = colors.background) == null ? void 0 : _colors$background3.secondary) || '#F8F9FA',
      borderRadius: 12,
      padding: 12,
      marginRight: 12,
      borderWidth: 1,
      borderColor: (colors == null ? void 0 : colors.border) || '#E5E5E5'
    },
    providerImageContainer: {
      width: 48,
      height: 48,
      borderRadius: 24,
      backgroundColor: (colors == null ? void 0 : colors.sage100) || '#E8F5E8',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 8
    },
    providerInitials: {
      fontSize: 16,
      fontWeight: 'bold',
      color: (colors == null ? void 0 : colors.sage600) || '#5A7A63'
    },
    providerInfo: {
      flex: 1
    },
    providerName: {
      fontSize: 14,
      fontWeight: 'bold',
      color: (colors == null || (_colors$text5 = colors.text) == null ? void 0 : _colors$text5.primary) || '#333',
      marginBottom: 4
    },
    providerRating: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 4
    },
    ratingText: {
      fontSize: 12,
      color: (colors == null || (_colors$text6 = colors.text) == null ? void 0 : _colors$text6.primary) || '#333',
      marginLeft: 4,
      fontWeight: '500'
    },
    reviewCount: {
      fontSize: 12,
      color: (colors == null || (_colors$text7 = colors.text) == null ? void 0 : _colors$text7.secondary) || '#666',
      marginLeft: 2
    },
    providerLocation: {
      fontSize: 12,
      color: (colors == null || (_colors$text8 = colors.text) == null ? void 0 : _colors$text8.secondary) || '#666'
    },
    seeAllButton: {
      backgroundColor: 'transparent',
      minHeight: 32,
      paddingHorizontal: 8
    }
  });
};
var _default = exports.default = CustomerHomeScreen;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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