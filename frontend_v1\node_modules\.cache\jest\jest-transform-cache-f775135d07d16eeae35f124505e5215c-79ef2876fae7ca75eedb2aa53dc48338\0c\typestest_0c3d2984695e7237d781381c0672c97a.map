{"version": 3, "names": ["describe", "it", "mockParams", "<PERSON><PERSON>", "undefined", "CustomerTabs", "ProviderTabs", "expect", "toBeDefined", "toBeUndefined", "<PERSON><PERSON>", "Register", "Home", "Search", "Bookings", "Messages", "Profile", "Dashboard", "Services", "rootKeys", "auth<PERSON><PERSON><PERSON>", "customerKeys", "providerKeys", "toHave<PERSON>ength"], "sources": ["types.test.ts"], "sourcesContent": ["/**\n * Navigation Types Tests - TDD Implementation\n * Following Red-Green-Refactor methodology\n */\n\nimport type {\n  RootStackParamList,\n  AuthStackParamList,\n  CustomerTabParamList,\n  ProviderTabParamList,\n} from '../types';\n\ndescribe('Navigation Types', () => {\n  describe('Type Definitions', () => {\n    it('should define RootStackParamList correctly', () => {\n      // This test ensures the type exists and has the expected structure\n      const mockParams: RootStackParamList = {\n        Auth: undefined,\n        CustomerTabs: undefined,\n        ProviderTabs: undefined,\n      };\n\n      expect(mockParams).toBeDefined();\n      expect(mockParams.Auth).toBeUndefined();\n      expect(mockParams.CustomerTabs).toBeUndefined();\n      expect(mockParams.ProviderTabs).toBeUndefined();\n    });\n\n    it('should define AuthStackParamList correctly', () => {\n      const mockParams: AuthStackParamList = {\n        Login: undefined,\n        Register: undefined,\n      };\n\n      expect(mockParams).toBeDefined();\n      expect(mockParams.Login).toBeUndefined();\n      expect(mockParams.Register).toBeUndefined();\n    });\n\n    it('should define CustomerTabParamList correctly', () => {\n      const mockParams: CustomerTabParamList = {\n        Home: undefined,\n        Search: undefined,\n        Bookings: undefined,\n        Messages: undefined,\n        Profile: undefined,\n      };\n\n      expect(mockParams).toBeDefined();\n      expect(mockParams.Home).toBeUndefined();\n      expect(mockParams.Search).toBeUndefined();\n      expect(mockParams.Bookings).toBeUndefined();\n      expect(mockParams.Messages).toBeUndefined();\n      expect(mockParams.Profile).toBeUndefined();\n    });\n\n    it('should define ProviderTabParamList correctly', () => {\n      const mockParams: ProviderTabParamList = {\n        Dashboard: undefined,\n        Services: undefined,\n        Bookings: undefined,\n        Messages: undefined,\n        Profile: undefined,\n      };\n\n      expect(mockParams).toBeDefined();\n      expect(mockParams.Dashboard).toBeUndefined();\n      expect(mockParams.Services).toBeUndefined();\n      expect(mockParams.Bookings).toBeUndefined();\n      expect(mockParams.Messages).toBeUndefined();\n      expect(mockParams.Profile).toBeUndefined();\n    });\n  });\n\n  describe('Type Safety', () => {\n    it('should enforce correct parameter types', () => {\n      // This test ensures TypeScript compilation will catch type errors\n      // The actual test is at compile time, but we can verify the types exist\n\n      type RootKeys = keyof RootStackParamList;\n      type AuthKeys = keyof AuthStackParamList;\n      type CustomerKeys = keyof CustomerTabParamList;\n      type ProviderKeys = keyof ProviderTabParamList;\n\n      const rootKeys: RootKeys[] = ['Auth', 'CustomerTabs', 'ProviderTabs'];\n      const authKeys: AuthKeys[] = ['Login', 'Register'];\n      const customerKeys: CustomerKeys[] = [\n        'Home',\n        'Search',\n        'Bookings',\n        'Messages',\n        'Profile',\n      ];\n      const providerKeys: ProviderKeys[] = [\n        'Dashboard',\n        'Services',\n        'Bookings',\n        'Messages',\n        'Profile',\n      ];\n\n      expect(rootKeys).toHaveLength(3);\n      expect(authKeys).toHaveLength(2);\n      expect(customerKeys).toHaveLength(5);\n      expect(providerKeys).toHaveLength(5);\n    });\n  });\n});\n"], "mappings": ";;;AAYAA,QAAQ,CAAC,kBAAkB,EAAE,YAAM;EACjCA,QAAQ,CAAC,kBAAkB,EAAE,YAAM;IACjCC,EAAE,CAAC,4CAA4C,EAAE,YAAM;MAErD,IAAMC,UAA8B,GAAG;QACrCC,IAAI,EAAEC,SAAS;QACfC,YAAY,EAAED,SAAS;QACvBE,YAAY,EAAEF;MAChB,CAAC;MAEDG,MAAM,CAACL,UAAU,CAAC,CAACM,WAAW,CAAC,CAAC;MAChCD,MAAM,CAACL,UAAU,CAACC,IAAI,CAAC,CAACM,aAAa,CAAC,CAAC;MACvCF,MAAM,CAACL,UAAU,CAACG,YAAY,CAAC,CAACI,aAAa,CAAC,CAAC;MAC/CF,MAAM,CAACL,UAAU,CAACI,YAAY,CAAC,CAACG,aAAa,CAAC,CAAC;IACjD,CAAC,CAAC;IAEFR,EAAE,CAAC,4CAA4C,EAAE,YAAM;MACrD,IAAMC,UAA8B,GAAG;QACrCQ,KAAK,EAAEN,SAAS;QAChBO,QAAQ,EAAEP;MACZ,CAAC;MAEDG,MAAM,CAACL,UAAU,CAAC,CAACM,WAAW,CAAC,CAAC;MAChCD,MAAM,CAACL,UAAU,CAACQ,KAAK,CAAC,CAACD,aAAa,CAAC,CAAC;MACxCF,MAAM,CAACL,UAAU,CAACS,QAAQ,CAAC,CAACF,aAAa,CAAC,CAAC;IAC7C,CAAC,CAAC;IAEFR,EAAE,CAAC,8CAA8C,EAAE,YAAM;MACvD,IAAMC,UAAgC,GAAG;QACvCU,IAAI,EAAER,SAAS;QACfS,MAAM,EAAET,SAAS;QACjBU,QAAQ,EAAEV,SAAS;QACnBW,QAAQ,EAAEX,SAAS;QACnBY,OAAO,EAAEZ;MACX,CAAC;MAEDG,MAAM,CAACL,UAAU,CAAC,CAACM,WAAW,CAAC,CAAC;MAChCD,MAAM,CAACL,UAAU,CAACU,IAAI,CAAC,CAACH,aAAa,CAAC,CAAC;MACvCF,MAAM,CAACL,UAAU,CAACW,MAAM,CAAC,CAACJ,aAAa,CAAC,CAAC;MACzCF,MAAM,CAACL,UAAU,CAACY,QAAQ,CAAC,CAACL,aAAa,CAAC,CAAC;MAC3CF,MAAM,CAACL,UAAU,CAACa,QAAQ,CAAC,CAACN,aAAa,CAAC,CAAC;MAC3CF,MAAM,CAACL,UAAU,CAACc,OAAO,CAAC,CAACP,aAAa,CAAC,CAAC;IAC5C,CAAC,CAAC;IAEFR,EAAE,CAAC,8CAA8C,EAAE,YAAM;MACvD,IAAMC,UAAgC,GAAG;QACvCe,SAAS,EAAEb,SAAS;QACpBc,QAAQ,EAAEd,SAAS;QACnBU,QAAQ,EAAEV,SAAS;QACnBW,QAAQ,EAAEX,SAAS;QACnBY,OAAO,EAAEZ;MACX,CAAC;MAEDG,MAAM,CAACL,UAAU,CAAC,CAACM,WAAW,CAAC,CAAC;MAChCD,MAAM,CAACL,UAAU,CAACe,SAAS,CAAC,CAACR,aAAa,CAAC,CAAC;MAC5CF,MAAM,CAACL,UAAU,CAACgB,QAAQ,CAAC,CAACT,aAAa,CAAC,CAAC;MAC3CF,MAAM,CAACL,UAAU,CAACY,QAAQ,CAAC,CAACL,aAAa,CAAC,CAAC;MAC3CF,MAAM,CAACL,UAAU,CAACa,QAAQ,CAAC,CAACN,aAAa,CAAC,CAAC;MAC3CF,MAAM,CAACL,UAAU,CAACc,OAAO,CAAC,CAACP,aAAa,CAAC,CAAC;IAC5C,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFT,QAAQ,CAAC,aAAa,EAAE,YAAM;IAC5BC,EAAE,CAAC,wCAAwC,EAAE,YAAM;MASjD,IAAMkB,QAAoB,GAAG,CAAC,MAAM,EAAE,cAAc,EAAE,cAAc,CAAC;MACrE,IAAMC,QAAoB,GAAG,CAAC,OAAO,EAAE,UAAU,CAAC;MAClD,IAAMC,YAA4B,GAAG,CACnC,MAAM,EACN,QAAQ,EACR,UAAU,EACV,UAAU,EACV,SAAS,CACV;MACD,IAAMC,YAA4B,GAAG,CACnC,WAAW,EACX,UAAU,EACV,UAAU,EACV,UAAU,EACV,SAAS,CACV;MAEDf,MAAM,CAACY,QAAQ,CAAC,CAACI,YAAY,CAAC,CAAC,CAAC;MAChChB,MAAM,CAACa,QAAQ,CAAC,CAACG,YAAY,CAAC,CAAC,CAAC;MAChChB,MAAM,CAACc,YAAY,CAAC,CAACE,YAAY,CAAC,CAAC,CAAC;MACpChB,MAAM,CAACe,YAAY,CAAC,CAACC,YAAY,CAAC,CAAC,CAAC;IACtC,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}