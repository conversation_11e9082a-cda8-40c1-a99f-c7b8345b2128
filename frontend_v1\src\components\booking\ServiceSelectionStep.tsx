/**
 * Service Selection Step - Booking Flow Component
 *
 * Component Contract:
 * - Displays available services from a provider
 * - Allows service selection with details and pricing
 * - Integrates with backend provider service API
 * - Provides service filtering and search functionality
 * - Implements proper loading states and error handling
 * - Follows responsive design and accessibility guidelines
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, TextInput } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Card } from '../molecules/Card';
import { StandardizedButton } from '../atoms/StandardizedButton';
import { useTheme } from '../../contexts/ThemeContext';
import { getResponsiveSpacing, getResponsiveFontSize } from '../../utils/responsiveUtils';
import { providerService } from '../../services/providerService';

interface Service {
  id: string;
  name: string;
  description: string;
  price: number;
  duration: number;
  category: string;
  image?: string;
}

interface ServiceSelectionStepProps {
  providerId: string;
  selectedServiceId?: string;
  onServiceSelect: (data: { serviceId: string; serviceName: string; servicePrice: number; serviceDuration: number }) => void;
}

export const ServiceSelectionStep: React.FC<ServiceSelectionStepProps> = ({
  providerId,
  selectedServiceId,
  onServiceSelect,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  
  const [services, setServices] = useState<Service[]>([]);
  const [filteredServices, setFilteredServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedService, setSelectedService] = useState<Service | null>(null);

  useEffect(() => {
    loadServices();
  }, [providerId]);

  useEffect(() => {
    filterServices();
  }, [services, searchQuery, selectedCategory]);

  const loadServices = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const providerServices = await providerService.getProviderServices(providerId);
      setServices(providerServices);
      
      // Pre-select service if provided
      if (selectedServiceId) {
        const preSelectedService = providerServices.find(s => s.id === selectedServiceId);
        if (preSelectedService) {
          setSelectedService(preSelectedService);
        }
      }
    } catch (err) {
      console.error('Failed to load services:', err);
      setError('Failed to load services');
    } finally {
      setLoading(false);
    }
  };

  const filterServices = () => {
    let filtered = services;

    // Filter by search query
    if (searchQuery.trim()) {
      filtered = filtered.filter(service =>
        service.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        service.description.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(service => service.category === selectedCategory);
    }

    setFilteredServices(filtered);
  };

  const handleServiceSelect = (service: Service) => {
    setSelectedService(service);
  };

  const handleContinue = () => {
    if (selectedService) {
      onServiceSelect({
        serviceId: selectedService.id,
        serviceName: selectedService.name,
        servicePrice: selectedService.price,
        serviceDuration: selectedService.duration,
      });
    }
  };

  const getUniqueCategories = () => {
    const categories = services.map(service => service.category);
    return ['all', ...Array.from(new Set(categories))];
  };

  const renderSearchAndFilter = () => (
    <Card style={styles.searchCard}>
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color={colors.text.tertiary} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search services..."
          placeholderTextColor={colors.text.tertiary}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>
      
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.categoriesContainer}>
        {getUniqueCategories().map((category) => (
          <TouchableOpacity
            key={category}
            style={[
              styles.categoryChip,
              selectedCategory === category && styles.categoryChipSelected,
            ]}
            onPress={() => setSelectedCategory(category)}>
            <Text style={[
              styles.categoryChipText,
              selectedCategory === category && styles.categoryChipTextSelected,
            ]}>
              {category === 'all' ? 'All Services' : category}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </Card>
  );

  const renderService = (service: Service) => (
    <TouchableOpacity
      key={service.id}
      style={[
        styles.serviceCard,
        selectedService?.id === service.id && styles.serviceCardSelected,
      ]}
      onPress={() => handleServiceSelect(service)}>
      
      <View style={styles.serviceHeader}>
        <View style={styles.serviceInfo}>
          <Text style={styles.serviceName}>{service.name}</Text>
          <Text style={styles.serviceCategory}>{service.category}</Text>
        </View>
        
        <View style={styles.servicePrice}>
          <Text style={styles.priceText}>${service.price}</Text>
          <Text style={styles.durationText}>{service.duration} min</Text>
        </View>
      </View>
      
      <Text style={styles.serviceDescription}>{service.description}</Text>
      
      {selectedService?.id === service.id && (
        <View style={styles.selectedIndicator}>
          <Ionicons name="checkmark-circle" size={20} color={colors.sage500} />
          <Text style={styles.selectedText}>Selected</Text>
        </View>
      )}
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <Card style={styles.loadingCard}>
        <Text style={styles.loadingText}>Loading services...</Text>
      </Card>
    );
  }

  if (error) {
    return (
      <Card style={styles.errorCard}>
        <Text style={styles.errorText}>{error}</Text>
        <StandardizedButton
          action="retry"
          onPress={loadServices}
          style={styles.retryButton}>
          Try Again
        </StandardizedButton>
      </Card>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.stepTitle}>Select a Service</Text>
      <Text style={styles.stepSubtitle}>Choose the service you'd like to book</Text>
      
      {renderSearchAndFilter()}
      
      <ScrollView style={styles.servicesContainer} showsVerticalScrollIndicator={false}>
        {filteredServices.length === 0 ? (
          <Card style={styles.emptyCard}>
            <Text style={styles.emptyText}>
              {searchQuery || selectedCategory !== 'all' 
                ? 'No services found matching your criteria'
                : 'No services available'
              }
            </Text>
          </Card>
        ) : (
          filteredServices.map(renderService)
        )}
      </ScrollView>
      
      {selectedService && (
        <View style={styles.continueContainer}>
          <StandardizedButton
            action="next"
            onPress={handleContinue}
            style={styles.continueButton}>
            Continue with {selectedService.name}
          </StandardizedButton>
        </View>
      )}
    </View>
  );
};

const createStyles = (colors: any) => ({
  container: {
    flex: 1,
  },
  stepTitle: {
    fontSize: getResponsiveFontSize(24),
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: getResponsiveSpacing(8),
  },
  stepSubtitle: {
    fontSize: getResponsiveFontSize(16),
    color: colors.text.secondary,
    marginBottom: getResponsiveSpacing(24),
  },
  searchCard: {
    padding: getResponsiveSpacing(16),
    marginBottom: getResponsiveSpacing(16),
  },
  searchContainer: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    backgroundColor: colors.background.secondary,
    borderRadius: 8,
    paddingHorizontal: getResponsiveSpacing(12),
    paddingVertical: getResponsiveSpacing(8),
    marginBottom: getResponsiveSpacing(16),
  },
  searchInput: {
    flex: 1,
    fontSize: getResponsiveFontSize(16),
    color: colors.text.primary,
    marginLeft: getResponsiveSpacing(8),
  },
  categoriesContainer: {
    flexDirection: 'row' as const,
  },
  categoryChip: {
    paddingHorizontal: getResponsiveSpacing(16),
    paddingVertical: getResponsiveSpacing(8),
    borderRadius: 20,
    backgroundColor: colors.background.secondary,
    marginRight: getResponsiveSpacing(8),
  },
  categoryChipSelected: {
    backgroundColor: colors.sage400,
  },
  categoryChipText: {
    fontSize: getResponsiveFontSize(14),
    color: colors.text.secondary,
  },
  categoryChipTextSelected: {
    color: colors.background.primary,
    fontWeight: '500',
  },
  servicesContainer: {
    flex: 1,
  },
  serviceCard: {
    backgroundColor: colors.background.primary,
    borderRadius: 12,
    padding: getResponsiveSpacing(16),
    marginBottom: getResponsiveSpacing(12),
    borderWidth: 1,
    borderColor: colors.border.primary,
  },
  serviceCardSelected: {
    borderColor: colors.sage400,
    backgroundColor: colors.sage50,
  },
  serviceHeader: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'flex-start' as const,
    marginBottom: getResponsiveSpacing(8),
  },
  serviceInfo: {
    flex: 1,
  },
  serviceName: {
    fontSize: getResponsiveFontSize(18),
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: getResponsiveSpacing(4),
  },
  serviceCategory: {
    fontSize: getResponsiveFontSize(14),
    color: colors.text.tertiary,
  },
  servicePrice: {
    alignItems: 'flex-end' as const,
  },
  priceText: {
    fontSize: getResponsiveFontSize(20),
    fontWeight: '600',
    color: colors.sage500,
  },
  durationText: {
    fontSize: getResponsiveFontSize(14),
    color: colors.text.secondary,
  },
  serviceDescription: {
    fontSize: getResponsiveFontSize(14),
    color: colors.text.secondary,
    lineHeight: 20,
    marginBottom: getResponsiveSpacing(8),
  },
  selectedIndicator: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    marginTop: getResponsiveSpacing(8),
  },
  selectedText: {
    fontSize: getResponsiveFontSize(14),
    color: colors.sage500,
    fontWeight: '500',
    marginLeft: getResponsiveSpacing(4),
  },
  continueContainer: {
    paddingTop: getResponsiveSpacing(16),
  },
  continueButton: {
    width: '100%',
  },
  loadingCard: {
    padding: getResponsiveSpacing(32),
    alignItems: 'center' as const,
  },
  loadingText: {
    fontSize: getResponsiveFontSize(16),
    color: colors.text.secondary,
  },
  errorCard: {
    padding: getResponsiveSpacing(24),
    alignItems: 'center' as const,
  },
  errorText: {
    fontSize: getResponsiveFontSize(16),
    color: colors.error,
    textAlign: 'center' as const,
    marginBottom: getResponsiveSpacing(16),
  },
  retryButton: {
    minWidth: 120,
  },
  emptyCard: {
    padding: getResponsiveSpacing(32),
    alignItems: 'center' as const,
  },
  emptyText: {
    fontSize: getResponsiveFontSize(16),
    color: colors.text.secondary,
    textAlign: 'center' as const,
  },
});
