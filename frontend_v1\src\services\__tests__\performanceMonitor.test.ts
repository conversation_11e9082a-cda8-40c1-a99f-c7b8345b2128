/**
 * Performance Monitor Service Tests
 *
 * Test Coverage:
 * - Performance metric tracking
 * - Memory monitoring
 * - Network performance tracking
 * - Report generation
 * - Error handling
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { performanceMonitor } from '../performanceMonitor';

describe('PerformanceMonitorService', () => {
  beforeEach(() => {
    performanceMonitor.clearMetrics();
    performanceMonitor.startMonitoring();
  });

  afterEach(() => {
    performanceMonitor.stopMonitoring();
    jest.clearAllMocks();
  });

  describe('Initialization', () => {
    it('starts monitoring correctly', () => {
      expect(performanceMonitor['isMonitoring']).toBe(true);
    });

    it('stops monitoring correctly', () => {
      performanceMonitor.stopMonitoring();
      expect(performanceMonitor['isMonitoring']).toBe(false);
    });

    it('prevents double initialization', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      performanceMonitor.startMonitoring();
      performanceMonitor.startMonitoring();
      
      // Should only log start message once
      expect(consoleSpy).toHaveBeenCalledTimes(1);
      
      consoleSpy.mockRestore();
    });
  });

  describe('Render Performance Tracking', () => {
    it('tracks component render times', () => {
      const componentName = 'TestComponent';
      const renderTime = 50;
      
      performanceMonitor.trackRender(componentName, renderTime);
      
      const metrics = performanceMonitor.getMetricsByCategory('render');
      expect(metrics).toHaveLength(1);
      expect(metrics[0].name).toBe('component_render');
      expect(metrics[0].value).toBe(renderTime);
      expect(metrics[0].metadata?.componentName).toBe(componentName);
    });

    it('calculates average render times for components', () => {
      const componentName = 'TestComponent';
      
      performanceMonitor.trackRender(componentName, 40);
      performanceMonitor.trackRender(componentName, 60);
      
      const renderMetrics = performanceMonitor['renderMetrics'];
      const componentMetrics = renderMetrics.get(componentName);
      
      expect(componentMetrics?.renderTime).toBe(50); // Average of 40 and 60
      expect(componentMetrics?.reRenders).toBe(2);
    });

    it('warns about slow renders', () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
      
      performanceMonitor.trackRender('SlowComponent', 100); // Above 16ms threshold
      
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Slow render detected: SlowComponent took 100ms')
      );
      
      consoleSpy.mockRestore();
    });

    it('tracks render metadata correctly', () => {
      const metadata = { propsCount: 5, stateUpdates: 2 };
      
      performanceMonitor.trackRender('TestComponent', 30, metadata);
      
      const renderMetrics = performanceMonitor['renderMetrics'];
      const componentMetrics = renderMetrics.get('TestComponent');
      
      expect(componentMetrics?.propsCount).toBe(5);
      expect(componentMetrics?.stateUpdates).toBe(2);
    });
  });

  describe('Network Performance Tracking', () => {
    it('tracks network request performance', () => {
      const url = '/api/test';
      const method = 'GET';
      const responseTime = 200;
      const statusCode = 200;
      
      performanceMonitor.trackNetworkRequest(url, method, responseTime, statusCode);
      
      const metrics = performanceMonitor.getMetricsByCategory('network');
      expect(metrics).toHaveLength(1);
      expect(metrics[0].value).toBe(responseTime);
      expect(metrics[0].metadata?.url).toBe(url);
      expect(metrics[0].metadata?.method).toBe(method);
      expect(metrics[0].metadata?.statusCode).toBe(statusCode);
    });

    it('warns about slow network requests', () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
      
      performanceMonitor.trackNetworkRequest('/api/slow', 'GET', 3000, 200); // Above 2s threshold
      
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Slow network request: GET /api/slow took 3000ms')
      );
      
      consoleSpy.mockRestore();
    });

    it('tracks cached vs non-cached requests', () => {
      performanceMonitor.trackNetworkRequest('/api/cached', 'GET', 50, 200, 0, 0, true);
      performanceMonitor.trackNetworkRequest('/api/fresh', 'GET', 200, 200, 0, 0, false);
      
      const networkMetrics = performanceMonitor['networkMetrics'];
      expect(networkMetrics[0].cached).toBe(true);
      expect(networkMetrics[1].cached).toBe(false);
    });

    it('limits stored network metrics', () => {
      const maxMetrics = performanceMonitor['MAX_METRICS'];
      
      // Add more than max metrics
      for (let i = 0; i < maxMetrics + 100; i++) {
        performanceMonitor.trackNetworkRequest(`/api/test${i}`, 'GET', 100, 200);
      }
      
      const networkMetrics = performanceMonitor['networkMetrics'];
      expect(networkMetrics.length).toBeLessThanOrEqual(maxMetrics);
    });
  });

  describe('User Interaction Tracking', () => {
    it('tracks user interaction performance', () => {
      const interactionType = 'button_click';
      const responseTime = 50;
      const metadata = { buttonId: 'submit' };
      
      performanceMonitor.trackUserInteraction(interactionType, responseTime, metadata);
      
      const metrics = performanceMonitor.getMetricsByCategory('user_interaction');
      expect(metrics).toHaveLength(1);
      expect(metrics[0].value).toBe(responseTime);
      expect(metrics[0].metadata?.interactionType).toBe(interactionType);
      expect(metrics[0].metadata?.buttonId).toBe('submit');
    });

    it('warns about slow interactions', () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
      
      performanceMonitor.trackUserInteraction('slow_interaction', 150); // Above 100ms threshold
      
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Slow interaction: slow_interaction took 150ms')
      );
      
      consoleSpy.mockRestore();
    });
  });

  describe('Navigation Performance Tracking', () => {
    it('tracks navigation performance', () => {
      const fromScreen = 'Home';
      const toScreen = 'Profile';
      const navigationTime = 300;
      
      performanceMonitor.trackNavigation(fromScreen, toScreen, navigationTime);
      
      const metrics = performanceMonitor.getMetricsByCategory('navigation');
      expect(metrics).toHaveLength(1);
      expect(metrics[0].value).toBe(navigationTime);
      expect(metrics[0].metadata?.fromScreen).toBe(fromScreen);
      expect(metrics[0].metadata?.toScreen).toBe(toScreen);
    });
  });

  describe('Performance Reports', () => {
    beforeEach(() => {
      // Add some test data
      performanceMonitor.trackRender('FastComponent', 10);
      performanceMonitor.trackRender('SlowComponent', 50);
      performanceMonitor.trackNetworkRequest('/api/fast', 'GET', 100, 200, 0, 0, true);
      performanceMonitor.trackNetworkRequest('/api/slow', 'GET', 3000, 200, 0, 0, false);
      performanceMonitor.trackUserInteraction('click', 25);
    });

    it('generates comprehensive performance report', () => {
      const report = performanceMonitor.getPerformanceReport();
      
      expect(report.summary).toBeDefined();
      expect(report.summary.averageRenderTime).toBeGreaterThan(0);
      expect(report.summary.averageNetworkTime).toBeGreaterThan(0);
      expect(report.summary.cacheHitRate).toBeDefined();
      
      expect(report.slowComponents).toBeDefined();
      expect(report.slowNetworkRequests).toBeDefined();
      expect(report.recommendations).toBeDefined();
    });

    it('calculates cache hit rate correctly', () => {
      const report = performanceMonitor.getPerformanceReport();
      
      // 1 cached out of 2 total requests = 50%
      expect(report.summary.cacheHitRate).toBe(0.5);
    });

    it('identifies slow components', () => {
      const report = performanceMonitor.getPerformanceReport();
      
      const slowComponent = report.slowComponents.find(c => c.componentName === 'SlowComponent');
      expect(slowComponent).toBeDefined();
      expect(slowComponent?.renderTime).toBe(50);
    });

    it('identifies slow network requests', () => {
      const report = performanceMonitor.getPerformanceReport();
      
      const slowRequest = report.slowNetworkRequests.find(r => r.url === '/api/slow');
      expect(slowRequest).toBeDefined();
      expect(slowRequest?.responseTime).toBe(3000);
    });

    it('generates performance recommendations', () => {
      const report = performanceMonitor.getPerformanceReport();
      
      expect(report.recommendations).toContain(
        expect.stringContaining('Optimize slow components')
      );
      expect(report.recommendations).toContain(
        expect.stringContaining('request caching')
      );
    });
  });

  describe('Memory Monitoring', () => {
    it('collects memory metrics when available', () => {
      // Mock performance.memory
      const mockMemory = {
        usedJSHeapSize: 1000000,
        totalJSHeapSize: 2000000,
        jsHeapSizeLimit: 4000000,
      };
      
      (global as any).performance = { memory: mockMemory };
      
      performanceMonitor['collectMemoryMetrics']();
      
      const memoryMetrics = performanceMonitor['memoryMetrics'];
      expect(memoryMetrics).toHaveLength(1);
      expect(memoryMetrics[0].usedJSHeapSize).toBe(1000000);
    });

    it('detects potential memory leaks', () => {
      // Mock memory growth
      const memoryMetrics = performanceMonitor['memoryMetrics'];
      
      // Add metrics showing memory growth
      for (let i = 0; i < 10; i++) {
        memoryMetrics.push({
          usedJSHeapSize: 1000000 + (i * 10000000), // Growing memory
          totalJSHeapSize: 2000000,
          jsHeapSizeLimit: 4000000,
          timestamp: Date.now() + i,
        });
      }
      
      const leaks = performanceMonitor['detectMemoryLeaks']();
      expect(leaks.length).toBeGreaterThan(0);
      expect(leaks[0]).toContain('Memory usage increased');
    });

    it('detects excessive re-renders', () => {
      // Create component with many re-renders
      for (let i = 0; i < 150; i++) {
        performanceMonitor.trackRender('ExcessiveComponent', 10);
      }
      
      const leaks = performanceMonitor['detectMemoryLeaks']();
      const reRenderLeak = leaks.find(leak => leak.includes('ExcessiveComponent'));
      
      expect(reRenderLeak).toBeDefined();
      expect(reRenderLeak).toContain('150 re-renders');
    });
  });

  describe('Cleanup and Maintenance', () => {
    it('clears all metrics', () => {
      performanceMonitor.trackRender('TestComponent', 50);
      performanceMonitor.trackNetworkRequest('/api/test', 'GET', 100, 200);
      
      performanceMonitor.clearMetrics();
      
      expect(performanceMonitor.getMetricsByCategory('render')).toHaveLength(0);
      expect(performanceMonitor.getMetricsByCategory('network')).toHaveLength(0);
    });

    it('cleans up old metrics automatically', () => {
      const oldTimestamp = Date.now() - 700000; // 11+ minutes ago
      
      // Manually add old metric
      performanceMonitor['metrics'].push({
        name: 'old_metric',
        value: 100,
        timestamp: oldTimestamp,
        category: 'render',
      });
      
      performanceMonitor['cleanupOldMetrics']();
      
      const oldMetric = performanceMonitor['metrics'].find(m => m.timestamp === oldTimestamp);
      expect(oldMetric).toBeUndefined();
    });

    it('destroys service correctly', () => {
      const clearIntervalSpy = jest.spyOn(global, 'clearInterval');
      
      performanceMonitor.destroy();
      
      expect(clearIntervalSpy).toHaveBeenCalled();
      expect(performanceMonitor['memoryCache'].size).toBe(0);
      
      clearIntervalSpy.mockRestore();
    });
  });
});
