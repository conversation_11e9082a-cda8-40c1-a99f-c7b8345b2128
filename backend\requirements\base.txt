# V<PERSON><PERSON>end - Base Requirements
# Django 5.1 + DRF + PostgreSQL + Redis + Celery

# Core Django Framework
Django==4.2.16
djangorestframework==3.15.2
django-cors-headers==4.4.0
django-filter==24.3
django-extensions==3.2.3

# Database & ORM
psycopg2-binary==2.9.9
django-environ==0.11.2

# PostGIS for Geospatial Support (built into Django)
# PostGIS support is included in Django's GIS framework

# Authentication & Security
djangorestframework-simplejwt==5.3.0
django-oauth-toolkit==1.7.1
cryptography==45.0.4
PyJWT==2.8.0

# API Documentation
drf-spectacular==0.27.0
drf-spectacular-sidecar==2024.1.1

# Caching & Session Management
django-redis==5.4.0
redis==5.0.1

# WebSocket Support for Real-time Features
channels==4.0.0
channels-redis==4.1.0

# Performance Monitoring
psutil==5.9.6

# Background Tasks
celery==5.3.4
django-celery-beat==2.5.0
django-celery-results==2.5.1

# File Storage & Media
Pillow==10.1.0
django-storages==1.14.2
boto3==1.34.0

# Utilities
python-decouple==3.8
pytz==2023.3
python-dateutil==2.8.2
requests==2.31.0

# Validation & Serialization
marshmallow==3.20.2
bleach==6.1.0

# Monitoring & Logging
structlog==23.2.0
django-prometheus==2.3.1
python-json-logger==3.3.0
psutil==5.9.6

# Performance
django-debug-toolbar==4.2.0
