{"version": 3, "names": ["EventEmitter", "exports", "default", "_classCallCheck2", "Object", "defineProperty", "_registry", "writable", "value", "_createClass2", "key", "addListener", "eventType", "listener", "context", "TypeError", "registrations", "allocate", "_classPrivateFieldLooseBase2", "registration", "remove", "delete", "add", "emit", "_len", "arguments", "length", "args", "Array", "_key", "from", "apply", "removeAllListeners", "listenerCount", "size", "registry", "Set"], "sources": ["EventEmitter.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict\n * @format\n */\n\n// $FlowFixMe[unclear-type] unclear type of events\ntype UnsafeObject = Object;\n\nexport interface EventSubscription {\n  remove(): void;\n}\n\nexport interface IEventEmitter<\n  TEventToArgsMap: $ReadOnly<Record<string, $ReadOnlyArray<UnsafeObject>>>,\n> {\n  addListener<TEvent: $Keys<TEventToArgsMap>>(\n    eventType: TEvent,\n    listener: (...args: TEventToArgsMap[TEvent]) => mixed,\n    context?: mixed,\n  ): EventSubscription;\n\n  emit<TEvent: $Keys<TEventToArgsMap>>(\n    eventType: TEvent,\n    ...args: TEventToArgsMap[TEvent]\n  ): void;\n\n  removeAllListeners<TEvent: $Keys<TEventToArgsMap>>(eventType?: ?TEvent): void;\n\n  listenerCount<TEvent: $Keys<TEventToArgsMap>>(eventType: TEvent): number;\n}\n\ninterface Registration<TArgs> {\n  +context: mixed;\n  +listener: (...args: TArgs) => mixed;\n  +remove: () => void;\n}\n\ntype Registry<\n  TEventToArgsMap: $ReadOnly<Record<string, $ReadOnlyArray<UnsafeObject>>>,\n> = {\n  [K in keyof TEventToArgsMap]: Set<Registration<TEventToArgsMap[K]>>,\n};\n\n/**\n * EventEmitter manages listeners and publishes events to them.\n *\n * EventEmitter accepts a single type parameter that defines the valid events\n * and associated listener argument(s).\n *\n * @example\n *\n *   const emitter = new EventEmitter<{\n *     success: [number, string],\n *     error: [Error],\n *   }>();\n *\n *   emitter.on('success', (statusCode, responseText) => {...});\n *   emitter.emit('success', 200, '...');\n *\n *   emitter.on('error', error => {...});\n *   emitter.emit('error', new Error('Resource not found'));\n *\n */\nexport default class EventEmitter<\n  TEventToArgsMap: $ReadOnly<\n    Record<string, $ReadOnlyArray<UnsafeObject>>,\n  > = $ReadOnly<Record<string, $ReadOnlyArray<UnsafeObject>>>,\n> implements IEventEmitter<TEventToArgsMap>\n{\n  // $FlowFixMe[incompatible-type]\n  #registry: Registry<TEventToArgsMap> = {};\n\n  /**\n   * Registers a listener that is called when the supplied event is emitted.\n   * Returns a subscription that has a `remove` method to undo registration.\n   */\n  addListener<TEvent: $Keys<TEventToArgsMap>>(\n    eventType: TEvent,\n    listener: (...args: TEventToArgsMap[TEvent]) => mixed,\n    context: mixed,\n  ): EventSubscription {\n    if (typeof listener !== 'function') {\n      throw new TypeError(\n        'EventEmitter.addListener(...): 2nd argument must be a function.',\n      );\n    }\n    const registrations = allocate<\n      TEventToArgsMap,\n      $Keys<TEventToArgsMap>,\n      TEventToArgsMap[TEvent],\n    >(this.#registry, eventType);\n    const registration: Registration<TEventToArgsMap[TEvent]> = {\n      context,\n      listener,\n      remove(): void {\n        registrations.delete(registration);\n      },\n    };\n    registrations.add(registration);\n    return registration;\n  }\n\n  /**\n   * Emits the supplied event. Additional arguments supplied to `emit` will be\n   * passed through to each of the registered listeners.\n   *\n   * If a listener modifies the listeners registered for the same event, those\n   * changes will not be reflected in the current invocation of `emit`.\n   */\n  emit<TEvent: $Keys<TEventToArgsMap>>(\n    eventType: TEvent,\n    ...args: TEventToArgsMap[TEvent]\n  ): void {\n    const registrations: ?Set<Registration<TEventToArgsMap[TEvent]>> =\n      this.#registry[eventType];\n    if (registrations != null) {\n      // Copy `registrations` to take a snapshot when we invoke `emit`, in case\n      // registrations are added or removed when listeners are invoked.\n      for (const registration of Array.from(registrations)) {\n        // $FlowFixMe[incompatible-call]\n        registration.listener.apply(registration.context, args);\n      }\n    }\n  }\n\n  /**\n   * Removes all registered listeners.\n   */\n  removeAllListeners<TEvent: $Keys<TEventToArgsMap>>(\n    eventType?: ?TEvent,\n  ): void {\n    if (eventType == null) {\n      // $FlowFixMe[incompatible-type]\n      this.#registry = {};\n    } else {\n      delete this.#registry[eventType];\n    }\n  }\n\n  /**\n   * Returns the number of registered listeners for the supplied event.\n   */\n  listenerCount<TEvent: $Keys<TEventToArgsMap>>(eventType: TEvent): number {\n    const registrations: ?Set<Registration<TEventToArgsMap[TEvent]>> =\n      this.#registry[eventType];\n    return registrations == null ? 0 : registrations.size;\n  }\n}\n\nfunction allocate<\n  TEventToArgsMap: $ReadOnly<Record<string, $ReadOnlyArray<UnsafeObject>>>,\n  TEvent: $Keys<TEventToArgsMap>,\n  TEventArgs: TEventToArgsMap[TEvent],\n>(\n  registry: Registry<TEventToArgsMap>,\n  eventType: TEvent,\n): Set<Registration<TEventToArgsMap[TEvent]>> {\n  let registrations: ?Set<Registration<TEventToArgsMap[TEvent]>> =\n    registry[eventType];\n  if (registrations == null) {\n    registrations = new Set();\n    registry[eventType] = registrations;\n  }\n  return registrations;\n}\n"], "mappings": ";;;;;;;;;;IAoEqBA,YAAY,GAAAC,OAAA,CAAAC,OAAA;EAAA,SAAAF,aAAA;IAAA,IAAAG,gBAAA,CAAAD,OAAA,QAAAF,YAAA;IAAAI,MAAA,CAAAC,cAAA,OAAAC,SAAA;MAAAC,QAAA;MAAAC,KAAA,EAOQ,CAAC;IAAC;EAAA;EAAA,WAAAC,aAAA,CAAAP,OAAA,EAAAF,YAAA;IAAAU,GAAA;IAAAF,KAAA,EAMzC,SAAAG,WAAWA,CACTC,SAAiB,EACjBC,QAAqD,EACrDC,OAAc,EACK;MACnB,IAAI,OAAOD,QAAQ,KAAK,UAAU,EAAE;QAClC,MAAM,IAAIE,SAAS,CACjB,iEACF,CAAC;MACH;MACA,IAAMC,aAAa,GAAGC,QAAQ,KAAAC,4BAAA,CAAAhB,OAAA,EAI5B,IAAI,EAAAI,SAAA,EAAAA,SAAA,GAAYM,SAAS,CAAC;MAC5B,IAAMO,YAAmD,GAAG;QAC1DL,OAAO,EAAPA,OAAO;QACPD,QAAQ,EAARA,QAAQ;QACRO,MAAM,WAANA,MAAMA,CAAA,EAAS;UACbJ,aAAa,CAACK,MAAM,CAACF,YAAY,CAAC;QACpC;MACF,CAAC;MACDH,aAAa,CAACM,GAAG,CAACH,YAAY,CAAC;MAC/B,OAAOA,YAAY;IACrB;EAAC;IAAAT,GAAA;IAAAF,KAAA,EASD,SAAAe,IAAIA,CACFX,SAAiB,EAEX;MACN,IAAMI,aAA0D,GAC9D,IAAAE,4BAAA,CAAAhB,OAAA,MAAI,EAAAI,SAAA,EAAAA,SAAA,EAAWM,SAAS,CAAC;MAC3B,IAAII,aAAa,IAAI,IAAI,EAAE;QAAA,SAAAQ,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAJxBC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;UAAJF,IAAI,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;QAAA;QAOL,KAAK,IAAMV,YAAY,IAAIS,KAAK,CAACE,IAAI,CAACd,aAAa,CAAC,EAAE;UAEpDG,YAAY,CAACN,QAAQ,CAACkB,KAAK,CAACZ,YAAY,CAACL,OAAO,EAAEa,IAAI,CAAC;QACzD;MACF;IACF;EAAC;IAAAjB,GAAA;IAAAF,KAAA,EAKD,SAAAwB,kBAAkBA,CAChBpB,SAAmB,EACb;MACN,IAAIA,SAAS,IAAI,IAAI,EAAE;QAErB,IAAAM,4BAAA,CAAAhB,OAAA,MAAI,EAAAI,SAAA,EAAAA,SAAA,IAAa,CAAC,CAAC;MACrB,CAAC,MAAM;QACL,OAAO,IAAAY,4BAAA,CAAAhB,OAAA,MAAI,EAAAI,SAAA,EAAAA,SAAA,EAAWM,SAAS,CAAC;MAClC;IACF;EAAC;IAAAF,GAAA;IAAAF,KAAA,EAKD,SAAAyB,aAAaA,CAAiCrB,SAAiB,EAAU;MACvE,IAAMI,aAA0D,GAC9D,IAAAE,4BAAA,CAAAhB,OAAA,MAAI,EAAAI,SAAA,EAAAA,SAAA,EAAWM,SAAS,CAAC;MAC3B,OAAOI,aAAa,IAAI,IAAI,GAAG,CAAC,GAAGA,aAAa,CAACkB,IAAI;IACvD;EAAC;AAAA;AAGH,SAASjB,QAAQA,CAKfkB,QAAmC,EACnCvB,SAAiB,EAC2B;EAC5C,IAAII,aAA0D,GAC5DmB,QAAQ,CAACvB,SAAS,CAAC;EACrB,IAAII,aAAa,IAAI,IAAI,EAAE;IACzBA,aAAa,GAAG,IAAIoB,GAAG,CAAC,CAAC;IACzBD,QAAQ,CAACvB,SAAS,CAAC,GAAGI,aAAa;EACrC;EACA,OAAOA,aAAa;AACtB", "ignoreList": []}