98085269130773af1f94035064535b3a
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.AnimatedButton = void 0;
var _react = _interopRequireWildcard(require("react"));
var _reactNative = require("react-native");
var _vectorIcons = require("@expo/vector-icons");
var _HighContrastContext = require("../../contexts/HighContrastContext");
var _MotorAccessibilityContext = require("../../contexts/MotorAccessibilityContext");
var _Typography = require("../typography/Typography");
var _animationUtils = require("../../utils/animationUtils");
var _jsxRuntime = require("react/jsx-runtime");
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var AnimatedButton = exports.AnimatedButton = function AnimatedButton(_ref) {
  var title = _ref.title,
    icon = _ref.icon,
    _ref$iconPosition = _ref.iconPosition,
    iconPosition = _ref$iconPosition === void 0 ? 'left' : _ref$iconPosition,
    onPress = _ref.onPress,
    _ref$disabled = _ref.disabled,
    disabled = _ref$disabled === void 0 ? false : _ref$disabled,
    _ref$state = _ref.state,
    state = _ref$state === void 0 ? 'idle' : _ref$state,
    _ref$variant = _ref.variant,
    variant = _ref$variant === void 0 ? 'primary' : _ref$variant,
    _ref$size = _ref.size,
    size = _ref$size === void 0 ? 'medium' : _ref$size,
    _ref$fullWidth = _ref.fullWidth,
    fullWidth = _ref$fullWidth === void 0 ? false : _ref$fullWidth,
    style = _ref.style,
    _ref$enableAnimations = _ref.enableAnimations,
    enableAnimations = _ref$enableAnimations === void 0 ? true : _ref$enableAnimations,
    _ref$pressScale = _ref.pressScale,
    pressScale = _ref$pressScale === void 0 ? 0.95 : _ref$pressScale,
    accessibilityLabel = _ref.accessibilityLabel,
    accessibilityHint = _ref.accessibilityHint,
    testID = _ref.testID;
  var scaleValue = (0, _react.useRef)(new _reactNative.Animated.Value(1)).current;
  var rotationValue = (0, _react.useRef)(new _reactNative.Animated.Value(0)).current;
  var opacityValue = (0, _react.useRef)(new _reactNative.Animated.Value(1)).current;
  var shakeValue = (0, _react.useRef)(new _reactNative.Animated.Value(0)).current;
  var _useHighContrastColor = (0, _HighContrastContext.useHighContrastColors)(),
    colors = _useHighContrastColor.colors;
  var touchTargetStyles = (0, _MotorAccessibilityContext.useTouchTargetStyles)();
  var triggerHapticFeedback = (0, _MotorAccessibilityContext.useHapticFeedback)();
  var _createPressAnimation = (0, _animationUtils.createPressAnimation)(scaleValue, pressScale),
    pressIn = _createPressAnimation.pressIn,
    pressOut = _createPressAnimation.pressOut;
  var handlePressIn = (0, _react.useCallback)(function () {
    if (!disabled && enableAnimations && !(0, _animationUtils.shouldReduceMotion)()) {
      pressIn();
    }
    triggerHapticFeedback('light');
  }, [disabled, enableAnimations, pressIn, triggerHapticFeedback]);
  var handlePressOut = (0, _react.useCallback)(function () {
    if (!disabled && enableAnimations && !(0, _animationUtils.shouldReduceMotion)()) {
      pressOut();
    }
  }, [disabled, enableAnimations, pressOut]);
  var handlePress = (0, _react.useCallback)(function () {
    if (!disabled && state !== 'loading') {
      triggerHapticFeedback('medium');
      onPress();
    }
  }, [disabled, state, onPress, triggerHapticFeedback]);
  (0, _react.useEffect)(function () {
    if (state === 'loading' && enableAnimations && !(0, _animationUtils.shouldReduceMotion)()) {
      var loadingAnimation = (0, _animationUtils.createLoadingAnimation)(rotationValue);
      loadingAnimation.start();
      return function () {
        return loadingAnimation.stop();
      };
    } else {
      rotationValue.setValue(0);
    }
  }, [state, enableAnimations, rotationValue]);
  (0, _react.useEffect)(function () {
    if (state === 'success' && enableAnimations && !(0, _animationUtils.shouldReduceMotion)()) {
      var successAnimation = (0, _animationUtils.pulse)(scaleValue, 1.1);
      successAnimation.start(function () {
        if (_reactNative.Platform.OS === 'ios' || _reactNative.Platform.OS === 'android') {
          _reactNative.AccessibilityInfo.announceForAccessibility('Action completed successfully');
        }
      });
    }
  }, [state, enableAnimations, scaleValue]);
  (0, _react.useEffect)(function () {
    if (state === 'error' && enableAnimations && !(0, _animationUtils.shouldReduceMotion)()) {
      var errorAnimation = (0, _animationUtils.shake)(shakeValue, 8);
      errorAnimation.start(function () {
        if (_reactNative.Platform.OS === 'ios' || _reactNative.Platform.OS === 'android') {
          _reactNative.AccessibilityInfo.announceForAccessibility('Action failed. Please try again.');
        }
      });
    }
  }, [state, enableAnimations, shakeValue]);
  var getButtonStyles = function getButtonStyles() {
    var _colors$button, _colors$primary, _colors$button2, _colors$secondary, _colors$border, _colors$button3, _colors$primary2;
    var baseStyles = [styles.button, styles[`${size}Button`]];
    if (fullWidth) {
      baseStyles.push(styles.fullWidth);
    }
    switch (variant) {
      case 'primary':
        baseStyles.push({
          backgroundColor: disabled ? (colors == null || (_colors$button = colors.button) == null ? void 0 : _colors$button.disabled) || '#CCC' : (colors == null || (_colors$primary = colors.primary) == null ? void 0 : _colors$primary.default) || '#5A7A63'
        });
        break;
      case 'secondary':
        baseStyles.push({
          backgroundColor: disabled ? (colors == null || (_colors$button2 = colors.button) == null ? void 0 : _colors$button2.disabled) || '#CCC' : (colors == null || (_colors$secondary = colors.secondary) == null ? void 0 : _colors$secondary.default) || '#F0F0F0',
          borderWidth: 1,
          borderColor: (colors == null || (_colors$border = colors.border) == null ? void 0 : _colors$border.primary) || '#DDD'
        });
        break;
      case 'outline':
        baseStyles.push({
          backgroundColor: 'transparent',
          borderWidth: 2,
          borderColor: disabled ? (colors == null || (_colors$button3 = colors.button) == null ? void 0 : _colors$button3.disabled) || '#CCC' : (colors == null || (_colors$primary2 = colors.primary) == null ? void 0 : _colors$primary2.default) || '#5A7A63'
        });
        break;
      case 'ghost':
        baseStyles.push({
          backgroundColor: 'transparent'
        });
        break;
    }
    if (state === 'loading') {
      baseStyles.push(styles.loadingButton);
    }
    return baseStyles;
  };
  var getTextColor = function getTextColor() {
    var _colors$text, _colors$text2, _colors$text3, _colors$primary3, _colors$primary4, _colors$text4;
    if (disabled) return (colors == null || (_colors$text = colors.text) == null ? void 0 : _colors$text.disabled) || '#999';
    switch (variant) {
      case 'primary':
        return (colors == null || (_colors$text2 = colors.text) == null ? void 0 : _colors$text2.inverse) || '#FFFFFF';
      case 'secondary':
        return (colors == null || (_colors$text3 = colors.text) == null ? void 0 : _colors$text3.primary) || '#333';
      case 'outline':
        return (colors == null || (_colors$primary3 = colors.primary) == null ? void 0 : _colors$primary3.default) || '#5A7A63';
      case 'ghost':
        return (colors == null || (_colors$primary4 = colors.primary) == null ? void 0 : _colors$primary4.default) || '#5A7A63';
      default:
        return (colors == null || (_colors$text4 = colors.text) == null ? void 0 : _colors$text4.primary) || '#333';
    }
  };
  var getIconSize = function getIconSize() {
    switch (size) {
      case 'small':
        return 16;
      case 'large':
        return 24;
      default:
        return 20;
    }
  };
  var renderLoadingIcon = function renderLoadingIcon() {
    if (state !== 'loading') return null;
    return (0, _jsxRuntime.jsx)(_reactNative.Animated.View, {
      style: [styles.iconContainer, {
        transform: [{
          rotate: rotationValue.interpolate({
            inputRange: [0, 1],
            outputRange: ['0deg', '360deg']
          })
        }]
      }],
      children: (0, _jsxRuntime.jsx)(_vectorIcons.Ionicons, {
        name: "refresh",
        size: getIconSize(),
        color: getTextColor()
      })
    });
  };
  var renderIcon = function renderIcon() {
    if (state === 'loading') return renderLoadingIcon();
    if (!icon) return null;
    var iconName = icon;
    if (state === 'success') iconName = 'checkmark';
    if (state === 'error') iconName = 'close';
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: styles.iconContainer,
      children: (0, _jsxRuntime.jsx)(_vectorIcons.Ionicons, {
        name: iconName,
        size: getIconSize(),
        color: getTextColor()
      })
    });
  };
  var getButtonTitle = function getButtonTitle() {
    switch (state) {
      case 'loading':
        return 'Loading...';
      case 'success':
        return 'Success!';
      case 'error':
        return 'Try Again';
      default:
        return title;
    }
  };
  return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
    style: [touchTargetStyles, style],
    onPress: handlePress,
    onPressIn: handlePressIn,
    onPressOut: handlePressOut,
    disabled: disabled || state === 'loading',
    activeOpacity: 0.8,
    accessibilityRole: "button",
    accessibilityLabel: accessibilityLabel || title,
    accessibilityHint: accessibilityHint,
    accessibilityState: {
      disabled: disabled || state === 'loading',
      busy: state === 'loading'
    },
    testID: testID,
    children: (0, _jsxRuntime.jsxs)(_reactNative.Animated.View, {
      style: [getButtonStyles(), {
        transform: [{
          scale: scaleValue
        }, {
          translateX: shakeValue
        }],
        opacity: opacityValue
      }],
      children: [iconPosition === 'left' && renderIcon(), (0, _jsxRuntime.jsx)(_Typography.Typography, {
        variant: "button",
        color: getTextColor(),
        style: [styles.buttonText, icon && iconPosition === 'left' && styles.textWithLeftIcon, icon && iconPosition === 'right' && styles.textWithRightIcon],
        children: getButtonTitle()
      }), iconPosition === 'right' && renderIcon()]
    })
  });
};
var styles = _reactNative.StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12
  },
  smallButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6
  },
  mediumButton: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8
  },
  largeButton: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderRadius: 10
  },
  fullWidth: {
    width: '100%'
  },
  loadingButton: {
    opacity: 0.8
  },
  buttonText: {
    textAlign: 'center'
  },
  textWithLeftIcon: {
    marginLeft: 8
  },
  textWithRightIcon: {
    marginRight: 8
  },
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center'
  }
});
var _default = exports.default = AnimatedButton;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfcmVhY3QiLCJfaW50ZXJvcFJlcXVpcmVXaWxkY2FyZCIsInJlcXVpcmUiLCJfcmVhY3ROYXRpdmUiLCJfdmVjdG9ySWNvbnMiLCJfSGlnaENvbnRyYXN0Q29udGV4dCIsIl9Nb3RvckFjY2Vzc2liaWxpdHlDb250ZXh0IiwiX1R5cG9ncmFwaHkiLCJfYW5pbWF0aW9uVXRpbHMiLCJfanN4UnVudGltZSIsImUiLCJ0IiwiV2Vha01hcCIsInIiLCJuIiwiX19lc01vZHVsZSIsIm8iLCJpIiwiZiIsIl9fcHJvdG9fXyIsImRlZmF1bHQiLCJoYXMiLCJnZXQiLCJzZXQiLCJfdCIsImhhc093blByb3BlcnR5IiwiY2FsbCIsIk9iamVjdCIsImRlZmluZVByb3BlcnR5IiwiZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yIiwiQW5pbWF0ZWRCdXR0b24iLCJleHBvcnRzIiwiX3JlZiIsInRpdGxlIiwiaWNvbiIsIl9yZWYkaWNvblBvc2l0aW9uIiwiaWNvblBvc2l0aW9uIiwib25QcmVzcyIsIl9yZWYkZGlzYWJsZWQiLCJkaXNhYmxlZCIsIl9yZWYkc3RhdGUiLCJzdGF0ZSIsIl9yZWYkdmFyaWFudCIsInZhcmlhbnQiLCJfcmVmJHNpemUiLCJzaXplIiwiX3JlZiRmdWxsV2lkdGgiLCJmdWxsV2lkdGgiLCJzdHlsZSIsIl9yZWYkZW5hYmxlQW5pbWF0aW9ucyIsImVuYWJsZUFuaW1hdGlvbnMiLCJfcmVmJHByZXNzU2NhbGUiLCJwcmVzc1NjYWxlIiwiYWNjZXNzaWJpbGl0eUxhYmVsIiwiYWNjZXNzaWJpbGl0eUhpbnQiLCJ0ZXN0SUQiLCJzY2FsZVZhbHVlIiwidXNlUmVmIiwiQW5pbWF0ZWQiLCJWYWx1ZSIsImN1cnJlbnQiLCJyb3RhdGlvblZhbHVlIiwib3BhY2l0eVZhbHVlIiwic2hha2VWYWx1ZSIsIl91c2VIaWdoQ29udHJhc3RDb2xvciIsInVzZUhpZ2hDb250cmFzdENvbG9ycyIsImNvbG9ycyIsInRvdWNoVGFyZ2V0U3R5bGVzIiwidXNlVG91Y2hUYXJnZXRTdHlsZXMiLCJ0cmlnZ2VySGFwdGljRmVlZGJhY2siLCJ1c2VIYXB0aWNGZWVkYmFjayIsIl9jcmVhdGVQcmVzc0FuaW1hdGlvbiIsImNyZWF0ZVByZXNzQW5pbWF0aW9uIiwicHJlc3NJbiIsInByZXNzT3V0IiwiaGFuZGxlUHJlc3NJbiIsInVzZUNhbGxiYWNrIiwic2hvdWxkUmVkdWNlTW90aW9uIiwiaGFuZGxlUHJlc3NPdXQiLCJoYW5kbGVQcmVzcyIsInVzZUVmZmVjdCIsImxvYWRpbmdBbmltYXRpb24iLCJjcmVhdGVMb2FkaW5nQW5pbWF0aW9uIiwic3RhcnQiLCJzdG9wIiwic2V0VmFsdWUiLCJzdWNjZXNzQW5pbWF0aW9uIiwicHVsc2UiLCJQbGF0Zm9ybSIsIk9TIiwiQWNjZXNzaWJpbGl0eUluZm8iLCJhbm5vdW5jZUZvckFjY2Vzc2liaWxpdHkiLCJlcnJvckFuaW1hdGlvbiIsInNoYWtlIiwiZ2V0QnV0dG9uU3R5bGVzIiwiX2NvbG9ycyRidXR0b24iLCJfY29sb3JzJHByaW1hcnkiLCJfY29sb3JzJGJ1dHRvbjIiLCJfY29sb3JzJHNlY29uZGFyeSIsIl9jb2xvcnMkYm9yZGVyIiwiX2NvbG9ycyRidXR0b24zIiwiX2NvbG9ycyRwcmltYXJ5MiIsImJhc2VTdHlsZXMiLCJzdHlsZXMiLCJidXR0b24iLCJwdXNoIiwiYmFja2dyb3VuZENvbG9yIiwicHJpbWFyeSIsInNlY29uZGFyeSIsImJvcmRlcldpZHRoIiwiYm9yZGVyQ29sb3IiLCJib3JkZXIiLCJsb2FkaW5nQnV0dG9uIiwiZ2V0VGV4dENvbG9yIiwiX2NvbG9ycyR0ZXh0IiwiX2NvbG9ycyR0ZXh0MiIsIl9jb2xvcnMkdGV4dDMiLCJfY29sb3JzJHByaW1hcnkzIiwiX2NvbG9ycyRwcmltYXJ5NCIsIl9jb2xvcnMkdGV4dDQiLCJ0ZXh0IiwiaW52ZXJzZSIsImdldEljb25TaXplIiwicmVuZGVyTG9hZGluZ0ljb24iLCJqc3giLCJWaWV3IiwiaWNvbkNvbnRhaW5lciIsInRyYW5zZm9ybSIsInJvdGF0ZSIsImludGVycG9sYXRlIiwiaW5wdXRSYW5nZSIsIm91dHB1dFJhbmdlIiwiY2hpbGRyZW4iLCJJb25pY29ucyIsIm5hbWUiLCJjb2xvciIsInJlbmRlckljb24iLCJpY29uTmFtZSIsImdldEJ1dHRvblRpdGxlIiwiVG91Y2hhYmxlT3BhY2l0eSIsIm9uUHJlc3NJbiIsIm9uUHJlc3NPdXQiLCJhY3RpdmVPcGFjaXR5IiwiYWNjZXNzaWJpbGl0eVJvbGUiLCJhY2Nlc3NpYmlsaXR5U3RhdGUiLCJidXN5IiwianN4cyIsInNjYWxlIiwidHJhbnNsYXRlWCIsIm9wYWNpdHkiLCJUeXBvZ3JhcGh5IiwiYnV0dG9uVGV4dCIsInRleHRXaXRoTGVmdEljb24iLCJ0ZXh0V2l0aFJpZ2h0SWNvbiIsIlN0eWxlU2hlZXQiLCJjcmVhdGUiLCJmbGV4RGlyZWN0aW9uIiwiYWxpZ25JdGVtcyIsImp1c3RpZnlDb250ZW50IiwiYm9yZGVyUmFkaXVzIiwicGFkZGluZ0hvcml6b250YWwiLCJwYWRkaW5nVmVydGljYWwiLCJzbWFsbEJ1dHRvbiIsIm1lZGl1bUJ1dHRvbiIsImxhcmdlQnV0dG9uIiwid2lkdGgiLCJ0ZXh0QWxpZ24iLCJtYXJnaW5MZWZ0IiwibWFyZ2luUmlnaHQiLCJfZGVmYXVsdCJdLCJzb3VyY2VzIjpbIkFuaW1hdGVkQnV0dG9uLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEFuaW1hdGVkIEJ1dHRvbiBDb21wb25lbnRcbiAqXG4gKiBFbmhhbmNlZCBidXR0b24gY29tcG9uZW50IHdpdGggc21vb3RoIGFuaW1hdGlvbnMgYW5kIG1pY3JvLWludGVyYWN0aW9uc1xuICogZm9yIGltcHJvdmVkIHVzZXIgZXhwZXJpZW5jZSBhbmQgdmlzdWFsIGZlZWRiYWNrLlxuICpcbiAqIEZlYXR1cmVzOlxuICogLSBQcmVzcyBhbmltYXRpb25zXG4gKiAtIExvYWRpbmcgc3RhdGVzXG4gKiAtIFN1Y2Nlc3MvRXJyb3IgZmVlZGJhY2tcbiAqIC0gQWNjZXNzaWJpbGl0eSBjb21wbGlhbmNlXG4gKiAtIEN1c3RvbWl6YWJsZSBhbmltYXRpb25zXG4gKlxuICogQHZlcnNpb24gMS4wLjBcbiAqIEBhdXRob3IgVmllcmxhIERldmVsb3BtZW50IFRlYW1cbiAqL1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlUmVmLCB1c2VDYWxsYmFjaywgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgVG91Y2hhYmxlT3BhY2l0eSwgVmlldyB9IGZyb20gJ3JlYWN0LW5hdGl2ZSc7XG5pbXBvcnQgeyBQbGF0Zm9ybSB9IGZyb20gJ3JlYWN0LW5hdGl2ZSc7XG5pbXBvcnQgeyBBbmltYXRlZCwgU3R5bGVTaGVldCwgQWNjZXNzaWJpbGl0eUluZm8sICB9IGZyb20gJ3JlYWN0LW5hdGl2ZSc7XG5pbXBvcnQgeyBJb25pY29ucyB9IGZyb20gJ0BleHBvL3ZlY3Rvci1pY29ucyc7XG5pbXBvcnQgeyB1c2VIaWdoQ29udHJhc3RDb2xvcnMgfSBmcm9tICcuLi8uLi9jb250ZXh0cy9IaWdoQ29udHJhc3RDb250ZXh0JztcbmltcG9ydCB7IHVzZVRvdWNoVGFyZ2V0U3R5bGVzLCB1c2VIYXB0aWNGZWVkYmFjayB9IGZyb20gJy4uLy4uL2NvbnRleHRzL01vdG9yQWNjZXNzaWJpbGl0eUNvbnRleHQnO1xuaW1wb3J0IHsgVHlwb2dyYXBoeSB9IGZyb20gJy4uL3R5cG9ncmFwaHkvVHlwb2dyYXBoeSc7XG5pbXBvcnQge1xuICBjcmVhdGVQcmVzc0FuaW1hdGlvbixcbiAgY3JlYXRlTG9hZGluZ0FuaW1hdGlvbixcbiAgcHVsc2UsXG4gIHNoYWtlLFxuICBzaG91bGRSZWR1Y2VNb3Rpb24sXG4gIEFOSU1BVElPTl9EVVJBVElPTlMsXG59IGZyb20gJy4uLy4uL3V0aWxzL2FuaW1hdGlvblV0aWxzJztcblxuLy8gQnV0dG9uIHN0YXRlc1xuZXhwb3J0IHR5cGUgQnV0dG9uU3RhdGUgPSAnaWRsZScgfCAnbG9hZGluZycgfCAnc3VjY2VzcycgfCAnZXJyb3InO1xuXG4vLyBDb21wb25lbnQgcHJvcHNcbmV4cG9ydCBpbnRlcmZhY2UgQW5pbWF0ZWRCdXR0b25Qcm9wcyB7XG4gIC8vIENvbnRlbnRcbiAgdGl0bGU6IHN0cmluZztcbiAgaWNvbj86IHN0cmluZztcbiAgaWNvblBvc2l0aW9uPzogJ2xlZnQnIHwgJ3JpZ2h0JztcbiAgXG4gIC8vIEJlaGF2aW9yXG4gIG9uUHJlc3M6ICgpID0+IHZvaWQ7XG4gIGRpc2FibGVkPzogYm9vbGVhbjtcbiAgc3RhdGU/OiBCdXR0b25TdGF0ZTtcbiAgXG4gIC8vIFN0eWxpbmdcbiAgdmFyaWFudD86ICdwcmltYXJ5JyB8ICdzZWNvbmRhcnknIHwgJ291dGxpbmUnIHwgJ2dob3N0JztcbiAgc2l6ZT86ICdzbWFsbCcgfCAnbWVkaXVtJyB8ICdsYXJnZSc7XG4gIGZ1bGxXaWR0aD86IGJvb2xlYW47XG4gIHN0eWxlPzogYW55O1xuICBcbiAgLy8gQW5pbWF0aW9uXG4gIGVuYWJsZUFuaW1hdGlvbnM/OiBib29sZWFuO1xuICBwcmVzc1NjYWxlPzogbnVtYmVyO1xuICBcbiAgLy8gQWNjZXNzaWJpbGl0eVxuICBhY2Nlc3NpYmlsaXR5TGFiZWw/OiBzdHJpbmc7XG4gIGFjY2Vzc2liaWxpdHlIaW50Pzogc3RyaW5nO1xuICBcbiAgLy8gVGVzdGluZ1xuICB0ZXN0SUQ/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBjb25zdCBBbmltYXRlZEJ1dHRvbjogUmVhY3QuRkM8QW5pbWF0ZWRCdXR0b25Qcm9wcz4gPSAoe1xuICB0aXRsZSxcbiAgaWNvbixcbiAgaWNvblBvc2l0aW9uID0gJ2xlZnQnLFxuICBvblByZXNzLFxuICBkaXNhYmxlZCA9IGZhbHNlLFxuICBzdGF0ZSA9ICdpZGxlJyxcbiAgdmFyaWFudCA9ICdwcmltYXJ5JyxcbiAgc2l6ZSA9ICdtZWRpdW0nLFxuICBmdWxsV2lkdGggPSBmYWxzZSxcbiAgc3R5bGUsXG4gIGVuYWJsZUFuaW1hdGlvbnMgPSB0cnVlLFxuICBwcmVzc1NjYWxlID0gMC45NSxcbiAgYWNjZXNzaWJpbGl0eUxhYmVsLFxuICBhY2Nlc3NpYmlsaXR5SGludCxcbiAgdGVzdElELFxufSkgPT4ge1xuICAvLyBBbmltYXRpb24gdmFsdWVzXG4gIGNvbnN0IHNjYWxlVmFsdWUgPSB1c2VSZWYobmV3IEFuaW1hdGVkLlZhbHVlKDEpKS5jdXJyZW50O1xuICBjb25zdCByb3RhdGlvblZhbHVlID0gdXNlUmVmKG5ldyBBbmltYXRlZC5WYWx1ZSgwKSkuY3VycmVudDtcbiAgY29uc3Qgb3BhY2l0eVZhbHVlID0gdXNlUmVmKG5ldyBBbmltYXRlZC5WYWx1ZSgxKSkuY3VycmVudDtcbiAgY29uc3Qgc2hha2VWYWx1ZSA9IHVzZVJlZihuZXcgQW5pbWF0ZWQuVmFsdWUoMCkpLmN1cnJlbnQ7XG5cbiAgLy8gSG9va3NcbiAgY29uc3QgeyBjb2xvcnMgfSA9IHVzZUhpZ2hDb250cmFzdENvbG9ycygpO1xuICBjb25zdCB0b3VjaFRhcmdldFN0eWxlcyA9IHVzZVRvdWNoVGFyZ2V0U3R5bGVzKCk7XG4gIGNvbnN0IHRyaWdnZXJIYXB0aWNGZWVkYmFjayA9IHVzZUhhcHRpY0ZlZWRiYWNrKCk7XG5cbiAgLy8gUHJlc3MgYW5pbWF0aW9uIGhhbmRsZXJzXG4gIGNvbnN0IHsgcHJlc3NJbiwgcHJlc3NPdXQgfSA9IGNyZWF0ZVByZXNzQW5pbWF0aW9uKHNjYWxlVmFsdWUsIHByZXNzU2NhbGUpO1xuXG4gIC8vIEhhbmRsZSBwcmVzcyBpblxuICBjb25zdCBoYW5kbGVQcmVzc0luID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIGlmICghZGlzYWJsZWQgJiYgZW5hYmxlQW5pbWF0aW9ucyAmJiAhc2hvdWxkUmVkdWNlTW90aW9uKCkpIHtcbiAgICAgIHByZXNzSW4oKTtcbiAgICB9XG4gICAgdHJpZ2dlckhhcHRpY0ZlZWRiYWNrKCdsaWdodCcpO1xuICB9LCBbZGlzYWJsZWQsIGVuYWJsZUFuaW1hdGlvbnMsIHByZXNzSW4sIHRyaWdnZXJIYXB0aWNGZWVkYmFja10pO1xuXG4gIC8vIEhhbmRsZSBwcmVzcyBvdXRcbiAgY29uc3QgaGFuZGxlUHJlc3NPdXQgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgaWYgKCFkaXNhYmxlZCAmJiBlbmFibGVBbmltYXRpb25zICYmICFzaG91bGRSZWR1Y2VNb3Rpb24oKSkge1xuICAgICAgcHJlc3NPdXQoKTtcbiAgICB9XG4gIH0sIFtkaXNhYmxlZCwgZW5hYmxlQW5pbWF0aW9ucywgcHJlc3NPdXRdKTtcblxuICAvLyBIYW5kbGUgcHJlc3NcbiAgY29uc3QgaGFuZGxlUHJlc3MgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgaWYgKCFkaXNhYmxlZCAmJiBzdGF0ZSAhPT0gJ2xvYWRpbmcnKSB7XG4gICAgICB0cmlnZ2VySGFwdGljRmVlZGJhY2soJ21lZGl1bScpO1xuICAgICAgb25QcmVzcygpO1xuICAgIH1cbiAgfSwgW2Rpc2FibGVkLCBzdGF0ZSwgb25QcmVzcywgdHJpZ2dlckhhcHRpY0ZlZWRiYWNrXSk7XG5cbiAgLy8gTG9hZGluZyBhbmltYXRpb25cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoc3RhdGUgPT09ICdsb2FkaW5nJyAmJiBlbmFibGVBbmltYXRpb25zICYmICFzaG91bGRSZWR1Y2VNb3Rpb24oKSkge1xuICAgICAgY29uc3QgbG9hZGluZ0FuaW1hdGlvbiA9IGNyZWF0ZUxvYWRpbmdBbmltYXRpb24ocm90YXRpb25WYWx1ZSk7XG4gICAgICBsb2FkaW5nQW5pbWF0aW9uLnN0YXJ0KCk7XG4gICAgICBcbiAgICAgIHJldHVybiAoKSA9PiBsb2FkaW5nQW5pbWF0aW9uLnN0b3AoKTtcbiAgICB9IGVsc2Uge1xuICAgICAgcm90YXRpb25WYWx1ZS5zZXRWYWx1ZSgwKTtcbiAgICB9XG4gIH0sIFtzdGF0ZSwgZW5hYmxlQW5pbWF0aW9ucywgcm90YXRpb25WYWx1ZV0pO1xuXG4gIC8vIFN1Y2Nlc3MgYW5pbWF0aW9uXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHN0YXRlID09PSAnc3VjY2VzcycgJiYgZW5hYmxlQW5pbWF0aW9ucyAmJiAhc2hvdWxkUmVkdWNlTW90aW9uKCkpIHtcbiAgICAgIGNvbnN0IHN1Y2Nlc3NBbmltYXRpb24gPSBwdWxzZShzY2FsZVZhbHVlLCAxLjEpO1xuICAgICAgc3VjY2Vzc0FuaW1hdGlvbi5zdGFydCgoKSA9PiB7XG4gICAgICAgIC8vIEFubm91bmNlIHN1Y2Nlc3MgdG8gc2NyZWVuIHJlYWRlcnNcbiAgICAgICAgaWYgKFBsYXRmb3JtLk9TID09PSAnaW9zJyB8fCBQbGF0Zm9ybS5PUyA9PT0gJ2FuZHJvaWQnKSB7XG4gICAgICAgICAgQWNjZXNzaWJpbGl0eUluZm8uYW5ub3VuY2VGb3JBY2Nlc3NpYmlsaXR5KCdBY3Rpb24gY29tcGxldGVkIHN1Y2Nlc3NmdWxseScpO1xuICAgICAgICB9XG4gICAgICB9KTtcbiAgICB9XG4gIH0sIFtzdGF0ZSwgZW5hYmxlQW5pbWF0aW9ucywgc2NhbGVWYWx1ZV0pO1xuXG4gIC8vIEVycm9yIGFuaW1hdGlvblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChzdGF0ZSA9PT0gJ2Vycm9yJyAmJiBlbmFibGVBbmltYXRpb25zICYmICFzaG91bGRSZWR1Y2VNb3Rpb24oKSkge1xuICAgICAgY29uc3QgZXJyb3JBbmltYXRpb24gPSBzaGFrZShzaGFrZVZhbHVlLCA4KTtcbiAgICAgIGVycm9yQW5pbWF0aW9uLnN0YXJ0KCgpID0+IHtcbiAgICAgICAgLy8gQW5ub3VuY2UgZXJyb3IgdG8gc2NyZWVuIHJlYWRlcnNcbiAgICAgICAgaWYgKFBsYXRmb3JtLk9TID09PSAnaW9zJyB8fCBQbGF0Zm9ybS5PUyA9PT0gJ2FuZHJvaWQnKSB7XG4gICAgICAgICAgQWNjZXNzaWJpbGl0eUluZm8uYW5ub3VuY2VGb3JBY2Nlc3NpYmlsaXR5KCdBY3Rpb24gZmFpbGVkLiBQbGVhc2UgdHJ5IGFnYWluLicpO1xuICAgICAgICB9XG4gICAgICB9KTtcbiAgICB9XG4gIH0sIFtzdGF0ZSwgZW5hYmxlQW5pbWF0aW9ucywgc2hha2VWYWx1ZV0pO1xuXG4gIC8vIEdldCBidXR0b24gc3R5bGVzIGJhc2VkIG9uIHZhcmlhbnQgYW5kIHN0YXRlXG4gIGNvbnN0IGdldEJ1dHRvblN0eWxlcyA9ICgpID0+IHtcbiAgICBjb25zdCBiYXNlU3R5bGVzID0gW3N0eWxlcy5idXR0b24sIHN0eWxlc1tgJHtzaXplfUJ1dHRvbmBdXTtcbiAgICBcbiAgICBpZiAoZnVsbFdpZHRoKSB7XG4gICAgICBiYXNlU3R5bGVzLnB1c2goc3R5bGVzLmZ1bGxXaWR0aCk7XG4gICAgfVxuICAgIFxuICAgIC8vIFZhcmlhbnQgc3R5bGVzXG4gICAgc3dpdGNoICh2YXJpYW50KSB7XG4gICAgICBjYXNlICdwcmltYXJ5JzpcbiAgICAgICAgYmFzZVN0eWxlcy5wdXNoKHtcbiAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IGRpc2FibGVkID8gY29sb3JzPy5idXR0b24/LmRpc2FibGVkIHx8ICcjQ0NDJyA6IGNvbG9ycz8ucHJpbWFyeT8uZGVmYXVsdCB8fCAnIzVBN0E2MycsXG4gICAgICAgIH0pO1xuICAgICAgICBicmVhaztcbiAgICAgIGNhc2UgJ3NlY29uZGFyeSc6XG4gICAgICAgIGJhc2VTdHlsZXMucHVzaCh7XG4gICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBkaXNhYmxlZCA/IGNvbG9ycz8uYnV0dG9uPy5kaXNhYmxlZCB8fCAnI0NDQycgOiBjb2xvcnM/LnNlY29uZGFyeT8uZGVmYXVsdCB8fCAnI0YwRjBGMCcsXG4gICAgICAgICAgYm9yZGVyV2lkdGg6IDEsXG4gICAgICAgICAgYm9yZGVyQ29sb3I6IGNvbG9ycz8uYm9yZGVyPy5wcmltYXJ5IHx8ICcjREREJyxcbiAgICAgICAgfSk7XG4gICAgICAgIGJyZWFrO1xuICAgICAgY2FzZSAnb3V0bGluZSc6XG4gICAgICAgIGJhc2VTdHlsZXMucHVzaCh7XG4gICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAndHJhbnNwYXJlbnQnLFxuICAgICAgICAgIGJvcmRlcldpZHRoOiAyLFxuICAgICAgICAgIGJvcmRlckNvbG9yOiBkaXNhYmxlZCA/IGNvbG9ycz8uYnV0dG9uPy5kaXNhYmxlZCB8fCAnI0NDQycgOiBjb2xvcnM/LnByaW1hcnk/LmRlZmF1bHQgfHwgJyM1QTdBNjMnLFxuICAgICAgICB9KTtcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlICdnaG9zdCc6XG4gICAgICAgIGJhc2VTdHlsZXMucHVzaCh7XG4gICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAndHJhbnNwYXJlbnQnLFxuICAgICAgICB9KTtcbiAgICAgICAgYnJlYWs7XG4gICAgfVxuICAgIFxuICAgIC8vIFN0YXRlLXNwZWNpZmljIHN0eWxlc1xuICAgIGlmIChzdGF0ZSA9PT0gJ2xvYWRpbmcnKSB7XG4gICAgICBiYXNlU3R5bGVzLnB1c2goc3R5bGVzLmxvYWRpbmdCdXR0b24pO1xuICAgIH1cbiAgICBcbiAgICByZXR1cm4gYmFzZVN0eWxlcztcbiAgfTtcblxuICAvLyBHZXQgdGV4dCBjb2xvclxuICBjb25zdCBnZXRUZXh0Q29sb3IgPSAoKSA9PiB7XG4gICAgaWYgKGRpc2FibGVkKSByZXR1cm4gY29sb3JzPy50ZXh0Py5kaXNhYmxlZCB8fCAnIzk5OSc7XG4gICAgXG4gICAgc3dpdGNoICh2YXJpYW50KSB7XG4gICAgICBjYXNlICdwcmltYXJ5JzpcbiAgICAgICAgcmV0dXJuIGNvbG9ycz8udGV4dD8uaW52ZXJzZSB8fCAnI0ZGRkZGRic7XG4gICAgICBjYXNlICdzZWNvbmRhcnknOlxuICAgICAgICByZXR1cm4gY29sb3JzPy50ZXh0Py5wcmltYXJ5IHx8ICcjMzMzJztcbiAgICAgIGNhc2UgJ291dGxpbmUnOlxuICAgICAgICByZXR1cm4gY29sb3JzPy5wcmltYXJ5Py5kZWZhdWx0IHx8ICcjNUE3QTYzJztcbiAgICAgIGNhc2UgJ2dob3N0JzpcbiAgICAgICAgcmV0dXJuIGNvbG9ycz8ucHJpbWFyeT8uZGVmYXVsdCB8fCAnIzVBN0E2Myc7XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gY29sb3JzPy50ZXh0Py5wcmltYXJ5IHx8ICcjMzMzJztcbiAgICB9XG4gIH07XG5cbiAgLy8gR2V0IGljb24gc2l6ZSBiYXNlZCBvbiBidXR0b24gc2l6ZVxuICBjb25zdCBnZXRJY29uU2l6ZSA9ICgpID0+IHtcbiAgICBzd2l0Y2ggKHNpemUpIHtcbiAgICAgIGNhc2UgJ3NtYWxsJzpcbiAgICAgICAgcmV0dXJuIDE2O1xuICAgICAgY2FzZSAnbGFyZ2UnOlxuICAgICAgICByZXR1cm4gMjQ7XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gMjA7XG4gICAgfVxuICB9O1xuXG4gIC8vIFJlbmRlciBsb2FkaW5nIGljb25cbiAgY29uc3QgcmVuZGVyTG9hZGluZ0ljb24gPSAoKSA9PiB7XG4gICAgaWYgKHN0YXRlICE9PSAnbG9hZGluZycpIHJldHVybiBudWxsO1xuICAgIFxuICAgIHJldHVybiAoXG4gICAgICA8QW5pbWF0ZWQuVmlld1xuICAgICAgICBzdHlsZT17W1xuICAgICAgICAgIHN0eWxlcy5pY29uQ29udGFpbmVyLFxuICAgICAgICAgIHtcbiAgICAgICAgICAgIHRyYW5zZm9ybTogW1xuICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgcm90YXRlOiByb3RhdGlvblZhbHVlLmludGVycG9sYXRlKHtcbiAgICAgICAgICAgICAgICAgIGlucHV0UmFuZ2U6IFswLCAxXSxcbiAgICAgICAgICAgICAgICAgIG91dHB1dFJhbmdlOiBbJzBkZWcnLCAnMzYwZGVnJ10sXG4gICAgICAgICAgICAgICAgfSksXG4gICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBdLFxuICAgICAgICAgIH0sXG4gICAgICAgIF19XG4gICAgICA+XG4gICAgICAgIDxJb25pY29uc1xuICAgICAgICAgIG5hbWU9XCJyZWZyZXNoXCJcbiAgICAgICAgICBzaXplPXtnZXRJY29uU2l6ZSgpfVxuICAgICAgICAgIGNvbG9yPXtnZXRUZXh0Q29sb3IoKX1cbiAgICAgICAgLz5cbiAgICAgIDwvQW5pbWF0ZWQuVmlldz5cbiAgICApO1xuICB9O1xuXG4gIC8vIFJlbmRlciBpY29uXG4gIGNvbnN0IHJlbmRlckljb24gPSAoKSA9PiB7XG4gICAgaWYgKHN0YXRlID09PSAnbG9hZGluZycpIHJldHVybiByZW5kZXJMb2FkaW5nSWNvbigpO1xuICAgIGlmICghaWNvbikgcmV0dXJuIG51bGw7XG4gICAgXG4gICAgbGV0IGljb25OYW1lID0gaWNvbjtcbiAgICBpZiAoc3RhdGUgPT09ICdzdWNjZXNzJykgaWNvbk5hbWUgPSAnY2hlY2ttYXJrJztcbiAgICBpZiAoc3RhdGUgPT09ICdlcnJvcicpIGljb25OYW1lID0gJ2Nsb3NlJztcbiAgICBcbiAgICByZXR1cm4gKFxuICAgICAgPFZpZXcgc3R5bGU9e3N0eWxlcy5pY29uQ29udGFpbmVyfT5cbiAgICAgICAgPElvbmljb25zXG4gICAgICAgICAgbmFtZT17aWNvbk5hbWUgYXMgYW55fVxuICAgICAgICAgIHNpemU9e2dldEljb25TaXplKCl9XG4gICAgICAgICAgY29sb3I9e2dldFRleHRDb2xvcigpfVxuICAgICAgICAvPlxuICAgICAgPC9WaWV3PlxuICAgICk7XG4gIH07XG5cbiAgLy8gR2V0IGJ1dHRvbiBjb250ZW50XG4gIGNvbnN0IGdldEJ1dHRvblRpdGxlID0gKCkgPT4ge1xuICAgIHN3aXRjaCAoc3RhdGUpIHtcbiAgICAgIGNhc2UgJ2xvYWRpbmcnOlxuICAgICAgICByZXR1cm4gJ0xvYWRpbmcuLi4nO1xuICAgICAgY2FzZSAnc3VjY2Vzcyc6XG4gICAgICAgIHJldHVybiAnU3VjY2VzcyEnO1xuICAgICAgY2FzZSAnZXJyb3InOlxuICAgICAgICByZXR1cm4gJ1RyeSBBZ2Fpbic7XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gdGl0bGU7XG4gICAgfVxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPFRvdWNoYWJsZU9wYWNpdHlcbiAgICAgIHN0eWxlPXtbdG91Y2hUYXJnZXRTdHlsZXMsIHN0eWxlXX1cbiAgICAgIG9uUHJlc3M9e2hhbmRsZVByZXNzfVxuICAgICAgb25QcmVzc0luPXtoYW5kbGVQcmVzc0lufVxuICAgICAgb25QcmVzc091dD17aGFuZGxlUHJlc3NPdXR9XG4gICAgICBkaXNhYmxlZD17ZGlzYWJsZWQgfHwgc3RhdGUgPT09ICdsb2FkaW5nJ31cbiAgICAgIGFjdGl2ZU9wYWNpdHk9ezAuOH1cbiAgICAgIGFjY2Vzc2liaWxpdHlSb2xlPVwiYnV0dG9uXCJcbiAgICAgIGFjY2Vzc2liaWxpdHlMYWJlbD17YWNjZXNzaWJpbGl0eUxhYmVsIHx8IHRpdGxlfVxuICAgICAgYWNjZXNzaWJpbGl0eUhpbnQ9e2FjY2Vzc2liaWxpdHlIaW50fVxuICAgICAgYWNjZXNzaWJpbGl0eVN0YXRlPXt7XG4gICAgICAgIGRpc2FibGVkOiBkaXNhYmxlZCB8fCBzdGF0ZSA9PT0gJ2xvYWRpbmcnLFxuICAgICAgICBidXN5OiBzdGF0ZSA9PT0gJ2xvYWRpbmcnLFxuICAgICAgfX1cbiAgICAgIHRlc3RJRD17dGVzdElEfVxuICAgID5cbiAgICAgIDxBbmltYXRlZC5WaWV3XG4gICAgICAgIHN0eWxlPXtbXG4gICAgICAgICAgZ2V0QnV0dG9uU3R5bGVzKCksXG4gICAgICAgICAge1xuICAgICAgICAgICAgdHJhbnNmb3JtOiBbXG4gICAgICAgICAgICAgIHsgc2NhbGU6IHNjYWxlVmFsdWUgfSxcbiAgICAgICAgICAgICAgeyB0cmFuc2xhdGVYOiBzaGFrZVZhbHVlIH0sXG4gICAgICAgICAgICBdLFxuICAgICAgICAgICAgb3BhY2l0eTogb3BhY2l0eVZhbHVlLFxuICAgICAgICAgIH0sXG4gICAgICAgIF19XG4gICAgICA+XG4gICAgICAgIHtpY29uUG9zaXRpb24gPT09ICdsZWZ0JyAmJiByZW5kZXJJY29uKCl9XG4gICAgICAgIFxuICAgICAgICA8VHlwb2dyYXBoeVxuICAgICAgICAgIHZhcmlhbnQ9XCJidXR0b25cIlxuICAgICAgICAgIGNvbG9yPXtnZXRUZXh0Q29sb3IoKX1cbiAgICAgICAgICBzdHlsZT17W1xuICAgICAgICAgICAgc3R5bGVzLmJ1dHRvblRleHQsXG4gICAgICAgICAgICBpY29uICYmIGljb25Qb3NpdGlvbiA9PT0gJ2xlZnQnICYmIHN0eWxlcy50ZXh0V2l0aExlZnRJY29uLFxuICAgICAgICAgICAgaWNvbiAmJiBpY29uUG9zaXRpb24gPT09ICdyaWdodCcgJiYgc3R5bGVzLnRleHRXaXRoUmlnaHRJY29uLFxuICAgICAgICAgIF19XG4gICAgICAgID5cbiAgICAgICAgICB7Z2V0QnV0dG9uVGl0bGUoKX1cbiAgICAgICAgPC9UeXBvZ3JhcGh5PlxuICAgICAgICBcbiAgICAgICAge2ljb25Qb3NpdGlvbiA9PT0gJ3JpZ2h0JyAmJiByZW5kZXJJY29uKCl9XG4gICAgICA8L0FuaW1hdGVkLlZpZXc+XG4gICAgPC9Ub3VjaGFibGVPcGFjaXR5PlxuICApO1xufTtcblxuY29uc3Qgc3R5bGVzID0gU3R5bGVTaGVldC5jcmVhdGUoe1xuICBidXR0b246IHtcbiAgICBmbGV4RGlyZWN0aW9uOiAncm93JyxcbiAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXG4gICAgYm9yZGVyUmFkaXVzOiA4LFxuICAgIHBhZGRpbmdIb3Jpem9udGFsOiAxNixcbiAgICBwYWRkaW5nVmVydGljYWw6IDEyLFxuICB9LFxuICBzbWFsbEJ1dHRvbjoge1xuICAgIHBhZGRpbmdIb3Jpem9udGFsOiAxMixcbiAgICBwYWRkaW5nVmVydGljYWw6IDgsXG4gICAgYm9yZGVyUmFkaXVzOiA2LFxuICB9LFxuICBtZWRpdW1CdXR0b246IHtcbiAgICBwYWRkaW5nSG9yaXpvbnRhbDogMTYsXG4gICAgcGFkZGluZ1ZlcnRpY2FsOiAxMixcbiAgICBib3JkZXJSYWRpdXM6IDgsXG4gIH0sXG4gIGxhcmdlQnV0dG9uOiB7XG4gICAgcGFkZGluZ0hvcml6b250YWw6IDIwLFxuICAgIHBhZGRpbmdWZXJ0aWNhbDogMTYsXG4gICAgYm9yZGVyUmFkaXVzOiAxMCxcbiAgfSxcbiAgZnVsbFdpZHRoOiB7XG4gICAgd2lkdGg6ICcxMDAlJyxcbiAgfSxcbiAgbG9hZGluZ0J1dHRvbjoge1xuICAgIG9wYWNpdHk6IDAuOCxcbiAgfSxcbiAgYnV0dG9uVGV4dDoge1xuICAgIHRleHRBbGlnbjogJ2NlbnRlcicsXG4gIH0sXG4gIHRleHRXaXRoTGVmdEljb246IHtcbiAgICBtYXJnaW5MZWZ0OiA4LFxuICB9LFxuICB0ZXh0V2l0aFJpZ2h0SWNvbjoge1xuICAgIG1hcmdpblJpZ2h0OiA4LFxuICB9LFxuICBpY29uQ29udGFpbmVyOiB7XG4gICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICB9LFxufSk7XG5cbmV4cG9ydCBkZWZhdWx0IEFuaW1hdGVkQnV0dG9uO1xuIl0sIm1hcHBpbmdzIjoiOzs7O0FBaUJBLElBQUFBLE1BQUEsR0FBQUMsdUJBQUEsQ0FBQUMsT0FBQTtBQUNBLElBQUFDLFlBQUEsR0FBQUQsT0FBQTtBQUdBLElBQUFFLFlBQUEsR0FBQUYsT0FBQTtBQUNBLElBQUFHLG9CQUFBLEdBQUFILE9BQUE7QUFDQSxJQUFBSSwwQkFBQSxHQUFBSixPQUFBO0FBQ0EsSUFBQUssV0FBQSxHQUFBTCxPQUFBO0FBQ0EsSUFBQU0sZUFBQSxHQUFBTixPQUFBO0FBT29DLElBQUFPLFdBQUEsR0FBQVAsT0FBQTtBQUFBLFNBQUFELHdCQUFBUyxDQUFBLEVBQUFDLENBQUEsNkJBQUFDLE9BQUEsTUFBQUMsQ0FBQSxPQUFBRCxPQUFBLElBQUFFLENBQUEsT0FBQUYsT0FBQSxZQUFBWCx1QkFBQSxZQUFBQSx3QkFBQVMsQ0FBQSxFQUFBQyxDQUFBLFNBQUFBLENBQUEsSUFBQUQsQ0FBQSxJQUFBQSxDQUFBLENBQUFLLFVBQUEsU0FBQUwsQ0FBQSxNQUFBTSxDQUFBLEVBQUFDLENBQUEsRUFBQUMsQ0FBQSxLQUFBQyxTQUFBLFFBQUFDLE9BQUEsRUFBQVYsQ0FBQSxpQkFBQUEsQ0FBQSx1QkFBQUEsQ0FBQSx5QkFBQUEsQ0FBQSxTQUFBUSxDQUFBLE1BQUFGLENBQUEsR0FBQUwsQ0FBQSxHQUFBRyxDQUFBLEdBQUFELENBQUEsUUFBQUcsQ0FBQSxDQUFBSyxHQUFBLENBQUFYLENBQUEsVUFBQU0sQ0FBQSxDQUFBTSxHQUFBLENBQUFaLENBQUEsR0FBQU0sQ0FBQSxDQUFBTyxHQUFBLENBQUFiLENBQUEsRUFBQVEsQ0FBQSxjQUFBTSxFQUFBLElBQUFkLENBQUEsZ0JBQUFjLEVBQUEsT0FBQUMsY0FBQSxDQUFBQyxJQUFBLENBQUFoQixDQUFBLEVBQUFjLEVBQUEsT0FBQVAsQ0FBQSxJQUFBRCxDQUFBLEdBQUFXLE1BQUEsQ0FBQUMsY0FBQSxLQUFBRCxNQUFBLENBQUFFLHdCQUFBLENBQUFuQixDQUFBLEVBQUFjLEVBQUEsT0FBQVAsQ0FBQSxDQUFBSyxHQUFBLElBQUFMLENBQUEsQ0FBQU0sR0FBQSxJQUFBUCxDQUFBLENBQUFFLENBQUEsRUFBQU0sRUFBQSxFQUFBUCxDQUFBLElBQUFDLENBQUEsQ0FBQU0sRUFBQSxJQUFBZCxDQUFBLENBQUFjLEVBQUEsV0FBQU4sQ0FBQSxLQUFBUixDQUFBLEVBQUFDLENBQUE7QUFtQzdCLElBQU1tQixjQUE2QyxHQUFBQyxPQUFBLENBQUFELGNBQUEsR0FBRyxTQUFoREEsY0FBNkNBLENBQUFFLElBQUEsRUFnQnBEO0VBQUEsSUFmSkMsS0FBSyxHQUFBRCxJQUFBLENBQUxDLEtBQUs7SUFDTEMsSUFBSSxHQUFBRixJQUFBLENBQUpFLElBQUk7SUFBQUMsaUJBQUEsR0FBQUgsSUFBQSxDQUNKSSxZQUFZO0lBQVpBLFlBQVksR0FBQUQsaUJBQUEsY0FBRyxNQUFNLEdBQUFBLGlCQUFBO0lBQ3JCRSxPQUFPLEdBQUFMLElBQUEsQ0FBUEssT0FBTztJQUFBQyxhQUFBLEdBQUFOLElBQUEsQ0FDUE8sUUFBUTtJQUFSQSxRQUFRLEdBQUFELGFBQUEsY0FBRyxLQUFLLEdBQUFBLGFBQUE7SUFBQUUsVUFBQSxHQUFBUixJQUFBLENBQ2hCUyxLQUFLO0lBQUxBLEtBQUssR0FBQUQsVUFBQSxjQUFHLE1BQU0sR0FBQUEsVUFBQTtJQUFBRSxZQUFBLEdBQUFWLElBQUEsQ0FDZFcsT0FBTztJQUFQQSxPQUFPLEdBQUFELFlBQUEsY0FBRyxTQUFTLEdBQUFBLFlBQUE7SUFBQUUsU0FBQSxHQUFBWixJQUFBLENBQ25CYSxJQUFJO0lBQUpBLElBQUksR0FBQUQsU0FBQSxjQUFHLFFBQVEsR0FBQUEsU0FBQTtJQUFBRSxjQUFBLEdBQUFkLElBQUEsQ0FDZmUsU0FBUztJQUFUQSxTQUFTLEdBQUFELGNBQUEsY0FBRyxLQUFLLEdBQUFBLGNBQUE7SUFDakJFLEtBQUssR0FBQWhCLElBQUEsQ0FBTGdCLEtBQUs7SUFBQUMscUJBQUEsR0FBQWpCLElBQUEsQ0FDTGtCLGdCQUFnQjtJQUFoQkEsZ0JBQWdCLEdBQUFELHFCQUFBLGNBQUcsSUFBSSxHQUFBQSxxQkFBQTtJQUFBRSxlQUFBLEdBQUFuQixJQUFBLENBQ3ZCb0IsVUFBVTtJQUFWQSxVQUFVLEdBQUFELGVBQUEsY0FBRyxJQUFJLEdBQUFBLGVBQUE7SUFDakJFLGtCQUFrQixHQUFBckIsSUFBQSxDQUFsQnFCLGtCQUFrQjtJQUNsQkMsaUJBQWlCLEdBQUF0QixJQUFBLENBQWpCc0IsaUJBQWlCO0lBQ2pCQyxNQUFNLEdBQUF2QixJQUFBLENBQU51QixNQUFNO0VBR04sSUFBTUMsVUFBVSxHQUFHLElBQUFDLGFBQU0sRUFBQyxJQUFJQyxxQkFBUSxDQUFDQyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQ0MsT0FBTztFQUN4RCxJQUFNQyxhQUFhLEdBQUcsSUFBQUosYUFBTSxFQUFDLElBQUlDLHFCQUFRLENBQUNDLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDQyxPQUFPO0VBQzNELElBQU1FLFlBQVksR0FBRyxJQUFBTCxhQUFNLEVBQUMsSUFBSUMscUJBQVEsQ0FBQ0MsS0FBSyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUNDLE9BQU87RUFDMUQsSUFBTUcsVUFBVSxHQUFHLElBQUFOLGFBQU0sRUFBQyxJQUFJQyxxQkFBUSxDQUFDQyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQ0MsT0FBTztFQUd4RCxJQUFBSSxxQkFBQSxHQUFtQixJQUFBQywwQ0FBcUIsRUFBQyxDQUFDO0lBQWxDQyxNQUFNLEdBQUFGLHFCQUFBLENBQU5FLE1BQU07RUFDZCxJQUFNQyxpQkFBaUIsR0FBRyxJQUFBQywrQ0FBb0IsRUFBQyxDQUFDO0VBQ2hELElBQU1DLHFCQUFxQixHQUFHLElBQUFDLDRDQUFpQixFQUFDLENBQUM7RUFHakQsSUFBQUMscUJBQUEsR0FBOEIsSUFBQUMsb0NBQW9CLEVBQUNoQixVQUFVLEVBQUVKLFVBQVUsQ0FBQztJQUFsRXFCLE9BQU8sR0FBQUYscUJBQUEsQ0FBUEUsT0FBTztJQUFFQyxRQUFRLEdBQUFILHFCQUFBLENBQVJHLFFBQVE7RUFHekIsSUFBTUMsYUFBYSxHQUFHLElBQUFDLGtCQUFXLEVBQUMsWUFBTTtJQUN0QyxJQUFJLENBQUNyQyxRQUFRLElBQUlXLGdCQUFnQixJQUFJLENBQUMsSUFBQTJCLGtDQUFrQixFQUFDLENBQUMsRUFBRTtNQUMxREosT0FBTyxDQUFDLENBQUM7SUFDWDtJQUNBSixxQkFBcUIsQ0FBQyxPQUFPLENBQUM7RUFDaEMsQ0FBQyxFQUFFLENBQUM5QixRQUFRLEVBQUVXLGdCQUFnQixFQUFFdUIsT0FBTyxFQUFFSixxQkFBcUIsQ0FBQyxDQUFDO0VBR2hFLElBQU1TLGNBQWMsR0FBRyxJQUFBRixrQkFBVyxFQUFDLFlBQU07SUFDdkMsSUFBSSxDQUFDckMsUUFBUSxJQUFJVyxnQkFBZ0IsSUFBSSxDQUFDLElBQUEyQixrQ0FBa0IsRUFBQyxDQUFDLEVBQUU7TUFDMURILFFBQVEsQ0FBQyxDQUFDO0lBQ1o7RUFDRixDQUFDLEVBQUUsQ0FBQ25DLFFBQVEsRUFBRVcsZ0JBQWdCLEVBQUV3QixRQUFRLENBQUMsQ0FBQztFQUcxQyxJQUFNSyxXQUFXLEdBQUcsSUFBQUgsa0JBQVcsRUFBQyxZQUFNO0lBQ3BDLElBQUksQ0FBQ3JDLFFBQVEsSUFBSUUsS0FBSyxLQUFLLFNBQVMsRUFBRTtNQUNwQzRCLHFCQUFxQixDQUFDLFFBQVEsQ0FBQztNQUMvQmhDLE9BQU8sQ0FBQyxDQUFDO0lBQ1g7RUFDRixDQUFDLEVBQUUsQ0FBQ0UsUUFBUSxFQUFFRSxLQUFLLEVBQUVKLE9BQU8sRUFBRWdDLHFCQUFxQixDQUFDLENBQUM7RUFHckQsSUFBQVcsZ0JBQVMsRUFBQyxZQUFNO0lBQ2QsSUFBSXZDLEtBQUssS0FBSyxTQUFTLElBQUlTLGdCQUFnQixJQUFJLENBQUMsSUFBQTJCLGtDQUFrQixFQUFDLENBQUMsRUFBRTtNQUNwRSxJQUFNSSxnQkFBZ0IsR0FBRyxJQUFBQyxzQ0FBc0IsRUFBQ3JCLGFBQWEsQ0FBQztNQUM5RG9CLGdCQUFnQixDQUFDRSxLQUFLLENBQUMsQ0FBQztNQUV4QixPQUFPO1FBQUEsT0FBTUYsZ0JBQWdCLENBQUNHLElBQUksQ0FBQyxDQUFDO01BQUE7SUFDdEMsQ0FBQyxNQUFNO01BQ0x2QixhQUFhLENBQUN3QixRQUFRLENBQUMsQ0FBQyxDQUFDO0lBQzNCO0VBQ0YsQ0FBQyxFQUFFLENBQUM1QyxLQUFLLEVBQUVTLGdCQUFnQixFQUFFVyxhQUFhLENBQUMsQ0FBQztFQUc1QyxJQUFBbUIsZ0JBQVMsRUFBQyxZQUFNO0lBQ2QsSUFBSXZDLEtBQUssS0FBSyxTQUFTLElBQUlTLGdCQUFnQixJQUFJLENBQUMsSUFBQTJCLGtDQUFrQixFQUFDLENBQUMsRUFBRTtNQUNwRSxJQUFNUyxnQkFBZ0IsR0FBRyxJQUFBQyxxQkFBSyxFQUFDL0IsVUFBVSxFQUFFLEdBQUcsQ0FBQztNQUMvQzhCLGdCQUFnQixDQUFDSCxLQUFLLENBQUMsWUFBTTtRQUUzQixJQUFJSyxxQkFBUSxDQUFDQyxFQUFFLEtBQUssS0FBSyxJQUFJRCxxQkFBUSxDQUFDQyxFQUFFLEtBQUssU0FBUyxFQUFFO1VBQ3REQyw4QkFBaUIsQ0FBQ0Msd0JBQXdCLENBQUMsK0JBQStCLENBQUM7UUFDN0U7TUFDRixDQUFDLENBQUM7SUFDSjtFQUNGLENBQUMsRUFBRSxDQUFDbEQsS0FBSyxFQUFFUyxnQkFBZ0IsRUFBRU0sVUFBVSxDQUFDLENBQUM7RUFHekMsSUFBQXdCLGdCQUFTLEVBQUMsWUFBTTtJQUNkLElBQUl2QyxLQUFLLEtBQUssT0FBTyxJQUFJUyxnQkFBZ0IsSUFBSSxDQUFDLElBQUEyQixrQ0FBa0IsRUFBQyxDQUFDLEVBQUU7TUFDbEUsSUFBTWUsY0FBYyxHQUFHLElBQUFDLHFCQUFLLEVBQUM5QixVQUFVLEVBQUUsQ0FBQyxDQUFDO01BQzNDNkIsY0FBYyxDQUFDVCxLQUFLLENBQUMsWUFBTTtRQUV6QixJQUFJSyxxQkFBUSxDQUFDQyxFQUFFLEtBQUssS0FBSyxJQUFJRCxxQkFBUSxDQUFDQyxFQUFFLEtBQUssU0FBUyxFQUFFO1VBQ3REQyw4QkFBaUIsQ0FBQ0Msd0JBQXdCLENBQUMsa0NBQWtDLENBQUM7UUFDaEY7TUFDRixDQUFDLENBQUM7SUFDSjtFQUNGLENBQUMsRUFBRSxDQUFDbEQsS0FBSyxFQUFFUyxnQkFBZ0IsRUFBRWEsVUFBVSxDQUFDLENBQUM7RUFHekMsSUFBTStCLGVBQWUsR0FBRyxTQUFsQkEsZUFBZUEsQ0FBQSxFQUFTO0lBQUEsSUFBQUMsY0FBQSxFQUFBQyxlQUFBLEVBQUFDLGVBQUEsRUFBQUMsaUJBQUEsRUFBQUMsY0FBQSxFQUFBQyxlQUFBLEVBQUFDLGdCQUFBO0lBQzVCLElBQU1DLFVBQVUsR0FBRyxDQUFDQyxNQUFNLENBQUNDLE1BQU0sRUFBRUQsTUFBTSxDQUFDLEdBQUcxRCxJQUFJLFFBQVEsQ0FBQyxDQUFDO0lBRTNELElBQUlFLFNBQVMsRUFBRTtNQUNidUQsVUFBVSxDQUFDRyxJQUFJLENBQUNGLE1BQU0sQ0FBQ3hELFNBQVMsQ0FBQztJQUNuQztJQUdBLFFBQVFKLE9BQU87TUFDYixLQUFLLFNBQVM7UUFDWjJELFVBQVUsQ0FBQ0csSUFBSSxDQUFDO1VBQ2RDLGVBQWUsRUFBRW5FLFFBQVEsR0FBRyxDQUFBMkIsTUFBTSxhQUFBNkIsY0FBQSxHQUFON0IsTUFBTSxDQUFFc0MsTUFBTSxxQkFBZFQsY0FBQSxDQUFnQnhELFFBQVEsS0FBSSxNQUFNLEdBQUcsQ0FBQTJCLE1BQU0sYUFBQThCLGVBQUEsR0FBTjlCLE1BQU0sQ0FBRXlDLE9BQU8scUJBQWZYLGVBQUEsQ0FBaUI1RSxPQUFPLEtBQUk7UUFDL0YsQ0FBQyxDQUFDO1FBQ0Y7TUFDRixLQUFLLFdBQVc7UUFDZGtGLFVBQVUsQ0FBQ0csSUFBSSxDQUFDO1VBQ2RDLGVBQWUsRUFBRW5FLFFBQVEsR0FBRyxDQUFBMkIsTUFBTSxhQUFBK0IsZUFBQSxHQUFOL0IsTUFBTSxDQUFFc0MsTUFBTSxxQkFBZFAsZUFBQSxDQUFnQjFELFFBQVEsS0FBSSxNQUFNLEdBQUcsQ0FBQTJCLE1BQU0sYUFBQWdDLGlCQUFBLEdBQU5oQyxNQUFNLENBQUUwQyxTQUFTLHFCQUFqQlYsaUJBQUEsQ0FBbUI5RSxPQUFPLEtBQUksU0FBUztVQUN4R3lGLFdBQVcsRUFBRSxDQUFDO1VBQ2RDLFdBQVcsRUFBRSxDQUFBNUMsTUFBTSxhQUFBaUMsY0FBQSxHQUFOakMsTUFBTSxDQUFFNkMsTUFBTSxxQkFBZFosY0FBQSxDQUFnQlEsT0FBTyxLQUFJO1FBQzFDLENBQUMsQ0FBQztRQUNGO01BQ0YsS0FBSyxTQUFTO1FBQ1pMLFVBQVUsQ0FBQ0csSUFBSSxDQUFDO1VBQ2RDLGVBQWUsRUFBRSxhQUFhO1VBQzlCRyxXQUFXLEVBQUUsQ0FBQztVQUNkQyxXQUFXLEVBQUV2RSxRQUFRLEdBQUcsQ0FBQTJCLE1BQU0sYUFBQWtDLGVBQUEsR0FBTmxDLE1BQU0sQ0FBRXNDLE1BQU0scUJBQWRKLGVBQUEsQ0FBZ0I3RCxRQUFRLEtBQUksTUFBTSxHQUFHLENBQUEyQixNQUFNLGFBQUFtQyxnQkFBQSxHQUFObkMsTUFBTSxDQUFFeUMsT0FBTyxxQkFBZk4sZ0JBQUEsQ0FBaUJqRixPQUFPLEtBQUk7UUFDM0YsQ0FBQyxDQUFDO1FBQ0Y7TUFDRixLQUFLLE9BQU87UUFDVmtGLFVBQVUsQ0FBQ0csSUFBSSxDQUFDO1VBQ2RDLGVBQWUsRUFBRTtRQUNuQixDQUFDLENBQUM7UUFDRjtJQUNKO0lBR0EsSUFBSWpFLEtBQUssS0FBSyxTQUFTLEVBQUU7TUFDdkI2RCxVQUFVLENBQUNHLElBQUksQ0FBQ0YsTUFBTSxDQUFDUyxhQUFhLENBQUM7SUFDdkM7SUFFQSxPQUFPVixVQUFVO0VBQ25CLENBQUM7RUFHRCxJQUFNVyxZQUFZLEdBQUcsU0FBZkEsWUFBWUEsQ0FBQSxFQUFTO0lBQUEsSUFBQUMsWUFBQSxFQUFBQyxhQUFBLEVBQUFDLGFBQUEsRUFBQUMsZ0JBQUEsRUFBQUMsZ0JBQUEsRUFBQUMsYUFBQTtJQUN6QixJQUFJaEYsUUFBUSxFQUFFLE9BQU8sQ0FBQTJCLE1BQU0sYUFBQWdELFlBQUEsR0FBTmhELE1BQU0sQ0FBRXNELElBQUkscUJBQVpOLFlBQUEsQ0FBYzNFLFFBQVEsS0FBSSxNQUFNO0lBRXJELFFBQVFJLE9BQU87TUFDYixLQUFLLFNBQVM7UUFDWixPQUFPLENBQUF1QixNQUFNLGFBQUFpRCxhQUFBLEdBQU5qRCxNQUFNLENBQUVzRCxJQUFJLHFCQUFaTCxhQUFBLENBQWNNLE9BQU8sS0FBSSxTQUFTO01BQzNDLEtBQUssV0FBVztRQUNkLE9BQU8sQ0FBQXZELE1BQU0sYUFBQWtELGFBQUEsR0FBTmxELE1BQU0sQ0FBRXNELElBQUkscUJBQVpKLGFBQUEsQ0FBY1QsT0FBTyxLQUFJLE1BQU07TUFDeEMsS0FBSyxTQUFTO1FBQ1osT0FBTyxDQUFBekMsTUFBTSxhQUFBbUQsZ0JBQUEsR0FBTm5ELE1BQU0sQ0FBRXlDLE9BQU8scUJBQWZVLGdCQUFBLENBQWlCakcsT0FBTyxLQUFJLFNBQVM7TUFDOUMsS0FBSyxPQUFPO1FBQ1YsT0FBTyxDQUFBOEMsTUFBTSxhQUFBb0QsZ0JBQUEsR0FBTnBELE1BQU0sQ0FBRXlDLE9BQU8scUJBQWZXLGdCQUFBLENBQWlCbEcsT0FBTyxLQUFJLFNBQVM7TUFDOUM7UUFDRSxPQUFPLENBQUE4QyxNQUFNLGFBQUFxRCxhQUFBLEdBQU5yRCxNQUFNLENBQUVzRCxJQUFJLHFCQUFaRCxhQUFBLENBQWNaLE9BQU8sS0FBSSxNQUFNO0lBQzFDO0VBQ0YsQ0FBQztFQUdELElBQU1lLFdBQVcsR0FBRyxTQUFkQSxXQUFXQSxDQUFBLEVBQVM7SUFDeEIsUUFBUTdFLElBQUk7TUFDVixLQUFLLE9BQU87UUFDVixPQUFPLEVBQUU7TUFDWCxLQUFLLE9BQU87UUFDVixPQUFPLEVBQUU7TUFDWDtRQUNFLE9BQU8sRUFBRTtJQUNiO0VBQ0YsQ0FBQztFQUdELElBQU04RSxpQkFBaUIsR0FBRyxTQUFwQkEsaUJBQWlCQSxDQUFBLEVBQVM7SUFDOUIsSUFBSWxGLEtBQUssS0FBSyxTQUFTLEVBQUUsT0FBTyxJQUFJO0lBRXBDLE9BQ0UsSUFBQWhDLFdBQUEsQ0FBQW1ILEdBQUEsRUFBQ3pILFlBQUEsQ0FBQXVELFFBQVEsQ0FBQ21FLElBQUk7TUFDWjdFLEtBQUssRUFBRSxDQUNMdUQsTUFBTSxDQUFDdUIsYUFBYSxFQUNwQjtRQUNFQyxTQUFTLEVBQUUsQ0FDVDtVQUNFQyxNQUFNLEVBQUVuRSxhQUFhLENBQUNvRSxXQUFXLENBQUM7WUFDaENDLFVBQVUsRUFBRSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUM7WUFDbEJDLFdBQVcsRUFBRSxDQUFDLE1BQU0sRUFBRSxRQUFRO1VBQ2hDLENBQUM7UUFDSCxDQUFDO01BRUwsQ0FBQyxDQUNEO01BQUFDLFFBQUEsRUFFRixJQUFBM0gsV0FBQSxDQUFBbUgsR0FBQSxFQUFDeEgsWUFBQSxDQUFBaUksUUFBUTtRQUNQQyxJQUFJLEVBQUMsU0FBUztRQUNkekYsSUFBSSxFQUFFNkUsV0FBVyxDQUFDLENBQUU7UUFDcEJhLEtBQUssRUFBRXRCLFlBQVksQ0FBQztNQUFFLENBQ3ZCO0lBQUMsQ0FDVyxDQUFDO0VBRXBCLENBQUM7RUFHRCxJQUFNdUIsVUFBVSxHQUFHLFNBQWJBLFVBQVVBLENBQUEsRUFBUztJQUN2QixJQUFJL0YsS0FBSyxLQUFLLFNBQVMsRUFBRSxPQUFPa0YsaUJBQWlCLENBQUMsQ0FBQztJQUNuRCxJQUFJLENBQUN6RixJQUFJLEVBQUUsT0FBTyxJQUFJO0lBRXRCLElBQUl1RyxRQUFRLEdBQUd2RyxJQUFJO0lBQ25CLElBQUlPLEtBQUssS0FBSyxTQUFTLEVBQUVnRyxRQUFRLEdBQUcsV0FBVztJQUMvQyxJQUFJaEcsS0FBSyxLQUFLLE9BQU8sRUFBRWdHLFFBQVEsR0FBRyxPQUFPO0lBRXpDLE9BQ0UsSUFBQWhJLFdBQUEsQ0FBQW1ILEdBQUEsRUFBQ3pILFlBQUEsQ0FBQTBILElBQUk7TUFBQzdFLEtBQUssRUFBRXVELE1BQU0sQ0FBQ3VCLGFBQWM7TUFBQU0sUUFBQSxFQUNoQyxJQUFBM0gsV0FBQSxDQUFBbUgsR0FBQSxFQUFDeEgsWUFBQSxDQUFBaUksUUFBUTtRQUNQQyxJQUFJLEVBQUVHLFFBQWdCO1FBQ3RCNUYsSUFBSSxFQUFFNkUsV0FBVyxDQUFDLENBQUU7UUFDcEJhLEtBQUssRUFBRXRCLFlBQVksQ0FBQztNQUFFLENBQ3ZCO0lBQUMsQ0FDRSxDQUFDO0VBRVgsQ0FBQztFQUdELElBQU15QixjQUFjLEdBQUcsU0FBakJBLGNBQWNBLENBQUEsRUFBUztJQUMzQixRQUFRakcsS0FBSztNQUNYLEtBQUssU0FBUztRQUNaLE9BQU8sWUFBWTtNQUNyQixLQUFLLFNBQVM7UUFDWixPQUFPLFVBQVU7TUFDbkIsS0FBSyxPQUFPO1FBQ1YsT0FBTyxXQUFXO01BQ3BCO1FBQ0UsT0FBT1IsS0FBSztJQUNoQjtFQUNGLENBQUM7RUFFRCxPQUNFLElBQUF4QixXQUFBLENBQUFtSCxHQUFBLEVBQUN6SCxZQUFBLENBQUF3SSxnQkFBZ0I7SUFDZjNGLEtBQUssRUFBRSxDQUFDbUIsaUJBQWlCLEVBQUVuQixLQUFLLENBQUU7SUFDbENYLE9BQU8sRUFBRTBDLFdBQVk7SUFDckI2RCxTQUFTLEVBQUVqRSxhQUFjO0lBQ3pCa0UsVUFBVSxFQUFFL0QsY0FBZTtJQUMzQnZDLFFBQVEsRUFBRUEsUUFBUSxJQUFJRSxLQUFLLEtBQUssU0FBVTtJQUMxQ3FHLGFBQWEsRUFBRSxHQUFJO0lBQ25CQyxpQkFBaUIsRUFBQyxRQUFRO0lBQzFCMUYsa0JBQWtCLEVBQUVBLGtCQUFrQixJQUFJcEIsS0FBTTtJQUNoRHFCLGlCQUFpQixFQUFFQSxpQkFBa0I7SUFDckMwRixrQkFBa0IsRUFBRTtNQUNsQnpHLFFBQVEsRUFBRUEsUUFBUSxJQUFJRSxLQUFLLEtBQUssU0FBUztNQUN6Q3dHLElBQUksRUFBRXhHLEtBQUssS0FBSztJQUNsQixDQUFFO0lBQ0ZjLE1BQU0sRUFBRUEsTUFBTztJQUFBNkUsUUFBQSxFQUVmLElBQUEzSCxXQUFBLENBQUF5SSxJQUFBLEVBQUMvSSxZQUFBLENBQUF1RCxRQUFRLENBQUNtRSxJQUFJO01BQ1o3RSxLQUFLLEVBQUUsQ0FDTDhDLGVBQWUsQ0FBQyxDQUFDLEVBQ2pCO1FBQ0VpQyxTQUFTLEVBQUUsQ0FDVDtVQUFFb0IsS0FBSyxFQUFFM0Y7UUFBVyxDQUFDLEVBQ3JCO1VBQUU0RixVQUFVLEVBQUVyRjtRQUFXLENBQUMsQ0FDM0I7UUFDRHNGLE9BQU8sRUFBRXZGO01BQ1gsQ0FBQyxDQUNEO01BQUFzRSxRQUFBLEdBRURoRyxZQUFZLEtBQUssTUFBTSxJQUFJb0csVUFBVSxDQUFDLENBQUMsRUFFeEMsSUFBQS9ILFdBQUEsQ0FBQW1ILEdBQUEsRUFBQ3JILFdBQUEsQ0FBQStJLFVBQVU7UUFDVDNHLE9BQU8sRUFBQyxRQUFRO1FBQ2hCNEYsS0FBSyxFQUFFdEIsWUFBWSxDQUFDLENBQUU7UUFDdEJqRSxLQUFLLEVBQUUsQ0FDTHVELE1BQU0sQ0FBQ2dELFVBQVUsRUFDakJySCxJQUFJLElBQUlFLFlBQVksS0FBSyxNQUFNLElBQUltRSxNQUFNLENBQUNpRCxnQkFBZ0IsRUFDMUR0SCxJQUFJLElBQUlFLFlBQVksS0FBSyxPQUFPLElBQUltRSxNQUFNLENBQUNrRCxpQkFBaUIsQ0FDNUQ7UUFBQXJCLFFBQUEsRUFFRE0sY0FBYyxDQUFDO01BQUMsQ0FDUCxDQUFDLEVBRVp0RyxZQUFZLEtBQUssT0FBTyxJQUFJb0csVUFBVSxDQUFDLENBQUM7SUFBQSxDQUM1QjtFQUFDLENBQ0EsQ0FBQztBQUV2QixDQUFDO0FBRUQsSUFBTWpDLE1BQU0sR0FBR21ELHVCQUFVLENBQUNDLE1BQU0sQ0FBQztFQUMvQm5ELE1BQU0sRUFBRTtJQUNOb0QsYUFBYSxFQUFFLEtBQUs7SUFDcEJDLFVBQVUsRUFBRSxRQUFRO0lBQ3BCQyxjQUFjLEVBQUUsUUFBUTtJQUN4QkMsWUFBWSxFQUFFLENBQUM7SUFDZkMsaUJBQWlCLEVBQUUsRUFBRTtJQUNyQkMsZUFBZSxFQUFFO0VBQ25CLENBQUM7RUFDREMsV0FBVyxFQUFFO0lBQ1hGLGlCQUFpQixFQUFFLEVBQUU7SUFDckJDLGVBQWUsRUFBRSxDQUFDO0lBQ2xCRixZQUFZLEVBQUU7RUFDaEIsQ0FBQztFQUNESSxZQUFZLEVBQUU7SUFDWkgsaUJBQWlCLEVBQUUsRUFBRTtJQUNyQkMsZUFBZSxFQUFFLEVBQUU7SUFDbkJGLFlBQVksRUFBRTtFQUNoQixDQUFDO0VBQ0RLLFdBQVcsRUFBRTtJQUNYSixpQkFBaUIsRUFBRSxFQUFFO0lBQ3JCQyxlQUFlLEVBQUUsRUFBRTtJQUNuQkYsWUFBWSxFQUFFO0VBQ2hCLENBQUM7RUFDRGhILFNBQVMsRUFBRTtJQUNUc0gsS0FBSyxFQUFFO0VBQ1QsQ0FBQztFQUNEckQsYUFBYSxFQUFFO0lBQ2JxQyxPQUFPLEVBQUU7RUFDWCxDQUFDO0VBQ0RFLFVBQVUsRUFBRTtJQUNWZSxTQUFTLEVBQUU7RUFDYixDQUFDO0VBQ0RkLGdCQUFnQixFQUFFO0lBQ2hCZSxVQUFVLEVBQUU7RUFDZCxDQUFDO0VBQ0RkLGlCQUFpQixFQUFFO0lBQ2pCZSxXQUFXLEVBQUU7RUFDZixDQUFDO0VBQ0QxQyxhQUFhLEVBQUU7SUFDYitCLFVBQVUsRUFBRSxRQUFRO0lBQ3BCQyxjQUFjLEVBQUU7RUFDbEI7QUFDRixDQUFDLENBQUM7QUFBQyxJQUFBVyxRQUFBLEdBQUExSSxPQUFBLENBQUFYLE9BQUEsR0FFWVUsY0FBYyIsImlnbm9yZUxpc3QiOltdfQ==