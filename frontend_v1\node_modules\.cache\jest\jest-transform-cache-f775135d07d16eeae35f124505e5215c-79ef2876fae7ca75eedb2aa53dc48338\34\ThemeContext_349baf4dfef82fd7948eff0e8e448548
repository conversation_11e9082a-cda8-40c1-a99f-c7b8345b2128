36b7bbb868aa80288591154e914ee619
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useTheme = exports.ThemeProvider = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _asyncStorage = _interopRequireDefault(require("@react-native-async-storage/async-storage"));
var _react = _interopRequireWildcard(require("react"));
var _reactNative = require("react-native");
var _moduleInitializer = require("../utils/moduleInitializer");
var _globalErrorInterceptor = require("../utils/globalErrorInterceptor");
var _accessibilityUtils = require("../utils/accessibilityUtils");
var _jsxRuntime = require("react/jsx-runtime");
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var colorsModule = (0, _moduleInitializer.safeModuleLoader)('Colors', function () {
  var module = require("../constants/Colors");
  if (!module.Colors || !module.DarkModeColors) {
    throw new Error('Colors module missing required exports');
  }
  return module;
}, {
  Colors: {
    primary: {
      default: '#4A6B52',
      light: '#6B8A74',
      dark: '#2A4B32',
      contrast: '#FFFFFF'
    },
    text: {
      primary: '#1A1A1A',
      secondary: '#6B7280',
      tertiary: '#9CA3AF'
    },
    background: {
      primary: '#FFFFFF',
      secondary: '#F9FAFB',
      tertiary: '#F3F4F6'
    },
    surface: {
      primary: '#FFFFFF',
      secondary: '#F9FAFB',
      tertiary: '#F3F4F6'
    }
  },
  DarkModeColors: {
    sage200: '#1F3A26',
    sage300: '#2A4B32',
    sage400: '#4A6B52',
    sage500: '#5A7A63',
    sage600: '#6B8A74'
  }
});
var Colors = colorsModule.Colors;
var DarkModeColors = colorsModule.DarkModeColors;
if (!(0, _moduleInitializer.checkModuleHealth)('Colors', Colors)) {
  console.error('[ThemeContext] Colors module failed health check');
}
var DarkColors = Object.assign({}, Colors, {
  background: {
    primary: '#121212',
    secondary: '#1E1E1E',
    tertiary: '#2C2C2C',
    elevated: '#1F1F1F',
    overlay: 'rgba(0, 0, 0, 0.8)',
    sage: '#2A4B32'
  },
  surface: {
    primary: '#1E1E1E',
    secondary: '#2C2C2C',
    tertiary: '#383838',
    inverse: '#F9FAFB',
    disabled: '#2C2C2C'
  },
  text: {
    primary: '#FFFFFF',
    secondary: '#D1D5DB',
    tertiary: '#9CA3AF',
    inverse: '#1A1A1A',
    disabled: '#6B7280',
    onPrimary: '#FFFFFF',
    onSecondary: '#FFFFFF',
    link: (DarkModeColors == null ? void 0 : DarkModeColors.sage600) || '#6B8A74',
    linkHover: (DarkModeColors == null ? void 0 : DarkModeColors.sage500) || '#5A7A63'
  },
  border: {
    light: '#404040',
    medium: '#525252',
    dark: '#737373',
    focus: (DarkModeColors == null ? void 0 : DarkModeColors.sage600) || '#6B8A74',
    error: '#F87171',
    success: '#34D399'
  },
  primary: {
    default: (DarkModeColors == null ? void 0 : DarkModeColors.sage300) || '#2A4B32',
    light: (DarkModeColors == null ? void 0 : DarkModeColors.sage400) || '#4A6B52',
    dark: (DarkModeColors == null ? void 0 : DarkModeColors.sage200) || '#1F3A26',
    contrast: '#FFFFFF'
  },
  primaryDark: (DarkModeColors == null ? void 0 : DarkModeColors.sage200) || '#1F3A26',
  primaryLight: (DarkModeColors == null ? void 0 : DarkModeColors.sage400) || '#4A6B52',
  sage400: (DarkModeColors == null ? void 0 : DarkModeColors.sage300) || '#2A4B32',
  sage500: (DarkModeColors == null ? void 0 : DarkModeColors.sage500) || '#4A6B52',
  sage600: (DarkModeColors == null ? void 0 : DarkModeColors.sage600) || '#6B8A74'
});
var ThemeContext = (0, _react.createContext)(undefined);
var THEME_STORAGE_KEY = '@vierla_theme_preference';
var ThemeProvider = exports.ThemeProvider = function ThemeProvider(_ref) {
  var children = _ref.children;
  var systemColorScheme = (0, _reactNative.useColorScheme)();
  var _useState = (0, _react.useState)(false),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    isDark = _useState2[0],
    setIsDark = _useState2[1];
  (0, _react.useEffect)(function () {
    var loadThemePreference = function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        try {
          var savedTheme = yield _asyncStorage.default.getItem(THEME_STORAGE_KEY);
          if (savedTheme !== null) {
            setIsDark(savedTheme === 'dark');
          } else {
            setIsDark(systemColorScheme === 'dark');
          }
        } catch (error) {
          console.warn('Failed to load theme preference:', error);
          setIsDark(systemColorScheme === 'dark');
        }
      });
      return function loadThemePreference() {
        return _ref2.apply(this, arguments);
      };
    }();
    loadThemePreference();
  }, [systemColorScheme]);
  var currentColors = _react.default.useMemo(function () {
    if ((0, _globalErrorInterceptor.hasRecentThemeErrors)()) {
      console.warn('[ThemeContext] Recent theme errors detected, using fallback theme');
      var fallbackTheme = (0, _globalErrorInterceptor.getFallbackTheme)();
      return fallbackTheme.colors;
    }
    var colors = isDark ? DarkColors : Colors;
    if (!colors || typeof colors !== 'object') {
      console.warn('[ThemeContext] Theme colors object is invalid, using fallback');
      var _fallbackTheme = (0, _globalErrorInterceptor.getFallbackTheme)();
      return _fallbackTheme.colors;
    }
    if (!colors.primary) {
      console.warn('[ThemeContext] Theme colors missing primary property, adding fallback');
      return Object.assign({}, colors, {
        primary: Colors.primary
      });
    }
    return colors;
  }, [isDark]);
  var saveThemePreference = function () {
    var _ref3 = (0, _asyncToGenerator2.default)(function* (darkMode) {
      try {
        yield _asyncStorage.default.setItem(THEME_STORAGE_KEY, darkMode ? 'dark' : 'light');
      } catch (error) {
        console.warn('Failed to save theme preference:', error);
      }
    });
    return function saveThemePreference(_x) {
      return _ref3.apply(this, arguments);
    };
  }();
  var toggleTheme = function toggleTheme() {
    var newIsDark = !isDark;
    setIsDark(newIsDark);
    saveThemePreference(newIsDark);
  };
  var setTheme = function setTheme(darkMode) {
    setIsDark(darkMode);
    saveThemePreference(darkMode);
  };
  var safeColors = _react.default.useMemo(function () {
    if (!currentColors || typeof currentColors !== 'object') {
      console.warn('Current colors is invalid, using fallback Colors');
      return Colors;
    }
    var requiredProperties = ['primary', 'text', 'background', 'surface'];
    var missingProperties = requiredProperties.filter(function (prop) {
      return !currentColors[prop];
    });
    if (missingProperties.length > 0) {
      console.warn(`Theme colors missing properties: ${missingProperties.join(', ')}, using fallback`);
      return Object.assign({}, Colors, currentColors, {
        primary: currentColors.primary || Colors.primary,
        text: currentColors.text || Colors.text,
        background: currentColors.background || Colors.background,
        surface: currentColors.surface || Colors.surface
      });
    }
    return currentColors;
  }, [currentColors]);
  var getTypography = function getTypography(size) {
    var typographySizes = {
      'xs': 12,
      'sm': 14,
      'base': 16,
      'lg': 18,
      'xl': 20,
      '2xl': 24,
      '3xl': 30,
      '4xl': 36
    };
    return typographySizes[size] || 16;
  };
  var getAccessibleColor = function getAccessibleColor(color, background) {
    var targetBackground = background || (isDark ? '#121212' : '#FFFFFF');
    return _accessibilityUtils.ColorContrastUtils.enhanceColorContrast(color, targetBackground, _accessibilityUtils.WCAG_STANDARDS.CONTRAST_RATIOS.AA_NORMAL);
  };
  var validateColorContrast = function validateColorContrast(foreground, background) {
    var ratio = _accessibilityUtils.ColorContrastUtils.getContrastRatio(foreground, background);
    return ratio >= _accessibilityUtils.WCAG_STANDARDS.CONTRAST_RATIOS.AA_NORMAL;
  };
  var getWCAGCompliantColors = function getWCAGCompliantColors() {
    var baseColors = isDark ? DarkColors : Colors;
    var backgroundColor = isDark ? '#121212' : '#FFFFFF';
    var compliantColors = Object.assign({}, baseColors, {
      text: Object.assign({}, baseColors.text, {
        primary: getAccessibleColor(baseColors.text.primary, backgroundColor),
        secondary: getAccessibleColor(baseColors.text.secondary, backgroundColor),
        tertiary: getAccessibleColor(baseColors.text.tertiary, backgroundColor)
      }),
      primary: Object.assign({}, baseColors.primary, {
        default: getAccessibleColor(baseColors.primary.default, backgroundColor)
      })
    });
    return compliantColors;
  };
  var contextValue = {
    colors: safeColors,
    isDark: isDark,
    isDarkMode: isDark,
    toggleTheme: toggleTheme,
    setTheme: setTheme,
    getTypography: getTypography,
    getAccessibleColor: getAccessibleColor,
    validateColorContrast: validateColorContrast,
    getWCAGCompliantColors: getWCAGCompliantColors
  };
  return (0, _jsxRuntime.jsx)(ThemeContext.Provider, {
    value: contextValue,
    children: children
  });
};
var useTheme = exports.useTheme = function useTheme() {
  var context = (0, _react.useContext)(ThemeContext);
  var fallbackGetTypography = function fallbackGetTypography(size) {
    var typographySizes = {
      'xs': 12,
      'sm': 14,
      'base': 16,
      'lg': 18,
      'xl': 20,
      '2xl': 24,
      '3xl': 30,
      '4xl': 36
    };
    return typographySizes[size] || 16;
  };
  if (context === undefined) {
    var _global;
    console.warn('[useTheme] useTheme used outside ThemeProvider, using fallback theme');
    var globalFallback = (_global = global) == null ? void 0 : _global.__VIERLA_FALLBACK_THEME__;
    var fallbackColors = (globalFallback == null ? void 0 : globalFallback.colors) || (0, _globalErrorInterceptor.getFallbackTheme)().colors;
    return {
      colors: fallbackColors,
      isDark: false,
      isDarkMode: false,
      toggleTheme: function toggleTheme() {},
      setTheme: function setTheme() {},
      getTypography: fallbackGetTypography,
      getAccessibleColor: function getAccessibleColor(color, background) {
        return color;
      },
      validateColorContrast: function validateColorContrast() {
        return true;
      },
      getWCAGCompliantColors: function getWCAGCompliantColors() {
        return fallbackColors;
      }
    };
  }
  if (!context.colors || typeof context.colors !== 'object') {
    var _global2;
    console.warn('[useTheme] Theme colors object is invalid, using fallback');
    var _globalFallback = (_global2 = global) == null ? void 0 : _global2.__VIERLA_FALLBACK_THEME__;
    var _fallbackColors = (_globalFallback == null ? void 0 : _globalFallback.colors) || (0, _globalErrorInterceptor.getFallbackTheme)().colors;
    return Object.assign({}, context, {
      colors: _fallbackColors,
      isDarkMode: context.isDark,
      getTypography: context.getTypography || fallbackGetTypography
    });
  }
  if (!context.colors.primary) {
    console.warn('Theme primary colors missing, using fallback');
    return Object.assign({}, context, {
      colors: Object.assign({}, context.colors, {
        primary: Colors.primary
      }),
      getTypography: context.getTypography || fallbackGetTypography
    });
  }
  if (!context.colors.text || typeof context.colors.text !== 'object') {
    console.warn('Theme text colors missing, using fallback');
    return Object.assign({}, context, {
      colors: Object.assign({}, context.colors, {
        text: Colors.text
      }),
      getTypography: context.getTypography || fallbackGetTypography
    });
  }
  if (!context.colors.background || typeof context.colors.background !== 'object') {
    console.warn('Theme background colors missing, using fallback');
    return Object.assign({}, context, {
      colors: Object.assign({}, context.colors, {
        background: Colors.background
      }),
      getTypography: context.getTypography || fallbackGetTypography
    });
  }
  if (!context.getTypography) {
    return Object.assign({}, context, {
      getTypography: fallbackGetTypography
    });
  }
  return context;
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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