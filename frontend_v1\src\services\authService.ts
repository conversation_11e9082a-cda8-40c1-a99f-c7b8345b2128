/**
 * Authentication Service - API Integration for Frontend V1
 *
 * Service Contract:
 * - Handles authentication API calls to backend
 * - Provides login and registration functionality
 * - Supports dual-role authentication (customer/provider)
 * - Implements proper error handling and response parsing
 * - Follows TDD methodology with comprehensive test coverage
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { Platform } from 'react-native';

// API Configuration
const getApiBaseUrl = () => {
  if (!__DEV__) {
    return 'https://api.vierla.com';
  }

  // In development, use localhost for all platforms
  // This works for both Android emulator and iOS simulator when backend is bound to localhost
  return 'http://127.0.0.1:8000';
};

const API_BASE_URL = getApiBaseUrl();

// Request/Response Types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  first_name: string;
  last_name: string;
  email: string;
  password: string;
  role: 'customer' | 'service_provider';
}

export interface PasswordlessLoginRequest {
  method: 'email' | 'phone' | 'biometric';
  email?: string;
  phone?: string;
  verificationCode?: string;
}

export interface AuthResponse {
  access: string;
  refresh: string;
  user: {
    id: string;
    email: string;
    first_name: string;
    last_name: string;
    role: 'customer' | 'service_provider';
    is_verified: boolean;
    phone?: string;
    avatar?: string;
  };
}

export interface ApiError {
  detail?: string;
  message?: string;
  errors?: Record<string, string[]>;
  non_field_errors?: string[];
}

export interface ProfileUpdateRequest {
  first_name?: string;
  last_name?: string;
  phone_number?: string;
  profile_image?: string;
}

export interface PasswordResetRequest {
  email: string;
}

export interface PasswordResetConfirmRequest {
  token: string;
  new_password: string;
}

export interface ChangePasswordRequest {
  current_password: string;
  new_password: string;
}

class AuthService {
  private baseUrl = API_BASE_URL;

  /**
   * Make HTTP request with proper error handling
   */
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit,
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    console.log('🌐 Making request to:', url);
    console.log('📤 Request options:', { method: options.method, hasBody: !!options.body });

    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
      });

      console.log('📥 Response status:', response.status, response.statusText);
      const data = await response.json();
      console.log('📄 Response data keys:', Object.keys(data));

      if (!response.ok) {
        // Handle API errors
        const error = data as ApiError;
        let errorMessage = 'An error occurred';

        if (error.detail) {
          errorMessage = error.detail;
        } else if (error.message) {
          errorMessage = error.message;
        } else if (
          error.non_field_errors &&
          error.non_field_errors.length > 0
        ) {
          errorMessage = error.non_field_errors[0];
        } else if (error.errors) {
          // Handle field-specific errors
          const firstError = Object.values(error.errors)[0];
          if (firstError && firstError.length > 0) {
            errorMessage = firstError[0];
          }
        }

        throw new Error(errorMessage);
      }

      return data;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Network error occurred');
    }
  }

  /**
   * Login user with email and password
   */
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    console.log('🔐 AuthService.login called with:', { email: credentials.email });
    console.log('🌐 API Base URL:', this.baseUrl);

    const result = await this.makeRequest<AuthResponse>('/api/auth/login/', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });

    console.log('✅ AuthService.login successful');
    return result;
  }

  /**
   * Register new user with role selection
   */
  async register(userData: RegisterRequest): Promise<AuthResponse> {
    return this.makeRequest<AuthResponse>('/api/auth/register/', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  /**
   * Passwordless authentication login
   */
  async passwordlessLogin(request: PasswordlessLoginRequest): Promise<AuthResponse> {
    // For now, simulate passwordless authentication
    // In production, this would integrate with actual passwordless auth providers
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // Simulate successful authentication
        if (request.method === 'email' && request.email) {
          resolve({
            access: 'mock-access-token-passwordless',
            refresh: 'mock-refresh-token-passwordless',
            user: {
              id: '1',
              email: request.email,
              first_name: 'Passwordless',
              last_name: 'User',
              role: 'customer',
              is_verified: true,
            },
          });
        } else if (request.method === 'phone' && request.phone) {
          resolve({
            access: 'mock-access-token-passwordless',
            refresh: 'mock-refresh-token-passwordless',
            user: {
              id: '2',
              email: '<EMAIL>',
              first_name: 'Phone',
              last_name: 'User',
              role: 'customer',
              is_verified: true,
              phone: request.phone,
            },
          });
        } else if (request.method === 'biometric') {
          resolve({
            access: 'mock-access-token-passwordless',
            refresh: 'mock-refresh-token-passwordless',
            user: {
              id: '3',
              email: '<EMAIL>',
              first_name: 'Biometric',
              last_name: 'User',
              role: 'customer',
              is_verified: true,
            },
          });
        } else {
          reject(new Error('Invalid passwordless authentication request'));
        }
      }, 1000);
    });
  }

  /**
   * Refresh authentication token
   */
  async refreshToken(refreshToken: string): Promise<{ access: string }> {
    return this.makeRequest<{ access: string }>('/api/auth/token/refresh/', {
      method: 'POST',
      body: JSON.stringify({ refresh: refreshToken }),
    });
  }

  /**
   * Authenticate user with email (for magic link)
   */
  async authenticateWithEmail(email: string): Promise<any> {
    // In a real implementation, this would validate the email against the backend
    // For demo purposes, we'll create a mock user
    return {
      success: true,
      user: {
        id: 'email_' + Date.now(),
        email,
        first_name: 'Email',
        last_name: 'User',
        role: 'customer',
        is_verified: true,
      },
      token: 'mock_email_token_' + Date.now(),
      refreshToken: 'mock_email_refresh_' + Date.now(),
    };
  }

  /**
   * Authenticate user with phone (for SMS OTP)
   */
  async authenticateWithPhone(phone: string): Promise<any> {
    // In a real implementation, this would validate the phone against the backend
    // For demo purposes, we'll create a mock user
    return {
      success: true,
      user: {
        id: 'phone_' + Date.now(),
        phone,
        first_name: 'Phone',
        last_name: 'User',
        role: 'customer',
        is_verified: true,
      },
      token: 'mock_phone_token_' + Date.now(),
      refreshToken: 'mock_phone_refresh_' + Date.now(),
    };
  }

  /**
   * Authenticate user with biometric data
   */
  async authenticateWithBiometric(userData: any): Promise<any> {
    // In a real implementation, this would validate biometric data against the backend
    // For demo purposes, we'll return the stored user data
    return {
      success: true,
      user: userData,
      token: 'mock_biometric_token_' + Date.now(),
      refreshToken: 'mock_biometric_refresh_' + Date.now(),
    };
  }

  /**
   * Logout user (optional - for server-side logout)
   */
  async logout(refreshToken: string): Promise<void> {
    try {
      await this.makeRequest<void>('/api/auth/logout/', {
        method: 'POST',
        body: JSON.stringify({ refresh: refreshToken }),
      });
    } catch (error) {
      // Logout errors are not critical - user can still be logged out locally
      console.warn('Logout API call failed:', error);
    }
  }

  /**
   * Get user profile
   */
  async getProfile(token: string): Promise<AuthResponse['user']> {
    return this.makeRequest<AuthResponse['user']>('/api/auth/profile/', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });
  }

  /**
   * Update user profile
   */
  async updateProfile(profileData: ProfileUpdateRequest, token: string): Promise<AuthResponse['user']> {
    return this.makeRequest<AuthResponse['user']>('/api/auth/profile/', {
      method: 'PATCH',
      body: JSON.stringify(profileData),
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });
  }

  /**
   * Request password reset
   */
  async requestPasswordReset(data: PasswordResetRequest): Promise<{ message: string }> {
    return this.makeRequest<{ message: string }>('/api/auth/password-reset/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  /**
   * Confirm password reset
   */
  async confirmPasswordReset(data: PasswordResetConfirmRequest): Promise<{ message: string }> {
    return this.makeRequest<{ message: string }>('/api/auth/password-reset/confirm/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  /**
   * Change password (authenticated user)
   */
  async changePassword(data: ChangePasswordRequest, token: string): Promise<{ message: string }> {
    return this.makeRequest<{ message: string }>('/api/auth/change-password/', {
      method: 'POST',
      body: JSON.stringify(data),
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });
  }

  /**
   * Validate token with backend
   */
  async validateToken(token: string): Promise<boolean> {
    try {
      await this.makeRequest('/api/auth/validate-token/', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      return true;
    } catch {
      return false;
    }
  }
}

// Export singleton instance
export const authService = new AuthService();
