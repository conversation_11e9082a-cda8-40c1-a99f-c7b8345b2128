{"version": 3, "names": ["_asyncStorage", "_interopRequireDefault", "require", "_react", "_interopRequireWildcard", "_reactNative", "_moduleInitializer", "_globalErrorInterceptor", "_accessibilityUtils", "_jsxRuntime", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "colorsModule", "safeModuleLoader", "module", "Colors", "DarkModeColors", "Error", "primary", "light", "dark", "contrast", "text", "secondary", "tertiary", "background", "surface", "sage200", "sage300", "sage400", "sage500", "sage600", "checkModuleHealth", "console", "error", "DarkColors", "assign", "elevated", "overlay", "sage", "inverse", "disabled", "onPrimary", "onSecondary", "link", "linkHover", "border", "medium", "focus", "success", "primaryDark", "primaryLight", "ThemeContext", "createContext", "undefined", "THEME_STORAGE_KEY", "ThemeProvider", "exports", "_ref", "children", "systemColorScheme", "useColorScheme", "_useState", "useState", "_useState2", "_slicedToArray2", "isDark", "setIsDark", "useEffect", "loadThemePreference", "_ref2", "_asyncToGenerator2", "savedTheme", "AsyncStorage", "getItem", "warn", "apply", "arguments", "currentColors", "React", "useMemo", "hasRecentThemeErrors", "fallbackTheme", "getFallbackTheme", "colors", "saveThemePreference", "_ref3", "darkMode", "setItem", "_x", "toggleTheme", "newIsDark", "setTheme", "safeColors", "requiredProperties", "missingProperties", "filter", "prop", "length", "join", "getTypography", "size", "typographySizes", "getAccessibleColor", "color", "targetBackground", "ColorContrastUtils", "enhanceColorContrast", "WCAG_STANDARDS", "CONTRAST_RATIOS", "AA_NORMAL", "validateColorContrast", "foreground", "ratio", "getContrastRatio", "getWCAGCompliantColors", "baseColors", "backgroundColor", "compliantColors", "contextValue", "isDarkMode", "jsx", "Provider", "value", "useTheme", "context", "useContext", "fallbackGetTypography", "_global", "globalFallback", "global", "__VIERLA_FALLBACK_THEME__", "fallbackColors", "_global2"], "sources": ["ThemeContext.tsx"], "sourcesContent": ["import AsyncStorage from '@react-native-async-storage/async-storage';\nimport React, {\n  createContext,\n  useContext,\n  useState,\n  useEffect,\n  ReactNode,\n} from 'react';\nimport { useColorScheme } from 'react-native';\n\n// CRITICAL: Import with error handling for <PERSON><PERSON> engine using module initializer\nimport { safeModuleLoader, checkModuleHealth } from '../utils/moduleInitializer';\nimport { getFallbackTheme, hasRecentThemeErrors } from '../utils/globalErrorInterceptor';\nimport { ColorContrastUtils, WCAG_STANDARDS } from '../utils/accessibilityUtils';\n\n// Safe module loading with fallbacks\nconst colorsModule = safeModuleLoader(\n  'Colors',\n  () => {\n    const module = require('../constants/Colors');\n    if (!module.Colors || !module.DarkModeColors) {\n      throw new Error('Colors module missing required exports');\n    }\n    return module;\n  },\n  {\n    Colors: {\n      primary: { default: '#4A6B52', light: '#6B8A74', dark: '#2A4B32', contrast: '#FFFFFF' },\n      text: { primary: '#1A1A1A', secondary: '#6B7280', tertiary: '#9CA3AF' },\n      background: { primary: '#FFFFFF', secondary: '#F9FAFB', tertiary: '#F3F4F6' },\n      surface: { primary: '#FFFFFF', secondary: '#F9FAFB', tertiary: '#F3F4F6' },\n    },\n    DarkModeColors: {\n      sage200: '#1F3A26',\n      sage300: '#2A4B32',\n      sage400: '#4A6B52',\n      sage500: '#5A7A63',\n      sage600: '#6B8A74',\n    },\n  }\n);\n\nconst Colors = colorsModule.Colors;\nconst DarkModeColors = colorsModule.DarkModeColors;\n\n// Validate the loaded modules\nif (!checkModuleHealth('Colors', Colors)) {\n  console.error('[ThemeContext] Colors module failed health check');\n}\n\n// Theme Context Types\nexport interface ThemeContextType {\n  colors: typeof Colors;\n  isDark: boolean;\n  isDarkMode: boolean; // Add alias for compatibility\n  toggleTheme: () => void;\n  setTheme: (isDark: boolean) => void;\n  getTypography?: (size: string) => number;\n  // Enhanced accessibility features\n  getAccessibleColor: (color: string, background?: string) => string;\n  validateColorContrast: (foreground: string, background: string) => boolean;\n  getWCAGCompliantColors: () => typeof Colors;\n}\n\n// Dark theme colors - WCAG AA compliant with Vierla dark green theme\n// Using safe fallbacks to prevent undefined access during module loading\nconst DarkColors = {\n  ...Colors,\n  // Override specific colors for dark mode with conventional dark theme colors\n  background: {\n    primary: '#121212', // Standard dark mode primary background\n    secondary: '#1E1E1E', // Standard dark mode secondary background\n    tertiary: '#2C2C2C', // Standard dark mode tertiary background\n    elevated: '#1F1F1F', // Standard dark mode elevated surfaces\n    overlay: 'rgba(0, 0, 0, 0.8)', // Dark overlay\n    sage: '#2A4B32', // Dark sage green for header in dark mode\n  },\n  surface: {\n    primary: '#1E1E1E', // Standard dark mode surface\n    secondary: '#2C2C2C', // Standard dark mode secondary surface\n    tertiary: '#383838', // Standard dark mode tertiary surface\n    inverse: '#F9FAFB', // Near white for contrast\n    disabled: '#2C2C2C', // Standard dark mode disabled surface\n  },\n  text: {\n    primary: '#FFFFFF', // White for primary text\n    secondary: '#D1D5DB', // Light gray for secondary text\n    tertiary: '#9CA3AF', // Medium gray for tertiary text\n    inverse: '#1A1A1A', // Dark gray for inverse text\n    disabled: '#6B7280', // Gray for disabled text\n    onPrimary: '#FFFFFF', // White text on primary backgrounds\n    onSecondary: '#FFFFFF', // White text on secondary backgrounds\n    link: DarkModeColors?.sage600 || '#6B8A74', // Sage green for links with fallback\n    linkHover: DarkModeColors?.sage500 || '#5A7A63', // Darker sage for hover with fallback\n  },\n  border: {\n    light: '#404040', // Standard dark mode light border\n    medium: '#525252', // Standard dark mode medium border\n    dark: '#737373', // Standard dark mode dark border\n    focus: DarkModeColors?.sage600 || '#6B8A74', // Sage green for focus states with fallback\n    error: '#F87171', // Red for errors\n    success: '#34D399', // Green for success\n  },\n  // Override primary brand colors for dark mode - maintaining sage green theme\n  primary: {\n    default: DarkModeColors?.sage300 || '#2A4B32', // Dark sage green with fallback\n    light: DarkModeColors?.sage400 || '#4A6B52',\n    dark: DarkModeColors?.sage200 || '#1F3A26',\n    contrast: '#FFFFFF',\n  },\n  primaryDark: DarkModeColors?.sage200 || '#1F3A26', // Darker sage for pressed states with fallback\n  primaryLight: DarkModeColors?.sage400 || '#4A6B52', // Lighter sage for highlights with fallback\n  sage400: DarkModeColors?.sage300 || '#2A4B32', // Dark sage green with fallback\n  sage500: DarkModeColors?.sage500 || '#4A6B52', // Sage 500 for dark mode with fallback\n  sage600: DarkModeColors?.sage600 || '#6B8A74', // Sage 600 for dark mode with fallback\n} as const;\n\n// Create theme context\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\nexport interface ThemeProviderProps {\n  children: ReactNode;\n}\n\n// Storage key for theme preference\nconst THEME_STORAGE_KEY = '@vierla_theme_preference';\n\nexport const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {\n  const systemColorScheme = useColorScheme();\n  const [isDark, setIsDark] = useState(false);\n\n  // Load saved theme preference\n  useEffect(() => {\n    const loadThemePreference = async () => {\n      try {\n        const savedTheme = await AsyncStorage.getItem(THEME_STORAGE_KEY);\n        if (savedTheme !== null) {\n          setIsDark(savedTheme === 'dark');\n        } else {\n          // Default to system preference\n          setIsDark(systemColorScheme === 'dark');\n        }\n      } catch (error) {\n        console.warn('Failed to load theme preference:', error);\n        setIsDark(systemColorScheme === 'dark');\n      }\n    };\n\n    loadThemePreference();\n  }, [systemColorScheme]);\n\n  // Get current colors based on dark mode state with safety checks\n  const currentColors = React.useMemo(() => {\n    // Check if there have been recent theme errors\n    if (hasRecentThemeErrors()) {\n      console.warn('[ThemeContext] Recent theme errors detected, using fallback theme');\n      const fallbackTheme = getFallbackTheme();\n      return fallbackTheme.colors;\n    }\n\n    const colors = isDark ? DarkColors : Colors;\n\n    // Ensure colors object has required properties\n    if (!colors || typeof colors !== 'object') {\n      console.warn('[ThemeContext] Theme colors object is invalid, using fallback');\n      const fallbackTheme = getFallbackTheme();\n      return fallbackTheme.colors;\n    }\n\n    // Ensure primary property exists\n    if (!colors.primary) {\n      console.warn('[ThemeContext] Theme colors missing primary property, adding fallback');\n      return {\n        ...colors,\n        primary: Colors.primary,\n      };\n    }\n\n    return colors;\n  }, [isDark]);\n\n  // Save theme preference\n  const saveThemePreference = async (darkMode: boolean) => {\n    try {\n      await AsyncStorage.setItem(\n        THEME_STORAGE_KEY,\n        darkMode ? 'dark' : 'light',\n      );\n    } catch (error) {\n      console.warn('Failed to save theme preference:', error);\n    }\n  };\n\n  // Theme actions\n  const toggleTheme = () => {\n    const newIsDark = !isDark;\n    setIsDark(newIsDark);\n    saveThemePreference(newIsDark);\n  };\n\n  const setTheme = (darkMode: boolean) => {\n    setIsDark(darkMode);\n    saveThemePreference(darkMode);\n  };\n\n  // Ensure colors object is valid before providing context with comprehensive safety checks\n  const safeColors = React.useMemo(() => {\n    if (!currentColors || typeof currentColors !== 'object') {\n      console.warn('Current colors is invalid, using fallback Colors');\n      return Colors;\n    }\n\n    // Ensure all required color properties exist\n    const requiredProperties = ['primary', 'text', 'background', 'surface'];\n    const missingProperties = requiredProperties.filter(prop => !currentColors[prop]);\n\n    if (missingProperties.length > 0) {\n      console.warn(`Theme colors missing properties: ${missingProperties.join(', ')}, using fallback`);\n      return {\n        ...Colors,\n        ...currentColors,\n        // Ensure primary exists\n        primary: currentColors.primary || Colors.primary,\n        // Ensure text exists\n        text: currentColors.text || Colors.text,\n        // Ensure background exists\n        background: currentColors.background || Colors.background,\n        // Ensure surface exists\n        surface: currentColors.surface || Colors.surface,\n      };\n    }\n\n    return currentColors;\n  }, [currentColors]);\n\n  // Typography helper function\n  const getTypography = (size: string): number => {\n    const typographySizes: Record<string, number> = {\n      'xs': 12,\n      'sm': 14,\n      'base': 16,\n      'lg': 18,\n      'xl': 20,\n      '2xl': 24,\n      '3xl': 30,\n      '4xl': 36,\n    };\n    return typographySizes[size] || 16;\n  };\n\n  // Enhanced accessibility functions\n  const getAccessibleColor = (color: string, background?: string): string => {\n    const targetBackground = background || (isDark ? '#121212' : '#FFFFFF');\n    return ColorContrastUtils.enhanceColorContrast(color, targetBackground, WCAG_STANDARDS.CONTRAST_RATIOS.AA_NORMAL);\n  };\n\n  const validateColorContrast = (foreground: string, background: string): boolean => {\n    const ratio = ColorContrastUtils.getContrastRatio(foreground, background);\n    return ratio >= WCAG_STANDARDS.CONTRAST_RATIOS.AA_NORMAL;\n  };\n\n  const getWCAGCompliantColors = (): typeof Colors => {\n    const baseColors = isDark ? DarkColors : Colors;\n    const backgroundColor = isDark ? '#121212' : '#FFFFFF';\n\n    // Ensure all text colors meet WCAG AA standards\n    const compliantColors = {\n      ...baseColors,\n      text: {\n        ...baseColors.text,\n        primary: getAccessibleColor(baseColors.text.primary, backgroundColor),\n        secondary: getAccessibleColor(baseColors.text.secondary, backgroundColor),\n        tertiary: getAccessibleColor(baseColors.text.tertiary, backgroundColor),\n      },\n      primary: {\n        ...baseColors.primary,\n        default: getAccessibleColor(baseColors.primary.default, backgroundColor),\n      }\n    };\n\n    return compliantColors;\n  };\n\n  const contextValue: ThemeContextType = {\n    colors: safeColors,\n    isDark,\n    isDarkMode: isDark, // Add alias for compatibility\n    toggleTheme,\n    setTheme,\n    getTypography,\n    // Enhanced accessibility features\n    getAccessibleColor,\n    validateColorContrast,\n    getWCAGCompliantColors,\n  };\n\n  return (\n    <ThemeContext.Provider value={contextValue}>\n      {children}\n    </ThemeContext.Provider>\n  );\n};\n\n// Custom hook to use theme context with enhanced error handling\nexport const useTheme = (): ThemeContextType => {\n  const context = useContext(ThemeContext);\n\n  // Fallback typography function\n  const fallbackGetTypography = (size: string): number => {\n    const typographySizes: Record<string, number> = {\n      'xs': 12,\n      'sm': 14,\n      'base': 16,\n      'lg': 18,\n      'xl': 20,\n      '2xl': 24,\n      '3xl': 30,\n      '4xl': 36,\n    };\n    return typographySizes[size] || 16;\n  };\n\n  if (context === undefined) {\n    // Return fallback theme instead of throwing error\n    console.warn('[useTheme] useTheme used outside ThemeProvider, using fallback theme');\n\n    // Check for global fallback theme\n    const globalFallback = global?.__VIERLA_FALLBACK_THEME__;\n    const fallbackColors = globalFallback?.colors || getFallbackTheme().colors;\n\n    return {\n      colors: fallbackColors,\n      isDark: false,\n      isDarkMode: false, // Add alias for compatibility\n      toggleTheme: () => {},\n      setTheme: () => {},\n      getTypography: fallbackGetTypography,\n      // Fallback accessibility functions\n      getAccessibleColor: (color: string, background?: string) => color,\n      validateColorContrast: () => true,\n      getWCAGCompliantColors: () => fallbackColors,\n    };\n  }\n\n  // Additional safety check for colors object with comprehensive validation\n  if (!context.colors || typeof context.colors !== 'object') {\n    console.warn('[useTheme] Theme colors object is invalid, using fallback');\n    const globalFallback = global?.__VIERLA_FALLBACK_THEME__;\n    const fallbackColors = globalFallback?.colors || getFallbackTheme().colors;\n\n    return {\n      ...context,\n      colors: fallbackColors,\n      isDarkMode: context.isDark, // Ensure alias is present\n      getTypography: context.getTypography || fallbackGetTypography,\n    };\n  }\n\n  // Ensure primary colors exist\n  if (!context.colors.primary) {\n    console.warn('Theme primary colors missing, using fallback');\n    return {\n      ...context,\n      colors: {\n        ...context.colors,\n        primary: Colors.primary,\n      },\n      getTypography: context.getTypography || fallbackGetTypography,\n    };\n  }\n\n  // Ensure text colors exist\n  if (!context.colors.text || typeof context.colors.text !== 'object') {\n    console.warn('Theme text colors missing, using fallback');\n    return {\n      ...context,\n      colors: {\n        ...context.colors,\n        text: Colors.text,\n      },\n      getTypography: context.getTypography || fallbackGetTypography,\n    };\n  }\n\n  // Ensure background colors exist\n  if (!context.colors.background || typeof context.colors.background !== 'object') {\n    console.warn('Theme background colors missing, using fallback');\n    return {\n      ...context,\n      colors: {\n        ...context.colors,\n        background: Colors.background,\n      },\n      getTypography: context.getTypography || fallbackGetTypography,\n    };\n  }\n\n  // Ensure getTypography function exists\n  if (!context.getTypography) {\n    return {\n      ...context,\n      getTypography: fallbackGetTypography,\n    };\n  }\n\n  return context;\n};\n"], "mappings": ";;;;;;;AAAA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAC,uBAAA,CAAAF,OAAA;AAOA,IAAAG,YAAA,GAAAH,OAAA;AAGA,IAAAI,kBAAA,GAAAJ,OAAA;AACA,IAAAK,uBAAA,GAAAL,OAAA;AACA,IAAAM,mBAAA,GAAAN,OAAA;AAAiF,IAAAO,WAAA,GAAAP,OAAA;AAAA,SAAAE,wBAAAM,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAR,uBAAA,YAAAA,wBAAAM,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAGjF,IAAMmB,YAAY,GAAG,IAAAC,mCAAgB,EACnC,QAAQ,EACR,YAAM;EACJ,IAAMC,MAAM,GAAG9B,OAAO,sBAAsB,CAAC;EAC7C,IAAI,CAAC8B,MAAM,CAACC,MAAM,IAAI,CAACD,MAAM,CAACE,cAAc,EAAE;IAC5C,MAAM,IAAIC,KAAK,CAAC,wCAAwC,CAAC;EAC3D;EACA,OAAOH,MAAM;AACf,CAAC,EACD;EACEC,MAAM,EAAE;IACNG,OAAO,EAAE;MAAEhB,OAAO,EAAE,SAAS;MAAEiB,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE,SAAS;MAAEC,QAAQ,EAAE;IAAU,CAAC;IACvFC,IAAI,EAAE;MAAEJ,OAAO,EAAE,SAAS;MAAEK,SAAS,EAAE,SAAS;MAAEC,QAAQ,EAAE;IAAU,CAAC;IACvEC,UAAU,EAAE;MAAEP,OAAO,EAAE,SAAS;MAAEK,SAAS,EAAE,SAAS;MAAEC,QAAQ,EAAE;IAAU,CAAC;IAC7EE,OAAO,EAAE;MAAER,OAAO,EAAE,SAAS;MAAEK,SAAS,EAAE,SAAS;MAAEC,QAAQ,EAAE;IAAU;EAC3E,CAAC;EACDR,cAAc,EAAE;IACdW,OAAO,EAAE,SAAS;IAClBC,OAAO,EAAE,SAAS;IAClBC,OAAO,EAAE,SAAS;IAClBC,OAAO,EAAE,SAAS;IAClBC,OAAO,EAAE;EACX;AACF,CACF,CAAC;AAED,IAAMhB,MAAM,GAAGH,YAAY,CAACG,MAAM;AAClC,IAAMC,cAAc,GAAGJ,YAAY,CAACI,cAAc;AAGlD,IAAI,CAAC,IAAAgB,oCAAiB,EAAC,QAAQ,EAAEjB,MAAM,CAAC,EAAE;EACxCkB,OAAO,CAACC,KAAK,CAAC,kDAAkD,CAAC;AACnE;AAkBA,IAAMC,UAAU,GAAA1B,MAAA,CAAA2B,MAAA,KACXrB,MAAM;EAETU,UAAU,EAAE;IACVP,OAAO,EAAE,SAAS;IAClBK,SAAS,EAAE,SAAS;IACpBC,QAAQ,EAAE,SAAS;IACnBa,QAAQ,EAAE,SAAS;IACnBC,OAAO,EAAE,oBAAoB;IAC7BC,IAAI,EAAE;EACR,CAAC;EACDb,OAAO,EAAE;IACPR,OAAO,EAAE,SAAS;IAClBK,SAAS,EAAE,SAAS;IACpBC,QAAQ,EAAE,SAAS;IACnBgB,OAAO,EAAE,SAAS;IAClBC,QAAQ,EAAE;EACZ,CAAC;EACDnB,IAAI,EAAE;IACJJ,OAAO,EAAE,SAAS;IAClBK,SAAS,EAAE,SAAS;IACpBC,QAAQ,EAAE,SAAS;IACnBgB,OAAO,EAAE,SAAS;IAClBC,QAAQ,EAAE,SAAS;IACnBC,SAAS,EAAE,SAAS;IACpBC,WAAW,EAAE,SAAS;IACtBC,IAAI,EAAE,CAAA5B,cAAc,oBAAdA,cAAc,CAAEe,OAAO,KAAI,SAAS;IAC1Cc,SAAS,EAAE,CAAA7B,cAAc,oBAAdA,cAAc,CAAEc,OAAO,KAAI;EACxC,CAAC;EACDgB,MAAM,EAAE;IACN3B,KAAK,EAAE,SAAS;IAChB4B,MAAM,EAAE,SAAS;IACjB3B,IAAI,EAAE,SAAS;IACf4B,KAAK,EAAE,CAAAhC,cAAc,oBAAdA,cAAc,CAAEe,OAAO,KAAI,SAAS;IAC3CG,KAAK,EAAE,SAAS;IAChBe,OAAO,EAAE;EACX,CAAC;EAED/B,OAAO,EAAE;IACPhB,OAAO,EAAE,CAAAc,cAAc,oBAAdA,cAAc,CAAEY,OAAO,KAAI,SAAS;IAC7CT,KAAK,EAAE,CAAAH,cAAc,oBAAdA,cAAc,CAAEa,OAAO,KAAI,SAAS;IAC3CT,IAAI,EAAE,CAAAJ,cAAc,oBAAdA,cAAc,CAAEW,OAAO,KAAI,SAAS;IAC1CN,QAAQ,EAAE;EACZ,CAAC;EACD6B,WAAW,EAAE,CAAAlC,cAAc,oBAAdA,cAAc,CAAEW,OAAO,KAAI,SAAS;EACjDwB,YAAY,EAAE,CAAAnC,cAAc,oBAAdA,cAAc,CAAEa,OAAO,KAAI,SAAS;EAClDA,OAAO,EAAE,CAAAb,cAAc,oBAAdA,cAAc,CAAEY,OAAO,KAAI,SAAS;EAC7CE,OAAO,EAAE,CAAAd,cAAc,oBAAdA,cAAc,CAAEc,OAAO,KAAI,SAAS;EAC7CC,OAAO,EAAE,CAAAf,cAAc,oBAAdA,cAAc,CAAEe,OAAO,KAAI;AAAS,EACrC;AAGV,IAAMqB,YAAY,GAAG,IAAAC,oBAAa,EAA+BC,SAAS,CAAC;AAO3E,IAAMC,iBAAiB,GAAG,0BAA0B;AAE7C,IAAMC,aAA2C,GAAAC,OAAA,CAAAD,aAAA,GAAG,SAA9CA,aAA2CA,CAAAE,IAAA,EAAqB;EAAA,IAAfC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;EACpE,IAAMC,iBAAiB,GAAG,IAAAC,2BAAc,EAAC,CAAC;EAC1C,IAAAC,SAAA,GAA4B,IAAAC,eAAQ,EAAC,KAAK,CAAC;IAAAC,UAAA,OAAAC,eAAA,CAAA/D,OAAA,EAAA4D,SAAA;IAApCI,MAAM,GAAAF,UAAA;IAAEG,SAAS,GAAAH,UAAA;EAGxB,IAAAI,gBAAS,EAAC,YAAM;IACd,IAAMC,mBAAmB;MAAA,IAAAC,KAAA,OAAAC,kBAAA,CAAArE,OAAA,EAAG,aAAY;QACtC,IAAI;UACF,IAAMsE,UAAU,SAASC,qBAAY,CAACC,OAAO,CAACnB,iBAAiB,CAAC;UAChE,IAAIiB,UAAU,KAAK,IAAI,EAAE;YACvBL,SAAS,CAACK,UAAU,KAAK,MAAM,CAAC;UAClC,CAAC,MAAM;YAELL,SAAS,CAACP,iBAAiB,KAAK,MAAM,CAAC;UACzC;QACF,CAAC,CAAC,OAAO1B,KAAK,EAAE;UACdD,OAAO,CAAC0C,IAAI,CAAC,kCAAkC,EAAEzC,KAAK,CAAC;UACvDiC,SAAS,CAACP,iBAAiB,KAAK,MAAM,CAAC;QACzC;MACF,CAAC;MAAA,gBAbKS,mBAAmBA,CAAA;QAAA,OAAAC,KAAA,CAAAM,KAAA,OAAAC,SAAA;MAAA;IAAA,GAaxB;IAEDR,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAACT,iBAAiB,CAAC,CAAC;EAGvB,IAAMkB,aAAa,GAAGC,cAAK,CAACC,OAAO,CAAC,YAAM;IAExC,IAAI,IAAAC,4CAAoB,EAAC,CAAC,EAAE;MAC1BhD,OAAO,CAAC0C,IAAI,CAAC,mEAAmE,CAAC;MACjF,IAAMO,aAAa,GAAG,IAAAC,wCAAgB,EAAC,CAAC;MACxC,OAAOD,aAAa,CAACE,MAAM;IAC7B;IAEA,IAAMA,MAAM,GAAGlB,MAAM,GAAG/B,UAAU,GAAGpB,MAAM;IAG3C,IAAI,CAACqE,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;MACzCnD,OAAO,CAAC0C,IAAI,CAAC,+DAA+D,CAAC;MAC7E,IAAMO,cAAa,GAAG,IAAAC,wCAAgB,EAAC,CAAC;MACxC,OAAOD,cAAa,CAACE,MAAM;IAC7B;IAGA,IAAI,CAACA,MAAM,CAAClE,OAAO,EAAE;MACnBe,OAAO,CAAC0C,IAAI,CAAC,uEAAuE,CAAC;MACrF,OAAAlE,MAAA,CAAA2B,MAAA,KACKgD,MAAM;QACTlE,OAAO,EAAEH,MAAM,CAACG;MAAO;IAE3B;IAEA,OAAOkE,MAAM;EACf,CAAC,EAAE,CAAClB,MAAM,CAAC,CAAC;EAGZ,IAAMmB,mBAAmB;IAAA,IAAAC,KAAA,OAAAf,kBAAA,CAAArE,OAAA,EAAG,WAAOqF,QAAiB,EAAK;MACvD,IAAI;QACF,MAAMd,qBAAY,CAACe,OAAO,CACxBjC,iBAAiB,EACjBgC,QAAQ,GAAG,MAAM,GAAG,OACtB,CAAC;MACH,CAAC,CAAC,OAAOrD,KAAK,EAAE;QACdD,OAAO,CAAC0C,IAAI,CAAC,kCAAkC,EAAEzC,KAAK,CAAC;MACzD;IACF,CAAC;IAAA,gBATKmD,mBAAmBA,CAAAI,EAAA;MAAA,OAAAH,KAAA,CAAAV,KAAA,OAAAC,SAAA;IAAA;EAAA,GASxB;EAGD,IAAMa,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;IACxB,IAAMC,SAAS,GAAG,CAACzB,MAAM;IACzBC,SAAS,CAACwB,SAAS,CAAC;IACpBN,mBAAmB,CAACM,SAAS,CAAC;EAChC,CAAC;EAED,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAIL,QAAiB,EAAK;IACtCpB,SAAS,CAACoB,QAAQ,CAAC;IACnBF,mBAAmB,CAACE,QAAQ,CAAC;EAC/B,CAAC;EAGD,IAAMM,UAAU,GAAGd,cAAK,CAACC,OAAO,CAAC,YAAM;IACrC,IAAI,CAACF,aAAa,IAAI,OAAOA,aAAa,KAAK,QAAQ,EAAE;MACvD7C,OAAO,CAAC0C,IAAI,CAAC,kDAAkD,CAAC;MAChE,OAAO5D,MAAM;IACf;IAGA,IAAM+E,kBAAkB,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,YAAY,EAAE,SAAS,CAAC;IACvE,IAAMC,iBAAiB,GAAGD,kBAAkB,CAACE,MAAM,CAAC,UAAAC,IAAI;MAAA,OAAI,CAACnB,aAAa,CAACmB,IAAI,CAAC;IAAA,EAAC;IAEjF,IAAIF,iBAAiB,CAACG,MAAM,GAAG,CAAC,EAAE;MAChCjE,OAAO,CAAC0C,IAAI,CAAC,oCAAoCoB,iBAAiB,CAACI,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC;MAChG,OAAA1F,MAAA,CAAA2B,MAAA,KACKrB,MAAM,EACN+D,aAAa;QAEhB5D,OAAO,EAAE4D,aAAa,CAAC5D,OAAO,IAAIH,MAAM,CAACG,OAAO;QAEhDI,IAAI,EAAEwD,aAAa,CAACxD,IAAI,IAAIP,MAAM,CAACO,IAAI;QAEvCG,UAAU,EAAEqD,aAAa,CAACrD,UAAU,IAAIV,MAAM,CAACU,UAAU;QAEzDC,OAAO,EAAEoD,aAAa,CAACpD,OAAO,IAAIX,MAAM,CAACW;MAAO;IAEpD;IAEA,OAAOoD,aAAa;EACtB,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EAGnB,IAAMsB,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,IAAY,EAAa;IAC9C,IAAMC,eAAuC,GAAG;MAC9C,IAAI,EAAE,EAAE;MACR,IAAI,EAAE,EAAE;MACR,MAAM,EAAE,EAAE;MACV,IAAI,EAAE,EAAE;MACR,IAAI,EAAE,EAAE;MACR,KAAK,EAAE,EAAE;MACT,KAAK,EAAE,EAAE;MACT,KAAK,EAAE;IACT,CAAC;IACD,OAAOA,eAAe,CAACD,IAAI,CAAC,IAAI,EAAE;EACpC,CAAC;EAGD,IAAME,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIC,KAAa,EAAE/E,UAAmB,EAAa;IACzE,IAAMgF,gBAAgB,GAAGhF,UAAU,KAAKyC,MAAM,GAAG,SAAS,GAAG,SAAS,CAAC;IACvE,OAAOwC,sCAAkB,CAACC,oBAAoB,CAACH,KAAK,EAAEC,gBAAgB,EAAEG,kCAAc,CAACC,eAAe,CAACC,SAAS,CAAC;EACnH,CAAC;EAED,IAAMC,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAIC,UAAkB,EAAEvF,UAAkB,EAAc;IACjF,IAAMwF,KAAK,GAAGP,sCAAkB,CAACQ,gBAAgB,CAACF,UAAU,EAAEvF,UAAU,CAAC;IACzE,OAAOwF,KAAK,IAAIL,kCAAc,CAACC,eAAe,CAACC,SAAS;EAC1D,CAAC;EAED,IAAMK,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAA,EAAwB;IAClD,IAAMC,UAAU,GAAGlD,MAAM,GAAG/B,UAAU,GAAGpB,MAAM;IAC/C,IAAMsG,eAAe,GAAGnD,MAAM,GAAG,SAAS,GAAG,SAAS;IAGtD,IAAMoD,eAAe,GAAA7G,MAAA,CAAA2B,MAAA,KAChBgF,UAAU;MACb9F,IAAI,EAAAb,MAAA,CAAA2B,MAAA,KACCgF,UAAU,CAAC9F,IAAI;QAClBJ,OAAO,EAAEqF,kBAAkB,CAACa,UAAU,CAAC9F,IAAI,CAACJ,OAAO,EAAEmG,eAAe,CAAC;QACrE9F,SAAS,EAAEgF,kBAAkB,CAACa,UAAU,CAAC9F,IAAI,CAACC,SAAS,EAAE8F,eAAe,CAAC;QACzE7F,QAAQ,EAAE+E,kBAAkB,CAACa,UAAU,CAAC9F,IAAI,CAACE,QAAQ,EAAE6F,eAAe;MAAC,EACxE;MACDnG,OAAO,EAAAT,MAAA,CAAA2B,MAAA,KACFgF,UAAU,CAAClG,OAAO;QACrBhB,OAAO,EAAEqG,kBAAkB,CAACa,UAAU,CAAClG,OAAO,CAAChB,OAAO,EAAEmH,eAAe;MAAC;IACzE,EACF;IAED,OAAOC,eAAe;EACxB,CAAC;EAED,IAAMC,YAA8B,GAAG;IACrCnC,MAAM,EAAES,UAAU;IAClB3B,MAAM,EAANA,MAAM;IACNsD,UAAU,EAAEtD,MAAM;IAClBwB,WAAW,EAAXA,WAAW;IACXE,QAAQ,EAARA,QAAQ;IACRQ,aAAa,EAAbA,aAAa;IAEbG,kBAAkB,EAAlBA,kBAAkB;IAClBQ,qBAAqB,EAArBA,qBAAqB;IACrBI,sBAAsB,EAAtBA;EACF,CAAC;EAED,OACE,IAAA5H,WAAA,CAAAkI,GAAA,EAACrE,YAAY,CAACsE,QAAQ;IAACC,KAAK,EAAEJ,YAAa;IAAA5D,QAAA,EACxCA;EAAQ,CACY,CAAC;AAE5B,CAAC;AAGM,IAAMiE,QAAQ,GAAAnE,OAAA,CAAAmE,QAAA,GAAG,SAAXA,QAAQA,CAAA,EAA2B;EAC9C,IAAMC,OAAO,GAAG,IAAAC,iBAAU,EAAC1E,YAAY,CAAC;EAGxC,IAAM2E,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAI1B,IAAY,EAAa;IACtD,IAAMC,eAAuC,GAAG;MAC9C,IAAI,EAAE,EAAE;MACR,IAAI,EAAE,EAAE;MACR,MAAM,EAAE,EAAE;MACV,IAAI,EAAE,EAAE;MACR,IAAI,EAAE,EAAE;MACR,KAAK,EAAE,EAAE;MACT,KAAK,EAAE,EAAE;MACT,KAAK,EAAE;IACT,CAAC;IACD,OAAOA,eAAe,CAACD,IAAI,CAAC,IAAI,EAAE;EACpC,CAAC;EAED,IAAIwB,OAAO,KAAKvE,SAAS,EAAE;IAAA,IAAA0E,OAAA;IAEzB/F,OAAO,CAAC0C,IAAI,CAAC,sEAAsE,CAAC;IAGpF,IAAMsD,cAAc,IAAAD,OAAA,GAAGE,MAAM,qBAANF,OAAA,CAAQG,yBAAyB;IACxD,IAAMC,cAAc,GAAG,CAAAH,cAAc,oBAAdA,cAAc,CAAE7C,MAAM,KAAI,IAAAD,wCAAgB,EAAC,CAAC,CAACC,MAAM;IAE1E,OAAO;MACLA,MAAM,EAAEgD,cAAc;MACtBlE,MAAM,EAAE,KAAK;MACbsD,UAAU,EAAE,KAAK;MACjB9B,WAAW,EAAE,SAAbA,WAAWA,CAAA,EAAQ,CAAC,CAAC;MACrBE,QAAQ,EAAE,SAAVA,QAAQA,CAAA,EAAQ,CAAC,CAAC;MAClBQ,aAAa,EAAE2B,qBAAqB;MAEpCxB,kBAAkB,EAAE,SAApBA,kBAAkBA,CAAGC,KAAa,EAAE/E,UAAmB;QAAA,OAAK+E,KAAK;MAAA;MACjEO,qBAAqB,EAAE,SAAvBA,qBAAqBA,CAAA;QAAA,OAAQ,IAAI;MAAA;MACjCI,sBAAsB,EAAE,SAAxBA,sBAAsBA,CAAA;QAAA,OAAQiB,cAAc;MAAA;IAC9C,CAAC;EACH;EAGA,IAAI,CAACP,OAAO,CAACzC,MAAM,IAAI,OAAOyC,OAAO,CAACzC,MAAM,KAAK,QAAQ,EAAE;IAAA,IAAAiD,QAAA;IACzDpG,OAAO,CAAC0C,IAAI,CAAC,2DAA2D,CAAC;IACzE,IAAMsD,eAAc,IAAAI,QAAA,GAAGH,MAAM,qBAANG,QAAA,CAAQF,yBAAyB;IACxD,IAAMC,eAAc,GAAG,CAAAH,eAAc,oBAAdA,eAAc,CAAE7C,MAAM,KAAI,IAAAD,wCAAgB,EAAC,CAAC,CAACC,MAAM;IAE1E,OAAA3E,MAAA,CAAA2B,MAAA,KACKyF,OAAO;MACVzC,MAAM,EAAEgD,eAAc;MACtBZ,UAAU,EAAEK,OAAO,CAAC3D,MAAM;MAC1BkC,aAAa,EAAEyB,OAAO,CAACzB,aAAa,IAAI2B;IAAqB;EAEjE;EAGA,IAAI,CAACF,OAAO,CAACzC,MAAM,CAAClE,OAAO,EAAE;IAC3Be,OAAO,CAAC0C,IAAI,CAAC,8CAA8C,CAAC;IAC5D,OAAAlE,MAAA,CAAA2B,MAAA,KACKyF,OAAO;MACVzC,MAAM,EAAA3E,MAAA,CAAA2B,MAAA,KACDyF,OAAO,CAACzC,MAAM;QACjBlE,OAAO,EAAEH,MAAM,CAACG;MAAO,EACxB;MACDkF,aAAa,EAAEyB,OAAO,CAACzB,aAAa,IAAI2B;IAAqB;EAEjE;EAGA,IAAI,CAACF,OAAO,CAACzC,MAAM,CAAC9D,IAAI,IAAI,OAAOuG,OAAO,CAACzC,MAAM,CAAC9D,IAAI,KAAK,QAAQ,EAAE;IACnEW,OAAO,CAAC0C,IAAI,CAAC,2CAA2C,CAAC;IACzD,OAAAlE,MAAA,CAAA2B,MAAA,KACKyF,OAAO;MACVzC,MAAM,EAAA3E,MAAA,CAAA2B,MAAA,KACDyF,OAAO,CAACzC,MAAM;QACjB9D,IAAI,EAAEP,MAAM,CAACO;MAAI,EAClB;MACD8E,aAAa,EAAEyB,OAAO,CAACzB,aAAa,IAAI2B;IAAqB;EAEjE;EAGA,IAAI,CAACF,OAAO,CAACzC,MAAM,CAAC3D,UAAU,IAAI,OAAOoG,OAAO,CAACzC,MAAM,CAAC3D,UAAU,KAAK,QAAQ,EAAE;IAC/EQ,OAAO,CAAC0C,IAAI,CAAC,iDAAiD,CAAC;IAC/D,OAAAlE,MAAA,CAAA2B,MAAA,KACKyF,OAAO;MACVzC,MAAM,EAAA3E,MAAA,CAAA2B,MAAA,KACDyF,OAAO,CAACzC,MAAM;QACjB3D,UAAU,EAAEV,MAAM,CAACU;MAAU,EAC9B;MACD2E,aAAa,EAAEyB,OAAO,CAACzB,aAAa,IAAI2B;IAAqB;EAEjE;EAGA,IAAI,CAACF,OAAO,CAACzB,aAAa,EAAE;IAC1B,OAAA3F,MAAA,CAAA2B,MAAA,KACKyF,OAAO;MACVzB,aAAa,EAAE2B;IAAqB;EAExC;EAEA,OAAOF,OAAO;AAChB,CAAC", "ignoreList": []}