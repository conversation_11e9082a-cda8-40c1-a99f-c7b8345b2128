/**
 * Provider Details Screen - Comprehensive Provider Information Display
 *
 * Component Contract:
 * - Displays detailed provider information including services, contact info, and reviews
 * - Supports booking services and contacting providers
 * - Implements modern UI/UX with responsive design
 * - Follows accessibility guidelines and testing standards
 * - Integrates with navigation and state management
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Image } from 'react-native';
import { Alert, Linking, ActivityIndicator, RefreshControl } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Box } from '../components/atoms/Box';
import { Button } from '../components/atoms/Button';
import { IconButton } from '../components/atoms/IconButton';
import { SafeAreaWrapper } from '../components/ui/SafeAreaWrapper';
import { HeaderHelpButton } from '../components/help';
import { ErrorBoundary } from '../components/error/ErrorBoundary';
import { DataLoadingFallback } from '../components/error/DataLoadingFallback';
import { useTheme } from '../contexts/ThemeContext';
import { useErrorHandling } from '../hooks/useErrorHandling';
import { usePerformance } from '../hooks/usePerformance';
import providerService, { Provider, ProviderService as ProviderServiceType, ProviderReview } from '../services/providerService';
import type { CustomerStackParamList } from '../navigation/types';
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
} from '../utils/responsiveUtils';
import EnhancedProviderProfile, {
  EnhancedProvider,
  PortfolioItem,
  Certification,
  ServiceOffering
} from '../components/providers/EnhancedProviderProfile';

// Debug: Provider details screen loaded
console.log('🔍 ProviderDetailsScreen: Component loaded with backend integration');

// Transform basic provider data to enhanced provider format
const transformToEnhancedProvider = (basicProvider: any): EnhancedProvider => {
  return {
    id: basicProvider.id,
    businessName: basicProvider.businessName || basicProvider.name,
    bio: basicProvider.bio || `Professional ${basicProvider.services?.[0]?.category || 'service'} provider with years of experience delivering exceptional results.`,
    profileImage: basicProvider.profileImage || 'https://via.placeholder.com/150x150',
    coverImage: basicProvider.coverImage || 'https://via.placeholder.com/400x200',
    rating: basicProvider.rating || 4.5,
    reviewCount: basicProvider.reviewCount || 127,
    completedJobs: basicProvider.completedJobs || 250,
    yearsOfExperience: basicProvider.yearsOfExperience || 5,
    responseTime: basicProvider.responseTime || '< 1 hour',
    location: {
      address: basicProvider.location?.address || '123 Main St',
      city: basicProvider.location?.city || 'Toronto',
      distance: basicProvider.location?.distance || '2.5 km',
    },
    contact: {
      phone: basicProvider.contact?.phone || '+****************',
      email: basicProvider.contact?.email || '<EMAIL>',
      website: basicProvider.contact?.website,
    },
    portfolio: generateSamplePortfolio(basicProvider),
    certifications: generateSampleCertifications(basicProvider),
    services: transformServices(basicProvider.services || []),
    specialties: basicProvider.specialties || ['Professional Service', 'Quality Work', 'Customer Satisfaction'],
    languages: basicProvider.languages || ['English', 'French'],
    workingHours: basicProvider.workingHours || {
      monday: { open: '09:00', close: '17:00', isOpen: true },
      tuesday: { open: '09:00', close: '17:00', isOpen: true },
      wednesday: { open: '09:00', close: '17:00', isOpen: true },
      thursday: { open: '09:00', close: '17:00', isOpen: true },
      friday: { open: '09:00', close: '17:00', isOpen: true },
      saturday: { open: '10:00', close: '16:00', isOpen: true },
      sunday: { open: '10:00', close: '16:00', isOpen: false },
    },
    policies: {
      cancellation: '24 hours notice required for cancellations',
      rescheduling: 'Free rescheduling up to 2 hours before appointment',
      payment: ['Credit Card', 'Debit Card', 'Cash', 'E-Transfer'],
    },
    verification: {
      isVerified: basicProvider.verified || true,
      verifiedBadges: ['Identity Verified', 'Background Check', 'Insurance Verified'],
      backgroundCheck: true,
      insurance: true,
    },
  };
};

const generateSamplePortfolio = (provider: any): PortfolioItem[] => {
  return [
    {
      id: '1',
      title: 'Recent Project Showcase',
      description: 'High-quality service delivery with exceptional attention to detail and customer satisfaction.',
      images: ['https://via.placeholder.com/300x200', 'https://via.placeholder.com/300x200'],
      category: provider.services?.[0]?.category || 'Professional Service',
      completedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      clientTestimonial: 'Outstanding work! Exceeded all expectations and delivered on time.',
    },
    {
      id: '2',
      title: 'Premium Service Example',
      description: 'Demonstration of premium service capabilities with innovative solutions.',
      images: ['https://via.placeholder.com/300x200'],
      category: provider.services?.[0]?.category || 'Professional Service',
      completedAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
    },
  ];
};

const generateSampleCertifications = (provider: any): Certification[] => {
  return [
    {
      id: '1',
      name: 'Professional Service Certification',
      issuer: 'Industry Standards Board',
      dateObtained: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString(),
      level: 'advanced',
      badgeImage: 'https://via.placeholder.com/100x100',
    },
    {
      id: '2',
      name: 'Quality Assurance Certificate',
      issuer: 'Quality Standards Institute',
      dateObtained: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000).toISOString(),
      level: 'expert',
      credentialId: 'QSI-2024-001',
    },
  ];
};

const transformServices = (services: any[]): ServiceOffering[] => {
  return services.map((service, index) => ({
    id: service.id || `service-${index}`,
    name: service.name || service.title || 'Professional Service',
    description: service.description || 'High-quality professional service tailored to your needs.',
    category: service.category || 'Professional Services',
    duration: service.duration || 60,
    price: {
      min: service.price?.min || service.price || 50,
      max: service.price?.max || (service.price ? service.price * 1.5 : 100),
      currency: 'CAD',
    },
    images: service.images || ['https://via.placeholder.com/300x200'],
    features: service.features || ['Professional Quality', 'Satisfaction Guaranteed', 'Flexible Scheduling'],
    addOns: service.addOns || [
      { id: '1', name: 'Express Service', price: 25, description: 'Priority scheduling and faster completion' },
      { id: '2', name: 'Premium Package', price: 50, description: 'Enhanced service with additional benefits' },
    ],
    availability: {
      daysOfWeek: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
      timeSlots: ['09:00', '10:00', '11:00', '14:00', '15:00', '16:00'],
    },
    requirements: service.requirements || ['Valid ID required', 'Please arrive 5 minutes early'],
    cancellationPolicy: '24 hours notice required for cancellations',
  }));
};

// Navigation types

type ProviderDetailsScreenRouteProp = RouteProp<
  CustomerStackParamList,
  'ProviderDetails'
>;
type ProviderDetailsScreenNavigationProp = StackNavigationProp<
  CustomerStackParamList,
  'ProviderDetails'
>;

export const ProviderDetailsScreen: React.FC = () => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  const navigation = useNavigation<ProviderDetailsScreenNavigationProp>();
  const route = useRoute<ProviderDetailsScreenRouteProp>();
  const { providerId } = route.params;

  // State management
  const [provider, setProvider] = useState<Provider | null>(null);
  const [services, setServices] = useState<ProviderServiceType[]>([]);
  const [reviews, setReviews] = useState<ProviderReview[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [isFavorite, setIsFavorite] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [activeTab, setActiveTab] = useState<'services' | 'reviews' | 'about' | 'portfolio'>('services');
  const [showAllReviews, setShowAllReviews] = useState(false);
  const [showBookingModal, setShowBookingModal] = useState(false);
  const [selectedService, setSelectedService] = useState<ProviderServiceType | null>(null);

  // Performance tracking
  const { trackInteraction, trackAsyncOperation } = usePerformance({
    componentName: 'ProviderDetailsScreen',
    trackRenders: true,
    trackInteractions: true,
  });

  // Error handling
  const {
    error,
    isError,
    handleError,
    clearError,
    retry,
  } = useErrorHandling({
    maxRetries: 3,
    errorContext: { component: 'ProviderDetailsScreen', providerId },
  });

  useEffect(() => {
    loadProviderDetails();
  }, [providerId]);

  const loadProviderDetails = useCallback(async () => {
    const operation = trackAsyncOperation('loadProviderDetails');
    setLoading(true);
    clearError();

    try {
      // Load provider details from backend
      const [providerData, servicesData, reviewsData] = await Promise.all([
        providerService.getProviderById(providerId),
        providerService.getProviderServices(providerId),
        providerService.getProviderReviews(providerId, { limit: 10 })
      ]);

      setProvider(providerData);
      setServices(servicesData);
      setReviews(reviewsData.results);

      // Check if provider is in favorites (this would come from user preferences)
      // For now, we'll use a simple check
      setIsFavorite(false); // TODO: Implement favorites check

      operation.success();
    } catch (error: any) {
      console.error('Failed to load provider details:', error);
      handleError(error);
      operation.error(error);
    } finally {
      setLoading(false);
    }
  }, [providerId, trackAsyncOperation, clearError, handleError]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await loadProviderDetails();
    } finally {
      setRefreshing(false);
    }
  }, [loadProviderDetails]);

  const handleFavoriteToggle = useCallback(async () => {
    trackInteraction('favoriteToggle');
    try {
      if (isFavorite) {
        await providerService.removeFromFavorites(providerId);
      } else {
        await providerService.addToFavorites(providerId);
      }
      setIsFavorite(!isFavorite);
    } catch (error: any) {
      console.error('Failed to toggle favorite:', error);
      Alert.alert('Error', 'Failed to update favorites. Please try again.');
    }
  }, [providerId, isFavorite, trackInteraction]);

  const handleServiceBooking = useCallback((service: ProviderServiceType) => {
    trackInteraction('serviceBooking', { serviceId: service.id });
    setSelectedService(service);
    setShowBookingModal(true);
  }, [trackInteraction]);

  const handleContactProvider = useCallback(async () => {
    trackInteraction('contactProvider');
    if (!provider) return;

    const options = [];
    if (provider.contact.phone) {
      options.push({ text: 'Call', onPress: () => Linking.openURL(`tel:${provider.contact.phone}`) });
    }
    if (provider.contact.email) {
      options.push({ text: 'Email', onPress: () => Linking.openURL(`mailto:${provider.contact.email}`) });
    }
    options.push({ text: 'Message', onPress: () => navigation.navigate('Conversation', { providerId }) });
    options.push({ text: 'Cancel', style: 'cancel' });

    Alert.alert('Contact Provider', 'How would you like to contact this provider?', options);
  }, [provider, navigation, providerId, trackInteraction]);

  // Transform provider data for EnhancedProviderProfile component
  const transformProviderData = useCallback((provider: Provider): EnhancedProvider => {
    return {
      id: provider.id,
      name: provider.name,
      businessName: provider.businessName,
      description: provider.description,
      avatar: provider.avatar,
      coverImage: provider.coverImage,
      rating: provider.rating,
      reviewCount: provider.reviewCount,
      isVerified: provider.isVerified,
      isOnline: provider.isOnline,
      categories: provider.categories,
      location: provider.location,
      contact: provider.contact,
      businessHours: provider.businessHours,
      services: transformServices(services),
      portfolio: generateSamplePortfolio(provider),
      certifications: generateSampleCertifications(provider),
      amenities: ['WiFi', 'Parking', 'Wheelchair Accessible', 'Air Conditioning'],
      policies: {
        cancellation: '24 hours notice required for cancellations',
        rescheduling: 'Rescheduling allowed up to 2 hours before appointment',
        noShow: 'No-show fee may apply',
        payment: 'Payment due at time of service',
      },
      verification: {
        status: provider.isVerified ? 'verified' : 'pending',
        documents: [],
        verifiedAt: provider.isVerified ? new Date().toISOString() : undefined,
      },
      analytics: {
        totalBookings: provider.stats?.totalBookings || 0,
        averageRating: provider.rating,
        responseTime: provider.stats?.responseTime || '< 1 hour',
        completionRate: provider.stats?.completionRate || 95,
        repeatCustomers: 75,
        monthlyRevenue: 0,
        topServices: services.slice(0, 3).map(s => s.name),
      },
      settings: {
        notifications: {
          bookings: true,
          messages: true,
          reviews: true,
          promotions: false,
        },
        privacy: {
          showPhone: true,
          showEmail: false,
          showLocation: true,
        },
        booking: {
          autoAccept: false,
          requireDeposit: false,
          cancellationWindow: 24,
        },
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
  }, [services]);

  // Helper functions for data transformation
  const transformServices = useCallback((services: ProviderServiceType[]): ServiceOffering[] => {
    return services.map(service => ({
      id: service.id,
      name: service.name,
      description: service.description,
      price: {
        min: service.price,
        max: service.price,
        currency: 'CAD',
      },
      duration: service.duration,
      category: service.category,
      isAvailable: service.isActive,
      tags: service.tags || [],
      requirements: service.requirements || [],
      images: service.images || [],
    }));
  }, []);

  const generateSamplePortfolio = useCallback((provider: Provider): PortfolioItem[] => {
    // Generate sample portfolio items based on provider category
    const sampleItems: PortfolioItem[] = [];
    const categories = provider.categories || [];

    categories.forEach((category, index) => {
      sampleItems.push({
        id: `portfolio_${index + 1}`,
        title: `${category} Work ${index + 1}`,
        description: `Professional ${category.toLowerCase()} service showcase`,
        imageUrl: `https://images.unsplash.com/photo-${1560066984138 + index}?w=400&h=400&fit=crop`,
        category: category,
        tags: [category, 'Professional', 'Quality'],
        createdAt: new Date(Date.now() - index * 86400000).toISOString(),
      });
    });

    return sampleItems;
  }, []);

  const generateSampleCertifications = useCallback((provider: Provider): Certification[] => {
    // Generate sample certifications based on provider category
    const sampleCerts: Certification[] = [];
    const categories = provider.categories || [];

    categories.forEach((category, index) => {
      sampleCerts.push({
        id: `cert_${index + 1}`,
        name: `${category} Professional Certification`,
        issuer: `${category} Professional Association`,
        issueDate: new Date(2020 + index, index % 12, 1).toISOString(),
        expiryDate: new Date(2025 + index, index % 12, 1).toISOString(),
        credentialId: `CERT-${category.toUpperCase()}-${1000 + index}`,
        verificationUrl: `https://verify.example.com/cert_${index + 1}`,
        isVerified: true,
      });
    });

    return sampleCerts;
  }, []);

  // Error handling for retry
  const handleRetry = useCallback(() => {
    retry();
    loadProviderDetails();
  }, [retry, loadProviderDetails]);

  // Loading state
  if (loading && !provider) {
    return (
      <SafeAreaWrapper>
        <DataLoadingFallback
          message="Loading provider details..."
          onRetry={handleRetry}
        />
      </SafeAreaWrapper>
    );
  }

  // Error state
  if (isError && !provider) {
    return (
      <SafeAreaWrapper>
        <DataLoadingFallback
          message="Failed to load provider details"
          error={error}
          onRetry={handleRetry}
        />
      </SafeAreaWrapper>
    );
  }

  // No provider found
  if (!provider) {
    return (
      <SafeAreaWrapper>
        <DataLoadingFallback
          message="Provider not found"
          onRetry={() => navigation.goBack()}
          retryText="Go Back"
        />
      </SafeAreaWrapper>
    );
  }

  const enhancedProvider = transformProviderData(provider);
  // Main render
  return (
    <ErrorBoundary>
      <SafeAreaWrapper>
        <ScrollView
          style={styles.container}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[colors.sage600]}
              tintColor={colors.sage600}
            />
          }
          showsVerticalScrollIndicator={false}
        >
          <EnhancedProviderProfile
            provider={enhancedProvider}
            onServiceBook={handleServiceBooking}
            onContactProvider={handleContactProvider}
            onToggleFavorite={handleFavoriteToggle}
            isFavorite={isFavorite}
            reviews={reviews}
            onViewAllReviews={() => setShowAllReviews(true)}
          />
        </ScrollView>

        {/* Header with back button and favorite */}
        <View style={styles.header}>
          <IconButton
            icon="arrow-back"
            onPress={() => navigation.goBack()}
            style={styles.backButton}
            accessibilityLabel="Go back"
          />
          <IconButton
            icon={isFavorite ? "heart" : "heart-outline"}
            onPress={handleFavoriteToggle}
            style={[styles.favoriteButton, isFavorite && styles.favoriteActive]}
            accessibilityLabel={isFavorite ? "Remove from favorites" : "Add to favorites"}
          />
        </View>

        {/* Booking Modal */}
        {showBookingModal && selectedService && (
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <Text style={styles.modalTitle}>Book {selectedService.name}</Text>
              <Text style={styles.modalDescription}>{selectedService.description}</Text>
              <Text style={styles.modalPrice}>
                ${selectedService.price} • {selectedService.duration} min
              </Text>
              <View style={styles.modalButtons}>
                <Button
                  title="Cancel"
                  onPress={() => setShowBookingModal(false)}
                  variant="secondary"
                  style={styles.modalButton}
                />
                <Button
                  title="Continue"
                  onPress={() => {
                    setShowBookingModal(false);
                    navigation.navigate('ServiceDetails', {
                      serviceId: selectedService.id,
                      providerId: provider.id
                    });
                  }}
                  variant="primary"
                  style={styles.modalButton}
                />
              </View>
            </View>
          </View>
        )}
      </SafeAreaWrapper>
    </ErrorBoundary>
  );

};
// Default export
export default ProviderDetailsScreen;

// Styles
const createStyles = (colors: any) => ({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: getResponsiveSpacing(16),
    paddingTop: getResponsiveSpacing(50),
    paddingBottom: getResponsiveSpacing(12),
    backgroundColor: 'transparent',
    zIndex: 1000,
  },
  backButton: {
    backgroundColor: colors.background.primary,
    borderRadius: 20,
    shadowColor: colors.shadow.default,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  favoriteButton: {
    backgroundColor: colors.background.primary,
    borderRadius: 20,
    shadowColor: colors.shadow.default,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  favoriteActive: {
    backgroundColor: colors.sage100,
  },
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2000,
  },
  modalContent: {
    backgroundColor: colors.background.primary,
    margin: getResponsiveSpacing(20),
    padding: getResponsiveSpacing(24),
    borderRadius: 16,
    shadowColor: colors.shadow.default,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 8,
  },
  modalTitle: {
    fontSize: getResponsiveFontSize(20),
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: getResponsiveSpacing(8),
  },
  modalDescription: {
    fontSize: getResponsiveFontSize(14),
    color: colors.text.secondary,
    marginBottom: getResponsiveSpacing(12),
    lineHeight: 20,
  },
  modalPrice: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '500',
    color: colors.sage600,
    marginBottom: getResponsiveSpacing(20),
  },
  modalButtons: {
    flexDirection: 'row',
    gap: getResponsiveSpacing(12),
  },
  modalButton: {
    flex: 1,
  },


});
