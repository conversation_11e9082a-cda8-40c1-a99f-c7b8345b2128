2c07f09551f0049c078f054f16350d92
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.DataLoadingFallback = void 0;
var _react = _interopRequireWildcard(require("react"));
var _reactNative = require("react-native");
var _vectorIcons = require("@expo/vector-icons");
var _ThemeContext = require("../../contexts/ThemeContext");
var _performanceMonitor = require("../../services/performanceMonitor");
var _NetworkErrorFallback = _interopRequireDefault(require("./NetworkErrorFallback"));
var _jsxRuntime = require("react/jsx-runtime");
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
var DataLoadingFallback = exports.DataLoadingFallback = function DataLoadingFallback(_ref) {
  var isLoading = _ref.isLoading,
    isError = _ref.isError,
    _ref$isEmpty = _ref.isEmpty,
    isEmpty = _ref$isEmpty === void 0 ? false : _ref$isEmpty,
    error = _ref.error,
    _ref$retryCount = _ref.retryCount,
    retryCount = _ref$retryCount === void 0 ? 0 : _ref$retryCount,
    _ref$maxRetries = _ref.maxRetries,
    maxRetries = _ref$maxRetries === void 0 ? 3 : _ref$maxRetries,
    _ref$loadingMessage = _ref.loadingMessage,
    loadingMessage = _ref$loadingMessage === void 0 ? 'Loading data...' : _ref$loadingMessage,
    _ref$emptyMessage = _ref.emptyMessage,
    emptyMessage = _ref$emptyMessage === void 0 ? 'No data available' : _ref$emptyMessage,
    _ref$emptyIcon = _ref.emptyIcon,
    emptyIcon = _ref$emptyIcon === void 0 ? 'document-outline' : _ref$emptyIcon,
    onRetry = _ref.onRetry,
    onEmptyAction = _ref.onEmptyAction,
    _ref$emptyActionText = _ref.emptyActionText,
    emptyActionText = _ref$emptyActionText === void 0 ? 'Refresh' : _ref$emptyActionText,
    loadingComponent = _ref.loadingComponent,
    errorComponent = _ref.errorComponent,
    emptyComponent = _ref.emptyComponent,
    _ref$loadingDelay = _ref.loadingDelay,
    loadingDelay = _ref$loadingDelay === void 0 ? 300 : _ref$loadingDelay,
    _ref$showLoadingIndic = _ref.showLoadingIndicator,
    showLoadingIndicator = _ref$showLoadingIndic === void 0 ? true : _ref$showLoadingIndic,
    _ref$testID = _ref.testID,
    testID = _ref$testID === void 0 ? 'data-loading-fallback' : _ref$testID;
  var _useTheme = (0, _ThemeContext.useTheme)(),
    colors = _useTheme.colors;
  (0, _react.useEffect)(function () {
    if (isLoading) {
      var startTime = Date.now();
      return function () {
        var loadTime = Date.now() - startTime;
        if (loadTime > loadingDelay) {
          _performanceMonitor.performanceMonitor.trackUserInteraction('data_loading', loadTime, {
            success: !isError,
            retryCount: retryCount
          });
        }
      };
    }
  }, [isLoading, isError, loadingDelay, retryCount]);
  var handleRetry = function handleRetry() {
    _performanceMonitor.performanceMonitor.trackUserInteraction('data_loading_retry', 0, {
      errorType: (error == null ? void 0 : error.name) || 'DataLoadingError',
      retryCount: retryCount + 1,
      errorMessage: error == null ? void 0 : error.message
    });
    onRetry == null || onRetry();
  };
  var handleEmptyAction = function handleEmptyAction() {
    _performanceMonitor.performanceMonitor.trackUserInteraction('empty_state_action', 0, {
      actionText: emptyActionText
    });
    onEmptyAction == null || onEmptyAction();
  };
  if (isLoading) {
    if (loadingComponent) {
      return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
        children: loadingComponent
      });
    }
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: [styles.container, {
        backgroundColor: colors.background.primary
      }],
      testID: `${testID}-loading`,
      accessibilityLabel: loadingMessage,
      accessibilityRole: "progressbar",
      accessibilityState: {
        busy: true
      },
      children: [showLoadingIndicator && (0, _jsxRuntime.jsx)(_reactNative.ActivityIndicator, {
        size: "large",
        color: colors.primary.default,
        style: styles.loadingIndicator
      }), (0, _jsxRuntime.jsx)(_reactNative.Text, {
        style: [styles.loadingText, {
          color: colors.text.secondary
        }],
        children: loadingMessage
      })]
    });
  }
  if (isError) {
    var _error$message, _error$message2, _error$message3;
    if (errorComponent) {
      return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
        children: errorComponent
      });
    }
    if ((error == null ? void 0 : error.name) === 'NetworkError' || error != null && (_error$message = error.message) != null && _error$message.includes('network') || error != null && (_error$message2 = error.message) != null && _error$message2.includes('connection') || error != null && (_error$message3 = error.message) != null && _error$message3.includes('timeout')) {
      return (0, _jsxRuntime.jsx)(_NetworkErrorFallback.default, {
        error: error,
        onRetry: handleRetry,
        retryCount: retryCount,
        maxRetries: maxRetries,
        testID: `${testID}-network-error`
      });
    }
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: [styles.container, {
        backgroundColor: colors.background.primary
      }],
      testID: `${testID}-error`,
      accessibilityLabel: "Error loading data",
      accessibilityRole: "alert",
      children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: [styles.iconContainer, {
          backgroundColor: colors.errorLight
        }],
        children: (0, _jsxRuntime.jsx)(_vectorIcons.Ionicons, {
          name: "alert-circle-outline",
          size: 48,
          color: colors.error
        })
      }), (0, _jsxRuntime.jsx)(_reactNative.Text, {
        style: [styles.errorTitle, {
          color: colors.text.primary
        }],
        children: "Unable to Load Data"
      }), (0, _jsxRuntime.jsx)(_reactNative.Text, {
        style: [styles.errorMessage, {
          color: colors.text.secondary
        }],
        children: (error == null ? void 0 : error.message) || 'Something went wrong while loading the data.'
      }), retryCount < maxRetries && (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        style: [styles.retryButton, {
          backgroundColor: colors.interactive.primary.default
        }],
        onPress: handleRetry,
        testID: `${testID}-retry-button`,
        accessibilityRole: "button",
        accessibilityLabel: "Retry loading data",
        children: [(0, _jsxRuntime.jsx)(_vectorIcons.Ionicons, {
          name: "refresh-outline",
          size: 20,
          color: colors.interactive.primary.text,
          style: styles.buttonIcon
        }), (0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: [styles.buttonText, {
            color: colors.interactive.primary.text
          }],
          children: "Try Again"
        })]
      }), __DEV__ && error && (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.debugContainer,
        children: (0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: [styles.debugText, {
            color: colors.text.tertiary
          }],
          children: error.stack || error.message
        })
      })]
    });
  }
  if (isEmpty) {
    if (emptyComponent) {
      return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
        children: emptyComponent
      });
    }
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: [styles.container, {
        backgroundColor: colors.background.primary
      }],
      testID: `${testID}-empty`,
      accessibilityLabel: emptyMessage,
      accessibilityRole: "text",
      children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: [styles.iconContainer, {
          backgroundColor: colors.background.secondary
        }],
        children: (0, _jsxRuntime.jsx)(_vectorIcons.Ionicons, {
          name: emptyIcon,
          size: 48,
          color: colors.text.secondary
        })
      }), (0, _jsxRuntime.jsx)(_reactNative.Text, {
        style: [styles.emptyText, {
          color: colors.text.secondary
        }],
        children: emptyMessage
      }), onEmptyAction && (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        style: [styles.emptyActionButton, {
          backgroundColor: colors.interactive.secondary.default,
          borderColor: colors.interactive.secondary.border
        }],
        onPress: handleEmptyAction,
        testID: `${testID}-empty-action-button`,
        accessibilityRole: "button",
        accessibilityLabel: emptyActionText,
        children: (0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: [styles.emptyActionText, {
            color: colors.interactive.secondary.text
          }],
          children: emptyActionText
        })
      })]
    });
  }
  return null;
};
var styles = _reactNative.StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24
  },
  loadingIndicator: {
    marginBottom: 16
  },
  loadingText: {
    fontSize: 16,
    textAlign: 'center'
  },
  iconContainer: {
    width: 96,
    height: 96,
    borderRadius: 48,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center'
  },
  errorMessage: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
    maxWidth: 300,
    lineHeight: 22
  },
  retryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8
  },
  buttonIcon: {
    marginRight: 8
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '500'
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
    maxWidth: 300,
    lineHeight: 22
  },
  emptyActionButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    borderWidth: 1
  },
  emptyActionText: {
    fontSize: 16,
    fontWeight: '500'
  },
  debugContainer: {
    marginTop: 24,
    padding: 12,
    borderRadius: 8,
    maxWidth: '100%',
    maxHeight: 200,
    overflow: 'scroll'
  },
  debugText: {
    fontSize: 12,
    fontFamily: 'monospace'
  }
});
var _default = exports.default = DataLoadingFallback;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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