14eb1fcf00d543953835f6cedd6ad89e
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _Platform = _interopRequireDefault(require("../Utilities/Platform"));
var _RCTDeviceEventEmitter = _interopRequireDefault(require("./RCTDeviceEventEmitter"));
var _invariant = _interopRequireDefault(require("invariant"));
var NativeEventEmitter = exports.default = function () {
  function NativeEventEmitter(nativeModule) {
    (0, _classCallCheck2.default)(this, NativeEventEmitter);
    if (_Platform.default.OS === 'ios') {
      (0, _invariant.default)(nativeModule != null, '`new NativeEventEmitter()` requires a non-null argument.');
    }
    var hasAddListener = !!nativeModule && typeof nativeModule.addListener === 'function';
    var hasRemoveListeners = !!nativeModule && typeof nativeModule.removeListeners === 'function';
    if (nativeModule && hasAddListener && hasRemoveListeners) {
      this._nativeModule = nativeModule;
    } else if (nativeModule != null) {
      if (!hasAddListener) {
        console.warn('`new NativeEventEmitter()` was called with a non-null argument without the required `addListener` method.');
      }
      if (!hasRemoveListeners) {
        console.warn('`new NativeEventEmitter()` was called with a non-null argument without the required `removeListeners` method.');
      }
    }
  }
  return (0, _createClass2.default)(NativeEventEmitter, [{
    key: "addListener",
    value: function addListener(eventType, listener, context) {
      var _this$_nativeModule,
        _this = this;
      (_this$_nativeModule = this._nativeModule) == null || _this$_nativeModule.addListener(eventType);
      var subscription = _RCTDeviceEventEmitter.default.addListener(eventType, listener, context);
      return {
        remove: function remove() {
          if (subscription != null) {
            var _this$_nativeModule2;
            (_this$_nativeModule2 = _this._nativeModule) == null || _this$_nativeModule2.removeListeners(1);
            subscription.remove();
            subscription = null;
          }
        }
      };
    }
  }, {
    key: "emit",
    value: function emit(eventType) {
      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
        args[_key - 1] = arguments[_key];
      }
      _RCTDeviceEventEmitter.default.emit.apply(_RCTDeviceEventEmitter.default, [eventType].concat(args));
    }
  }, {
    key: "removeAllListeners",
    value: function removeAllListeners(eventType) {
      var _this$_nativeModule3;
      (0, _invariant.default)(eventType != null, '`NativeEventEmitter.removeAllListener()` requires a non-null argument.');
      (_this$_nativeModule3 = this._nativeModule) == null || _this$_nativeModule3.removeListeners(this.listenerCount(eventType));
      _RCTDeviceEventEmitter.default.removeAllListeners(eventType);
    }
  }, {
    key: "listenerCount",
    value: function listenerCount(eventType) {
      return _RCTDeviceEventEmitter.default.listenerCount(eventType);
    }
  }]);
}();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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