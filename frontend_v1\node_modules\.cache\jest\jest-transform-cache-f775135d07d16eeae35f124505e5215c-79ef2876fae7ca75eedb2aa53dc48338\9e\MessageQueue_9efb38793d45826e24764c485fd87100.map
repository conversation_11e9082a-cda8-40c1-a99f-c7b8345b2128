{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "default", "_toConsumableArray2", "_classCallCheck2", "_createClass2", "Systrace", "deepFreezeAndThrowOnMutationInDev", "stringifySafe", "warnOnce", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "invariant", "TO_JS", "TO_NATIVE", "MODULE_IDS", "METHOD_IDS", "PARAMS", "MIN_TIME_BETWEEN_FLUSHES_MS", "TRACE_TAG_REACT_APPS", "DEBUG_INFO_LIMIT", "MessageQueue", "_lazyCallableModules", "_queue", "_successCallbacks", "Map", "_failureCallbacks", "_callID", "_lastFlush", "_eventLoopStartTime", "Date", "now", "_reactNativeMicrotasksCallback", "__DEV__", "_debugInfo", "_remoteModuleTable", "_remoteMethodTable", "callFunctionReturnFlushedQueue", "bind", "flushedQueue", "invokeCallbackAndReturnFlushedQueue", "key", "module", "method", "args", "_this", "__guard", "__callFunction", "cbID", "_this2", "__invokeCallback", "_this3", "__callReactNativeMicrotasks", "queue", "length", "getEventLoopRunningTime", "registerCallableModule", "name", "registerLazyCallableModule", "factory", "getValue", "getCallableModule", "callNativeSyncHook", "moduleID", "methodID", "params", "onFail", "onSucc", "global", "nativeCallSyncHook", "processCallbacks", "_this4", "size", "info", "for<PERSON>ach", "_", "callID", "debug", "push", "set", "nativeTraceBeginAsyncFlow", "enqueueNativeCall", "isValidArgument", "val", "isFinite", "Array", "isArray", "every", "k", "replacer", "t", "toString", "JSON", "stringify", "nativeFlushQueueImmediate", "counterEvent", "__spy", "type", "createDebugLookup", "methods", "setReactNativeMicrotasksCallback", "fn", "__shouldPauseOnThrow", "error", "reportFatalError", "DebuggerInternal", "shouldPauseOnThrow", "beginEvent", "endEvent", "moduleMethods", "callableModuleNames", "keys", "n", "callableModuleNameList", "join", "isBridgelessMode", "RN$Bridgeless", "apply", "isSuccess", "callback", "get", "profileName", "delete", "spy", "spyOrToggle", "prototype", "console", "log", "_default"], "sources": ["MessageQueue.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict\n * @format\n */\n\n'use strict';\n\nconst Systrace = require('../Performance/Systrace');\nconst deepFreezeAndThrowOnMutationInDev =\n  require('../Utilities/deepFreezeAndThrowOnMutationInDev').default;\nconst stringifySafe = require('../Utilities/stringifySafe').default;\nconst warnOnce = require('../Utilities/warnOnce').default;\nconst ErrorUtils = require('../vendor/core/ErrorUtils').default;\nconst invariant = require('invariant');\n\nexport type SpyData = {\n  type: number,\n  module: ?string,\n  method: string | number,\n  args: mixed[],\n  ...\n};\n\nconst TO_JS = 0;\nconst TO_NATIVE = 1;\n\nconst MODULE_IDS = 0;\nconst METHOD_IDS = 1;\nconst PARAMS = 2;\nconst MIN_TIME_BETWEEN_FLUSHES_MS = 5;\n\n// eslint-disable-next-line no-bitwise\nconst TRACE_TAG_REACT_APPS = 1 << 17;\n\nconst DEBUG_INFO_LIMIT = 32;\n\nclass MessageQueue {\n  _lazyCallableModules: {[key: string]: (void) => {...}, ...};\n  _queue: [number[], number[], mixed[], number];\n  _successCallbacks: Map<number, ?(...mixed[]) => void>;\n  _failureCallbacks: Map<number, ?(...mixed[]) => void>;\n  _callID: number;\n  _lastFlush: number;\n  _eventLoopStartTime: number;\n  _reactNativeMicrotasksCallback: ?() => void;\n\n  _debugInfo: {[number]: [number, number], ...};\n  _remoteModuleTable: {[number]: string, ...};\n  _remoteMethodTable: {[number]: $ReadOnlyArray<string>, ...};\n\n  __spy: ?(data: SpyData) => void;\n\n  constructor() {\n    this._lazyCallableModules = {};\n    this._queue = [[], [], [], 0];\n    this._successCallbacks = new Map();\n    this._failureCallbacks = new Map();\n    this._callID = 0;\n    this._lastFlush = 0;\n    this._eventLoopStartTime = Date.now();\n    this._reactNativeMicrotasksCallback = null;\n\n    if (__DEV__) {\n      this._debugInfo = {};\n      this._remoteModuleTable = {};\n      this._remoteMethodTable = {};\n    }\n\n    // $FlowFixMe[cannot-write]\n    this.callFunctionReturnFlushedQueue =\n      // $FlowFixMe[method-unbinding] added when improving typing for this parameters\n      this.callFunctionReturnFlushedQueue.bind(this);\n    // $FlowFixMe[cannot-write]\n    // $FlowFixMe[method-unbinding] added when improving typing for this parameters\n    this.flushedQueue = this.flushedQueue.bind(this);\n\n    // $FlowFixMe[cannot-write]\n    this.invokeCallbackAndReturnFlushedQueue =\n      // $FlowFixMe[method-unbinding] added when improving typing for this parameters\n      this.invokeCallbackAndReturnFlushedQueue.bind(this);\n  }\n\n  /**\n   * Public APIs\n   */\n\n  static spy(spyOrToggle: boolean | ((data: SpyData) => void)) {\n    if (spyOrToggle === true) {\n      MessageQueue.prototype.__spy = info => {\n        console.log(\n          `${info.type === TO_JS ? 'N->JS' : 'JS->N'} : ` +\n            `${info.module != null ? info.module + '.' : ''}${info.method}` +\n            `(${JSON.stringify(info.args)})`,\n        );\n      };\n    } else if (spyOrToggle === false) {\n      MessageQueue.prototype.__spy = null;\n    } else {\n      MessageQueue.prototype.__spy = spyOrToggle;\n    }\n  }\n\n  callFunctionReturnFlushedQueue(\n    module: string,\n    method: string,\n    args: mixed[],\n  ): null | [Array<number>, Array<number>, Array<mixed>, number] {\n    this.__guard(() => {\n      this.__callFunction(module, method, args);\n    });\n\n    return this.flushedQueue();\n  }\n\n  invokeCallbackAndReturnFlushedQueue(\n    cbID: number,\n    args: mixed[],\n  ): null | [Array<number>, Array<number>, Array<mixed>, number] {\n    this.__guard(() => {\n      this.__invokeCallback(cbID, args);\n    });\n\n    return this.flushedQueue();\n  }\n\n  flushedQueue(): null | [Array<number>, Array<number>, Array<mixed>, number] {\n    this.__guard(() => {\n      this.__callReactNativeMicrotasks();\n    });\n\n    const queue = this._queue;\n    this._queue = [[], [], [], this._callID];\n    return queue[0].length ? queue : null;\n  }\n\n  getEventLoopRunningTime(): number {\n    return Date.now() - this._eventLoopStartTime;\n  }\n\n  registerCallableModule(name: string, module: {...}) {\n    this._lazyCallableModules[name] = () => module;\n  }\n\n  registerLazyCallableModule(name: string, factory: void => interface {}) {\n    let module: interface {};\n    let getValue: ?(void) => interface {} = factory;\n    this._lazyCallableModules[name] = () => {\n      if (getValue) {\n        module = getValue();\n        getValue = null;\n      }\n      /* $FlowFixMe[class-object-subtyping] added when improving typing for\n       * this parameters */\n      return module;\n    };\n  }\n\n  getCallableModule(name: string): {...} | null {\n    const getValue = this._lazyCallableModules[name];\n    return getValue ? getValue() : null;\n  }\n\n  callNativeSyncHook(\n    moduleID: number,\n    methodID: number,\n    params: mixed[],\n    onFail: ?(...mixed[]) => void,\n    onSucc: ?(...mixed[]) => void,\n  ): mixed {\n    if (__DEV__) {\n      invariant(\n        global.nativeCallSyncHook,\n        'Calling synchronous methods on native ' +\n          'modules is not supported in Chrome.\\n\\n Consider providing alternative ' +\n          'methods to expose this method in debug mode, e.g. by exposing constants ' +\n          'ahead-of-time.',\n      );\n    }\n    this.processCallbacks(moduleID, methodID, params, onFail, onSucc);\n    return global.nativeCallSyncHook(moduleID, methodID, params);\n  }\n\n  processCallbacks(\n    moduleID: number,\n    methodID: number,\n    params: mixed[],\n    onFail: ?(...mixed[]) => void,\n    onSucc: ?(...mixed[]) => void,\n  ): void {\n    if (onFail || onSucc) {\n      if (__DEV__) {\n        this._debugInfo[this._callID] = [moduleID, methodID];\n        if (this._callID > DEBUG_INFO_LIMIT) {\n          delete this._debugInfo[this._callID - DEBUG_INFO_LIMIT];\n        }\n        if (this._successCallbacks.size > 500) {\n          const info: {[number]: {method: string, module: string}} = {};\n          this._successCallbacks.forEach((_, callID) => {\n            const debug = this._debugInfo[callID];\n            const module = debug && this._remoteModuleTable[debug[0]];\n            const method = debug && this._remoteMethodTable[debug[0]][debug[1]];\n            info[callID] = {module, method};\n          });\n          warnOnce(\n            'excessive-number-of-pending-callbacks',\n            `Excessive number of pending callbacks: ${\n              this._successCallbacks.size\n            }. Some pending callbacks that might have leaked by never being called from native code: ${stringifySafe(\n              info,\n            )}`,\n          );\n        }\n      }\n      // Encode callIDs into pairs of callback identifiers by shifting left and using the rightmost bit\n      // to indicate fail (0) or success (1)\n      // eslint-disable-next-line no-bitwise\n      onFail && params.push(this._callID << 1);\n      // eslint-disable-next-line no-bitwise\n      onSucc && params.push((this._callID << 1) | 1);\n      this._successCallbacks.set(this._callID, onSucc);\n      this._failureCallbacks.set(this._callID, onFail);\n    }\n    if (__DEV__) {\n      global.nativeTraceBeginAsyncFlow &&\n        global.nativeTraceBeginAsyncFlow(\n          TRACE_TAG_REACT_APPS,\n          'native',\n          this._callID,\n        );\n    }\n    this._callID++;\n  }\n\n  enqueueNativeCall(\n    moduleID: number,\n    methodID: number,\n    params: mixed[],\n    onFail: ?(...mixed[]) => void,\n    onSucc: ?(...mixed[]) => void,\n  ): void {\n    this.processCallbacks(moduleID, methodID, params, onFail, onSucc);\n\n    this._queue[MODULE_IDS].push(moduleID);\n    this._queue[METHOD_IDS].push(methodID);\n\n    if (__DEV__) {\n      // Validate that parameters passed over the bridge are\n      // folly-convertible.  As a special case, if a prop value is a\n      // function it is permitted here, and special-cased in the\n      // conversion.\n      const isValidArgument = (val: mixed): boolean => {\n        switch (typeof val) {\n          case 'undefined':\n          case 'boolean':\n          case 'string':\n            return true;\n          case 'number':\n            return isFinite(val);\n          case 'object':\n            if (val == null) {\n              return true;\n            }\n\n            if (Array.isArray(val)) {\n              return val.every(isValidArgument);\n            }\n\n            for (const k in val) {\n              if (typeof val[k] !== 'function' && !isValidArgument(val[k])) {\n                return false;\n              }\n            }\n\n            return true;\n          case 'function':\n            return false;\n          default:\n            return false;\n        }\n      };\n\n      // Replacement allows normally non-JSON-convertible values to be\n      // seen.  There is ambiguity with string values, but in context,\n      // it should at least be a strong hint.\n      const replacer = (key: string, val: $FlowFixMe) => {\n        const t = typeof val;\n        if (t === 'function') {\n          return '<<Function ' + val.name + '>>';\n        } else if (t === 'number' && !isFinite(val)) {\n          return '<<' + val.toString() + '>>';\n        } else {\n          return val;\n        }\n      };\n\n      // Note that JSON.stringify\n      invariant(\n        isValidArgument(params),\n        '%s is not usable as a native method argument',\n        JSON.stringify(params, replacer),\n      );\n\n      // The params object should not be mutated after being queued\n      deepFreezeAndThrowOnMutationInDev(params);\n    }\n    this._queue[PARAMS].push(params);\n\n    const now = Date.now();\n    if (\n      global.nativeFlushQueueImmediate &&\n      now - this._lastFlush >= MIN_TIME_BETWEEN_FLUSHES_MS\n    ) {\n      const queue = this._queue;\n      this._queue = [[], [], [], this._callID];\n      this._lastFlush = now;\n      global.nativeFlushQueueImmediate(queue);\n    }\n    Systrace.counterEvent('pending_js_to_native_queue', this._queue[0].length);\n    if (__DEV__ && this.__spy && isFinite(moduleID)) {\n      // $FlowFixMe[not-a-function]\n      this.__spy({\n        type: TO_NATIVE,\n        module: this._remoteModuleTable[moduleID],\n        method: this._remoteMethodTable[moduleID][methodID],\n        args: params,\n      });\n    } else if (this.__spy) {\n      this.__spy({\n        type: TO_NATIVE,\n        module: moduleID + '',\n        method: methodID,\n        args: params,\n      });\n    }\n  }\n\n  createDebugLookup(\n    moduleID: number,\n    name: string,\n    methods: ?$ReadOnlyArray<string>,\n  ) {\n    if (__DEV__) {\n      this._remoteModuleTable[moduleID] = name;\n      this._remoteMethodTable[moduleID] = methods || [];\n    }\n  }\n\n  // For JSTimers to register its callback. Otherwise a circular dependency\n  // between modules is introduced. Note that only one callback may be\n  // registered at a time.\n  setReactNativeMicrotasksCallback(fn: () => void) {\n    this._reactNativeMicrotasksCallback = fn;\n  }\n\n  /**\n   * Private methods\n   */\n\n  __guard(fn: () => void) {\n    if (this.__shouldPauseOnThrow()) {\n      fn();\n    } else {\n      try {\n        fn();\n      } catch (error) {\n        ErrorUtils.reportFatalError(error);\n      }\n    }\n  }\n\n  // MessageQueue installs a global handler to catch all exceptions where JS users can register their own behavior\n  // This handler makes all exceptions to be propagated from inside MessageQueue rather than by the VM at their origin\n  // This makes stacktraces to be placed at MessageQueue rather than at where they were launched\n  // The parameter DebuggerInternal.shouldPauseOnThrow is used to check before catching all exceptions and\n  // can be configured by the VM or any Inspector\n  __shouldPauseOnThrow(): boolean {\n    return (\n      // $FlowFixMe[cannot-resolve-name]\n      typeof DebuggerInternal !== 'undefined' &&\n      // $FlowFixMe[cannot-resolve-name]\n      DebuggerInternal.shouldPauseOnThrow === true\n    );\n  }\n\n  __callReactNativeMicrotasks() {\n    Systrace.beginEvent('JSTimers.callReactNativeMicrotasks()');\n    try {\n      if (this._reactNativeMicrotasksCallback != null) {\n        this._reactNativeMicrotasksCallback();\n      }\n    } finally {\n      Systrace.endEvent();\n    }\n  }\n\n  __callFunction(module: string, method: string, args: mixed[]): void {\n    this._lastFlush = Date.now();\n    this._eventLoopStartTime = this._lastFlush;\n    if (__DEV__ || this.__spy) {\n      Systrace.beginEvent(`${module}.${method}(${stringifySafe(args)})`);\n    } else {\n      Systrace.beginEvent(`${module}.${method}(...)`);\n    }\n    try {\n      if (this.__spy) {\n        this.__spy({type: TO_JS, module, method, args});\n      }\n      const moduleMethods = this.getCallableModule(module);\n      if (!moduleMethods) {\n        const callableModuleNames = Object.keys(this._lazyCallableModules);\n        const n = callableModuleNames.length;\n        const callableModuleNameList = callableModuleNames.join(', ');\n\n        // TODO(T122225939): Remove after investigation: Why are we getting to this line in bridgeless mode?\n        const isBridgelessMode =\n          global.RN$Bridgeless === true ? 'true' : 'false';\n        invariant(\n          false,\n          `Failed to call into JavaScript module method ${module}.${method}(). Module has not been registered as callable. Bridgeless Mode: ${isBridgelessMode}. Registered callable JavaScript modules (n = ${n}): ${callableModuleNameList}.\n          A frequent cause of the error is that the application entry file path is incorrect. This can also happen when the JS bundle is corrupt or there is an early initialization error when loading React Native.`,\n        );\n      }\n      // $FlowFixMe[invalid-computed-prop]\n      if (!moduleMethods[method]) {\n        invariant(\n          false,\n          `Failed to call into JavaScript module method ${module}.${method}(). Module exists, but the method is undefined.`,\n        );\n      }\n      moduleMethods[method].apply(moduleMethods, args);\n    } finally {\n      Systrace.endEvent();\n    }\n  }\n\n  __invokeCallback(cbID: number, args: mixed[]): void {\n    this._lastFlush = Date.now();\n    this._eventLoopStartTime = this._lastFlush;\n\n    // The rightmost bit of cbID indicates fail (0) or success (1), the other bits are the callID shifted left.\n    // eslint-disable-next-line no-bitwise\n    const callID = cbID >>> 1;\n    // eslint-disable-next-line no-bitwise\n    const isSuccess = cbID & 1;\n    const callback = isSuccess\n      ? this._successCallbacks.get(callID)\n      : this._failureCallbacks.get(callID);\n\n    if (__DEV__) {\n      const debug = this._debugInfo[callID];\n      const module = debug && this._remoteModuleTable[debug[0]];\n      const method = debug && this._remoteMethodTable[debug[0]][debug[1]];\n      invariant(\n        callback,\n        `No callback found with cbID ${cbID} and callID ${callID} for ` +\n          (method\n            ? ` ${module}.${method} - most likely the callback was already invoked`\n            : `module ${module || '<unknown>'}`) +\n          `. Args: '${stringifySafe(args)}'`,\n      );\n      const profileName = debug\n        ? '<callback for ' + module + '.' + method + '>'\n        : cbID;\n      if (callback && this.__spy) {\n        this.__spy({type: TO_JS, module: null, method: profileName, args});\n      }\n      Systrace.beginEvent(\n        `MessageQueue.invokeCallback(${profileName}, ${stringifySafe(args)})`,\n      );\n    }\n\n    try {\n      if (!callback) {\n        return;\n      }\n\n      this._successCallbacks.delete(callID);\n      this._failureCallbacks.delete(callID);\n      callback(...args);\n    } finally {\n      if (__DEV__) {\n        Systrace.endEvent();\n      }\n    }\n  }\n}\n\nexport default MessageQueue;\n"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAAA,IAAAC,mBAAA,GAAAP,sBAAA,CAAAC,OAAA;AAAA,IAAAO,gBAAA,GAAAR,sBAAA,CAAAC,OAAA;AAAA,IAAAQ,aAAA,GAAAT,sBAAA,CAAAC,OAAA;AAEb,IAAMS,QAAQ,GAAGT,OAAO,0BAA0B,CAAC;AACnD,IAAMU,iCAAiC,GACrCV,OAAO,iDAAiD,CAAC,CAACK,OAAO;AACnE,IAAMM,aAAa,GAAGX,OAAO,6BAA6B,CAAC,CAACK,OAAO;AACnE,IAAMO,QAAQ,GAAGZ,OAAO,wBAAwB,CAAC,CAACK,OAAO;AACzD,IAAMQ,UAAU,GAAGb,OAAO,4BAA4B,CAAC,CAACK,OAAO;AAC/D,IAAMS,SAAS,GAAGd,OAAO,CAAC,WAAW,CAAC;AAUtC,IAAMe,KAAK,GAAG,CAAC;AACf,IAAMC,SAAS,GAAG,CAAC;AAEnB,IAAMC,UAAU,GAAG,CAAC;AACpB,IAAMC,UAAU,GAAG,CAAC;AACpB,IAAMC,MAAM,GAAG,CAAC;AAChB,IAAMC,2BAA2B,GAAG,CAAC;AAGrC,IAAMC,oBAAoB,GAAG,CAAC,IAAI,EAAE;AAEpC,IAAMC,gBAAgB,GAAG,EAAE;AAAC,IAEtBC,YAAY;EAgBhB,SAAAA,aAAA,EAAc;IAAA,IAAAhB,gBAAA,CAAAF,OAAA,QAAAkB,YAAA;IACZ,IAAI,CAACC,oBAAoB,GAAG,CAAC,CAAC;IAC9B,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;IAC7B,IAAI,CAACC,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAAC;IAClC,IAAI,CAACC,iBAAiB,GAAG,IAAID,GAAG,CAAC,CAAC;IAClC,IAAI,CAACE,OAAO,GAAG,CAAC;IAChB,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,mBAAmB,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IACrC,IAAI,CAACC,8BAA8B,GAAG,IAAI;IAE1C,IAAIC,OAAO,EAAE;MACX,IAAI,CAACC,UAAU,GAAG,CAAC,CAAC;MACpB,IAAI,CAACC,kBAAkB,GAAG,CAAC,CAAC;MAC5B,IAAI,CAACC,kBAAkB,GAAG,CAAC,CAAC;IAC9B;IAGA,IAAI,CAACC,8BAA8B,GAEjC,IAAI,CAACA,8BAA8B,CAACC,IAAI,CAAC,IAAI,CAAC;IAGhD,IAAI,CAACC,YAAY,GAAG,IAAI,CAACA,YAAY,CAACD,IAAI,CAAC,IAAI,CAAC;IAGhD,IAAI,CAACE,mCAAmC,GAEtC,IAAI,CAACA,mCAAmC,CAACF,IAAI,CAAC,IAAI,CAAC;EACvD;EAAC,WAAAhC,aAAA,CAAAH,OAAA,EAAAkB,YAAA;IAAAoB,GAAA;IAAAvC,KAAA,EAsBD,SAAAmC,8BAA8BA,CAC5BK,MAAc,EACdC,MAAc,EACdC,IAAa,EACgD;MAAA,IAAAC,KAAA;MAC7D,IAAI,CAACC,OAAO,CAAC,YAAM;QACjBD,KAAI,CAACE,cAAc,CAACL,MAAM,EAAEC,MAAM,EAAEC,IAAI,CAAC;MAC3C,CAAC,CAAC;MAEF,OAAO,IAAI,CAACL,YAAY,CAAC,CAAC;IAC5B;EAAC;IAAAE,GAAA;IAAAvC,KAAA,EAED,SAAAsC,mCAAmCA,CACjCQ,IAAY,EACZJ,IAAa,EACgD;MAAA,IAAAK,MAAA;MAC7D,IAAI,CAACH,OAAO,CAAC,YAAM;QACjBG,MAAI,CAACC,gBAAgB,CAACF,IAAI,EAAEJ,IAAI,CAAC;MACnC,CAAC,CAAC;MAEF,OAAO,IAAI,CAACL,YAAY,CAAC,CAAC;IAC5B;EAAC;IAAAE,GAAA;IAAAvC,KAAA,EAED,SAAAqC,YAAYA,CAAA,EAAgE;MAAA,IAAAY,MAAA;MAC1E,IAAI,CAACL,OAAO,CAAC,YAAM;QACjBK,MAAI,CAACC,2BAA2B,CAAC,CAAC;MACpC,CAAC,CAAC;MAEF,IAAMC,KAAK,GAAG,IAAI,CAAC9B,MAAM;MACzB,IAAI,CAACA,MAAM,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,CAACI,OAAO,CAAC;MACxC,OAAO0B,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,GAAGD,KAAK,GAAG,IAAI;IACvC;EAAC;IAAAZ,GAAA;IAAAvC,KAAA,EAED,SAAAqD,uBAAuBA,CAAA,EAAW;MAChC,OAAOzB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACF,mBAAmB;IAC9C;EAAC;IAAAY,GAAA;IAAAvC,KAAA,EAED,SAAAsD,sBAAsBA,CAACC,IAAY,EAAEf,MAAa,EAAE;MAClD,IAAI,CAACpB,oBAAoB,CAACmC,IAAI,CAAC,GAAG;QAAA,OAAMf,MAAM;MAAA;IAChD;EAAC;IAAAD,GAAA;IAAAvC,KAAA,EAED,SAAAwD,0BAA0BA,CAACD,IAAY,EAAEE,OAA6B,EAAE;MACtE,IAAIjB,MAAoB;MACxB,IAAIkB,QAAiC,GAAGD,OAAO;MAC/C,IAAI,CAACrC,oBAAoB,CAACmC,IAAI,CAAC,GAAG,YAAM;QACtC,IAAIG,QAAQ,EAAE;UACZlB,MAAM,GAAGkB,QAAQ,CAAC,CAAC;UACnBA,QAAQ,GAAG,IAAI;QACjB;QAGA,OAAOlB,MAAM;MACf,CAAC;IACH;EAAC;IAAAD,GAAA;IAAAvC,KAAA,EAED,SAAA2D,iBAAiBA,CAACJ,IAAY,EAAgB;MAC5C,IAAMG,QAAQ,GAAG,IAAI,CAACtC,oBAAoB,CAACmC,IAAI,CAAC;MAChD,OAAOG,QAAQ,GAAGA,QAAQ,CAAC,CAAC,GAAG,IAAI;IACrC;EAAC;IAAAnB,GAAA;IAAAvC,KAAA,EAED,SAAA4D,kBAAkBA,CAChBC,QAAgB,EAChBC,QAAgB,EAChBC,MAAe,EACfC,MAA6B,EAC7BC,MAA6B,EACtB;MACP,IAAIlC,OAAO,EAAE;QACXrB,SAAS,CACPwD,MAAM,CAACC,kBAAkB,EACzB,wCAAwC,GACtC,yEAAyE,GACzE,0EAA0E,GAC1E,gBACJ,CAAC;MACH;MACA,IAAI,CAACC,gBAAgB,CAACP,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,CAAC;MACjE,OAAOC,MAAM,CAACC,kBAAkB,CAACN,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,CAAC;IAC9D;EAAC;IAAAxB,GAAA;IAAAvC,KAAA,EAED,SAAAoE,gBAAgBA,CACdP,QAAgB,EAChBC,QAAgB,EAChBC,MAAe,EACfC,MAA6B,EAC7BC,MAA6B,EACvB;MAAA,IAAAI,MAAA;MACN,IAAIL,MAAM,IAAIC,MAAM,EAAE;QACpB,IAAIlC,OAAO,EAAE;UACX,IAAI,CAACC,UAAU,CAAC,IAAI,CAACP,OAAO,CAAC,GAAG,CAACoC,QAAQ,EAAEC,QAAQ,CAAC;UACpD,IAAI,IAAI,CAACrC,OAAO,GAAGP,gBAAgB,EAAE;YACnC,OAAO,IAAI,CAACc,UAAU,CAAC,IAAI,CAACP,OAAO,GAAGP,gBAAgB,CAAC;UACzD;UACA,IAAI,IAAI,CAACI,iBAAiB,CAACgD,IAAI,GAAG,GAAG,EAAE;YACrC,IAAMC,IAAkD,GAAG,CAAC,CAAC;YAC7D,IAAI,CAACjD,iBAAiB,CAACkD,OAAO,CAAC,UAACC,CAAC,EAAEC,MAAM,EAAK;cAC5C,IAAMC,KAAK,GAAGN,MAAI,CAACrC,UAAU,CAAC0C,MAAM,CAAC;cACrC,IAAMlC,MAAM,GAAGmC,KAAK,IAAIN,MAAI,CAACpC,kBAAkB,CAAC0C,KAAK,CAAC,CAAC,CAAC,CAAC;cACzD,IAAMlC,MAAM,GAAGkC,KAAK,IAAIN,MAAI,CAACnC,kBAAkB,CAACyC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,CAAC,CAAC,CAAC;cACnEJ,IAAI,CAACG,MAAM,CAAC,GAAG;gBAAClC,MAAM,EAANA,MAAM;gBAAEC,MAAM,EAANA;cAAM,CAAC;YACjC,CAAC,CAAC;YACFjC,QAAQ,CACN,uCAAuC,EACvC,0CACE,IAAI,CAACc,iBAAiB,CAACgD,IAAI,2FAC8D/D,aAAa,CACtGgE,IACF,CAAC,EACH,CAAC;UACH;QACF;QAIAP,MAAM,IAAID,MAAM,CAACa,IAAI,CAAC,IAAI,CAACnD,OAAO,IAAI,CAAC,CAAC;QAExCwC,MAAM,IAAIF,MAAM,CAACa,IAAI,CAAE,IAAI,CAACnD,OAAO,IAAI,CAAC,GAAI,CAAC,CAAC;QAC9C,IAAI,CAACH,iBAAiB,CAACuD,GAAG,CAAC,IAAI,CAACpD,OAAO,EAAEwC,MAAM,CAAC;QAChD,IAAI,CAACzC,iBAAiB,CAACqD,GAAG,CAAC,IAAI,CAACpD,OAAO,EAAEuC,MAAM,CAAC;MAClD;MACA,IAAIjC,OAAO,EAAE;QACXmC,MAAM,CAACY,yBAAyB,IAC9BZ,MAAM,CAACY,yBAAyB,CAC9B7D,oBAAoB,EACpB,QAAQ,EACR,IAAI,CAACQ,OACP,CAAC;MACL;MACA,IAAI,CAACA,OAAO,EAAE;IAChB;EAAC;IAAAc,GAAA;IAAAvC,KAAA,EAED,SAAA+E,iBAAiBA,CACflB,QAAgB,EAChBC,QAAgB,EAChBC,MAAe,EACfC,MAA6B,EAC7BC,MAA6B,EACvB;MACN,IAAI,CAACG,gBAAgB,CAACP,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,CAAC;MAEjE,IAAI,CAAC5C,MAAM,CAACR,UAAU,CAAC,CAAC+D,IAAI,CAACf,QAAQ,CAAC;MACtC,IAAI,CAACxC,MAAM,CAACP,UAAU,CAAC,CAAC8D,IAAI,CAACd,QAAQ,CAAC;MAEtC,IAAI/B,OAAO,EAAE;QAKX,IAAMiD,gBAAe,GAAG,SAAlBA,eAAeA,CAAIC,GAAU,EAAc;UAC/C,QAAQ,OAAOA,GAAG;YAChB,KAAK,WAAW;YAChB,KAAK,SAAS;YACd,KAAK,QAAQ;cACX,OAAO,IAAI;YACb,KAAK,QAAQ;cACX,OAAOC,QAAQ,CAACD,GAAG,CAAC;YACtB,KAAK,QAAQ;cACX,IAAIA,GAAG,IAAI,IAAI,EAAE;gBACf,OAAO,IAAI;cACb;cAEA,IAAIE,KAAK,CAACC,OAAO,CAACH,GAAG,CAAC,EAAE;gBACtB,OAAOA,GAAG,CAACI,KAAK,CAACL,gBAAe,CAAC;cACnC;cAEA,KAAK,IAAMM,CAAC,IAAIL,GAAG,EAAE;gBACnB,IAAI,OAAOA,GAAG,CAACK,CAAC,CAAC,KAAK,UAAU,IAAI,CAACN,gBAAe,CAACC,GAAG,CAACK,CAAC,CAAC,CAAC,EAAE;kBAC5D,OAAO,KAAK;gBACd;cACF;cAEA,OAAO,IAAI;YACb,KAAK,UAAU;cACb,OAAO,KAAK;YACd;cACE,OAAO,KAAK;UAChB;QACF,CAAC;QAKD,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAIhD,GAAW,EAAE0C,GAAe,EAAK;UACjD,IAAMO,CAAC,GAAG,OAAOP,GAAG;UACpB,IAAIO,CAAC,KAAK,UAAU,EAAE;YACpB,OAAO,aAAa,GAAGP,GAAG,CAAC1B,IAAI,GAAG,IAAI;UACxC,CAAC,MAAM,IAAIiC,CAAC,KAAK,QAAQ,IAAI,CAACN,QAAQ,CAACD,GAAG,CAAC,EAAE;YAC3C,OAAO,IAAI,GAAGA,GAAG,CAACQ,QAAQ,CAAC,CAAC,GAAG,IAAI;UACrC,CAAC,MAAM;YACL,OAAOR,GAAG;UACZ;QACF,CAAC;QAGDvE,SAAS,CACPsE,gBAAe,CAACjB,MAAM,CAAC,EACvB,8CAA8C,EAC9C2B,IAAI,CAACC,SAAS,CAAC5B,MAAM,EAAEwB,QAAQ,CACjC,CAAC;QAGDjF,iCAAiC,CAACyD,MAAM,CAAC;MAC3C;MACA,IAAI,CAAC1C,MAAM,CAACN,MAAM,CAAC,CAAC6D,IAAI,CAACb,MAAM,CAAC;MAEhC,IAAMlC,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;MACtB,IACEqC,MAAM,CAAC0B,yBAAyB,IAChC/D,GAAG,GAAG,IAAI,CAACH,UAAU,IAAIV,2BAA2B,EACpD;QACA,IAAMmC,KAAK,GAAG,IAAI,CAAC9B,MAAM;QACzB,IAAI,CAACA,MAAM,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,CAACI,OAAO,CAAC;QACxC,IAAI,CAACC,UAAU,GAAGG,GAAG;QACrBqC,MAAM,CAAC0B,yBAAyB,CAACzC,KAAK,CAAC;MACzC;MACA9C,QAAQ,CAACwF,YAAY,CAAC,4BAA4B,EAAE,IAAI,CAACxE,MAAM,CAAC,CAAC,CAAC,CAAC+B,MAAM,CAAC;MAC1E,IAAIrB,OAAO,IAAI,IAAI,CAAC+D,KAAK,IAAIZ,QAAQ,CAACrB,QAAQ,CAAC,EAAE;QAE/C,IAAI,CAACiC,KAAK,CAAC;UACTC,IAAI,EAAEnF,SAAS;UACf4B,MAAM,EAAE,IAAI,CAACP,kBAAkB,CAAC4B,QAAQ,CAAC;UACzCpB,MAAM,EAAE,IAAI,CAACP,kBAAkB,CAAC2B,QAAQ,CAAC,CAACC,QAAQ,CAAC;UACnDpB,IAAI,EAAEqB;QACR,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI,IAAI,CAAC+B,KAAK,EAAE;QACrB,IAAI,CAACA,KAAK,CAAC;UACTC,IAAI,EAAEnF,SAAS;UACf4B,MAAM,EAAEqB,QAAQ,GAAG,EAAE;UACrBpB,MAAM,EAAEqB,QAAQ;UAChBpB,IAAI,EAAEqB;QACR,CAAC,CAAC;MACJ;IACF;EAAC;IAAAxB,GAAA;IAAAvC,KAAA,EAED,SAAAgG,iBAAiBA,CACfnC,QAAgB,EAChBN,IAAY,EACZ0C,OAAgC,EAChC;MACA,IAAIlE,OAAO,EAAE;QACX,IAAI,CAACE,kBAAkB,CAAC4B,QAAQ,CAAC,GAAGN,IAAI;QACxC,IAAI,CAACrB,kBAAkB,CAAC2B,QAAQ,CAAC,GAAGoC,OAAO,IAAI,EAAE;MACnD;IACF;EAAC;IAAA1D,GAAA;IAAAvC,KAAA,EAKD,SAAAkG,gCAAgCA,CAACC,EAAc,EAAE;MAC/C,IAAI,CAACrE,8BAA8B,GAAGqE,EAAE;IAC1C;EAAC;IAAA5D,GAAA;IAAAvC,KAAA,EAMD,SAAA4C,OAAOA,CAACuD,EAAc,EAAE;MACtB,IAAI,IAAI,CAACC,oBAAoB,CAAC,CAAC,EAAE;QAC/BD,EAAE,CAAC,CAAC;MACN,CAAC,MAAM;QACL,IAAI;UACFA,EAAE,CAAC,CAAC;QACN,CAAC,CAAC,OAAOE,KAAK,EAAE;UACd5F,UAAU,CAAC6F,gBAAgB,CAACD,KAAK,CAAC;QACpC;MACF;IACF;EAAC;IAAA9D,GAAA;IAAAvC,KAAA,EAOD,SAAAoG,oBAAoBA,CAAA,EAAY;MAC9B,OAEE,OAAOG,gBAAgB,KAAK,WAAW,IAEvCA,gBAAgB,CAACC,kBAAkB,KAAK,IAAI;IAEhD;EAAC;IAAAjE,GAAA;IAAAvC,KAAA,EAED,SAAAkD,2BAA2BA,CAAA,EAAG;MAC5B7C,QAAQ,CAACoG,UAAU,CAAC,sCAAsC,CAAC;MAC3D,IAAI;QACF,IAAI,IAAI,CAAC3E,8BAA8B,IAAI,IAAI,EAAE;UAC/C,IAAI,CAACA,8BAA8B,CAAC,CAAC;QACvC;MACF,CAAC,SAAS;QACRzB,QAAQ,CAACqG,QAAQ,CAAC,CAAC;MACrB;IACF;EAAC;IAAAnE,GAAA;IAAAvC,KAAA,EAED,SAAA6C,cAAcA,CAACL,MAAc,EAAEC,MAAc,EAAEC,IAAa,EAAQ;MAClE,IAAI,CAAChB,UAAU,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC;MAC5B,IAAI,CAACF,mBAAmB,GAAG,IAAI,CAACD,UAAU;MAC1C,IAAIK,OAAO,IAAI,IAAI,CAAC+D,KAAK,EAAE;QACzBzF,QAAQ,CAACoG,UAAU,CAAC,GAAGjE,MAAM,IAAIC,MAAM,IAAIlC,aAAa,CAACmC,IAAI,CAAC,GAAG,CAAC;MACpE,CAAC,MAAM;QACLrC,QAAQ,CAACoG,UAAU,CAAC,GAAGjE,MAAM,IAAIC,MAAM,OAAO,CAAC;MACjD;MACA,IAAI;QACF,IAAI,IAAI,CAACqD,KAAK,EAAE;UACd,IAAI,CAACA,KAAK,CAAC;YAACC,IAAI,EAAEpF,KAAK;YAAE6B,MAAM,EAANA,MAAM;YAAEC,MAAM,EAANA,MAAM;YAAEC,IAAI,EAAJA;UAAI,CAAC,CAAC;QACjD;QACA,IAAMiE,aAAa,GAAG,IAAI,CAAChD,iBAAiB,CAACnB,MAAM,CAAC;QACpD,IAAI,CAACmE,aAAa,EAAE;UAClB,IAAMC,mBAAmB,GAAG/G,MAAM,CAACgH,IAAI,CAAC,IAAI,CAACzF,oBAAoB,CAAC;UAClE,IAAM0F,CAAC,GAAGF,mBAAmB,CAACxD,MAAM;UACpC,IAAM2D,sBAAsB,GAAGH,mBAAmB,CAACI,IAAI,CAAC,IAAI,CAAC;UAG7D,IAAMC,gBAAgB,GACpB/C,MAAM,CAACgD,aAAa,KAAK,IAAI,GAAG,MAAM,GAAG,OAAO;UAClDxG,SAAS,CACP,KAAK,EACL,gDAAgD8B,MAAM,IAAIC,MAAM,oEAAoEwE,gBAAgB,iDAAiDH,CAAC,MAAMC,sBAAsB;AAC5O,sNACQ,CAAC;QACH;QAEA,IAAI,CAACJ,aAAa,CAAClE,MAAM,CAAC,EAAE;UAC1B/B,SAAS,CACP,KAAK,EACL,gDAAgD8B,MAAM,IAAIC,MAAM,iDAClE,CAAC;QACH;QACAkE,aAAa,CAAClE,MAAM,CAAC,CAAC0E,KAAK,CAACR,aAAa,EAAEjE,IAAI,CAAC;MAClD,CAAC,SAAS;QACRrC,QAAQ,CAACqG,QAAQ,CAAC,CAAC;MACrB;IACF;EAAC;IAAAnE,GAAA;IAAAvC,KAAA,EAED,SAAAgD,gBAAgBA,CAACF,IAAY,EAAEJ,IAAa,EAAQ;MAClD,IAAI,CAAChB,UAAU,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC;MAC5B,IAAI,CAACF,mBAAmB,GAAG,IAAI,CAACD,UAAU;MAI1C,IAAMgD,MAAM,GAAG5B,IAAI,KAAK,CAAC;MAEzB,IAAMsE,SAAS,GAAGtE,IAAI,GAAG,CAAC;MAC1B,IAAMuE,QAAQ,GAAGD,SAAS,GACtB,IAAI,CAAC9F,iBAAiB,CAACgG,GAAG,CAAC5C,MAAM,CAAC,GAClC,IAAI,CAAClD,iBAAiB,CAAC8F,GAAG,CAAC5C,MAAM,CAAC;MAEtC,IAAI3C,OAAO,EAAE;QACX,IAAM4C,KAAK,GAAG,IAAI,CAAC3C,UAAU,CAAC0C,MAAM,CAAC;QACrC,IAAMlC,MAAM,GAAGmC,KAAK,IAAI,IAAI,CAAC1C,kBAAkB,CAAC0C,KAAK,CAAC,CAAC,CAAC,CAAC;QACzD,IAAMlC,MAAM,GAAGkC,KAAK,IAAI,IAAI,CAACzC,kBAAkB,CAACyC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,CAAC,CAAC,CAAC;QACnEjE,SAAS,CACP2G,QAAQ,EACR,+BAA+BvE,IAAI,eAAe4B,MAAM,OAAO,IAC5DjC,MAAM,GACH,IAAID,MAAM,IAAIC,MAAM,iDAAiD,GACrE,UAAUD,MAAM,IAAI,WAAW,EAAE,CAAC,GACtC,YAAYjC,aAAa,CAACmC,IAAI,CAAC,GACnC,CAAC;QACD,IAAM6E,WAAW,GAAG5C,KAAK,GACrB,gBAAgB,GAAGnC,MAAM,GAAG,GAAG,GAAGC,MAAM,GAAG,GAAG,GAC9CK,IAAI;QACR,IAAIuE,QAAQ,IAAI,IAAI,CAACvB,KAAK,EAAE;UAC1B,IAAI,CAACA,KAAK,CAAC;YAACC,IAAI,EAAEpF,KAAK;YAAE6B,MAAM,EAAE,IAAI;YAAEC,MAAM,EAAE8E,WAAW;YAAE7E,IAAI,EAAJA;UAAI,CAAC,CAAC;QACpE;QACArC,QAAQ,CAACoG,UAAU,CACjB,+BAA+Bc,WAAW,KAAKhH,aAAa,CAACmC,IAAI,CAAC,GACpE,CAAC;MACH;MAEA,IAAI;QACF,IAAI,CAAC2E,QAAQ,EAAE;UACb;QACF;QAEA,IAAI,CAAC/F,iBAAiB,CAACkG,MAAM,CAAC9C,MAAM,CAAC;QACrC,IAAI,CAAClD,iBAAiB,CAACgG,MAAM,CAAC9C,MAAM,CAAC;QACrC2C,QAAQ,CAAAF,KAAA,aAAAjH,mBAAA,CAAAD,OAAA,EAAIyC,IAAI,EAAC;MACnB,CAAC,SAAS;QACR,IAAIX,OAAO,EAAE;UACX1B,QAAQ,CAACqG,QAAQ,CAAC,CAAC;QACrB;MACF;IACF;EAAC;IAAAnE,GAAA;IAAAvC,KAAA,EA9YD,SAAOyH,GAAGA,CAACC,WAAgD,EAAE;MAC3D,IAAIA,WAAW,KAAK,IAAI,EAAE;QACxBvG,YAAY,CAACwG,SAAS,CAAC7B,KAAK,GAAG,UAAAvB,IAAI,EAAI;UACrCqD,OAAO,CAACC,GAAG,CACT,GAAGtD,IAAI,CAACwB,IAAI,KAAKpF,KAAK,GAAG,OAAO,GAAG,OAAO,KAAK,GAC7C,GAAG4D,IAAI,CAAC/B,MAAM,IAAI,IAAI,GAAG+B,IAAI,CAAC/B,MAAM,GAAG,GAAG,GAAG,EAAE,GAAG+B,IAAI,CAAC9B,MAAM,EAAE,GAC/D,IAAIiD,IAAI,CAACC,SAAS,CAACpB,IAAI,CAAC7B,IAAI,CAAC,GACjC,CAAC;QACH,CAAC;MACH,CAAC,MAAM,IAAIgF,WAAW,KAAK,KAAK,EAAE;QAChCvG,YAAY,CAACwG,SAAS,CAAC7B,KAAK,GAAG,IAAI;MACrC,CAAC,MAAM;QACL3E,YAAY,CAACwG,SAAS,CAAC7B,KAAK,GAAG4B,WAAW;MAC5C;IACF;EAAC;AAAA;AAAA,IAAAI,QAAA,GAAA/H,OAAA,CAAAE,OAAA,GAmYYkB,YAAY", "ignoreList": []}