{"version": 3, "names": ["TurboModuleRegistry", "_interopRequireWildcard", "require", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "NativeModule", "getEnforcing", "constants", "NativeDeviceInfo", "getConstants", "_default", "exports"], "sources": ["NativeDeviceInfo.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict\n * @format\n */\n\nimport type {TurboModule} from '../../../../Libraries/TurboModule/RCTExport';\n\nimport * as TurboModuleRegistry from '../../../../Libraries/TurboModule/TurboModuleRegistry';\n\nexport type DisplayMetricsAndroid = {\n  width: number,\n  height: number,\n  scale: number,\n  fontScale: number,\n  densityDpi: number,\n};\n\nexport type DisplayMetrics = {\n  width: number,\n  height: number,\n  scale: number,\n  fontScale: number,\n};\n\nexport type DimensionsPayload = {\n  window?: DisplayMetrics,\n  screen?: DisplayMetrics,\n  windowPhysicalPixels?: DisplayMetricsAndroid,\n  screenPhysicalPixels?: DisplayMetricsAndroid,\n};\n\nexport type DeviceInfoConstants = {\n  +Dimensions: DimensionsPayload,\n  +isIPhoneX_deprecated?: boolean,\n};\n\nexport interface Spec extends TurboModule {\n  +getConstants: () => DeviceInfoConstants;\n}\n\nconst NativeModule: Spec = TurboModuleRegistry.getEnforcing<Spec>('DeviceInfo');\nlet constants: ?DeviceInfoConstants = null;\n\nconst NativeDeviceInfo = {\n  getConstants(): DeviceInfoConstants {\n    if (constants == null) {\n      constants = NativeModule.getConstants();\n    }\n    return constants;\n  },\n};\n\nexport default NativeDeviceInfo;\n"], "mappings": ";;;;AAYA,IAAAA,mBAAA,GAAAC,uBAAA,CAAAC,OAAA;AAA6F,SAAAD,wBAAAE,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAJ,uBAAA,YAAAA,wBAAAE,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAiC7F,IAAMmB,YAAkB,GAAGvB,mBAAmB,CAACwB,YAAY,CAAO,YAAY,CAAC;AAC/E,IAAIC,SAA+B,GAAG,IAAI;AAE1C,IAAMC,gBAAgB,GAAG;EACvBC,YAAY,WAAZA,YAAYA,CAAA,EAAwB;IAClC,IAAIF,SAAS,IAAI,IAAI,EAAE;MACrBA,SAAS,GAAGF,YAAY,CAACI,YAAY,CAAC,CAAC;IACzC;IACA,OAAOF,SAAS;EAClB;AACF,CAAC;AAAC,IAAAG,QAAA,GAAAC,OAAA,CAAAhB,OAAA,GAEaa,gBAAgB", "ignoreList": []}