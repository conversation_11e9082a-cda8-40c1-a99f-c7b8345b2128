{"version": 3, "names": ["_interopRequireDefault", "require", "Object", "defineProperty", "exports", "value", "default", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_get2", "_inherits2", "_NativeAnimatedHelper", "_normalizeColor", "_PlatformColorValueTypes", "_AnimatedValue", "_interopRequireWildcard", "_AnimatedWithChildren2", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "has", "get", "set", "_t", "hasOwnProperty", "call", "getOwnPropertyDescriptor", "_callSuper", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "_superPropGet", "p", "NativeAnimatedAPI", "NativeAnimatedHelper", "API", "defaultColor", "g", "b", "a", "processColor", "color", "undefined", "isRgbaValue", "normalizedColor", "normalizeColor", "processedColorObj", "processColorObject", "isRgbaAnimatedValue", "AnimatedValue", "AnimatedColor", "_Animated<PERSON>ith<PERSON><PERSON><PERSON><PERSON>", "valueIn", "config", "_this", "_suspendCallbacks", "rgbaAnimatedValue", "_processColor", "processedColor", "initColor", "nativeColor", "useNativeDriver", "__makeNative", "key", "setValue", "_processColor2", "_this2", "shouldUpdateNodeConfig", "__isNative", "nativeTag", "__getNativeTag", "setWaitingForIdentifier", "toString", "_withSuspendedCallbacks", "rgbaValue", "updateAnimatedNodeConfig", "__getNativeConfig", "unsetWaitingForIdentifier", "flushValue", "__callListeners", "__getValue", "setOffset", "offset", "flattenOffset", "extractOffset", "stopAnimation", "callback", "resetAnimation", "__attach", "__add<PERSON><PERSON>d", "__detach", "__remove<PERSON><PERSON>d", "platformConfig", "type", "debugID", "__getDebugID", "AnimatedWithChildren"], "sources": ["AnimatedColor.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n * @format\n */\n\n'use strict';\n\nimport type {ProcessedColorValue} from '../../StyleSheet/processColor';\nimport type {ColorValue} from '../../StyleSheet/StyleSheet';\nimport type {NativeColorValue} from '../../StyleSheet/StyleSheetTypes';\nimport type {PlatformConfig} from '../AnimatedPlatformConfig';\nimport type {AnimatedNodeConfig} from './AnimatedNode';\n\nimport NativeAnimatedHelper from '../../../src/private/animated/NativeAnimatedHelper';\nimport normalizeColor from '../../StyleSheet/normalizeColor';\nimport {processColorObject} from '../../StyleSheet/PlatformColorValueTypes';\nimport AnimatedValue, {flushValue} from './AnimatedValue';\nimport AnimatedWithChildren from './AnimatedWithChildren';\n\nexport type AnimatedColorConfig = $ReadOnly<{\n  ...AnimatedNodeConfig,\n  useNativeDriver: boolean,\n}>;\n\ntype ColorListenerCallback = (value: ColorValue) => mixed;\n\nexport type RgbaValue = {\n  +r: number,\n  +g: number,\n  +b: number,\n  +a: number,\n  ...\n};\n\ntype RgbaAnimatedValue = {\n  +r: AnimatedValue,\n  +g: AnimatedValue,\n  +b: AnimatedValue,\n  +a: AnimatedValue,\n  ...\n};\n\nexport type InputValue = ?(RgbaValue | RgbaAnimatedValue | ColorValue);\n\nconst NativeAnimatedAPI = NativeAnimatedHelper.API;\n\nconst defaultColor: RgbaValue = {r: 0, g: 0, b: 0, a: 1.0};\n\n/* eslint no-bitwise: 0 */\nfunction processColor(\n  color?: ?(ColorValue | RgbaValue),\n): ?(RgbaValue | NativeColorValue) {\n  if (color === undefined || color === null) {\n    return null;\n  }\n\n  if (isRgbaValue(color)) {\n    // $FlowIgnore[incompatible-cast] - Type is verified above\n    return (color: RgbaValue);\n  }\n\n  let normalizedColor: ?ProcessedColorValue = normalizeColor(\n    // $FlowIgnore[incompatible-cast] - Type is verified above\n    (color: ColorValue),\n  );\n  if (normalizedColor === undefined || normalizedColor === null) {\n    return null;\n  }\n\n  if (typeof normalizedColor === 'object') {\n    const processedColorObj: ?NativeColorValue =\n      processColorObject(normalizedColor);\n    if (processedColorObj != null) {\n      return processedColorObj;\n    }\n  } else if (typeof normalizedColor === 'number') {\n    const r: number = (normalizedColor & 0xff000000) >>> 24;\n    const g: number = (normalizedColor & 0x00ff0000) >>> 16;\n    const b: number = (normalizedColor & 0x0000ff00) >>> 8;\n    const a: number = (normalizedColor & 0x000000ff) / 255;\n\n    return {r, g, b, a};\n  }\n\n  return null;\n}\n\nfunction isRgbaValue(value: any): boolean {\n  return (\n    value &&\n    typeof value.r === 'number' &&\n    typeof value.g === 'number' &&\n    typeof value.b === 'number' &&\n    typeof value.a === 'number'\n  );\n}\n\nfunction isRgbaAnimatedValue(value: any): boolean {\n  return (\n    value &&\n    value.r instanceof AnimatedValue &&\n    value.g instanceof AnimatedValue &&\n    value.b instanceof AnimatedValue &&\n    value.a instanceof AnimatedValue\n  );\n}\n\nexport default class AnimatedColor extends AnimatedWithChildren {\n  r: AnimatedValue;\n  g: AnimatedValue;\n  b: AnimatedValue;\n  a: AnimatedValue;\n  nativeColor: ?NativeColorValue;\n\n  _suspendCallbacks: number = 0;\n\n  constructor(valueIn?: InputValue, config?: ?AnimatedColorConfig) {\n    super(config);\n\n    let value: RgbaValue | RgbaAnimatedValue | ColorValue =\n      valueIn ?? defaultColor;\n    if (isRgbaAnimatedValue(value)) {\n      // $FlowIgnore[incompatible-cast] - Type is verified above\n      const rgbaAnimatedValue: RgbaAnimatedValue = (value: RgbaAnimatedValue);\n      this.r = rgbaAnimatedValue.r;\n      this.g = rgbaAnimatedValue.g;\n      this.b = rgbaAnimatedValue.b;\n      this.a = rgbaAnimatedValue.a;\n    } else {\n      const processedColor: RgbaValue | NativeColorValue =\n        // $FlowIgnore[incompatible-cast] - Type is verified above\n        processColor((value: ColorValue | RgbaValue)) ?? defaultColor;\n      let initColor: RgbaValue = defaultColor;\n      if (isRgbaValue(processedColor)) {\n        // $FlowIgnore[incompatible-cast] - Type is verified above\n        initColor = (processedColor: RgbaValue);\n      } else {\n        // $FlowIgnore[incompatible-cast] - Type is verified above\n        this.nativeColor = (processedColor: NativeColorValue);\n      }\n\n      this.r = new AnimatedValue(initColor.r);\n      this.g = new AnimatedValue(initColor.g);\n      this.b = new AnimatedValue(initColor.b);\n      this.a = new AnimatedValue(initColor.a);\n    }\n\n    if (config?.useNativeDriver) {\n      this.__makeNative();\n    }\n  }\n\n  /**\n   * Directly set the value. This will stop any animations running on the value\n   * and update all the bound properties.\n   */\n  setValue(value: RgbaValue | ColorValue): void {\n    let shouldUpdateNodeConfig = false;\n    if (this.__isNative) {\n      const nativeTag = this.__getNativeTag();\n      NativeAnimatedAPI.setWaitingForIdentifier(nativeTag.toString());\n    }\n\n    const processedColor: RgbaValue | NativeColorValue =\n      processColor(value) ?? defaultColor;\n    this._withSuspendedCallbacks(() => {\n      if (isRgbaValue(processedColor)) {\n        // $FlowIgnore[incompatible-type] - Type is verified above\n        const rgbaValue: RgbaValue = processedColor;\n        this.r.setValue(rgbaValue.r);\n        this.g.setValue(rgbaValue.g);\n        this.b.setValue(rgbaValue.b);\n        this.a.setValue(rgbaValue.a);\n        if (this.nativeColor != null) {\n          this.nativeColor = null;\n          shouldUpdateNodeConfig = true;\n        }\n      } else {\n        // $FlowIgnore[incompatible-type] - Type is verified above\n        const nativeColor: NativeColorValue = processedColor;\n        if (this.nativeColor !== nativeColor) {\n          this.nativeColor = nativeColor;\n          shouldUpdateNodeConfig = true;\n        }\n      }\n    });\n\n    if (this.__isNative) {\n      const nativeTag = this.__getNativeTag();\n      if (shouldUpdateNodeConfig) {\n        NativeAnimatedAPI.updateAnimatedNodeConfig(\n          nativeTag,\n          this.__getNativeConfig(),\n        );\n      }\n      NativeAnimatedAPI.unsetWaitingForIdentifier(nativeTag.toString());\n    } else {\n      flushValue(this);\n    }\n\n    // $FlowFixMe[incompatible-call]\n    this.__callListeners(this.__getValue());\n  }\n\n  /**\n   * Sets an offset that is applied on top of whatever value is set, whether\n   * via `setValue`, an animation, or `Animated.event`. Useful for compensating\n   * things like the start of a pan gesture.\n   */\n  setOffset(offset: RgbaValue): void {\n    this.r.setOffset(offset.r);\n    this.g.setOffset(offset.g);\n    this.b.setOffset(offset.b);\n    this.a.setOffset(offset.a);\n  }\n\n  /**\n   * Merges the offset value into the base value and resets the offset to zero.\n   * The final output of the value is unchanged.\n   */\n  flattenOffset(): void {\n    this.r.flattenOffset();\n    this.g.flattenOffset();\n    this.b.flattenOffset();\n    this.a.flattenOffset();\n  }\n\n  /**\n   * Sets the offset value to the base value, and resets the base value to\n   * zero. The final output of the value is unchanged.\n   */\n  extractOffset(): void {\n    this.r.extractOffset();\n    this.g.extractOffset();\n    this.b.extractOffset();\n    this.a.extractOffset();\n  }\n\n  /**\n   * Stops any running animation or tracking. `callback` is invoked with the\n   * final value after stopping the animation, which is useful for updating\n   * state to match the animation position with layout.\n   */\n  stopAnimation(callback?: ColorListenerCallback): void {\n    this.r.stopAnimation();\n    this.g.stopAnimation();\n    this.b.stopAnimation();\n    this.a.stopAnimation();\n    callback && callback(this.__getValue());\n  }\n\n  /**\n   * Stops any animation and resets the value to its original.\n   */\n  resetAnimation(callback?: ColorListenerCallback): void {\n    this.r.resetAnimation();\n    this.g.resetAnimation();\n    this.b.resetAnimation();\n    this.a.resetAnimation();\n    callback && callback(this.__getValue());\n  }\n\n  __getValue(): ColorValue {\n    if (this.nativeColor != null) {\n      return this.nativeColor;\n    } else {\n      return `rgba(${this.r.__getValue()}, ${this.g.__getValue()}, ${this.b.__getValue()}, ${this.a.__getValue()})`;\n    }\n  }\n\n  __attach(): void {\n    this.r.__addChild(this);\n    this.g.__addChild(this);\n    this.b.__addChild(this);\n    this.a.__addChild(this);\n    super.__attach();\n  }\n\n  __detach(): void {\n    this.r.__removeChild(this);\n    this.g.__removeChild(this);\n    this.b.__removeChild(this);\n    this.a.__removeChild(this);\n    super.__detach();\n  }\n\n  _withSuspendedCallbacks(callback: () => void) {\n    this._suspendCallbacks++;\n    callback();\n    this._suspendCallbacks--;\n  }\n\n  __callListeners(value: number): void {\n    if (this._suspendCallbacks === 0) {\n      super.__callListeners(value);\n    }\n  }\n\n  __makeNative(platformConfig: ?PlatformConfig) {\n    this.r.__makeNative(platformConfig);\n    this.g.__makeNative(platformConfig);\n    this.b.__makeNative(platformConfig);\n    this.a.__makeNative(platformConfig);\n    super.__makeNative(platformConfig);\n  }\n\n  __getNativeConfig(): {...} {\n    return {\n      type: 'color',\n      r: this.r.__getNativeTag(),\n      g: this.g.__getNativeTag(),\n      b: this.b.__getNativeTag(),\n      a: this.a.__getNativeTag(),\n      nativeColor: this.nativeColor,\n      debugID: this.__getDebugID(),\n    };\n  }\n}\n"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAAA,IAAAC,gBAAA,GAAAP,sBAAA,CAAAC,OAAA;AAAA,IAAAO,aAAA,GAAAR,sBAAA,CAAAC,OAAA;AAAA,IAAAQ,2BAAA,GAAAT,sBAAA,CAAAC,OAAA;AAAA,IAAAS,gBAAA,GAAAV,sBAAA,CAAAC,OAAA;AAAA,IAAAU,KAAA,GAAAX,sBAAA,CAAAC,OAAA;AAAA,IAAAW,UAAA,GAAAZ,sBAAA,CAAAC,OAAA;AAQb,IAAAY,qBAAA,GAAAb,sBAAA,CAAAC,OAAA;AACA,IAAAa,eAAA,GAAAd,sBAAA,CAAAC,OAAA;AACA,IAAAc,wBAAA,GAAAd,OAAA;AACA,IAAAe,cAAA,GAAAC,uBAAA,CAAAhB,OAAA;AACA,IAAAiB,sBAAA,GAAAlB,sBAAA,CAAAC,OAAA;AAA0D,SAAAgB,wBAAAE,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAJ,uBAAA,YAAAA,wBAAAE,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAtB,OAAA,EAAAa,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAI,GAAA,CAAAV,CAAA,UAAAM,CAAA,CAAAK,GAAA,CAAAX,CAAA,GAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,EAAAQ,CAAA,cAAAK,EAAA,IAAAb,CAAA,gBAAAa,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAa,EAAA,OAAAN,CAAA,IAAAD,CAAA,GAAAvB,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAiC,wBAAA,CAAAhB,CAAA,EAAAa,EAAA,OAAAN,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAK,EAAA,EAAAN,CAAA,IAAAC,CAAA,CAAAK,EAAA,IAAAb,CAAA,CAAAa,EAAA,WAAAL,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAAA,SAAAgB,WAAAhB,CAAA,EAAAK,CAAA,EAAAN,CAAA,WAAAM,CAAA,OAAAf,gBAAA,CAAAJ,OAAA,EAAAmB,CAAA,OAAAhB,2BAAA,CAAAH,OAAA,EAAAc,CAAA,EAAAiB,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAd,CAAA,EAAAN,CAAA,YAAAT,gBAAA,CAAAJ,OAAA,EAAAc,CAAA,EAAAoB,WAAA,IAAAf,CAAA,CAAAgB,KAAA,CAAArB,CAAA,EAAAD,CAAA;AAAA,SAAAkB,0BAAA,cAAAjB,CAAA,IAAAsB,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAV,IAAA,CAAAI,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAtB,CAAA,aAAAiB,yBAAA,YAAAA,0BAAA,aAAAjB,CAAA;AAAA,SAAAyB,cAAAzB,CAAA,EAAAK,CAAA,EAAAN,CAAA,EAAAG,CAAA,QAAAwB,CAAA,OAAAnC,KAAA,CAAAL,OAAA,MAAAI,gBAAA,CAAAJ,OAAA,MAAAgB,CAAA,GAAAF,CAAA,CAAAuB,SAAA,GAAAvB,CAAA,GAAAK,CAAA,EAAAN,CAAA,cAAAG,CAAA,yBAAAwB,CAAA,aAAA1B,CAAA,WAAA0B,CAAA,CAAAL,KAAA,CAAAtB,CAAA,EAAAC,CAAA,OAAA0B,CAAA;AA2B1D,IAAMC,iBAAiB,GAAGC,6BAAoB,CAACC,GAAG;AAElD,IAAMC,YAAuB,GAAG;EAAC5B,CAAC,EAAE,CAAC;EAAE6B,CAAC,EAAE,CAAC;EAAEC,CAAC,EAAE,CAAC;EAAEC,CAAC,EAAE;AAAG,CAAC;AAG1D,SAASC,YAAYA,CACnBC,KAAiC,EACA;EACjC,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,EAAE;IACzC,OAAO,IAAI;EACb;EAEA,IAAIE,WAAW,CAACF,KAAK,CAAC,EAAE;IAEtB,OAAQA,KAAK;EACf;EAEA,IAAIG,eAAqC,GAAG,IAAAC,uBAAc,EAEvDJ,KACH,CAAC;EACD,IAAIG,eAAe,KAAKF,SAAS,IAAIE,eAAe,KAAK,IAAI,EAAE;IAC7D,OAAO,IAAI;EACb;EAEA,IAAI,OAAOA,eAAe,KAAK,QAAQ,EAAE;IACvC,IAAME,iBAAoC,GACxC,IAAAC,2CAAkB,EAACH,eAAe,CAAC;IACrC,IAAIE,iBAAiB,IAAI,IAAI,EAAE;MAC7B,OAAOA,iBAAiB;IAC1B;EACF,CAAC,MAAM,IAAI,OAAOF,eAAe,KAAK,QAAQ,EAAE;IAC9C,IAAMpC,CAAS,GAAG,CAACoC,eAAe,GAAG,UAAU,MAAM,EAAE;IACvD,IAAMP,CAAS,GAAG,CAACO,eAAe,GAAG,UAAU,MAAM,EAAE;IACvD,IAAMN,CAAS,GAAG,CAACM,eAAe,GAAG,UAAU,MAAM,CAAC;IACtD,IAAML,CAAS,GAAG,CAACK,eAAe,GAAG,UAAU,IAAI,GAAG;IAEtD,OAAO;MAACpC,CAAC,EAADA,CAAC;MAAE6B,CAAC,EAADA,CAAC;MAAEC,CAAC,EAADA,CAAC;MAAEC,CAAC,EAADA;IAAC,CAAC;EACrB;EAEA,OAAO,IAAI;AACb;AAEA,SAASI,WAAWA,CAACpD,KAAU,EAAW;EACxC,OACEA,KAAK,IACL,OAAOA,KAAK,CAACiB,CAAC,KAAK,QAAQ,IAC3B,OAAOjB,KAAK,CAAC8C,CAAC,KAAK,QAAQ,IAC3B,OAAO9C,KAAK,CAAC+C,CAAC,KAAK,QAAQ,IAC3B,OAAO/C,KAAK,CAACgD,CAAC,KAAK,QAAQ;AAE/B;AAEA,SAASS,mBAAmBA,CAACzD,KAAU,EAAW;EAChD,OACEA,KAAK,IACLA,KAAK,CAACiB,CAAC,YAAYyC,sBAAa,IAChC1D,KAAK,CAAC8C,CAAC,YAAYY,sBAAa,IAChC1D,KAAK,CAAC+C,CAAC,YAAYW,sBAAa,IAChC1D,KAAK,CAACgD,CAAC,YAAYU,sBAAa;AAEpC;AAAC,IAEoBC,aAAa,GAAA5D,OAAA,CAAAE,OAAA,aAAA2D,qBAAA;EAShC,SAAAD,cAAYE,OAAoB,EAAEC,MAA6B,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAA7D,gBAAA,CAAAD,OAAA,QAAA0D,aAAA;IAC/DI,KAAA,GAAAhC,UAAA,OAAA4B,aAAA,GAAMG,MAAM;IAAEC,KAAA,CAHhBC,iBAAiB,GAAW,CAAC;IAK3B,IAAIhE,KAAiD,GACnD6D,OAAO,WAAPA,OAAO,GAAIhB,YAAY;IACzB,IAAIY,mBAAmB,CAACzD,KAAK,CAAC,EAAE;MAE9B,IAAMiE,iBAAoC,GAAIjE,KAAyB;MACvE+D,KAAA,CAAK9C,CAAC,GAAGgD,iBAAiB,CAAChD,CAAC;MAC5B8C,KAAA,CAAKjB,CAAC,GAAGmB,iBAAiB,CAACnB,CAAC;MAC5BiB,KAAA,CAAKhB,CAAC,GAAGkB,iBAAiB,CAAClB,CAAC;MAC5BgB,KAAA,CAAKf,CAAC,GAAGiB,iBAAiB,CAACjB,CAAC;IAC9B,CAAC,MAAM;MAAA,IAAAkB,aAAA;MACL,IAAMC,cAA4C,IAAAD,aAAA,GAEhDjB,YAAY,CAAEjD,KAA8B,CAAC,YAAAkE,aAAA,GAAIrB,YAAY;MAC/D,IAAIuB,SAAoB,GAAGvB,YAAY;MACvC,IAAIO,WAAW,CAACe,cAAc,CAAC,EAAE;QAE/BC,SAAS,GAAID,cAA0B;MACzC,CAAC,MAAM;QAELJ,KAAA,CAAKM,WAAW,GAAIF,cAAiC;MACvD;MAEAJ,KAAA,CAAK9C,CAAC,GAAG,IAAIyC,sBAAa,CAACU,SAAS,CAACnD,CAAC,CAAC;MACvC8C,KAAA,CAAKjB,CAAC,GAAG,IAAIY,sBAAa,CAACU,SAAS,CAACtB,CAAC,CAAC;MACvCiB,KAAA,CAAKhB,CAAC,GAAG,IAAIW,sBAAa,CAACU,SAAS,CAACrB,CAAC,CAAC;MACvCgB,KAAA,CAAKf,CAAC,GAAG,IAAIU,sBAAa,CAACU,SAAS,CAACpB,CAAC,CAAC;IACzC;IAEA,IAAIc,MAAM,YAANA,MAAM,CAAEQ,eAAe,EAAE;MAC3BP,KAAA,CAAKQ,YAAY,CAAC,CAAC;IACrB;IAAC,OAAAR,KAAA;EACH;EAAC,IAAAxD,UAAA,CAAAN,OAAA,EAAA0D,aAAA,EAAAC,qBAAA;EAAA,WAAAzD,aAAA,CAAAF,OAAA,EAAA0D,aAAA;IAAAa,GAAA;IAAAxE,KAAA,EAMD,SAAAyE,QAAQA,CAACzE,KAA6B,EAAQ;MAAA,IAAA0E,cAAA;QAAAC,MAAA;MAC5C,IAAIC,sBAAsB,GAAG,KAAK;MAClC,IAAI,IAAI,CAACC,UAAU,EAAE;QACnB,IAAMC,SAAS,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;QACvCrC,iBAAiB,CAACsC,uBAAuB,CAACF,SAAS,CAACG,QAAQ,CAAC,CAAC,CAAC;MACjE;MAEA,IAAMd,cAA4C,IAAAO,cAAA,GAChDzB,YAAY,CAACjD,KAAK,CAAC,YAAA0E,cAAA,GAAI7B,YAAY;MACrC,IAAI,CAACqC,uBAAuB,CAAC,YAAM;QACjC,IAAI9B,WAAW,CAACe,cAAc,CAAC,EAAE;UAE/B,IAAMgB,SAAoB,GAAGhB,cAAc;UAC3CQ,MAAI,CAAC1D,CAAC,CAACwD,QAAQ,CAACU,SAAS,CAAClE,CAAC,CAAC;UAC5B0D,MAAI,CAAC7B,CAAC,CAAC2B,QAAQ,CAACU,SAAS,CAACrC,CAAC,CAAC;UAC5B6B,MAAI,CAAC5B,CAAC,CAAC0B,QAAQ,CAACU,SAAS,CAACpC,CAAC,CAAC;UAC5B4B,MAAI,CAAC3B,CAAC,CAACyB,QAAQ,CAACU,SAAS,CAACnC,CAAC,CAAC;UAC5B,IAAI2B,MAAI,CAACN,WAAW,IAAI,IAAI,EAAE;YAC5BM,MAAI,CAACN,WAAW,GAAG,IAAI;YACvBO,sBAAsB,GAAG,IAAI;UAC/B;QACF,CAAC,MAAM;UAEL,IAAMP,WAA6B,GAAGF,cAAc;UACpD,IAAIQ,MAAI,CAACN,WAAW,KAAKA,WAAW,EAAE;YACpCM,MAAI,CAACN,WAAW,GAAGA,WAAW;YAC9BO,sBAAsB,GAAG,IAAI;UAC/B;QACF;MACF,CAAC,CAAC;MAEF,IAAI,IAAI,CAACC,UAAU,EAAE;QACnB,IAAMC,UAAS,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;QACvC,IAAIH,sBAAsB,EAAE;UAC1BlC,iBAAiB,CAAC0C,wBAAwB,CACxCN,UAAS,EACT,IAAI,CAACO,iBAAiB,CAAC,CACzB,CAAC;QACH;QACA3C,iBAAiB,CAAC4C,yBAAyB,CAACR,UAAS,CAACG,QAAQ,CAAC,CAAC,CAAC;MACnE,CAAC,MAAM;QACL,IAAAM,yBAAU,EAAC,IAAI,CAAC;MAClB;MAGA,IAAI,CAACC,eAAe,CAAC,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC;IACzC;EAAC;IAAAjB,GAAA;IAAAxE,KAAA,EAOD,SAAA0F,SAASA,CAACC,MAAiB,EAAQ;MACjC,IAAI,CAAC1E,CAAC,CAACyE,SAAS,CAACC,MAAM,CAAC1E,CAAC,CAAC;MAC1B,IAAI,CAAC6B,CAAC,CAAC4C,SAAS,CAACC,MAAM,CAAC7C,CAAC,CAAC;MAC1B,IAAI,CAACC,CAAC,CAAC2C,SAAS,CAACC,MAAM,CAAC5C,CAAC,CAAC;MAC1B,IAAI,CAACC,CAAC,CAAC0C,SAAS,CAACC,MAAM,CAAC3C,CAAC,CAAC;IAC5B;EAAC;IAAAwB,GAAA;IAAAxE,KAAA,EAMD,SAAA4F,aAAaA,CAAA,EAAS;MACpB,IAAI,CAAC3E,CAAC,CAAC2E,aAAa,CAAC,CAAC;MACtB,IAAI,CAAC9C,CAAC,CAAC8C,aAAa,CAAC,CAAC;MACtB,IAAI,CAAC7C,CAAC,CAAC6C,aAAa,CAAC,CAAC;MACtB,IAAI,CAAC5C,CAAC,CAAC4C,aAAa,CAAC,CAAC;IACxB;EAAC;IAAApB,GAAA;IAAAxE,KAAA,EAMD,SAAA6F,aAAaA,CAAA,EAAS;MACpB,IAAI,CAAC5E,CAAC,CAAC4E,aAAa,CAAC,CAAC;MACtB,IAAI,CAAC/C,CAAC,CAAC+C,aAAa,CAAC,CAAC;MACtB,IAAI,CAAC9C,CAAC,CAAC8C,aAAa,CAAC,CAAC;MACtB,IAAI,CAAC7C,CAAC,CAAC6C,aAAa,CAAC,CAAC;IACxB;EAAC;IAAArB,GAAA;IAAAxE,KAAA,EAOD,SAAA8F,aAAaA,CAACC,QAAgC,EAAQ;MACpD,IAAI,CAAC9E,CAAC,CAAC6E,aAAa,CAAC,CAAC;MACtB,IAAI,CAAChD,CAAC,CAACgD,aAAa,CAAC,CAAC;MACtB,IAAI,CAAC/C,CAAC,CAAC+C,aAAa,CAAC,CAAC;MACtB,IAAI,CAAC9C,CAAC,CAAC8C,aAAa,CAAC,CAAC;MACtBC,QAAQ,IAAIA,QAAQ,CAAC,IAAI,CAACN,UAAU,CAAC,CAAC,CAAC;IACzC;EAAC;IAAAjB,GAAA;IAAAxE,KAAA,EAKD,SAAAgG,cAAcA,CAACD,QAAgC,EAAQ;MACrD,IAAI,CAAC9E,CAAC,CAAC+E,cAAc,CAAC,CAAC;MACvB,IAAI,CAAClD,CAAC,CAACkD,cAAc,CAAC,CAAC;MACvB,IAAI,CAACjD,CAAC,CAACiD,cAAc,CAAC,CAAC;MACvB,IAAI,CAAChD,CAAC,CAACgD,cAAc,CAAC,CAAC;MACvBD,QAAQ,IAAIA,QAAQ,CAAC,IAAI,CAACN,UAAU,CAAC,CAAC,CAAC;IACzC;EAAC;IAAAjB,GAAA;IAAAxE,KAAA,EAED,SAAAyF,UAAUA,CAAA,EAAe;MACvB,IAAI,IAAI,CAACpB,WAAW,IAAI,IAAI,EAAE;QAC5B,OAAO,IAAI,CAACA,WAAW;MACzB,CAAC,MAAM;QACL,OAAO,QAAQ,IAAI,CAACpD,CAAC,CAACwE,UAAU,CAAC,CAAC,KAAK,IAAI,CAAC3C,CAAC,CAAC2C,UAAU,CAAC,CAAC,KAAK,IAAI,CAAC1C,CAAC,CAAC0C,UAAU,CAAC,CAAC,KAAK,IAAI,CAACzC,CAAC,CAACyC,UAAU,CAAC,CAAC,GAAG;MAC/G;IACF;EAAC;IAAAjB,GAAA;IAAAxE,KAAA,EAED,SAAAiG,QAAQA,CAAA,EAAS;MACf,IAAI,CAAChF,CAAC,CAACiF,UAAU,CAAC,IAAI,CAAC;MACvB,IAAI,CAACpD,CAAC,CAACoD,UAAU,CAAC,IAAI,CAAC;MACvB,IAAI,CAACnD,CAAC,CAACmD,UAAU,CAAC,IAAI,CAAC;MACvB,IAAI,CAAClD,CAAC,CAACkD,UAAU,CAAC,IAAI,CAAC;MACvB1D,aAAA,CAAAmB,aAAA;IACF;EAAC;IAAAa,GAAA;IAAAxE,KAAA,EAED,SAAAmG,QAAQA,CAAA,EAAS;MACf,IAAI,CAAClF,CAAC,CAACmF,aAAa,CAAC,IAAI,CAAC;MAC1B,IAAI,CAACtD,CAAC,CAACsD,aAAa,CAAC,IAAI,CAAC;MAC1B,IAAI,CAACrD,CAAC,CAACqD,aAAa,CAAC,IAAI,CAAC;MAC1B,IAAI,CAACpD,CAAC,CAACoD,aAAa,CAAC,IAAI,CAAC;MAC1B5D,aAAA,CAAAmB,aAAA;IACF;EAAC;IAAAa,GAAA;IAAAxE,KAAA,EAED,SAAAkF,uBAAuBA,CAACa,QAAoB,EAAE;MAC5C,IAAI,CAAC/B,iBAAiB,EAAE;MACxB+B,QAAQ,CAAC,CAAC;MACV,IAAI,CAAC/B,iBAAiB,EAAE;IAC1B;EAAC;IAAAQ,GAAA;IAAAxE,KAAA,EAED,SAAAwF,eAAeA,CAACxF,KAAa,EAAQ;MACnC,IAAI,IAAI,CAACgE,iBAAiB,KAAK,CAAC,EAAE;QAChCxB,aAAA,CAAAmB,aAAA,+BAAsB3D,KAAK;MAC7B;IACF;EAAC;IAAAwE,GAAA;IAAAxE,KAAA,EAED,SAAAuE,YAAYA,CAAC8B,cAA+B,EAAE;MAC5C,IAAI,CAACpF,CAAC,CAACsD,YAAY,CAAC8B,cAAc,CAAC;MACnC,IAAI,CAACvD,CAAC,CAACyB,YAAY,CAAC8B,cAAc,CAAC;MACnC,IAAI,CAACtD,CAAC,CAACwB,YAAY,CAAC8B,cAAc,CAAC;MACnC,IAAI,CAACrD,CAAC,CAACuB,YAAY,CAAC8B,cAAc,CAAC;MACnC7D,aAAA,CAAAmB,aAAA,4BAAmB0C,cAAc;IACnC;EAAC;IAAA7B,GAAA;IAAAxE,KAAA,EAED,SAAAqF,iBAAiBA,CAAA,EAAU;MACzB,OAAO;QACLiB,IAAI,EAAE,OAAO;QACbrF,CAAC,EAAE,IAAI,CAACA,CAAC,CAAC8D,cAAc,CAAC,CAAC;QAC1BjC,CAAC,EAAE,IAAI,CAACA,CAAC,CAACiC,cAAc,CAAC,CAAC;QAC1BhC,CAAC,EAAE,IAAI,CAACA,CAAC,CAACgC,cAAc,CAAC,CAAC;QAC1B/B,CAAC,EAAE,IAAI,CAACA,CAAC,CAAC+B,cAAc,CAAC,CAAC;QAC1BV,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7BkC,OAAO,EAAE,IAAI,CAACC,YAAY,CAAC;MAC7B,CAAC;IACH;EAAC;AAAA,EAjNwCC,8BAAoB", "ignoreList": []}