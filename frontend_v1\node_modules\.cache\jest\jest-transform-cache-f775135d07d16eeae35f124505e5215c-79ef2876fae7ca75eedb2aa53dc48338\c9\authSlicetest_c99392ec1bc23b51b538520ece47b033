5945b8fbb603fbee2118c60d8464eb02
_getJestObj().mock('@react-native-async-storage/async-storage', function () {
  return {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    multiRemove: jest.fn()
  };
});
_getJestObj().mock("../../../services/authService", function () {
  return {
    authService: {
      refreshToken: jest.fn()
    }
  };
});
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _asyncStorage = _interopRequireDefault(require("@react-native-async-storage/async-storage"));
var _authSlice = require("../../../store/authSlice");
function _getJestObj() {
  var _require = require("@jest/globals"),
    jest = _require.jest;
  _getJestObj = function _getJestObj() {
    return jest;
  };
  return jest;
}
describe('AuthSlice', function () {
  beforeEach(function () {
    _authSlice.useAuthStore.getState().reset();
    jest.clearAllMocks();
  });
  describe('Initial State', function () {
    it('should have correct initial state', function () {
      var state = _authSlice.useAuthStore.getState();
      expect(state.authToken).toBeNull();
      expect(state.refreshToken).toBeNull();
      expect(state.user).toBeNull();
      expect(state.userRole).toBeNull();
      expect(state.status).toBe('idle');
      expect(state.error).toBeNull();
      expect(state.tokenExpiresAt).toBeNull();
      expect(state.isAuthenticated).toBe(false);
    });
  });
  describe('Login Actions', function () {
    var mockUser = {
      id: '1',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      role: 'customer',
      isVerified: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    };
    it('should handle loginStart', function () {
      var _useAuthStore$getStat = _authSlice.useAuthStore.getState(),
        loginStart = _useAuthStore$getStat.loginStart;
      loginStart();
      var state = _authSlice.useAuthStore.getState();
      expect(state.status).toBe('loading');
      expect(state.error).toBeNull();
    });
    it('should handle loginSuccess', function () {
      var _useAuthStore$getStat2 = _authSlice.useAuthStore.getState(),
        loginSuccess = _useAuthStore$getStat2.loginSuccess;
      var token = 'test-token';
      var refreshToken = 'test-refresh-token';
      loginSuccess(token, refreshToken, mockUser);
      var state = _authSlice.useAuthStore.getState();
      expect(state.authToken).toBe(token);
      expect(state.refreshToken).toBe(refreshToken);
      expect(state.user).toEqual(mockUser);
      expect(state.userRole).toBe('customer');
      expect(state.status).toBe('success');
      expect(state.error).toBeNull();
      expect(state.isAuthenticated).toBe(true);
      expect(state.tokenExpiresAt).toBeGreaterThan(Date.now());
    });
    it('should handle loginFailure', function () {
      var _useAuthStore$getStat3 = _authSlice.useAuthStore.getState(),
        loginFailure = _useAuthStore$getStat3.loginFailure;
      var errorMessage = 'Login failed';
      loginFailure(errorMessage);
      var state = _authSlice.useAuthStore.getState();
      expect(state.authToken).toBeNull();
      expect(state.refreshToken).toBeNull();
      expect(state.user).toBeNull();
      expect(state.userRole).toBeNull();
      expect(state.status).toBe('error');
      expect(state.error).toBe(errorMessage);
      expect(state.isAuthenticated).toBe(false);
      expect(state.tokenExpiresAt).toBeNull();
    });
  });
  describe('Registration Actions', function () {
    var mockUser = {
      id: '2',
      email: '<EMAIL>',
      firstName: 'New',
      lastName: 'User',
      role: 'provider',
      isVerified: false,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    };
    it('should handle registerStart', function () {
      var _useAuthStore$getStat4 = _authSlice.useAuthStore.getState(),
        registerStart = _useAuthStore$getStat4.registerStart;
      registerStart();
      var state = _authSlice.useAuthStore.getState();
      expect(state.status).toBe('loading');
      expect(state.error).toBeNull();
    });
    it('should handle registerSuccess', function () {
      var _useAuthStore$getStat5 = _authSlice.useAuthStore.getState(),
        registerSuccess = _useAuthStore$getStat5.registerSuccess;
      var token = 'register-token';
      var refreshToken = 'register-refresh-token';
      registerSuccess(token, refreshToken, mockUser);
      var state = _authSlice.useAuthStore.getState();
      expect(state.authToken).toBe(token);
      expect(state.refreshToken).toBe(refreshToken);
      expect(state.user).toEqual(mockUser);
      expect(state.userRole).toBe('provider');
      expect(state.status).toBe('success');
      expect(state.error).toBeNull();
      expect(state.isAuthenticated).toBe(true);
    });
    it('should handle registerFailure', function () {
      var _useAuthStore$getStat6 = _authSlice.useAuthStore.getState(),
        registerFailure = _useAuthStore$getStat6.registerFailure;
      var errorMessage = 'Registration failed';
      registerFailure(errorMessage);
      var state = _authSlice.useAuthStore.getState();
      expect(state.authToken).toBeNull();
      expect(state.refreshToken).toBeNull();
      expect(state.user).toBeNull();
      expect(state.userRole).toBeNull();
      expect(state.status).toBe('error');
      expect(state.error).toBe(errorMessage);
      expect(state.isAuthenticated).toBe(false);
    });
  });
  describe('Profile Management', function () {
    var mockUser = {
      id: '1',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      role: 'customer',
      isVerified: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    };
    beforeEach(function () {
      var _useAuthStore$getStat7 = _authSlice.useAuthStore.getState(),
        loginSuccess = _useAuthStore$getStat7.loginSuccess;
      loginSuccess('token', 'refresh-token', mockUser);
    });
    it('should handle updateProfile', function () {
      var _state$user, _state$user2, _state$user3;
      var _useAuthStore$getStat8 = _authSlice.useAuthStore.getState(),
        updateProfile = _useAuthStore$getStat8.updateProfile;
      var updates = {
        firstName: 'Updated',
        phoneNumber: '+1234567890'
      };
      updateProfile(updates);
      var state = _authSlice.useAuthStore.getState();
      expect((_state$user = state.user) == null ? void 0 : _state$user.firstName).toBe('Updated');
      expect((_state$user2 = state.user) == null ? void 0 : _state$user2.phoneNumber).toBe('+1234567890');
      expect((_state$user3 = state.user) == null ? void 0 : _state$user3.lastName).toBe('User');
    });
    it('should handle updateTokens', function () {
      var _useAuthStore$getStat9 = _authSlice.useAuthStore.getState(),
        updateTokens = _useAuthStore$getStat9.updateTokens;
      var newToken = 'new-token';
      var newRefreshToken = 'new-refresh-token';
      updateTokens(newToken, newRefreshToken);
      var state = _authSlice.useAuthStore.getState();
      expect(state.authToken).toBe(newToken);
      expect(state.refreshToken).toBe(newRefreshToken);
      expect(state.tokenExpiresAt).toBeGreaterThan(Date.now());
    });
  });
  describe('Logout', function () {
    it('should handle logout', function () {
      var _useAuthStore$getStat0 = _authSlice.useAuthStore.getState(),
        loginSuccess = _useAuthStore$getStat0.loginSuccess,
        logout = _useAuthStore$getStat0.logout;
      var mockUser = {
        id: '1',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        role: 'customer',
        isVerified: true,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      };
      loginSuccess('token', 'refresh-token', mockUser);
      expect(_authSlice.useAuthStore.getState().isAuthenticated).toBe(true);
      logout();
      var state = _authSlice.useAuthStore.getState();
      expect(state.authToken).toBeNull();
      expect(state.refreshToken).toBeNull();
      expect(state.user).toBeNull();
      expect(state.userRole).toBeNull();
      expect(state.status).toBe('idle');
      expect(state.error).toBeNull();
      expect(state.isAuthenticated).toBe(false);
      expect(state.tokenExpiresAt).toBeNull();
    });
  });
  describe('Authentication Status Check', function () {
    it('should load stored authentication data', (0, _asyncToGenerator2.default)(function* () {
      var mockUser = {
        id: '1',
        email: '<EMAIL>',
        firstName: 'Stored',
        lastName: 'User',
        role: 'customer',
        isVerified: true,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      };
      _asyncStorage.default.getItem.mockResolvedValueOnce('stored-token').mockResolvedValueOnce('stored-refresh-token').mockResolvedValueOnce(JSON.stringify(mockUser));
      var _useAuthStore$getStat1 = _authSlice.useAuthStore.getState(),
        checkAuthStatus = _useAuthStore$getStat1.checkAuthStatus;
      yield checkAuthStatus();
      var state = _authSlice.useAuthStore.getState();
      expect(state.authToken).toBe('stored-token');
      expect(state.refreshToken).toBe('stored-refresh-token');
      expect(state.user).toEqual(mockUser);
      expect(state.isAuthenticated).toBe(true);
    }));
    it('should handle missing stored data', (0, _asyncToGenerator2.default)(function* () {
      _asyncStorage.default.getItem.mockResolvedValue(null);
      var _useAuthStore$getStat10 = _authSlice.useAuthStore.getState(),
        checkAuthStatus = _useAuthStore$getStat10.checkAuthStatus;
      yield checkAuthStatus();
      var state = _authSlice.useAuthStore.getState();
      expect(state.authToken).toBeNull();
      expect(state.isAuthenticated).toBe(false);
    }));
    it('should handle corrupted stored user data', (0, _asyncToGenerator2.default)(function* () {
      _asyncStorage.default.getItem.mockResolvedValueOnce('stored-token').mockResolvedValueOnce('stored-refresh-token').mockResolvedValueOnce('invalid-json');
      var _useAuthStore$getStat11 = _authSlice.useAuthStore.getState(),
        checkAuthStatus = _useAuthStore$getStat11.checkAuthStatus;
      yield checkAuthStatus();
      var state = _authSlice.useAuthStore.getState();
      expect(state.authToken).toBeNull();
      expect(state.isAuthenticated).toBe(false);
      expect(_asyncStorage.default.multiRemove).toHaveBeenCalledWith(['auth_token', 'refresh_token', 'auth_user']);
    }));
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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