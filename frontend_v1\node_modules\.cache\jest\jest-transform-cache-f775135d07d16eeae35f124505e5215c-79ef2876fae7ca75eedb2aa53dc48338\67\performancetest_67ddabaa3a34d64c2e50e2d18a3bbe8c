6e672a6abcc0af5fd49408389eb54502
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _performance = require("../performance");
var mockPerformance = {
  now: jest.fn(function () {
    return Date.now();
  }),
  mark: jest.fn(),
  measure: jest.fn(),
  getEntriesByType: jest.fn(function () {
    return [];
  }),
  getEntriesByName: jest.fn(function () {
    return [];
  }),
  memory: {
    usedJSHeapSize: 1000000,
    totalJSHeapSize: 2000000
  }
};
var mockPerformanceObserver = jest.fn();
mockPerformanceObserver.prototype.observe = jest.fn();
mockPerformanceObserver.prototype.disconnect = jest.fn();
var mockWindow = {
  performance: mockPerformance,
  PerformanceObserver: mockPerformanceObserver,
  requestIdleCallback: jest.fn(function (callback) {
    return setTimeout(callback, 0);
  })
};
Object.defineProperty(global, 'window', {
  value: mockWindow,
  writable: true
});
Object.defineProperty(global, 'performance', {
  value: mockPerformance,
  writable: true
});
Object.defineProperty(global, 'requestIdleCallback', {
  value: mockWindow.requestIdleCallback,
  writable: true
});
var mockDocument = {
  createElement: jest.fn(function () {
    return {
      rel: '',
      href: '',
      as: '',
      type: '',
      media: '',
      onload: null
    };
  }),
  head: {
    appendChild: jest.fn()
  }
};
Object.defineProperty(global, 'window', {
  value: mockWindow,
  writable: true
});
Object.defineProperty(global, 'document', {
  value: mockDocument,
  writable: true
});
Object.defineProperty(global, 'performance', {
  value: mockPerformance,
  writable: true
});
Object.defineProperty(global, '__DEV__', {
  value: true,
  writable: true
});
describe('Performance Utilities', function () {
  beforeEach(function () {
    jest.clearAllMocks();
    mockPerformance.now.mockReturnValue(1000);
  });
  describe('PerformanceMonitor', function () {
    var monitor;
    beforeEach(function () {
      monitor = _performance.PerformanceMonitor.getInstance();
      monitor.clearMetrics();
    });
    it('creates singleton instance', function () {
      var instance1 = _performance.PerformanceMonitor.getInstance();
      var instance2 = _performance.PerformanceMonitor.getInstance();
      expect(instance1).toBe(instance2);
    });
    it('records metrics correctly', function () {
      var metric = {
        name: 'test-metric',
        value: 100,
        timestamp: Date.now(),
        type: 'timing',
        tags: {
          test: 'true'
        }
      };
      monitor.recordMetric(metric);
      var metrics = monitor.getMetrics('test-metric');
      expect(metrics).toHaveLength(1);
      expect(metrics[0]).toEqual(metric);
    });
    it('gets all metrics when no name specified', function () {
      var metric1 = {
        name: 'metric-1',
        value: 100,
        timestamp: Date.now(),
        type: 'timing'
      };
      var metric2 = {
        name: 'metric-2',
        value: 200,
        timestamp: Date.now(),
        type: 'counter'
      };
      monitor.recordMetric(metric1);
      monitor.recordMetric(metric2);
      var allMetrics = monitor.getMetrics();
      expect(allMetrics).toHaveLength(2);
    });
    it('generates performance summary', function () {
      monitor.recordMetric({
        name: 'test-timing',
        value: 100,
        timestamp: Date.now(),
        type: 'timing'
      });
      monitor.recordMetric({
        name: 'test-timing',
        value: 200,
        timestamp: Date.now(),
        type: 'timing'
      });
      var summary = monitor.getSummary();
      expect(summary['test-timing']).toEqual({
        avg: 150,
        min: 100,
        max: 200,
        count: 2
      });
    });
    it('limits metrics to prevent memory leaks', function () {
      var originalLog = console.log;
      console.log = jest.fn();
      try {
        for (var i = 0; i < 10; i++) {
          monitor.recordMetric({
            name: `metric-${i}`,
            value: i,
            timestamp: Date.now(),
            type: 'counter'
          });
        }
        var allMetrics = monitor.getMetrics();
        expect(allMetrics.length).toBe(10);
        expect(allMetrics[0].name).toBe('metric-0');
        expect(allMetrics[9].name).toBe('metric-9');
      } finally {
        console.log = originalLog;
      }
    });
    it('clears metrics', function () {
      monitor.recordMetric({
        name: 'test',
        value: 100,
        timestamp: Date.now(),
        type: 'timing'
      });
      expect(monitor.getMetrics()).toHaveLength(1);
      monitor.clearMetrics();
      expect(monitor.getMetrics()).toHaveLength(0);
    });
  });
  describe('PerformanceTimer', function () {
    it('measures timing correctly', function () {
      mockPerformance.now.mockReturnValueOnce(1000).mockReturnValueOnce(1500);
      var timer = new _performance.PerformanceTimer('test-timer');
      var duration = timer.end();
      expect(duration).toBe(500);
      var metrics = _performance.performanceMonitor.getMetrics('test-timer');
      expect(metrics).toHaveLength(1);
      expect(metrics[0].value).toBe(500);
    });
    it('includes tags in metrics', function () {
      mockPerformance.now.mockReturnValueOnce(1000).mockReturnValueOnce(1200);
      var timer = new _performance.PerformanceTimer('tagged-timer');
      timer.end({
        component: 'test',
        action: 'render'
      });
      var metrics = _performance.performanceMonitor.getMetrics('tagged-timer');
      expect(metrics[0].tags).toEqual({
        component: 'test',
        action: 'render'
      });
    });
  });
  describe('CriticalCSSOptimizer', function () {
    beforeEach(function () {
      mockDocument.createElement.mockReturnValue({
        rel: '',
        href: '',
        as: '',
        type: '',
        media: '',
        onload: null
      });
    });
    it('sets and gets critical CSS', function () {
      var css = 'body { margin: 0; }';
      _performance.CriticalCSSOptimizer.setCriticalCSS(css);
      expect(_performance.CriticalCSSOptimizer.getCriticalCSS()).toBe(css);
    });
    it('loads non-critical CSS asynchronously', function () {
      var cssUrl = '/styles/non-critical.css';
      _performance.CriticalCSSOptimizer.addNonCriticalCSS(cssUrl);
      expect(mockDocument.createElement).toHaveBeenCalledWith('link');
      expect(mockDocument.head.appendChild).toHaveBeenCalled();
    });
    it('preloads critical resources', function () {
      var resources = [{
        href: '/font.woff2',
        as: 'font',
        type: 'font/woff2'
      }, {
        href: '/critical.css',
        as: 'style'
      }];
      _performance.CriticalCSSOptimizer.preloadCriticalResources(resources);
      expect(mockDocument.createElement).toHaveBeenCalledTimes(2);
      expect(mockDocument.head.appendChild).toHaveBeenCalledTimes(2);
    });
  });
  describe('CodeSplittingUtils', function () {
    it('imports module with performance tracking', (0, _asyncToGenerator2.default)(function* () {
      mockPerformance.now.mockReturnValueOnce(1000).mockReturnValueOnce(1500);
      var mockModule = {
        default: 'test-component'
      };
      var importFn = jest.fn().mockResolvedValue(mockModule);
      var result = yield _performance.CodeSplittingUtils.importModule(importFn, 'test-chunk');
      expect(result).toBe(mockModule);
      expect(importFn).toHaveBeenCalled();
      var metrics = _performance.performanceMonitor.getMetrics('chunk-load-test-chunk');
      expect(metrics).toHaveLength(1);
      expect(metrics[0].tags).toEqual({
        chunk: 'test-chunk',
        status: 'success'
      });
    }));
    it('handles import errors', (0, _asyncToGenerator2.default)(function* () {
      var error = new Error('Import failed');
      var importFn = jest.fn().mockRejectedValue(error);
      yield expect(_performance.CodeSplittingUtils.importModule(importFn, 'error-chunk')).rejects.toThrow('Import failed');
      var metrics = _performance.performanceMonitor.getMetrics('chunk-load-error-chunk');
      expect(metrics[0].tags).toEqual({
        chunk: 'error-chunk',
        status: 'error'
      });
    }));
    it('preloads chunks', function () {
      var importFn = jest.fn().mockResolvedValue({
        default: 'preloaded'
      });
      _performance.CodeSplittingUtils.preloadChunk(importFn, 'preload-chunk');
      expect(mockWindow.requestIdleCallback).toHaveBeenCalled();
    });
    it('tracks loaded chunks', (0, _asyncToGenerator2.default)(function* () {
      var importFn = jest.fn().mockResolvedValue({
        default: 'test'
      });
      yield _performance.CodeSplittingUtils.importModule(importFn, 'tracked-chunk');
      var loadedChunks = _performance.CodeSplittingUtils.getLoadedChunks();
      expect(loadedChunks).toContain('tracked-chunk');
    }));
  });
  describe('MemoryMonitor', function () {
    it('gets memory usage information', function () {
      var usage = _performance.MemoryMonitor.getMemoryUsage();
      expect(usage).toEqual({
        used: 1000000,
        total: 2000000,
        percentage: 50
      });
    });
    it('returns null when memory API not available', function () {
      var originalMemory = mockPerformance.memory;
      delete mockPerformance.memory;
      var usage = _performance.MemoryMonitor.getMemoryUsage();
      expect(usage).toBeNull();
      mockPerformance.memory = originalMemory;
    });
    it('starts memory monitoring', function () {
      jest.useFakeTimers();
      _performance.performanceMonitor.clearMetrics();
      var originalLog = console.log;
      console.log = jest.fn();
      try {
        _performance.MemoryMonitor.startMemoryMonitoring(40);
        jest.advanceTimersByTime(30000);
        expect(jest.getTimerCount()).toBeGreaterThanOrEqual(0);
      } finally {
        console.log = originalLog;
        jest.useRealTimers();
      }
    });
  });
  describe('Utility Functions', function () {
    it('measurePerformance creates PerformanceTimer', function () {
      var timer = (0, _performance.measurePerformance)('test-measure');
      expect(timer).toBeInstanceOf(_performance.PerformanceTimer);
    });
    it('withPerformanceTracking wraps synchronous functions', function () {
      var originalFn = jest.fn(function (x) {
        return x * 2;
      });
      var wrappedFn = (0, _performance.withPerformanceTracking)(originalFn, 'sync-function');
      var result = wrappedFn(5);
      expect(result).toBe(10);
      expect(originalFn).toHaveBeenCalledWith(5);
      var metrics = _performance.performanceMonitor.getMetrics('sync-function');
      expect(metrics).toHaveLength(1);
      expect(typeof metrics[0].value).toBe('number');
    });
    it('withPerformanceTracking wraps asynchronous functions', (0, _asyncToGenerator2.default)(function* () {
      var originalFn = jest.fn().mockResolvedValue('async-result');
      var wrappedFn = (0, _performance.withPerformanceTracking)(originalFn, 'async-function');
      var result = yield wrappedFn();
      expect(result).toBe('async-result');
      var metrics = _performance.performanceMonitor.getMetrics('async-function');
      expect(metrics).toHaveLength(1);
      expect(typeof metrics[0].value).toBe('number');
    }));
    it('withPerformanceTracking handles errors', function () {
      var error = new Error('Test error');
      var originalFn = jest.fn().mockImplementation(function () {
        throw error;
      });
      var wrappedFn = (0, _performance.withPerformanceTracking)(originalFn, 'error-function');
      expect(function () {
        return wrappedFn();
      }).toThrow('Test error');
      var metrics = _performance.performanceMonitor.getMetrics('error-function');
      expect(metrics[0].tags).toEqual({
        status: 'error'
      });
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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