1dc5cdef4629de974d1fe471cad975d7
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.Typography = exports.Subtitle = exports.Subtitle = exports.Overline = exports.Overline = exports.Label = exports.Label = exports.Heading = exports.Heading = exports.Display = exports.Display = exports.Code = exports.Code = exports.Caption = exports.Caption = exports.Body = exports.Body = void 0;
var _objectWithoutProperties2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));
var _react = _interopRequireDefault(require("react"));
var _reactNative = require("react-native");
var _Typography = require("../../constants/Typography");
var _HighContrastContext = require("../../contexts/HighContrastContext");
var _CognitiveAccessibilityContext = require("../../contexts/CognitiveAccessibilityContext");
var _jsxRuntime = require("react/jsx-runtime");
var _excluded = ["children", "variant", "color", "align", "transform", "accessibilityLevel", "semanticLevel", "selectable", "numberOfLines", "ellipsizeMode", "style", "gutterBottom", "gutterTop", "testID"],
  _excluded2 = ["level"],
  _excluded3 = ["size"],
  _excluded4 = ["level"],
  _excluded5 = ["size"];
var Typography = exports.Typography = function Typography(_ref) {
  var _colors$text;
  var children = _ref.children,
    _ref$variant = _ref.variant,
    variant = _ref$variant === void 0 ? 'body1' : _ref$variant,
    color = _ref.color,
    _ref$align = _ref.align,
    align = _ref$align === void 0 ? 'left' : _ref$align,
    _ref$transform = _ref.transform,
    transform = _ref$transform === void 0 ? 'none' : _ref$transform,
    _ref$accessibilityLev = _ref.accessibilityLevel,
    accessibilityLevel = _ref$accessibilityLev === void 0 ? 'normal' : _ref$accessibilityLev,
    semanticLevel = _ref.semanticLevel,
    _ref$selectable = _ref.selectable,
    selectable = _ref$selectable === void 0 ? true : _ref$selectable,
    numberOfLines = _ref.numberOfLines,
    _ref$ellipsizeMode = _ref.ellipsizeMode,
    ellipsizeMode = _ref$ellipsizeMode === void 0 ? 'tail' : _ref$ellipsizeMode,
    style = _ref.style,
    _ref$gutterBottom = _ref.gutterBottom,
    gutterBottom = _ref$gutterBottom === void 0 ? false : _ref$gutterBottom,
    _ref$gutterTop = _ref.gutterTop,
    gutterTop = _ref$gutterTop === void 0 ? false : _ref$gutterTop,
    testID = _ref.testID,
    textProps = (0, _objectWithoutProperties2.default)(_ref, _excluded);
  var _useHighContrastColor = (0, _HighContrastContext.useHighContrastColors)(),
    colors = _useHighContrastColor.colors;
  var _useCognitiveAccessib = (0, _CognitiveAccessibilityContext.useCognitiveAccessibility)(),
    processText = _useCognitiveAccessib.processText,
    settings = _useCognitiveAccessib.settings;
  var variantStyles = _Typography.TYPOGRAPHY_VARIANTS[variant];
  var processedText = typeof children === 'string' ? processText(children) : children;
  var accessibleFontSize = (0, _Typography.getAccessibleFontSize)(variantStyles.fontSize, accessibilityLevel === 'normal' ? 'normal' : accessibilityLevel === 'large' ? 'large' : 'extraLarge');
  var effectiveColor = color || (colors == null || (_colors$text = colors.text) == null ? void 0 : _colors$text.primary) || '#333333';
  var getAccessibilityProps = function getAccessibilityProps() {
    var props = {};
    if (variant.startsWith('h') || semanticLevel) {
      props.accessibilityRole = 'header';
      props.accessibilityLevel = semanticLevel || parseInt(variant.charAt(1)) || 1;
    } else {
      props.accessibilityRole = 'text';
    }
    if (numberOfLines && typeof children === 'string') {
      props.accessibilityLabel = children;
    }
    return props;
  };
  var textStyles = [styles.base, {
    fontSize: accessibleFontSize,
    fontFamily: variantStyles.fontFamily,
    fontWeight: variantStyles.fontWeight,
    lineHeight: variantStyles.lineHeight,
    letterSpacing: variantStyles.letterSpacing,
    color: effectiveColor,
    textAlign: align,
    textTransform: transform
  }, gutterTop && styles.gutterTop, gutterBottom && styles.gutterBottom, style].filter(Boolean);
  if (__DEV__ && !(0, _Typography.isTextSizeAccessible)(accessibleFontSize)) {
    console.warn(`Typography: Font size ${accessibleFontSize}px may not be accessible`);
  }
  return (0, _jsxRuntime.jsx)(_reactNative.Text, Object.assign({
    style: textStyles,
    selectable: selectable,
    numberOfLines: numberOfLines,
    ellipsizeMode: ellipsizeMode,
    testID: testID
  }, getAccessibilityProps(), textProps, {
    children: processedText
  }));
};
var Heading = exports.Heading = exports.Heading = function Heading(_ref2) {
  var level = _ref2.level,
    props = (0, _objectWithoutProperties2.default)(_ref2, _excluded2);
  return (0, _jsxRuntime.jsx)(Typography, Object.assign({
    variant: `h${level}`,
    semanticLevel: level,
    gutterBottom: true
  }, props));
};
var Body = exports.Body = exports.Body = function Body(_ref3) {
  var _ref3$size = _ref3.size,
    size = _ref3$size === void 0 ? 'medium' : _ref3$size,
    props = (0, _objectWithoutProperties2.default)(_ref3, _excluded3);
  return (0, _jsxRuntime.jsx)(Typography, Object.assign({
    variant: size === 'small' ? 'body2' : 'body1',
    gutterBottom: true
  }, props));
};
var Caption = exports.Caption = exports.Caption = function Caption(props) {
  return (0, _jsxRuntime.jsx)(Typography, Object.assign({
    variant: "caption"
  }, props));
};
var Label = exports.Label = exports.Label = function Label(props) {
  return (0, _jsxRuntime.jsx)(Typography, Object.assign({
    variant: "label"
  }, props));
};
var Subtitle = exports.Subtitle = exports.Subtitle = function Subtitle(_ref4) {
  var _ref4$level = _ref4.level,
    level = _ref4$level === void 0 ? 1 : _ref4$level,
    props = (0, _objectWithoutProperties2.default)(_ref4, _excluded4);
  return (0, _jsxRuntime.jsx)(Typography, Object.assign({
    variant: level === 1 ? 'subtitle1' : 'subtitle2',
    gutterBottom: true
  }, props));
};
var Overline = exports.Overline = exports.Overline = function Overline(props) {
  return (0, _jsxRuntime.jsx)(Typography, Object.assign({
    variant: "overline"
  }, props));
};
var Display = exports.Display = exports.Display = function Display(_ref5) {
  var _ref5$size = _ref5.size,
    size = _ref5$size === void 0 ? 'medium' : _ref5$size,
    props = (0, _objectWithoutProperties2.default)(_ref5, _excluded5);
  var variantMap = {
    small: 'h2',
    medium: 'h1',
    large: 'h1'
  };
  return (0, _jsxRuntime.jsx)(Typography, Object.assign({
    variant: variantMap[size],
    gutterBottom: true
  }, props));
};
var Code = exports.Code = exports.Code = function Code(props) {
  return (0, _jsxRuntime.jsx)(Typography, Object.assign({
    variant: "code",
    selectable: true
  }, props));
};
var styles = _reactNative.StyleSheet.create({
  base: {},
  gutterTop: {
    marginTop: 16
  },
  gutterBottom: {
    marginBottom: 16
  }
});
var _default = exports.default = Typography;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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