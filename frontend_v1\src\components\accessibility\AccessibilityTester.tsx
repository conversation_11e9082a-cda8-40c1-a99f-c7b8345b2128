/**
 * Accessibility Tester Component
 * Provides tools for testing and validating accessibility features
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
} from 'react-native';
import { Colors } from '../../constants/Colors';
import {
  ScreenReaderUtils,
  ColorContrastUtils,
  AccessibilityTestUtils,
  AdvancedAccessibilityUtils,
  AccessibilityMonitoringUtils,
} from '../../utils/accessibility';

interface AccessibilityTesterProps {
  visible: boolean;
  onClose: () => void;
}

export const AccessibilityTester: React.FC<AccessibilityTesterProps> = ({
  visible,
  onClose,
}) => {
  const [screenReaderEnabled, setScreenReaderEnabled] = useState(false);
  const [reducedMotionEnabled, setReducedMotionEnabled] = useState(false);
  const [testResults, setTestResults] = useState<any>(null);
  const [complianceResults, setComplianceResults] = useState<any>(null);

  useEffect(() => {
    if (visible) {
      checkAccessibilityStatus();
    }
  }, [visible]);

  const checkAccessibilityStatus = async () => {
    try {
      const screenReader = await ScreenReaderUtils.isScreenReaderEnabled();
      const reducedMotion = await ScreenReaderUtils.isReduceMotionEnabled();
      
      setScreenReaderEnabled(screenReader);
      setReducedMotionEnabled(reducedMotion);

      // Get compliance results
      const compliance = await AccessibilityMonitoringUtils.validateCompliance();
      setComplianceResults(compliance);
    } catch (error) {
      console.error('Failed to check accessibility status:', error);
    }
  };

  const runAccessibilityTests = () => {
    // Mock component tree for testing
    const mockComponents = [
      {
        type: 'TouchableOpacity',
        props: {
          accessible: true,
          accessibilityRole: 'button',
          accessibilityLabel: 'Test Button',
          accessibilityHint: 'Double tap to test',
        },
      },
      {
        type: 'TextInput',
        props: {
          accessible: true,
          accessibilityLabel: 'Email Input',
          placeholder: 'Enter email',
        },
      },
      {
        type: 'Image',
        props: {
          accessible: true,
          accessibilityRole: 'image',
          accessibilityLabel: 'Profile picture',
        },
      },
    ];

    const results = AccessibilityTestUtils.auditComponentTree(mockComponents);
    setTestResults(results);
  };

  const testColorContrast = () => {
    const foreground = Colors.textPrimary;
    const background = Colors.background;
    
    const ratio = ColorContrastUtils.getContrastRatio(foreground, background);
    const meetsAA = ColorContrastUtils.meetsWCAGStandards(foreground, background, 'AA');
    const meetsAAA = ColorContrastUtils.meetsWCAGStandards(foreground, background, 'AAA');

    Alert.alert(
      'Color Contrast Test',
      `Contrast Ratio: ${ratio.toFixed(2)}\nWCAG AA: ${meetsAA ? 'Pass' : 'Fail'}\nWCAG AAA: ${meetsAAA ? 'Pass' : 'Fail'}`,
      [{ text: 'OK' }]
    );
  };

  const testAnnouncements = () => {
    AdvancedAccessibilityUtils.announceLiveRegion(
      'This is a test announcement for screen readers',
      'polite'
    );
  };

  const testFormValidation = () => {
    AdvancedAccessibilityUtils.announceFormValidation(
      'Email field',
      false,
      'Please enter a valid email address'
    );
  };

  const testProgressAnnouncement = () => {
    AdvancedAccessibilityUtils.announceProgress(3, 5, 'Upload progress');
  };

  if (!visible) {
    return null;
  }

  return (
    <View style={styles.overlay}>
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Accessibility Tester</Text>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={onClose}
            accessible={true}
            accessibilityRole="button"
            accessibilityLabel="Close accessibility tester"
            accessibilityHint="Double tap to close"
          >
            <Text style={styles.closeButtonText}>×</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content}>
          {/* Status Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Accessibility Status</Text>
            <View style={styles.statusItem}>
              <Text style={styles.statusLabel}>Screen Reader:</Text>
              <Text style={[styles.statusValue, screenReaderEnabled && styles.statusEnabled]}>
                {screenReaderEnabled ? 'Enabled' : 'Disabled'}
              </Text>
            </View>
            <View style={styles.statusItem}>
              <Text style={styles.statusLabel}>Reduced Motion:</Text>
              <Text style={[styles.statusValue, reducedMotionEnabled && styles.statusEnabled]}>
                {reducedMotionEnabled ? 'Enabled' : 'Disabled'}
              </Text>
            </View>
          </View>

          {/* Compliance Section */}
          {complianceResults && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>WCAG Compliance</Text>
              {Object.entries(complianceResults).map(([key, value]) => (
                <View key={key} style={styles.statusItem}>
                  <Text style={styles.statusLabel}>{key}:</Text>
                  <Text style={[styles.statusValue, value && styles.statusEnabled]}>
                    {value ? 'Pass' : 'Fail'}
                  </Text>
                </View>
              ))}
            </View>
          )}

          {/* Test Actions */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Test Actions</Text>
            
            <TouchableOpacity
              style={styles.testButton}
              onPress={runAccessibilityTests}
              accessible={true}
              accessibilityRole="button"
              accessibilityLabel="Run accessibility tests"
              accessibilityHint="Double tap to run component accessibility tests"
            >
              <Text style={styles.testButtonText}>Run Component Tests</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.testButton}
              onPress={testColorContrast}
              accessible={true}
              accessibilityRole="button"
              accessibilityLabel="Test color contrast"
              accessibilityHint="Double tap to test color contrast ratios"
            >
              <Text style={styles.testButtonText}>Test Color Contrast</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.testButton}
              onPress={testAnnouncements}
              accessible={true}
              accessibilityRole="button"
              accessibilityLabel="Test announcements"
              accessibilityHint="Double tap to test screen reader announcements"
            >
              <Text style={styles.testButtonText}>Test Announcements</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.testButton}
              onPress={testFormValidation}
              accessible={true}
              accessibilityRole="button"
              accessibilityLabel="Test form validation"
              accessibilityHint="Double tap to test form validation announcements"
            >
              <Text style={styles.testButtonText}>Test Form Validation</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.testButton}
              onPress={testProgressAnnouncement}
              accessible={true}
              accessibilityRole="button"
              accessibilityLabel="Test progress announcement"
              accessibilityHint="Double tap to test progress announcements"
            >
              <Text style={styles.testButtonText}>Test Progress</Text>
            </TouchableOpacity>
          </View>

          {/* Test Results */}
          {testResults && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Test Results</Text>
              <Text style={styles.resultText}>
                Passed: {testResults.passed}
              </Text>
              <Text style={styles.resultText}>
                Failed: {testResults.failed}
              </Text>
              {testResults.issues.length > 0 && (
                <View style={styles.issuesContainer}>
                  <Text style={styles.issuesTitle}>Issues Found:</Text>
                  {testResults.issues.map((issue: any, index: number) => (
                    <View key={index} style={styles.issue}>
                      <Text style={styles.issueComponent}>{issue.component}</Text>
                      {issue.issues.map((issueText: string, issueIndex: number) => (
                        <Text key={issueIndex} style={styles.issueText}>
                          • {issueText}
                        </Text>
                      ))}
                    </View>
                  ))}
                </View>
              )}
            </View>
          )}
        </ScrollView>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  container: {
    backgroundColor: Colors.background,
    borderRadius: 12,
    margin: 20,
    maxHeight: '80%',
    width: '90%',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.backgroundSecondary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 20,
    color: Colors.textPrimary,
    fontWeight: 'bold',
  },
  content: {
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: 12,
  },
  statusItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  statusLabel: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  statusValue: {
    fontSize: 14,
    color: Colors.error,
    fontWeight: '500',
  },
  statusEnabled: {
    color: Colors.success,
  },
  testButton: {
    backgroundColor: Colors.primary,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  testButtonText: {
    color: Colors.textInverse,
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
  resultText: {
    fontSize: 14,
    color: Colors.textPrimary,
    marginBottom: 4,
  },
  issuesContainer: {
    marginTop: 12,
  },
  issuesTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.error,
    marginBottom: 8,
  },
  issue: {
    backgroundColor: Colors.backgroundSecondary,
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  issueComponent: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: 4,
  },
  issueText: {
    fontSize: 12,
    color: Colors.textSecondary,
    marginLeft: 8,
  },
});
