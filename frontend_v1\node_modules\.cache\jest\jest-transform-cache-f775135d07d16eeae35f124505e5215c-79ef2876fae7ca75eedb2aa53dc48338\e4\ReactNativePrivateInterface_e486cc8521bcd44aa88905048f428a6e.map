{"version": 3, "names": ["module", "exports", "BatchedBridge", "require", "default", "ExceptionsManager", "Platform", "RCTEventEmitter", "ReactNativeViewConfigRegistry", "TextInputState", "UIManager", "<PERSON><PERSON><PERSON><PERSON>", "deepFreezeAndThrowOnMutationInDev", "flattenStyle", "ReactFiberErrorDialog", "legacySendAccessibilityEvent", "RawEventEmitter", "CustomEvent", "createAttributePayload", "create", "diffAttributePayloads", "diff", "createPublicRootInstance", "createPublicInstance", "createPublicTextInstance", "getNativeTagFromPublicInstance", "getNodeFromPublicInstance", "getInternalInstanceHandleFromPublicInstance"], "sources": ["ReactNativePrivateInterface.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow strict-local\n */\n\nimport typeof BatchedBridge from '../BatchedBridge/BatchedBridge';\nimport typeof legacySendAccessibilityEvent from '../Components/AccessibilityInfo/legacySendAccessibilityEvent';\nimport typeof TextInputState from '../Components/TextInput/TextInputState';\nimport typeof ExceptionsManager from '../Core/ExceptionsManager';\nimport typeof RawEventEmitter from '../Core/RawEventEmitter';\nimport typeof ReactFiberErrorDialog from '../Core/ReactFiberErrorDialog';\nimport typeof RCTEventEmitter from '../EventEmitter/RCTEventEmitter';\nimport typeof CustomEvent from '../Events/CustomEvent';\nimport typeof {\n  createPublicInstance,\n  createPublicRootInstance,\n  createPublicTextInstance,\n  getInternalInstanceHandleFromPublicInstance,\n  getNativeTagFromPublicInstance,\n  getNodeFromPublicInstance,\n} from '../ReactNative/ReactFabricPublicInstance/ReactFabricPublicInstance';\nimport typeof {\n  create as createAttributePayload,\n  diff as diffAttributePayloads,\n} from '../ReactNative/ReactFabricPublicInstance/ReactNativeAttributePayload';\nimport typeof UIManager from '../ReactNative/UIManager';\nimport typeof * as ReactNativeViewConfigRegistry from '../Renderer/shims/ReactNativeViewConfigRegistry';\nimport typeof flattenStyle from '../StyleSheet/flattenStyle';\nimport type {DangerouslyImpreciseStyleProp} from '../StyleSheet/StyleSheet';\nimport typeof deepFreezeAndThrowOnMutationInDev from '../Utilities/deepFreezeAndThrowOnMutationInDev';\nimport typeof deepDiffer from '../Utilities/differ/deepDiffer';\nimport typeof Platform from '../Utilities/Platform';\n\n// Expose these types to the React renderer\nexport type {\n  HostInstance as PublicInstance,\n\n  // These types are only necessary for Paper\n  LegacyHostInstanceMethods as LegacyPublicInstance,\n  MeasureOnSuccessCallback,\n  MeasureInWindowOnSuccessCallback,\n  MeasureLayoutOnSuccessCallback,\n} from '../../src/private/types/HostInstance';\n\nexport type {PublicRootInstance} from '../ReactNative/ReactFabricPublicInstance/ReactFabricPublicInstance';\nexport type PublicTextInstance = ReturnType<createPublicTextInstance>;\n\n// flowlint unsafe-getters-setters:off\nmodule.exports = {\n  get BatchedBridge(): BatchedBridge {\n    return require('../BatchedBridge/BatchedBridge').default;\n  },\n  get ExceptionsManager(): ExceptionsManager {\n    return require('../Core/ExceptionsManager').default;\n  },\n  get Platform(): Platform {\n    return require('../Utilities/Platform').default;\n  },\n  get RCTEventEmitter(): RCTEventEmitter {\n    return require('../EventEmitter/RCTEventEmitter').default;\n  },\n  get ReactNativeViewConfigRegistry(): ReactNativeViewConfigRegistry {\n    return require('../Renderer/shims/ReactNativeViewConfigRegistry');\n  },\n  get TextInputState(): TextInputState {\n    return require('../Components/TextInput/TextInputState').default;\n  },\n  get UIManager(): UIManager {\n    return require('../ReactNative/UIManager').default;\n  },\n  // TODO: Remove when React has migrated to `createAttributePayload` and `diffAttributePayloads`\n  get deepDiffer(): deepDiffer {\n    return require('../Utilities/differ/deepDiffer').default;\n  },\n  get deepFreezeAndThrowOnMutationInDev(): deepFreezeAndThrowOnMutationInDev<\n    {...} | Array<mixed>,\n  > {\n    return require('../Utilities/deepFreezeAndThrowOnMutationInDev').default;\n  },\n  // TODO: Remove when React has migrated to `createAttributePayload` and `diffAttributePayloads`\n  get flattenStyle(): flattenStyle<DangerouslyImpreciseStyleProp> {\n    // $FlowFixMe[underconstrained-implicit-instantiation]\n    // $FlowFixMe[incompatible-return]\n    return require('../StyleSheet/flattenStyle').default;\n  },\n  get ReactFiberErrorDialog(): ReactFiberErrorDialog {\n    return require('../Core/ReactFiberErrorDialog').default;\n  },\n  get legacySendAccessibilityEvent(): legacySendAccessibilityEvent {\n    return require('../Components/AccessibilityInfo/legacySendAccessibilityEvent')\n      .default;\n  },\n  get RawEventEmitter(): RawEventEmitter {\n    return require('../Core/RawEventEmitter').default;\n  },\n  get CustomEvent(): CustomEvent {\n    return require('../Events/CustomEvent').default;\n  },\n  get createAttributePayload(): createAttributePayload {\n    return require('../ReactNative/ReactFabricPublicInstance/ReactNativeAttributePayload')\n      .create;\n  },\n  get diffAttributePayloads(): diffAttributePayloads {\n    return require('../ReactNative/ReactFabricPublicInstance/ReactNativeAttributePayload')\n      .diff;\n  },\n  get createPublicRootInstance(): createPublicRootInstance {\n    return require('../ReactNative/ReactFabricPublicInstance/ReactFabricPublicInstance')\n      .createPublicRootInstance;\n  },\n  get createPublicInstance(): createPublicInstance {\n    return require('../ReactNative/ReactFabricPublicInstance/ReactFabricPublicInstance')\n      .createPublicInstance;\n  },\n  get createPublicTextInstance(): createPublicTextInstance {\n    return require('../ReactNative/ReactFabricPublicInstance/ReactFabricPublicInstance')\n      .createPublicTextInstance;\n  },\n  get getNativeTagFromPublicInstance(): getNativeTagFromPublicInstance {\n    return require('../ReactNative/ReactFabricPublicInstance/ReactFabricPublicInstance')\n      .getNativeTagFromPublicInstance;\n  },\n  get getNodeFromPublicInstance(): getNodeFromPublicInstance {\n    return require('../ReactNative/ReactFabricPublicInstance/ReactFabricPublicInstance')\n      .getNodeFromPublicInstance;\n  },\n  get getInternalInstanceHandleFromPublicInstance(): getInternalInstanceHandleFromPublicInstance {\n    return require('../ReactNative/ReactFabricPublicInstance/ReactFabricPublicInstance')\n      .getInternalInstanceHandleFromPublicInstance;\n  },\n};\n"], "mappings": "AAqDAA,MAAM,CAACC,OAAO,GAAG;EACf,IAAIC,aAAaA,CAAA,EAAkB;IACjC,OAAOC,OAAO,iCAAiC,CAAC,CAACC,OAAO;EAC1D,CAAC;EACD,IAAIC,iBAAiBA,CAAA,EAAsB;IACzC,OAAOF,OAAO,4BAA4B,CAAC,CAACC,OAAO;EACrD,CAAC;EACD,IAAIE,QAAQA,CAAA,EAAa;IACvB,OAAOH,OAAO,wBAAwB,CAAC,CAACC,OAAO;EACjD,CAAC;EACD,IAAIG,eAAeA,CAAA,EAAoB;IACrC,OAAOJ,OAAO,kCAAkC,CAAC,CAACC,OAAO;EAC3D,CAAC;EACD,IAAII,6BAA6BA,CAAA,EAAkC;IACjE,OAAOL,OAAO,kDAAkD,CAAC;EACnE,CAAC;EACD,IAAIM,cAAcA,CAAA,EAAmB;IACnC,OAAON,OAAO,yCAAyC,CAAC,CAACC,OAAO;EAClE,CAAC;EACD,IAAIM,SAASA,CAAA,EAAc;IACzB,OAAOP,OAAO,2BAA2B,CAAC,CAACC,OAAO;EACpD,CAAC;EAED,IAAIO,UAAUA,CAAA,EAAe;IAC3B,OAAOR,OAAO,iCAAiC,CAAC,CAACC,OAAO;EAC1D,CAAC;EACD,IAAIQ,iCAAiCA,CAAA,EAEnC;IACA,OAAOT,OAAO,iDAAiD,CAAC,CAACC,OAAO;EAC1E,CAAC;EAED,IAAIS,YAAYA,CAAA,EAAgD;IAG9D,OAAOV,OAAO,6BAA6B,CAAC,CAACC,OAAO;EACtD,CAAC;EACD,IAAIU,qBAAqBA,CAAA,EAA0B;IACjD,OAAOX,OAAO,gCAAgC,CAAC,CAACC,OAAO;EACzD,CAAC;EACD,IAAIW,4BAA4BA,CAAA,EAAiC;IAC/D,OAAOZ,OAAO,+DAA+D,CAAC,CAC3EC,OAAO;EACZ,CAAC;EACD,IAAIY,eAAeA,CAAA,EAAoB;IACrC,OAAOb,OAAO,0BAA0B,CAAC,CAACC,OAAO;EACnD,CAAC;EACD,IAAIa,WAAWA,CAAA,EAAgB;IAC7B,OAAOd,OAAO,wBAAwB,CAAC,CAACC,OAAO;EACjD,CAAC;EACD,IAAIc,sBAAsBA,CAAA,EAA2B;IACnD,OAAOf,OAAO,uEAAuE,CAAC,CACnFgB,MAAM;EACX,CAAC;EACD,IAAIC,qBAAqBA,CAAA,EAA0B;IACjD,OAAOjB,OAAO,uEAAuE,CAAC,CACnFkB,IAAI;EACT,CAAC;EACD,IAAIC,wBAAwBA,CAAA,EAA6B;IACvD,OAAOnB,OAAO,qEAAqE,CAAC,CACjFmB,wBAAwB;EAC7B,CAAC;EACD,IAAIC,oBAAoBA,CAAA,EAAyB;IAC/C,OAAOpB,OAAO,qEAAqE,CAAC,CACjFoB,oBAAoB;EACzB,CAAC;EACD,IAAIC,wBAAwBA,CAAA,EAA6B;IACvD,OAAOrB,OAAO,qEAAqE,CAAC,CACjFqB,wBAAwB;EAC7B,CAAC;EACD,IAAIC,8BAA8BA,CAAA,EAAmC;IACnE,OAAOtB,OAAO,qEAAqE,CAAC,CACjFsB,8BAA8B;EACnC,CAAC;EACD,IAAIC,yBAAyBA,CAAA,EAA8B;IACzD,OAAOvB,OAAO,qEAAqE,CAAC,CACjFuB,yBAAyB;EAC9B,CAAC;EACD,IAAIC,2CAA2CA,CAAA,EAAgD;IAC7F,OAAOxB,OAAO,qEAAqE,CAAC,CACjFwB,2CAA2C;EAChD;AACF,CAAC", "ignoreList": []}