#!/usr/bin/env python
"""
Script to check and verify test account passwords
"""
import os
import sys
import django

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from apps.authentication.models import User

def check_test_accounts():
    """Check passwords for common test accounts"""
    
    # Common test account emails to check
    test_emails = [
        '<EMAIL>',
        '<EMAIL>', 
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    ]
    
    # Common passwords to test
    common_passwords = [
        'testpass123',
        'TestPass123!',
        'password',
        'test123',
        'password123',
        'vierla123'
    ]
    
    print("=== TEST ACCOUNT PASSWORD VERIFICATION ===\n")
    
    for email in test_emails:
        try:
            user = User.objects.get(email=email)
            print(f"📧 {email} ({user.role})")
            
            found_password = False
            for pwd in common_passwords:
                if user.check_password(pwd):
                    print(f"   ✅ Password: {pwd}")
                    found_password = True
                    break
            
            if not found_password:
                print(f"   ❌ None of the common passwords work")
                
        except User.DoesNotExist:
            print(f"📧 {email}")
            print(f"   ❌ User not found")
        
        print()

if __name__ == '__main__':
    check_test_accounts()
