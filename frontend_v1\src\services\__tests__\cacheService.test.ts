/**
 * Cache Service Tests
 *
 * Test Coverage:
 * - Cache operations (get, set, remove, clear)
 * - TTL and expiration handling
 * - Memory management and limits
 * - Cache statistics and analytics
 * - Error handling and edge cases
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { cacheService } from '../cacheService';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  getAllKeys: jest.fn(),
  multiRemove: jest.fn(),
}));

const mockAsyncStorage = AsyncStorage as jest.Mocked<typeof AsyncStorage>;

describe('CacheService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    cacheService.clear();
    
    // Reset AsyncStorage mocks
    mockAsyncStorage.getItem.mockResolvedValue(null);
    mockAsyncStorage.setItem.mockResolvedValue();
    mockAsyncStorage.removeItem.mockResolvedValue();
    mockAsyncStorage.getAllKeys.mockResolvedValue([]);
    mockAsyncStorage.multiRemove.mockResolvedValue();
  });

  describe('Basic Cache Operations', () => {
    it('sets and gets data from memory cache', async () => {
      const key = 'test-key';
      const data = { message: 'Hello, World!' };
      
      await cacheService.set(key, data);
      const result = await cacheService.get(key);
      
      expect(result).toEqual(data);
    });

    it('returns null for non-existent keys', async () => {
      const result = await cacheService.get('non-existent-key');
      expect(result).toBeNull();
    });

    it('removes data from cache', async () => {
      const key = 'test-key';
      const data = { message: 'Hello, World!' };
      
      await cacheService.set(key, data);
      await cacheService.remove(key);
      
      const result = await cacheService.get(key);
      expect(result).toBeNull();
    });

    it('clears all cache data', async () => {
      await cacheService.set('key1', 'data1');
      await cacheService.set('key2', 'data2');
      
      await cacheService.clear();
      
      const result1 = await cacheService.get('key1');
      const result2 = await cacheService.get('key2');
      
      expect(result1).toBeNull();
      expect(result2).toBeNull();
    });
  });

  describe('TTL and Expiration', () => {
    it('respects TTL for cache entries', async () => {
      const key = 'expiring-key';
      const data = 'expiring-data';
      const ttl = 100; // 100ms
      
      await cacheService.set(key, data, ttl);
      
      // Should be available immediately
      let result = await cacheService.get(key);
      expect(result).toBe(data);
      
      // Wait for expiration
      await new Promise(resolve => setTimeout(resolve, 150));
      
      // Should be expired now
      result = await cacheService.get(key);
      expect(result).toBeNull();
    });

    it('uses default TTL when not specified', async () => {
      const key = 'default-ttl-key';
      const data = 'default-ttl-data';
      
      await cacheService.set(key, data);
      
      const entryInfo = cacheService.getEntryInfo(key);
      expect(entryInfo?.ttl).toBe(5 * 60 * 1000); // Default 5 minutes
    });

    it('updates TTL on cache hit', async () => {
      const key = 'update-ttl-key';
      const data = 'update-ttl-data';
      
      await cacheService.set(key, data);
      
      const initialInfo = cacheService.getEntryInfo(key);
      const initialLastAccessed = initialInfo?.lastAccessed;
      
      // Wait a bit and access again
      await new Promise(resolve => setTimeout(resolve, 10));
      await cacheService.get(key);
      
      const updatedInfo = cacheService.getEntryInfo(key);
      expect(updatedInfo?.lastAccessed).toBeGreaterThan(initialLastAccessed!);
    });
  });

  describe('Storage Cache Integration', () => {
    it('falls back to storage cache when memory cache misses', async () => {
      const key = 'storage-key';
      const data = { stored: 'data' };
      const storageData = JSON.stringify({
        data,
        timestamp: Date.now(),
        ttl: 5 * 60 * 1000,
        version: '1.0.0',
        accessCount: 0,
        lastAccessed: Date.now(),
      });
      
      mockAsyncStorage.getItem.mockResolvedValue(storageData);
      
      const result = await cacheService.get(key);
      
      expect(result).toEqual(data);
      expect(mockAsyncStorage.getItem).toHaveBeenCalledWith('@vierla_cache_' + key);
    });

    it('stores data in both memory and storage by default', async () => {
      const key = 'dual-storage-key';
      const data = { dual: 'storage' };
      
      await cacheService.set(key, data);
      
      // Should be in memory
      const memoryResult = await cacheService.get(key);
      expect(memoryResult).toEqual(data);
      
      // Should also be stored in AsyncStorage
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith(
        '@vierla_cache_' + key,
        expect.stringContaining('"dual":"storage"')
      );
    });

    it('supports memory-only storage option', async () => {
      const key = 'memory-only-key';
      const data = { memory: 'only' };
      
      await cacheService.set(key, data, undefined, { memoryOnly: true });
      
      // Should be in memory
      const result = await cacheService.get(key);
      expect(result).toEqual(data);
      
      // Should NOT be stored in AsyncStorage
      expect(mockAsyncStorage.setItem).not.toHaveBeenCalled();
    });

    it('supports storage-only option', async () => {
      const key = 'storage-only-key';
      const data = { storage: 'only' };
      
      await cacheService.set(key, data, undefined, { storageOnly: true });
      
      // Should be stored in AsyncStorage
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith(
        '@vierla_cache_' + key,
        expect.stringContaining('"storage":"only"')
      );
    });
  });

  describe('Memory Management', () => {
    it('enforces memory limits', async () => {
      // Create a cache service with small memory limit for testing
      const testCache = new (cacheService.constructor as any)({
        maxMemorySize: 1000, // 1KB limit
      });
      
      // Add data that exceeds the limit
      const largeData = 'x'.repeat(500); // 500 bytes each
      
      await testCache.set('key1', largeData);
      await testCache.set('key2', largeData);
      await testCache.set('key3', largeData); // This should trigger cleanup
      
      // Some entries should be evicted
      const stats = testCache.getStats();
      expect(stats.totalSize).toBeLessThanOrEqual(1000);
    });

    it('uses LFU + LRU eviction strategy', async () => {
      const testCache = new (cacheService.constructor as any)({
        maxMemorySize: 1000,
      });
      
      const data = 'x'.repeat(200);
      
      // Add entries
      await testCache.set('frequent', data);
      await testCache.set('infrequent', data);
      await testCache.set('recent', data);
      
      // Access 'frequent' multiple times
      await testCache.get('frequent');
      await testCache.get('frequent');
      await testCache.get('frequent');
      
      // Access 'recent' once but more recently
      await new Promise(resolve => setTimeout(resolve, 10));
      await testCache.get('recent');
      
      // Add more data to trigger eviction
      await testCache.set('trigger', data);
      await testCache.set('eviction', data);
      
      // 'infrequent' should be evicted first (least frequently used)
      const infrequentResult = await testCache.get('infrequent');
      expect(infrequentResult).toBeNull();
      
      // 'frequent' should still be there
      const frequentResult = await testCache.get('frequent');
      expect(frequentResult).toBe(data);
    });
  });

  describe('Cache Statistics', () => {
    it('tracks cache hits and misses', async () => {
      const key = 'stats-key';
      const data = 'stats-data';
      
      // Miss
      await cacheService.get('non-existent');
      
      // Set and hit
      await cacheService.set(key, data);
      await cacheService.get(key);
      
      const stats = cacheService.getStats();
      expect(stats.memoryHits).toBe(1);
      expect(stats.memoryMisses).toBeGreaterThan(0);
    });

    it('calculates hit rate correctly', async () => {
      await cacheService.set('key1', 'data1');
      await cacheService.set('key2', 'data2');
      
      // 2 hits
      await cacheService.get('key1');
      await cacheService.get('key2');
      
      // 1 miss
      await cacheService.get('non-existent');
      
      const stats = cacheService.getStats();
      expect(stats.hitRate).toBeCloseTo(2/3, 2); // 2 hits out of 3 total
    });

    it('tracks entry count and total size', async () => {
      await cacheService.set('key1', 'small');
      await cacheService.set('key2', 'larger data string');
      
      const stats = cacheService.getStats();
      expect(stats.entryCount).toBe(2);
      expect(stats.totalSize).toBeGreaterThan(0);
    });
  });

  describe('Cache Preloading', () => {
    it('preloads multiple entries', async () => {
      const entries = [
        { key: 'preload1', data: 'data1' },
        { key: 'preload2', data: 'data2', ttl: 1000 },
        { key: 'preload3', data: { complex: 'object' } },
      ];
      
      await cacheService.preload(entries);
      
      // All entries should be available
      for (const entry of entries) {
        const result = await cacheService.get(entry.key);
        expect(result).toEqual(entry.data);
      }
    });

    it('handles preload failures gracefully', async () => {
      // Mock a failure in set operation
      const originalSet = cacheService.set;
      cacheService.set = jest.fn().mockRejectedValueOnce(new Error('Set failed'));
      
      const entries = [
        { key: 'success', data: 'data1' },
        { key: 'failure', data: 'data2' },
      ];
      
      // Should not throw
      await expect(cacheService.preload(entries)).resolves.toBeUndefined();
      
      // Restore original method
      cacheService.set = originalSet;
    });
  });

  describe('Pattern-based Invalidation', () => {
    it('invalidates entries matching pattern', async () => {
      await cacheService.set('user:1:profile', 'profile1');
      await cacheService.set('user:1:settings', 'settings1');
      await cacheService.set('user:2:profile', 'profile2');
      await cacheService.set('other:data', 'other');
      
      // Invalidate all user:1 entries
      await cacheService.invalidatePattern(/^user:1:/);
      
      // user:1 entries should be gone
      expect(await cacheService.get('user:1:profile')).toBeNull();
      expect(await cacheService.get('user:1:settings')).toBeNull();
      
      // Other entries should remain
      expect(await cacheService.get('user:2:profile')).toBe('profile2');
      expect(await cacheService.get('other:data')).toBe('other');
    });

    it('invalidates storage entries matching pattern', async () => {
      mockAsyncStorage.getAllKeys.mockResolvedValue([
        '@vierla_cache_user:1:profile',
        '@vierla_cache_user:1:settings',
        '@vierla_cache_user:2:profile',
        '@vierla_cache_other:data',
      ]);
      
      await cacheService.invalidatePattern(/^user:1:/);
      
      expect(mockAsyncStorage.multiRemove).toHaveBeenCalledWith([
        '@vierla_cache_user:1:profile',
        '@vierla_cache_user:1:settings',
      ]);
    });
  });

  describe('Entry Information', () => {
    it('provides entry metadata', async () => {
      const key = 'info-key';
      const data = 'info-data';
      const ttl = 10000;
      
      await cacheService.set(key, data, ttl);
      
      const info = cacheService.getEntryInfo(key);
      
      expect(info).toBeDefined();
      expect(info?.ttl).toBe(ttl);
      expect(info?.version).toBe('1.0.0');
      expect(info?.accessCount).toBe(0);
      expect(info?.timestamp).toBeGreaterThan(0);
      expect(info?.lastAccessed).toBeGreaterThan(0);
    });

    it('returns null for non-existent entries', () => {
      const info = cacheService.getEntryInfo('non-existent');
      expect(info).toBeNull();
    });

    it('updates access count on cache hits', async () => {
      const key = 'access-count-key';
      await cacheService.set(key, 'data');
      
      // Access multiple times
      await cacheService.get(key);
      await cacheService.get(key);
      await cacheService.get(key);
      
      const info = cacheService.getEntryInfo(key);
      expect(info?.accessCount).toBe(3);
    });
  });

  describe('Error Handling', () => {
    it('handles AsyncStorage errors gracefully', async () => {
      mockAsyncStorage.getItem.mockRejectedValue(new Error('Storage error'));
      
      // Should not throw and should return null
      const result = await cacheService.get('error-key');
      expect(result).toBeNull();
    });

    it('handles set errors gracefully', async () => {
      mockAsyncStorage.setItem.mockRejectedValue(new Error('Storage full'));
      
      // Should not throw
      await expect(cacheService.set('error-key', 'data')).resolves.toBeUndefined();
    });

    it('handles remove errors gracefully', async () => {
      mockAsyncStorage.removeItem.mockRejectedValue(new Error('Remove error'));
      
      // Should not throw
      await expect(cacheService.remove('error-key')).resolves.toBeUndefined();
    });

    it('handles clear errors gracefully', async () => {
      mockAsyncStorage.getAllKeys.mockRejectedValue(new Error('Keys error'));
      
      // Should not throw
      await expect(cacheService.clear()).resolves.toBeUndefined();
    });
  });

  describe('Service Lifecycle', () => {
    it('destroys service correctly', () => {
      const clearIntervalSpy = jest.spyOn(global, 'clearInterval');
      
      cacheService.destroy();
      
      expect(clearIntervalSpy).toHaveBeenCalled();
      
      clearIntervalSpy.mockRestore();
    });

    it('cleans up expired entries periodically', async () => {
      const key = 'cleanup-key';
      const shortTtl = 50; // 50ms
      
      await cacheService.set(key, 'data', shortTtl);
      
      // Entry should exist initially
      expect(await cacheService.get(key)).toBe('data');
      
      // Wait for cleanup interval + expiration
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Entry should be cleaned up
      expect(await cacheService.get(key)).toBeNull();
    });
  });
});
